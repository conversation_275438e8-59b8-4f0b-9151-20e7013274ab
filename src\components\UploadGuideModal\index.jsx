import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { UPLOAD_CONFIG, validateFile } from '../../config/uploads/upload';
import { UPLOAD_GUIDE_CONFIG } from '../../config/guides/upload-guide';
import { MdClose, MdAdd, MdAddCircleOutline, MdPhoto, MdAutoFixHigh } from 'react-icons/md';
import { createRequest } from '../../api';
import { useAuth } from '../../contexts/AuthContext';
import './index.css';
import '../../styles/close-buttons.css';
import { uploadImage } from '../../api/upload';
import { message } from 'antd';
import { Modal, Button, Upload, Divider, Row, Col, Alert } from 'antd';
import { UploadOutlined, InboxOutlined, FileImageOutlined } from '@ant-design/icons';
import Dragger from 'antd/es/upload/Dragger';

// 全局上传会话管理
let activeUploadSessions = {};

// 记录当前激活的上传会话ID
let currentActiveSessionId = null;

// 设置历史记录的最大数量
const MAX_HISTORY_COUNT = 10;

// 全局函数：跟踪当前显示的弹窗会话ID
export const trackUploadSession = (sessionId) => {
  console.log(`设置当前活跃会话ID: ${sessionId}`);
  currentActiveSessionId = sessionId;
};

const UploadGuideModal = ({ 
  onClose, 
  onUpload, 
  type = 'clothing', 
  pageType,
  initialFiles = [], // 初始文件列表
  initialView = 'guide' // 初始视图模式
}) => {
  // 创建唯一的会话ID
  const [sessionId] = useState(() => `upload_session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`);
  const { user } = useAuth();
  const [isDragging, setIsDragging] = useState(false);
  const [uploadView, setUploadView] = useState(initialView); // 使用传入的初始视图模式
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [uploadingFiles, setUploadingFiles] = useState({});
  const [activeTab, setActiveTab] = useState('guide'); // 新增：当前激活的标签页
  const [uploadHistory, setUploadHistory] = useState([]); // 新增：上传历史记录
  const [modalOpen, setModalOpen] = useState(true); // 新增：内部管理弹窗状态
  // 拖动相关状态
  const [isModalDragging, setIsModalDragging] = useState(false);
  const dragStartMouse = useRef({ x: 0, y: 0 });
  const dragStartTransform = useRef({ x: 0, y: 0 });
  const [dragTransform, setDragTransform] = useState({ x: 0, y: 0 });
  const [isMounted, setIsMounted] = useState(false);
  const modalRef = useRef(null);

  // 确保组件完全挂载后再显示
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 初始化时处理传入的文件列表
  useEffect(() => {
    const createdUrls = []; // 存储创建的URL，以便清理
    
    if (initialFiles && initialFiles.length > 0 && initialView === 'gallery') {
      // 一次处理多个文件，但防止文件被重复处理
      // 先将uploadView设置为'gallery'，避免在进入画廊视图时自动触发文件选择
      setUploadView('gallery');
      
      // 直接处理文件，跳过文件选择对话框
      const processedFiles = initialFiles.map(file => {
        // 创建本地预览
        const localUrl = URL.createObjectURL(file);
        createdUrls.push(localUrl); // 记录URL以便后续清理
        
        // 生成唯一ID
        const fileId = `file_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        
        return {
          id: fileId,
          file: file,
          url: localUrl,
          status: 'ready'
        };
      });
      
      // 更新上传文件状态
      setUploadedFiles(processedFiles);
    }
    
    // 组件卸载时清理创建的Blob URL
    return () => {
      createdUrls.forEach(url => {
        try {
          URL.revokeObjectURL(url);
        } catch (e) {
          console.error('清理Blob URL失败:', e);
        }
      });
    };
  }, []);

  // 获取服务器URL基础地址
  const SERVER_BASE_URL = useMemo(() => {
    // 优先使用环境变量中配置的API地址
    if (process.env.REACT_APP_API_BASE_URL) {
      return process.env.REACT_APP_API_BASE_URL;
    }
    // 否则使用固定的地址，这里统一用3002端口
    return process.env.REACT_APP_BACKEND_URL;
  }, []);

  // 当组件挂载时注册会话，卸载时注销会话
  useEffect(() => {
    console.log(`上传弹窗会话创建: ${sessionId}`);
    activeUploadSessions[sessionId] = true;
    
    // 设置当前活跃会话ID
    trackUploadSession(sessionId);
    
    return () => {
      console.log(`上传弹窗会话销毁: ${sessionId}`);
      delete activeUploadSessions[sessionId];
      
      // 如果当前活跃会话是此会话，则清除
      if (currentActiveSessionId === sessionId) {
        currentActiveSessionId = null;
      }
    };
  }, [sessionId]);

  // 拦截并包装onUpload回调，防止关闭错误的弹窗
  const safeOnUpload = useCallback((data) => {
    console.log('安全包装的onUpload被调用:', data);
    
    // 检查当前组件的会话ID是否是活跃会话ID
    const isCurrentSession = currentActiveSessionId === sessionId;
    console.log(`检查会话状态: 当前=${sessionId}, 活跃=${currentActiveSessionId}, 是否匹配=${isCurrentSession}`);
    
    // 确保在调用父组件回调时，只有当前组件是活跃会话时才关闭
    try {
      if (typeof onUpload === 'function') {
        // 在后台异步处理完成后的逻辑
        const callback = (needClose = true) => {
          // 只有在是当前会话时，才考虑关闭弹窗
          // 即使父组件传入关闭弹窗的指令，我们也只会在当前会话匹配时关闭
          onUpload({
            ...data,
            // 添加会话ID，方便父组件跟踪
            sessionId: sessionId,
            // 告诉父组件是否需要关闭弹窗
            shouldClose: needClose && isCurrentSession
          });
        };
        
        // 直接调用回调，让父组件决定是否关闭
        callback();
      }
    } catch (error) {
      console.error('调用父组件onUpload时出错:', error);
    }
  }, [onUpload, sessionId]);

  // 安全的关闭方法
  const userInitiatedClose = () => {
    console.log(`用户主动关闭弹窗，会话ID: ${sessionId}`);
    resetState();
    
    // 用户主动关闭时，不需要检查会话ID
    if (typeof onClose === 'function') {
      onClose();
    }
  };

  // 新增：保存和恢复Blob URL的方法
  const saveBlobAsDataURL = async (url) => {
    console.log('【DEBUG】saveBlobAsDataURL被调用，URL:', url.substring(0, 30) + '...');
    
    try {
      // 检查URL是否有效
      if (!url || typeof url !== 'string') {
        console.error('【ERROR】无效的URL格式');
        throw new Error('无效的URL格式');
      }
      
      // 如果已经是DataURL，则直接返回
      if (url.startsWith('data:')) {
        console.log('【INFO】URL已经是data:格式，直接返回');
        return url;
      }
      
      // 简化处理，直接fetch
      console.log('【INFO】开始获取URL内容');
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }
      
      const blob = await response.blob();
      console.log(`【INFO】获取到Blob: ${blob.size} 字节, 类型: ${blob.type}`);
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          console.log('【SUCCESS】FileReader加载完成，数据长度:', reader.result.length);
          resolve(reader.result);
        };
        reader.onerror = (error) => {
          console.error('【ERROR】FileReader错误:', error);
          reject(error);
        };
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('【ERROR】转换Blob URL到DataURL失败:', error);
      throw error;
    }
  };

  // 新增：从localStorage加载历史记录
  useEffect(() => {
    const historyKey = `upload_history_${type}_${pageType || 'default'}`;
    console.log('加载历史记录，键值:', historyKey);
    
    try {
      // 尝试清理localStorage（仅限开发模式使用，生产环境应移除此行）
      // localStorage.removeItem(historyKey);
      
      const savedHistory = localStorage.getItem(historyKey);
      if (savedHistory) {
        try {
          const parsedHistory = JSON.parse(savedHistory);
          console.log(`成功加载${parsedHistory.length}条历史记录`);
          
          // 过滤并转换历史记录，确保兼容性
          const validHistory = parsedHistory
            .filter(record => {
              // 检查记录是否有效（有thumbnailUrl或url或serverUrl）
              const hasValidImage = (record.thumbnailUrl && typeof record.thumbnailUrl === 'string') || 
                                   (record.url && typeof record.url === 'string') ||
                                   (record.serverUrl && typeof record.serverUrl === 'string');
              
              if (!hasValidImage) {
                console.log('忽略无效的历史记录:', record.id || '未知ID');
              }
              
              return hasValidImage;
            })
            .map(record => {
              // 将旧格式转换为新格式
              const updatedRecord = { ...record };
              
              // 检查并修复URL格式 - Blob URL会在页面刷新后失效
              if (record.url && typeof record.url === 'string') {
                // 如果URL以blob:开头，说明是临时的Blob URL，将在页面刷新后失效
                if (record.url.startsWith('blob:')) {
                  console.log('检测到Blob URL，将被替换:', record.id || '未知ID');
                  // 首先尝试使用thumbnailUrl
                  if (record.thumbnailUrl && typeof record.thumbnailUrl === 'string') {
                    updatedRecord.url = record.thumbnailUrl;
                  } 
                  // 如果没有thumbnailUrl，尝试使用serverUrl
                  else if (record.serverUrl && typeof record.serverUrl === 'string') {
                    updatedRecord.url = record.serverUrl;
                  }
                  // 如果仍然没有有效URL，这条记录在显示时可能会失败
                }
              }
              
              // 确保有显示用的thumbnailUrl
              if (!record.thumbnailUrl && record.url && !record.url.startsWith('blob:')) {
                updatedRecord.thumbnailUrl = record.url;
              }
              
              return updatedRecord;
            });
          
          console.log(`过滤后剩余${validHistory.length}条有效历史记录`);
          
          // 如果有无效记录，更新localStorage
          if (validHistory.length < parsedHistory.length) {
            // 仅当存在无效记录时更新localStorage
            try {
              localStorage.setItem(historyKey, JSON.stringify(validHistory));
              console.log('已清理无效历史记录并更新localStorage');
            } catch (storageError) {
              console.warn('清理历史记录时出错，可能是localStorage已满:', storageError.message);
            }
          }
          
          setUploadHistory(validHistory);
        } catch (error) {
          console.error('解析历史记录失败:', error);
          
          // 如果解析失败，清空历史记录并重新开始
          try {
            localStorage.removeItem(historyKey);
            console.log('已删除无效的历史记录数据');
          } catch (e) {
            console.warn('删除无效历史记录失败:', e);
          }
          
          setUploadHistory([]);
        }
      } else {
        console.log('未找到历史记录');
        setUploadHistory([]);
      }
    } catch (error) {
      console.error('加载历史记录时出错:', error);
      setUploadHistory([]);
    }
  }, [type, pageType]);

  // 添加图片压缩功能，生成小尺寸缩略图
  const createThumbnail = (file, callback) => {
    if (!file || !file.type.startsWith('image/')) {
      console.error('无效的文件类型，无法创建缩略图');
      callback(null);
      return;
    }

    try {
      // 从文件创建URL
      const objectUrl = URL.createObjectURL(file);
      
      // 创建图片对象加载文件
      const img = new Image();
      img.onload = () => {
        try {
          // 创建canvas用于绘制缩略图
          const canvas = document.createElement('canvas');
          
          // 设置缩略图最大尺寸
          const MAX_THUMBNAIL_SIZE = 400; // 调整为400像素，原来是200
          
          // 计算缩放比例
          let width = img.width;
          let height = img.height;
          const aspectRatio = width / height;
          
          if (width > height) {
            // 横向图片
            if (width > MAX_THUMBNAIL_SIZE) {
              width = MAX_THUMBNAIL_SIZE;
              height = width / aspectRatio;
            }
          } else {
            // 纵向图片
            if (height > MAX_THUMBNAIL_SIZE) {
              height = MAX_THUMBNAIL_SIZE;
              width = height * aspectRatio;
            }
          }
          
          // 设置canvas尺寸
          canvas.width = width;
          canvas.height = height;
          
          // 绘制缩放后的图片
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);
          
          // 输出为低质量的JPEG (质量0.6)
          const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.6);
          
          // 计算缩略图大小（KB）
          const base64Data = thumbnailDataUrl.split(',')[1];
          const byteSize = atob(base64Data).length;
          const kiloByteSize = byteSize / 1024;
          
          // 释放URL对象
          URL.revokeObjectURL(objectUrl);
          
          console.log(`缩略图创建成功，原始大小: ${img.width}x${img.height}, 缩略图大小: ${width}x${height}, 文件大小: ${kiloByteSize.toFixed(2)}KB`);
          
          // 返回缩略图数据URL
          callback(thumbnailDataUrl);
        } catch (error) {
          console.error('创建缩略图过程中出错:', error);
          URL.revokeObjectURL(objectUrl);
          callback(null);
        }
      };
      
      img.onerror = (error) => {
        console.error('加载图片出错:', error);
        URL.revokeObjectURL(objectUrl);
        callback(null);
      };
      
      img.src = objectUrl;
    } catch (error) {
      console.error('处理图片文件时出错:', error);
      callback(null);
    }
  };

  // 修改服务器上传成功后的回调函数，添加缩略图生成
  const serverUploadSuccess = (file, localUrl, serverUrl, fileName) => {
    // 添加延迟保存，减少并发保存的问题
    const pageTypeName = pageType || 'default';
    const typeName = type || 'clothing';
    
    console.log(`服务器上传成功，准备保存到历史记录，类型: ${typeName}, 页面: ${pageTypeName}, 服务器URL: ${serverUrl?.substring(0, 30) || '无'}...`);
    
    try {
      // 生成小尺寸缩略图，而不是保存完整DataURL
      createThumbnail(file, (thumbnailDataUrl) => {
        if (!thumbnailDataUrl) {
          console.warn('创建缩略图失败，将使用临时本地URL');
        }
        
        const historyRecord = {
          id: Date.now().toString(),
          url: localUrl,                               // 本地URL (仅用于当前会话显示)
          thumbnailUrl: thumbnailDataUrl || localUrl,  // 持久的缩略图URL (低分辨率)
          serverUrl: serverUrl,                        // 服务器URL (用于重新使用时获取)
          fileName: fileName || file.name,
          type: typeName,                              // 记录图片类型
          pageType: pageTypeName,                      // 记录页面类型
          fileType: file.type,                         // 保存文件类型
          saveTime: Date.now()                         // 保存时间戳
        };
        
        // 使用延迟来避免并发保存同一文件
        setTimeout(() => {
          saveToHistory(historyRecord)
            .then(success => {
              if (success) {
                console.log(`历史记录保存成功, 文件名: ${historyRecord.fileName}, 使用缩略图: ${!!thumbnailDataUrl}`);
              } else {
                console.warn(`历史记录保存失败, 文件名: ${historyRecord.fileName}`);
              }
            })
            .catch(err => {
              console.error(`保存到历史记录时出错: ${err}, 文件名: ${historyRecord.fileName}`);
            });
        }, 300); // 增加300ms延迟，允许之前的操作完成
      });
    } catch (error) {
      console.error('准备保存历史记录数据时出错:', error);
    }
  };

  // 新增：保存历史记录到localStorage
  const saveToHistory = async (record) => {
    if (!record || !record.fileName) {
      console.warn('记录无效或没有文件名，无法保存到历史记录');
      return false;
    }
    
    try {
      let history = [];
      const recordPageType = record.pageType || pageType || 'default';
      const historyKey = `upload_history_${record.type || type}_${recordPageType}`;
      const historyJson = localStorage.getItem(historyKey);
      if (historyJson) {
        try {
          history = JSON.parse(historyJson);
          if (!Array.isArray(history)) {
            console.warn('历史记录格式无效，重置为空数组');
            history = [];
          }
        } catch (e) {
          console.warn('解析历史记录JSON失败，重置为空数组', e);
          history = [];
        }
      }

      // 检查是否已存在相同文件名的记录
      const existingIndex = history.findIndex(item => 
        item.fileName === record.fileName && 
        (item.type === record.type || (!item.type && !record.type)) &&
        (item.pageType === recordPageType || (!item.pageType && !recordPageType))
      );
      
      if (existingIndex !== -1) {
        console.log(`找到已存在的记录 [${existingIndex}]，文件名: ${record.fileName}，更新而不是添加新记录`);
        
        // 更新现有记录的服务器URL和缩略图URL（如果有的话）
        const existingRecord = history[existingIndex];
        
        // 保留原始ID，更新其他信息
        record.id = existingRecord.id;
        
        // 如果没有新的服务器URL，保留旧的
        if (!record.serverUrl && existingRecord.serverUrl) {
          record.serverUrl = existingRecord.serverUrl;
        }
        
        // 从历史记录中移除旧记录
        history.splice(existingIndex, 1);
      } else {
        console.log(`未找到相同文件名的记录，添加新记录: ${record.fileName}`);
        
        // 如果记录已经达到最大数量，移除最早的记录
        if (history.length >= MAX_HISTORY_COUNT) {
          const removed = history.pop();
          console.log(`历史记录达到最大限制 ${MAX_HISTORY_COUNT}，移除最旧记录: ${removed?.fileName || '未知'}`);
        }
      }

      // 将记录（新的或更新的）添加到数组开头（最新的在前面）
      // 删除文件对象，避免序列化错误
      const recordToSave = { ...record };
      delete recordToSave.file; // File对象不能序列化，需要删除
      
      history.unshift(recordToSave);

      // 保存回localStorage
      localStorage.setItem(historyKey, JSON.stringify(history));
      console.log(`成功保存历史记录，当前共有 ${history.length} 条记录`);
      return true;
    } catch (err) {
      console.error('保存到历史记录出错:', err);
      return false;
    }
  };

  // 新增：历史记录视图组件
  const HistoryView = () => {
    // 记录图片加载错误的状态
    const [errorImages, setErrorImages] = useState({});
    // 添加选中图片状态
    const [selectedRecords, setSelectedRecords] = useState({});
    
    // 增加调试信息显示
    useEffect(() => {
      // 输出历史记录的概要信息，帮助调试
      console.log(`历史记录概要: 共${uploadHistory.length}条记录`);
      uploadHistory.forEach((record, index) => {
        const urlType = record.url 
          ? (record.url.startsWith('blob:') ? 'Blob URL' 
             : record.url.startsWith('data:') ? 'Data URL' 
             : 'Other URL') 
          : '无URL';
        const thumbnailType = record.thumbnailUrl 
          ? (record.thumbnailUrl.startsWith('data:') ? 'Data URL' : 'Other URL') 
          : '无缩略图';
        
        console.log(`记录#${index + 1}: ${record.fileName || '未知文件名'}, URL类型: ${urlType}, 缩略图: ${thumbnailType}, 服务器URL: ${record.serverUrl ? '有' : '无'}`);
      });
    }, [uploadHistory]);
    
    // 处理图片加载错误
    const handleImageError = (recordId) => {
      const record = uploadHistory.find(r => r.id === recordId);
      console.error(`历史记录图片加载失败: ${recordId}`, record ? {
        url类型: record.url ? (record.url.startsWith('blob:') ? 'Blob URL' : record.url.startsWith('data:') ? 'Data URL' : 'Other URL') : '无',
        thumbnailUrl类型: record.thumbnailUrl ? (record.thumbnailUrl.startsWith('data:') ? 'Data URL' : 'Other URL') : '无',
        serverUrl: record.serverUrl ? '有服务器URL' : '无服务器URL',
        fileName: record.fileName || '未知'
      } : '未找到记录');
      
      // 尝试修复记录
      if (record) {
        // 创建一个副本
        const updatedRecord = { ...record };
        let needsUpdate = false;
        
        // 如果url是Blob URL并且失效了，尝试使用其他URL
        if (record.url && record.url.startsWith('blob:')) {
          console.log('检测到失效的Blob URL，尝试修复');
          if (record.thumbnailUrl) {
            updatedRecord.url = record.thumbnailUrl;
            needsUpdate = true;
          } else if (record.serverUrl) {
            updatedRecord.url = record.serverUrl;
            needsUpdate = true;
          }
        }
        
        // 如果thumbnailUrl也是Blob URL，尝试移除它
        if (record.thumbnailUrl && record.thumbnailUrl.startsWith('blob:')) {
          console.log('检测到失效的Blob URL作为thumbnailUrl，移除它');
          delete updatedRecord.thumbnailUrl;
          needsUpdate = true;
        }
        
        // 如果需要更新记录
        if (needsUpdate) {
          console.log('尝试修复历史记录:', updatedRecord);
          
          // 更新内存中的记录
          const newHistory = uploadHistory.map(r => 
            r.id === recordId ? updatedRecord : r
          );
          setUploadHistory(newHistory);
          
          // 更新localStorage中的记录
          try {
            const historyKey = `upload_history_${type}_${pageType || 'default'}`;
            localStorage.setItem(historyKey, JSON.stringify(newHistory));
            console.log('已更新修复后的历史记录到localStorage');
            return; // 提前返回，不要将这个记录标记为错误
          } catch (error) {
            console.error('更新localStorage中的历史记录失败:', error);
          }
        }
      }
      
      // 如果无法修复，或没有尝试修复，标记为错误图片
      setErrorImages(prev => ({ ...prev, [recordId]: true }));
      
      // 如果图片加载失败，从选中状态中移除
      if (selectedRecords[recordId]) {
        const newSelected = { ...selectedRecords };
        delete newSelected[recordId];
        setSelectedRecords(newSelected);
      }
    };
    
    // 删除历史记录
    const handleDeleteRecord = (recordId) => {
      console.log(`删除历史记录: ${recordId}`);
      
      // 从状态中删除记录
      const newHistory = uploadHistory.filter(record => record.id !== recordId);
      setUploadHistory(newHistory);
      
      // 从选择状态中移除
      if (selectedRecords[recordId]) {
        const newSelected = { ...selectedRecords };
        delete newSelected[recordId];
        setSelectedRecords(newSelected);
      }
      
      // 从localStorage中更新记录
      try {
        const historyKey = `upload_history_${type}_${pageType || 'default'}`;
        localStorage.setItem(historyKey, JSON.stringify(newHistory));
        console.log('历史记录已从localStorage中删除');
      } catch (error) {
        console.error('删除历史记录时出错:', error);
      }
    };
    
    // 切换选择状态
    const toggleSelectRecord = (record, event) => {
      // 阻止事件冒泡，避免触发其他点击事件
      event.stopPropagation();
      
      console.log(`切换选择状态: ${record.id}`);
      
      // 参考图片类型不允许多选
      const allowMultiSelect = false; // 参考图片不支持多选
      console.log(`是否允许多选: ${allowMultiSelect}`, {pageType, type});
      
      setSelectedRecords(prev => {
        const newSelected = { ...prev };
        
        if (newSelected[record.id]) {
          // 如果已经选中，则取消选择
          delete newSelected[record.id];
        } else {
          // 如果未选中
          
          // 如果不允许多选，先清除之前的选择
          if (!allowMultiSelect) {
            // 清空之前的选择，实现单选效果
            Object.keys(newSelected).forEach(key => {
              delete newSelected[key];
            });
          }
          
          // 添加到选中列表
          newSelected[record.id] = record;
        }
        
        return newSelected;
      });
    };
    
    // 选择全部有效记录
    const selectAllRecords = () => {
      // 参考图片不支持全选
      console.log('当前模式不支持多选，无法全选');
    };
    
    // 清除所有选择
    const clearAllSelections = () => {
      setSelectedRecords({});
    };
    
    // 将选中的图片发送到画廊
    const sendSelectedToGallery = async () => {
      const selectedCount = Object.keys(selectedRecords).length;
      if (selectedCount === 0) {
        message.warning('请先选择要使用的图片');
        return;
      }
      
      console.log(`准备使用${selectedCount}张选中的图片`);
      
      // 切换到上传视图并进入画廊模式
      setActiveTab('guide');
      setUploadView('gallery');
      
      // 清空现有的上传文件列表
      setUploadedFiles([]);
      
      // 处理每个选中的记录
      const selectedArray = Object.values(selectedRecords);
      for (const record of selectedArray) {
        try {
          console.log(`处理选中的图片: ${record.id}`);
          
          // 创建文件对象
          let file = null;
          
          // 优先尝试从服务器URL获取图片
          if (record.serverUrl) {
            console.log('从服务器URL获取图片:', record.serverUrl.substring(0, 30) + '...');
            try {
              // 检查URL，确保使用正确的服务器地址
              let imageUrl = record.serverUrl;
              
              // 如果URL不是完整的URL（没有http前缀），添加前缀
              if (!imageUrl.startsWith('http')) {
                imageUrl = `${SERVER_BASE_URL}${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`;
              }
              
              // 如果URL使用的是3001端口但应该使用3002端口，替换端口号
              if (imageUrl.includes('localhost:3001')) {
                // 从SERVER_BASE_URL中获取正确的主机和端口
                const baseUrlParts = SERVER_BASE_URL.match(/^(https?:\/\/[^:\/]+)(:\d+)?/);
                if (baseUrlParts) {
                  const host = baseUrlParts[1];
                  const port = baseUrlParts[2] || '';
                  imageUrl = imageUrl.replace(/http:\/\/localhost:3001/, `${host}${port}`);
                  console.log('修复URL为正确服务器:', imageUrl.substring(0, 30) + '...');
                }
              }
              
              const response = await fetch(imageUrl);
              if (!response.ok) {
                throw new Error(`获取服务器图片失败: ${response.status} ${response.statusText}`);
              }
              
              const blob = await response.blob();
              if (!blob || blob.size === 0) {
                throw new Error('获取到的服务器图片数据为空');
              }
              
              const fileType = record.fileType || blob.type || 'image/jpeg';
              file = new File([blob], record.fileName || 'image.jpg', { type: fileType });
              
              console.log('成功从服务器创建文件对象:', {
                name: file.name,
                size: file.size,
                type: file.type
              });
            } catch (error) {
              console.error('从服务器获取图片失败，尝试使用缩略图:', error);
              file = null; // 重置file以便使用缩略图
            }
          }
          
          // 如果从服务器获取失败，尝试使用缩略图
          if (!file) {
            const imgUrl = record.thumbnailUrl || record.url;
            if (imgUrl && imgUrl.startsWith('data:')) {
              // 如果是dataURL，直接转换为Blob
              const byteString = atob(imgUrl.split(',')[1]);
              const mimeType = imgUrl.split(',')[0].split(':')[1].split(';')[0];
              const ab = new ArrayBuffer(byteString.length);
              const ia = new Uint8Array(ab);
              
              for (let i = 0; i < byteString.length; i++) {
                ia[i] = byteString.charCodeAt(i);
              }
              
              const blob = new Blob([ab], { type: mimeType });
              const fileType = record.fileType || blob.type || 'image/jpeg';
              file = new File([blob], record.fileName || 'image.jpg', { type: fileType });
              
              console.log('从缩略图创建文件对象:', {
                name: file.name,
                size: file.size,
                type: file.type
              });
            } else if (imgUrl) {
              // 如果是普通URL，通过fetch获取
              const response = await fetch(imgUrl);
              if (!response.ok) {
                throw new Error(`获取图片失败: ${response.status} ${response.statusText}`);
              }
              
              const blob = await response.blob();
              if (!blob || blob.size === 0) {
                throw new Error('获取到的图片数据为空');
              }
              
              const fileType = record.fileType || blob.type || 'image/jpeg';
              file = new File([blob], record.fileName || 'image.jpg', { type: fileType });
            } else {
              throw new Error('历史记录中没有有效的图片URL');
            }
          }
          
          // 检查文件有效性
          if (!file) {
            throw new Error('无法从记录创建有效的文件对象');
          }
          
          // 为从历史记录选择的图片创建上传对象
          const uploadedFile = {
            id: Date.now().toString() + Math.random().toString(36).slice(2),
            file,
            url: URL.createObjectURL(file)
          };
          
          // 处理上传历史记录中选择的图片
          handleFileUpload(file);
          
          // 清除旧的上传文件列表
          setUploadedFiles([]);
          
        } catch (error) {
          console.error('处理选中图片时出错:', error);
          message.error(`处理图片 ${record.fileName || '未知'} 失败: ${error.message}`);
        }
      }
      
      // 清除选择状态
      setSelectedRecords({});
    };
    
    // 过滤出有效的历史记录（不包括加载错误的图片）
    const validRecords = uploadHistory.filter(record => !errorImages[record.id]);
    const selectedCount = Object.keys(selectedRecords).length;
    
    // 清空所有历史记录
    const clearAllHistory = () => {
      console.log('清空所有历史记录');
      // 从状态中清空历史记录
      setUploadHistory([]);
      // 清空选中状态
      setSelectedRecords({});
      // 清空错误状态
      setErrorImages({});
      
      // 从localStorage中删除历史记录
      try {
        const historyKey = `upload_history_${type}_${pageType || 'default'}`;
        localStorage.removeItem(historyKey);
        console.log('已从localStorage中清空历史记录');
      } catch (error) {
        console.error('清空历史记录时出错:', error);
      }
    };
    
    // 清理失效的历史记录
    const cleanupInvalidRecords = () => {
      console.log('开始清理失效的历史记录');
      
      // 过滤出有效记录(不在errorImages中的)
      const validRecordsArray = uploadHistory.filter(record => !errorImages[record.id]);
      
      // 如果没有无效记录，不进行操作
      if (validRecordsArray.length === uploadHistory.length) {
        console.log('没有检测到失效的记录，无需清理');
        return;
      }
      
      // 更新状态
      setUploadHistory(validRecordsArray);
      
      // 从选中记录中移除无效记录
      const newSelected = { ...selectedRecords };
      Object.keys(newSelected).forEach(id => {
        if (errorImages[id]) {
          delete newSelected[id];
        }
      });
      setSelectedRecords(newSelected);
      
      // 更新localStorage
      try {
        const historyKey = `upload_history_${type}_${pageType || 'default'}`;
        localStorage.setItem(historyKey, JSON.stringify(validRecordsArray));
        console.log(`清理完成，移除了${uploadHistory.length - validRecordsArray.length}条失效记录，剩余${validRecordsArray.length}条`);
      } catch (error) {
        console.error('更新localStorage失败:', error);
      }
    };
    
    return (
      <div className="history-view">
        <div className="gallery-grid">
          {validRecords.length > 0 ? (
            validRecords.map(record => (
              <div 
                key={record.id} 
                className={`gallery-item ${selectedRecords[record.id] ? 'selected' : ''}`}
                onClick={(e) => toggleSelectRecord(record, e)}
              >
                <img 
                  src={record.thumbnailUrl || record.url || record.serverUrl || ''} 
                  alt={record.fileName} 
                  onError={() => handleImageError(record.id)}
                />
                {/* 添加时间角标 */}
                {record.saveTime && (
                  <div className="time-badge">
                    {formatDate(record.saveTime)}
                  </div>
                )}
                <div className="item-overlay">
                  {/* 居中的添加按钮 */}
                  <button 
                    className="use-button centered"
                    onClick={(e) => {
                      e.stopPropagation(); // 阻止事件冒泡，避免触发选择
                      console.log('尝试使用历史记录图片:', {
                        fileName: record.fileName,
                        fileType: record.fileType,
                        serverUrl: record.serverUrl ? (record.serverUrl.substring(0, 30) + '...') : '无服务器URL'
                      });
                      
                      try {
                        // 修改逻辑：优先使用服务器URL获取原始图片
                        if (record.serverUrl) {
                          console.log('优先使用服务器原始图片:', record.serverUrl.substring(0, 30) + '...');
                          tryUseServerImage();
                        } else {
                          // 如果没有服务器URL，再尝试使用本地URL
                          console.log('服务器URL不可用，回退到使用本地图片');
                          const localImageUrl = record.url;
                          if (localImageUrl) {
                            console.log('使用本地图片URL:', localImageUrl.substring(0, 30) + '...');
                            
                            // 处理dataURL（如果是）
                            if (localImageUrl.startsWith('data:')) {
                              processDataUrlOrFallback(localImageUrl);
                            } else {
                              // 处理普通URL
                              fetch(localImageUrl)
                                .then(response => {
                                  if (!response.ok) {
                                    throw new Error(`获取本地图片失败: ${response.status} ${response.statusText}`);
                                  }
                                  return response.blob();
                                })
                                .then(blob => {
                                  if (!blob || blob.size === 0) {
                                    throw new Error('获取到的本地图片数据为空');
                                  }
                                  
                                  console.log('成功获取本地图片:', {
                                    size: blob.size,
                                    type: blob.type
                                  });
                                  
                                  const fileType = record.fileType || blob.type || 'image/jpeg';
                                  const file = new File([blob], record.fileName || 'image.jpg', { type: fileType });
                                  
                                  // 切换到上传视图
                                  setActiveTab('guide');
                                  
                                  // 处理文件上传
                                  handleFileUpload(file);
                                })
                                .catch(error => {
                                  console.error('获取本地图片失败，尝试使用缩略图:', error);
                                  // 如果本地URL失败，尝试使用缩略图
                                  processDataUrlOrFallback(record.thumbnailUrl);
                                });
                            }
                          } else {
                            // 如果没有本地URL，尝试使用缩略图
                            processDataUrlOrFallback(record.thumbnailUrl);
                          }
                        }
                        
                        // 尝试使用服务器图片的辅助函数
                        function tryUseServerImage() {
                          // 使用服务器上的图片URL
                          console.log('使用服务器图片URL:', record.serverUrl);
                          
                          // 检查URL，确保使用正确的服务器地址
                          let imageUrl = record.serverUrl;
                          
                          // 如果URL包含uploads路径，尝试使用直接访问
                          if (imageUrl.includes('/storage/developer/uploads/')) {
                            // 转换为直接访问路径
                            const fileName = imageUrl.split('/').pop();
                            imageUrl = `${SERVER_BASE_URL}/direct-uploads/${fileName}`;
                            console.log('转换为直接访问路径:', imageUrl);
                          }
                          // 处理可能的相对路径
                          else if (imageUrl.startsWith('/storage/')) {
                            imageUrl = `${SERVER_BASE_URL}${imageUrl}`;
                            console.log('处理storage路径:', imageUrl);
                          }
                          
                          // 如果URL不是完整的URL（没有http前缀），添加前缀
                          if (!imageUrl.startsWith('http')) {
                            imageUrl = `${SERVER_BASE_URL}${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`;
                          }
                          
                          // 如果URL使用的是3001端口但应该使用3002端口，替换端口号
                          if (imageUrl.includes('localhost:3001')) {
                            // 从SERVER_BASE_URL中获取正确的主机和端口
                            const baseUrlParts = SERVER_BASE_URL.match(/^(https?:\/\/[^:\/]+)(:\d+)?/);
                            if (baseUrlParts) {
                              const host = baseUrlParts[1];
                              const port = baseUrlParts[2] || '';
                              imageUrl = imageUrl.replace(/http:\/\/localhost:3001/, `${host}${port}`);
                              console.log('修复URL为正确服务器:', imageUrl);
                            }
                          }
                          
                          console.log('最终请求的URL:', imageUrl);
                          
                          fetch(imageUrl)
                            .then(response => {
                              if (!response.ok) {
                                console.error(`获取服务器图片失败: ${response.status} ${response.statusText}`);
                                throw new Error(`获取服务器图片失败: ${response.status} ${response.statusText}`);
                              }
                              return response.blob();
                            })
                            .then(blob => {
                              if (!blob || blob.size === 0) {
                                throw new Error('获取到的服务器图片数据为空');
                              }
                              
                              console.log('成功从服务器获取图片:', {
                                size: blob.size,
                                type: blob.type
                              });
                              
                              const fileType = record.fileType || blob.type || 'image/jpeg';
                              const file = new File([blob], record.fileName || 'image.jpg', { type: fileType });
                              
                              console.log('从服务器URL创建的File对象:', {
                                name: file.name,
                                size: file.size,
                                type: file.type
                              });
                              
                              // 切换到上传视图
                              setActiveTab('guide');
                              
                              // 处理文件上传
                              handleFileUpload(file);
                            })
                            .catch(error => {
                              console.error('从服务器获取图片失败，尝试使用本地图片:', error);
                              // 服务器URL失败时，尝试使用本地图片
                              const localImageUrl = record.url;
                              if (localImageUrl) {
                                console.log('回退到使用本地图片URL');
                                if (localImageUrl.startsWith('data:')) {
                                  processDataUrlOrFallback(localImageUrl);
                                } else {
                                  fetch(localImageUrl)
                                    .then(response => response.blob())
                                    .then(blob => {
                                      const fileType = record.fileType || blob.type || 'image/jpeg';
                                      const file = new File([blob], record.fileName || 'image.jpg', { type: fileType });
                                      setActiveTab('guide');
                                      handleFileUpload(file);
                                    })
                                    .catch(err => {
                                      console.error('本地图片获取失败，尝试使用缩略图:', err);
                                      processDataUrlOrFallback(record.thumbnailUrl);
                                    });
                                }
                              } else {
                                // 本地图片也不可用，使用缩略图
                                processDataUrlOrFallback(record.thumbnailUrl);
                              }
                            });
                        }
                        
                        // 从缩略图或旧格式URL创建文件对象的辅助函数
                        function processDataUrlOrFallback(imgUrl) {
                          // 处理dataURL（无论是缩略图还是完整图像）
                          if (imgUrl && imgUrl.startsWith('data:')) {
                            // 如果是dataURL，直接转换为Blob
                            const byteString = atob(imgUrl.split(',')[1]);
                            const mimeType = imgUrl.split(',')[0].split(':')[1].split(';')[0];
                            const ab = new ArrayBuffer(byteString.length);
                            const ia = new Uint8Array(ab);
                            
                            for (let i = 0; i < byteString.length; i++) {
                              ia[i] = byteString.charCodeAt(i);
                            }
                            
                            const blob = new Blob([ab], { type: mimeType });
                            console.log('从图片URL创建Blob (回退方案):', {
                              size: blob.size,
                              type: blob.type
                            });
                            
                            // 创建文件对象
                            const fileType = record.fileType || blob.type || 'image/jpeg';
                            const file = new File([blob], record.fileName || 'image.jpg', { type: fileType });
                            
                            console.log('从图片URL创建的File对象:', {
                              name: file.name,
                              size: file.size,
                              type: file.type
                            });
                            
                            // 切换到上传视图
                            setActiveTab('guide');
                            
                            // 处理文件上传
                            handleFileUpload(file);
                          } else if (imgUrl) {
                            // 普通URL，通过fetch获取
                            fetch(imgUrl)
                              .then(response => {
                                if (!response.ok) {
                                  throw new Error(`获取图片失败: ${response.status} ${response.statusText}`);
                                }
                                return response.blob();
                              })
                              .then(blob => {
                                if (!blob || blob.size === 0) {
                                  throw new Error('获取到的图片数据为空');
                                }
                                
                                console.log('成功获取到Blob数据:', {
                                  size: blob.size, 
                                  type: blob.type
                                });
                                
                                const fileType = record.fileType || blob.type || 'image/jpeg';
                                const file = new File([blob], record.fileName || 'image.jpg', { type: fileType });
                                
                                console.log('创建的File对象:', {
                                  name: file.name,
                                  size: file.size,
                                  type: file.type
                                });
                                
                                setActiveTab('guide');
                                handleFileUpload(file);
                              })
                              .catch(error => {
                                console.error('处理历史记录图片时出错:', error);
                                message.error(`处理历史记录图片失败: ${error.message}，请重试`);
                                handleImageError(record.id);
                              });
                          } else {
                            throw new Error('历史记录中没有有效的图片URL');
                          }
                        }
                      } catch (error) {
                        console.error('处理历史记录图片时出错:', error);
                        message.error(`处理历史记录图片失败: ${error.message}，请重试`);
                        handleImageError(record.id);
                      }
                    }}
                  >
                    使用
                  </button>
                  
                  {/* 右上角的删除按钮 */}
                  <button 
                    className="remove-btn"
                    onClick={(e) => {
                      e.stopPropagation(); // 阻止事件冒泡，避免触发选择
                      handleDeleteRecord(record.id);
                    }}
                  >
                    <MdClose />
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="no-history">
              {uploadHistory.length > 0 && errorImages && Object.keys(errorImages).length === uploadHistory.length
                ? '所有历史记录图片已失效'
                : '暂无历史记录'
              }
            </div>
          )}
        </div>
        
        {/* 添加底边条 */}
        <div className="gallery-footer">
          <div className="file-count">
            {/* 所有类型只能单选 */}
            <>已选择 {selectedCount} / 1 张（此类型仅支持单选）</>
          </div>
          <div className="gallery-actions">
            <button 
              className="clear-btn"
              onClick={clearAllHistory}
              disabled={uploadHistory.length === 0}
            >
              清空记录
            </button>
            {Object.keys(errorImages).length > 0 && (
              <button
                className="clear-btn"
                onClick={cleanupInvalidRecords}
              >
                清理失效记录({Object.keys(errorImages).length})
              </button>
            )}
            <button 
              className="confirm-button"
              onClick={sendSelectedToGallery}
              disabled={selectedCount === 0}
            >
              {selectedCount > 0 ? `使用选中图片(${selectedCount})` : '选择图片后使用'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 获取相应类型的推荐示例
  const getRecommendedExamples = () => {
    let examples;
    
    // 根据pageType优先选择对应的示例配置
    if (pageType === 'change-model' && type === 'clothing') {
      examples = UPLOAD_GUIDE_CONFIG.changeModelRecommendedExamples;
    } else if (pageType === 'try-on' && type === 'clothing') {
      examples = UPLOAD_GUIDE_CONFIG.tryOnRecommendedExamples;
    } else if (pageType === 'try-on-other' && type === 'clothing') {
      examples = UPLOAD_GUIDE_CONFIG.tryOnOtherRecommendedExamples || UPLOAD_GUIDE_CONFIG.tryOnRecommendedExamples;
    } else if (pageType === 'recolor' && (type === 'clothing' || type === 'recolorClothing')) {
      examples = UPLOAD_GUIDE_CONFIG.recolorRecommendedExamples;
    } else if (pageType === 'fabric' && type === 'clothing') {
      examples = UPLOAD_GUIDE_CONFIG.fabricRecommendedExamples;
    } else if (pageType === 'fabric' && type === 'fabric') {
      examples = UPLOAD_GUIDE_CONFIG.fabricMaterialRecommendedExamples;
    } else if (pageType === 'drawing' && type === 'clothing') {
      // 线稿生成页面使用换面料页面的示例卡片
      examples = UPLOAD_GUIDE_CONFIG.fabricRecommendedExamples;
    } else if (type === 'model') {
      examples = UPLOAD_GUIDE_CONFIG.modelRecommendedExamples;
    } else if (type === 'foreground' || type === 'virtual') {
      examples = UPLOAD_GUIDE_CONFIG.foregroundRecommendedExamples;
    } else if (type === 'printing') {
      // 面料印花图片专用示例
      examples = UPLOAD_GUIDE_CONFIG.printingRecommendedExamples;
    } else if (type === 'design') {
      // 款式图片专用示例
      examples = UPLOAD_GUIDE_CONFIG.designRecommendedExamples;
    } else if (type === 'pattern') {
      // 版型图片专用示例
      examples = UPLOAD_GUIDE_CONFIG.trendingRecommendedExamples || UPLOAD_GUIDE_CONFIG.patternRecommendedExamples;
    } else if (type === 'reference') {
      // 时尚大片页面的参考图片专用示例
      examples = UPLOAD_GUIDE_CONFIG.fashionReferenceRecommendedExamples;
    } else {
      // 默认返回换模特页面示例（作为通用服装示例）
      examples = UPLOAD_GUIDE_CONFIG.changeModelRecommendedExamples;
    }
    
    // 确保返回值是数组类型
    return Array.isArray(examples) ? examples : [];
  };

  // 获取相应类型的错误示例
  const getErrorExamples = () => {
    let examples;
    
    // 根据pageType优先选择对应的示例配置
    if (pageType === 'change-model' && type === 'clothing') {
      examples = UPLOAD_GUIDE_CONFIG.changeModelErrorExamples;
    } else if (pageType === 'try-on' && type === 'clothing') {
      examples = UPLOAD_GUIDE_CONFIG.tryOnErrorExamples;
    } else if (pageType === 'try-on-other' && type === 'clothing') {
      examples = UPLOAD_GUIDE_CONFIG.tryOnOtherErrorExamples || UPLOAD_GUIDE_CONFIG.tryOnErrorExamples;
    } else if (pageType === 'recolor' && (type === 'clothing' || type === 'recolorClothing')) {
      examples = UPLOAD_GUIDE_CONFIG.recolorErrorExamples;
    } else if (pageType === 'fabric' && type === 'clothing') {
      examples = UPLOAD_GUIDE_CONFIG.fabricErrorExamples;
    } else if (pageType === 'fabric' && type === 'fabric') {
      examples = UPLOAD_GUIDE_CONFIG.fabricMaterialErrorExamples;
    } else if (pageType === 'drawing' && type === 'clothing') {
      // 线稿生成页面使用换面料页面的错误示例卡片
      examples = UPLOAD_GUIDE_CONFIG.fabricErrorExamples;
    } else if (type === 'model') {
      examples = UPLOAD_GUIDE_CONFIG.modelErrorExamples;
    } else if (type === 'foreground' || type === 'virtual') {
      examples = UPLOAD_GUIDE_CONFIG.foregroundErrorExamples;
    } else if (type === 'printing') {
      // 面料印花图片专用示例
      examples = UPLOAD_GUIDE_CONFIG.printingErrorExamples;
    } else if (type === 'design') {
      // 款式图片专用示例
      examples = UPLOAD_GUIDE_CONFIG.designErrorExamples;
    } else if (type === 'pattern') {
      // 版型图片专用示例
      examples = UPLOAD_GUIDE_CONFIG.trendingErrorExamples || UPLOAD_GUIDE_CONFIG.patternErrorExamples;
    } else if (type === 'reference') {
      // 时尚大片页面的参考图片错误示例
      examples = UPLOAD_GUIDE_CONFIG.fashionReferenceErrorExamples;
    } else {
      // 默认返回换模特页面示例（作为通用服装示例）
      examples = UPLOAD_GUIDE_CONFIG.changeModelErrorExamples;
    }
    
    // 确保返回值是数组类型
    return Array.isArray(examples) ? examples : [];
  };

  // 根据type和pageType获取相应的角标文本
  const getTagText = () => {
    if (pageType === 'detail-migration') return '服装原图';
    if (type === 'model') {
      return '模特图';
    } else if (type === 'virtual') {
      return '人物照';
    } else if (type === 'foreground') {
      return '前景图';
    } else if (type === 'recolorClothing') {
      return '服装图';
    } else if (type === 'fabric') {
      return '面料图';
    } else if (type === 'design') {
      return '款式图';
    } else if (type === 'reference') {
      return '参考图';
    } else if (type === 'pattern') {
      return '版型图';
    } else if (type === 'printing') {
      return '面料印花图';
    } else {
      // 默认标签
      return '服装图';
    }
  };

  // 根据type获取相应的标题
  const getTitle = () => {
    if (pageType === 'detail-migration') return '上传服装原图';
    if (type === 'model') {
      return '上传模特图片';
    } else if (type === 'virtual') {
      return '上传人物照片';
    } else if (type === 'foreground') {
      return '上传前景图片';
    } else if (type === 'recolorClothing') {
      return '上传服装图片';
    } else if (type === 'fabric') {
      return '上传面料图片';
    } else if (type === 'printing') {
      return '上传面料印花参考图片';
    } else if (type === 'design') {
      return '上传款式图片';
    } else if (type === 'pattern') {
      return '上传版型参考图片';
    } else if (type === 'reference') {
      return '上传参考图片';
    } else {
      // 默认标题
      return '上传服装图片';
    }
  };

  // 获取上传类型显示名称
  const getUploadTypeText = () => {
    if (pageType === 'detail-migration') return '服装原图';
    if (type === 'reference') return '参考图片';
    if (type === 'model') return '模特图片';
    if (type === 'foreground') return '前景图片';
    if (type === 'pattern') return '版型图片';
    if (type === 'printing') return '面料印花';
    if (type === 'fabric') return '面料图片';
    if (type === 'design') return '款式图片';
    if (type === 'source') return '原始图片';
    return '服装';
  };
  
  // 获取弹窗标题
  const getModalTitle = () => {
    if (pageType === 'detail-migration') return '上传服装原图';
    if (type === 'reference') return '上传参考图片';
    if (type === 'model') return '上传模特图片';
    if (type === 'foreground') return '上传前景图片';
    if (type === 'pattern') return '上传版型图片';
    if (type === 'printing') return '上传面料印花图片';
    if (type === 'fabric') return '上传面料图片';
    if (type === 'design') return '上传款式图片';
    if (type === 'source') return '上传原始图片';
    return '上传服装图片';
  };

  // 获取拖放区文本
  const getDragText = () => {
    if (type === 'clothing' && pageType === 'try-on') return '或 拖拽服装图片到此处';
    if (type === 'reference') return '或 拖拽参考图片到此处';
    if (type === 'model') return '或 拖拽模特照片到此处';
    if (type === 'foreground') return '或 拖拽前景图片到此处';
    if (type === 'pattern') return '或 拖拽版型图片到此处';
    if (type === 'printing') return '或 拖拽面料印花图片到此处';
    if (type === 'fabric') return '或 拖拽面料图片到此处';
    if (type === 'design') return '或 拖拽款式图片到此处';
    if (type === 'source') return '或 拖拽原始图片到此处';
    return '或 拖拽图片到此处';
  };

  // 清理函数 - 在关闭弹窗时调用
  const resetState = () => {
    setUploadView('guide');
    setUploadedFiles([]);
    setUploadingFiles({});
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    
    // 首先检查是否有自定义的拖拽数据（从TaskPanel拖拽过来的图片）
    try {
      const customData = e.dataTransfer.getData('application/json');
      if (customData) {
        const dragData = JSON.parse(customData);
        if (dragData.type === 'image-upload') {
          // 处理从TaskPanel拖拽过来的图片
          handleImageFromTaskPanel(dragData);
          return;
        }
      }
    } catch (error) {
      console.log('解析拖拽数据失败，尝试处理文件:', error);
    }
    
    const files = Array.from(e.dataTransfer.files);
    handleFilesUpload(files);
  };

  // 处理从TaskPanel拖拽过来的图片
  const handleImageFromTaskPanel = async (dragData) => {
    try {
      const { imageUrl, fileName, taskId, imageIndex } = dragData;
      
      // 将HTTP转换为HTTPS，解决混合内容问题
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      
      // 从URL获取图片数据
      const response = await fetch(httpsUrl);
      if (!response.ok) {
        throw new Error('无法获取图片数据');
      }
      
      const blob = await response.blob();
      
      // 创建一个File对象
      const file = new File([blob], fileName, {
        type: blob.type || 'image/jpeg',
        lastModified: Date.now()
      });
      
      console.log(`处理从任务面板拖拽的图片: ${fileName}, 任务ID: ${taskId}, 图片索引: ${imageIndex}`);
      
      // 对从TaskPanel拖拽过来的图片进行校验
      const validation = await validateFile(file);
      if (!validation.isValid) {
        message.error(validation.error);
        return;
      }
      
      // 使用现有的文件上传逻辑
      handleFileUpload(file);
      
      // message.success('图片已添加到上传区域');
      
    } catch (error) {
      console.error('处理拖拽图片时出错:', error);
      message.error('处理图片失败，请重试');
    }
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    handleFilesUpload(files);
  };

  const handleFilesUpload = (files) => {
    // 如果是模特换装页面的服装上传，只允许选择一张图片
    if (pageType === 'try-on' && type === 'clothing' && files.length > 1) {
      message.warning('模特换装页面只能上传一张服装图片');
      return;
    }
    
    // 参考图片只能上传一张
    if (type === 'reference' && files.length > 1) {
      message.warning('只能上传一张参考图片');
      return;
    }
    
    // 爆款开发页面的版型上传，也只允许上传一张图片
    if (type === 'pattern' && files.length > 1) {
      message.warning('只能上传一张版型参考图片');
      return;
    }
    
    // 模特图片也只允许上传一张
    if (type === 'model' && files.length > 1) {
      message.warning('只能上传一张模特图片');
      return;
    }

    // 检查是否超出最大上传数量
    if (uploadedFiles.length + files.length > 9) {
      message.warning('最多支持上传9张图片');
      return;
    }

    // 处理每个文件
    files.forEach(file => {
      handleFileUpload(file);
    });
  };

  const handleFileUpload = async (file) => {
    // 使用统一的文件校验函数
    const validation = await validateFile(file);
    if (!validation.isValid) {
      message.error(validation.error);
      return;
    }

    console.log(`开始处理文件上传: ${file.name}, 会话ID: ${sessionId}`);

    // 处理模特图片上传（需要蒙版）
    if (type === 'model') {
      try {
        // 创建唯一ID，使用与服务器一致的格式，不使用前缀
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 10);
        const imageId = `${timestamp}-${random}`;
        const imageUrl = URL.createObjectURL(file);
        
        // 创建完成状态的面板数据（直接使用原始图片）
        const completedPanel = {
          id: imageId,
          title: '模特原图',
          status: 'completed',
          serverFileName: file.name, // 替换为serverFileName
          url: imageUrl, // 直接使用本地URL
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type,
            serverFileName: file.name // 在fileInfo中也设置serverFileName
          },
          type: 'model',      // 设置业务类型为模特
          source: 'upload',    // 设置来源为用户上传
          file: file, // 保存原始文件对象，供后续上传使用
          // 确保在processInfo中也设置serverFileName
          processInfo: {
            serverFileName: file.name
          }
        };
        
        // 通知父组件创建完成的面板
        console.log(`通知父组件创建面板: ${imageId}, 会话ID: ${sessionId}`);
        safeOnUpload({ type: 'panels', panels: [completedPanel] });
        
        // 重置上传区状态，但不关闭弹窗
        resetState();
        
        // 不立即上传到服务器，而是在点击生成按钮时再上传
        console.log('模特图片上传完成（本地预览），关闭弹窗');
        if (typeof onClose === 'function') {
          onClose();
        }
        return;
      } catch (error) {
        console.error(`处理模特图片时出错:`, error);
        safeOnUpload({ 
          type: 'error', 
          error: error.message 
        });
        return;
      }
    }
    
    // 处理前景图片、虚拟模特图片上传
    if (type === 'foreground' || type === 'virtual' || type === 'clothing' || (pageType === 'trending' && (type === 'pattern' || type === 'printing'))) {
      try {
        // 创建唯一ID
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 10);
        const imageId = `${timestamp}-${random}`;
        const imageUrl = URL.createObjectURL(file);
        
        // 创建完成状态的面板数据
        const completedPanel = {
          componentId: imageId,
          title: type === 'virtual' ? '人物照' : 
                 type === 'foreground' ? '前景图' : 
                 type === 'pattern' ? '版型图' : 
                 type === 'clothing' ? '服装' : '印花图',
          status: 'completed',
          serverFileName: file.name, // 替换为serverFileName
          url: imageUrl, // 直接使用本地URL
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type,
            serverFileName: file.name // 在fileInfo中也设置serverFileName
          },
          file: file, // 保存原始文件对象，供后续上传使用
          type: type, // 设置业务类型
          source: 'upload', // 设置来源为用户上传
          // 确保在processInfo中也设置serverFileName
          processInfo: {
            serverFileName: file.name
          }
        };
        
        // 通知父组件创建完成的面板
        console.log(`通知父组件创建面板: ${imageId}, 会话ID: ${sessionId}`);
        safeOnUpload({ type: 'panels', panels: [completedPanel] });
        
        // 重置上传区状态，但不关闭弹窗
        resetState();
        
        // 如果是背景页面的前景图、爆款开发页面的版型/印花图、服装图片（try-on/recolor/fashion等），不立即上传，而是在点击生成按钮时再上传
        if ((type === 'foreground' && pageType === 'background') || 
            (pageType === 'trending' && (type === 'pattern' || type === 'printing')) ||
            type === 'clothing') { // 添加对服装图片的判断
          // 不在这里保存到历史记录，而是在点击生成按钮上传到服务器后再保存
          
          // 关闭弹窗
          console.log(`${type}图片上传完成（本地预览），关闭弹窗`);
          if (typeof onClose === 'function') {
            onClose();
          }
          return;
        }
        
        // 对于其他类型，仍然采用原有的立即上传逻辑
        console.log(`开始上传${type === 'virtual' ? '人物照' : '前景图'}到服务器, 会话ID: ${sessionId}`);
        
        // 将工作流类型改回为'upload'
        uploadImage(file, 'upload', pageType, type === 'virtual' ? 'virtual' : 'foreground')
          .then(result => {
            console.log(`${type === 'virtual' ? '人物照' : '前景图'}上传成功:`, result);
            
            // 上传成功后保存到历史记录
            try {
              // 从结果中获取服务器URL和文件名
              let serverUrl = null;
              let serverFileName = null;
              if (result && result.results && result.results.length > 0) {
                const processedResult = result.results[0];
                // 保存服务器返回的文件名
                serverFileName = processedResult.serverFileName;
                if (processedResult.relativePath) {
                  serverUrl = `${SERVER_BASE_URL}${processedResult.relativePath}`;
                }
              }
              
              console.log(`服务器处理后的URL: ${serverUrl || '无服务器URL'}`);
              console.log(`服务器文件名: ${serverFileName || '未获取到服务器文件名'}`);
              
              // 如果有服务器文件名，更新面板信息
              if (serverFileName) {
                // 将服务器文件名添加到面板对象
                safeOnUpload({
                  type: 'update',
                  panelId: completedPanel.componentId,
                  data: {
                    serverFileName: serverFileName,
                    fileInfo: {
                      serverFileName: serverFileName
                    },
                    processInfo: {
                      serverFileName: serverFileName,
                      uploadResult: result
                    }
                  }
                });
              }
              
              serverUploadSuccess(file, imageUrl, serverUrl, serverFileName || file.name);
              
              // 上传成功后自动关闭弹窗
              console.log('前景图片上传成功，关闭弹窗');
              if (typeof onClose === 'function') {
                onClose();
              }
            } catch (error) {
              console.error('准备保存历史记录数据时出错:', error);
              // 即使出错，也尝试保存本地版本
              serverUploadSuccess(file, imageUrl, null, file.name);
              
              // 无论成功与否都自动关闭弹窗
              console.log('前景图片处理完成，关闭弹窗');
              if (typeof onClose === 'function') {
                onClose();
              }
            }
          })
          .catch(error => {
            console.error(`${type === 'virtual' ? '人物照' : '前景图'}上传失败:`, error);
            // 上传失败也保存本地版本
            serverUploadSuccess(file, imageUrl, null, file.name);
            
            // 无论成功与否都自动关闭弹窗
            console.log('前景图片处理完成，关闭弹窗');
            if (typeof onClose === 'function') {
              onClose();
            }
          });
        
        return;
      } catch (error) {
        console.error(`处理${type === 'virtual' ? '人物图片' : '前景图片'}时出错:`, error);
        safeOnUpload({ 
          type: 'error', 
          error: error.message 
        });
        return;
      }
    }
    
    // 处理爆款开发页面的版型图片和面料印花图片上传
    if ((pageType === 'trending' && type === 'pattern') || (pageType === 'trending' && type === 'printing')) {
      try {
        // 创建唯一ID，使用与服务器一致的格式
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 10);
        const imageId = `${timestamp}-${random}`;
        const imageUrl = URL.createObjectURL(file);
        
        // 创建完成状态的面板数据
        const completedPanel = {
          componentId: imageId,
          title: type === 'pattern' ? '版型参考图片' : '面料印花图片',
          status: 'completed',
          serverFileName: file.name, // 替换为serverFileName
          url: imageUrl, // 直接使用本地URL
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type,
            // 用于确保fileInfo中也能访问到文件名
            filename: file.name,
            serverFileName: file.name // 替换为serverFileName
          },
          // 按照新标准设置业务类型和来源
          // 注意：保留临时映射以兼容父组件期望的类型
          // 内部使用标准命名：pattern和printing，父组件使用：clothing和style
          type: type,  // 使用标准化的业务类型：'pattern'或'printing'
          source: 'upload', // 设置来源为用户上传
          // 为向下兼容保留旧格式字段
          _type: type === 'pattern' ? 'clothing' : 'style', // 兼容字段，供父组件使用
          name: type === 'pattern' ? '版型参考图片' : '面料印花图片', // 添加name字段
          preview: imageUrl, // 添加preview字段作为备用
          // 确保在processInfo中也设置serverFileName
          processInfo: {
            serverFileName: file.name
          }
        };
        
        // 通知父组件创建完成的面板
        console.log(`通知父组件创建${type === 'pattern' ? '版型' : '面料印花'}面板: ${imageId}, 会话ID: ${sessionId}`);
        safeOnUpload({ type: 'panels', panels: [completedPanel] });
        
        // 重置上传区状态，但不关闭弹窗
        resetState();
        
        // 异步上传图片到服务器
        console.log(`开始上传${type === 'pattern' ? '版型' : '面料印花'}图片到服务器, 会话ID: ${sessionId}`);
        
        // TODO: 当前需要对内部type和API所需type进行映射转换。建议在未来统一命名规范
        uploadImage(file, 'upload', 'trending', type === 'pattern' ? 'clothing' : 'style')
          .then(result => {
            console.log(`${type === 'pattern' ? '版型' : '面料印花'}图片上传成功:`, result);
            
            // 上传成功后保存到历史记录
            try {
              // 从结果中获取服务器URL和文件名
              let serverUrl = null;
              let serverFileName = null;
              if (result && result.results && result.results.length > 0) {
                const processedResult = result.results[0];
                // 保存服务器返回的文件名
                serverFileName = processedResult.serverFileName;
                if (processedResult.relativePath) {
                  serverUrl = `${SERVER_BASE_URL}${processedResult.relativePath}`;
                }
              }
              
              console.log(`服务器处理后的URL: ${serverUrl || '无服务器URL'}`);
              console.log(`服务器文件名: ${serverFileName || '未获取到服务器文件名'}`);
              
              // 如果有服务器文件名，更新面板信息
              if (serverFileName) {
                // 将服务器文件名添加到面板对象的顶层
                const updateData = {
                  serverFileName: serverFileName,
                  fileInfo: {
                    serverFileName: serverFileName,
                    filename: serverFileName
                  }
                };
                
                // TODO: patternServerFileName和printingServerFileName字段命名与组件内部使用的pattern和printing不一致
                // 建议在未来版本中统一命名规范，消除这种映射关系
                // 根据类型添加特定的服务器文件名字段
                if (type === 'pattern') {
                  updateData.patternServerFileName = serverFileName;
                } else if (type === 'printing') {
                  updateData.printingServerFileName = serverFileName;
                }
                
                // 添加处理信息
                updateData.processInfo = {
                  serverFileName: serverFileName, // 替换为serverFileName
                  uploadResult: result
                };
                
                // 更新面板
                safeOnUpload({
                  type: 'update',
                  panelId: completedPanel.componentId,
                  data: updateData
                });
              }
              
              serverUploadSuccess(file, imageUrl, serverUrl, serverFileName || file.name);
              
              // 上传成功后自动关闭弹窗
              console.log(`${type === 'pattern' ? '版型' : '面料印花'}图片上传成功，关闭弹窗`);
              if (typeof onClose === 'function') {
                onClose();
              }
            } catch (error) {
              console.error('准备保存历史记录数据时出错:', error);
              // 即使出错，也尝试保存本地版本
              serverUploadSuccess(file, imageUrl, null, file.name);
              
              // 无论成功与否都自动关闭弹窗
              console.log(`${type === 'pattern' ? '版型' : '面料印花'}图片处理完成，关闭弹窗`);
              if (typeof onClose === 'function') {
                onClose();
              }
            }
          })
          .catch(error => {
            console.error(`${type === 'pattern' ? '版型' : '面料印花'}图片上传失败:`, error);
            // 上传失败也保存本地版本
            serverUploadSuccess(file, imageUrl, null, file.name);
            
            // 无论成功与否都自动关闭弹窗
            console.log(`${type === 'pattern' ? '版型' : '面料印花'}图片处理完成，关闭弹窗`);
            if (typeof onClose === 'function') {
              onClose();
            }
          });
          
        return;
      } catch (error) {
        console.error(`处理${type === 'pattern' ? '版型' : '面料印花'}图片时出错:`, error);
        safeOnUpload({ 
          type: 'error', 
          error: error.message 
        });
        return;
      }
    }

    // 如果是服装复色页面或换面料页面的服装上传
    if (type === 'recolorClothing' || (type === 'clothing' && (pageType === 'recolor' || pageType === 'fabric'))) {
      try {
        // 创建图片本地URL
        const imageUrl = URL.createObjectURL(file);
        
        // 创建完成状态的面板数据（直接使用原始图片）
        // 使用与服务器一致的格式，不使用前缀
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 10);
        const imageId = `${timestamp}-${random}`;
        const completedPanel = {
          componentId: imageId,
          title: '服装图',
          status: 'completed', // 直接设为完成状态，无需处理中状态
          serverFileName: file.name, // 替换为serverFileName
          url: imageUrl, // 直接使用本地URL
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type,
            serverFileName: file.name // 在fileInfo中也设置serverFileName
          },
          file: file, // 保存原始文件对象，供后续上传使用
          type: 'clothing',    // 设置业务类型为服装图片 
          source: 'upload',    // 设置来源为用户上传，在点击生成时检查这个属性决定是否上传
          // 确保在processInfo中也设置serverFileName
          processInfo: {
            serverFileName: file.name
          }
        };
        
        // 通知父组件创建完成的面板
        console.log(`通知父组件创建服装面板: ${completedPanel.componentId}`);
        safeOnUpload({ type: 'panels', panels: [completedPanel] });
        
        // 重置上传区状态，但不关闭弹窗
        resetState();
        
        // 异步上传到服务器
        console.log(`开始上传服装图片到服务器，会话ID: ${sessionId}`);
        
        // 使用统一的上传函数
        uploadImage(file, 'upload', pageType, 'clothing')
          .then(result => {
            console.log(`服装图片上传成功:`, result);
            
            // 上传成功后保存到历史记录
            try {
              // 从结果中获取服务器URL
              let serverUrl = null;
              let serverFileName = null;
              if (result && result.results && result.results.length > 0) {
                const processedResult = result.results[0];
                // 保存服务器返回的文件名
                serverFileName = processedResult.serverFileName;
                if (processedResult.relativePath) {
                  serverUrl = `${SERVER_BASE_URL}${processedResult.relativePath}`;
                }
              }
              
              console.log(`服务器处理后的URL: ${serverUrl || '无服务器URL'}`);
              console.log(`服务器文件名: ${serverFileName || '未获取到服务器文件名'}`);
              
              // 如果有服务器文件名，更新面板信息
              if (serverFileName) {
                // 将服务器文件名添加到面板对象
                safeOnUpload({
                  type: 'update',
                  panelId: completedPanel.componentId,
                  data: {
                    serverFileName: serverFileName,
                    fileInfo: {
                      serverFileName: serverFileName
                    },
                    processInfo: {
                      serverFileName: serverFileName,
                      uploadResult: result
                    }
                  }
                });
              }
              
              serverUploadSuccess(file, imageUrl, serverUrl, serverFileName || file.name);
              
              // 上传成功后自动关闭弹窗
              console.log('服装图片上传成功，关闭弹窗');
              if (typeof onClose === 'function') {
                onClose();
              }
            } catch (error) {
              console.error('准备保存历史记录数据时出错:', error);
              // 即使出错，也尝试保存本地版本
              serverUploadSuccess(file, imageUrl, null, file.name);
              
              // 无论成功与否都自动关闭弹窗
              console.log('服装图片处理完成，关闭弹窗');
              if (typeof onClose === 'function') {
                onClose();
              }
            }
          })
          .catch(error => {
            console.error(`服装图片上传失败:`, error);
            // 上传失败也保存本地版本
            serverUploadSuccess(file, imageUrl, null, file.name);
            
            // 无论成功与否都自动关闭弹窗
            console.log('服装图片处理完成，关闭弹窗');
            if (typeof onClose === 'function') {
              onClose();
            }
          });
          
        return;
      } catch (error) {
        console.error(`处理服装图片时出错:`, error);
        safeOnUpload({ 
          type: 'error', 
          error: error.message 
        });
        return;
      }
    }

    // 如果是模特换装页面的服装上传
    if (pageType === 'try-on' && type === 'clothing') {
      try {        
        // 创建唯一ID
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 10);
        const imageId = `${timestamp}-${random}`;
        const imageUrl = URL.createObjectURL(file);
        
        // 创建完成状态的面板数据（使用本地预览）
        const completedPanel = {
          componentId: imageId,
          title: '服装',
          status: 'completed', // 直接设置为完成状态，而不是processing
          serverFileName: file.name,
          url: imageUrl, // 直接使用本地URL
          previewUrl: imageUrl,
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type,
            serverFileName: file.name
          },
          file: file, // 保存原始文件对象，供后续上传使用
          type: 'clothing',  // 设置业务类型为服装
          source: 'upload'   // 设置来源为用户上传
        };
        
        // 通知父组件创建完成状态的面板
        console.log(`通知父组件创建服装面板: ${completedPanel.componentId}, 会话ID: ${sessionId}`);
        safeOnUpload({ type: 'panels', panels: [completedPanel] });
        
        // 重置上传区状态，但不关闭弹窗
        resetState();
        
        // 关闭弹窗
        console.log(`服装图片上传完成（本地预览），关闭弹窗`);
        if (typeof onClose === 'function') {
          onClose();
        }
        
        return;
      } catch (error) {
        console.error('处理服装图片时出错:', error);
        safeOnUpload({ 
          type: 'error', 
          error: error.message 
        });
        return;
      }
    }


    // 处理面料图片上传
    if (type === 'fabric') {
      try {
      } catch (error) {
        console.error('处理面料图片时出错:', error);
        // 出错时通知父组件
        safeOnUpload({ 
          type: 'error', 
          error: error.message 
        });
        return;
      }
    }

    // 处理设计图片上传
    if (type === 'design' || (pageType === 'optimize' && type === 'design')) {
      try {
        // 创建唯一ID
        const imageId = `design-${Date.now()}`;
        const imageUrl = URL.createObjectURL(file);
        
        // 创建完成状态的面板数据（直接使用原始图片）
        const completedPanel = {
          componentId: imageId,
          title: '款式图片',
          status: 'completed',
          serverFileName: file.name,
          url: imageUrl, // 直接使用本地URL
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type
          },
          file: file, // 保存原始文件对象，供后续上传使用
          type: 'design',     // 设置业务类型为款式图片
          source: 'upload'    // 设置来源为用户上传
        };
        
        // 通知父组件创建完成的面板
        console.log(`通知父组件创建面板: ${imageId}, 会话ID: ${sessionId}`);
        safeOnUpload({ type: 'panels', panels: [completedPanel] });
        
        // 重置上传区状态，但不关闭弹窗
        resetState();
        
        // 如果是优化页面的款式图，不立即上传到服务器，而是在点击生成按钮时再上传
        if (pageType === 'optimize') {
          // 不在这里保存到历史记录，而是在点击生成按钮上传到服务器后再保存
          // 这样能避免localStorage记录过多的本地图片并导致配额问题
          
          // 关闭弹窗
          console.log('款式图片上传完成（本地预览），关闭弹窗');
          if (typeof onClose === 'function') {
            onClose();
          }
          return;
        }
        
        // 对于其他页面类型的设计图，仍然使用原有的立即上传逻辑
        console.log(`开始上传设计图片到服务器, 会话ID: ${sessionId}`);
        uploadImage(file, 'upload', 'optimize', 'design')
          .then(result => {
            console.log(`设计图片上传成功:`, result);
            
            // 上传成功后保存到历史记录
            try {
              // 从结果中获取服务器URL
              let serverUrl = null;
              if (result && result.results && result.results.length > 0) {
                const processedResult = result.results[0];
                if (processedResult.relativePath) {
                  serverUrl = `${SERVER_BASE_URL}${processedResult.relativePath}`;
                }
              }
              
              console.log(`服务器处理后的URL: ${serverUrl || '无服务器URL'}`);
              serverUploadSuccess(file, imageUrl, serverUrl, file.name);
              
              // 上传成功后自动关闭弹窗
              console.log('设计图片上传成功，关闭弹窗');
              if (typeof onClose === 'function') {
                onClose();
              }
            } catch (error) {
              console.error('准备保存历史记录数据时出错:', error);
              // 即使出错，也尝试保存本地版本
              serverUploadSuccess(file, imageUrl, null, file.name);
              
              // 无论成功与否都自动关闭弹窗
              console.log('设计图片处理完成，关闭弹窗');
              if (typeof onClose === 'function') {
                onClose();
              }
            }
          })
          .catch(error => {
            console.error(`设计图片上传失败:`, error);
            // 上传失败也保存本地版本
            serverUploadSuccess(file, imageUrl, null, file.name);
            
            // 无论成功与否都自动关闭弹窗
            console.log('设计图片处理完成，关闭弹窗');
            if (typeof onClose === 'function') {
              onClose();
            }
          });
        
        return;
      } catch (error) {
        console.error(`处理设计图片时出错:`, error);
        safeOnUpload({ 
          type: 'error', 
          error: error.message 
        });
        return;
      }
    }

    // 处理时尚大片页面的参考图片上传
    if (type === 'reference') {
      try {
        // 创建唯一ID
        const imageId = `reference-${Date.now()}`;
        const imageUrl = URL.createObjectURL(file);
        
        // 创建完成状态的面板数据
        const completedPanel = {
          componentId: imageId,
          title: '参考图片',
          status: 'completed',
          serverFileName: file.name,
          url: imageUrl, // 直接使用本地URL
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type
          },
          file: file, // 保存原始文件对象，供后续上传使用
          type: 'reference',    // 设置业务类型为参考图片
          source: 'upload'      // 设置来源为用户上传
        };
        
        // 通知父组件创建完成的面板
        console.log(`通知父组件创建参考图面板: ${imageId}, 会话ID: ${sessionId}`);
        safeOnUpload({ type: 'panels', panels: [completedPanel] });
        
        // 重置上传区状态，但不关闭弹窗
        resetState();
        
        // 如果是时尚大片页面，参考图片不立即上传到服务器，而是在点击生成按钮时再上传
        if (pageType === 'fashion') {
          console.log('时尚大片页面参考图片上传完成（本地预览），关闭弹窗');
          if (typeof onClose === 'function') {
            onClose();
          }
          return;
        }
        
        // 其他页面类型仍然使用原有的立即上传逻辑
        console.log(`开始上传参考图片到服务器, 会话ID: ${sessionId}`);
        uploadImage(file, 'upload', pageType, 'reference')
          .then(result => {
            console.log(`参考图片上传成功:`, result);
            
            // 上传成功后保存到历史记录
            try {
              // 从结果中获取服务器URL
              let serverUrl = null;
              if (result && result.results && result.results.length > 0) {
                const processedResult = result.results[0];
                if (processedResult.relativePath) {
                  serverUrl = `${SERVER_BASE_URL}${processedResult.relativePath}`;
                }
              }
              
              console.log(`服务器处理后的URL: ${serverUrl || '无服务器URL'}`);
              serverUploadSuccess(file, imageUrl, serverUrl, file.name);
              
              // 上传成功后自动关闭弹窗
              console.log('参考图片上传成功，关闭弹窗');
              if (typeof onClose === 'function') {
                onClose();
              }
            } catch (error) {
              console.error('准备保存历史记录数据时出错:', error);
              // 即使出错，也尝试保存本地版本
              serverUploadSuccess(file, imageUrl, null, file.name);
              
              // 无论成功与否都自动关闭弹窗
              console.log('参考图片处理完成，关闭弹窗');
              if (typeof onClose === 'function') {
                onClose();
              }
            }
          })
          .catch(error => {
            console.error(`参考图片上传失败:`, error);
            // 上传失败也保存本地版本
            serverUploadSuccess(file, imageUrl, null, file.name);
            
            // 无论成功与否都自动关闭弹窗
            console.log('参考图片处理完成，关闭弹窗');
            if (typeof onClose === 'function') {
              onClose();
            }
          });
        
        return;
      } catch (error) {
        console.error(`处理参考图片时出错:`, error);
        safeOnUpload({ 
          type: 'error', 
          error: error.message 
        });
        return;
      }
    }

    // 其他未处理的情况，创建一个通用的面板
    try {
      const imageId = `image-${Date.now()}`;
      const imageUrl = URL.createObjectURL(file);
      
      // 创建完成状态的面板数据
      const completedPanel = {
        componentId: imageId,
        title: '上传图片',
        status: 'completed',
        serverFileName: file.name,
        url: imageUrl,
        fileInfo: {
          name: file.name,
          size: file.size,
          type: file.type
        },
        file: file, // 保存原始文件对象，供后续上传使用
        type: 'general',    // 设置默认业务类型
        source: 'upload'    // 设置来源为用户上传
      };
      
      // 通知父组件创建完成的面板
      console.log(`通知父组件创建通用面板: ${imageId}`);
      safeOnUpload({ type: 'panels', panels: [completedPanel] });
      
      // 重置上传区状态，但不关闭弹窗
      resetState();
      
      // 保存到历史记录
      serverUploadSuccess(file, imageUrl, null, file.name);
      
    } catch (error) {
      console.error('处理通用图片上传时出错:', error);
      safeOnUpload({ 
        type: 'error', 
        error: error.message 
      });
    }
  };

  const handleRemoveFile = (fileId) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };
  const [isProcessing, setIsProcessing] = useState(false);

  const handleExampleClick = async (imageUrl) => {
    try {
      // 防止重复点击
      
      if (isProcessing) {
        console.log('正在处理中,请勿重复点击');
        return;
      }
      
      setIsProcessing(true);
      
      try {
        // 从URL获取图片并转换为File对象 - 使用大图进行任务生成
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const filename = imageUrl.split('/').pop() || 'example.jpg';
        const file = new File([blob], filename, { type: blob.type });
        
        console.log('处理示例图片:', {
          名称: filename,
          类型: blob.type,
          大小: blob.size,
          使用图片: imageUrl
        });
        
        // 使用标准的文件上传处理函数
        await handleFileUpload(file);
        
        // 所有类型都自动关闭弹窗
        userInitiatedClose();
      } catch (error) {
        console.error('处理示例图片失败:', error);
        message.error('处理失败,请重试');
      } finally {
        setIsProcessing(false);
      }
      
      // 移除自动保存到历史记录的部分
      // 示例图片现在只有在点击确认上传按钮后才会保存到历史记录中
    } catch (error) {
      console.error('处理示例图片时出错:', error);
      message.error('处理示例图片失败，请重试');
    }
  };

  const handleConfirm = async () => {
    try {
      console.log('【DEBUG】用户点击确认按钮');
      
      // 检查是否有文件要上传
      if (uploadedFiles.length === 0) {
        console.error('【ERROR】没有文件要上传');
        return;
      }
      
      console.log(`【INFO】准备上传${uploadedFiles.length}个文件`);
      
      // 创建处理中状态的面板数据
      let panelTitle;
      if (type === 'model') {
        panelTitle = '模特';
      } else if (type === 'pattern') {
        panelTitle = '版型';
      } else if (type === 'printing') {
        panelTitle = '面料印花';
      } else if (type === 'reference') {
        panelTitle = '参考图';
      } else {
        panelTitle = '服装';
      }
      
      const processingPanels = uploadedFiles.map((file, index) => ({
        id: `upload-${Date.now()}-${index}`,
        title: uploadedFiles.length === 1 ? panelTitle : `${panelTitle} ${index + 1}`,
        status: 'processing',
        serverFileName: file.file.name,
        previewUrl: file.url,
        type: type,      // 设置业务类型与当前页面一致
        source: 'upload' // 设置来源为用户上传
      }));
      
      // 在resetState之前保存一个uploadedFiles的副本，以便后续使用
      // 不仅保存引用，而是深拷贝相关的数据
      const filesToSave = uploadedFiles.map(file => ({
        file: file.file,
        url: file.url,
        id: file.id
      }));
      
      console.log(`【INFO】已保存${filesToSave.length}个文件的副本供后续使用`);
      
      // 通知父组件创建处理中的面板
      console.log(`【INFO】通知父组件创建${processingPanels.length}个面板`);
      safeOnUpload({ type: 'panels', panels: processingPanels });
      
      // 重置状态，但不关闭弹窗
      resetState();
      console.log('【INFO】已重置上传区状态');
      
      // 确定当前页面类型和图片类型
      const effectivePageType = pageType || (type === 'model' ? 'try-on' : 'fashion');
      const imageType = type === 'model' ? 'model' : (type === 'reference' ? 'reference' : 'clothing');
      
      
      // 确定API类型 - 统一使用upload
      const apiType = 'upload';
      
      console.log(`【INFO】上传参数:`, {
        页面类型: effectivePageType,
        图片类型: imageType,
        API类型: apiType
      });
      
      // 记录当前处理该上传的会话ID
      const uploadSessionId = sessionId;
      
      // 处理每个文件的上传和后台处理
      filesToSave.forEach(async (fileData, index) => {
        try {
          const file = fileData.file;
          const panel = processingPanels[index];
          
          console.log(`【INFO】开始处理文件 ${index + 1}/${filesToSave.length}: ${file.name}`);
          
          // 上传图片
          uploadImage(file, apiType, effectivePageType, imageType)
            .then(result => {
              console.log(`【INFO】文件 ${index + 1}/${filesToSave.length} 上传结果:`, result);
              
              // 处理结果
              if (result && result.results && result.results.length > 0) {
                const processedResult = result.results[0];
                
                // 检查是否已有URL构建
                let processedImageUrl = null;
                if (processedResult.relativePath) {
                  processedImageUrl = `${SERVER_BASE_URL}${processedResult.relativePath}`;
                }
                
                // 更新面板状态
                console.log(`【INFO】更新面板状态: ${panel.componentId}, 原会话ID: ${uploadSessionId}, 当前活跃会话: ${currentActiveSessionId}`);
                
                // 保存到历史记录
                serverUploadSuccess(file, fileData.url, processedImageUrl, processedResult.fileName || file.name);
                
                safeOnUpload({
                  type: 'update',
                  panelId: panel.componentId,
                  data: { 
                    processed: {
                      ...processedResult,
                      url: processedImageUrl,
                      originalUrl: panel.previewUrl
                    }
                  }
                });
              } else {
                console.error(`【ERROR】服务器返回的结果缺少预期的数据:`, result);
                safeOnUpload({ 
                  type: 'error', 
                  panelId: panel.componentId,
                  error: '服务器返回的数据格式不正确' 
                });
              }
            })
            .catch(error => {
              console.error(`【ERROR】处理上传文件 ${index + 1}/${filesToSave.length} 时出错:`, error);
              safeOnUpload({ 
                type: 'error', 
                panelId: panel.componentId,
                error: error.message 
              });
            });
        } catch (error) {
          console.error(`【ERROR】处理文件 ${index + 1}/${filesToSave.length} 时出错:`, error);
        }
      });
    } catch (error) {
      console.error('【ERROR】确认上传时出错:', error);
    }
  };

  // 画廊视图组件
  const GalleryView = () => (
    <div className="gallery-view">
      <div className="gallery-grid">
        {/* 上传入口 */}
        <div 
          className={`gallery-item upload-entry ${uploadedFiles.length >= 9 ? 'disabled' : ''} ${isDragging ? 'dragging' : ''}`}
          onClick={(e) => {
            if (uploadedFiles.length < 9) {
              // 如果点击的是label或其子元素，不做额外处理，让默认行为触发
              if (e.target.closest('.upload-label')) {
                return;
              }
              // 否则，手动触发文件选择
              const fileInput = e.currentTarget.querySelector('.file-input');
              if (fileInput) {
                fileInput.click();
              }
            }
          }}
          onDragOver={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (uploadedFiles.length < 9) {
              setIsDragging(true);
            }
          }}
          onDragLeave={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsDragging(false);
          }}
          onDrop={async (e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsDragging(false);
            
            if (uploadedFiles.length >= 9) {
              message.warning('最多支持上传9张图片');
              return;
            }
            
            const files = Array.from(e.dataTransfer.files);
            
            // 使用统一的文件校验函数
            for (const file of files) {
              const validation = await validateFile(file);
              if (!validation.isValid) {
                message.error(validation.error);
                return;
              }
            }
            
            // 检查是否超出最大上传数量
            if (uploadedFiles.length + files.length > 9) {
              message.warning(`最多支持上传9张图片，您已上传${uploadedFiles.length}张，选择了${files.length}张`);
              return;
            }
            
            // 处理文件上传
            handleFilesUpload(files);
          }}
        >
          {type === 'clothing' && pageType !== 'optimize' && (
            <div className="support-tag">
              {getTagText()}
            </div>
          )}
          {pageType === 'optimize' && type === 'design' && (
            <div className="support-tag">
              {getTagText()}
            </div>
          )}
          <label className="upload-label">
            <div className="upload-icon">
              <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 14V6H13V14H11Z" fill="currentColor"/>
                <path d="M7 10L12 5L17 10H7Z" fill="currentColor"/>
                <path d="M6 18V16H18V18H6Z" fill="currentColor"/>
              </svg>
            </div>
            <div className="upload-text">
              <span>继续上传</span>
            </div>
            <input 
              type="file" 
              className="file-input" 
              accept={UPLOAD_CONFIG.getAcceptTypes()}
              onChange={handleFileSelect}
              multiple={pageType === 'matting' && type === 'source'} // 只有自动抠图页面的第一个标签允许多选
              style={{ display: 'none' }}
            />
          </label>
        </div>

        {/* 已上传文件 */}
        {uploadedFiles.map(file => (
          <div key={file.id} className="gallery-item">
            <img src={file.url} alt={file.file.name} />
            <div className="item-overlay">
              <button className="remove-btn" onClick={() => handleRemoveFile(file.id)}>
                <MdClose />
              </button>
            </div>
          </div>
        ))}

        {/* 上传中的文件 */}
        {Object.entries(uploadingFiles).map(([id, info]) => (
          <div key={id} className="gallery-item uploading">
            <div className="upload-status-text">
              {info.progress < 100 ? '上传中…' : '处理中…'}
            </div>
            <div className="upload-progress">
              <div 
                className="progress-bar" 
                style={{width: `${info.progress}%`}} 
              ></div>
            </div>
          </div>
        ))}
      </div>

      {/* 底边条 */}
      <div className="gallery-footer">
        <div className="file-count">
          最多支持 9 张（已选择 {uploadedFiles.length} 张）
        </div>
        <button 
          className="confirm-button"
          onClick={handleConfirm}
          disabled={uploadedFiles.length === 0 || Object.keys(uploadingFiles).length > 0}
        >
          确认
        </button>
      </div>
    </div>
  );

  // 格式化日期时间显示
  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  };

  // 拖动事件
  const handleModalMouseDown = (e) => {
    if (e.target.closest('.modal-header')) {
      setIsModalDragging(true);
      dragStartMouse.current = { x: e.clientX, y: e.clientY };
      dragStartTransform.current = { ...dragTransform };
      const modal = modalRef.current;
      if (modal) {
        document.body.classList.add('no-select');
        modal.classList.add('dragging');
      }
      e.preventDefault();
    }
  };
  const handleModalMouseMove = useCallback((e) => {
    if (isModalDragging) {
      const deltaX = e.clientX - dragStartMouse.current.x;
      const deltaY = e.clientY - dragStartMouse.current.y;
      const newX = dragStartTransform.current.x + deltaX;
      const newY = dragStartTransform.current.y + deltaY;
      setDragTransform({ x: newX, y: newY });
    }
  }, [isModalDragging]);
  const handleModalMouseUp = useCallback(() => {
    if (isModalDragging) {
      setIsModalDragging(false);
      document.body.classList.remove('no-select');
      const modal = modalRef.current;
      if (modal) {
        modal.classList.remove('dragging');
      }
    }
  }, [isModalDragging]);
  useEffect(() => {
    if (isModalDragging) {
      document.addEventListener('mousemove', handleModalMouseMove);
      document.addEventListener('mouseup', handleModalMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleModalMouseMove);
        document.removeEventListener('mouseup', handleModalMouseUp);
      };
    }
  }, [isModalDragging, handleModalMouseMove, handleModalMouseUp]);

  return (
    <div className="upload-guide-modal">
      <div
        className={`upload-guide-modal-content${isModalDragging ? ' dragging' : ''}`}
        ref={modalRef}
        style={{
          transform: `translate(-50%, -50%) translate(${dragTransform.x}px, ${dragTransform.y}px)`,
          opacity: isMounted ? 1 : 0
        }}
        onMouseDown={handleModalMouseDown}
      >
        <button 
          className="large-close-button"
          onClick={() => {
            console.log('用户点击关闭按钮');
            resetState(); // 关闭时重置所有状态
            userInitiatedClose(); // 使用用户主动关闭方法
          }}
        >
          <MdClose />
        </button>
        <div className="modal-header">
          <div className="modal-tabs">
            <button 
              className={`tab-button ${activeTab === 'guide' ? 'active' : ''}`}
              onClick={() => setActiveTab('guide')}
            >
              {getTitle()}
            </button>
            <button 
              className={`tab-button ${activeTab === 'history' ? 'active' : ''}`}
              onClick={() => setActiveTab('history')}
            >
              上传记录
            </button>
          </div>
        </div>

        <div className="modal-body">
          {activeTab === 'guide' ? (
            uploadView === 'guide' ? (
              <>
                {/* 上传区域 */}
                <div className="upload-section">
                  <input 
                    type="file" 
                    id="file-upload-guide" 
                    className="file-input" 
                    accept={UPLOAD_CONFIG.getAcceptTypes()}
                    onChange={handleFileSelect}
                    multiple={pageType === 'matting' && type === 'source'} // 只有自动抠图页面的第一个标签允许多选
                    style={{ display: 'none' }}
                  />
                  <label 
                    htmlFor="file-upload-guide" 
                    className={`guide-upload-zone ${isDragging ? 'dragging' : ''}`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                  >
                    <div className="support-tag">
                      {getTagText()}
                    </div>
                    <div className="upload-content">
                      <div className="upload-main">
                        <label htmlFor="file-upload-guide" className="upload-button">
                          点击上传
                        </label>
                        <span className="upload-text">{getDragText()}</span>
                      </div>
                      <div className="upload-requirements">
                        <span className="requirement-item">支持 {UPLOAD_CONFIG.getSupportedTypesString()}</span>
                        <span className="divider">|</span>
                        <span className="requirement-item">文件{UPLOAD_CONFIG.maxSize}MB以内</span>
                        <span className="divider">|</span>
                        <span className="requirement-item">分辨率范围 {UPLOAD_CONFIG.getResolutionString()}px</span>
                      </div>
                    </div>
                  </label>
                </div>

                {/* 图文指导区 */}
                <div className="guide-section">
                  {/* 推荐图片区域 */}
                  <div className="recommended-section">
                    <h3>推荐示例</h3>
                    <p className="section-desc">点击图片可直接使用</p>
                    <div className="example-grid">
                      {getRecommendedExamples().map((example) => (
                        <div 
                          key={example.id} 
                          className="example-item recommended"
                          onClick={() => handleExampleClick(example.image)}
                        >
                          <img src={(example.thumbnail || example.image) + '?v=' + new Date().getTime()} alt={example.title} />
                          <div className="example-hover">
                            <div className="hover-content">
                              <button 
                                className="use-button" 
                              >
                                使用
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 错误示例区域 */}
                  <div className="error-section">
                    <h3>错误示例</h3>
                    <p className="section-desc">请避免以下情况，以获得更佳效果</p>
                    <div className="example-grid">
                      {/* 自动补齐左侧空白占位 */}
                      {(() => {
                        const errorExamples = getErrorExamples();
                        const emptyCount = Math.max(0, 7 - errorExamples.length);
                        return [
                          ...Array(emptyCount).fill(0).map((_, idx) => (
                            <div className="empty-grid-item" key={"empty-" + idx}></div>
                          )),
                          ...errorExamples.map((example) => (
                            <div key={example.id} className="example-item error">
                              <img src={example.image + '?v=' + new Date().getTime()} alt={example.title} />
                              <div className="example-info">
                                <h4>{example.title}</h4>
                              </div>
                            </div>
                          ))
                        ];
                      })()}
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <GalleryView />
            )
          ) : (
            <HistoryView />
          )}
        </div>
      </div>
    </div>
  );
};

export default UploadGuideModal; 