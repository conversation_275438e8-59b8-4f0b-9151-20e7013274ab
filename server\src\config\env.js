// 加载ComfyUI云环境配置
if (!process.env.COMFYUI_URL) {
  console.log('未配置COMFYUI_URL，使用默认值');
  process.env.COMFYUI_URL = 'http://htc-ksifabooi78t9jm6i-ewrdtowe-custom.service.onethingrobot.com';
}

// 设置API密钥
if (!process.env.COMFYUI_API_KEY) {
  console.log('未配置COMFYUI_API_KEY，使用默认值');
  process.env.COMFYUI_API_KEY = '9b1b825a734bfe35b703d4bc01a2396e';
}

// 设置实例ID
if (!process.env.COMFYUI_INSTANCE_ID) {
  console.log('未配置COMFYUI_INSTANCE_ID，使用默认值');
  process.env.COMFYUI_INSTANCE_ID = 'tru42aamuc6mzyyl-c3tta1n5';
}

// 设置WebSocket URL
if (!process.env.COMFYUI_WS_URL) {
  console.log('未配置COMFYUI_WS_URL，默认使用HTTP URL转换');
  // 将HTTP转换为WebSocket协议
  process.env.COMFYUI_WS_URL = process.env.COMFYUI_URL.replace(/^http/, 'ws');
}

// 设置API路径前缀（如有需要）
if (!process.env.COMFYUI_API_PATH_PREFIX) {
  process.env.COMFYUI_API_PATH_PREFIX = '';
} 