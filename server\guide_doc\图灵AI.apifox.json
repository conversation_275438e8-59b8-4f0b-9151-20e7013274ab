{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "图灵AI", "description": "", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 56988070, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "moduleId": 4342947, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "runninghub-api", "id": 62110332, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 4342947, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "发起ComfyUI任务1-简易", "api": {"id": "324692870", "method": "post", "path": "/task/openapi/create", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "GCA5BVZ6Cj", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "725618126", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/185908596", "description": ""}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": {\n        \"netWssUrl\": \"wss://www.runninghub.cn:443/ws/c_instance?c_host=*************&c_port=80&clientId=e825290b08ca2015b8f62f0bbdb5f5f6&workflowId=1904136902449209346&Rh-Comfy-Auth=eyJ1c2VySWQiOiJkZTBkYjZmMjU2NGM4Njk3YjA3ZGY1NWE3N2YwN2JlOSIsInNpZ25FeHBpcmUiOjE3NDM1NjQ2NTQ1NTIsInRzIjoxNzQyOTU5ODU0NTUyLCJzaWduIjoiNjVkMTVhYjA3Njg2MjJlOGM1YzJkNTc2MzQwOWFmYzkifQ%3D%3D&target=https://hbxy.runninghub.cn:11143\",\n        \"taskId\": \"1904737800233889793\",\n        \"clientId\": \"e825290b08ca2015b8f62f0bbdb5f5f6\",\n        \"taskStatus\": \"RUNNING\",\n        \"promptTips\": \"{\\\"result\\\": true, \\\"error\\\": null, \\\"outputs_to_execute\\\": [\\\"9\\\"], \\\"node_errors\\\": {}}\"\n    }\n}", "responseId": 725618126, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/185908591", "description": ""}, "oasExtensions": ""}, "description": "该方式运行 workflow，相当于在不改变原有workflow的任何参数的情况下，直接点了一下\"运行\"按钮。", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 0, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept": true, "accept-encoding": true, "connection": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.725618126"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "发起ComfyUI任务2-高级", "api": {"id": "324692871", "method": "post", "path": "https://www.runninghub.cn/task/openapi/create", "parameters": {"query": [], "path": [], "cookie": [], "header": [{"id": "AiOzPBaHcJ", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string", "enable": true}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": [{"name": "authorization"}, {"name": "X-Instance-ID"}, {"name": "Authorization"}]}, "responses": [{"id": "725618127", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/185908596", "description": ""}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "排队", "data": "{\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": {\n        \"netWssUrl\": null,\n        \"taskId\": \"1910246754753896450\",\n        \"clientId\": \"e825290b08ca2015b8f62f0bbdb5f5f6\",\n        \"taskStatus\": \"QUEUED\",\n        \"promptTips\": \"{\\\"result\\\": true, \\\"error\\\": null, \\\"outputs_to_execute\\\": [\\\"9\\\"], \\\"node_errors\\\": {}}\"\n    }\n}", "responseId": 725618127, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}, {"name": "成功", "data": "{\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": {\n        \"netWssUrl\": \"websocket-url\",\n        \"taskId\": \"1910246754753896450\",\n        \"clientId\": \"e825290b08ca2015b8f62f0bbdb5f5f6\",\n        \"taskStatus\": \"RUNNING\",\n        \"promptTips\": \"{\\\"result\\\": true, \\\"error\\\": null, \\\"outputs_to_execute\\\": [\\\"9\\\"], \\\"node_errors\\\": {}}\"\n    }\n}", "responseId": 725618127, "ordering": 2, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/185908593"}, "examples": [{"value": "{\r\n    \"workflowId\":\"1946846884944338945\",\r\n    \"apiKey\":\"5036dc30549545c99ffed9428685b234\",\r\n    \"nodeInfoList\": [\r\n    {\r\n      \"nodeId\": \"32\",\r\n      \"fieldName\": \"text\",\r\n      \"fieldValue\": \"一个女人\"\r\n    },\r\n    {\r\n      \"nodeId\": \"34\",\r\n      \"fieldName\": \"seed\",\r\n      \"fieldValue\": \"56654498987449849\"\r\n    },{\r\n      \"nodeId\": \"44\",\r\n      \"fieldName\": \"url\",\r\n      \"fieldValue\": \"https://file.aibikini.cn/trending/1753180054615-recommended_5-a.jpg\"\r\n    },{\r\n      \"nodeId\": \"45\",\r\n      \"fieldName\": \"url\",\r\n      \"fieldValue\": \"https://file.aibikini.cn/trending/1753180054237-recommended_3-a.jpg\"\r\n    },{\r\n      \"nodeId\": \"36\",\r\n      \"fieldName\": \"filename_prefix\",\r\n      \"fieldValue\": \"acdddd\"\r\n    }\r\n]\r\n}", "mediaType": "application/json", "description": ""}], "oasExtensions": ""}, "description": "# 发起 ComfyUI 任务（高级）\n\n该接口用于基于已有的工作流模板（workflow）自定义节点参数，发起 ComfyUI 图像生成任务。\n\n适用于修改任意节点参数的场景，例如修改图生图中的采样器、步数、提示词、种子值等。  \n通过传入 `nodeInfoList` 实现动态参数替换，使得任务运行灵活可控。\n\n---\n\n## 请求地址\n\n```\nPOST https://www.runninghub.cn/task/openapi/create\n```\n\n---\n\n## 请求方式\n\n`POST`，请求体格式为 `application/json`\n\n---\n\n## 请求头部\n\n| Header          | 是否必填 | 示例值                 | 说明                       |\n|-----------------|----------|------------------------|----------------------------|\n| `Host`          | 是       | `www.runninghub.cn`    | API 域名，必须精确填写     |\n| `Content-Type`  | 是       | `application/json`     | 请求体类型                 |\n\n> ⚠️ 注意：某些 HTTP 客户端可能会自动添加 `Host` 头，但建议在接口测试或 SDK 实现时手动确认。\n\n---\n\n## 请求参数\n\n### 基础参数（必填）\n\n| 参数名         | 类型     | 是否必填 | 说明 |\n|----------------|----------|----------|------|\n| `apiKey`       | string   | 是       | 用户的 API 密钥，用于身份认证 |\n| `workflowId`   | string   | 是       | 工作流模板 ID，可通过平台导出获得 |\n| `nodeInfoList` | array    | 否       | 节点参数修改列表，用于在执行前替换默认参数 |\n\n#### nodeInfoList 结构说明\n\n每项表示一个节点参数的修改：\n\n| 字段         | 类型     | 说明 |\n|--------------|----------|------|\n| `nodeId`     | string   | 节点的唯一编号，来源于工作流 JSON 文件 |\n| `fieldName`  | string   | 要修改的字段名，例如 `text`、`seed`、`steps` |\n| `fieldValue` | any      | 替换后的新值，需与原字段类型一致 |\n\n#### 示例请求体\n\n```json\n{\n  \"apiKey\": \"your-api-key\",\n  \"workflowId\": \"1904136902449209346\",\n  \"nodeInfoList\": [\n    {\n      \"nodeId\": \"6\",\n      \"fieldName\": \"text\",\n      \"fieldValue\": \"1 girl in classroom\"\n    },\n    {\n      \"nodeId\": \"3\",\n      \"fieldName\": \"seed\",\n      \"fieldValue\": \"1231231\"\n    }\n  ]\n}\n```\n\n---\n\n## 附加参数（可选）\n\n| 参数名         | 类型     | 默认值 | 说明 |\n|----------------|----------|--------|------|\n| `addMetadata`  | boolean  | true   | 是否在图片中写入元信息（如提示词） |\n| `webhookUrl`   | string   | 无     | 任务完成后回调的 URL，平台会主动向该地址发送任务结果 |\n| `workflow`     | string   | 无     | 自定义完整工作流（JSON 字符串），如指定则忽略 `workflowId` |\n| `instanceType`     | string   | 无     | 发起任务指定实例类型|\n| `usePersonalQueue`     | boolean   | false     | 独占类型任务是否入队|\n\n---\n### usePersonalQueue 使用说明\n\n此参数只对独占类型的apiKey生效，若不想自己控制排队，可设置此参数为true，任务会自动进入排队状态，当用户持有的独占机器空闲时会自动执行；\n注意：单用户排队的数量限制为1000，超过会返回错误码(814, \"PERSONAL_QUEUE_COUNT_LIMIT\")\n\n```json\n\"usePersonalQueue\": \"true\"\n```\n---\n### instanceType 使用说明\n\n若希望发起plus任务到48G显存机器上执行，可设置 `instanceType` 参数。例如：\n\n```json\n\"instanceType\": \"plus\"\n```\n---\n### webhookUrl 使用说明（高级）\n\n若希望任务执行完成后平台自动通知结果，可设置 `webhookUrl` 参数。例如：\n\n```json\n\"webhookUrl\": \"https://your-webhook-url\"\n```\n\n> ⚠️ **推荐仅开发人员使用此参数**\n\n任务完成后，RunningHub 会向该地址发送如下 `POST` 请求：\n\n```json\n{\n  \"event\": \"TASK_END\",\n  \"taskId\": \"1904163390028185602\",\n  \"eventData\": \"{\\\"code\\\":0,\\\"msg\\\":\\\"success\\\",\\\"data\\\":[{\\\"fileUrl\\\":\\\"https://rh-images.xiaoyaoyou.com/de0db6f2564c8697b07df55a77f07be9/output/ComfyUI_00033_hpgko_1742822929.png\\\",\\\"fileType\\\":\\\"png\\\",\\\"taskCostTime\\\":0,\\\"nodeId\\\":\\\"9\\\"}]}\"\n}\n```\n\n- `event`：固定为 `TASK_END`\n- `taskId`：对应任务 ID\n- `eventData`：与“查询任务生成结果”接口返回结构一致\n\n> ⚠️ **特别注意**：接收 webhook 回调的接口**必须异步处理**，否则平台请求超时可能会触发**多次重试**。\n\n---\n\n## 返回结果\n\n### 成功响应示例\n\n```json\n{\n  \"code\": 0,\n  \"msg\": \"success\",\n  \"data\": {\n    \"netWssUrl\": null,\n    \"taskId\": \"1910246754753896450\",\n    \"clientId\": \"e825290b08ca2015b8f62f0bbdb5f5f6\",\n    \"taskStatus\": \"QUEUED\",\n    \"promptTips\": \"{\\\"result\\\": true, \\\"error\\\": null, \\\"outputs_to_execute\\\": [\\\"9\\\"], \\\"node_errors\\\": {}}\"\n  }\n}\n```\n\n### 返回字段说明\n\n| 字段名       | 类型     | 说明 |\n|--------------|----------|------|\n| `code`       | int      | 状态码，0 表示成功 |\n| `msg`        | string   | 提示信息 |\n| `data`       | object   | 返回数据对象，见下表 |\n\n#### data 子字段说明\n\n| 字段名        | 类型     | 说明 |\n|---------------|----------|------|\n| `taskId`      | string   | 创建的任务 ID，可用于查询状态或获取结果 |\n| `taskStatus`  | string   | 初始状态，可能为：`QUEUED`、`RUNNING`、`FAILED` |\n| `clientId`    | string   | 平台内部标识，用于排错，无需关注 |\n| `netWssUrl`   | string   | WebSocket 地址（当前不稳定，**不推荐使用**） |\n| `promptTips`  | string   | ComfyUI 校验信息（字符串格式的 JSON），可用于识别配置异常节点 |\n\n---\n\n## 使用建议\n\n- ✅ 在调用前请确认 `nodeId` 和 `fieldName` 的准确性\n- ✅ 可通过导出 workflow JSON 结构查看可配置字段\n- ⚠️ 如果返回 `promptTips` 含有错误信息，请根据 `nodeId` 精确排查问题\n- ✅ 推荐通过 `webhookUrl` 接收结果通知，或轮询状态与结果接口\n- ❌ 不建议使用 `netWssUrl` 监听实时状态（当前版本不稳定）\n\n---\n\n## 相关接口\n\n- [查询任务状态](https://www.runninghub.cn/runninghub-api-doc/api-276613252)\n- [查询任务生成结果](https://www.runninghub.cn/runninghub-api-doc/api-276613253)\n- [上传资源接口](https://www.runninghub.cn/runninghub-api-doc/api-276613256)\n- [获取上传 Lora 链接接口](https://www.runninghub.cn/runninghub-api-doc/api-276613257)\n", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 6, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept": true, "accept-encoding": true, "connection": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.725618127"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "发起AI应用任务", "api": {"id": "324692872", "method": "post", "path": "/task/openapi/ai-app/run", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "aKbw9gN9T2", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "725618128", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/185908589", "description": ""}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": {\n        \"netWssUrl\": \"wss://www.runninghub.cn:443/ws/c_instance?c_host=***************&c_port=85&clientId=14caa1db2110a81629c101b9bb4cb0ce&workflowId=1876205853438365698&Rh-Comfy-Auth=eyJ1c2VySWQiOiJkZTBkYjZmMjU2NGM4Njk3YjA3ZGY1NWE3N2YwN2JlOSIsInNpZ25FeHBpcmUiOjE3NDQxMTI1MjEyMzYsInRzIjoxNzQzNTA3NzIxMjM2LCJzaWduIjoiZDExOTE0MzkwMjJlNjViMjQ5MjU2YzU2ZmQxYTUwZjUifQ%3D%3D\",\n        \"taskId\": \"1907035719658053634\",\n        \"clientId\": \"14caa1db2110a81629c101b9bb4cb0ce\",\n        \"taskStatus\": \"RUNNING\",\n        \"promptTips\": \"{\\\"result\\\": true, \\\"error\\\": null, \\\"outputs_to_execute\\\": [\\\"115\\\", \\\"129\\\", \\\"124\\\"], \\\"node_errors\\\": {}}\"\n    }\n}", "responseId": 725618128, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/185908592", "description": ""}, "oasExtensions": ""}, "description": "在AI应用详情页中可查看示例nodeInfoList", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 12, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.725618128"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取工作流Json", "api": {"id": "324692873", "method": "post", "path": "/api/openapi/getJsonApiFormat", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "uiTkX64H6C", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "725618129", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/185908590"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n    \"code\": 0,\n    \"msg\": \"SUCCESS\",\n    \"data\": {\n        \"prompt\": \"{\\\"3\\\":{\\\"class_type\\\":\\\"KSampler\\\",\\\"inputs\\\":{\\\"scheduler\\\":\\\"karras\\\",\\\"negative\\\":[\\\"7\\\",0],\\\"denoise\\\":1,\\\"latent_image\\\":[\\\"5\\\",0],\\\"seed\\\":669816362794144,\\\"cfg\\\":8,\\\"sampler_name\\\":\\\"dpmpp_2m\\\",\\\"model\\\":[\\\"4\\\",0],\\\"positive\\\":[\\\"6\\\",0],\\\"steps\\\":20},\\\"_meta\\\":{\\\"title\\\":\\\"KSampler\\\"}},\\\"4\\\":{\\\"class_type\\\":\\\"CheckpointLoaderSimple\\\",\\\"inputs\\\":{\\\"ckpt_name\\\":\\\"MR 3DQ _SDXL V0.2.safetensors\\\"},\\\"_meta\\\":{\\\"title\\\":\\\"Load Checkpoint\\\"}},\\\"37\\\":{\\\"class_type\\\":\\\"VAEDecode\\\",\\\"inputs\\\":{\\\"vae\\\":[\\\"4\\\",2],\\\"samples\\\":[\\\"3\\\",0]},\\\"_meta\\\":{\\\"title\\\":\\\"VAE Decode\\\"}},\\\"5\\\":{\\\"class_type\\\":\\\"EmptyLatentImage\\\",\\\"inputs\\\":{\\\"batch_size\\\":1,\\\"width\\\":512,\\\"height\\\":512},\\\"_meta\\\":{\\\"title\\\":\\\"Empty Latent Image\\\"}},\\\"6\\\":{\\\"class_type\\\":\\\"CLIPTextEncode\\\",\\\"inputs\\\":{\\\"speak_and_recognation\\\":{\\\"__value__\\\":[false,true]},\\\"text\\\":\\\"DreamWork 3D Style, a cute panda holding a bamboo in hands at sunset, highly detailed, ultra-high resolutions, 32K UHD, best quality, masterpiece, \\\",\\\"clip\\\":[\\\"4\\\",1]},\\\"_meta\\\":{\\\"title\\\":\\\"CLIP Text Encode (Prompt)\\\"}},\\\"7\\\":{\\\"class_type\\\":\\\"CLIPTextEncode\\\",\\\"inputs\\\":{\\\"speak_and_recognation\\\":{\\\"__value__\\\":[false,true]},\\\"text\\\":\\\"\\\",\\\"clip\\\":[\\\"4\\\",1]},\\\"_meta\\\":{\\\"title\\\":\\\"CLIP Text Encode (Prompt)\\\"}},\\\"9\\\":{\\\"class_type\\\":\\\"SaveImage\\\",\\\"inputs\\\":{\\\"filename_prefix\\\":\\\"ComfyUI\\\",\\\"images\\\":[\\\"37\\\",0]},\\\"_meta\\\":{\\\"title\\\":\\\"Save Image\\\"}}}\"\n    }\n}", "responseId": 725618129, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/185908588", "description": ""}, "oasExtensions": ""}, "description": "\n\n", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 18, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept": true, "accept-encoding": true, "connection": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.725618129"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "查询任务状态", "api": {"id": "324692874", "method": "post", "path": "https://www.runninghub.cn/task/openapi/status", "parameters": {"query": [], "path": [], "cookie": [], "header": [{"id": "7DLI3b0HY0", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string", "enable": true}]}, "auth": {"type": "<PERSON><PERSON><PERSON>"}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": [{"name": "authorization"}, {"name": "X-Instance-ID"}, {"name": "Authorization"}]}, "responses": [{"id": "725618130", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"description": "", "type": "object", "x-apifox-refs": {"01JWFYNVYGDCC4TYVZQ04WA6R8": {"$ref": "#/definitions/185908611", "x-apifox-overrides": {"data": {"type": "string", "description": "[\"QUEUED\",\"RUNNING\",\"FAILED\",\"SUCCESS\"]"}}}}, "x-apifox-orders": ["01JWFYNVYGDCC4TYVZQ04WA6R8"], "properties": {}}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"code\": 0,\n  \"msg\": \"\",\n  \"data\": \"\"\n}", "responseId": 725618130, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/185908598", "description": ""}, "examples": [{"value": "{\r\n    \"apiKey\": \"ee754f56ae3e4895ae1179a53af6ae6f\",\r\n    \"taskId\": \"1947247304483971074\"\r\n}", "mediaType": "application/json", "description": ""}], "oasExtensions": ""}, "description": "", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 24, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept": true, "accept-encoding": true, "connection": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.725618130"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "查询任务生成结果", "api": {"id": "324692875", "method": "post", "path": "https://www.runninghub.cn/task/openapi/outputs", "parameters": {"query": [], "path": [], "cookie": [], "header": [{"id": "BsgIHdaheu", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string", "enable": true}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": [{"name": "authorization"}, {"name": "X-Instance-ID"}, {"name": "Authorization"}]}, "responses": [{"id": "725618131", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********", "description": ""}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": [\n        {\n            \"fileUrl\": \"https://rh-images.xiaoyaoyou.com/de0db6f2564c8697b07df55a77f07be9/output/ComfyUI_00033_hpgko_1742822929.png\",\n            \"fileType\": \"png\",\n            \"taskCostTime\": \"0\",\n            \"nodeId\": \"9\"\n        }\n    ]\n}", "responseId": 725618131, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}, {"name": "任务失败会返失败原因", "data": "{\r\n    \"code\": 805,\r\n    \"msg\": \"APIKEY_TASK_STATUS_ERROR\",\r\n    \"data\":\"{\\\"failedReason\\\":\\\"{\\\\\\\"current_outputs\\\\\\\":\\\\\\\"{}\\\\\\\",\\\\\\\"exception_type\\\\\\\":\\\\\\\"RuntimeError\\\\\\\",\\\\\\\"node_name\\\\\\\":\\\\\\\"LoadAudio\\\\\\\",\\\\\\\"current_inputs\\\\\\\":\\\\\\\"{}\\\\\\\",\\\\\\\"traceback\\\\\\\":\\\\\\\"[\\\\\\\\\\\\\\\"  File \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"/workspace/ComfyUI/execution.py\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", line 507, in execute\\\\\\\\\\\\\\\\n    output_data, output_ui, has_subgraph \\\\\\= get_output_data(obj, input_data_all, execution_block_cb\\\\\\=execution_block_cb, pre_execute_cb\\\\\\=pre_execute_cb)\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"  File \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"/workspace/ComfyUI/execution.py\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", line 235, in get_output_data\\\\\\\\\\\\\\\\n    return_values \\\\\\= _map_node_over_list(obj, input_data_all, obj.FUNCTION, allow_interrupt\\\\\\=True, execution_block_cb\\\\\\=execution_block_cb, pre_execute_cb\\\\\\=pre_execute_cb)\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"  File \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"/workspace/ComfyUI/custom_nodes/ComfyUI-0246/utils.py\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", line 353, in new_func\\\\\\\\\\\\\\\\n    res_value \\\\\\= old_func(*final_args, **kwargs)\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"  File \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"/workspace/ComfyUI/execution.py\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", line 207, in _map_node_over_list\\\\\\\\\\\\\\\\n    process_inputs(input_dict, i)\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"  File \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"/workspace/ComfyUI/execution.py\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", line 196, in process_inputs\\\\\\\\\\\\\\\\n    results.append(getattr(obj, func)(**inputs))\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"  File \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"/workspace/ComfyUI/comfy_extras/nodes_audio.py\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", line 228, in load\\\\\\\\\\\\\\\\n    waveform, sample_rate \\\\\\= torchaudio.load(audio_path)\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"  File \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"/usr/local/lib/python3.10/dist-packages/torchaudio/_backend/utils.py\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", line 205, in load\\\\\\\\\\\\\\\\n    return backend.load(uri, frame_offset, num_frames, normalize, channels_first, format, buffer_size)\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"  File \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"/usr/local/lib/python3.10/dist-packages/torchaudio/_backend/ffmpeg.py\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", line 297, in load\\\\\\\\\\\\\\\\n    return load_audio(uri, frame_offset, num_frames, normalize, channels_first, format)\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"  File \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"/usr/local/lib/python3.10/dist-packages/torchaudio/_backend/ffmpeg.py\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", line 88, in load_audio\\\\\\\\\\\\\\\\n    s \\\\\\= torchaudio.io.StreamReader(src, format, None, buffer_size)\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"  File \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"/usr/local/lib/python3.10/dist-packages/torio/io/_streaming_media_decoder.py\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", line 526, in __init__\\\\\\\\\\\\\\\\n    self._be \\\\\\= ffmpeg_ext.StreamingMediaDecoder(os.path.normpath(src), format, option)\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\"]\\\\\\\",\\\\\\\"node_id\\\\\\\":\\\\\\\"12\\\\\\\",\\\\\\\"exception_message\\\\\\\":\\\\\\\"Failed to open the input \\\\\\\\\\\\\\\"/data/ComfyUI/personal/input\\\\\\\\\\\\\\\" (Is a directory).\\\\\\\\nException raised from get_input_format_context at /__w/audio/audio/pytorch/audio/src/libtorio/ffmpeg/stream_reader/stream_reader.cpp:42 (most recent call first):\\\\\\\\nframe #0: c10::Error::Error(c10::SourceLocation, std::string) + 0x96 (0x7838c376c446 in /usr/local/lib/python3.10/dist-packages/torch/lib/libc10.so)\\\\\\\\nframe #1: c10::detail::torchCheckFail(char const*, char const*, unsigned int, std::string const\\\\\\&) + 0x64 (0x7838c37166e4 in /usr/local/lib/python3.10/dist-packages/torch/lib/libc10.so)\\\\\\\\nframe #2: \\\\\\<unknown function\\\\\\> + 0x42134 (0x78383feca134 in /usr/local/lib/python3.10/dist-packages/torio/lib/libtorio_ffmpeg4.so)\\\\\\\\nframe #3: torio::io::StreamingMediaDecoder::StreamingMediaDecoder(std::string const\\\\\\&, std::optional\\\\\\<std::string\\\\\\> const\\\\\\&, std::optional\\\\\\<std::map\\\\\\<std::string, std::string, std::less\\\\\\<std::string\\\\\\>, std::allocator\\\\\\<std::pair\\\\\\<std::string const, std::string\\\\\\> \\\\\\> \\\\\\> \\\\\\> const\\\\\\&) + 0x14 (0x78383feccb34 in /usr/local/lib/python3.10/dist-packages/torio/lib/libtorio_ffmpeg4.so)\\\\\\\\nframe #4: \\\\\\<unknown function\\\\\\> + 0x3a19e (0x78375baf319e in /usr/local/lib/python3.10/dist-packages/torio/lib/_torio_ffmpeg4.so)\\\\\\\\nframe #5: \\\\\\<unknown function\\\\\\> + 0x31d47 (0x78375baead47 in /usr/local/lib/python3.10/dist-packages/torio/lib/_torio_ffmpeg4.so)\\\\\\\\n\\\\\\<omitting python frames\\\\\\>\\\\\\\\nframe #11: \\\\\\<unknown function\\\\\\> + 0xf72b (0x7838bfe1672b in /usr/local/lib/python3.10/dist-packages/torchaudio/lib/_torchaudio.so)\\\\\\\\nframe #57: \\\\\\<unknown function\\\\\\> + 0x94ac3 (0x783941c7aac3 in /usr/lib/x86_64-linux-gnu/libc.so.6)\\\\\\\\nframe #58: \\\\\\<unknown function\\\\\\> + 0x126a40 (0x783941d0ca40 in /usr/lib/x86_64-linux-gnu/libc.so.6)\\\\\\\\n\\\\\\\"}\\\"}\"\r\n}", "responseId": 725618131, "ordering": 2, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/185908598", "description": ""}, "examples": [{"value": "{\r\n    \"apiKey\": \"ee754f56ae3e4895ae1179a53af6ae6f\",\r\n    \"taskId\": \"1947247817204080642\"\r\n}", "mediaType": "application/json", "description": ""}], "oasExtensions": ""}, "description": "", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 30, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept": true, "accept-encoding": true, "connection": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.725618131"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "取消ComfyUI任务", "api": {"id": "324692876", "method": "post", "path": "/task/openapi/cancel", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "HWOeusfQj3", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********", "description": ""}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": null\n}", "responseId": *********, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}, {"name": "任务不存在", "data": "{\n    \"code\": 807,\n    \"msg\": \"APIKEY_TASK_NOT_FOUND\",\n    \"data\": null\n}", "responseId": *********, "ordering": 2, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/185908598", "description": ""}, "oasExtensions": ""}, "description": "", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 36, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept": true, "accept-encoding": true, "connection": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.*********"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取账户信息", "api": {"id": "*********", "method": "post", "path": "/uc/openapi/accountStatus", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "VBC2Ne5xLu", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"code\": 0,\n  \"msg\": \"\",\n  \"data\": {\n    \"remainCoins\": 0,\n    \"currentTaskCounts\": 0\n  }\n}", "responseId": *********, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}, {"name": "成功示例", "data": "{\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": {\n        \"remainCoins\": \"177814\",\n        \"currentTaskCounts\": \"0\",\n        \"remainMoney\": null,\n        \"currency\": null,\n        \"apiType\": \"NORMAL\"\n    }\n}", "responseId": *********, "ordering": 2, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/185908600", "description": ""}, "oasExtensions": ""}, "description": "", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 42, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept": true, "accept-encoding": true, "connection": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.*********"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "上传资源（图片、视频、音频）", "api": {"id": "324692878", "method": "post", "path": "/task/openapi/upload", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "tq9xbO5p5C", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "725618234", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/185908612", "description": ""}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": {\n        \"fileName\": \"api/9d77b8530f8b3591edc5c4e8f3f55b2cf0960bb2ca35c04e32c1677687866576.png\",\n        \"fileType\": \"image\"\n    }\n}", "responseId": 725618234, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}, {"name": "文件超过10M", "data": "{\n    \"code\": 809,\n    \"msg\": \"APIKEY_FILE_SIZE_EXCEEDED\",\n    \"data\": null\n}", "responseId": 725618234, "ordering": 2, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "multipart/form-data", "parameters": [{"id": "cTpHSGJbtJ", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "description": "", "example": "{{a<PERSON><PERSON><PERSON>}}", "type": "string", "contentType": ""}, {"id": "MgZp6zL1x1", "name": "file", "required": false, "description": "", "example": "file://D:\\temp\\ComfyUI_00743_uiqpt_1742470204.png", "type": "file", "contentType": ""}, {"id": "TwzSJlf97B", "name": "fileType", "required": false, "description": "", "example": "image", "type": "string", "contentType": ""}], "jsonSchema": {"$ref": "#/definitions/185908601", "description": ""}, "oasExtensions": ""}, "description": "# RunningHub 图片上传说明（用于图生图场景）\n\n## 📌 场景简介\n\n本接口用于“图生图”流程中上传图片至 RunningHub 服务器。上传后的图片将被 `LoadImage` 节点加载，作为工作流中的输入图像使用。\n\n⚠️ **注意事项：**\n\n* **不是图床服务**：上传后的图片**无法通过 URL 直接访问**。\n* 返回的 `fileName` 字段为图片在服务器上的相对路径，请勿随意拼接为外链访问。\n\n---\n\n## 📤 上传要求\n\n* **支持类型**：图片文件（如 JPG, PNG）\n* **单文件大小限制**：**10MB**\n\n> ✅ **推荐做法**：\n>\n> * 图片超过 10MB 时，**请上传到云存储（如 OSS、COS、S3 等）**，并将图片的 **公开直链 URL** 传入工作流的 `LoadImage` 节点。\n> * 云图像路径支持外链访问，但请确保链接可访问，且稳定可靠。\n\n---\n\n## 🧾 上传响应示例\n\n上传成功后，服务端会返回如下 JSON 响应：\n\n```json\n{\n  \"code\": 0,\n  \"msg\": \"success\",\n  \"data\": {\n    \"fileName\": \"api/9d77b8530f8b3591edc5c4e8f3f55b2cf0960bb2ca35c04e32c1677687866576.png\",\n    \"fileType\": \"image\"\n  }\n}\n```\n\n* `fileName`：用于加载图片的路径，**这是关键字段**\n\n---\n\n## 📥 如何将图片用于 `LoadImage` 节点？\n\n获取上传返回的 `fileName` 后，可在工作流配置中填入如下结构：\n\n```json\n\"nodeInfoList\": [\n  {\n    \"nodeId\": \"10\",\n    \"fieldName\": \"image\",\n    \"fieldValue\": \"api/9d77b8530f8b3591edc5c4e8f3f55b2cf0960bb2ca35c04e32c1677687866576.png\"\n  }\n]\n```\n\n* `nodeId`：`LoadImage` 节点的编号\n* `fieldName`：字段名，图像输入请使用 `\"image\"`\n* `fieldValue`：上传返回的 `fileName` 字段值\n\n---\n\n如需进一步支持，请联系技术团队。\n", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 48, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept-encoding": true, "connection": true, "accept": true, "content-type": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.725618234"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "上传Lora-获取Lora上传地址", "api": {"id": "324692879", "method": "post", "path": "/api/openapi/getLoraUploadUrl", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "FAWmLMwXDH", "name": "Host", "required": false, "description": "", "example": "www.runninghub.cn", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "725618235", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": {\n        \"fileName\": \"api-lora-cn/f8d958506e6c8044f79ccd7c814c6179.safetensors\",\n        \"url\": \"https://rh-models-1252422369.cos.ap-beijing.myqcloud.com/api-lora-cn/f8d958506e6c8044f79ccd7c814c6179.safetensors?q-sign-algorithm=sha1&q-ak=AKIDv56FISEJUsKsMeELk0gmbNCGKTYSaZ3N&q-sign-time=1742889838%3B1742893438&q-key-time=1742889838%3B1742893438&q-header-list=host&q-url-param-list=&q-signature=72361dbd6e4eb58ba32fb575400f942f097e47ea\"\n    }\n}", "responseId": 725618235, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/*********", "description": ""}, "oasExtensions": ""}, "description": "# RHLoraLoader 专用 LoRA 上传接口说明\n\n⚠️ **注意事项**\n\n* 此接口上传的 **LoRA 模型文件** 与平台常规上传方式不同，仅限通过 `RHLoraLoader` 节点调用，**其他节点无法识别和使用**。\n* 上传的 LoRA 会以 `md5Hex` 为唯一标识进行缓存，请务必**准确计算并使用正确的 `md5Hex` 值**进行上传。\n\n---\n\n## 上传接口响应示例\n\n成功调用上传接口后，返回如下 JSON 数据：\n\n```json\n{\n  \"code\": 0,\n  \"msg\": \"success\",\n  \"data\": {\n    \"fileName\": \"api-lora-cn/f8d958506e6c8044f79ccd7c814c6179.safetensors\",\n    \"url\": \"https://rh-models-1252422369.cos.ap-beijing.myqcloud.com/api-lora-cn/f8d958506e6c8044f79ccd7c814c6179.safetensors?q-sign-algorithm=sha1&q-ak=AKIDv56FISEJUsKsMeELk0gmbNCGKTYSaZ3N&q-sign-time=1742960296%3B1742963896&q-key-time=1742960296%3B1742963896&q-header-list=host&q-url-param-list=&q-signature=623812b89cf6bc4cb51dc3824436cc921f02aa0e\"\n  }\n}\n```\n\n---\n\n## 上传 LoRA 模型文件至云服务\n\n请使用获取到的 `url` 地址，通过 **PUT 请求** 将本地 `.safetensors` 文件上传至云服务：\n\n```bash\ncurl --location --request PUT 'https://rh-models-1252422369.cos.ap-beijing.myqcloud.com/api-lora-cn/f8d958506e6c8044f79ccd7c814c6179.safetensors?q-sign-algorithm=sha1&q-ak=AKIDv56FISEJUsKsMeELk0gmbNCGKTYSaZ3N&q-sign-time=1742821197%3B1742824797&q-key-time=1742821197%3B1742824797&q-header-list=host&q-url-param-list=&q-signature=28c6eb55d944709ab1dc381290867fb520cd7610' \\\n--header 'Content-Type: application/octet-stream' \\\n--data-binary '@D:\\temp\\my-lora-name.safetensors'\n```\n\n---\n\n## 使用说明\n\n上传完成后 **立即可用**，无需等待同步。\n\n将响应中的 `fileName` 值：\n\n```\napi-lora-cn/f8d958506e6c8044f79ccd7c814c6179.safetensors\n```\n\n填入 `RHLoraLoader` 节点对应参数即可调用该 LoRA 模型。\n", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 54, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept": true, "accept-encoding": true, "connection": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.725618235"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取webhook事件详情", "api": {"id": "324692880", "method": "post", "path": "/task/openapi/getWebhookDetail", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "xJ7P6l8AIV", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "725618236", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n    \"code\": 0,\n    \"msg\": \"success\",\n    \"data\": {\n        \"id\": \"1904444422778503169\",\n        \"userApiKey\": \"******************\",\n        \"taskId\": \"1904444422770114561\",\n        \"webhookUrl\": \"https://your-webhook-url\",\n        \"eventData\": \"{\\\"code\\\":0,\\\"msg\\\":\\\"success\\\",\\\"data\\\":[{\\\"fileUrl\\\":\\\"https://rh-images.xiaoyaoyou.com/de0db6f2564c8697b07df55a77f07be9/output/ComfyUI_00059_hnona_1742889987.png\\\",\\\"fileType\\\":\\\"png\\\",\\\"taskCostTime\\\":78,\\\"nodeId\\\":\\\"9\\\"}]}\",\n        \"callbackStatus\": \"FAILED\",\n        \"callbackResponse\": \"I/O error on POST request for \\\"https://your-webhook-url\\\": Remote host terminated the handshake\",\n        \"retryCount\": 3,\n        \"createTime\": \"2025-03-25T16:05:07\",\n        \"updateTime\": \"2025-03-25T16:08:10\"\n    }\n}", "responseId": 725618236, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/185908602", "description": ""}, "oasExtensions": ""}, "description": "此接口旨在帮助调试用户的webhook，通过taskId查询到当前webhook事件的详细状态，拿到事件的id后可以发起重试", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 60, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept": true, "accept-encoding": true, "connection": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.725618236"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "重新发送指定webhook事件", "api": {"id": "324692881", "method": "post", "path": "/task/openapi/retryWebhook", "parameters": {"path": [], "query": [], "cookie": [], "header": [{"id": "y7RVOmF60d", "name": "Host", "required": true, "description": "", "example": "www.runninghub.cn", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "725618237", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"code\": 0,\n  \"msg\": \"\",\n  \"data\": null\n}", "responseId": 725618237, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/185908603", "description": ""}, "oasExtensions": ""}, "description": "webhookId 为 获取webhook事件详情中返回的id", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 66, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {"cookie": true, "user-agent": true, "accept": true, "accept-encoding": true, "connection": true, "host": true}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.725618237"], "visibility": "INHERITED", "moduleId": 4342947, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [{"name": "根目录", "parentId": 0, "description": "", "moduleId": 4342947, "items": [{"name": "后端接口", "parentId": 0, "description": "", "moduleId": 4342947, "items": [{"name": "实例接口", "parentId": 57031751, "description": "", "moduleId": 4342947, "items": [{"name": "等待", "api": {"id": "3632682", "path": "ws://jt6-rnliqjgjgg3zr1nvo-3w4d0o0e-custom.service.onethingrobot.com", "parameters": {"query": [{"id": "EJLgDYX4lL", "name": "api_key", "example": "9b1b825a734bfe35b703d4bc01a2396e", "required": false, "description": "", "enable": true, "type": "string"}, {"id": "93DRBetAgD", "name": "instance_id", "example": "tru42aamuc6mzyyl-c3tta1n5", "required": false, "description": "", "enable": true, "type": "string"}]}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "requestBody": {"parameters": []}, "description": "", "tags": [], "status": "developing", "ordering": 10, "advancedSettings": {}, "moduleId": 4342947}}]}]}]}], "socketIOCollection": [], "responseCollection": [{"_databaseId": 6682484, "updatedAt": "2025-05-19T02:38:15.000Z", "name": "根目录", "type": "root", "children": [], "moduleId": 4342947, "parentId": 0, "id": 6682484, "items": [{"_databaseId": 341328998, "name": "记录不存在", "moduleId": 4342947, "code": 404, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string", "mock": {"mock": "Not found"}}}, "required": ["code", "message"], "x-apifox-orders": ["code", "message"]}, "defaultEnable": false, "folderId": 0, "id": 341328998, "databaseResponseExamples": [], "responseExamples": []}, {"_databaseId": 341328999, "name": "参数不正确", "moduleId": 4342947, "code": 400, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "mock": {"mock": "400"}}, "message": {"type": "string", "mock": {"mock": "Invalid input"}}}, "required": ["code", "message"], "x-apifox-orders": ["code", "message"]}, "defaultEnable": false, "folderId": 0, "id": 341328999, "databaseResponseExamples": [], "responseExamples": []}]}], "schemaCollection": [{"id": 14409308, "name": "根目录", "visibility": "SHARED", "moduleId": 4342947, "items": [{"name": "获取工作流Json Request", "displayName": "", "id": "#/definitions/185908588", "description": "com.haima.runninghub.common.model.GetJsonApiFormatRequest", "schema": {"jsonSchema": {"type": "object", "properties": {"apiKey": {"type": "string", "x-apifox-mock": "{{a<PERSON><PERSON><PERSON>}}", "description": "", "examples": ["{{a<PERSON><PERSON><PERSON>}}"]}, "workflowId": {"type": "string", "x-apifox-mock": "1904136902449209346", "examples": ["1904136902449209346"]}}, "x-apifox-orders": ["<PERSON><PERSON><PERSON><PERSON>", "workflowId"], "required": ["workflowId", "<PERSON><PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "RTaskCreateResponse", "displayName": "", "id": "#/definitions/185908589", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回标记：成功标记=0，非0失败，或者是功能码"}, "msg": {"type": "string", "description": "返回信息"}, "data": {"$ref": "#/definitions/185908597", "description": "数据"}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "TaskCreateResponse", "displayName": "", "id": "#/definitions/185908597", "description": "com.haima.runninghub.common.model.response.task.TaskCreateResponse", "schema": {"jsonSchema": {"type": "object", "properties": {"netWssUrl": {"type": "string", "description": "Wss服务地址"}, "taskId": {"type": "integer", "description": "任务Id", "format": "int64"}, "clientId": {"type": "string", "description": "客户端ID，当客户端首次接收clientId时，需要保存到本地，以便页面刷新重连或者二次运行任务传参使用"}, "taskStatus": {"type": "string", "description": "任务状态: CREATE, SUCCESS, FAILED, RUNNING, QUEUED;"}, "promptTips": {"type": "string", "description": "工作流验证结果提示,当不为空是UI需要展示节点错误信息"}}, "x-apifox-orders": ["netWssUrl", "taskId", "clientId", "taskStatus", "promptTips"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "获取工作流Json Response", "displayName": "", "id": "#/definitions/185908590", "description": "R", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回标记：成功标记=0，非0失败，或者是功能码", "examples": [0]}, "msg": {"type": "string", "description": "返回信息", "examples": ["success"]}, "data": {"type": "object", "properties": {"prompt": {"type": "string", "examples": ["{\\\"3\\\":{\\\"class_type\\\":\\\"KSampler\\\",\\\"inputs\\\":{\\\"scheduler\\\":\\\"karras\\\",\\\"negative\\\":[\\\"7\\\",0],\\\"denoise\\\":1,\\\"latent_image\\\":[\\\"5\\\",0],\\\"seed\\\":669816362794144,\\\"cfg\\\":8,\\\"sampler_name\\\":\\\"dpmpp_2m\\\",\\\"model\\\":[\\\"4\\\",0],\\\"positive\\\":[\\\"6\\\",0],\\\"steps\\\":20},\\\"_meta\\\":{\\\"title\\\":\\\"KSampler\\\"}},\\\"4\\\":{\\\"class_type\\\":\\\"CheckpointLoaderSimple\\\",\\\"inputs\\\":{\\\"ckpt_name\\\":\\\"MR 3DQ _SDXL V0.2.safetensors\\\"},\\\"_meta\\\":{\\\"title\\\":\\\"Load Checkpoint\\\"}},\\\"37\\\":{\\\"class_type\\\":\\\"VAEDecode\\\",\\\"inputs\\\":{\\\"vae\\\":[\\\"4\\\",2],\\\"samples\\\":[\\\"3\\\",0]},\\\"_meta\\\":{\\\"title\\\":\\\"VAE Decode\\\"}},\\\"5\\\":{\\\"class_type\\\":\\\"EmptyLatentImage\\\",\\\"inputs\\\":{\\\"batch_size\\\":1,\\\"width\\\":512,\\\"height\\\":512},\\\"_meta\\\":{\\\"title\\\":\\\"Empty Latent Image\\\"}},\\\"6\\\":{\\\"class_type\\\":\\\"CLIPTextEncode\\\",\\\"inputs\\\":{\\\"speak_and_recognation\\\":{\\\"__value__\\\":[false,true]},\\\"text\\\":\\\"DreamWork 3D Style, a cute panda holding a bamboo in hands at sunset, highly detailed, ultra-high resolutions, 32K UHD, best quality, masterpiece, \\\",\\\"clip\\\":[\\\"4\\\",1]},\\\"_meta\\\":{\\\"title\\\":\\\"CLIP Text Encode (Prompt)\\\"}},\\\"7\\\":{\\\"class_type\\\":\\\"CLIPTextEncode\\\",\\\"inputs\\\":{\\\"speak_and_recognation\\\":{\\\"__value__\\\":[false,true]},\\\"text\\\":\\\"\\\",\\\"clip\\\":[\\\"4\\\",1]},\\\"_meta\\\":{\\\"title\\\":\\\"CLIP Text Encode (Prompt)\\\"}},\\\"9\\\":{\\\"class_type\\\":\\\"SaveImage\\\",\\\"inputs\\\":{\\\"filename_prefix\\\":\\\"ComfyUI\\\",\\\"images\\\":[\\\"37\\\",0]},\\\"_meta\\\":{\\\"title\\\":\\\"Save Image\\\"}}}"]}}, "x-apifox-orders": ["prompt"], "description": "数据"}}, "x-apifox-orders": ["code", "msg", "data"], "required": ["code", "msg"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "发起ComfyUI任务 Request 1", "displayName": "", "id": "#/definitions/185908591", "description": "com.haima.runninghub.common.model.request.task.TaskCreateByKeyRequest", "schema": {"jsonSchema": {"type": "object", "properties": {"workflowId": {"type": "string", "examples": ["1904136902449209346"]}, "apiKey": {"type": "string", "description": "", "examples": ["{{a<PERSON><PERSON><PERSON>}}"]}, "addMetadata": {"type": "boolean", "description": ""}}, "x-apifox-orders": ["<PERSON><PERSON><PERSON><PERSON>", "workflowId", "addMetadata"], "required": ["workflowId", "<PERSON><PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "TaskRunWebappByKeyRequest", "displayName": "", "id": "#/definitions/185908592", "description": "com.haima.runninghub.common.model.request.TaskRunWebappByKeyRequest", "schema": {"jsonSchema": {"type": "object", "properties": {"apiKey": {"type": "string", "description": ""}, "webappId": {"type": "integer", "description": "", "format": "int64"}, "nodeInfoList": {"type": "array", "items": {"$ref": "#/definitions/185908594", "description": "com.haima.runninghub.common.model.pojo.NodeInfo"}, "description": ""}, "webhookUrl": {"type": "string", "description": ""}}, "x-apifox-orders": ["<PERSON><PERSON><PERSON><PERSON>", "webappId", "nodeInfoList", "webhookUrl"], "required": ["webappId", "nodeInfoList", "<PERSON><PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "NodeInfo", "displayName": "", "id": "#/definitions/185908594", "description": "com.haima.runninghub.common.model.pojo.NodeInfo", "schema": {"jsonSchema": {"type": "object", "properties": {"nodeId": {"type": "string", "description": ""}, "nodeName": {"type": "string", "description": ""}, "fieldName": {"type": "string", "description": ""}, "fieldValue": {"type": "string", "description": ""}, "fieldData": {"type": "string", "description": ""}, "description": {"type": "string", "description": ""}, "descriptionEn": {"type": "string", "description": ""}}, "x-apifox-orders": ["nodeId", "nodeName", "fieldName", "fieldValue", "fieldData", "description", "descriptionEn"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "发起ComfyUI任务 Request 2", "displayName": "", "id": "#/definitions/185908593", "description": "com.haima.runninghub.common.model.request.task.TaskCreateByKeyRequest", "schema": {"jsonSchema": {"type": "object", "properties": {"workflowId": {"type": "string", "examples": ["1904136902449209346"]}, "apiKey": {"type": "string", "description": "", "examples": ["{{a<PERSON><PERSON><PERSON>}}"]}, "nodeInfoList": {"type": "array", "items": {"$ref": "#/definitions/185908599"}, "description": ""}, "workflow": {"type": "string", "description": "", "examples": ["{\"3\":{\"class_type\":\"KSampler\",\"inputs\":{\"scheduler\":\"karras\",\"negative\":[\"7\",0],\"denoise\":1,\"latent_image\":[\"5\",0],\"seed\":669816362794144,\"cfg\":8,\"sampler_name\":\"dpmpp_2m\",\"model\":[\"4\",0],\"positive\":[\"6\",0],\"steps\":20},\"_meta\":{\"title\":\"KSampler\"}},\"4\":{\"class_type\":\"CheckpointLoaderSimple\",\"inputs\":{\"ckpt_name\":\"MR 3DQ _SDXL V0.2.safetensors\"},\"_meta\":{\"title\":\"Load Checkpoint\"}},\"37\":{\"class_type\":\"VAEDecode\",\"inputs\":{\"vae\":[\"4\",2],\"samples\":[\"3\",0]},\"_meta\":{\"title\":\"VAE Decode\"}},\"5\":{\"class_type\":\"EmptyLatentImage\",\"inputs\":{\"batch_size\":1,\"width\":512,\"height\":512},\"_meta\":{\"title\":\"Empty Latent Image\"}},\"6\":{\"class_type\":\"CLIPTextEncode\",\"inputs\":{\"speak_and_recognation\":{\"__value__\":[false,true]},\"text\":\"DreamWork 3D Style, a cute panda holding a bamboo in hands at sunset, highly detailed, ultra-high resolutions, 32K UHD, best quality, masterpiece, \",\"clip\":[\"4\",1]},\"_meta\":{\"title\":\"CLIP Text Encode (Prompt)\"}},\"7\":{\"class_type\":\"CLIPTextEncode\",\"inputs\":{\"speak_and_recognation\":{\"__value__\":[false,true]},\"text\":\"\",\"clip\":[\"4\",1]},\"_meta\":{\"title\":\"CLIP Text Encode (Prompt)\"}},\"9\":{\"class_type\":\"SaveImage\",\"inputs\":{\"filename_prefix\":\"ComfyUI\",\"images\":[\"37\",0]},\"_meta\":{\"title\":\"Save Image\"}}}"]}, "addMetadata": {"type": "boolean", "description": ""}}, "x-apifox-orders": ["<PERSON><PERSON><PERSON><PERSON>", "workflowId", "nodeInfoList", "workflow", "addMetadata"], "required": ["workflowId", "<PERSON><PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "节点输入信息", "displayName": "", "id": "#/definitions/185908599", "description": "com.haima.runninghub.common.model.pojo.NodeInfo", "schema": {"jsonSchema": {"type": "object", "properties": {"nodeId": {"type": "string", "description": "", "examples": ["6"]}, "fieldName": {"type": "string", "description": "", "examples": ["text"]}, "fieldValue": {"type": "string", "description": "", "examples": ["1 girl in classroom"]}}, "x-apifox-orders": ["nodeId", "fieldName", "fieldValue"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "发起ComfyUI任务 Response", "displayName": "", "id": "#/definitions/185908596", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回标记：成功标记=0，非0失败，或者是功能码", "examples": [0]}, "msg": {"type": "string", "description": "返回信息", "examples": ["success"]}, "data": {"$ref": "#/definitions/185908597", "description": "数据"}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "查询任务状态 Request", "displayName": "", "id": "#/definitions/185908598", "description": "com.haima.runninghub.common.model.request.task.TaskStatusRequest", "schema": {"jsonSchema": {"type": "object", "properties": {"taskId": {"type": "string", "description": "", "examples": ["1904152026220003329"]}, "apiKey": {"type": "string", "description": "", "examples": ["{{a<PERSON><PERSON><PERSON>}}"]}}, "x-apifox-orders": ["<PERSON><PERSON><PERSON><PERSON>", "taskId"], "required": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "获取账户信息 Request", "displayName": "", "id": "#/definitions/185908600", "description": "com.haima.runninghub.common.model.UserApikeyRequest", "schema": {"jsonSchema": {"type": "object", "properties": {"apikey": {"type": "string", "description": "", "examples": ["{{a<PERSON><PERSON><PERSON>}}"]}}, "x-apifox-orders": ["apikey"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "上传资源Request", "displayName": "", "id": "#/definitions/185908601", "description": "com.haima.runninghub.common.model.request.task.TaskUploadRequest", "schema": {"jsonSchema": {"type": "object", "properties": {"apiKey": {"type": "string", "description": "", "examples": ["{{a<PERSON><PERSON><PERSON>}}"]}, "file": {"type": "string", "description": ""}, "fileType": {"type": "string", "description": "", "examples": ["image", "video", "audio"]}}, "x-apifox-orders": ["<PERSON><PERSON><PERSON><PERSON>", "file", "fileType"], "required": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "获取webhook事件详情Request", "displayName": "", "id": "#/definitions/185908602", "description": "com.haima.runninghub.common.model.request.task.GetWebhookDetailRequest", "schema": {"jsonSchema": {"type": "object", "properties": {"apiKey": {"type": "string", "description": "", "examples": ["{{a<PERSON><PERSON><PERSON>}}"]}, "taskId": {"type": "string", "examples": ["1904154698679771137"]}}, "x-apifox-orders": ["<PERSON><PERSON><PERSON><PERSON>", "taskId"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "重新发送指定webhook Request", "displayName": "", "id": "#/definitions/185908603", "description": "com.haima.runninghub.common.model.request.task.RetryWebhookRequest", "schema": {"jsonSchema": {"type": "object", "properties": {"apiKey": {"type": "string", "description": "", "examples": ["{{a<PERSON><PERSON><PERSON>}}"]}, "webhookId": {"type": "string", "examples": ["1904154698688159745"]}, "webhookUrl": {"type": "string", "description": "", "examples": ["https://your-webhook-url"]}}, "x-apifox-orders": ["<PERSON><PERSON><PERSON><PERSON>", "webhookId", "webhookUrl"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "R?", "displayName": "", "id": "#/definitions/*********", "description": "R", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回标记：成功标记=0，非0失败，或者是功能码"}, "msg": {"type": "string", "description": "返回信息"}, "data": {"description": "数据", "type": "null"}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "RAccountStatusResponse", "displayName": "", "id": "#/definitions/*********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回标记：成功标记=0，非0失败，或者是功能码"}, "msg": {"type": "string", "description": "返回信息"}, "data": {"$ref": "#/definitions/*********", "description": "数据"}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "AccountStatusResponse", "displayName": "", "id": "#/definitions/*********", "description": "com.haima.runninghub.common.model.response.AccountStatusResponse", "schema": {"jsonSchema": {"type": "object", "properties": {"remainCoins": {"type": "integer", "description": ""}, "currentTaskCounts": {"type": "integer", "description": ""}}, "x-apifox-orders": ["remainCoins", "currentTaskCounts"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "ApiUploadLoraRequest", "displayName": "", "id": "#/definitions/*********", "description": "com.haima.runninghub.common.model.ApiUploadLoraRequest", "schema": {"jsonSchema": {"type": "object", "properties": {"loraName": {"type": "string", "description": "lora name, cannot be blank", "examples": ["my-lora-name"]}, "md5Hex": {"type": "string", "description": "file MD5, cannot be blank", "examples": ["f8d958506e6c8044f79ccd7c814c6179"]}, "apiKey": {"type": "string", "description": "a<PERSON><PERSON><PERSON>, cannot be blank", "examples": ["{{a<PERSON><PERSON><PERSON>}}"]}}, "x-apifox-orders": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "md5Hex"], "required": ["<PERSON><PERSON><PERSON><PERSON>", "md5Hex", "<PERSON><PERSON><PERSON><PERSON>"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "RString", "displayName": "", "id": "#/definitions/185908611", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回标记：成功标记=0，非0失败，或者是功能码"}, "msg": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "RTaskUploadResponse", "displayName": "", "id": "#/definitions/185908612", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回标记：成功标记=0，非0失败，或者是功能码"}, "msg": {"type": "string", "description": "返回信息"}, "data": {"$ref": "#/definitions/185908613", "description": "数据"}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 4342947}, {"name": "TaskUploadResponse", "displayName": "", "id": "#/definitions/185908613", "description": "com.haima.runninghub.common.model.request.task.TaskUploadResponse", "schema": {"jsonSchema": {"type": "object", "properties": {"fileName": {"type": "string", "description": ""}, "fileType": {"type": "string", "description": ""}}, "x-apifox-orders": ["fileName", "fileType"]}}, "visibility": "INHERITED", "moduleId": 4342947}]}], "securitySchemeCollection": [], "requestCollection": [{"name": "根目录", "children": [], "ordering": ["requestFolder.6785145"], "items": []}], "environments": [], "commonScripts": [], "globalVariables": [{"id": "6412110", "variables": [{"name": "baseUrl", "value": "http://jt6-rnliqjgjgg3zr1nvo-3w4d0o0e-custom.service.onethingrobot.com", "description": "", "isBindInitial": false, "initialValue": "http://jt6-rnliqjgjgg3zr1nvo-3w4d0o0e-custom.service.onethingrobot.com"}]}], "moduleVariables": [{"id": "4342947", "variables": []}], "commonParameters": {"id": 774204, "createdAt": "2025-05-19T07:46:20.000Z", "updatedAt": "2025-06-02T18:17:17.000Z", "deletedAt": null, "parameters": {"header": [{"name": "authorization", "defaultEnable": false, "type": "string", "defaultValue": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODIxOTUwNDNlZDZlNDFmYzE1YjYxY2EiLCJpYXQiOjE3NDg3ODI4NzQsImV4cCI6MTc0OTM4NzY3NH0.rgU6mJaTf3NvpPexBd-FpWsI_PveHDlhONl1sfJLtR4", "required": false, "description": ""}, {"name": "X-Instance-ID", "defaultEnable": false, "type": "string", "defaultValue": "lh16obf0b8tq0p6t-7nr1km3z", "required": true}, {"name": "Authorization", "defaultEnable": false, "type": "string", "defaultValue": "Bearer 9b1b825a734bfe35b703d4bc01a2396e", "required": true}]}, "projectId": 6412110, "creatorId": 609880, "editorId": 609880}, "projectSetting": {"id": "6443462", "auth": {}, "securityScheme": {}, "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "preProcessors": [], "postProcessors": [], "advancedSettings": {"enableJsonc": true, "enableBigint": false, "responseValidate": true, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "isDefaultUrlEncoding": 2, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "NEW"}, "preferredHttpVersion": {}}, "initialDisabledMockIds": [], "servers": [{"id": "default", "name": "默认服务"}], "cloudMock": {"security": "free", "enable": false, "tokenKey": "apifoxToken"}}, "customFunctions": [], "projectAssociations": []}