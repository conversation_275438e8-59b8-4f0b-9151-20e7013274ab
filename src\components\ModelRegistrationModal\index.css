/* 导入统一样式 */
@import '../../styles/buttons.css';
@import '../../styles/close-buttons.css';
@import '../../styles/modals.css';

/* 弹窗包装器 */
.model-registration-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* 使用dvh确保在移动端正确显示 */
  height: 100dvh;
  z-index: 99999; /* 大幅提高z-index，确保在所有内容之上，包括移动端导航栏 */
  background-color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
}

/* 模特登记弹窗容器 */
.model-registration-modal {
  position: relative;
  width: 450px;
  max-width: 90vw;
  max-height: 90vh;
  background: var(--bg-primary);
  pointer-events: auto;
  box-shadow: var(--shadow-md);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 100000; /* 确保弹窗内容在包装器之上 */
}

/* 暗色主题下添加白边 */
[data-theme="dark"] .model-registration-modal {
  background: var(--bg-primary);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
}

/* 模态框头部 */
.model-registration-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 20px 20px 0;
  margin: 0;
  border-bottom: 1px solid var(--border-light);
  position: relative;
  background: var(--bg-primary);
  cursor: grab;
  user-select: none;
}

.model-registration-modal .modal-header:active {
  cursor: grabbing;
}

.model-registration-modal.dragging .modal-header {
  cursor: grabbing;
}

/* 标签页组样式 */
.model-registration-modal .tab-group {
  display: flex;
  gap: 0;
  height: 36px;
  margin-bottom: -1px;
  margin-left: 16px;
  margin-top: 0;
  width: auto;
}

/* 标签按钮样式 */
.model-registration-modal .tab-btn {
  padding: 0 24px;
  border: 1px solid transparent;
  background: transparent;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  position: relative;
  transition: var(--transition-fast);
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  margin-right: 1px;
  user-select: none;
  white-space: nowrap;
  flex-shrink: 0;
}

.model-registration-modal .tab-btn:first-child {
  margin-left: 0;
}

.model-registration-modal .tab-btn:last-child {
  margin-right: 4px;
}

.model-registration-modal .tab-btn:hover {
  background: transparent;
  color: var(--text-primary);
  position: relative;
}

.model-registration-modal .tab-btn:not(.active):hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(100% - 2px);
  height: 28px;
  background: var(--bg-hover);
  border-radius: var(--radius-md);
}

.model-registration-modal .tab-btn.active {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-color);
  border-bottom-color: var(--bg-primary);
  font-weight: 500;
}

.model-registration-modal .tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--brand-primary);
  width: calc(100% - 48px);
  margin: 0 24px;
  transition: transform 0.2s ease;
}

.model-registration-modal .tab-btn:not(.active) {
  border-bottom: 1px solid var(--border-color);
}

/* 模态框内容 */
.model-registration-modal .modal-body {
  padding: 24px;
  padding-top: 20px;
  height: calc(90vh - 116px);
  max-height: 800px;
  overflow-y: auto !important;
  flex: 1;
}

/* 弹窗内容样式 */
.registration-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  background-color: var(--bg-primary);
}

/* 标签页内容容器 */
.tab-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  overflow-y: visible;
}

/* 内容区域标题 */
.registration-content .modal-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-lighter);
}

/* 基本信息区域布局 */
.basic-info-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

/* 表单输入区域，控制宽度 */
.form-inputs {
  flex: 1;
  max-width: calc(50% - var(--spacing-md)); /* 控制为大约2/3的宽度 */
}

/* 模特缩略图样式 */
.model-thumbnail-wrapper {
  position: relative;
  width: auto; /* 宽度将通过JS计算以保持3:4比例 */
  height: auto; /* 高度将通过JSX设置 */
  flex-shrink: 0;
}

.model-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-sm); /* 使用与图片详情弹窗一致的圆角 */
  overflow: hidden;
  background-color: var(--background-tertiary);
  border: 1px solid var(--border-light);
}

.model-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top; /* 确保图片从顶部开始显示，避免头部被裁剪 */
}

/* 表单项容器 */
.form-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

.form-item label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.form-item .model-id {
  font-family: inherit;
  padding: var(--spacing-xs);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  border: 1px solid var(--border-light);
}

.form-item input {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: var(--transition-normal);
}

.form-item input:hover {
  border-color: var(--brand-primary);
}

.form-item input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

/* 标签部分 */
.tags-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding-bottom: var(--spacing-md);
}

.tags-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
}

/* 标签分类选项卡 - 与取色器弹窗保持一致 */
.tags-section {
  margin-bottom: var(--spacing-sm);
}

.tags-section label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-xxs);
  display: block;
}

.tags-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xxs);
}

/* 使标签按钮与tab-btn样式一致 */
.tag-option {
  padding: var(--spacing-xxs) var(--spacing-sm);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 28px;
}

.tag-option:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
  background: var(--bg-hover);
}

.tag-option.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
  font-weight: 500;
}

/* 底部按钮区域 - 确保不显示阴影 */
.model-registration-modal .modal-footer {
  height: 60px;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-md);
  box-shadow: none !important;
  position: sticky;
  bottom: 0;
  z-index: 5;
}

/* 适配移动端 */
@media (max-width: 576px) {
  .model-registration-modal .modal-body {
    padding: 20px;
  }
  
  .model-registration-modal .tab-group {
    width: auto;
    margin-left: 0;
  }
  
  .model-registration-modal .tab-btn {
    flex: none;
    font-size: 13px;
    padding: 0 24px;
  }
  
  .model-registration-modal .tab-btn.active::after {
    width: calc(100% - 48px);
    margin: 0 24px;
  }
  
  /* 移动端下的基本信息区域调整为横向布局（去除column-reverse） */
  .basic-info-container {
    flex-direction: row;
    gap: var(--spacing-sm);
    align-items: center;
  }
  
  /* 移动端下的表单输入区域宽度调整 */
  .form-inputs {
    max-width: 160px;
    min-width: 120px;
    width: 100%;
  }
  
  /* 移动端下的模特缩略图样式调整 */
  .model-thumbnail-wrapper {
    /* 让图片容器高度与左侧输入区域一致 */
    height: 100%;
    display: flex;
    align-items: stretch;
    margin-bottom: 0;
    width: auto;
    min-width: 0;
  }
  .model-thumbnail {
    height: 100%;
    aspect-ratio: 3/4;
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    min-width: 0;
  }
  .model-thumbnail img {
    height: 100%;
    width: auto;
    object-fit: contain;
    display: block;
    border-radius: 8px;
  }
  
  .model-thumbnail-wrapper,
  .model-thumbnail {
    background: var(--bg-primary) !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  .form-item {
    margin-bottom: var(--spacing-sm);
  }
  
  .model-preview img {
    max-height: 150px;
  }
  
  .tag-option {
    padding: var(--spacing-xxs) var(--spacing-xs);
    height: 24px;
    font-size: var(--font-size-xs);
  }
}

/* 错误提示样式 */
.message-error {
  color: #ff4d4f;
  font-size: 12px !important;
  margin-top: 4px !important;
  display: block;
}

/* 错误输入框样式 */
.input-error {
  border-color: #ff4d4f !important;
}

.input-error:hover, .input-error:focus {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}

/* 添加移动端适配样式 */
@media (max-width: 768px) {
  .model-registration-modal {
    width: 95%;
    max-width: none;
    max-height: calc(100vh - 40px);
    max-height: calc(100dvh - 40px);
  }
}

@media (max-width: 480px) {
  .model-registration-modal {
    width: 98%;
    max-height: calc(100vh - 20px);
    max-height: calc(100dvh - 20px);
  }
}

@media (max-width: 360px) {
  .model-registration-modal {
    width: 100%;
    max-height: 100vh;
    max-height: 100dvh;
    border-radius: 0;
  }
}

/* 真实移动设备适配 */
@media (hover: none) and (pointer: coarse) {
  .model-registration-wrapper {
    padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  }

  .model-registration-modal {
    max-height: calc(100dvh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  }
}

/* 横屏模式适配 */
@media (hover: none) and (pointer: coarse) and (orientation: landscape) {
  .model-registration-modal {
    max-height: calc(100dvh - 20px);
  }
} 