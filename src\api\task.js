import api from './index';
import { getCurrentUserId } from './request';
import request from './request';
import { generateId, ID_TYPES } from '../utils/idGenerator';

/**
 * 获取任务列表
 * @param {string} userId - 用户ID，为null时使用当前登录用户
 * @returns {Promise<Array>} 任务列表
 */
export const getTasks = async (userId = null) => {
  try {
    // 开发环境下固定使用'developer'用户ID
    const currentUserId = process.env.NODE_ENV === 'development' ? 'developer' : (userId || getCurrentUserId());
    console.log('获取任务列表，使用用户ID:', currentUserId);
    
    const response = await api.get('/tasks', { userId: currentUserId }, '获取任务列表');
    
    if (response && response.success && response.data) {
      console.log(`成功获取到${response.data.length}个任务`);
      return response.data;
    }
    
    console.log('获取任务列表成功，但没有任务数据');
    return [];
  } catch (error) {
    console.error('获取任务列表失败:', error);
    return [];
  }
};

/**
 * 获取单个任务详情
 * @param {string} taskId - 任务ID
 * @param {string} userId - 用户ID，为null时使用当前登录用户
 * @returns {Promise<Object|null>} 任务详情
 */
export const getTaskById = async (taskId, userId = null) => {
  try {
    // 移除错误的MongoDB ObjectID格式检查，我们使用的是自定义17位数字ID格式
    // 验证任务ID是否是有效的17位数字ID
    if (!/^\d{17}$/.test(taskId)) {
      console.error(`【数据流程跟踪】getTaskById - 无效的任务ID格式: ${taskId}`);
      // 检查是否是带前缀的任务ID（例如"task-1234567890123456"）
      if (taskId && taskId.startsWith('task-')) {
        // 尝试提取数字部分
        const numericPart = taskId.substring(5);
        if (/^\d{17}$/.test(numericPart)) {
          console.log(`【数据流程跟踪】getTaskById - 从带前缀的ID中提取数字ID: ${numericPart}`);
          taskId = numericPart; // 使用提取出的数字部分
        } else {
          return null; // 即使去掉前缀也不是正确的格式
        }
      } else {
        return null; // 不是正确的17位数字格式，也不是带前缀的格式
      }
    }
    
    // 检查是否有任务缓存（从createTask返回的）
    const taskCacheKey = `task_cache_${taskId}`;
    const cachedTask = sessionStorage.getItem(taskCacheKey);
    
    if (cachedTask) {
      try {
        console.log(`【数据流程跟踪】getTaskById - 找到任务缓存 ${taskId}，使用缓存数据`);
        const parsedTask = JSON.parse(cachedTask);
        // 缓存过期时间检查 - 10分钟
        const cacheTime = parsedTask._cacheTime || 0;
        const now = Date.now();
        if (now - cacheTime < 10 * 60 * 1000) {
          return parsedTask;
        } else {
          // 缓存过期，继续走API获取
          console.log(`【数据流程跟踪】getTaskById - 任务 ${taskId} 缓存已过期，重新获取`);
          sessionStorage.removeItem(taskCacheKey);
        }
      } catch (e) {
        console.error('【数据流程跟踪】getTaskById - 解析缓存失败:', e);
        // 缓存解析错误，继续走API获取
      }
    }
    
    // 使用API调用，传递用户ID
    const currentUserId = userId || getCurrentUserId();
    console.log(`【数据流程跟踪】getTaskById - 正在获取任务 ${taskId}，用户ID: ${currentUserId}`);
    // console.log(`【数据流程跟踪】getTaskById - API请求URL: /tasks/${taskId}`);
    
    const startTime = Date.now();
    const response = await api.get(`/tasks/${taskId}`, { userId: currentUserId }, '获取任务详情');
    const requestTime = Date.now() - startTime;
    
    // console.log(`【数据流程跟踪】getTaskById - API响应时间: ${requestTime}ms`);
    
    if (response && response.success && response.data) {
      console.log(`【数据流程跟踪】getTaskById - 成功获取任务数据，任务ID: ${response.data.taskId || response.data.id}`);
      console.log(`【数据流程跟踪】getTaskById - 组件数据类型: ${typeof response.data.components}, 是否数组: ${Array.isArray(response.data.components)}`);
      
      // 查看主图片信息
      const hasServerFileName = response.data.primaryImageFileName || 
                             (response.data.components && Object.values(response.data.components).some(c => c.serverFileName));
      
      // console.log(`【数据流程跟踪】getTaskById - 任务是否包含serverFileName: ${hasServerFileName ? '是' : '否'}`);
      
      if (response.data.components) {
        // 检查组件内的serverFileName
        if (Array.isArray(response.data.components)) {
          console.log(`【数据流程跟踪】getTaskById - 组件是数组格式，长度: ${response.data.components.length}`);
          
          const mainImageComponents = response.data.components.filter(c => 
            c.isMainImage || (c.componentType && c.componentType.toLowerCase() === 'sourceImagePanel'.toLowerCase())
          );
          
          console.log(`【数据流程跟踪】getTaskById - 找到主图片组件: ${mainImageComponents.length}个`);
          
          mainImageComponents.forEach((comp, index) => {
            // console.log(`【数据流程跟踪】getTaskById - 主图片组件${index+1}: componentType=${comp.componentType}, serverFileName=${comp.serverFileName || '未设置'}`);
          });
        } else {
          console.log(`【数据流程跟踪】getTaskById - 组件是对象格式，键数量: ${Object.keys(response.data.components).length}`);
          
          Object.entries(response.data.components).forEach(([key, comp]) => {
            if (comp.isMainImage || (comp.componentType && comp.componentType.toLowerCase() === 'sourceImagePanel'.toLowerCase())) {
              // console.log(`【数据流程跟踪】getTaskById - 主图片组件[${key}]: serverFileName=${comp.serverFileName || '未设置'}`);
            }
          });
        }
      }
      
      // 缓存任务数据
      try {
        // 设置缓存标记和时间戳
        const taskToCache = {
          ...response.data,
          _cacheTime: Date.now()
        };
        sessionStorage.setItem(taskCacheKey, JSON.stringify(taskToCache));
        console.log(`【数据流程跟踪】getTaskById - 任务 ${taskId} 已缓存`);
      } catch (e) {
        console.error('【数据流程跟踪】getTaskById - 缓存任务失败:', e);
        // 缓存失败不影响正常流程
      }
      
      return response.data;
    }
    
    console.log(`【数据流程跟踪】getTaskById - 获取任务失败或数据为空`);
    return null;
  } catch (error) {
    console.error('【数据流程跟踪】getTaskById - 获取任务详情失败:', error);
    return null;
  }
};

/**
 * 删除任务
 * @param {string} taskId - 任务ID
 * @param {string} userId - 用户ID，为null时使用当前登录用户
 * @returns {Promise<boolean>} 是否删除成功
 */
export const deleteTask = async (taskId, userId = null) => {
  try {
    // 使用API调用，传递用户ID
    const currentUserId = userId || getCurrentUserId();
    console.log(`正在删除任务: ${taskId}, 用户ID: ${currentUserId}`);
    
    // 添加Debug URL参数，启用开发环境特殊处理
    const debugParam = process.env.NODE_ENV === 'development' ? '?debug=true' : '';
    const response = await api.delete(`/tasks/${taskId}${debugParam}`, '删除任务');
    
    console.log('删除任务响应:', response);
    return response && response.success;
  } catch (error) {
    console.error('删除任务失败:', error);
    // 显示详细错误信息
    if (error.message) {
      console.error(`错误信息: ${error.message}`);
    }
    return false;
  }
};

/**
 * 处理组件，确保每个组件都有componentId
 * @param {Object} components - 组件对象
 */
const handleComponentTransformation = (components) => {
  if (!components) return;
  
  for (const key in components) {
    const comp = components[key];
    // 如果缺少componentId，生成一个新的
    if (!comp.componentId) {
      // 使用新的格式生成componentId，而不是旧的06前缀数字ID
      comp.componentId = `${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    }
  }
};

/**
 * 创建任务
 * @param {Object} taskData - 任务数据
 * @param {string} userId - 用户ID，为null时使用当前登录用户
 * @returns {Promise<Object|null>} 创建的任务
 */
export const createTask = async (taskData, userId = null) => {
  try {
    // 开发环境下固定使用'developer'用户ID
    const currentUserId = process.env.NODE_ENV === 'development' ? 'developer' : (userId || getCurrentUserId());
    console.log('创建任务，使用用户ID:', currentUserId);
    
    // 准备FormData对象用于提交文件和任务数据
    const formData = new FormData();
    
    // 检查任务是否需要图片
    const requiresImage = taskData.requiresImage !== false; // 默认需要图片，除非明确设置为false
    
    // 始终优先使用文件引用模式
    let useFileReferences = false;
    
    // 检查是否有有效的服务器文件名
    let validServerFileName = null;
    
    // 找到组件中包含服务器文件名的组件
    if (taskData.components) {
      // 检查components是数组还是对象
      const componentsArray = Array.isArray(taskData.components) 
        ? taskData.components 
        : Object.values(taskData.components);
      
      // 扫描components查找包含serverFileName的组件
      for (const component of componentsArray) {
        // 直接在组件中查找
        if (component.serverFileName) {
          // 获取实际的服务器文件名 - 优先从processInfo.uploadResult中获取
          let actualServerFileName = component.serverFileName;
          
          // 首先从processInfo.uploadResult获取文件名
          if (component.processInfo?.uploadResult?.results?.[0]?.processedFile) {
            actualServerFileName = component.processInfo.uploadResult.results[0].processedFile;
            // console.log(`从组件 ${component.componentType || component.type} 的 processInfo.uploadResult.results[0].processedFile 获取文件名: ${actualServerFileName}`);
          } 
          // 然后从fileInfo.processInfo.uploadResult获取
          else if (component.fileInfo?.processInfo?.uploadResult?.results?.[0]?.processedFile) {
            actualServerFileName = component.fileInfo.processInfo.uploadResult.results[0].processedFile;
            // console.log(`从组件 ${component.componentType || component.type} 的 fileInfo.processInfo.uploadResult.results[0].processedFile 获取文件名: ${actualServerFileName}`);
          }
          // 最后使用serverFileName
          else {
            // console.log(`从组件 ${component.componentType || component.type} 使用默认的 serverFileName: ${actualServerFileName}`);
          }
          
          // 将文件名添加到引用列表 - 如果是URL则提取文件名
          if (actualServerFileName) {
            if (actualServerFileName.startsWith('http')) {
              // 尝试从URL中提取文件名
              const urlParts = actualServerFileName.split('/');
              const fileName = urlParts[urlParts.length - 1];
              // console.log(`从URL提取文件名: ${fileName}`);
              formData.append('fileReferences', fileName);
            } else {
              // console.log(`添加文件引用: ${actualServerFileName}`);
              formData.append('fileReferences', actualServerFileName);
            }
            validServerFileName = validServerFileName || actualServerFileName;
            useFileReferences = true;
          }
        }
      }
      
      // 记录找到的文件引用数量
      console.log(`添加了${useFileReferences ? formData.getAll('fileReferences').length : 0}个文件引用`);
      
      // 如果有文件引用，打印详细的引用列表用于调试
      if (useFileReferences) {
        const fileRefs = formData.getAll('fileReferences');
        console.log('详细的文件引用列表:');
        fileRefs.forEach((ref, index) => {
          // console.log(`[${index + 1}] ${ref}`);
        });
      }
      
      // 如果任务需要图片但没有找到文件引用，记录错误
      if (requiresImage && !useFileReferences) {
        // console.error('没有找到有效的serverFileName文件引用，任务创建可能失败');
        // 是否应该抛出错误取决于业务需求，这里改为警告
        // console.warn('缺少必要的serverFileName文件引用');
      }
    }
    
    // 如果有多个文件引用，检查是否有主图片文件名
    if (taskData.primaryImageFileName || taskData.serverFileName) {
      const primaryFileName = taskData.primaryImageFileName || taskData.serverFileName;
      validServerFileName = primaryFileName;
      // console.log(`使用主图片文件名: ${primaryFileName}`);
    }
    
    // 添加标志，表示使用文件引用
    formData.append('useExistingFiles', String(useFileReferences));
    
    // 确定工作流类型
    let workflowType = taskData.taskType;
    
    // 添加工作流类型 - 使用处理后的workflowType
    formData.append('workflow', workflowType);
    
    // 添加页面类型
    formData.append('pageType', taskData.pageType || 'unknown');
    
    // 添加图片类型
    formData.append('imageType', taskData.imageType || 'original'); // 使用传入的imageType，如果没有则默认为original
    
    // 添加是否需要图片的标志
    formData.append('requiresImage', requiresImage.toString());
    
    // 只添加组件数据，不再使用settings结构
    if (taskData.components) {
      formData.append('components', JSON.stringify(taskData.components));
    }
    
    // 使用传入的任务ID，明确使用taskId字段
    const taskId = taskData.taskId;
    formData.append('taskId', taskId);
    formData.append('userId', currentUserId); // 确保将userId添加到请求中
    
    // 确保传递imageCount
    if (taskData.imageCount) {
      formData.append('imageCount', taskData.imageCount);
    }
    
    // 打印FormData内容
    console.log('FormData内容:');
    for (const pair of formData.entries()) {
      // console.log(pair[0], pair[1]);
    }
    
    // 准备要发送的任务数据
    // 构建一个没有图片引用的干净JSON对象
    const taskDataForSubmission = {
      taskId: taskId, // 使用taskId而不是id
      userId: currentUserId,
      createdAt: new Date(),
      taskType: taskData.taskType,
      pageType: taskData.pageType || 'unknown',
      imageCount: taskData.imageCount || 1,
      status: 'processing',
      // 包含全部components数据
      components: taskData.components || {},
      // 包含生成图像列表（初始为空）
      generatedImages: taskData.generatedImages || []
    };
    
    // 如果有文件引用，添加到任务数据
    if (validServerFileName) {
      taskDataForSubmission.serverFileName = validServerFileName;
    }

    // 添加任务JSON数据
    formData.append('taskData', JSON.stringify(taskDataForSubmission));
    
    // 调用API创建任务
    // console.log('发送FormData请求到/process');
    const response = await fetch(`${process.env.REACT_APP_BACKEND_URL}/api/process`, {
      method: 'POST',
      body: formData,
      headers: {
        // 不要设置Content-Type，让浏览器自动设置，包含boundary
        // Authorization头，如果用户已登录
        ...(localStorage.getItem('token') ? { 'Authorization': `Bearer ${localStorage.getItem('token')}` } : {})
      },
    });
    
    // 解析响应
    const data = await response.json();
    // console.log('接收到响应:', data);
    
    // 定义一个外层变量，以便在catch块中使用
    let trueServerFileName = null;

    if (response.ok && data) {
      try {
        // 处理生成的图片路径，确保设置正确的存储路径
        let processedResults = [];
        
        // 原始响应可能是单个结果对象或结果数组
        const resultsArray = data.results || [data];
        console.log('处理生成的图片路径:', resultsArray);
        
        // 处理每个结果对象，添加正确的路径信息
        processedResults = resultsArray.map(result => {
          // 获取或生成文件名
          const filename = result.filename || 
                        (result.path ? result.path.split('/').pop() : 
                        result.relativePath ? result.relativePath.split('/').pop() : null);
          
          if (!filename) {
            // console.warn('无法确定图片文件名:', result);
            return result;
          }
          
          // 构建相对路径，使用正确的目录结构 - 抠图结果应该保存在tools/matting/generated
          const userId = process.env.NODE_ENV === 'development' ? 'developer' : currentUserId;
          let relativePath;
          
          // 根据任务类型和页面类型选择正确的保存路径
          if (taskData.taskType === 'tryonauto' || taskData.pageType === 'try-on') {
            // 模特换装任务应该保存在model/try-on/generated
            relativePath = `/storage/${userId}/model/try-on/generated/${filename}`;
            // console.log('使用模特换装页面保存路径:', relativePath);
          } else if (taskData.taskType === 'mattingbg' || taskData.taskType === 'mattingclo' || taskData.taskType === 'mattingclofile' || taskData.pageType === 'matting') {
            // 自动抠图任务应该保存在tools/matting/generated
            relativePath = `/storage/${userId}/tools/matting/generated/${filename}`;
            // console.log('使用自动抠图页面保存路径:', relativePath);
          } else if (taskData.pageType === 'optimize') {
            // 款式优化任务
            relativePath = `/storage/${userId}/style/optimize/generated/${filename}`;
            // console.log('使用款式优化页面保存路径:', relativePath);
          } else if (taskData.pageType === 'extend') {
            // 智能扩图任务
            relativePath = `/storage/${userId}/tools/extend/generated/${filename}`;
            // console.log('使用智能扩图页面保存路径:', relativePath);
          } else if (taskData.pageType === 'extract') {
            // 图片取词任务
            relativePath = `/storage/${userId}/tools/extract/generated/${filename}`;
            // console.log('使用图片取词页面保存路径:', relativePath);
          } else if (taskData.pageType === 'upscale') {
            // 高清放大任务
            relativePath = `/storage/${userId}/tools/upscale/generated/${filename}`;
            // console.log('使用高清放大页面保存路径:', relativePath);
          } else if (taskData.pageType === 'fashion') {
            // 时尚大片任务
            relativePath = `/storage/${userId}/model/fashion/generated/${filename}`;
            // console.log('使用时尚大片页面保存路径:', relativePath);
          } else if (taskData.pageType === 'recolor') {
            // 服装复色任务
            relativePath = `/storage/${userId}/model/recolor/generated/${filename}`;
            // console.log('使用服装复色页面保存路径:', relativePath);
          } else if (taskData.pageType === 'fabric') {
            // 换面料任务
            relativePath = `/storage/${userId}/model/fabric/generated/${filename}`;
            // console.log('使用换面料页面保存路径:', relativePath);
          } else if (taskData.pageType === 'background') {
            // 换背景任务
            relativePath = `/storage/${userId}/model/background/generated/${filename}`;
            // console.log('使用换背景页面保存路径:', relativePath);
          } else if (taskData.pageType === 'virtual') {
            // 虚拟模特任务
            relativePath = `/storage/${userId}/model/virtual/generated/${filename}`;
            // console.log('使用虚拟模特页面保存路径:', relativePath);
          } else if (taskData.pageType === 'inspiration') {
            // 灵感探索任务
            relativePath = `/storage/${userId}/style/inspiration/generated/${filename}`;
            // console.log('使用灵感探索页面保存路径:', relativePath);
          } else if (taskData.pageType === 'trending') {
            // 爆款开发任务
            relativePath = `/storage/${userId}/style/trending/generated/${filename}`;
            // console.log('使用爆款开发页面保存路径:', relativePath);
          } else if (taskData.pageType === 'divergent') {
            // 爆款延伸任务
            relativePath = `/storage/${userId}/style/divergent/generated/${filename}`;
            // console.log('使用爆款延伸页面保存路径:', relativePath);
          } else {
            // 默认使用自动抠图路径
            relativePath = `/storage/${userId}/tools/matting/generated/${filename}`;
            // console.log('使用默认保存路径:', relativePath);
          }
          
          return {
            ...result,
            filename,
            path: relativePath,
            // 保留原始URL如果有的话
            url: result.url || null
          };
        });
        
        console.log('处理后的结果:', processedResults);
        
        // 创建任务记录，确保任务被持久化存储
        console.log('创建任务记录，确保任务被持久化');
        
        // 提取所需的组件数据并转换组件结构
        const transformedComponents = { ...(taskData.components || {}) };
        
        // 处理组件，确保使用componentId并删除id字段
        handleComponentTransformation(transformedComponents);
        
        // 只有在需要图片的任务和有图片处理结果时才处理服务器文件名
        if (requiresImage) {
          // 关键修改：获取真正的服务器文件名(上传后生成的新文件名)
          
          // 1. 从API响应中获取服务器文件名
          if (data.serverFileName) {
            trueServerFileName = data.serverFileName;
            // console.log('从API响应中获取到serverFileName:', trueServerFileName);
          }
          
          // 2. 如果响应中没有，但使用了文件引用，从fileReferences中获取
          if (!trueServerFileName && useFileReferences) {
            // 获取fileReferences
            const fileReferences = [];
            for (const pair of formData.entries()) {
              if (pair[0] === 'fileReferences') {
                fileReferences.push(pair[1]);
              }
            }
            
            if (fileReferences.length > 0) {
              trueServerFileName = fileReferences[0]; // 第一个文件引用应该是正确的服务器文件名
              // console.log('从文件引用中获取到serverFileName:', trueServerFileName);
            }
          }
          
          // 3. 如果是新上传的文件，检查response结果中可能包含的上传文件名信息
          if (!trueServerFileName && resultsArray.length > 0 && resultsArray[0].serverFileName) {
            // 这里的serverFileName是上传后服务器生成的文件名
            if (resultsArray[0].serverFileName.match(/^\d{13}-[a-z0-9]+\.\w+$/)) {
              trueServerFileName = resultsArray[0].serverFileName;
              // console.log('从处理结果中提取服务器生成的文件名:', trueServerFileName);
            }
          }
          
          // 如果有真正的服务器文件名且任务需要图片，更新组件中的信息
          if (trueServerFileName) {
            // 找到主图片组件，更新其服务器文件名
            let mainComponentKey = null;
            for (const [key, component] of Object.entries(transformedComponents)) {
              if (component.isMainImage) {
                mainComponentKey = key;
                
                // 构建完整的服务器URL路径
                const serverUrl = `${process.env.REACT_APP_BACKEND_URL}/storage/${currentUserId}/uploads/${trueServerFileName}`;
                
                // 更新组件信息
                transformedComponents[key] = {
                  ...component,
                  serverFileName: trueServerFileName,
                  url: serverUrl
                };
                
                // console.log(`更新主图片组件 ${key} 的serverFileName:`, trueServerFileName);
              }
            }
            
            // 如果没有主图片组件，创建一个新的sourceImagePanel组件
            if (!mainComponentKey && taskData.pageType === 'matting') {
              // 构建完整的服务器URL路径
              const serverUrl = `${process.env.REACT_APP_BACKEND_URL}/storage/${currentUserId}/uploads/${trueServerFileName}`;
              
              // 移除服务器文件名中的文件扩展名用作图片ID
              const cleanImageId = trueServerFileName.includes('.') 
                ? trueServerFileName.substring(0, trueServerFileName.lastIndexOf('.')) 
                : trueServerFileName;
                
              // 创建新的源图片组件
              
              // 创建sourceImagePanel组件
              transformedComponents.sourceImagePanel = {
                componentType: 'sourceImagePanel',
                componentId: `${Date.now()}_${Math.floor(Math.random() * 1000)}`,
                isMainImage: true,
                serverFileName: trueServerFileName,
                url: serverUrl,
                fileInfo: {
                  serverFileName: trueServerFileName
                },
                processInfo: {
                  serverFileName: trueServerFileName
                }
              };
              
              // console.log('创建新的sourceImagePanel组件:', transformedComponents.sourceImagePanel);
            }
          } else if (requiresImage) {
            // console.warn('任务需要图片，但没有找到有效的服务器文件名，任务可能在刷新后无法正确显示图片');
          }
        } else {
          // console.log('任务不需要图片，跳过服务器文件名处理');
        }
        
        // 直接使用taskData，不需要转换
        const transformParams = taskData;
        
        // 构造基本任务记录
        const taskRecord = {
          taskId: taskId,
          userId: currentUserId,
          status: 'completed', 
          taskType: taskData.taskType,
          pageType: taskData.pageType || 'matting',
          imageType: taskData.imageType || 'original',
          requiresImage, // 添加是否需要图片的标志
          components: transformedComponents, // 组件数据
          results: processedResults,
          // 添加生成的图片数组
          generatedImages: Array.isArray(processedResults) && processedResults.length > 0 
            ? processedResults.map((result, index) => ({
                imageIndex: index,
                status: 'completed',
                url: result.url || '',
                path: result.path || '',
                filename: result.filename || '',
                createdAt: new Date()
              }))
            : []
        };
        
        // 为模特换装页面特别处理，将model信息保存到components
        if (taskData.pageType === 'try-on' && taskData.model) {
          if (!taskRecord.components) {
            taskRecord.components = {};
          }
          taskRecord.components.modelPanel = {
            ...(taskData.model || {}),
            componentType: 'ModelPanel'
          };
        }
        
        console.log('准备保存任务:', {
          taskId: taskRecord.taskId
        });
        
        // 保存任务到数据库，添加重试机制处理ID冲突
        try {
          // 准备请求路径和数据
          const saveUrl = `${process.env.REACT_APP_BACKEND_URL}/api/tasks/save`;
          // console.log('准备保存任务到数据库，使用路径:', saveUrl);
          
          // 确保taskRecord包含必要的字段
          if (!taskRecord.taskId) {
            console.error('任务ID缺失，无法保存任务');
            return null;
          }
          
          // 强制移除id字段，避免MongoDB索引冲突
          if ('id' in taskRecord) {
            console.log('检测到id字段，将其移除以避免冲突');
            delete taskRecord.id;
          }
          
          // 最大重试次数
          const maxRetries = 3;
          let currentRetry = 0;
          let saveResponse;
          let saveSuccess = false;
          
          // 重试循环
          while (currentRetry < maxRetries && !saveSuccess) {
            try {
              // 发送请求保存任务
              saveResponse = await fetch(saveUrl, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  // Authorization头，如果用户已登录
                  ...(localStorage.getItem('token') ? { 'Authorization': `Bearer ${localStorage.getItem('token')}` } : {})
                },
                body: JSON.stringify(taskRecord)
              });
              
              // 检查是否有ID冲突(409状态码)
              if (saveResponse.status === 409) {
                const errorMessage = '任务ID冲突，请检查系统时钟或ID生成逻辑';
                console.error(errorMessage, {taskId: taskRecord.taskId});
                // 向用户报告错误而不是尝试修复
                throw new Error(errorMessage);
              } else if (!saveResponse.ok) {
                // 其他错误
                const errorText = await saveResponse.text();
                console.error(`保存任务失败，状态码: ${saveResponse.status}, 错误信息:`, errorText);
                throw new Error(`保存任务失败: ${saveResponse.status} ${saveResponse.statusText}`);
              }
              
              // 保存成功
              saveSuccess = true;
            } catch (retryError) {
              if (retryError.message.includes('任务保存失败: 无法解决ID冲突')) {
                throw retryError; // 重新抛出无法解决的冲突错误
              }
              
              currentRetry++;
              if (currentRetry < maxRetries) {
                console.log(`保存任务出错，正在重试(${currentRetry}/${maxRetries})...`, retryError);
                continue;
              } else {
                console.error(`达到最大重试次数(${maxRetries})，放弃重试`);
                throw retryError;
              }
            }
          }
          
          // 解析保存响应
          const saveData = await saveResponse.json();
          // console.log('任务保存成功，服务器返回:', saveData);
          
          // 构建结果对象
          const result = {
            ...data,
            ...saveData.data,
            taskId: saveData.data.taskId || taskRecord.taskId,
            results: processedResults,
            primaryImageFileName: trueServerFileName || saveData.data.primaryImageFileName || saveData.data.mainServerFileName
          };
          
          // 缓存任务数据
          cacheTaskData(taskId, result, processedResults);
          
          return result;
        } catch (saveError) {
          console.error('保存任务失败:', saveError);
          // 如果保存失败但API请求成功，仍然返回API响应结果，确保包含generatedImages
          const result = {
            ...data,
            generatedImages: data.results ? data.results.map((result, index) => ({
              imageIndex: index,
              status: 'completed',
              url: result.url || '',
              path: result.path || '',
              filename: result.filename || '',
              createdAt: new Date()
            })) : []
          };
          
          // 缓存任务数据
          cacheTaskData(taskId, result, data.results);
          
          return result;
        }
      } catch (saveError) {
        console.error('保存任务记录失败，但图片处理成功:', saveError);
        // 即使任务记录保存失败，也继续返回图片处理结果，确保包含generatedImages
        const result = {
          ...data,
          primaryImageFileName: trueServerFileName || null,
          generatedImages: data.results ? data.results.map((result, index) => ({
            imageIndex: index,
            status: 'completed',
            url: result.url || '',
            path: result.path || '',
            filename: result.filename || '',
            createdAt: new Date()
          })) : []
        };
        
        // 缓存任务数据
        cacheTaskData(taskId, result, data.results);
        
        return result;
      }
    }
    
    console.error('请求失败:', data);
    return null;
  } catch (error) {
    console.error('创建任务失败:', error);
    return null;
  }
};

/**
 * 缓存任务数据，确保generatedImages字段正确
 * @param {string} taskId - 任务ID
 * @param {Object} taskData - 任务数据
 * @param {Array} results - 任务结果数据
 */
const cacheTaskData = (taskId, taskData, results) => {
  try {
    const taskCacheKey = `task_cache_${taskId}`;
    // 确保缓存数据包含正确的taskId字段
    const taskToCache = {
      ...taskData,
      taskId: taskId, // 使用taskId作为标准字段，不再添加id字段
      _cacheTime: Date.now()
    };
    
    // 确保generatedImages格式正确
    if (!taskToCache.generatedImages && results) {
      console.log('缓存前创建generatedImages数组，结果数量:', results.length);
      taskToCache.generatedImages = results.map((result, index) => ({
        imageIndex: index,
        status: 'completed',
        url: result.url || '',
        path: result.path || '',
        filename: result.filename || '',
        createdAt: new Date()
      }));
    }
    
    sessionStorage.setItem(taskCacheKey, JSON.stringify(taskToCache));
    console.log(`createTask - 任务 ${taskId} 已缓存，包含 ${taskToCache.generatedImages?.length || 0} 张图片`);
  } catch (e) {
    console.error('createTask - 缓存任务失败:', e);
    // 缓存失败不影响正常流程
  }
};

/**
 * 过滤任务列表，仅显示指定用户的任务
 * @param {Array} tasks - 任务列表
 * @param {string} userId - 用户ID
 * @returns {Array} 过滤后的任务列表
 */
export const filterTasksByUser = (tasks, userId) => {
  if (!userId || !tasks || !Array.isArray(tasks)) {
    return tasks || [];
  }
  
  return tasks.filter(task => task.userId === userId);
};

/**
 * 生成模拟任务数据（仅用于开发环境）
 * @param {string} userId - 用户ID
 * @returns {Array} 模拟任务数据
 */
export const getFakeTasksForUser = (userId = 'developer') => {
  // 开发环境使用mock数据
  if (process.env.NODE_ENV !== 'production') {
    console.warn('警告: 当前使用的是测试数据，上线前必须删除');
    
    // 创建10个模拟任务
    return Array.from({ length: 10 }).map((_, index) => {
      return {
        taskId: `TASK${String(index + 1).padStart(3, '0')}`, // 使用taskId代替id
        userId: userId,
        title: `测试任务 ${index + 1}`,
        createdAt: new Date(Date.now() - (index * 1000 * 60 * 30)), // 每条数据间隔30分钟
        status: index < 8 ? 'completed' : index === 8 ? 'processing' : 'failed',
        type: index % 3 === 0 ? 'fashion' : index % 3 === 1 ? 'tryon' : 'recolor',
        images: Array(index % 3 + 1).fill(null).map((_, imgIndex) => ({
          url: `/images/test/${index % 3 === 0 ? 'fashion' : 
               index % 3 === 1 ? 'tryon' : 'recolor'}/result${imgIndex + 1}.jpg`,
          imageIndex: imgIndex
        }))
      };
    });
  } else {
    // 生产环境返回空数组
    return [];
  }
};

export default {
  getTasks,
  getTaskById,
  createTask,
  deleteTask,
  filterTasksByUser,
  getFakeTasksForUser
}; 