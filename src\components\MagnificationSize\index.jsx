import React from 'react';
import PropTypes from 'prop-types';
import './index.css';

/**
 * 放大倍数与尺寸设置组件
 */
const MagnificationSize = ({ onExpandClick, savedSettings }) => {
  // 格式化尺寸信息显示
  const formatSize = () => {
    if (!savedSettings) return '未设置';
    
    // 如果没有原图信息，显示功能说明文字
    if (!savedSettings.hasOriginalImage) return '设置想要放大的倍数或尺寸';
    
    const { scale, width, height } = savedSettings;
    return `${scale.toFixed(1)}倍 (${width} × ${height}px)`;
  };
  
  // 处理点击事件
  const handleExpandClick = (event) => {
    if (onExpandClick) {
      onExpandClick(event);
    }
  };
  
  // 判断是否应该显示激活状态的图标
  const shouldShowActiveIcon = () => {
    if (!savedSettings) return false;
    
    // 检查是否有有效的设置数据
    const hasValidSettings = (
      savedSettings.scale || 
      savedSettings.width || 
      savedSettings.height
    );
    
    // 如果有有效设置且不是"未设置"状态，则显示激活图标
    return hasValidSettings && formatSize() !== '未设置' && formatSize() !== '设置想要放大的倍数或尺寸';
  };
  
  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          <img 
            src={shouldShowActiveIcon() ? 'https://file.aibikini.cn/config/icons/magnification-active.png' : 'https://file.aibikini.cn/config/icons/magnification.png'} 
            alt="放大倍数与尺寸" 
            className="component-icon" 
          />
          <div className="component-text">
            <h3>放大倍数与尺寸</h3>
            <div className="component-content">
              <p>{formatSize()}</p>
            </div>
          </div>
        </div>
        <button 
          className="expand-btn"
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

MagnificationSize.propTypes = {
  onExpandClick: PropTypes.func.isRequired,
  savedSettings: PropTypes.object
};

export default MagnificationSize; 