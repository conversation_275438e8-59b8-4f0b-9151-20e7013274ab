const mongoose = require('mongoose');

const uploadSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  filename: {
    type: String,
    required: true
  },
  originalname: {
    type: String,
    required: true
  },
  mimetype: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    enum: ['temporary', 'used', 'archived'],
    default: 'temporary'
  },
  taskId: {
    type: String,
    ref: 'Task',
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// 添加索引以提高查询性能
uploadSchema.index({ userId: 1, createdAt: -1 });
uploadSchema.index({ status: 1, createdAt: 1 }); // 添加用于清理临时文件的索引
uploadSchema.index({ taskId: 1 }); // 添加用于查找任务关联文件的索引

const Upload = mongoose.model('Upload', uploadSchema);

module.exports = Upload; 