const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const PlanSchema = new Schema({
  code: {
    type: String,
    required: true,
    unique: true,
  },
  name: {
    type: String,
    required: true
  },
  description: String,
  price: {
    monthly: Number,
    yearly: Number,
    discount: Number // 年付折扣百分比
  },
  features: {
    // 设计功能
    design: {
      enabled: { type: Boolean, default: false },
      drawing: { type: Boolean, default: false },
      trending: { type: Boolean, default: false },      // 爆款开发
      divergent: { type: Boolean, default: false },     // 爆款延伸
      optimize: { type: Boolean, default: false },      // 款式优化
      inspiration: { type: <PERSON>olean, default: false }    // 灵感探索
    },
    // 模特功能
    model: {
      'change-posture': { type: Boolean, default: false },
      enabled: { type: Boolean, default: false },
      fashion: { type: Boolean, default: false },       // 时尚大片
      'try-on': { type: Boolean, default: false },      // 模特换装
      'change-model': { type: Boolean, default: false }, // 换模特
      recolor: { type: <PERSON><PERSON>an, default: false },       // 服装复色
      fabric: { type: Boolean, default: false },        // 换面料
      background: { type: <PERSON>olean, default: false },    // 换背景
      virtual: { type: Boolean, default: false },       // 虚拟模特
      'detail-migration': { type: Boolean, default: false }, // 细节还原
      'hand-fix': { type: Boolean, default: false }     // 手部修复
    },
    // 工具功能
    tools: {
      enabled: { type: Boolean, default: true },
      extract: { type: Boolean, default: true },        // 图片取词
      upscale: { type: Boolean, default: true },        // 高清放大
      matting: { type: Boolean, default: true },        // 自动抠图
      extend: { type: Boolean, default: true },         // 智能扩图
      inpaint: { type: Boolean, default: true }         // 消除笔
    },
    // 视频功能
    video: {
      enabled: { type: Boolean, default: false },
      imgtextvideo: { type: Boolean, default: false },  // 图文成片
      mulimgvideo: { type: Boolean, default: false }    // 多图成片
    },
    support: {
      level: {
        type: String,
        enum: ['standard', 'premium', 'enterprise'],
        default: 'standard'
      },
      responseTime: {
        type: String,
        enum: ['5x8', '7x24'],
        default: '5x8'
      }
    }
  },
  usageQuota: {
    totalRequests: { type: Number, default: -1 }, // -1 表示无限制
    dailyRequests: { type: Number, default: -1 },
    maxConcurrentTasks: { type: Number, default: 1 } // 新增：最大并发任务数
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  isRecommended: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// 更新时自动更新 updatedAt 字段
PlanSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const Plan = mongoose.model('Plan', PlanSchema);

module.exports = Plan;