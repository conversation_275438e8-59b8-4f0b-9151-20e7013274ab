/* 服装上传区域样式 */
.upload-area {
  width: 100%;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
}

.file-input {
  display: none;
}

.upload-zone {
  display: block;
  width: 100%;
  /* 重要: 此高度为设计规范确定的尺寸,请勿修改 */
  min-height: 150px;
  border: 1px dashed var(--border-color);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-normal);
  background: var(--bg-secondary);
  position: relative;
  overflow: hidden;
}

.support-tag {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px 8px;
  background: var(--brand-primary-light);
  border-radius: 0 0 0 var(--radius-sm);
  font-size: var(--font-size-xs);
  color: var(--brand-primary);
  font-weight: 500;
  pointer-events: none;
  transition: var(--transition-normal);
}

.upload-zone:hover .support-tag {
  background: var(--brand-primary-lighter);
}

.upload-zone.compact .support-tag {
  display: none;
}

.upload-zone:hover {
  border-color: var(--brand-primary);
  background: var(--brand-primary-lighter);
}

.upload-zone.dragging {
  border-color: var(--brand-primary);
  background: var(--brand-primary-light);
  box-shadow: 0 0 0 4px var(--brand-primary-lighter);
  transform: scale(1.01);
}

.upload-zone.dragging::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--brand-primary-lighter);
  pointer-events: none;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 0.4;
  }
}

.upload-zone.compact {
  /* 重要: 紧凑模式的高度为固定值,请勿修改 */
  min-height: 88px;
  padding: 0;
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--bg-secondary);
}

.upload-zone.compact .upload-content {
  flex-direction: row;
  align-items: center;
  padding: 0;
  height: 100%;
  gap: 0;
  width: 100%;
}

.upload-zone.compact .upload-icon {
  display: none;
}

.upload-zone.compact .upload-text {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0;
  margin: 0;
  width: 100%;
  height: 100%;
}

.upload-zone.compact .upload-text .primary-text {
  width: 88px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-md);
  color: var(--brand-primary);
  font-weight: 400;
  margin: 0;
  border-right: 1px solid var(--border-light);
  flex-shrink: 0;
  line-height: 2px;
  background: var(--bg-secondary);
  transition: var(--transition-normal);
}

.upload-zone.compact .upload-text .secondary-text {
  padding: 0 16px;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 7px;
  margin: 0;
  flex: 1;
  height: 100%;
}

.upload-zone.compact .upload-text .requirements-container {
  display: flex;
  align-items: center;
  gap: 7px;
  background: var(--bg-secondary);
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.upload-zone.compact .upload-text .divider {
  display: inline-block;
  width: 1px;
  height: 16px;
  background: var(--border-light);
  margin: 0;
  align-self: center;
}

.upload-zone.compact:hover {
  border-color: var(--brand-primary);
  background: var(--brand-primary-lighter);
}

.upload-zone.compact:hover .upload-text .requirements-container {
  background: var(--bg-primary);
}

.upload-zone.compact.dragging {
  border-color: var(--brand-primary);
  background: var(--brand-primary-light);
  box-shadow: 0 0 0 2px var(--brand-primary-lighter);
}

.upload-zone.compact.dragging .upload-text .requirements-container {
  background: var(--bg-primary);
}

.upload-zone.compact:hover .upload-text .primary-text {
  background: var(--bg-primary);
}

.upload-zone.compact.dragging .upload-text .primary-text {
  background: var(--bg-primary);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 16px;
  height: 100%;
  position: relative;
  z-index: 1;
}

.upload-icon {
  width: 40px;
  height: 40px;
  color: var(--brand-primary);
  opacity: 0.8;
  transition: var(--transition-normal);
  display: flex;
  justify-content: center;
  margin: 0 auto -4px auto;
}

.upload-zone:hover .upload-icon {
  transform: translateY(-2px);
  opacity: 1;
}

.upload-text {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: -6px;
}

.primary-text {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0;
}

.highlight {
  color: var(--brand-primary);
  font-weight: 500;
}

.secondary-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

.upload-text .primary-text {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 400;
  margin-bottom: 8px;
}

.upload-text .primary-text .highlight {
  color: var(--brand-primary);
  font-weight: 400;
  font-size: 1.1em;
}

.upload-text .secondary-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 2;
}

.upload-text .secondary-text:last-child {
  margin-bottom: 0;
} 