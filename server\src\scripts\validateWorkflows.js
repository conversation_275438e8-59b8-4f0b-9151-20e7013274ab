/**
 * 工作流数据验证脚本
 * 
 * 使用方法:
 * node server/src/scripts/validateWorkflows.js
 * 
 * 功能:
 * 1. 验证数据库中的工作流数据完整性
 * 2. 检查JSON文件与数据库记录的一致性
 * 3. 发现缺失或多余的工作流
 * 4. 验证数据格式和必填字段
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 导入模型
const Workflow = require('../models/Workflow');

class WorkflowValidator {
  constructor() {
    this.jsonDir = path.resolve(__dirname, '../services/comfyClient/apiJson');
    this.issues = [];
    this.stats = {
      totalJsonFiles: 0,
      totalDbRecords: 0,
      missingInDb: 0,
      missingJsonFiles: 0,
      validRecords: 0,
      invalidRecords: 0
    };
  }

  // 连接数据库
  async connectDB() {
    try {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aibikini');
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error);
      process.exit(1);
    }
  }

  // 添加问题记录
  addIssue(type, message, details = null) {
    this.issues.push({
      type,
      message,
      details,
      timestamp: new Date()
    });
  }

  // 获取JSON文件列表
  getJsonFiles() {
    if (!fs.existsSync(this.jsonDir)) {
      this.addIssue('error', `JSON目录不存在: ${this.jsonDir}`);
      return [];
    }

    const files = fs.readdirSync(this.jsonDir);
    const jsonFiles = files.filter(file => file.endsWith('.json'));
    this.stats.totalJsonFiles = jsonFiles.length;
    
    console.log(`📁 找到 ${jsonFiles.length} 个JSON文件`);
    return jsonFiles.map(file => path.basename(file, '.json'));
  }

  // 获取数据库中的工作流
  async getDbWorkflows() {
    const workflows = await Workflow.find({}).select('id name category enabled createdBy');
    this.stats.totalDbRecords = workflows.length;
    
    console.log(`💾 数据库中有 ${workflows.length} 个工作流记录`);
    return workflows;
  }

  // 验证单个工作流记录
  validateWorkflowRecord(workflow) {
    const issues = [];

    // 检查必填字段
    if (!workflow.id || workflow.id.trim() === '') {
      issues.push('缺少工作流ID');
    }

    if (!workflow.name || workflow.name.trim() === '') {
      issues.push('缺少工作流名称');
    }

    if (!workflow.category || workflow.category.trim() === '') {
      issues.push('缺少工作流分类');
    }

    // 检查分类是否有效
    const validCategories = ['款式设计', '模特图', '工具', '其他'];
    if (workflow.category && !validCategories.includes(workflow.category)) {
      issues.push(`无效的分类: ${workflow.category}`);
    }

    // 检查创建者
    if (!workflow.createdBy) {
      issues.push('缺少创建者信息');
    }

    // 检查优先级
    if (workflow.priority !== undefined && (workflow.priority < 0 || workflow.priority > 100)) {
      issues.push(`优先级超出范围 (0-100): ${workflow.priority}`);
    }

    // 检查支持的平台
    if (workflow.supportedPlatforms && workflow.supportedPlatforms.length === 0) {
      issues.push('支持的平台列表为空');
    }

    return issues;
  }

  // 检查数据一致性
  async checkConsistency() {
    console.log('\n🔍 检查数据一致性...');

    const jsonWorkflowIds = this.getJsonFiles();
    const dbWorkflows = await this.getDbWorkflows();
    const dbWorkflowIds = dbWorkflows.map(w => w.id);

    // 检查JSON文件对应的数据库记录
    const missingInDb = jsonWorkflowIds.filter(id => !dbWorkflowIds.includes(id));
    if (missingInDb.length > 0) {
      this.stats.missingInDb = missingInDb.length;
      this.addIssue('warning', `以下工作流有JSON文件但数据库中缺少记录`, missingInDb);
      console.log(`⚠️  数据库中缺少 ${missingInDb.length} 个工作流记录:`);
      missingInDb.forEach(id => console.log(`   - ${id}`));
    }

    // 检查数据库记录对应的JSON文件
    const missingJsonFiles = dbWorkflowIds.filter(id => !jsonWorkflowIds.includes(id));
    if (missingJsonFiles.length > 0) {
      this.stats.missingJsonFiles = missingJsonFiles.length;
      this.addIssue('warning', `以下工作流有数据库记录但缺少JSON文件`, missingJsonFiles);
      console.log(`⚠️  缺少 ${missingJsonFiles.length} 个JSON文件:`);
      missingJsonFiles.forEach(id => console.log(`   - ${id}.json`));
    }

    if (missingInDb.length === 0 && missingJsonFiles.length === 0) {
      console.log('✅ JSON文件与数据库记录完全一致');
    }
  }

  // 验证数据库记录
  async validateDbRecords() {
    console.log('\n🔍 验证数据库记录...');

    const workflows = await Workflow.find({});
    let validCount = 0;
    let invalidCount = 0;

    for (const workflow of workflows) {
      const issues = this.validateWorkflowRecord(workflow);
      
      if (issues.length === 0) {
        validCount++;
      } else {
        invalidCount++;
        this.addIssue('error', `工作流 ${workflow.id} 存在问题`, issues);
        console.log(`❌ 工作流 ${workflow.id} 存在问题:`);
        issues.forEach(issue => console.log(`   - ${issue}`));
      }
    }

    this.stats.validRecords = validCount;
    this.stats.invalidRecords = invalidCount;

    console.log(`✅ 有效记录: ${validCount} 个`);
    if (invalidCount > 0) {
      console.log(`❌ 无效记录: ${invalidCount} 个`);
    }
  }

  // 检查JSON文件格式
  checkJsonFiles() {
    console.log('\n🔍 检查JSON文件格式...');

    const jsonFiles = fs.readdirSync(this.jsonDir).filter(file => file.endsWith('.json'));
    let validJsonCount = 0;
    let invalidJsonCount = 0;

    for (const filename of jsonFiles) {
      const filePath = path.join(this.jsonDir, filename);
      
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        JSON.parse(content);
        validJsonCount++;
      } catch (error) {
        invalidJsonCount++;
        this.addIssue('error', `JSON文件格式错误: ${filename}`, error.message);
        console.log(`❌ JSON文件格式错误: ${filename}`);
        console.log(`   错误: ${error.message}`);
      }
    }

    console.log(`✅ 有效JSON文件: ${validJsonCount} 个`);
    if (invalidJsonCount > 0) {
      console.log(`❌ 无效JSON文件: ${invalidJsonCount} 个`);
    }
  }

  // 检查分类分布
  async checkCategoryDistribution() {
    console.log('\n📊 检查分类分布...');

    const workflows = await Workflow.find({}).select('category');
    const categoryStats = {};

    workflows.forEach(w => {
      categoryStats[w.category] = (categoryStats[w.category] || 0) + 1;
    });

    console.log('分类统计:');
    Object.entries(categoryStats).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} 个`);
    });

    // 检查是否有空分类
    if (categoryStats[''] || categoryStats[undefined] || categoryStats[null]) {
      this.addIssue('warning', '存在空分类的工作流');
    }
  }

  // 检查优先级分布
  async checkPriorityDistribution() {
    console.log('\n🎯 检查优先级分布...');

    const workflows = await Workflow.find({}).select('priority id name');
    const priorityRanges = {
      '高优先级 (70-100)': [],
      '中优先级 (30-69)': [],
      '低优先级 (1-29)': [],
      '未设置 (0)': []
    };

    workflows.forEach(w => {
      const priority = w.priority || 0;
      if (priority >= 70) priorityRanges['高优先级 (70-100)'].push(w);
      else if (priority >= 30) priorityRanges['中优先级 (30-69)'].push(w);
      else if (priority >= 1) priorityRanges['低优先级 (1-29)'].push(w);
      else priorityRanges['未设置 (0)'].push(w);
    });

    Object.entries(priorityRanges).forEach(([range, workflows]) => {
      console.log(`   ${range}: ${workflows.length} 个`);
      if (workflows.length > 0 && range === '未设置 (0)') {
        this.addIssue('info', `有 ${workflows.length} 个工作流未设置优先级`, 
          workflows.map(w => w.id));
      }
    });
  }

  // 生成验证报告
  generateReport() {
    console.log('\n📋 验证报告');
    console.log('='.repeat(50));

    // 统计信息
    console.log('\n📈 统计信息:');
    console.log(`   JSON文件总数: ${this.stats.totalJsonFiles}`);
    console.log(`   数据库记录总数: ${this.stats.totalDbRecords}`);
    console.log(`   数据库中缺少的记录: ${this.stats.missingInDb}`);
    console.log(`   缺少的JSON文件: ${this.stats.missingJsonFiles}`);
    console.log(`   有效记录: ${this.stats.validRecords}`);
    console.log(`   无效记录: ${this.stats.invalidRecords}`);

    // 问题汇总
    const errorIssues = this.issues.filter(i => i.type === 'error');
    const warningIssues = this.issues.filter(i => i.type === 'warning');
    const infoIssues = this.issues.filter(i => i.type === 'info');

    console.log('\n🚨 问题汇总:');
    console.log(`   错误: ${errorIssues.length} 个`);
    console.log(`   警告: ${warningIssues.length} 个`);
    console.log(`   信息: ${infoIssues.length} 个`);

    // 健康度评分
    const totalIssues = errorIssues.length + warningIssues.length;
    const healthScore = Math.max(0, 100 - (errorIssues.length * 10 + warningIssues.length * 5));
    
    console.log(`\n💚 健康度评分: ${healthScore}/100`);
    
    if (healthScore >= 90) {
      console.log('🎉 工作流数据状态优秀！');
    } else if (healthScore >= 70) {
      console.log('👍 工作流数据状态良好，建议修复警告问题');
    } else if (healthScore >= 50) {
      console.log('⚠️  工作流数据存在一些问题，建议及时修复');
    } else {
      console.log('🚨 工作流数据存在严重问题，需要立即修复！');
    }

    // 建议操作
    console.log('\n💡 建议操作:');
    if (this.stats.missingInDb > 0) {
      console.log('   - 运行 quickInsertWorkflows.js 插入缺失的工作流记录');
    }
    if (this.stats.invalidRecords > 0) {
      console.log('   - 运行 updateWorkflowsInfo.js 修复无效的工作流记录');
    }
    if (errorIssues.length > 0) {
      console.log('   - 手动修复数据错误问题');
    }
    if (warningIssues.length > 0) {
      console.log('   - 检查并处理警告问题');
    }
  }

  // 主执行方法
  async run() {
    try {
      console.log('🔍 开始工作流数据验证...\n');
      
      await this.connectDB();
      
      // 执行各项检查
      this.checkJsonFiles();
      await this.checkConsistency();
      await this.validateDbRecords();
      await this.checkCategoryDistribution();
      await this.checkPriorityDistribution();
      
      // 生成报告
      this.generateReport();
      
      console.log('\n✅ 验证完成!');
      
      // 如果有严重问题，返回非零退出码
      const errorCount = this.issues.filter(i => i.type === 'error').length;
      if (errorCount > 0) {
        process.exit(1);
      }
      
    } catch (error) {
      console.error('\n💥 验证失败:', error.message);
      process.exit(1);
    } finally {
      await mongoose.disconnect();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const validator = new WorkflowValidator();
  validator.run();
}

module.exports = WorkflowValidator;
