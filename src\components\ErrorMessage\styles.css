/* 错误消息容器 */
.error-message {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-radius: 6px;
  margin: 8px 0;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.3s;
}

/* 简单错误消息样式 - 红色小字 */
.simple-error-message {
  color: var(--error-color);
  font-size: 12px;
  line-height: 1.5;
  margin: 4px 0;
  display: block;
  text-align: left;
}

.simple-error-message .remaining-attempts,
.simple-error-message .lockout-time {
  margin-left: 4px;
  font-size: 12px;
}

/* 不同类型的错误消息样式 */
.error-message.network {
  background-color: #f0f5ff;
  border-color: #d6e4ff;
}

.error-message.validation {
  background-color: #fffbe6;
  border-color: #ffe58f;
}

.error-message.server {
  background-color: #fff2f0;
  border-color: #ffccc7;
}

.error-message.auth {
  background-color: #f9f0ff;
  border-color: #efdbff;
}

.error-message.timeout {
  background-color: #fcf4e6;
  border-color: #ffd591;
}

/* 账户锁定错误样式 */
.error-message.locked {
  background-color: #fff1f0;
  border-color: #ffa39e;
}

/* 错误图标 */
.error-icon {
  margin-right: 12px;
  font-size: 18px;
  line-height: 1;
}

/* 错误内容 */
.error-content {
  flex: 1;
}

/* 错误文本 */
.error-text {
  margin: 0 0 4px 0;
  font-weight: 500;
}

/* 错误建议 */
.error-suggestion {
  margin: 0;
  font-size: 12px;
  color: #666;
}

/* 重试按钮 */
.error-retry {
  margin-top: 8px;
  padding: 4px 12px;
  background-color: transparent;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.error-retry:hover {
  color: #1890ff;
  border-color: #1890ff;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .error-message {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .error-icon {
    font-size: 16px;
  }
  
  .error-suggestion {
    font-size: 11px;
  }
} 