import React from 'react';
import { useNavigate } from 'react-router-dom';
import './index.css';

/**
 * 登录要求组件
 * 如果用户未登录，显示登录提示
 * 如果用户已登录，显示正常内容
 * 
 * @param {Object} props
 * @param {boolean} props.isLoggedIn - 用户是否已登录
 * @param {ReactNode} props.children - 如果用户已登录，显示的内容
 * @param {string} props.featureName - 功能名称，用于显示在提示信息中
 */
const RequireLogin = ({ isLoggedIn, children, featureName = '此功能' }) => {
  const navigate = useNavigate();

  // 如果用户已登录，正常显示内容
  if (isLoggedIn) {
    return children;
  }

  // 如果用户未登录，显示登录提示
  return (
    <div className="main-content">
      <div className="login-prompt">
        <h2>请先登录</h2>
        <p>您需要登录后才能使用{featureName}</p>
        <div className="btn-group">
          <button 
            className="login-btn"
            onClick={() => navigate('/')}
          >
            返回首页
          </button>
          <button 
            className="login-btn"
            onClick={() => {
              // 触发全局登录弹窗
              if (window.showLoginModal) {
                window.showLoginModal();
              } else {
                // 如果全局方法不可用，直接跳转到首页
                navigate('/');
              }
            }}
          >
            登录 / 注册
          </button>
        </div>
      </div>
    </div>
  );
};

export default RequireLogin; 