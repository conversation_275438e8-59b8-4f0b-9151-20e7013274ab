import React, { useState, useEffect, useImperativeHandle, forwardRef, useCallback } from 'react';
import './index.css';
import { EditOutlined, DeleteOutlined, SearchOutlined, SortAscendingOutlined, SortDescendingOutlined, FilterOutlined, DownloadOutlined } from '@ant-design/icons';
import { Button, Empty, Tooltip, message, Dropdown, Menu, Input } from 'antd';
import { showDeleteConfirmModal } from '../../utils/modalUtils';
import request from '../../api/request';
import { getCurrentUserId } from '../../api';
import { MdOutlineZoomOutMap, MdClose, MdChevronLeft, MdChevronRight } from 'react-icons/md';
import Pagination from '../Pagination';
import JSZip from 'jszip';
import '../../styles/close-buttons.css';
import ImagePreviewModal from '../common/ImagePreviewModal';

// 虚拟模特数量上限
const MAX_MODELS_COUNT = 36;

const VirtualModelManager = forwardRef(({ onEditModel }, ref) => {
  const [models, setModels] = useState([]);
  const [filteredModels, setFilteredModels] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10; // 每页显示10个模特
  const [loading, setLoading] = useState(true);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState('');
  const [previewImageAlt, setPreviewImageAlt] = useState('');
  const [previewImages, setPreviewImages] = useState([]);
  const [currentPreviewIndex, setCurrentPreviewIndex] = useState(0);
  const [sortField, setSortField] = useState('createdAt'); // 默认按创建时间排序
  const [sortOrder, setSortOrder] = useState('desc'); // 默认为降序(最新的在前)
  const [filters, setFilters] = useState({
    id: '',
    name: '',
    gender: '',
    age: '',
    bodyType: '',
    region: '',
    createdAt: ''
  });
  
  // 加载模特数据
  useEffect(() => {
    loadModelsData();
  }, []);
  
  // 当排序条件变化时重新排序并重置到第一页
  useEffect(() => {
    if (models.length > 0) {
      // 创建一个新的引用，避免直接修改状态
      const sortedModels = [...filteredModels];
      // 原地排序
      sortModels(sortedModels);
      // 批量更新状态，减少重渲染次数
      setFilteredModels(sortedModels);
      setCurrentPage(1); // 重置到第一页
    }
  }, [sortField, sortOrder]);

  // 当筛选条件变化时重新筛选数据
  useEffect(() => {
    applyFilters();
  }, [filters, models]);
  
  // 应用筛选条件
  const applyFilters = () => {
    let result = [...models];
    
    // 筛选ID
    if (filters.id) {
      result = result.filter(model => 
        model.id && model.id.toString().toLowerCase().includes(filters.id.toLowerCase())
      );
    }
    
    // 筛选名称
    if (filters.name) {
      result = result.filter(model => 
        model.name && model.name.toLowerCase().includes(filters.name.toLowerCase())
      );
    }
    
    // 筛选性别
    if (filters.gender) {
      result = result.filter(model => 
        model.tags?.gender === filters.gender
      );
    }
    
    // 筛选年龄
    if (filters.age) {
      result = result.filter(model => 
        model.tags?.age === filters.age
      );
    }
    
    // 筛选体型
    if (filters.bodyType) {
      result = result.filter(model => 
        model.tags?.bodyType === filters.bodyType
      );
    }
    
    // 筛选地区
    if (filters.region) {
      result = result.filter(model => 
        model.tags?.region === filters.region
      );
    }
    
    // 筛选创建时间
    if (filters.createdAt) {
      result = result.filter(model => 
        model.createdAt && formatDate(model.createdAt).includes(filters.createdAt)
      );
    }
    
    // 应用排序
    sortModels(result);
    setFilteredModels(result);
    setCurrentPage(1); // 重置到第一页
  };
  
  // 处理筛选变化
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  // 清除指定字段的筛选
  const clearFilter = (field) => {
    setFilters(prev => ({
      ...prev,
      [field]: ''
    }));
  };
  
  // 清除所有筛选
  const clearAllFilters = () => {
    setFilters({
      id: '',
      name: '',
      gender: '',
      age: '',
      bodyType: '',
      region: '',
      createdAt: ''
    });
  };
  
  // 从后端API获取虚拟模特列表
  const loadModelsData = async () => {
    setLoading(true);
    try {
      const response = await request.get('/models/virtual-models');
      
      if (response && response.success) {
        const modelData = response.data;
        
        // 处理每个模特的图片数据和标签信息
        const processedModels = modelData.map(model => ({
          ...model,
          // 处理变体图片
          variantUrls: model.images?.map(img => img.url) || [],
          // 从 tags 对象中提取各个标签值
          gender: model.tags?.gender || '',
          age: model.tags?.age || '',
          region: model.tags?.region || '',
          bodyType: model.tags?.bodyType || '',
          // 保留原始 tags 对象
          tags: model.tags || {
            gender: '',
            age: '',
            region: '',
            bodyType: ''
          }
        }));

        // 应用排序
        sortModels(processedModels);
        setModels(processedModels);
        setFilteredModels(processedModels);
      } else {
        throw new Error(response?.message || '获取模特列表失败');
      }
    } catch (error) {
      console.error('加载虚拟模特数据失败:', error);
      message.error('加载虚拟模特列表失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 排序模特数据
  const sortModels = (data) => {
    return data.sort((a, b) => {
      let valueA, valueB;
      
      // 根据排序字段获取对应的值
      switch(sortField) {
        case 'id':
          valueA = a.id;
          valueB = b.id;
          break;
        case 'name':
          valueA = a.name || '';
          valueB = b.name || '';
          break;
        case 'createdAt':
          valueA = new Date(a.createdAt);
          valueB = new Date(b.createdAt);
          break;
        case 'gender':
          valueA = a.tags?.gender || '';
          valueB = b.tags?.gender || '';
          break;
        case 'age':
          valueA = a.tags?.age || '';
          valueB = b.tags?.age || '';
          break;
        case 'bodyType':
          valueA = a.tags?.bodyType || '';
          valueB = b.tags?.bodyType || '';
          break;
        case 'region':
          valueA = a.tags?.region || '';
          valueB = b.tags?.region || '';
          break;
        default:
          valueA = new Date(a.createdAt);
          valueB = new Date(b.createdAt);
      }
      
      // 对比值并根据排序顺序返回结果
      if (sortOrder === 'asc') {
        if (valueA < valueB) return -1;
        if (valueA > valueB) return 1;
        return 0;
      } else {
        if (valueA > valueB) return -1;
        if (valueA < valueB) return 1;
        return 0;
      }
    });
  };
  
  // 处理排序
  const handleSort = (field) => {
    // 如果点击当前排序字段，切换排序顺序
    if (field === sortField) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // 如果点击新字段，设置为新的排序字段，并默认为升序
      setSortField(field);
      setSortOrder('asc');
    }
  };
  
  // 渲染排序图标
  const renderSortIcon = (field) => {
    if (field !== sortField) {
      return <SortAscendingOutlined className="sort-icon default" />;
    }
    
    return sortOrder === 'asc' 
      ? <SortAscendingOutlined className="sort-icon active" /> 
      : <SortDescendingOutlined className="sort-icon active" />;
  };
  
  // 获取缩略图URL
  const getThumbnailUrl = (model) => {
    // 优先使用变体图片
    if (model.variantUrls && model.variantUrls.length > 0) {
      return model.variantUrls[0];
    }
    
    // 回退到主图片URL
    return model.imageUrl;
  };
  
  // 向父组件暴露方法
  useImperativeHandle(ref, () => ({
    // 提供刷新数据的方法
    refresh: () => {
      loadModelsData();
    },
    // 检查是否达到数量上限
    isAtMaxLimit: () => {
      return models.length >= MAX_MODELS_COUNT;
    },
    // 获取当前模特数量
    getModelsCount: () => {
      return models.length;
    }
  }));
  
  // 处理删除模特
  const handleDeleteModel = (modelId) => {
    showDeleteConfirmModal({
      title: '确认删除',
      content: '确定要删除这个虚拟模特吗？删除后将无法恢复。',
      okText: '确认删除',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      onOk: async () => {
        try {
          // 显示加载提示
          const loadingMsg = message.loading('正在删除模特数据...', 0);
          
          // 调用删除API
          const response = await request.delete(`/models/virtual-model/${modelId}`);
          
          if (response && response.success) {
            // 更新本地状态
            const updatedModels = models.filter(model => model.id !== modelId);
            setModels(updatedModels);
            setFilteredModels(updatedModels);
            
            loadingMsg(); // 关闭加载提示
            message.success('虚拟模特已删除');
          } else {
            throw new Error(response?.message || '删除失败');
          }
        } catch (error) {
          console.error('删除虚拟模特失败:', error);
          message.error('删除失败: ' + error.message);
        }
      }
    });
  };
  
  // 计算当前页的模特
  const currentPageModels = filteredModels.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );
  
  // 处理页码变化
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };
  
  // 格式化日期显示
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  };
  
  // 处理图片预览
  const handlePreviewImage = (imageUrl, allImages) => {
    const images = allImages || [imageUrl];
    const currentIndex = images.indexOf(imageUrl);
    
    setPreviewImages(images);
    setPreviewImageUrl(imageUrl);
    setPreviewImageAlt('虚拟模特预览图片');
    setCurrentPreviewIndex(currentIndex >= 0 ? currentIndex : 0);
    setPreviewModalVisible(true);
  };
  
  // 处理翻页
  const handlePrevImage = () => {
    if (previewImages.length <= 1) return;
    
    const newIndex = (currentPreviewIndex - 1 + previewImages.length) % previewImages.length;
    setCurrentPreviewIndex(newIndex);
    setPreviewImageUrl(previewImages[newIndex]);
  };
  
  const handleNextImage = () => {
    if (previewImages.length <= 1) return;
    
    const newIndex = (currentPreviewIndex + 1) % previewImages.length;
    setCurrentPreviewIndex(newIndex);
    setPreviewImageUrl(previewImages[newIndex]);
  };
  
  // 处理键盘方向键事件
  const handleKeyDown = useCallback((e) => {
    if (e.key === 'ArrowLeft') {
      handlePrevImage();
    } else if (e.key === 'ArrowRight') {
      handleNextImage();
    } else if (e.key === 'Escape') {
      setPreviewModalVisible(false);
    }
  }, [previewImages, currentPreviewIndex]);
  
  // 添加键盘事件监听器
  useEffect(() => {
    if (previewModalVisible) {
      window.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [previewModalVisible, handleKeyDown]);
  
  // 关闭图片预览
  const handleCloseImagePreview = () => {
    setPreviewModalVisible(false);
    setPreviewImageUrl('');
    setPreviewImageAlt('');
    setPreviewImages([]);
    setCurrentPreviewIndex(0);
  };
  
  // 为每个列渲染筛选菜单
  const renderFilterMenu = (field) => {
    const getUniqueValues = () => {
      if (field === 'gender') {
        // 从实际数据中获取性别选项
        const uniqueGenders = [...new Set(models.map(model => model.tags?.gender).filter(Boolean))];
        return uniqueGenders.length > 0 ? uniqueGenders : ['男性', '女性'];
      }
      
      if (field === 'age') {
        // 从实际数据中获取年龄选项
        return [...new Set(models.map(model => model.tags?.age).filter(Boolean))].sort();
      }
      
      if (field === 'bodyType') {
        // 从实际数据中获取体型选项
        return [...new Set(models.map(model => model.tags?.bodyType).filter(Boolean))].sort();
      }
      
      if (field === 'region') {
        // 从实际数据中获取地区选项
        return [...new Set(models.map(model => model.tags?.region).filter(Boolean))].sort();
      }
      
      return null;
    };
    
    const uniqueValues = getUniqueValues();
    
    // 文本输入筛选
    if (!uniqueValues) {
      return (
        <div className="filter-dropdown" onClick={e => e.stopPropagation()}>
          <Input 
            value={filters[field]} 
            onChange={e => handleFilterChange(field, e.target.value)}
            placeholder="输入关键词"
            size="small"
            allowClear
            onPressEnter={(e) => e.preventDefault()}
            style={{ 
              width: '90%',
              backgroundColor: 'var(--bg-primary)',
              color: 'var(--text-primary)'
            }}
            className="filter-search-input"
          />
        </div>
      );
    }
    
    // 选项列表筛选 - 扁平化设计
    return (
      <div className="filter-dropdown" onClick={e => e.stopPropagation()}>
        {uniqueValues.map(value => (
          <div 
            key={value} 
            className={`filter-menu-item ${filters[field] === value ? 'active' : ''}`}
            onClick={() => handleFilterChange(field, value)}
          >
            {value}
          </div>
        ))}
        {filters[field] && (
          <div className="filter-actions">
            <Button 
              type="text"
              size="small" 
              onClick={() => clearFilter(field)}
              style={{ padding: '0', height: 'auto', fontSize: 'calc(var(--font-size-xs) - 1px)', color: 'var(--text-secondary)' }}
            >
              清除
            </Button>
          </div>
        )}
      </div>
    );
  };
  
  // 渲染筛选按钮
  const renderFilterButton = (field) => {
    return (
      <Dropdown 
        dropdownRender={() => renderFilterMenu(field)} 
        trigger={['click']} 
        placement="bottomRight"
        overlayStyle={{ padding: 0 }}
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
        destroyPopupOnHide={false}
        transitionName=""
        animation=""
      >
        <button 
          className={`filter-button ${filters[field] ? 'active' : ''}`}
          title={filters[field] ? `当前筛选: ${filters[field]}` : '筛选'}
        >
          <FilterOutlined />
        </button>
      </Dropdown>
    );
  };
  
  // 处理批量下载虚拟模特图片
  const handleBatchDownload = async (modelId) => {
    // 查找模特
    const model = models.find(m => m.id === modelId);
    if (!model) {
      message.error('无法找到模特数据');
      return;
    }

    // 检查是否有变体图片
    if (!model.variantUrls || model.variantUrls.length === 0) {
      message.error('该模特没有可下载的变体图片');
      return;
    }

    // 显示加载提示
    const loadingMsg = message.loading('正在生成压缩包...', 0);
    
    try {
      const zip = new JSZip();
      
      // 下载所有变体图片
      const imageUrls = model.variantUrls.map((url, index) => ({
        url: url.replace(/^http:/, 'https:'),
        fileName: `${model.id}_变体${index + 1}.jpg`
      }));
      
      // 下载图片并添加到zip
      const fetchPromises = imageUrls.map(async (item) => {
        try {
          const response = await fetch(item.url);
          if (!response.ok) throw new Error(`下载失败: ${response.statusText}`);
          
          const blob = await response.blob();
          zip.file(item.fileName, blob);
          
          return true;
        } catch (error) {
          console.error(`下载图片 ${item.url} 失败:`, error);
          return false;
        }
      });
      
      await Promise.all(fetchPromises);
      
      // 生成并下载zip文件
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      const zipUrl = URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = zipUrl;
      link.download = `虚拟模特_${model.id}_${model.name || '未命名'}_变体图.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setTimeout(() => URL.revokeObjectURL(zipUrl), 100);
      
      loadingMsg();
      message.success('变体图片打包下载成功');
    } catch (error) {
      console.error('打包下载失败:', error);
      loadingMsg();
      message.error('打包下载失败，请稍后重试');
    }
  };
  
  return (
    <div className="virtual-model-manager">
      <div className="model-table-container">
        {models.length === 0 && !loading ? (
          <div className="no-models-message">
            <Empty 
              description={<span style={{ color: 'var(--text-secondary)' }}>暂无登记的虚拟模特</span>}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              styles={{ 
                image: { filter: 'var(--icon-filter)' } // 使用主题变量处理图片颜色
              }}
            />
            <p className="tip">您可以在生成结果中选择满意的图片，添加为虚拟模特</p>
          </div>
        ) : (
          <table className="model-table">
            <thead>
              <tr>
                <th className="thumbnail-col">缩略图</th>
                <th className="id-col">
                  <div className="th-content">
                    <button 
                      className="sort-button" 
                      onClick={() => handleSort('id')}
                      title={`按ID${sortField === 'id' && sortOrder === 'asc' ? '降序' : '升序'}排列`}
                    >
                      {renderSortIcon('id')}
                    </button>
                    <span>模特ID</span>
                    {renderFilterButton('id')}
                  </div>
                </th>
                <th className="name-col">
                  <div className="th-content">
                    <button 
                      className="sort-button" 
                      onClick={() => handleSort('name')}
                      title={`按名称${sortField === 'name' && sortOrder === 'asc' ? '降序' : '升序'}排列`}
                    >
                      {renderSortIcon('name')}
                    </button>
                    <span>名称</span>
                    {renderFilterButton('name')}
                  </div>
                </th>
                <th className="gender-col">
                  <div className="th-content">
                    <button 
                      className="sort-button" 
                      onClick={() => handleSort('gender')}
                      title={`按性别${sortField === 'gender' && sortOrder === 'asc' ? '降序' : '升序'}排列`}
                    >
                      {renderSortIcon('gender')}
                    </button>
                    <span>性别</span>
                    {renderFilterButton('gender')}
                  </div>
                </th>
                <th className="age-col">
                  <div className="th-content">
                    <button 
                      className="sort-button" 
                      onClick={() => handleSort('age')}
                      title={`按年龄${sortField === 'age' && sortOrder === 'asc' ? '降序' : '升序'}排列`}
                    >
                      {renderSortIcon('age')}
                    </button>
                    <span>年龄</span>
                    {renderFilterButton('age')}
                  </div>
                </th>
                <th className="body-type-col">
                  <div className="th-content">
                    <button 
                      className="sort-button" 
                      onClick={() => handleSort('bodyType')}
                      title={`按体型${sortField === 'bodyType' && sortOrder === 'asc' ? '降序' : '升序'}排列`}
                    >
                      {renderSortIcon('bodyType')}
                    </button>
                    <span>体型</span>
                    {renderFilterButton('bodyType')}
                  </div>
                </th>
                <th className="region-col">
                  <div className="th-content">
                    <button 
                      className="sort-button" 
                      onClick={() => handleSort('region')}
                      title={`按地区${sortField === 'region' && sortOrder === 'asc' ? '降序' : '升序'}排列`}
                    >
                      {renderSortIcon('region')}
                    </button>
                    <span>地区</span>
                    {renderFilterButton('region')}
                  </div>
                </th>
                <th className="date-col">
                  <div className="th-content">
                    <button 
                      className="sort-button" 
                      onClick={() => handleSort('createdAt')}
                      title={`按创建时间${sortField === 'createdAt' && sortOrder === 'asc' ? '降序' : '升序'}排列`}
                    >
                      {renderSortIcon('createdAt')}
                    </button>
                    <span>创建时间</span>
                    {renderFilterButton('createdAt')}
                  </div>
                </th>
                <th className="actions-col">操作</th>
              </tr>
            </thead>
            <tbody>
              {filteredModels.length === 0 ? (
                <tr className="no-hover-effect">
                  <td colSpan="9" style={{ textAlign: 'center', height: '150px' }}>
                    <Empty 
                      description={<span style={{ color: 'var(--text-secondary)' }}>没有符合筛选条件的虚拟模特</span>}
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      styles={{ 
                        image: { filter: 'var(--icon-filter)' }
                      }}
                    />
                  </td>
                </tr>
              ) : (
                currentPageModels.map(model => (
                  <tr key={model.id}>
                    <td className="thumbnail-col">
                      {model.variantUrls && model.variantUrls.length > 0 ? (
                        <div className="variant-images-grid">
                          {model.variantUrls.map((url, index) => (
                            <div className="variant-thumbnail-wrapper" key={index}>
                              <img 
                                src={url} 
                                alt={`${model.name} - 变体${index+1}`} 
                                className="variant-thumbnail"
                                onClick={() => handlePreviewImage(url, model.variantUrls)}
                                onError={(e) => {
                                  // 如果服务器上的图片加载失败，回退到原始URL
                                  if (index === 0) {
                                    e.target.src = model.imageUrl;
                                  } else {
                                    // 其他变体图加载失败则隐藏
                                    e.target.style.display = 'none';
                                  }
                                }}
                              />
                              <div 
                                className="variant-thumbnail-overlay"
                                onClick={() => handlePreviewImage(url, model.variantUrls)}
                              >
                                <MdOutlineZoomOutMap />
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="variant-thumbnail-wrapper">
                          <img 
                            src={getThumbnailUrl(model)} 
                            alt={model.name} 
                            className="model-thumbnail" 
                            onClick={() => handlePreviewImage(getThumbnailUrl(model))}
                            onError={(e) => {
                              // 如果服务器上的图片加载失败，回退到原始URL
                              e.target.src = model.imageUrl;
                            }}
                          />
                          <div 
                            className="variant-thumbnail-overlay"
                            onClick={() => handlePreviewImage(getThumbnailUrl(model))}
                          >
                            <MdOutlineZoomOutMap />
                          </div>
                        </div>
                      )}
                    </td>
                    <td className="id-col">{model.id}</td>
                    <td className="name-col">{model.name}</td>
                    <td className="gender-col">{model.tags?.gender}</td>
                    <td className="age-col">{model.tags?.age}</td>
                    <td className="body-type-col">{model.tags?.bodyType}</td>
                    <td className="region-col">{model.tags?.region}</td>
                    <td className="date-col">{formatDate(model.createdAt)}</td>
                    <td className="actions-col">
                      <div className="action-buttons">
                        <button 
                          className="edit-button" 
                          onClick={() => onEditModel(model)}
                          title="编辑"
                        >
                          <EditOutlined />
                        </button>
                        <button 
                          className="delete-button" 
                          onClick={() => handleDeleteModel(model.id)}
                          title="删除"
                        >
                          <DeleteOutlined />
                        </button>
                        <button 
                          className="download-button" 
                          onClick={() => handleBatchDownload(model.id)}
                          title="批量下载"
                        >
                          <DownloadOutlined />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        )}
      </div>
      
      {filteredModels.length > pageSize && (
        <Pagination
          current={currentPage}
          total={filteredModels.length}
          pageSize={pageSize}
          onChange={handlePageChange}
        />
      )}

      {/* 图片预览模态框 */}
      <ImagePreviewModal
        visible={previewModalVisible}
        imageUrl={previewImageUrl}
        onClose={handleCloseImagePreview}
        alt={previewImageAlt}
        featureName="虚拟模特"
        showHint={false}
      />
      
      {/* 多图片预览的翻页按钮 */}
      {previewModalVisible && previewImages.length > 1 && (
        <div className="multi-image-preview-controls">
          <button 
            className="prev-image-button"
            onClick={handlePrevImage}
            style={{
              position: 'fixed',
              left: '20px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 10001,
              background: 'rgba(0, 0, 0, 0.5)',
              border: 'none',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              color: 'white',
              fontSize: '20px'
            }}
          >
            <MdChevronLeft />
          </button>
          <button 
            className="next-image-button"
            onClick={handleNextImage}
            style={{
              position: 'fixed',
              right: '20px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 10001,
              background: 'rgba(0, 0, 0, 0.5)',
              border: 'none',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              color: 'white',
              fontSize: '20px'
            }}
          >
            <MdChevronRight />
          </button>
          <div 
            className="image-index-indicator"
            style={{
              position: 'fixed',
              bottom: '20px',
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 10001,
              background: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '20px',
              fontSize: '14px'
            }}
          >
            {currentPreviewIndex + 1} / {previewImages.length}
          </div>
        </div>
      )}
    </div>
  );
});

// 导出虚拟模特管理器组件和常量
VirtualModelManager.MAX_MODELS_COUNT = MAX_MODELS_COUNT;
export default VirtualModelManager;