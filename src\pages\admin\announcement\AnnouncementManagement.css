.announcement-management {
  padding: 24px;
}

.table-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ant-upload-list-picture-card .ant-upload-list-item {
  float: left;
  width: 104px;
  height: 104px;
  margin: 0 8px 8px 0;
}

.ant-upload.ant-upload-select-picture-card {
  width: 104px;
  height: 104px;
  margin: 0 8px 8px 0;
}

.announcement-preview {
  padding: 24px;
  background: #fff;
  border-radius: 4px;
}

.announcement-preview-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
}

.announcement-preview-content {
  margin-bottom: 16px;
  white-space: pre-wrap;
}

.announcement-preview-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.announcement-preview-image {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 4px;
}

@media (max-width: 768px) {
  .announcement-management {
    padding: 12px;
  }
  
  .announcement-preview-image {
    width: 100%;
    height: auto;
  }
} 