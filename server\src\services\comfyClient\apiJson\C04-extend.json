{"3": {"inputs": {"needInput": true, "seed": 712707610204231, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["24", 0], "positive": ["74", 0], "negative": ["74", 1], "latent_image": ["92", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "随机种子"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["26", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "24": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "25": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "26": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "49": {"inputs": {"guidance": 30, "conditioning": ["75", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "65": {"inputs": {"text": "", "speak_and_recognation": true, "clip": ["25", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "71": {"inputs": {"needInput": true, "left": 304, "top": 408, "right": 304, "bottom": 408, "feathering": 10, "image": ["93", 0]}, "class_type": "ImagePadForOutpaintMasked", "_meta": {"title": "四个方向扩展尺寸输入"}}, "74": {"inputs": {"noise_mask": false, "positive": ["49", 0], "negative": ["84", 0], "vae": ["26", 0], "pixels": ["71", 0], "mask": ["71", 1]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "75": {"inputs": {"text": ["86", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["25", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "77": {"inputs": {"noise_mask": true, "positive": ["49", 0], "negative": ["84", 0], "vae": ["26", 0], "pixels": ["8", 0], "mask": ["78", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "78": {"inputs": {"expand": 2, "tapered_corners": true, "mask": ["71", 1]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "79": {"inputs": {"seed": 1032707809307525, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 0.30000000000000004, "model": ["80", 0], "positive": ["77", 0], "negative": ["77", 1], "latent_image": ["77", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "80": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "81": {"inputs": {"samples": ["79", 0], "vae": ["26", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "84": {"inputs": {"conditioning": ["65", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "85": {"inputs": {"filename_prefix": "Extend", "images": ["91", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "86": {"inputs": {"from_translate": "auto", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "GoogleTranslator", "text": "", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "描述词"}}, "91": {"inputs": {"needInput": true, "width": 1340, "height": 1785, "interpolation": "bicubic", "method": "fill / crop", "condition": "always", "multiple_of": 0, "image": ["81", 0]}, "class_type": "ImageResize+", "_meta": {"title": "扩图最终整体尺寸输入"}}, "92": {"inputs": {"needInput": true, "amount": 2, "samples": ["74", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "图片数量"}}, "93": {"inputs": {"needInput": true, "url": "https://images.pexels.com/photos/32085609/pexels-photo-32085609.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "原始图片上传"}}}