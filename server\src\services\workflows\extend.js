const workflow = {
  prompt: {
    "3": {
      "inputs": {
        "seed": 1108469638786844,
        "steps": 20,
        "cfg": 1,
        "sampler_name": "euler",
        "scheduler": "normal",
        "denoise": 1,
        "model": [
          "24",
          0
        ],
        "positive": [
          "74",
          0
        ],
        "negative": [
          "74",
          1
        ],
        "latent_image": [
          "92",
          0
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "随机种子"
      }
    },
    "8": {
      "inputs": {
        "samples": [
          "3",
          0
        ],
        "vae": [
          "26",
          0
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE解码"
      }
    },
    "24": {
      "inputs": {
        "unet_name": "flux1-fill-dev.safetensors",
        "weight_dtype": "fp8_e4m3fn"
      },
      "class_type": "UNETLoader",
      "_meta": {
        "title": "UNet加载器"
      }
    },
    "25": {
      "inputs": {
        "clip_name1": "clip_l.safetensors",
        "clip_name2": "t5xxl_fp8_e4m3fn.safetensors",
        "type": "flux",
        "device": "default"
      },
      "class_type": "DualCLIPLoader",
      "_meta": {
        "title": "双CLIP加载器"
      }
    },
    "26": {
      "inputs": {
        "vae_name": "ae.sft"
      },
      "class_type": "VAELoader",
      "_meta": {
        "title": "加载VAE"
      }
    },
    "49": {
      "inputs": {
        "guidance": 30,
        "conditioning": [
          "75",
          0
        ]
      },
      "class_type": "FluxGuidance",
      "_meta": {
        "title": "Flux引导"
      }
    },
    "63": {
      "inputs": {
        "image": "oldman.webp"
      },
      "class_type": "LoadImage",
      "_meta": {
        "title": "原始图片上传"
      }
    },
    "65": {
      "inputs": {
        "text": "",
        "speak_and_recognation": true,
        "clip": [
          "25",
          0
        ]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIP文本编码"
      }
    },
    "71": {
      "inputs": {
        "left": 304,
        "top": 408,
        "right": 304,
        "bottom": 408,
        "feathering": 10,
        "image": [
          "63",
          0
        ]
      },
      "class_type": "ImagePadForOutpaintMasked",
      "_meta": {
        "title": "四个方向扩展尺寸输入"
      }
    },
    "74": {
      "inputs": {
        "noise_mask": false,
        "positive": [
          "49",
          0
        ],
        "negative": [
          "84",
          0
        ],
        "vae": [
          "26",
          0
        ],
        "pixels": [
          "71",
          0
        ],
        "mask": [
          "71",
          1
        ]
      },
      "class_type": "InpaintModelConditioning",
      "_meta": {
        "title": "内补模型条件"
      }
    },
    "75": {
      "inputs": {
        "text": [
          "86",
          0
        ],
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        },
        "clip": [
          "25",
          0
        ]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIP文本编码"
      }
    },
    "77": {
      "inputs": {
        "noise_mask": true,
        "positive": [
          "49",
          0
        ],
        "negative": [
          "84",
          0
        ],
        "vae": [
          "26",
          0
        ],
        "pixels": [
          "8",
          0
        ],
        "mask": [
          "78",
          0
        ]
      },
      "class_type": "InpaintModelConditioning",
      "_meta": {
        "title": "内补模型条件"
      }
    },
    "78": {
      "inputs": {
        "expand": 2,
        "tapered_corners": true,
        "mask": [
          "71",
          1
        ]
      },
      "class_type": "GrowMask",
      "_meta": {
        "title": "扩展遮罩"
      }
    },
    "79": {
      "inputs": {
        "seed": 1032707809307525,
        "steps": 20,
        "cfg": 1,
        "sampler_name": "euler",
        "scheduler": "normal",
        "denoise": 0.30000000000000004,
        "model": [
          "80",
          0
        ],
        "positive": [
          "77",
          0
        ],
        "negative": [
          "77",
          1
        ],
        "latent_image": [
          "77",
          2
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "K采样器"
      }
    },
    "80": {
      "inputs": {
        "unet_name": "flux1-dev-fp8.safetensors",
        "weight_dtype": "fp8_e4m3fn_fast"
      },
      "class_type": "UNETLoader",
      "_meta": {
        "title": "UNet加载器"
      }
    },
    "81": {
      "inputs": {
        "samples": [
          "79",
          0
        ],
        "vae": [
          "26",
          0
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE解码"
      }
    },
    "84": {
      "inputs": {
        "conditioning": [
          "65",
          0
        ]
      },
      "class_type": "ConditioningZeroOut",
      "_meta": {
        "title": "条件零化"
      }
    },
    "85": {
      "inputs": {
        "filename_prefix": "Extend",
        "images": [
          "91",
          0
        ]
      },
      "class_type": "SaveImage",
      "_meta": {
        "title": "保存图像"
      }
    },
    "86": {
      "inputs": {
        "from_translate": "auto",
        "to_translate": "english",
        "add_proxies": false,
        "proxies": "",
        "auth_data": "",
        "service": "GoogleTranslator",
        "text": "",
        "Show proxy": "proxy_hide",
        "Show authorization": "authorization_hide",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "DeepTranslatorTextNode",
      "_meta": {
        "title": "描述词"
      }
    },
    "91": {
      "inputs": {
        "width": 1340,
        "height": 1785,
        "interpolation": "bicubic",
        "method": "fill / crop",
        "condition": "always",
        "multiple_of": 0,
        "image": [
          "81",
          0
        ]
      },
      "class_type": "ImageResize+",
      "_meta": {
        "title": "扩图最终整体尺寸输入"
      }
    },
    "92": {
      "inputs": {
        "amount": 1,
        "samples": [
          "74",
          2
        ]
      },
      "class_type": "RepeatLatentBatch",
      "_meta": {
        "title": "图片数量"
      }
    }
  }
};

module.exports = workflow; 