import { globalWebSocketManager } from './comfyUITaskTracker';

class WebSocketLifecycleManager {
  constructor() {
    this.isInitialized = false;
    this.beforeUnloadHandler = null;
    this.visibilityChangeHandler = null;
  }

  initialize() {
    if (this.isInitialized) {
      return;
    }

    console.log('初始化WebSocket生命周期管理器');

    // 监听浏览器关闭事件
    this.beforeUnloadHandler = this.handleBeforeUnload.bind(this);
    window.addEventListener('beforeunload', this.beforeUnloadHandler);

    // 监听页面可见性变化（用户切换标签页或最小化）
    this.visibilityChangeHandler = this.handleVisibilityChange.bind(this);
    document.addEventListener('visibilitychange', this.visibilityChangeHandler);

    // 监听页面卸载事件
    window.addEventListener('unload', this.handleUnload.bind(this));

    this.isInitialized = true;
    console.log('WebSocket生命周期管理器已初始化');
  }

  handleBeforeUnload(event) {
    console.log('检测到浏览器即将关闭，准备关闭WebSocket连接');
    // 在浏览器关闭前关闭WebSocket连接
    this.cleanup();
  }

  handleVisibilityChange() {
    if (document.hidden) {
      console.log('页面不可见，但保持WebSocket连接');
    } else {
      console.log('页面重新可见，检查WebSocket连接状态');
      // 页面重新可见时，检查连接状态
      this.checkConnectionStatus();
    }
  }

  handleUnload() {
    console.log('页面卸载，关闭WebSocket连接');
    this.cleanup();
  }

  checkConnectionStatus() {
    const status = globalWebSocketManager.getConnectionStatus();
    console.log('WebSocket连接状态:', status);
    
    // 如果连接断开，尝试重连
    if (!status.isConnected && !status.isConnecting) {
      console.log('检测到WebSocket连接断开，尝试重连');
      globalWebSocketManager.connect().catch(error => {
        console.error('重连失败:', error);
      });
    }
  }

  // 用户退出登录时调用
  handleUserLogout() {
    console.log('用户退出登录，关闭WebSocket连接');
    this.cleanup();
  }

  cleanup() {
    console.log('清理WebSocket生命周期管理器');
    
    // 关闭WebSocket连接
    globalWebSocketManager.disconnect();

    // 移除事件监听器
    if (this.beforeUnloadHandler) {
      window.removeEventListener('beforeunload', this.beforeUnloadHandler);
      this.beforeUnloadHandler = null;
    }

    if (this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler);
      this.visibilityChangeHandler = null;
    }

    this.isInitialized = false;
  }

  // 获取管理器状态
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      hasBeforeUnloadHandler: !!this.beforeUnloadHandler,
      hasVisibilityChangeHandler: !!this.visibilityChangeHandler
    };
  }
}

// 创建全局单例
const websocketLifecycleManager = new WebSocketLifecycleManager();

// 自动初始化
websocketLifecycleManager.initialize();

export default websocketLifecycleManager; 