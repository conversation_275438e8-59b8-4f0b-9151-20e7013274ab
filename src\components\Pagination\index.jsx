import React, { useState } from 'react';
import './index.css';

const Pagination = ({
  current,
  total,
  pageSize,
  onChange,
  showTotal = true
}) => {
  const [jumpPage, setJumpPage] = useState('');
  const totalPages = Math.ceil(total / pageSize);

  // 生成页码按钮数组
  function getPageNumbers() {
    if (totalPages <= 7) {
      // 总页数少于等于7页时，显示所有页码
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }
    
    const pages = [];
    
    // 始终显示第一页
    pages.push(1);
    
    if (current <= 4) {
      // 当前页在前4页时：1, 2, 3, 4, 5, ..., totalPages
      for (let i = 2; i <= 5; i++) {
        if (i <= totalPages) {
          pages.push(i);
        }
      }
      if (totalPages > 5) {
        pages.push('...', totalPages);
      }
    } else if (current >= totalPages - 3) {
      // 当前页在后4页时：1, ..., totalPages-4, totalPages-3, totalPages-2, totalPages-1, totalPages
      pages.push('...');
      for (let i = totalPages - 4; i <= totalPages; i++) {
        if (i > 1) {
          pages.push(i);
        }
      }
    } else {
      // 当前页在中间时：1, ..., current-1, current, current+1, ..., totalPages
      pages.push('...', current - 1, current, current + 1, '...', totalPages);
    }
    
    // 最终去重，确保没有重复的页码
    const uniquePages = [];
    const seen = new Set();
    
    for (const page of pages) {
      if (page === '...') {
        uniquePages.push(page);
      } else if (typeof page === 'number' && !seen.has(page)) {
        seen.add(page);
        uniquePages.push(page);
      }
    }
    
    return uniquePages;
  }

  const handlePageChange = (page) => {
    onChange?.(page);
  };

  return (
    <div className="pagination-container">
      {showTotal && (
        <div className="pagination-info">
          第 {(current - 1) * pageSize + 1}-{Math.min(current * pageSize, total)} 条，共 {total} 条任务
        </div>
      )}
      <div className="pagination-group">
        {/* 首页按钮 */}
        <button
          className="pagination-button"
          onClick={() => handlePageChange(1)}
          disabled={current === 1}
          title="首页"
        >
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 18V6M17 6l-7 6 7 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        {/* 上一页按钮 */}
        <button
          className="pagination-button"
          onClick={() => handlePageChange(current - 1)}
          disabled={current === 1}
          title="上一页"
        >
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 6l-6 6 6 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        
        {/* 页码按钮 */}
        <div className="page-numbers">
          {getPageNumbers().map((page, idx) =>
            page === '...'
              ? <span key={`ellipsis-${idx}`} className="pagination-ellipsis">...</span>
              : (
                <button
                  key={`page-${page}-${idx}`}
                  className={`pagination-button ${current === page ? 'active' : ''}`}
                  onClick={() => handlePageChange(page)}
                  disabled={current === page}
                >
                  {page}
                </button>
              )
          )}
        </div>
        
        {/* 下一页按钮 */}
        <button
          className="pagination-button"
          onClick={() => handlePageChange(current + 1)}
          disabled={current === totalPages}
          title="下一页"
        >
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 6l6 6-6 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        {/* 尾页按钮 */}
        <button
          className="pagination-button"
          onClick={() => handlePageChange(totalPages)}
          disabled={current === totalPages}
          title="尾页"
        >
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 18V6M7 6l7 6-7 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
      <div className="pagination-jumper">
        <span>跳到</span>
        <input
          type="number"
          min="1"
          max={totalPages}
          value={jumpPage}
          onChange={(e) => {
            const value = e.target.value;
            if (!isNaN(value)) {
              setJumpPage(value);
            }
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              const page = Math.min(
                Math.max(1, parseInt(jumpPage || 1)),
                totalPages
              );
              handlePageChange(page);
              setJumpPage('');
            }
          }}
        />
        <span>页</span>
        <button 
          className="jump-button"
          onClick={() => {
            if (jumpPage) {
              const page = Math.min(
                Math.max(1, parseInt(jumpPage)),
                totalPages
              );
              handlePageChange(page);
              setJumpPage('');
            }
          }}
        >
          确定
        </button>
      </div>
    </div>
  );
};

export default Pagination; 