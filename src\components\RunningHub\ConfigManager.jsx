/**
 * RunningHub配置管理组件
 * 用于管理RunningHub API密钥和工作流配置
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  Snackbar,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { runningHubConfig } from '../../services/runningHub/config';
import runningHubAPI from '../../api/runningHub';

const ConfigManager = () => {
  const [configs, setConfigs] = useState([]);
  const [defaultConfig, setDefaultConfig] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    apiKey: '',
    workflowId: '',
    description: ''
  });
  const [showApiKey, setShowApiKey] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  const [validating, setValidating] = useState(false);

  // 加载配置列表
  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = () => {
    const allConfigs = runningHubConfig.getAllConfigs();
    const defaultConfigName = runningHubConfig.getDefaultConfigName();
    setConfigs(allConfigs);
    setDefaultConfig(defaultConfigName);
  };

  const handleOpenDialog = (config = null) => {
    if (config) {
      setEditingConfig(config.name);
      setFormData({
        name: config.name,
        apiKey: config.apiKey,
        workflowId: config.workflowId,
        description: config.description || ''
      });
    } else {
      setEditingConfig(null);
      setFormData({
        name: '',
        apiKey: '',
        workflowId: '',
        description: ''
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingConfig(null);
    setFormData({
      name: '',
      apiKey: '',
      workflowId: '',
      description: ''
    });
  };

  const handleSaveConfig = async () => {
    try {
      if (!formData.name || !formData.apiKey || !formData.workflowId) {
        showSnackbar('请填写所有必需字段', 'error');
        return;
      }

      // 验证API密钥格式
      if (formData.apiKey.length !== 32 || !/^[a-zA-Z0-9]+$/.test(formData.apiKey)) {
        showSnackbar('API密钥格式不正确（应为32位字母数字组合）', 'error');
        return;
      }

      // 验证工作流ID格式
      if (!/^\d+$/.test(formData.workflowId)) {
        showSnackbar('工作流ID格式不正确（应为数字）', 'error');
        return;
      }

      // 验证配置
      setValidating(true);
      try {
        await runningHubAPI.getAccountInfo(formData.apiKey);
        showSnackbar('API密钥验证成功', 'success');
      } catch (error) {
        showSnackbar('API密钥验证失败，请检查密钥是否正确', 'warning');
      }
      setValidating(false);

      if (editingConfig) {
        // 更新配置
        runningHubConfig.updateConfig(editingConfig, formData);
        showSnackbar('配置更新成功', 'success');
      } else {
        // 添加新配置
        if (runningHubConfig.hasConfig(formData.name)) {
          showSnackbar('配置名称已存在', 'error');
          return;
        }
        runningHubConfig.addConfig(formData.name, formData, configs.length === 0);
        showSnackbar('配置添加成功', 'success');
      }

      loadConfigs();
      handleCloseDialog();
    } catch (error) {
      console.error('保存配置失败:', error);
      showSnackbar('保存配置失败', 'error');
      setValidating(false);
    }
  };

  const handleDeleteConfig = (configName) => {
    if (window.confirm(`确定要删除配置 "${configName}" 吗？`)) {
      runningHubConfig.removeConfig(configName);
      loadConfigs();
      showSnackbar('配置删除成功', 'success');
    }
  };

  const handleSetDefault = (configName) => {
    runningHubConfig.setDefaultConfig(configName);
    loadConfigs();
    showSnackbar(`已设置 "${configName}" 为默认配置`, 'success');
  };

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const maskApiKey = (apiKey) => {
    if (!apiKey) return '';
    return `${apiKey.substring(0, 8)}${'*'.repeat(16)}${apiKey.substring(24)}`;
  };

  return (
    <Box>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">RunningHub配置管理</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
            >
              添加配置
            </Button>
          </Box>

          {configs.length === 0 ? (
            <Alert severity="info">
              暂无配置，请添加RunningHub API配置以开始使用
            </Alert>
          ) : (
            <List>
              {configs.map((config) => (
                <ListItem key={config.name} divider>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="subtitle1">{config.name}</Typography>
                        {defaultConfig === config.name && (
                          <Chip
                            label="默认"
                            size="small"
                            color="primary"
                            icon={<StarIcon />}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="textSecondary">
                          API密钥: {showApiKey ? config.apiKey : maskApiKey(config.apiKey)}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          工作流ID: {config.workflowId}
                        </Typography>
                        {config.description && (
                          <Typography variant="body2" color="textSecondary">
                            描述: {config.description}
                          </Typography>
                        )}
                        <Typography variant="caption" color="textSecondary">
                          创建时间: {new Date(config.createdAt).toLocaleString()}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Box display="flex" gap={1}>
                      {defaultConfig !== config.name && (
                        <IconButton
                          size="small"
                          onClick={() => handleSetDefault(config.name)}
                          title="设为默认"
                        >
                          <StarBorderIcon />
                        </IconButton>
                      )}
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog(config)}
                        title="编辑"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteConfig(config.name)}
                        title="删除"
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}

          <Box mt={2} display="flex" alignItems="center">
            <FormControlLabel
              control={
                <Switch
                  checked={showApiKey}
                  onChange={(e) => setShowApiKey(e.target.checked)}
                />
              }
              label="显示完整API密钥"
            />
          </Box>
        </CardContent>
      </Card>

      {/* 配置编辑对话框 */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingConfig ? '编辑配置' : '添加配置'}
        </DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <TextField
              label="配置名称"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              fullWidth
              required
              disabled={!!editingConfig}
            />
            <TextField
              label="API密钥"
              value={formData.apiKey}
              onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
              fullWidth
              required
              type={showApiKey ? 'text' : 'password'}
              InputProps={{
                endAdornment: (
                  <IconButton
                    onClick={() => setShowApiKey(!showApiKey)}
                    edge="end"
                  >
                    {showApiKey ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                )
              }}
              helperText="32位字母数字组合"
            />
            <TextField
              label="工作流ID"
              value={formData.workflowId}
              onChange={(e) => setFormData(prev => ({ ...prev, workflowId: e.target.value }))}
              fullWidth
              required
              helperText="纯数字格式"
            />
            <TextField
              label="描述"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              fullWidth
              multiline
              rows={2}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>取消</Button>
          <Button
            onClick={handleSaveConfig}
            variant="contained"
            disabled={validating}
          >
            {validating ? '验证中...' : '保存'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ConfigManager;
