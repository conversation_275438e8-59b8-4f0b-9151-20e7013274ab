# selectedImage 统一使用指南

## 背景

在当前代码库中，许多页面同时使用了 `selectedImage` 和 `selectedImageDetails` 两个状态变量来存储选中的图片信息。经过分析发现：

1. 在大多数情况下，这两个变量存储完全相同的内容
2. `ImageDetailsModal` 组件始终接收 `selectedImage` 作为属性
3. 同时使用两个变量造成了代码冗余和维护困难

为解决这个问题，决定统一使用 `selectedImage` 变量，移除 `selectedImageDetails`。

## 实施方案

### 第一阶段：状态定义修改

移除所有页面中的 `selectedImageDetails` 状态定义：

```jsx
// 修改前
const [selectedImage, setSelectedImage] = useState(null);
const [selectedImageDetails, setSelectedImageDetails] = useState(null);

// 修改后
const [selectedImage, setSelectedImage] = useState(null);
```

### 第二阶段：替换 setter 函数调用

将所有 `setSelectedImageDetails()` 调用替换为 `setSelectedImage()`：

```jsx
// 修改前
setSelectedImageDetails(adaptedGeneratedImage);

// 修改后
setSelectedImage(adaptedGeneratedImage);
```

### 第三阶段：移除辅助函数

移除专门用于设置 `selectedImageDetails` 的辅助函数：

```jsx
// 以下函数需要移除
const setImageDetails = (details) => {
  setSelectedImageDetails(details);
};

const setImageDetailState = (detailState) => {
  setSelectedImageDetails(detailState);
};
```

### 第四阶段：替换变量引用

确保所有引用 `selectedImageDetails` 的地方改为引用 `selectedImage`：

```jsx
// 修改前
{showImageDetails && selectedImageDetails ? (
  <ImageDetailsModal
    selectedImage={selectedImageDetails}
    onClose={handleCloseImageDetails}
  />
) : null}

// 修改后
{showImageDetails && selectedImage ? (
  <ImageDetailsModal
    selectedImage={selectedImage}
    onClose={handleCloseImageDetails}
  />
) : null}
```

## 需要修改的文件

以下是需要修改的主要文件列表：

1. `src/pages/model/background/index.jsx`
2. `src/pages/model/fabric/index.jsx`
3. `src/pages/model/fashion/index.jsx`
4. `src/pages/model/recolor/index.jsx`
5. `src/pages/model/try-on/index.jsx`
6. `src/pages/model/virtual/index.jsx`
7. `src/pages/style/inspiration/index.jsx`
8. `src/pages/style/optimize/index.jsx`
9. `src/pages/style/trending/index.jsx`
10. `src/pages/tools/extend/index.jsx`
11. `src/pages/tools/extract/index.jsx`
12. `src/pages/tools/matting/index.jsx`
13. `src/pages/tools/upscale/index.jsx`

## 特别注意事项

1. **测试验证**：每修改完一个文件后，需要验证功能是否正常工作
2. **兼容性**：确保所有传递给 `ImageDetailsModal` 的数据格式一致
3. **防止错误**：在移除变量前，确保没有遗漏替换所有引用的地方

## 执行优先级

按照以下顺序进行修改：

1. 首先修改同时使用两个变量且内容相同的页面（如 fabric 页面）
2. 然后修改只使用 `selectedImageDetails` 的页面
3. 最后检查所有页面，确保统一使用 `selectedImage` 