删除不必要的方法代码createThumbnailAndSaveToHistory、saveToHistory、buildServerUrl

删除组件MemoizedImageDetailsModal，因为已经在GenerationArea/index.jsx中实现集成了，相应的修改显示详情代码修改成返回详情数据

删除下面就的任务接口方法的使用
```
import { 
  getTasks, 
  getTaskById, 
  deleteTask, 
  createTask,
  filterTasksByUser
} from '../../../api/task';

```
换成
```
import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask } from '../../../api/flowtask';
```


参考
核心代码 handleGenerate 方法
```
try {
      // 检查是否有服装图面板
      if (clothingPanels.length === 0) {
        message.error('请先上传服装图片');
        return;
      }

      // 检查是否已选择颜色
      if (!selectedColor) {
        message.error('请选择目标颜色');
        return;
      }
      
      // 检查是否已选择面料类型
      if (!fabricType) {
        message.error('请选择面料类型');
        return;
      }
      
      // 验证蒙版描述是否已填写
      if ((!clothingMaskPanel.selectedTag || clothingMaskPanel.selectedTag.trim() === '') && 
          (!clothingMaskPanel.customText || clothingMaskPanel.customText.trim() === '') &&
          (!clothingMaskPanel.description || clothingMaskPanel.description.trim() === '')) {
        message.error('请选择款式标签或填写自定义描述');
        return;
      }
      
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId() || 'developer';
      
      // 处理可能的本地上传图片
      let clothingImageToUse = clothingPanels[0];
      
      // 设置处理状态
      setIsProcessing(true);
      
      // 检查是否有自定义上传的服装图片（同时检查文件对象和类型）
      if (clothingPanels[0].file && clothingPanels[0].source === 'upload') {
        // 保存本地临时URL，用于创建缩略图
        const localUrl = clothingPanels[0].url;
        
        // 显示上传中提示
        message.loading('正在上传服装图片...', 0);
        const {urls,fileInfos}  = await uploadFiles([clothingPanels[0].file], 'recolor');
        clothingImageToUse={
          ...clothingPanels[0],
          url: urls[0],
          fileInfo: fileInfos[0],
          serverFileName: fileInfos[0].name,
          originalImage: urls[0],
          file: undefined, // 清除file属性，避免重复上传和存储不必要的数据
          fileInfo: {
            ...(clothingPanels[0].fileInfo || {}),
            serverFileName: fileInfos[0].name // 确保在fileInfo中也设置serverFileName
          }
        }
      }

      // 创建一个新的任务ID
      const taskId = generateId(ID_TYPES.TASK);
      
      // 获取颜色预览图的数据
      const colorPreviewElement = document.querySelector('.color-preview-box');
      let colorPreviewData = null;
      
      if (colorPreviewElement) {
        // 创建一个canvas来生成预览图
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 88;
        canvas.height = 88;
        
        // 使用选中的颜色填充canvas
        ctx.fillStyle = selectedColor;
        ctx.fillRect(0, 0, 88, 88);
        
        // 获取base64格式的预览图数据
        colorPreviewData = canvas.toDataURL('image/jpeg');
      }
      
      // 构建任务对象 - 仅使用新的components结构
      const taskData = {
        taskId: taskId,
        userId: currentUserId,
        createdAt: new Date().toISOString(),
        status: 'processing',
        imageCount: 1, // 固定生成1张图片
        taskType: 'recolor', // 指定任务类型为服装复色
        pageType: 'recolor', // 指定页面类型
        serverFileName: clothingImageToUse.serverFileName,
        primaryImageFileName: clothingImageToUse.serverFileName,
        components: [
          // 服装组件
          {
            componentType: 'clothingPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: clothingImageToUse.title || '服装',
            status: 'completed',
            serverFileName: clothingImageToUse.serverFileName,
            originalImage: clothingImageToUse.originalImage || clothingImageToUse.url,
            url: clothingImageToUse.processedFile || clothingImageToUse.url,
            fileInfo: clothingImageToUse.fileInfo || {},
            processedFile: clothingImageToUse.processedFile,
            // 包含蒙版信息（如果有）
            hasMask: clothingImageToUse.hasMask || false,
            maskData: clothingImageToUse.maskData,
            maskPath: clothingImageToUse.maskPath,
            maskFile: clothingImageToUse.maskFile
          },
          // 颜色组件
          {
            componentType: 'colorPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '颜色选择器',
            hex: selectedColor.toUpperCase(), // 确保使用大写的颜色代码
            preview: colorPreviewData, // 添加颜色预览图数据
            adjustments: colorAdjustments, // 添加颜色调整数据
            colors: selectedColors.map(color => color.colorHex)
          },
          // 面料类型组件
          {
            componentType: 'typeSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '面料类型',
            value: fabricType // 使用数字值1、2或3，对应浅色系、中性色系和深色服装
          },
          // 蒙版描述组件（如果有描述内容）
          ...(clothingMaskPanel.description ? [{
            componentType: 'maskDescriptionPanel',
            componentId: clothingMaskPanel.componentId,
            name: '款式描述',
            selectedTag: clothingMaskPanel.selectedTag,
            customText: clothingMaskPanel.customText,
            description: clothingMaskPanel.description
          }] : [])
        ],
        // 初始状态下的空图像数组 - 使用generatedImages，固定为1张
        generatedImages: [{
          imageIndex: 0,
          status: 'processing'
        }]
      };
      
      console.log('创建任务数据:', taskData);
      
      // 使用 generationAreaRef 更新任务状态，而不是直接修改本地状态
      if (generationAreaRef.current && generationAreaRef.current.setGenerationTasks) {
        if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      }
      
      // 切换到结果标签页
      setActiveTab('result');
      
    
      // 生产环境下调用后端API
      if (process.env.NODE_ENV === 'production') {
        try {
          // 使用 flowTask API 创建任务，而不是 createTask
          const flowTaskData = {
            ...taskData,
            workflowName: WORKFLOW_NAME.RECOLOR, // 使用常量定义的工作流名称
            params: {
              fabricType: fabricType, // 面料类型参数
              targetColor: selectedColor.replace('#', ''), // 去掉#号的颜色值
              // 如果有蒙版描述，添加到参数中
              ...(clothingMaskPanel.description ? {
                maskDescription: clothingMaskPanel.description
              } : {})
            }
          };
          
          // 调用 createFlowTask 而不是 createTask
          const createdTask = await createFlowTask(flowTaskData);
          
          if (createdTask) {
            // 更新任务ID，以匹配服务器返回的ID
            taskData.taskId = createdTask.taskId;
            
            // 使用 generationAreaRef 更新任务状态
            if (generationAreaRef.current && generationAreaRef.current.setGenerationTasks) {
              if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
            }
            
            // 开始轮询任务状态
            setCurrentTaskId(createdTask.taskId);
            checkTaskStatus(createdTask.taskId);
          } else {
            message.error('创建任务失败');
            setIsProcessing(false);
            
            // 更新任务状态为失败
            taskData.status = 'failed';
            taskData.errorMessage = '创建任务失败';
            
            // 使用 generationAreaRef 更新任务状态
            if (generationAreaRef.current && generationAreaRef.current.setGenerationTasks) {
              if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
            }
          }
        } catch (error) {
          console.error('API调用失败:', error);
          message.error('创建任务失败: ' + (error.message || '未知错误'));
          setIsProcessing(false);
          
          // 更新任务状态为失败
          taskData.status = 'failed';
          taskData.errorMessage = error.message || '未知错误';
          
          // 使用 generationAreaRef 更新任务状态
          if (generationAreaRef.current && generationAreaRef.current.setGenerationTasks) {
            generationAreaRef.current.setGenerationTasks(taskData);
          }
        }
      }
    } catch (error) {
      console.error('生成过程中出错:', error);
      message.error('生成失败: ' + (error.message || '未知错误'));
      setIsProcessing(false);
    }
  ```



  结合以上内容和 /fashion/index.jsx 文件，重构工作流