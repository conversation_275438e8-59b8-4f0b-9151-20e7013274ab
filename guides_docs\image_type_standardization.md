# 图片类型与来源的统一标准化方案

## 问题背景

当前系统中不同页面对图片类型的处理存在不一致性：

- 背景页面：使用 `type: 'foreground'` 表示业务类型，但用 `type === 'custom'` 判断是否需要上传
- 时尚大片页面：统一使用 `type: 'custom'` 标记用户上传图片
- 服装复色页面：使用 `type: 'custom'` 标记用户上传图片
- 面料替换页面：原本使用 `type: 'fabric'`，现调整为使用 `type: 'custom'` 或引入 `source: 'upload'`

这种不一致性导致了代码维护困难，可能造成图片上传逻辑失效，需要进行统一标准化。

## 统一标准方案

### 核心原则

我们采用"分离职责"的方案，将图片的业务用途和图片来源分开表示：

1. **使用 `type` 表示图片的业务用途**
2. **使用 `source` 表示图片的来源**
3. **统一判断逻辑，通过 `source` 判断是否需要上传图片**

### 标准属性定义

```javascript
{
  // 业务用途 - 描述图片在功能中的角色
  type: 'foreground' | 'background' | 'fabric' | 'clothing' | 'reference',
  
  // 图片来源 - 统一使用source标记来源
  source: 'upload' | 'preset' | 'history' | 'generated',
  
  // 其他共通属性
  componentId: 'unique-id',  // 组件唯一标识符
  title: '面板标题',          // 显示名称
  status: 'completed',       // 处理状态
  url: '图片URL',            // 图片URL
  
  // 仅在source为'upload'时存在
  file: 文件对象           
}
```

### 各业务类型说明

1. **type: 'foreground'** - 背景替换功能中的前景图片（人物/物体）
2. **type: 'background'** - 背景替换功能中的背景图片/场景
3. **type: 'fabric'** - 面料替换功能中的面料图片
4. **type: 'clothing'** - 各功能中的服装图片
5. **type: 'reference'** - 时尚大片功能中的参考图片

### 各来源类型说明

1. **source: 'upload'** - 用户刚上传的需要处理的图片（含有file属性）
2. **source: 'preset'** - 系统预设的图片，已在服务器上
3. **source: 'history'** - 用户历史上传过的图片，已在服务器上
4. **source: 'generated'** - 系统生成的结果图片

## 统一判断逻辑

所有页面都使用相同的判断逻辑确定是否需要上传图片：

```javascript
// 判断是否需要上传
if (panel.file && panel.source === 'upload') {
  // 上传图片到服务器
}
```

## 实现示例

### 1. 图片上传组件返回结果

```javascript
// 处理上传结果 
const handleUploadResult = (results) => {
  if (results.type === 'panels') {
    const panelsWithAttributes = results.panels.map(panel => ({
      ...panel,
      type: 'clothing',  // 设置业务类型：根据具体页面设置相应的类型
      source: 'upload'   // 设置来源类型：用户上传
    }));
    setPanels(prevPanels => [...prevPanels, ...panelsWithAttributes]);
  }
};
```

### 2. 示例卡片选择处理

```javascript
const handleExampleClick = async (imageUrl) => {
  try {
    // 从URL获取图片并转换为File对象
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    const filename = imageUrl.split('/').pop() || 'example.jpg';
    const file = new File([blob], filename, { type: blob.type });
    
    // 创建面板数据 - 注意设置source为'upload'
    const panel = {
      componentId: generateId(ID_TYPES.COMPONENT),
      title: '示例图片',
      status: 'completed',
      url: URL.createObjectURL(file),
      file: file,
      type: 'clothing',    // 业务类型：根据页面设置
      source: 'upload'     // 来源标记：用户上传
    };
    
    // 添加到面板
    setPanels(prevPanels => [...prevPanels, panel]);
  } catch (error) {
    console.error('处理示例图片时出错:', error);
  }
};
```

### 3. 任务回填处理

```javascript
const handleEditTask = (task) => {
  // 获取组件信息
  const component = task.components.find(c => c.componentType === 'clothingPanel');
  if (component) {
    const panel = {
      componentId: generateId(ID_TYPES.COMPONENT),
      title: component.name || '服装',
      status: 'completed',
      url: component.url || component.originalImage,
      originalFile: component.originalFile,
      type: 'clothing',      // 设置业务类型
      source: 'history'      // 设置来源为历史记录，表示无需重新上传
    };
    setPanels([panel]);
  }
};
```

### 4. 生成按钮处理

```javascript
const handleGenerate = async () => {
  // 检查图片是否存在
  if (!panels || panels.length === 0) {
    message.error('请先上传图片');
    return;
  }
  
  // 处理图片上传
  let imageToUse = panels[0];
  
  // 判断是否需要上传到服务器 - 使用统一判断条件
  if (imageToUse.file && imageToUse.source === 'upload') {
    // 显示上传中提示
    message.loading('正在上传图片...', 0);
    
    try {
      // 上传图片
      const result = await uploadImage(imageToUse.file, 'upload', pageType, imageType);
      
      if (result && result.success) {
        // 更新图片信息
        imageToUse = {
          ...imageToUse,
          url: result.results[0].url,
          originalFile: result.results[0].originalFile,
          source: 'history'  // 上传后更新来源为历史记录
        };
      } else {
        message.error('图片上传失败');
        return;
      }
    } catch (error) {
      message.error('图片上传失败: ' + error.message);
      return;
    } finally {
      message.destroy();
    }
  }
  
  // 继续创建任务...
};
```

## 实施步骤

1. 在所有页面的上传结果处理函数中，统一设置 `type` 和 `source` 属性
2. 修改所有页面的生成函数，使用 `source === 'upload'` 判断是否需要上传图片
3. 更新任务回填逻辑，确保正确设置 `type` 和 `source` 属性
4. 更新场景选择和示例图片处理逻辑，确保设置正确的属性

## 注意事项

1. `type` 属性应该始终反映图片的业务用途，不应用于判断上传逻辑
2. `source` 属性应该始终用于判断图片来源和处理方式
3. 只有 `source === 'upload'` 且存在 `file` 属性的图片才需要上传到服务器
4. 上传完成后，应将 `source` 更新为 'history'，表示已经在服务器上

## 总结

通过分离图片业务用途(`type`)和图片来源(`source`)的职责，我们可以使代码更清晰、更易于维护，同时保持业务语义完整性。这种方法可以解决当前不同页面处理方式不一致的问题，提高系统整体可维护性。 