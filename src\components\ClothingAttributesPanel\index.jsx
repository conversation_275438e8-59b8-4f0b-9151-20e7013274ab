import React from 'react';
import PropTypes from 'prop-types';
import './index.css';

const ClothingAttributesPanel = ({
  clothingAttributes,
  onExpandClick
}) => {
  const title = '灵感探索方向';
  const description = clothingAttributes 
    ? '已设置服装属性和参数' 
    : '为灵感探索设定一些方向';

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          <img 
            src={clothingAttributes ? 'https://file.aibikini.cn/config/icons/inspiration-direction-active.png' : 'https://file.aibikini.cn/config/icons/inspiration-direction.png'} 
            alt="灵感探索方向" 
            className="component-icon" 
          />
          <div className="component-text">
            <h3>{title}</h3>
            <div className="component-content">
              <p>{description}</p>
            </div>
          </div>
        </div>
        <button 
          className="expand-btn"
          onClick={onExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

ClothingAttributesPanel.propTypes = {
  clothingAttributes: PropTypes.shape({
    type: PropTypes.string,
    description: PropTypes.string,
    image: PropTypes.string,
    attributes: PropTypes.object
  }),
  onExpandClick: PropTypes.func.isRequired
};

export default ClothingAttributesPanel; 