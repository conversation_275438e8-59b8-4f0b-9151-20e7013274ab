const jwt = require('jsonwebtoken');
const path = require('path');

/**
 * 文件访问控制中间件
 * 
 * 此中间件提供基于用户ID的文件访问控制，确保用户只能访问自己目录下的文件。
 * 工作原理：
 * 1. 从请求头中获取JWT令牌，并验证它以提取用户ID
 * 2. 修改请求URL，将其重定向到用户特定的存储目录（基于用户ID）
 * 3. 对于未登录用户或令牌无效的情况，可以选择重定向到developer目录或拒绝访问
 * 
 * 使用示例：
 * ```
 * // 为uploads目录配置文件访问控制
 * app.use('/uploads', fileAccessControl({ baseDir: 'uploads' }), express.static(storagePath));
 * 
 * // 为需要更高安全级别的目录配置，不允许访问developer目录
 * app.use('/secure', fileAccessControl({ baseDir: 'secure', allowDeveloper: false }), express.static(storagePath));
 * ```
 * 
 * 安全性考虑：
 * - 此中间件不会直接提供文件，它只是修改请求URL。实际的文件服务由 express.static 完成
 * - 目录遍历防护仍需依赖express.static和系统级别的文件权限配置
 * - 在生产环境中，应当设置allowDeveloper为false，确保用户必须登录才能访问文件
 * 
 * @param {Object} options 配置选项
 * @param {String} options.baseDir 基础目录路径，如 'uploads', 'fashion/generated' 等
 * @param {Boolean} options.allowDeveloper 是否允许访问developer目录（开发环境用），默认为true
 * @param {Boolean} options.debug 是否启用调试模式，默认为false
 * @returns {Function} Express中间件函数
 */
const fileAccessControl = ({ baseDir = '', allowDeveloper = false, debug = false } = {}) => {
  return (req, res, next) => {
    // 调试模式
    if (debug) {
      console.log('文件访问控制调试信息:');
      console.log('请求路径:', req.path);
      console.log('baseDir:', baseDir);
      console.log('是否允许developer访问:', allowDeveloper);
      console.log('请求用户:', req.user ? req.user._id.toString() : '未登录');
    }
    
    // 始终允许静态资源访问（如CSS、JS、图片等）
    if (req.path.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i)) {
      if (debug) console.log('静态资源直接通过:', req.path);
      return next();
    }
    
    // 允许开发者用户的资源直接访问（在开发环境下）
    if (allowDeveloper && req.path.includes('/developer/')) {
      if (debug) console.log('允许developer目录访问:', req.path);
      return next();
    }

    // 如果请求路径包含uploads，直接允许访问
    if (req.path.includes('/uploads/')) {
      if (debug) console.log('允许uploads目录访问:', req.path);
      return next(); 
    }
    
    // 如果用户未登录，且无特殊权限，则拒绝访问
    if (!req.user && !req.path.includes('/public/')) {
      if (debug) console.log('用户未登录，拒绝访问非公开内容:', req.path);
      return res.status(403).json({
        success: false,
        message: '无权访问此资源'
      });
    }
    
    // 确保用户只能访问自己的文件
    const userId = req.user ? req.user._id.toString() : null;
    
    // 如果是baseDir指定的目录，检查用户权限
    if (baseDir && userId) {
      // 构建应包含的路径部分
      const userPath = `/${baseDir}/${userId}/`;
      const devPath = `/${baseDir}/developer/`;
      
      // 检查路径是否包含当前用户ID或developer（如果允许）
      const allowedPath = req.path.includes(userPath) || 
                         (allowDeveloper && req.path.includes(devPath));
      
      if (!allowedPath) {
        if (debug) {
          console.log('权限检查失败:');
          console.log('- 用户路径:', userPath);
          console.log('- 开发者路径:', devPath);
          console.log('- 请求路径:', req.path);
          console.log('- 是否允许developer:', allowDeveloper);
        }
        return res.status(403).json({
          success: false,
          message: '无权访问此资源'
        });
      }
    }
    
    if (debug) console.log('文件访问控制通过:', req.path);
    next();
  };
};

module.exports = { fileAccessControl }; 