{"name": "server", "version": "1.0.0", "description": "AIBIKINI后端服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1", "init-workflows": "node scripts/initWorkflows.js", "init-workflows-clear": "node scripts/initWorkflows.js --clear"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@alicloud/dysmsapi20170525": "^4.1.0", "@alicloud/openapi-client": "^0.4.14", "@alicloud/pop-core": "^1.8.0", "@alicloud/tea-util": "^1.4.10", "ali-oss": "^6.23.0", "archiver": "^5.3.2", "axios": "^1.8.3", "bcryptjs": "^3.0.2", "config": "^4.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.7", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "form-data": "^4.0.2", "glob": "^10.4.5", "image-size": "^1.2.0", "jsonwebtoken": "^9.0.0", "mkdirp": "^3.0.1", "mongoose": "^8.10.1", "multer": "^1.4.5-lts.1", "node-cron": "^4.1.1", "node-fetch": "^2.7.0", "pngjs": "^7.0.0", "probe-image-size": "^7.2.3", "redis": "^4.7.0", "sharp": "^0.33.5", "svg-captcha": "^1.4.0", "uuid": "^11.1.0", "ws": "^8.18.1"}, "devDependencies": {"nodemon": "^3.1.9"}}