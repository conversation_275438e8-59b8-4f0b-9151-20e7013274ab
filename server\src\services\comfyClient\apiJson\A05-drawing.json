{"2": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 1024, "background_color": "#000000", "image": ["17", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "3": {"inputs": {"unet_name": "flux1-kontext-dev.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "4": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn_scaled.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "5": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "6": {"inputs": {"text": "Convert the image into designer's hand-drawn line drawings, with consistent line thickness and ignoring small details", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["4", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"pixels": ["2", 0], "vae": ["5", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "9": {"inputs": {"conditioning": ["6", 0], "latent": ["10", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "10": {"inputs": {"amount": 1, "samples": ["8", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "11": {"inputs": {"needInput": true, "seed": 190393800971419, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["3", 0], "positive": ["12", 0], "negative": ["13", 0], "latent_image": ["10", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "随机种子"}}, "12": {"inputs": {"guidance": 2.5, "conditioning": ["9", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "13": {"inputs": {"conditioning": ["6", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "14": {"inputs": {"samples": ["11", 0], "vae": ["5", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "15": {"inputs": {"filename_prefix": "Drawing", "images": ["22", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "17": {"inputs": {"rem_mode": "RMBG-2.0", "image_output": "<PERSON>de", "save_prefix": "ComfyUI", "torchscript_jit": false, "add_background": "white", "refine_foreground": false, "images": ["24", 0]}, "class_type": "easy imageRemBg", "_meta": {"title": "Image Remove Bg"}}, "19": {"inputs": {"image": ["24", 0]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}, "20": {"inputs": {"upscale_model": ["21", 0], "image": ["14", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "21": {"inputs": {"model_name": "RealESRGAN_x2.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "22": {"inputs": {"width": ["19", 0], "height": ["19", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": "crop", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 1, "device": "cpu", "image": ["20", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "24": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "上传图片"}}}