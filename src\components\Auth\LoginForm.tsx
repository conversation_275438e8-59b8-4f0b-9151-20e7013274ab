import React, { useState } from 'react';
import ErrorMessage from '../ErrorMessage';
import { useAuth } from '../../contexts/AuthContext';

// 通知类型接口
interface Notification {
  type: string;
  message: string;
  duration?: number;
}

// 登录结果接口定义
interface LoginResult {
  success: boolean;
  error?: string;
  message?: string;
  isLocked?: boolean;
  remainingMinutes?: number;
  abnormalLogin?: boolean;
  abnormalMessage?: string;
  lastLoginInfo?: {
    ip: string;
    time: string;
    userAgent: string;
  };
  user?: any;
}

// Auth类型定义
interface Auth {
  login: (loginId: string, password: string) => Promise<LoginResult>;
  getRemainingLockoutTime: (loginId: string) => number;
  setUser: (user: any) => void;
  // 其他可能的方法...
}

interface LoginFormProps {
  onClose: () => void;
  onForgotPassword: () => void;
  onLoginSuccess?: (notification: Notification) => void;
  className?: string;
}

interface LoginFormState {
  loginId: string;
  password: string;
  remember: boolean;
}

interface FormErrors {
  loginId?: string;
  password?: string;
  submit?: string;
}

// 错误消息组件属性接口
interface ErrorMessageProps {
  message: string;
  type: string;
  className?: string;
  onRetry?: () => void;
  showIcon?: boolean;
  remainingAttempts?: number;
  lockoutTime?: number;
}

/**
 * 登录表单组件
 * 处理用户登录逻辑，包括表单验证、错误处理和登录请求
 */
const LoginForm: React.FC<LoginFormProps> = ({
  onClose,
  onForgotPassword,
  onLoginSuccess,
  className = ''
}) => {
  // 将useAuth的返回值断言为我们定义的Auth接口
  const auth = useAuth() as unknown as Auth;
  
  // 表单状态
  const [form, setForm] = useState<LoginFormState>({
    loginId: '',
    password: '',
    remember: false
  });
  
  // 错误状态
  const [errors, setErrors] = useState<FormErrors>({});
  
  // 密码可见性状态
  const [passwordVisible, setPasswordVisible] = useState(false);
  
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  
  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setForm(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // 清除相关错误消息
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };
  
  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    const phoneRegex = /^1[3-9]\d{9}$/;
    const usernameRegex = /^[a-zA-Z0-9_-]{4,16}$/;
    
    // 验证登录ID
    if (!form.loginId) {
      newErrors.loginId = '请输入用户名或手机号';
    } else if (!phoneRegex.test(form.loginId) && !usernameRegex.test(form.loginId)) {
      newErrors.loginId = '请输入有效的用户名或手机号';
    }
    
    // 验证密码
    if (!form.password) {
      newErrors.password = '请输入密码';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setIsLoading(true);
      
      // 客户端预验证 - 避免发送不必要的网络请求
      // 密码格式验证
      if (form.password.length < 8) {
        setErrors({
          password: '密码长度不能小于8位'
        });
        setIsLoading(false);
        return;
      }
      
      if (form.password.length > 32) {
        setErrors({
          password: '密码长度不能超过32位'
        });
        setIsLoading(false);
        return;
      }
      
      // 检查账户是否被锁定
      const remainingMinutes = auth.getRemainingLockoutTime(form.loginId);
      if (remainingMinutes > 0) {
        setErrors({
          submit: `账户已被临时锁定，请${remainingMinutes}分钟后再试`
        });
        setIsLoading(false);
        return;
      }
      
      // 调用登录函数，为超时或网络问题设置超时处理
      const loginPromise = auth.login(form.loginId, form.password);
      
      // 避免卡住界面，如果5秒内没有响应，就继续处理UI
      setTimeout(() => {
        if (isLoading) {
          setIsLoading(false);
        }
      }, 5000);
      
      const result = await loginPromise;
      
      // 登录成功，正常处理
      if (result.success) {
        // 保存用户信息到localStorage
        if (result.user) {
          localStorage.setItem('user', JSON.stringify(result.user));
          auth.setUser(result.user);
        }

        // 如果有异常登录提醒，显示更长时间
        if (result.abnormalLogin && result.abnormalMessage && result.lastLoginInfo) {
          // 格式化时间
          let timeStr = '';
          try {
            timeStr = result.lastLoginInfo.time ? new Date(result.lastLoginInfo.time).toLocaleString() : '';
          } catch {
            timeStr = result.lastLoginInfo.time || '';
          }
          const message = `上次登录IP：${result.lastLoginInfo.ip || '-'}\n上次登录时间：${timeStr}`;
          onLoginSuccess?.({
            type: 'warning',
            message,
            duration: 10
          });
          setTimeout(() => {
            onClose();
          }, 10000);
        } else {
          onLoginSuccess?.({
            type: 'success',
            message: '登录成功'
          });
          // 正常登录3秒后关闭
          setTimeout(() => {
            onClose();
          }, 3000);
        }
      } else {
        // 显示错误信息
        if (result.isLocked) {
          setErrors({
            submit: `账户已被临时锁定，请${result.remainingMinutes}分钟后再试`
          });
        } else {
          setErrors({
            submit: result.message || result.error || '登录失败，请检查账号和密码'
          });
        }
        setIsLoading(false);
      }
    } catch (error) {
      let errorMessage = error instanceof Error ? error.message : '登录失败，请稍后重试';
      
      // 优化错误消息显示
      if (errorMessage.includes('400')) {
        // 请求错误
        errorMessage = '请求参数错误，请检查输入';
      } else if (errorMessage.includes('401') || errorMessage.includes('账号') || errorMessage.includes('密码')) {
        // 账号或密码错误
        errorMessage = '账号或密码错误';
      } else if (errorMessage.includes('JSON') || errorMessage.includes('格式错误')) {
        // JSON解析错误，这通常是内部错误
        errorMessage = '系统错误，请刷新页面后重试';
      } else if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
        // 超时错误
        errorMessage = '登录请求超时，请检查网络后重试';
      } else if (errorMessage.includes('network') || errorMessage.includes('网络')) {
        // 网络错误
        errorMessage = '网络连接错误，请检查网络后重试';
      }
      
      setErrors({
        submit: errorMessage
      });
      setIsLoading(false);
    }
  };
  
  // 开发者登录处理函数
  const handleDevLogin = () => {
    const devUser = {
      id: 'developer',
      role: 'admin'
    };
    
    // 生成一个临时token（包含当前时间戳，用于唯一性）
    const tempToken = 'dev_token_' + Date.now();
    
    // 将用户信息和token保存到localStorage
    localStorage.setItem('user', JSON.stringify(devUser));
    localStorage.setItem('token', tempToken);
    
    // 设置token过期时间（7天后过期）
    const expireDate = new Date();
    expireDate.setDate(expireDate.getDate() + 7);
    localStorage.setItem('tokenExpires', expireDate.toISOString());
    
    // 设置用户状态
    auth.setUser(devUser);
    onClose();
    
    // 如果有成功回调，调用它
    if (onLoginSuccess) {
      onLoginSuccess({
        type: 'success',
        message: '开发者登录成功'
      });
    }
  };
  
  return (
    <form className={`login-form ${className}`} onSubmit={handleSubmit}>
      <div className="form-group">
        <label>账号</label>
        <input
          type="text"
          name="loginId"
          value={form.loginId}
          onChange={handleChange}
          placeholder="请输入用户名或手机号"
        />
        {errors.loginId && (
          <span className="simple-error-message">{errors.loginId}</span>
        )}
      </div>
      
      <div className="form-group">
        <label>密码</label>
        <div className="password-input">
          <input
            type={passwordVisible ? "text" : "password"}
            name="password"
            placeholder="请输入密码"
            value={form.password}
            onChange={handleChange}
          />
          <button
            type="button"
            className={`toggle-password ${passwordVisible ? 'visible' : ''}`}
            onClick={() => setPasswordVisible(prev => !prev)}
          />
        </div>
        {errors.password && (
          <span className="simple-error-message">{errors.password}</span>
        )}
      </div>
      
      <div className="form-footer">
        <label className="remember-me" style={{ position: 'relative', paddingLeft: '24px', cursor: 'pointer' }}>
          <input
            type="checkbox"
            name="remember"
            checked={form.remember}
            onChange={handleChange}
            style={{ position: 'absolute', left: 0, top: '50%', transform: 'translateY(-50%)', zIndex: 1, opacity: 0, width: '16px', height: '16px', margin: 0, padding: 0 }}
          />
          <span className="custom-checkbox" style={{ position: 'absolute', left: 0, top: '50%', transform: 'translateY(-50%)', width: '16px', height: '16px', border: '1.5px solid var(--border-color)', borderRadius: '2px', background: 'var(--bg-primary)', display: 'flex', alignItems: 'center', justifyContent: 'center', pointerEvents: 'none' }}>
            {form.remember && (
              <span className="checkmark" style={{ color: '#FF6B6B', fontSize: '1rem', fontWeight: 'bold', fontFamily: 'inherit', lineHeight: 1, pointerEvents: 'none' }}>✓</span>
            )}
          </span>
          <span style={{ marginLeft: '5px', fontSize: '12px' }}>记住我</span>
        </label>
        <button 
          type="button"
          className="forgot-password-btn"
          onClick={onForgotPassword}
        >
          忘记密码
        </button>
      </div>
      
      {errors.submit && (
        <div className="submit-error">
          {errors.submit.includes('锁定') ? (
            <ErrorMessage
              type="locked"
              message="账户已被临时锁定"
              lockoutTime={parseInt(errors.submit.match(/\d+/)?.[0] || '15')}
              showIcon={false}
              onRetry={() => setErrors(prev => ({...prev, submit: undefined}))}
              className=""
              remainingAttempts={0}
              simple={true}
            />
          ) : errors.submit.includes('还剩') ? (
            <ErrorMessage
              type="auth"
              message="登录失败，请检查账号和密码"
              remainingAttempts={parseInt(errors.submit.match(/\d+/)?.[0] || '0')}
              showIcon={false}
              className=""
              simple={true}
              onRetry={() => setErrors(prev => ({...prev, submit: undefined}))}
              lockoutTime={0}
            />
          ) : errors.submit.includes('密码错误') || errors.submit.includes('账号或密码错误') ? (
            <ErrorMessage
              type="auth"
              message={errors.submit}
              showIcon={true}
              className=""
              simple={true}
              onRetry={() => {
                // 清除错误并聚焦密码输入框
                setErrors(prev => ({...prev, submit: undefined}));
                const passwordInput = document.querySelector('input[name="password"]');
                if (passwordInput) {
                  (passwordInput as HTMLInputElement).focus();
                  (passwordInput as HTMLInputElement).select();
                }
              }}
              remainingAttempts={0}
              lockoutTime={0}
            />
          ) : errors.submit.includes('网络') || errors.submit.includes('超时') ? (
            <ErrorMessage
              type="network"
              message={errors.submit}
              showIcon={true}
              className=""
              simple={true}
              onRetry={() => {
                setErrors(prev => ({...prev, submit: undefined}));
              }}
              remainingAttempts={0}
              lockoutTime={0}
            />
          ) : (
            <ErrorMessage
              type="auth"
              message={errors.submit}
              showIcon={false}
              onRetry={() => setErrors(prev => ({...prev, submit: undefined}))}
              className=""
              remainingAttempts={0}
              lockoutTime={0}
              simple={true}
            />
          )}
        </div>
      )}
      
      <button type="submit" className="submit-btn" disabled={isLoading}>
        <span>{isLoading ? '登录中...' : '登录'}</span>
      </button>
      
      {/*<button */}
      {/*  type="button" */}
      {/*  className="dev-login-btn"*/}
      {/*  onClick={handleDevLogin}*/}
      {/*>*/}
      {/*  开发者登录*/}
      {/*</button>*/}
    </form>
  );
};

export default LoginForm;
