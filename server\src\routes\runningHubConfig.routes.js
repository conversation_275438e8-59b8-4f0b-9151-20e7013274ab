/**
 * RunningHub配置路由（普通用户）
 * 提供普通用户查看和使用RunningHub配置的API
 */

const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth.middleware');
const RunningHubConfig = require('../models/RunningHubConfig');

/**
 * 获取用户可用的配置列表
 * GET /api/runninghub/configs
 */
router.get('/', auth, async (req, res) => {
  try {
    const userId = req.user._id;
    
    // 获取用户可见的所有配置
    const configs = await RunningHubConfig.getAvailableConfigs(userId, true);
    
    // 隐藏敏感信息（API密钥等）
    const safeConfigs = configs.map(config => ({
      _id: config._id,
      name: config.name,
      description: config.description,
      isDefault: config.isDefault,
      enabled: config.enabled,
      workflowMappings: Object.fromEntries(config.workflowMappings),
      usage: config.usage,
      testStatus: config.testStatus,
      lastTestedAt: config.lastTestedAt,
      createdBy: config.createdBy,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt
    }));
    
    res.json({
      success: true,
      data: safeConfigs
    });
  } catch (error) {
    console.error('获取可用配置列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取可用配置列表失败'
    });
  }
});

/**
 * 获取默认配置
 * GET /api/runninghub/configs/default
 */
router.get('/default', auth, async (req, res) => {
  try {
    const userId = req.user._id;
    
    const config = await RunningHubConfig.getDefaultConfig(userId);
    
    if (!config) {
      return res.status(404).json({
        success: false,
        message: '未找到默认配置'
      });
    }
    
    // 隐藏敏感信息
    const safeConfig = {
      _id: config._id,
      name: config.name,
      description: config.description,
      isDefault: config.isDefault,
      enabled: config.enabled,
      workflowMappings: Object.fromEntries(config.workflowMappings),
      usage: config.usage,
      testStatus: config.testStatus,
      lastTestedAt: config.lastTestedAt,
      createdBy: config.createdBy,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt
    };
    
    res.json({
      success: true,
      data: safeConfig
    });
  } catch (error) {
    console.error('获取默认配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取默认配置失败'
    });
  }
});

/**
 * 获取配置详情
 * GET /api/runninghub/configs/:id
 */
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;
    
    const config = await RunningHubConfig.findById(id);
    
    if (!config) {
      return res.status(404).json({
        success: false,
        message: '配置不存在'
      });
    }
    
    // 所有配置都是公共的，无需权限检查
    
    // 隐藏敏感信息
    const safeConfig = {
      _id: config._id,
      name: config.name,
      description: config.description,
      isDefault: config.isDefault,
      enabled: config.enabled,
      workflowMappings: Object.fromEntries(config.workflowMappings),
      usage: config.usage,
      testStatus: config.testStatus,
      lastTestedAt: config.lastTestedAt,
      createdBy: config.createdBy,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt
    };
    
    res.json({
      success: true,
      data: safeConfig
    });
  } catch (error) {
    console.error('获取配置详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取配置详情失败'
    });
  }
});

module.exports = router;
