const { createError } = require('../../../utils/error');
const User = require('../../auth/user.model');

const Plan = require('./plan.model');
const Subscription = require('./subscription.model');

/**
 * 获取所有订阅
 */
exports.getAllSubscriptions = async (req, res, next) => {
  try {
    console.log('订阅查询参数:', req.query); // 添加日志，帮助调试

    const { username, phone, plan, status, startDate, endDate, page = 1, limit = 20 } = req.query;

    // 构建查询条件
    const query = {};

    // 用户查询 - 支持用户名或手机号
    if (username || phone) {
      const userQuery = {};

      if (username) {
        userQuery.username = { $regex: username, $options: 'i' };
      }

      if (phone) {
        userQuery.phone = phone;
      }

      const users = await User.find(userQuery);
      if (users.length > 0) {
        query.user = { $in: users.map(u => u._id) };
      } else {
        // 如果找不到用户，返回空结果但保持正确的数据结构
        return res.json({
          total: 0,
          page: Number(page),
          limit: Number(limit),
          data: []
        });
      }
    }

    // 添加其他查询条件
    if (plan) query.plan = plan;
    if (status) query.status = status;

    // 日期范围查询 - 改进处理方式
    if (startDate) {
      query.startDate = query.startDate || {};
      query.startDate.$gte = new Date(startDate);
    }

    if (endDate) {
      query.endDate = query.endDate || {};
      query.endDate.$lte = new Date(endDate);
    }

    console.log('最终查询条件:', JSON.stringify(query)); // 添加日志，帮助调试

    // 计算总数
    const total = await Subscription.countDocuments(query);

    // 查询订阅并填充用户信息
    const subscriptions = await Subscription.find(query)
      .populate('user', 'username name email phone remark')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(Number(limit));

    // 自动更新每个订阅的状态
    for (const subscription of subscriptions) {
      const actualStatus = await Subscription.updateSubscriptionStatus(subscription._id);
      if (actualStatus && actualStatus !== subscription.status) {
        console.log(`订阅 ${subscription._id} 状态已从 ${subscription.status} 自动更新为 ${actualStatus}`);
        // 更新内存中的状态，确保返回的数据是最新的
        subscription.status = actualStatus;
      }
    }

    // 返回分页数据
    res.json({
      total,
      page: Number(page),
      limit: Number(limit),
      data: subscriptions
    });
  } catch (error) {
    console.error('获取订阅列表错误:', error);
    next(createError(500, '获取订阅列表失败', error));
  }
};

/**
 * 获取单个订阅
 */
exports.getSubscriptionById = async (req, res, next) => {
  try {
    let subscription = await Subscription.findById(req.params.id)
      .populate('user', 'username name email phone');

    if (!subscription) {
      return next(createError(404, '订阅不存在'));
    }

    // 自动更新订阅状态
    const actualStatus = await Subscription.updateSubscriptionStatus(subscription._id);
    if (actualStatus && actualStatus !== subscription.status) {
      console.log(`订阅 ${subscription._id} 状态已从 ${subscription.status} 自动更新为 ${actualStatus}`);
      // 重新获取更新后的订阅
      subscription = await Subscription.findById(req.params.id)
        .populate('user', 'username name email phone');
    }

    res.json(subscription);
  } catch (error) {
    next(createError(500, '获取订阅详情失败', error));
  }
};

/**
 * 获取用户当前订阅
 */
exports.getUserSubscription = async (req, res, next) => {
  try {
    const userId = req.params.userId || req.user._id;
    console.log('获取用户订阅信息，用户ID:', userId);

    // 查找用户的最新订阅
    let subscription = await Subscription.findOne({
      user: userId
    }).sort({ createdAt: -1 });

    // 如果找到订阅，自动更新状态
    if (subscription) {
      const actualStatus = await Subscription.updateSubscriptionStatus(subscription._id);
      if (actualStatus) {
        // 重新获取更新后的订阅
        subscription = await Subscription.findById(subscription._id);
        console.log(`订阅状态已自动更新为: ${actualStatus}`);
      }
    }

    console.log('订阅查询结果:', subscription ? {
      plan: subscription.plan,
      status: subscription.status,
      endDate: subscription.endDate
    } : '无任何订阅');

    if (!subscription) {
      // 获取免费计划信息
      const freePlan = await Plan.findOne({ code: 'free' });
      console.log('返回免费版订阅信息');
      return res.json({
        hasActiveSubscription: false,
        plan: 'free',
        planName: freePlan ? freePlan.name : '免费版',
        features: {
          design: { enabled: false },
          model: { enabled: false },
          tools: { enabled: true }
        }
      });
    }

    // 获取订阅计划的详细信息
    const planDoc = await Plan.findOne({ code: subscription.plan });

    const result = {
      hasActiveSubscription: subscription.status === 'active' && subscription.endDate > new Date(),
      ...subscription.toObject(),
      planName: planDoc ? planDoc.name : subscription.plan
    };

    console.log('返回订阅信息:', {
      plan: result.plan,
      planName: result.planName,
      status: result.status,
      endDate: result.endDate,
      hasActiveSubscription: result.hasActiveSubscription
    });

    res.json(result);
  } catch (error) {
    console.error('获取用户订阅失败:', error);
    next(createError(500, '获取用户订阅失败', error));
  }
};

/**
 * 创建订阅
 */
exports.createSubscription = async (req, res, next) => {
  try {
    console.log('接收到的订阅数据:', req.body);  // 添加日志，帮助调试

    const { userId, plan, status, startDate, endDate, price, autoRenew, paymentMethod, paymentId } = req.body;

    // 验证必要字段
    if (!userId || !plan) {
      return next(createError(400, '用户ID和计划代码为必填项'));
    }

    // 检查用户是否存在
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, '用户不存在'));
    }

    // 检查计划是否存在
    const planDoc = await Plan.findOne({ code: plan });
    if (!planDoc) {
      return next(createError(404, '订阅计划不存在'));
    }

    // 检查用户是否已有活跃订阅
    const existingSubscription = await Subscription.findOne({
      user: userId,
      status: 'active',
      endDate: { $gt: new Date() }
    });

    if (existingSubscription) {
      return next(createError(400, '用户已有活跃订阅，请先取消现有订阅'));
    }

    // 创建新订阅
    const newSubscription = new Subscription({
      user: userId,
      plan,
      status: status || 'pending',
      startDate: startDate ? new Date(startDate) : new Date(),
      endDate: endDate ? new Date(endDate) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),  // 默认30天
      price: price || planDoc.price.monthly,
      autoRenew: autoRenew || false,
      paymentMethod: paymentMethod || 'manual',
      paymentId: paymentId || '',
      features: planDoc.features,
      usageQuota: {
        totalRequests: planDoc.usageQuota.totalRequests,
        remainingRequests: planDoc.usageQuota.totalRequests,
        dailyRequests: planDoc.usageQuota.dailyRequests
      },
      createdAt: new Date()
    });

    await newSubscription.save();

    // 返回创建的订阅
    const populatedSubscription = await Subscription.findById(newSubscription._id)
      .populate('user', 'username name email');

    res.status(201).json({
      message: '订阅创建成功',
      subscription: populatedSubscription
    });
  } catch (error) {
    console.error('创建订阅错误:', error);  // 添加详细错误日志
    next(createError(500, '创建订阅失败', error));
  }
};

/**
 * 更新订阅
 */
exports.updateSubscription = async (req, res, next) => {
  try {
    const {
      plan,
      status,
      startDate,
      endDate,
      autoRenew,
      price,
      paymentMethod,
      paymentId,
      customFeatures,
      usageQuota,
      features
    } = req.body;

    const subscription = await Subscription.findById(req.params.id);
    if (!subscription) {
      return next(createError(404, '订阅不存在'));
    }

    // 更新基本信息
    if (plan) subscription.plan = plan;
    if (status) subscription.status = status;
    if (startDate) subscription.startDate = new Date(startDate);
    if (endDate) subscription.endDate = new Date(endDate);
    if (autoRenew !== undefined) subscription.autoRenew = autoRenew;
    if (price) subscription.price = price;
    if (paymentMethod) subscription.paymentMethod = paymentMethod;
    if (paymentId) subscription.paymentId = paymentId;

    // 更新自定义功能
    if (customFeatures && Array.isArray(customFeatures)) {
      subscription.customFeatures = customFeatures;
    }

    // 更新使用配额
    if (usageQuota) {
      subscription.usageQuota = {
        ...subscription.usageQuota,
        ...usageQuota
      };
    }

    // 更新功能设置
    if (features) {
      // 深度合并功能设置
      subscription.features = {
        ...subscription.features,
        ...features,
        design: {
          ...subscription.features.design,
          ...(features.design || {})
        },
        model: {
          ...subscription.features.model,
          ...(features.model || {})
        },
        tools: {
          ...subscription.features.tools,
          ...(features.tools || {})
        },
        support: {
          ...subscription.features.support,
          ...(features.support || {})
        }
      };
    }

    await subscription.save();

    // 如果状态或计划发生变化，更新用户信息
    if (status === 'active' || plan) {
      await User.findByIdAndUpdate(subscription.user, {        'subscription.type': subscription.plan,
        'subscription.endDate': subscription.endDate,
        'subscription.autoRenew': subscription.autoRenew,
        'subscription.paymentMethod': subscription.paymentMethod
      });
    }

    res.json({
      message: '订阅更新成功',
      subscription
    });
  } catch (error) {
    console.error('更新订阅错误:', error);
    next(createError(500, '更新订阅失败', error));
  }
};

/**
 * 取消订阅
 */
exports.cancelSubscription = async (req, res, next) => {
  try {
    const { reason } = req.body;

    const subscription = await Subscription.findById(req.params.id);
    if (!subscription) {
      return next(createError(404, '订阅不存在'));
    }

    // 更新订阅状态
    subscription.status = 'canceled';
    subscription.metadata = subscription.metadata || new Map();
    subscription.metadata.set('cancelReason', reason || '用户取消');
    subscription.metadata.set('canceledAt', new Date());

    await subscription.save();

    // 更新用户信息
    await User.findByIdAndUpdate(subscription.user, {
      $push: {
        subscriptionHistory: {
          plan: subscription.plan,
          startDate: subscription.startDate,
          endDate: new Date(),
          status: 'canceled',
          reason: reason || '用户取消',
          canceledAt: new Date()
        }
      }
    });

    res.json({
      message: '订阅已取消',
      subscription
    });
  } catch (error) {
    next(createError(500, '取消订阅失败', error));
  }
};

/**
 * 删除订阅
 */
exports.deleteSubscription = async (req, res, next) => {
  try {
    const subscription = await Subscription.findById(req.params.id);
    if (!subscription) {
      return next(createError(404, '订阅不存在'));
    }

    await Subscription.findByIdAndDelete(req.params.id);

    res.json({
      message: '订阅删除成功'
    });
  } catch (error) {
    next(createError(500, '删除订阅失败', error));
  }
};

/**
 * 检查功能权限
 */
exports.checkFeatureAccess = async (req, res, next) => {
  try {
    const { userId, feature, subFeature } = req.body;

    // 查找用户的订阅（不限制状态，因为我们要自动更新）
    let subscription = await Subscription.findOne({
      user: userId
    }).sort({ createdAt: -1 });

    // 如果找到订阅，自动更新状态
    if (subscription) {
      const actualStatus = await Subscription.updateSubscriptionStatus(subscription._id);
      if (actualStatus) {
        // 重新获取更新后的订阅
        subscription = await Subscription.findById(subscription._id);
      }
    }

    // 检查是否有有效的活跃订阅
    const hasValidSubscription = subscription &&
      subscription.status === 'active' &&
      subscription.endDate > new Date();

    // 如果没有有效订阅，只允许访问基础工具
    if (!hasValidSubscription) {
      const hasAccess = feature === 'tools' && ['upscale', 'matting', 'extend'].includes(subFeature);
      return res.json({
        hasAccess,
        plan: 'free'
      });
    }

    // 检查是否有权限访问请求的功能
    let hasAccess = false;

    if (feature && subscription.features[feature]) {
      if (subscription.features[feature].enabled) {
        if (!subFeature) {
          hasAccess = true;
        } else if (subscription.features[feature][subFeature]) {
          hasAccess = subscription.features[feature][subFeature];
        }
      }
    }

    // 检查自定义功能
    if (!hasAccess && subscription.customFeatures && subscription.customFeatures.length > 0) {
      const customFeature = subscription.customFeatures.find(
        cf => cf.name === `${feature}.${subFeature}` && cf.enabled
      );
      if (customFeature) {
        hasAccess = true;
      }
    }

    // 检查使用配额
    if (hasAccess && subscription.usageQuota.totalRequests !== -1) {
      if (subscription.usageQuota.remainingRequests <= 0) {
        hasAccess = false;
      } else {
        // 减少剩余请求次数
        subscription.usageQuota.remainingRequests -= 1;
        await subscription.save();
      }
    }

    res.json({
      hasAccess,
      plan: subscription.plan,
      subscription: subscription._id
    });
  } catch (error) {
    next(createError(500, '检查功能权限失败', error));
  }
};

/**
 * 获取订阅统计信息
 */
exports.getSubscriptionStats = async (req, res, next) => {
  try {
    // 获取活跃订阅数量
    const activeCount = await Subscription.countDocuments({ status: 'active' });

    // 按计划统计
    const planStats = await Subscription.aggregate([
      { $match: { status: 'active' } },
      { $group: { _id: '$plan', count: { $sum: 1 } } }
    ]);

    // 本月新增订阅
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const newThisMonth = await Subscription.countDocuments({
      createdAt: { $gte: startOfMonth }
    });

    // 即将到期的订阅
    const thirtyDaysLater = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    const expiringCount = await Subscription.countDocuments({
      status: 'active',
      endDate: { $gte: now, $lte: thirtyDaysLater }
    });

    // 总收入
    const revenue = await Subscription.aggregate([
      { $group: { _id: null, total: { $sum: '$price' } } }
    ]);

    res.json({
      activeSubscriptions: activeCount,
      byPlan: planStats.reduce((acc, curr) => {
        acc[curr._id] = curr.count;
        return acc;
      }, {}),
      newThisMonth,
      expiringCount,
      totalRevenue: revenue.length > 0 ? revenue[0].total : 0
    });
  } catch (error) {
    next(createError(500, '获取订阅统计失败', error));
  }
};

/**
 * 批量更新过期订阅状态
 */
exports.batchUpdateExpiredSubscriptions = async (req, res, next) => {
  try {
    const updatedCount = await Subscription.updateExpiredSubscriptions();

    res.json({
      message: `成功更新了 ${updatedCount} 个过期订阅`,
      updatedCount
    });
  } catch (error) {
    next(createError(500, '批量更新过期订阅失败', error));
  }
};