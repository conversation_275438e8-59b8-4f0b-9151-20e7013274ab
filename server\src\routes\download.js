const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const archiver = require('archiver');
const { auth } = require('../modules/auth');
const Task = require('../models/Task');

// 批量下载路由，添加auth中间件进行验证
router.get('/batch', auth, async (req, res) => {
  try {
    console.log('开始处理批量下载请求');
    const { taskId, urls } = req.query;
    const userId = req.user._id.toString();
    console.log(`用户ID: ${userId}, 任务ID: ${taskId}`);
    
    if (!taskId || !urls) {
      console.log('缺少必要参数:', { taskId, urls });
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 验证任务归属权 - 确保任务属于当前用户
    const task = await Task.findOne({ taskId, userId });
    console.log('查询任务结果:', task ? '任务存在' : '任务不存在');
    if (!task && userId !== 'developer') {
      console.log('权限检查失败: 用户无权访问该任务');
      return res.status(403).json({
        success: false,
        message: '无权访问该任务数据'
      });
    }

    const imageUrls = JSON.parse(decodeURIComponent(urls));
    console.log(`解析图片URL列表, 包含 ${imageUrls.length} 个URL:`, imageUrls);
    if (!Array.isArray(imageUrls) || imageUrls.length === 0) {
      console.log('无效的图片URL列表');
      return res.status(400).json({
        success: false,
        message: '无效的图片URL列表'
      });
    }

    // 确定文件的保存目录 - 使用用户特定目录
    const userTempDir = path.resolve(__dirname, `../../storage/${userId}/temp`);
    console.log('用户临时目录路径:', userTempDir);
    if (!fs.existsSync(userTempDir)) {
      console.log('临时目录不存在，创建目录');
      fs.mkdirSync(userTempDir, { recursive: true });
    }

    // 创建zip文件
    const zipFileName = `${taskId}_images.zip`;
    const zipFilePath = path.join(userTempDir, zipFileName);
    console.log('创建zip文件路径:', zipFilePath);
    const output = fs.createWriteStream(zipFilePath);
    const archive = archiver('zip', {
      zlib: { level: 9 } // 设置压缩级别
    });

    // 监听压缩完成事件
    output.on('close', () => {
      console.log('压缩完成，开始发送文件');
      // 发送zip文件
      res.download(zipFilePath, zipFileName, (err) => {
        if (err) {
          console.error('下载zip文件失败:', err);
        } else {
          console.log('文件发送成功');
        }
        // 删除临时zip文件
        fs.unlink(zipFilePath, (unlinkErr) => {
          if (unlinkErr) {
            console.error('删除临时zip文件失败:', unlinkErr);
          } else {
            console.log('临时文件已删除');
          }
        });
      });
    });

    output.on('end', () => {
      console.log('数据已经全部写入到输出流');
    });

    archive.on('error', (err) => {
      console.error('创建zip文件失败:', err);
      res.status(500).json({
        success: false,
        message: '创建压缩包失败'
      });
    });

    archive.on('warning', (err) => {
      console.warn('压缩警告:', err);
    });

    console.log('连接archive到output');
    archive.pipe(output);

    // 公共静态资源的根目录 - 前端访问的/images对应后端的public/images
    const publicImagesDir = path.resolve(__dirname, '../../../public');
    
    // 添加图片到zip
    console.log('开始添加图片到压缩包');
    let addedFiles = 0;
    for (let i = 0; i < imageUrls.length; i++) {
      const imageUrl = imageUrls[i];
      const imageName = path.basename(imageUrl);
      
      // 构造文件的完整路径 - 去掉URL开头的斜杠，因为publicImagesDir已包含了根路径
      const urlPath = imageUrl.startsWith('/') ? imageUrl.substring(1) : imageUrl;
      const imagePath = path.join(publicImagesDir, urlPath);
      
      console.log(`检查图片[${i+1}]: ${imagePath}`);
      
      if (fs.existsSync(imagePath)) {
        console.log(`添加文件: ${imagePath} 为 ${taskId}_${i + 1}${path.extname(imageName)}`);
        archive.file(imagePath, { name: `${taskId}_${i + 1}${path.extname(imageName)}` });
        addedFiles++;
      } else {
        console.warn(`图片不存在于public目录: ${imagePath}`);
        
        // 尝试在storage中查找 - 处理通过API上传的文件
        // 这里根据URL格式尝试将其映射到用户存储目录
        let storagePath = null;
        
        if (imageUrl.startsWith('/model/')) {
          // 来自模型目录的图片 /model/try-on/generated/xxx.jpg
          const pathParts = imageUrl.split('/').filter(Boolean);
          if (pathParts.length >= 3) {
            storagePath = path.resolve(
              __dirname, 
              `../../storage/${userId}/model/${pathParts[1]}/${pathParts[2]}`, 
              imageName
            );
          }
        } else if (imageUrl.startsWith('/uploads/')) {
          // 来自uploads目录的图片
          storagePath = path.resolve(__dirname, `../../storage/${userId}/uploads`, imageName);
        }
        
        if (storagePath && fs.existsSync(storagePath)) {
          console.log(`在storage目录找到文件: ${storagePath}`);
          archive.file(storagePath, { name: `${taskId}_${i + 1}${path.extname(imageName)}` });
          addedFiles++;
        } else if (storagePath) {
          console.warn(`在storage目录中也未找到: ${storagePath}`);
        }
      }
    }
    
    console.log(`共添加了 ${addedFiles} 个文件到压缩包`);
    if (addedFiles === 0) {
      console.log('没有找到任何需要压缩的文件');
      return res.status(404).json({
        success: false,
        message: '未找到需要下载的图片'
      });
    }

    console.log('完成所有文件添加，开始生成压缩包');
    archive.finalize();
    console.log('已调用archive.finalize()');

  } catch (error) {
    console.error('批量下载处理失败:', error);
    res.status(500).json({
      success: false,
      message: '批量下载处理失败: ' + error.message
    });
  }
});

module.exports = router; 