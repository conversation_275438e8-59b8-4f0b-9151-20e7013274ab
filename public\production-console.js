// 生产环境配置 - 禁用所有调试输出
// 只在生产环境下启用
if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
  
  // 定义要保留的关键错误信息
  const keepErrorKeywords = [
    '创建任务失败',
    '删除任务失败',
    '下载出错',
    'API删除任务可能失败',
    '上传失败',
    '网络连接失败',
    'API调用失败',
    '认证失败',
    '权限不足',
    '服务器错误',
    '系统错误'
  ];

  // 定义要清理的日志模式
  const cleanPatterns = [
    /react.*devtools/i,
    /react refresh/i,
    /NODE_ENV/i,
    /PUBLIC_URL/i,
    /用户ID/i,
    /创建请求/i,
    /渲染时的/i,
    /ImageDetailsModal/i,
    /TaskPanel/i,
    /=== 任务数据修复日志 ===/i,
    /任务状态改变/i,
    /初始化tasks/i,
    /图片.*加载成功/i,
    /正在获取图片/i,
    /获取到.*图片信息/i,
    /WebSocket.*连接/i,
    /开始建立WebSocket/i,
    /DEBUG/i,
    /Available scales/i
  ];

  // 检查是否应该保留错误信息
  function shouldKeepError(message) {
    if (!message || typeof message !== 'string') return false;
    
    // 检查关键词
    if (keepErrorKeywords.some(keyword => message.includes(keyword))) {
      return true;
    }
    
    // 检查网络错误
    if (message.includes('ERR_ABORTED') || message.includes('net::ERR_')) {
      return true;
    }
    
    return false;
  }

  // 检查是否应该清理日志
  function shouldCleanLog(message) {
    if (!message || typeof message !== 'string') return false;
    
    return cleanPatterns.some(pattern => pattern.test(message));
  }

  // 重写console方法
  const originalConsole = { ...console };
  
  window.console = {
    ...console,
    
    // 完全禁用log
    log: (...args) => {
      const message = args[0];
      if (shouldKeepError(message)) {
        originalConsole.log(...args);
      }
    },
    
    // 完全禁用info
    info: () => {},
    
    // 完全禁用debug
    debug: () => {},
    
    // 禁用warn（除非是关键错误）
    warn: (...args) => {
      const message = args[0];
      if (shouldKeepError(message)) {
        originalConsole.warn(...args);
      }
    },
    
    // 保留关键错误
    error: (...args) => {
      const message = args[0];
      if (shouldKeepError(message)) {
        originalConsole.error(...args);
      }
    }
  };

  // 禁用调试工具
  Object.defineProperty(window, 'console', {
    writable: false,
    configurable: false
  });
}
