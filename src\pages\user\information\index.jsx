import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { updateUserProfile } from '../../../api/auth';
import './index.css';
import logger from '../../../utils/logger';

const InformationPage = () => {
  const { user, setUser } = useAuth();
  const [formData, setFormData] = useState({
    username: '',
    phone: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // 当用户信息变化时更新表单数据
  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || '',
        phone: user.phone || ''
      });
      logger.debug('从 AuthContext 获取到用户信息');
      return;
    }
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const localUser = JSON.parse(userStr);
        setFormData({
          username: localUser.username || '',
          phone: localUser.phone || ''
        });
        logger.debug('从 localStorage 获取到用户信息');
      } else {
        logger.debug('localStorage 中没有用户信息');
      }
    } catch (err) {
      logger.error('解析本地用户数据失败', err);
    }
  }, [user]);

  // 验证用户名函数
  const validateUsername = (username) => {
    // 检查长度
    if (username.length < 4 || username.length > 12) {
      return '用户名必须是4-12位';
    }

    // 检查是否为纯数字
    if (/^\d+$/.test(username)) {
      return '用户名不能为纯数字';
    }

    // 检查字符类型
    if (!/^[\u4e00-\u9fa5a-zA-Z0-9]*$/.test(username)) {
      return '用户名只能包含中文、英文或数字';
    }

    return '';
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // 清除错误提示
    if (name === 'username') {
      setError('');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;

    // 提交前验证用户名
    const usernameError = validateUsername(formData.username);
    if (usernameError) {
      setError(usernameError);
      return;
    }

    try {
      setIsSubmitting(true);
      // 调用API更新用户资料
      const response = await updateUserProfile({ username: formData.username });

      // 更新本地用户信息
      const updatedUser = {
        ...user,
        username: formData.username
      };
      setUser(updatedUser);
    } catch (error) {
      console.error('更新帐户信息失败:', error);
      setError(error.message || '更新失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理修改密码
  const handleChangePassword = () => {
    // 触发全局的修改密码弹窗
    window.dispatchEvent(new CustomEvent('showResetPassword'));
  };

  // 手机号脱敏函数
  const maskPhone = (phone) => {
    if (!phone) return '';
    return phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
  };

  return (
    <div className="profile-page overflow-auto">
      <div className="profile-container">
        <div className="profile-content-area">
          <div className="content-header">
            <h2>账号信息</h2>
          </div>

          <div className="content-body">
            <form className="profile-form">
              {/* 手机号在上，纯文本显示且脱敏 */}
              <div className="form-group">
                <label>手机号</label>
                <div className="phone-text">{maskPhone(formData.phone)}</div>
              </div>

              {/* 用户名只读显示 */}
              <div className="form-group">
                <label>用户名</label>
                <div className="phone-text">{formData.username}</div>
                <div className="login-tip">* 用户名和手机号均可作为登录账号使用</div>
              </div>

              {/* 修改密码按钮保留 */}
              <div className="form-group">
                <label>密码</label>
                <div className="input-with-button">
                  <button
                    type="button"
                    className="edit-button"
                    onClick={handleChangePassword}
                  >
                    修改密码
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InformationPage;