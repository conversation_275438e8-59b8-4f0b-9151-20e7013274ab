import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { MdOutlineHelpOutline } from 'react-icons/md';
import './index.css';
import TipPopup from '../TipPopup';

const MaskExpander = ({
  expandValue = 50,
  onChange
}) => {
  const [localExpandValue, setLocalExpandValue] = useState(expandValue);
  
  // 提示弹窗相关状态
  const [isTipVisible, setIsTipVisible] = useState(false);
  const [tipPosition, setTipPosition] = useState({ left: 0, top: 0 });
  const tipButtonRef = useRef(null);
  
  // 当外部值变化时，更新本地状态
  useEffect(() => {
    setLocalExpandValue(expandValue);
  }, [expandValue]);

  // 当滑块变化时
  const handleExpandChange = (e) => {
    const newValue = parseInt(e.target.value, 10);
    setLocalExpandValue(newValue);
    onChange && onChange(newValue);
  };

  // 计算填充宽度的百分比
  const calculateFillWidth = (value) => {
    return (value / 100) * 100;
  };

  // 处理提示按钮点击
  const handleShowTip = () => {
    if (tipButtonRef.current) {
      const rect = tipButtonRef.current.getBoundingClientRect();
      setTipPosition({
        left: rect.left + rect.width + 28,
        top: rect.top - 8
      });
    }
    setIsTipVisible(true);
  };

  // 处理关闭提示
  const handleCloseTip = () => {
    setIsTipVisible(false);
  };

  return (
    <>
      <div className="mask-expander-setting">
        <div className="weight-content">
          <div className="weight-label">
            <span>蒙版扩张</span>
            <span className="weight-values">
              <span className="colored-value">{localExpandValue}px</span>
            </span>
          </div>
          <div className="weight-slider-container">
            <div className="weight-sliders">
              <div className="weight-slider-row">
                <div className="weight-item-label">扩张值</div>
                <div className="slider-container">
                  <div className="slider-track">
                    <div 
                      className="slider-fill"
                      style={{ width: `${calculateFillWidth(localExpandValue)}%` }}
                    ></div>
                    <input 
                      type="range" 
                      min={1}
                      max={100}
                      step={1}
                      value={localExpandValue}
                      onChange={handleExpandChange}
                      className="slider-input"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 提示按钮 */}
        <button 
          className="tip-button-common text-description-tip-button"
          onClick={handleShowTip}
          ref={tipButtonRef}
          title="查看使用提示"
        >
          <span className="tip-text">点我</span>
          <MdOutlineHelpOutline />
        </button>
      </div>
      
      {/* 提示弹窗 */}
      <TipPopup 
        type="mask-expander"
        position={tipPosition}
        isVisible={isTipVisible}
        onClose={handleCloseTip}
        content={[
          "• 蒙版扩张用于扩大自动生成的蒙版区域",
          "• 数值越大，蒙版区域扩张越多，覆盖范围更广，但不是越大越好",
          "• 建议值：20-50px，建议根据服装款式调整",
        ].join('\n')}
      />
    </>
  );
};

MaskExpander.propTypes = {
  expandValue: PropTypes.number,
  onChange: PropTypes.func
};

export default MaskExpander; 