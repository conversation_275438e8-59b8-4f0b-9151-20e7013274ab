/* 导入统一面板样式 */
@import '../../styles/panels.css';

/* 面板组件基础样式 */
.panel-component {
  margin-bottom: 10px;
}

/* 扩图组件专用的文字区域样式，替代图标区域 */
.extend-size-icon-text {
  width: 88px;
  height: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
  flex-shrink: 0;
  box-sizing: border-box;
}

/* 信息按钮样式 */
.info-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 0;
}

.info-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 信息图标样式 */
.info-icon {
  width: 16px;
  height: 16px;
  color: #666;
  opacity: 0.7;
}

.info-btn:hover .info-icon {
  opacity: 1;
  color: #1890ff;
}

.extend-size-icon {
  display: none;
} 