/**
 * RunningHub配置API（普通用户）
 * 提供普通用户查看和使用RunningHub配置的API接口
 */

import api from './index';

/**
 * RunningHub配置API类
 */
class RunningHubConfigAPI {
  constructor() {
    this.baseURL = '/api/runninghub/configs';
  }

  /**
   * 获取用户可用的配置列表
   * @returns {Promise<Object>} 配置列表
   */
  async getAvailableConfigs() {
    try {
      const response = await api.get(this.baseURL);
      return {
        success: true,
        data: response.data || []
      };
    } catch (error) {
      console.error('获取可用配置列表失败:', error);
      throw new Error(error.message || '获取可用配置列表失败');
    }
  }

  /**
   * 获取默认配置
   * @returns {Promise<Object>} 默认配置
   */
  async getDefaultConfig() {
    try {
      const response = await api.get(`${this.baseURL}/default`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取默认配置失败:', error);
      throw new Error(error.message || '获取默认配置失败');
    }
  }

  /**
   * 获取配置详情
   * @param {string} configId - 配置ID
   * @returns {Promise<Object>} 配置详情
   */
  async getConfigDetail(configId) {
    try {
      const response = await api.get(`${this.baseURL}/${configId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取配置详情失败:', error);
      throw new Error(error.message || '获取配置详情失败');
    }
  }

  /**
   * 检查配置是否支持指定工作流
   * @param {string} configId - 配置ID
   * @param {string} workflowName - 工作流名称
   * @returns {Promise<Object>} 检查结果
   */
  async checkWorkflowSupport(configId, workflowName) {
    try {
      const config = await this.getConfigDetail(configId);
      const workflowMappings = config.data.workflowMappings || {};
      const isSupported = workflowMappings.hasOwnProperty(workflowName);
      
      return {
        success: true,
        data: {
          supported: isSupported,
          workflowId: isSupported ? workflowMappings[workflowName] : null
        }
      };
    } catch (error) {
      console.error('检查工作流支持失败:', error);
      throw new Error(error.message || '检查工作流支持失败');
    }
  }

  /**
   * 获取支持指定工作流的配置列表
   * @param {string} workflowName - 工作流名称
   * @returns {Promise<Object>} 支持的配置列表
   */
  async getConfigsForWorkflow(workflowName) {
    try {
      const response = await this.getAvailableConfigs();
      const supportedConfigs = response.data.filter(config => {
        const workflowMappings = config.workflowMappings || {};
        return workflowMappings.hasOwnProperty(workflowName);
      });

      return {
        success: true,
        data: supportedConfigs
      };
    } catch (error) {
      console.error('获取工作流支持的配置失败:', error);
      throw new Error(error.message || '获取工作流支持的配置失败');
    }
  }

  /**
   * 获取配置的使用统计
   * @param {string} configId - 配置ID
   * @returns {Promise<Object>} 使用统计
   */
  async getConfigUsageStats(configId) {
    try {
      const config = await this.getConfigDetail(configId);
      return {
        success: true,
        data: config.data.usage || {
          totalTasks: 0,
          successTasks: 0,
          failedTasks: 0,
          lastUsed: null
        }
      };
    } catch (error) {
      console.error('获取配置使用统计失败:', error);
      throw new Error(error.message || '获取配置使用统计失败');
    }
  }
}

// 创建API实例
const runningHubConfigAPI = new RunningHubConfigAPI();

// 导出API方法
export const {
  getAvailableConfigs,
  getDefaultConfig,
  getConfigDetail,
  checkWorkflowSupport,
  getConfigsForWorkflow,
  getConfigUsageStats
} = runningHubConfigAPI;

export default runningHubConfigAPI;
