import React, { useRef, useEffect, useState, useCallback, Suspense, memo } from 'react';

import { processMask } from '../../../api/pyApi';
import './index.css';
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDescription } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { Modal, Button, Spin, message, Input, Tabs } from 'antd';
import 'antd/dist/reset.css';
import { filterShowcaseByTag } from '../../../config/showcase/showcase';
import { UPLOAD_CONFIG } from '../../../config/uploads/upload';
import { getModelImagePath } from '../../../data/models';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import MaskDrawModal from '../../../components/MaskDrawModal';
import UploadBox_Model from '../../../components/UploadBox_Model';
import ModelMaskPanel from '../../../components/ModelMaskPanel';
import QuantityPanel from '../../../components/QuantityPanel';
import TipsPanel from '../../../components/TipsPanel';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import GenerationArea from '../../../components/GenerationArea';
import TextDescriptionPanel from '../../../components/TextDescriptionPanel';
import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask,checkUserBalance } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import { executeFlow } from '../../../api/flow';
import { getCurrentUserId } from '../../../api';
import {
  getTasks,
  getTaskById,
  deleteTask,
  createTask,
  filterTasksByUser
} from '../../../api/task';
import { uploadImage } from '../../../api/upload';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import JSZip from 'jszip';
import RandomSeedSelector from '../../../components/RandomSeedSelector';
import { getFakeTasksForUser } from '../../../api/task';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import RequireLogin from '../../../components/RequireLogin';
import { getTaskComponent } from '../../../utils/taskAdapters';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import { useTaskContext } from '../../../contexts/TaskContext';
import TypeSelector from '../../../components/TypeSelector';
import WeightPanel from '../../../components/WeightPanel';

const UPLOAD_BOX_WIDTH = 172;
const UPLOAD_BOX_HEIGHT = 172;
const MODEL_UPLOAD_BOX_WIDTH = 258;
const MODEL_UPLOAD_BOX_HEIGHT = 344;
const DIVERGENT_TIP = "请上传爆款款式图片，系统将为您生成延伸款式。";
const MASK_SAVED_TIP = "请确保蒙版区域覆盖你想要延伸的区域。";

const DivergentPage = ({ isLoggedIn, userId }) => {
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [processedImages, setProcessedImages] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [modelPanels, setModelPanels] = useState([]);
  const [currentReuploadModelPanelId, setCurrentReuploadModelPanelId] = useState(null);
  const [showModelUploadGuide, setShowModelUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [imageQuantity, setImageQuantity] = useState(4);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);

  const generationAreaRef = useRef(null);

  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });

  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);

  // 添加状态控制提示组件的显示和提示内容
  const [showTips, setShowTips] = useState(false);
  const [tipContent, setTipContent] = useState(DIVERGENT_TIP);

  // 添加文本描述状态
  const [textDescription, setTextDescription] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 添加加载状态
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);

  // 添加随机种子相关状态
  const [useRandomSeed, setUseRandomSeed] = useState(true);  // 是否使用随机种子
  const [seed, setSeed] = useState(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));  // 种子值

  // 添加权重调节状态 - item1固定为0.5（隐藏），item2为用户可调节的变化参数
  const [weights, setWeights] = useState({ item1: 0.5, item2: 0.75 });

  // 在款式图上传成功后更新默认尺寸
  useEffect(() => {
    if (modelPanels.length > 0 && modelPanels[0].fileInfo) {
      // 移除与图片尺寸相关的代码
    }
  }, [modelPanels]);

  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);

  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 处理款式图上传结果
  const handleModelUploadResult = (results) => {
    try {
      setHasUnsavedChanges(true);
      console.log('处理款式图上传结果:', JSON.stringify(results, null, 2));

      if (results.type === 'panels') {
        let newPanel;

        if (currentReuploadModelPanelId) {
          // 如果是重新上传，替换原有面板，但使用新生成的ID
          const newComponentId = generateId(ID_TYPES.COMPONENT);
          setModelPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === currentReuploadModelPanelId
                ? {
                    ...results.panels[0],
                    componentId: newComponentId,
                    type: 'clothing',
                    source: 'upload', // 设置来源为用户上传，以便后续处理
                    file: results.panels[0].file // 保存原始文件对象，供后续上传使用
                  }
                : panel
            )
          );
          // 重置当前重新上传的面板ID
          setCurrentReuploadModelPanelId(null);
          // 获取新的面板引用，使用新生成的ID
          newPanel = {
            ...results.panels[0],
            componentId: newComponentId,
            type: 'clothing',
            source: 'upload', // 设置来源为用户上传
            file: results.panels[0].file // 保存原始文件对象
          };
        } else {
          // 如果是新上传，为每个面板添加新的componentId和source标记
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            componentId: generateId(ID_TYPES.COMPONENT),
            type: 'clothing',
            source: 'upload', // 设置来源为用户上传，以便后续处理
            file: panel.file // 保存原始文件对象，供后续上传使用
          }));
          setModelPanels(prevPanels => [...prevPanels, ...panelsWithType]);
          // 获取新的面板引用
          newPanel = panelsWithType[0];
        }

        // 显示提示，在款式图上传成功后显示
        setShowTips(true);
        setTipContent(DIVERGENT_TIP);

        // 临时隐藏自动打开蒙版绘制弹窗功能 - 爆款延伸页面暂不需要用户绘制蒙版
        // 如需恢复此功能，请取消下方注释并删除 false && 条件
        /* 上传成功后自动打开蒙版绘制弹窗
        setTimeout(() => {
          setCurrentMaskPanel(newPanel);
          setShowMaskDrawModal(true);
        }, 300); // 短暂延迟，确保上传指导弹窗已关闭
        */
        {false && setTimeout(() => {
          setCurrentMaskPanel(newPanel);
          setShowMaskDrawModal(true);
        }, 300)}
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        // 移除处理中的面板
        setModelPanels(prevPanels =>
          prevPanels.filter(panel => panel.status !== 'processing'));
        // 重置当前重新上传的面板ID
        setCurrentReuploadModelPanelId(null);
      }
    } catch (error) {
      console.error('处理款式图上传结果时出错:', error);
      message.error('处理款式图上传结果时出错: ' + (error.message || '未知错误'));
    }
  };

  // 处理款式图上传
  const handleModelFileUpload = (file) => {
    console.log('款式图上传:', file);
    // 注意：这里不需要立即上传到服务器
    // 文件将在handleModelUploadResult中处理，并在点击生成按钮时上传
  };

  // 处理删除款式图面板
  const handleDeleteModelPanel = (panelId) => {
    setModelPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 处理重新上传款式图
  const handleReuploadModel = (panel) => {
    if (panel && panel.componentId) {
      // 先删除当前面板
      handleDeleteModelPanel(panel.componentId);
      // 然后打开上传弹窗
      setShowModelUploadGuide(true);
      setOperationsPanel(null);
    }
  };

  // 处理款式图状态变更
  const handleModelStatusChange = (panelId, newStatus) => {
    setModelPanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId
          ? { ...panel, status: newStatus }
          : panel
      )
    );
  };

  // 构建服务器URL的辅助函数
  const buildServerUrl = (resultData, userId) => {
    // 获取当前用户ID
    const currentUserId = userId || getCurrentUserId() || 'developer';

    // 优先使用服务器返回的相对路径
    if (resultData.relativePath) {
      return `${process.env.NODE_ENV === 'development' 
        ? process.env.REACT_APP_BACKEND_URL 
        : ''}${resultData.relativePath}`;
    } else {
      // 使用文件名构建URL
      return `${process.env.NODE_ENV === 'development' 
        ? process.env.REACT_APP_BACKEND_URL 
        : ''}/storage/${currentUserId}/uploads/${resultData.serverFileName}`;
    }
  };
  function drawBrushOnImageByMask(imageUrl, maskUrl, brushColor = [255, 0, 0, 128], outputName = 'brushed.png') {
    if (!imageUrl || !maskUrl) {
      console.error('图像地址不能为空');
      return;
    }

    const img = new Image();
    const mask = new Image();

    img.crossOrigin = 'anonymous';
    mask.crossOrigin = 'anonymous';

    let imgLoaded = false;
    let maskLoaded = false;

    img.onload = () => {
      imgLoaded = true;
      if (maskLoaded) drawBrushEffect();
    };

    mask.onload = () => {
      maskLoaded = true;
      if (imgLoaded) drawBrushEffect();
    };

    img.onerror = (err) => console.error('原图加载失败:', err);
    mask.onerror = (err) => console.error('蒙版图加载失败:', err);

    img.src = imageUrl;
    mask.src = maskUrl;

    function drawBrushEffect() {
      const width = img.width;
      const height = img.height;

      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');

      // 步骤 1：绘制原图
      ctx.drawImage(img, 0, 0);

      // 步骤 2：绘制 mask 到临时 canvas，获取像素数据
      const maskCanvas = document.createElement('canvas');
      maskCanvas.width = width;
      maskCanvas.height = height;
      const maskCtx = maskCanvas.getContext('2d');
      maskCtx.drawImage(mask, 0, 0);
      const maskData = maskCtx.getImageData(0, 0, width, height).data;

      // 步骤 3：叠加笔刷效果
      const brushImageData = ctx.getImageData(0, 0, width, height);
      const pixels = brushImageData.data;

      for (let i = 0; i < pixels.length; i += 4) {
        const r = maskData[i];
        const g = maskData[i + 1];
        const b = maskData[i + 2];
        const gray = (r + g + b) / 3;

        if (gray > 200) { // 认为是白色笔刷区域
          pixels[i] = brushColor[0];     // R
          pixels[i + 1] = brushColor[1]; // G
          pixels[i + 2] = brushColor[2]; // B
          pixels[i + 3] = brushColor[3]; // A (0-255)
        }
      }

      ctx.putImageData(brushImageData, 0, 0);

      // 步骤 4：导出
      canvas.toBlob((blob) => {
        if (!blob) {
          console.error('导出失败');
          return;
        }

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = outputName;
        a.click();
        URL.revokeObjectURL(url);
      }, 'image/png');
    }
  }

  // 检查任务状态
  const checkTaskStatus = async (taskId) => {
    try {
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId() || 'developer';

      // 使用taskService获取任务详情
      const task = await getTaskById(taskId, currentUserId);

      if (task) {
        if (task.status === 'completed') {
          // 任务完成，获取生成的图片
          setGeneratedImages(task.results || []);
          setIsGenerating(false);
          message.success('图片生成成功');
        } else if (task.status === 'failed') {
          // 任务失败
          setIsGenerating(false);
          message.error('生成失败: ' + (task.errorMessage || '未知错误'));
        } else {
          // 任务仍在进行中，继续轮询
          setTimeout(() => checkTaskStatus(taskId), 3000);
        }
      } else {
        setIsGenerating(false);
        message.error('获取任务状态失败: 任务不存在');
      }
    } catch (error) {
      console.error('获取任务状态失败:', error);
      setIsGenerating(false);
      message.error('获取任务状态失败: ' + (error.message || '未知错误'));
    }
  };
  function generateAlphaMaskFromBrush(maskUrl, outputName = 'alpha-mask.png') {
    return new Promise((resolve, reject) => {
      if (!maskUrl) {
        console.error('必须提供蒙版图 URL');
        reject(new Error('必须提供蒙版图 URL'));
        return;
      }

      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        const width = img.width;
        const height = img.height;

        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');

        ctx.drawImage(img, 0, 0);
        const imageData = ctx.getImageData(0, 0, width, height);
        const pixels = imageData.data;

        for (let i = 0; i < pixels.length; i += 4) {
          const r = pixels[i];
          const g = pixels[i + 1];
          const b = pixels[i + 2];
          const gray = (r + g + b) / 3;

          // 设置 RGB 为 255（白色），Alpha 为 灰度值（白笔刷=255，黑背景=0）
          pixels[i] = 255;
          pixels[i + 1] = 255;
          pixels[i + 2] = 255;
          pixels[i + 3] = gray;
        }

        ctx.putImageData(imageData, 0, 0);

        canvas.toBlob((blob) => {
          if (!blob) {
            console.error('导出失败');
            reject(new Error('导出失败'));
            return;
          }

          // 下载功能
          // const url = URL.createObjectURL(blob);
          // const a = document.createElement('a');
          // a.href = url;
          // a.download = outputName;
          // a.click();
          // URL.revokeObjectURL(url);

          // 返回 File 对象（可选，也可以只返回 Blob）
          const file = new File([blob], outputName, { type: 'image/png' });
          resolve(file);
        }, 'image/png');
      };

      img.onerror = (err) => {
        console.error('蒙版加载失败:', err);
        reject(err);
      };

      img.src = maskUrl;
    });
  }

  // 处理开始生成按钮点击
  const handleGenerate = async () => {
    setSeed(useRandomSeed? (Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)) : seed);

    // 声明taskData变量在try块外面，以便catch块可以访问
    let taskData;

    try {
      // 验证必要的条件 - 爆款延伸页面需要上传款式图
      if (modelPanels.length === 0) {
        message.error('请先上传款式图');
        return;
      }

      // 确保款式图面板状态为completed
      if (!modelPanels.every(panel => panel.status === 'completed')) {
        message.error('款式图处理尚未完成，请稍后再试');
        return;
      }

      // 验证用户是否填写了文本描述 - 只在选择自定义描述时验证
      if (typeValue === 2 && (!textDescription || textDescription.trim() === '')) {
        message.error('请填写对生成延伸款式的具体要求描述');
        return;
      }
      setIsGenerating(true);

      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId() || 'developer';
      const balance = await checkUserBalance('爆款延伸', 'divergent', imageQuantity);
      if(balance.code !== 200){
        message.error(balance.message);
        setIsProcessing(false);
        setIsGenerating(false);
        return;
      }
      // 处理可能的自定义上传的款式图
      let clothingToUse = { ...modelPanels[0] };

      // 从TaskPanel拖拽过来的款式图也需要上传到服务器
      if ((modelPanels[0].file && modelPanels[0].source === 'upload') ||
          (modelPanels[0].source === 'upload' && !modelPanels[0].file)) {
        
        // 显示上传中提示
        message.loading('正在上传款式图...', 0);

        try {
          let fileToUpload = modelPanels[0].file;
          
          // 如果没有file对象但有URL，需要从URL获取文件
          if (!fileToUpload && modelPanels[0].url) {
            try {
              const response = await fetch(modelPanels[0].url);
              const blob = await response.blob();
              fileToUpload = new File([blob], modelPanels[0].serverFileName || 'design.jpg', {
                type: blob.type || 'image/jpeg'
              });
            } catch (error) {
              console.error('从URL获取款式图文件失败:', error);
              message.error('款式图处理失败，请重试');
              setIsProcessing(false);
              setIsGenerating(false);
              return;
            }
          }
          
          // 将文件上传到服务器
          const { urls, fileInfos } = await uploadFiles([fileToUpload], 'divergent');
          
          // 上传成功后，使用服务器返回的URL更新图片对象
          if (urls) {
            clothingToUse = {
              ...modelPanels[0],
              url: urls[0],
              serverFileName: modelPanels[0].serverFileName,
              originalImage: modelPanels[0].url,
              originalUrl: urls[0],
              image: urls[0], // 同时设置image字段与url保持一致
              processedFile: urls[0], // 添加processedFile字段，与url保持一致
              source: 'history',  // 修改图片来源为历史记录，表示已在服务器上
              file: undefined,
              fileInfo: fileInfos[0]
            };
            message.success('款式图上传成功');
          } else {
            message.error('款式图上传失败');
            setIsProcessing(false);
            setIsGenerating(false);
            return;
          }
        } catch (error) {
          console.error('上传款式图时出错:', error);
          message.error('款式图上传失败: ' + (error.message || '未知错误'));
          setIsProcessing(false);
          setIsGenerating(false);
          return;
        } finally {
          // 关闭上传中提示
          message.destroy();
        }
      }



      // 生成唯一任务ID
      const taskId = generateId(ID_TYPES.TASK);

      // 创建任务数据对象，使用处理后的设计图URL
      const taskData = {
        taskId: taskId,
        userId: currentUserId,
        createdAt: new Date(),
        status: 'processing',
        imageCount: imageQuantity, // 使用imageQuantity而不是固定值
        taskType: 'divergent',
        pageType: 'divergent',
        seed: seed,
        // 使用数组形式的组件结构
        components: [
          {
            componentType: 'modelMaskPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            isMainImage: true,
            name: clothingToUse.title || '款式图',
            serverFileName: clothingToUse.serverFileName,
            url: clothingToUse.url, // 使用更新后的URL（可能是服务器URL）
                          fileInfo: clothingToUse.fileInfo ? {
              ...clothingToUse.fileInfo,
              // 确保size属性是数字类型
              size: typeof clothingToUse.fileInfo.size === 'string' ?
                parseFloat(clothingToUse.fileInfo.size.replace(/[^\d.]/g, '')) :
                clothingToUse.fileInfo.size,
              // 确保fileInfo中也设置serverFileName
              serverFileName: clothingToUse.serverFileName
            } : { serverFileName: clothingToUse.serverFileName },
            status: 'completed',
            originalImage: clothingToUse.originalImage || clothingToUse.url,
            // 蒙版信息 - 临时设置为无蒙版状态
            // 如需恢复蒙版功能，请修改下方逻辑以使用实际的蒙版状态
            hasMask: clothingToUse.hasMask || false,  // 使用实际的蒙版状态，默认为false
            maskPath: clothingToUse.maskPath || null,
            maskFileName: clothingToUse.maskFileName || null
          },
          {
            componentType: 'textDescriptionPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '文本描述',
            status: 'completed',
            prompt: typeValue === 1 ? "自动生成延伸款式" : textDescription
          },
          {
            componentType: 'quantityPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '生成数量',
            quantity: imageQuantity
          },
          {
            componentType: 'randomSeedSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '随机种子',
            status: 'completed',
            useRandom: false,
            value: seed
          },
          {
            componentType: 'typeSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '类型选择',
            status: 'completed',
            value: typeValue
          },
          {
            componentType: 'weightPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '变化强度',
            status: 'completed',
            weights: weights
          },
        ],
        // 初始化生成图片数组
        generatedImages: Array(1).fill(null).map((_, index) => ({
          imageIndex: index,
          status: 'processing'
        })),
        processInfo:{
          results:[]
        }
      };

      if (generationAreaRef.current && generationAreaRef.current.setGenerationTasks) {
        if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      }
      // 先添加到本地状态，使UI立即响应
      try {
        // 创建工作流任务
        await createFlowTask(taskData);
        
        // 临时修改蒙版处理逻辑 - 爆款延伸页面暂不需要蒙版
        // 如需恢复蒙版功能，请取消下方注释并删除直接使用原图的逻辑
        let output_url;
        if (clothingToUse.hasMask && clothingToUse.maskPath) {
          // 如果有蒙版，使用蒙版处理
          const img = await processMask(clothingToUse.url, clothingToUse.maskPath);
          output_url = img.output_url;
          if(output_url==""){
            message.error('蒙版处理失败，检查图片格式信息是否正确');
            setIsProcessing(false);
            setIsGenerating(false);
            return;
          }
        } else {
          // 如果没有蒙版，直接使用原图URL
          output_url = clothingToUse.url;
          console.log('无蒙版模式：直接使用原图URL', output_url);
        }

        // 执行工作流
        const resultData = await executeFlow(WORKFLOW_NAME.DIVERGENT,{
          "53": {
            "amount":imageQuantity
          },
          "48":{
            "text": typeValue === 1 ? "自动生成延伸款式" : textDescription
          },
          "50":{
            "url":output_url
          },
          "19":{
            "seed": seed,
            "denoise": weights.item2
          },
          "47":{
            "select": typeValue
          },
          "subInfo":{
            "type": "divergent",
            "title":"爆款延伸",
            "count":imageQuantity
          }
        },taskData.taskId);
        setIsProcessing(false);
        setIsGenerating(false);
        setHasUnsavedChanges(false); 
        if( generationAreaRef.current){
 
                  taskData.promptId = resultData.promptId;
        taskData.instanceId = resultData.instanceId;
        taskData.url = resultData.url;
        taskData.newTask = true;
        taskData.netWssUrl=resultData.netWssUrl;
        taskData.clientId=resultData.clientId;
          generationAreaRef.current.setGenerationTasks(taskData);
      }
      } catch (error) {
        setIsProcessing(false);
        setIsGenerating(false);
        // 更新任务状态为失败
        taskData.status = 'failed';
        taskData.errorMessage = error.message;
        if( generationAreaRef.current){
          generationAreaRef.current.setGenerationTasks(taskData);
        }
        
        // 调用updateTask以触发失败提示音
        updateTask(taskData);
        
        // 更新任务列表
      }
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error('创建任务失败: ' + error.message);
      setIsProcessing(false);
      setIsGenerating(false);
      // 从状态中移除失败的任务，只有当taskData已定义时才执行
      if (taskData) {
        setGenerationTasks(prev => prev.filter(task => task.taskId !== taskData.taskId));
      }
    }
  };

  // 处理重新编辑任务
  const handleEditTask = (task) => {
    if (!task) return;

    try {
      console.log('编辑任务:', task);
      console.log('任务组件:', task.components);

      // 只处理数组结构，不再兼容对象结构
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);

      // 回填款式图面板 - 包括蒙版状态
      const clothingComponent = components.find(c => c.componentType === 'modelMaskPanel');
      if (clothingComponent) {
        console.log('获取到款式图组件:', clothingComponent);
        const modelPanel = {
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT), // 确保id与componentId一致
          type: 'clothing',
          title: clothingComponent.title || clothingComponent.name || '款式图',
          status: 'completed', // 确保状态为completed
          serverFileName: clothingComponent.serverFileName,
          url: clothingComponent.originalImage || clothingComponent.url, // 使用originalImage作为url
          processedFile: clothingComponent.processedFile,
          maskPath: clothingComponent.maskPath,
          fileInfo: clothingComponent.fileInfo ? {
            ...clothingComponent.fileInfo,
            // 确保size属性是数字类型
            size: typeof clothingComponent.fileInfo.size === 'string' ?
              parseFloat(clothingComponent.fileInfo.size.replace(/[^\d.]/g, '')) :
              clothingComponent.fileInfo.size,
            // 确保fileInfo中也设置serverFileName
            serverFileName: clothingComponent.serverFileName
          } : {
            size: 2500000, // 2.5MB对应的字节数
            width: 1024,
            height: 1024,
            format: 'image/jpeg',
            serverFileName: clothingComponent.serverFileName
          }, // 提供默认fileInfo，确保size为数字
          ...clothingComponent,
        };

        // 始终显示提示组件
        setShowTips(true);

        // 移除蒙版回填，让用户手动重新绘制蒙版
        modelPanel.hasMask = false;

        // 如果原任务有蒙版，提示用户需要重新绘制
        if (clothingComponent.hasMask) {
          // 更新提示内容，告知用户需要重新绘制蒙版
          setTipContent('需重新绘制蒙版，否则无法开始生成。(点击款式图右侧按钮)');
        } else {
                  // 更新提示内容为默认提示
        setTipContent(DIVERGENT_TIP);
        }

        setModelPanels([modelPanel]);
      } else {
        console.warn('未找到模型组件，请检查任务数据');
        message.warning('无法找到款式图数据');
      }

      // 获取文字描述组件并回填
      const textDescriptionComponent = components.find(c => c.componentType === 'textDescriptionPanel');
      if (textDescriptionComponent && textDescriptionComponent.prompt) {
        console.log('获取到文字描述组件:', textDescriptionComponent);
        setTextDescription(textDescriptionComponent.prompt || '');
      } else {
        // 如果在组件中没找到，尝试从任务级别属性中获取
        if (task.textPrompt) {
          console.log('使用任务级文本描述:', task.textPrompt);
          setTextDescription(task.textPrompt);
        } else {
          // 清空文本描述
          setTextDescription('');
          console.log('未找到文本描述数据，清空输入框');
        }
      }

      // 回填数量设置
      const quantityComponent = components.find(c => c.componentType === 'quantityPanel');
      if (quantityComponent?.quantity) {
        console.log('获取到数量组件:', quantityComponent);
        setImageQuantity(quantityComponent.quantity);
      } else if (task.imageCount) {
        // 回退：使用任务顶层属性
        console.log('使用任务级imageCount:', task.imageCount);
        setImageQuantity(task.imageCount);
      }

      // 回填种子设置
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      if (seedComponent) {
        console.log('获取到种子组件:', seedComponent);
        setUseRandomSeed(false);
        setSeed(seedComponent.value);
      }

      // 回填类型选择设置
      const typeSelectorComponent = components.find(c => c.componentType === 'typeSelector');
      if (typeSelectorComponent) {
        console.log('获取到类型选择组件:', typeSelectorComponent);
        setTypeValue(typeSelectorComponent.value || 1);
      } else {
        // 如果没有找到类型选择组件，使用默认值
        setTypeValue(1);
      }

      // 回填权重设置
      const weightComponent = components.find(c => c.componentType === 'weightPanel');
      if (weightComponent?.weights) {
        console.log('获取到权重组件:', weightComponent);
        setWeights(weightComponent.weights);
      } else {
        // 如果没有找到权重组件，使用默认值
        setWeights({ item1: 0.5, item2: 0.5 });
      }
     

      // 切换到结果标签页
      setActiveTab('result');

      // 显示成功消息
      message.success({
        content: '配置已重新导入，可继续进行调整',
        duration: 5
      });

      // 添加种子回填相关日志
      console.log('种子回填信息:', {
        任务种子组件: seedComponent,
        任务级种子值: task.seed,
        当前种子状态: {
          useRandomSeed: useRandomSeed,
          seedValue: seed
        }
      });

    } catch (error) {
      console.error('处理编辑任务时出错:', error);
      message.error('加载任务设置失败');
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleViewDetails = (image, task, index) => {
    try {
      console.log('查看图片详情:', image);
      console.log('任务数据:', task);

      // 只处理数组结构，不再兼容对象结构
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);

      // 获取所需组件数据
      const clothingComponent = components.find(c => c.componentType === 'modelMaskPanel');
      const randomSeedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      const textDescriptionComponent = components.find(c => c.componentType === 'textDescriptionPanel');
      const typeSelectorComponent = components.find(c => c.componentType === 'typeSelector');
      const weightComponent = components.find(c => c.componentType === 'weightPanel');
      console.log('查看任务详情 - 获取的组件:', {
        clothingComponent,
        randomSeedComponent,
        textDescriptionComponent,
        typeSelectorComponent,
        textPrompt: textDescriptionComponent?.prompt || '没有prompt字段'
      });

      // 准备适配后的组件数据 - 使用数组格式
      const adaptedComponents = [
        // 款式图组件
        clothingComponent ? {
          ...clothingComponent,
          componentType: 'modelMaskPanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          // 确保originalImage和url属性存在且有效
          originalImage: clothingComponent.originalImage || clothingComponent.url,
          url: clothingComponent.url || clothingComponent.originalImage,
          // 确保serverFileName被正确传递
          serverFileName: clothingComponent.serverFileName
        } : null,
        // 文本描述组件 - 添加标准的描述词组件
        textDescriptionComponent ? {
          ...textDescriptionComponent,
          componentType: 'textDescriptionPanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          name: '文本描述',
          status: 'completed',
          prompt: textDescriptionComponent.prompt || ''
        } : null,
        // 随机种子组件
        {
          componentType: 'randomSeedSelector',
          componentId: generateId(ID_TYPES.COMPONENT),
          useRandom: randomSeedComponent?.useRandom || false,
          value: randomSeedComponent?.value || 0
        },
        // 类型选择组件
        {
          componentType: 'typeSelector',
          componentId: generateId(ID_TYPES.COMPONENT),
          value: typeSelectorComponent?.value || typeValue || 1,
          options: [
            { value: 1, label: '自动生成' },
            { value: 2, label: '自定义描述' }
          ]
        },
        // 权重组件
        {
          componentType: 'weightPanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          weights: weightComponent?.weights || { item1: 0.5, item2: 0.5 }
        }
      ].filter(Boolean); // 过滤掉null值


      // 先设置基础数据，避免阻塞UI
      return {
        ...task,
        seed: randomSeedComponent?.value || 0,
        // 添加适配后的组件数据供ImageDetailsModal使用
        components: adaptedComponents
      }
    } catch (error) {
      console.error('处理查看图片详情时出错:', error);
      message.error('查看图片详情失败');
    }
  };

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);

    // 重置状态，无需延迟
    setSelectedImage(null);
    // 重置任务信息，避免保留旧任务导致重新打开时出错
    setImageDetailsTask(null);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);


  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');

      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        // 预览弹窗已移除，无需处理
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;

    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;

    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;

    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }

    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };

  // 处理缩放变化
  const handleScaleChange = (newScale) => {
    setImageScale(newScale);
  };

  // 添加复位处理函数
  const handleReset = () => {
    setImageScale(initialScale);
    setImagePosition({ x: 0, y: 0 });
    lastPosition.current = { x: 0, y: 0 };
  };

  const [uploadedClothingId, setUploadedClothingId] = useState(null);
  const [uploadedClothingUrl, setUploadedClothingUrl] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState(null);
  const [generatedImages, setGeneratedImages] = useState([]);
  const [showMaskDrawModal, setShowMaskDrawModal] = useState(false);
  const [currentMaskPanel, setCurrentMaskPanel] = useState(null);

  // 处理打开蒙版绘制弹窗
  const handleDrawMask = (panel) => {
            // 临时隐藏蒙版绘制弹窗功能 - 爆款延伸页面暂不需要用户绘制蒙版
        // 如需恢复此功能，请取消下方注释并删除 false && 条件
    console.log('蒙版绘制功能已临时隐藏');
    
    /* 蒙版绘制弹窗功能
    setCurrentMaskPanel(panel);
    console.log('currentMaskPanel', currentMaskPanel);
    
    setShowMaskDrawModal(true);
    */
    {false && (() => {
    })()}
  };

  // 处理保存蒙版
  const handleSaveMask = async (maskData, panelId, savePath) => {
    // 如果maskData为null，表示用户想要删除蒙版
    if (maskData === null) {
      console.log(`删除蒙版数据，面板ID: ${panelId}`);

      // 更新款式图面板，删除蒙版相关信息
      setModelPanels(prevPanels =>
        prevPanels.map(panel =>
          panel.componentId === panelId
            ? {
                ...panel,
                hasMask: false,
                maskData: null,
                maskPath: null,
                maskFileName: null
              }
            : panel
        )
      );

      // 恢复默认提示
      setTipContent(DIVERGENT_TIP);

      message.success({
        content: '蒙版已删除',
        duration: 2
      });

      return;
    }

    // 创建文件名 (使用面板ID + 时间戳，确保唯一性)
    const timestamp = Date.now();
    const fileName = `mask_${panelId}_${timestamp}.png`;
    console.log(`生成的蒙版文件名: ${fileName}`);

    // 显示处理中消息
    message.loading({
      content: '正在保存蒙版...',
      key: 'maskSave'
    });

    try {
      const file =await generateAlphaMaskFromBrush(maskData)
      const data = await uploadFiles([file],"divergent");
       // 先更新本地状态，确保UI响应良好
      updateLocalMaskState(panelId, maskData, data.fileInfos[0].url , fileName);
      if (data) {
        message.success({
          content: '蒙版保存成功',
          key: 'maskSave',
          duration: 2
        });

        // 更新款式图面板的蒙版路径信息，使用服务器返回的路径
        setModelPanels(prevPanels =>
          prevPanels.map(panel =>
            panel.componentId === panelId
              ? {
                  ...panel,
                  hasMask: true,
                  maskPath: data.fileInfos[0].url,
                  maskFileName: fileName
                }
              : panel
          )
        );
      } else {
        message.error({
          content: data.message || '服务器拒绝了蒙版保存请求',
          key: 'maskSave',
          duration: 3
        });
        console.error('蒙版上传失败:', data);
      }
    } catch (error) {
      console.error('处理蒙版数据时出错:', error);
      message.error({
        content: '处理蒙版数据时出错',
        key: 'maskSave',
        duration: 3
      });
    }

    // 更新提示内容
    setTipContent(MASK_SAVED_TIP);
  };

  // 辅助函数：更新本地蒙版状态
  const updateLocalMaskState = (panelId, maskData, path, fileName) => {
    setModelPanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId
          ? {
              ...panel,
              hasMask: true,
              maskData,
              maskPath: path,
              maskFileName: fileName
            }
          : panel
      )
    );
  };

  // 辅助函数: Base64转Blob
  const base64ToBlob = (base64, mimeType) => {
    const byteCharacters = atob(base64);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, { type: mimeType });
  };

  // 创建缩略图并保存到历史记录
  const createThumbnailAndSaveToHistory = (file, localUrl, serverUrl, fileName, imageType, pageType) => {
    try {
      console.log('创建缩略图并保存到历史记录:', fileName);

      // 创建图片对象用于加载文件
      const img = new Image();
      img.onload = () => {
        try {
          // 创建canvas用于绘制缩略图
          const canvas = document.createElement('canvas');

          // 设置缩略图最大尺寸
          const MAX_THUMBNAIL_SIZE = 400;

          // 计算缩放比例
          let width = img.width;
          let height = img.height;
          const aspectRatio = width / height;

          if (width > height) {
            // 横向图片
            if (width > MAX_THUMBNAIL_SIZE) {
              width = MAX_THUMBNAIL_SIZE;
              height = width / aspectRatio;
            }
          } else {
            // 纵向图片
            if (height > MAX_THUMBNAIL_SIZE) {
              height = MAX_THUMBNAIL_SIZE;
              width = height * aspectRatio;
            }
          }

          // 设置canvas尺寸
          canvas.width = width;
          canvas.height = height;

          // 绘制缩放后的图片
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);

          // 输出为低质量的JPEG (质量0.6)
          const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.6);

          // 计算缩略图大小（KB）
          const base64Data = thumbnailDataUrl.split(',')[1];
          const byteSize = atob(base64Data).length;
          const kiloByteSize = byteSize / 1024;

          console.log(`缩略图创建成功，原始大小: ${img.width}x${img.height}, 缩略图大小: ${width}x${height}, 文件大小: ${kiloByteSize.toFixed(2)}KB`);

          // 保存到历史记录
          const success = saveToHistory({
            id: Date.now().toString(),
            url: localUrl,
            thumbnailUrl: thumbnailDataUrl,
            serverUrl: serverUrl,
            fileName: fileName,
            type: imageType,
            pageType: pageType,
            fileType: file.type,
            saveTime: Date.now()
          });

          if (success) {
            console.log(`历史记录保存成功, 文件名: ${fileName}`);
          } else {
            console.warn(`历史记录保存失败, 文件名: ${fileName}`);
          }
        } catch (error) {
          console.error('创建缩略图过程中出错:', error);
        }
      };

      img.onerror = (error) => {
        console.error('加载图片出错:', error);
      };

      // 使用本地URL加载图片
      img.src = localUrl;
    } catch (error) {
      console.error('处理图片文件时出错:', error);
    }
  };

  // 保存到历史记录，返回布尔值而非Promise
  const saveToHistory = (record) => {
    // 使用更明确的类型定义方式，避免不一致
    if (!record || !record.fileName || !record.serverUrl || typeof record.serverUrl !== 'string' || record.serverUrl.trim() === '') {
      console.warn('记录无效或没有服务器URL，无法保存到历史记录');
      return false;
    }

    try {
      const MAX_HISTORY_COUNT = 10;
      let history = [];
      // 使用更明确的类型定义方式，避免不一致
      const imageType = record.type || 'design';
      const pageType = record.pageType || 'divergent';
      const historyKey = `upload_history_${imageType}_${pageType}`;
      console.log('保存历史记录，使用键值:', historyKey);

      try {
        const historyJson = localStorage.getItem(historyKey);
        if (historyJson) {
          history = JSON.parse(historyJson);
          if (!Array.isArray(history)) {
            console.warn('历史记录格式无效，重置为空数组');
            history = [];
          }
        }
      } catch (e) {
        console.warn('解析历史记录JSON失败，重置为空数组', e);
        history = [];
      }

      // 检查是否已存在相同服务器URL的记录
      const existingIndex = history.findIndex(item =>
        item.serverUrl === record.serverUrl &&
        (item.type === record.type || (!item.type && !record.type)) &&
        (item.pageType === record.pageType || (!item.pageType && !record.pageType))
      );

      if (existingIndex !== -1) {
        console.log(`找到已存在的记录 [${existingIndex}]，服务器URL: ${record.serverUrl}，更新而不是添加新记录`);

        // 保留原始ID，更新其他信息
        record.id = history[existingIndex].id;

        // 从历史记录中移除旧记录
        history.splice(existingIndex, 1);
      } else {
        console.log(`未找到相同服务器URL的记录，添加新记录: ${record.serverUrl}`);

        // 如果记录已经达到最大数量，移除最早的记录
        if (history.length >= MAX_HISTORY_COUNT) {
          const removed = history.pop();
          console.log(`历史记录达到最大限制 ${MAX_HISTORY_COUNT}，移除最旧记录: ${removed?.fileName || '未知'}`);
        }
      }

      // 删除文件对象，避免序列化错误
      const recordToSave = { ...record };
      delete recordToSave.file;

      // 将记录添加到数组开头（最新的在前面）
      history.unshift(recordToSave);

      // 保存回localStorage
      localStorage.setItem(historyKey, JSON.stringify(history));
      console.log(`成功保存历史记录，当前共有 ${history.length} 条记录`);

      // 验证保存是否成功
      const verifyRecords = localStorage.getItem(historyKey);
      if (verifyRecords) {
        const parsed = JSON.parse(verifyRecords);
        console.log(`验证保存：记录已成功保存，当前共有${parsed.length}条记录`);
      }

      return true;
    } catch (err) {
      console.error('保存到历史记录出错:', err);
      return false;
    }
  };

  function applyMaskAsAlpha(originalUrl, maskUrl, outputName = 'output_direct.png') {
    if (!originalUrl || !maskUrl) {
      console.error('必须提供原始图像和蒙版图像的 URL');
      return Promise.reject(new Error('缺少必要的图像 URL'));
    }

    return new Promise((resolve, reject) => {
      const originalImg = new Image();
      const maskImg = new Image();

      originalImg.crossOrigin = 'anonymous';
      maskImg.crossOrigin = 'anonymous';

      let originalLoaded = false;
      let maskLoaded = false;

      const processImages = () => {
        if (!originalLoaded || !maskLoaded) return;

        try {
          const canvas = document.createElement('canvas');
          canvas.width = originalImg.width;
          canvas.height = originalImg.height;
          const ctx = canvas.getContext('2d');

          // 绘制原始图像
          ctx.drawImage(originalImg, 0, 0);
          const originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

          // 创建临时画布处理蒙版
          const maskCanvas = document.createElement('canvas');
          maskCanvas.width = maskImg.width;
          maskCanvas.height = maskImg.height;
          const maskCtx = maskCanvas.getContext('2d');

          // 绘制蒙版图像
          maskCtx.drawImage(maskImg, 0, 0);
          const maskImageData = maskCtx.getImageData(0, 0, maskCanvas.width, maskCanvas.height);

          // 应用蒙版的 alpha 通道到原始图像
          const originalPixels = originalImageData.data;
          const maskPixels = maskImageData.data;

          // 确保两个图像尺寸一致，如果不一致则缩放蒙版
          let finalMaskPixels = maskPixels;
          if (maskImg.width !== originalImg.width || maskImg.height !== originalImg.height) {
            // 重新缩放蒙版到原始图像尺寸
            maskCanvas.width = originalImg.width;
            maskCanvas.height = originalImg.height;
            maskCtx.drawImage(maskImg, 0, 0, originalImg.width, originalImg.height);
            finalMaskPixels = maskCtx.getImageData(0, 0, originalImg.width, originalImg.height).data;
          }

          // 将蒙版的 alpha 通道应用到原始图像
          for (let i = 0; i < originalPixels.length; i += 4) {
            // 获取蒙版对应像素的 alpha 值
            const maskAlpha = finalMaskPixels[i + 3];
            // 将蒙版的 alpha 值应用到原始图像
            originalPixels[i + 3] = maskAlpha;
          }

          // 将处理后的图像数据放回画布
          ctx.putImageData(originalImageData, 0, 0);

          // 导出为 PNG 格式
          canvas.toBlob((blob) => {
            if (!blob) {
              reject(new Error('导出图像失败'));
              return;
            }

            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = outputName;
            a.click();
            URL.revokeObjectURL(url);

            resolve(true);
          }, 'image/png');

        } catch (error) {
          reject(error);
        }
      };

      originalImg.onload = () => {
        originalLoaded = true;
        processImages();
      };

      maskImg.onload = () => {
        maskLoaded = true;
        processImages();
      };

      originalImg.onerror = (err) => {
        reject(new Error('原始图像加载失败: ' + err));
      };

      maskImg.onerror = (err) => {
        reject(new Error('蒙版图像加载失败: ' + err));
      };

      originalImg.src = originalUrl;
      maskImg.src = maskUrl;
    });
  }

  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
  };
  }, [hasUnsavedChanges]);

  // 添加TaskContext的使用
  const { updateTask } = useTaskContext();

  const handleTextDescriptionChange = (value) => {
    setTextDescription(value);
    setHasUnsavedChanges(true);
  };

  const [typeValue, setTypeValue] = useState(1); // 默认为2
  const handleTypeSelect = (val) => {
    setTypeValue(val);
    setHasUnsavedChanges(true);
    
    // 当切换到自动生成时，清空文本描述
    if (val === 1) {
      setTextDescription('');
    }
  };

  // 处理权重变化 - 确保item1保持固定值0.5
  const handleWeightChange = (newWeights) => {
    // 确保第一个参数保持固定值0.5，只允许第二个参数变化
    const updatedWeights = {
      item1: 0.5, // 固定值
      item2: newWeights.item2 // 用户可调节的变化参数
    };
    setWeights(updatedWeights);
    setHasUnsavedChanges(true);
  };

  return (
          <RequireLogin isLoggedIn={isLoggedIn} featureName="爆款延伸功能">
        <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
        <div className="divergent-page">
          <div className="divergent-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isGenerating}
            featureName="divergent"
            quantity={imageQuantity}
          >
            {/* 款式图上传区域或款式图面板 */}
            {modelPanels.length === 0 ? (
              <UploadBox_Model
                title="款式图"
                accept="image/*"
                maxSize={UPLOAD_CONFIG.maxSize}
                onUpload={handleModelFileUpload}
                onShowGuide={() => setShowModelUploadGuide(true)}
                onUploadResult={handleModelUploadResult}
                showInDevelopment={true}
                reuploadFor={currentReuploadModelPanelId}
                pageType="divergent"
                uploadType="design"
              />
            ) : (
              // 展示款式图面板
              modelPanels.map((panel) => (
                <ModelMaskPanel
                  key={panel.componentId}
                  panel={panel}
                  onExpandClick={(panel, position) => {
                    // 创建增强的面板对象，避免警告
                    const enhancedPanel = {
                      ...panel,
                      // 标记为增强的款式图面板以避免不必要的警告
                      isEnhancedModelPanel: true,
                      processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                      fileInfo: panel.fileInfo ? {
                        ...panel.fileInfo,
                        size: typeof panel.fileInfo.size === 'string' ?
                          parseFloat(panel.fileInfo.size.replace(/[^\d.]/g, '')) :
                          panel.fileInfo.size
                      } : {
                        size: 500000, // 默认500KB
                        format: 'image/jpeg',
                        type: 'image/jpeg'
                      }
                    };
                    setOperationsPanel({
                      panel: enhancedPanel,
                      position
                    });
                  }}
                  onDelete={handleDeleteModelPanel}
                  onReupload={handleReuploadModel}
                  onStatusChange={handleModelStatusChange}
                  onDrawMask={handleDrawMask}
                  // 标记为增强的款式图面板以避免不必要的警告
                  isEnhanced={true}
                  pageType="divergent"
                />
              ))
            )}

            {/* 提示信息面板 - 只在上传款式图后显示 */}
            {modelPanels.length > 0 && (
              <TipsPanel
                tipContent={tipContent}
              />
            )}
            <TypeSelector
              onSelect={handleTypeSelect}
              defaultValue={typeValue}
              pageType="divergent"
            />

            {/* 变化强度调节组件 */}
            <WeightPanel
              weights={weights}
              onChange={handleWeightChange}
              item1Label="基础"
              item2Label="变化"
              pageType="divergent"
              hideItem1={true}
            />

            {/* 文本描述组件 - 只在选择自定义描述时显示 */}
            {typeValue === 2 && (
              <TextDescriptionPanel
                description={textDescription}
                onChange={handleTextDescriptionChange}
                placeholder="请输入对生成延伸款式的具体要求描述"
              />
            )}

            {/* 随机种子选择器 */}
            <RandomSeedSelector
              onRandomChange={setUseRandomSeed}
              onSeedChange={setSeed}
              defaultRandom={useRandomSeed}
              defaultSeed={seed}
              // 编辑模式下传递历史种子
              isEdit={selectedImage !== null}
              editSeed={selectedImage?.seed || null}
            />

            {/* 数量面板 */}
            <QuantityPanel
              imageQuantity={imageQuantity}
              onChange={setImageQuantity}
            />
          </ControlPanel>

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />

                  <GenerationArea
            ref={generationAreaRef}    setIsProcessing={setIsGenerating}

            activeTab={activeTab}
            onTabChange={setActiveTab}
            onEditTask={handleEditTask}
            onViewDetails={handleViewDetails}
            pageType="divergent"
          />
        </div>

        {/* 款式图上传指导弹窗 */}
        {showModelUploadGuide && (
          <UploadGuideModal
            type="design"
            pageType="divergent"
            onClose={() => setShowModelUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到款式图上传结果:', result);
              handleModelUploadResult(result);
              setHasUnsavedChanges(true);
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowModelUploadGuide(false);
              }
            }}
            title="款式图上传指南"
            description="请上传清晰的款式图，以便系统能够生成延伸款式。"
            tips={[
              "图片应具有良好的光照和清晰度",
              "图片尺寸建议为1024×1024像素或接近的比例",
              "支持JPG、PNG、WEBP等格式",
              "建议文件大小不超过5MB",
              "上传后可能需要绘制蒙版标记需要延伸的区域"
            ]}
          />
        )}

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={handleDeleteModelPanel}
            onReupload={handleReuploadModel}
            onDrawMask={handleDrawMask}
            pageType="divergent"
          />
        )}

        {/* 添加 ImageDetailsModal 组件 - 使用懒加载 */}
        {showImageDetails && selectedImage ? (
          <ImageDetailsModal
            selectedImage={selectedImage}
            onClose={handleCloseImageDetails}
            generationTasks={generationTasks}
            onEditTask={handleEditTask}
            pageType="divergent"
          />
        ) : null}



        {/* 显示上传的款式图和生成按钮 */}
        {isGenerating && (
          <div className="generating-status">
            <Spin size="large" />
            <p>正在生成中，请稍候...</p>
          </div>
        )}

        {/* 显示生成的图片 */}
        {generatedImages && generatedImages.length > 0 && (
          <div className="generated-images">
            <h3>生成结果</h3>
            <div className="image-grid">
              {generatedImages.map((image, index) => (
                <div key={index} className="image-item">
                  <img src={image.url} alt={`生成图片 ${index + 1}`} />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 蒙版绘制弹窗 - 临时隐藏但保留代码以便将来扩展 */}
        {/* 如需恢复此功能，请删除下方的 false && 条件 */}
        {false && showMaskDrawModal && currentMaskPanel && (
          <MaskDrawModal
            isOpen={showMaskDrawModal}
            panel={currentMaskPanel}
            onClose={() => setShowMaskDrawModal(false)}
            onSaveMask={handleSaveMask}
            pageType="divergent"
            savePath={`server/storage/${userId || getCurrentUserId() || 'developer'}/style/divergent/mask`}
          />
        )}


      </div>
    </RequireLogin>
  );
};

export default DivergentPage; 