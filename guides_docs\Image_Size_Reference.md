# 图片尺寸组件标准实现参考

本文档详细记录了背景替换页面关于图片尺寸组件的所有设置方式，包括状态管理、组件集成、数据结构、任务生成逻辑、测试数据生成、图片详情显示和编辑回填等所有相关实现。其他页面应参照本文档进行修改，确保完全一致性。

## 1. 基础数据结构与状态设置

### 1.1 尺寸相关状态管理
```javascript
// 添加图片尺寸相关状态
const [useDefaultSize, setUseDefaultSize] = useState(true);  // 是否使用默认尺寸
const [imageWidth, setImageWidth] = useState(1024);  // 图片宽度
const [imageHeight, setImageHeight] = useState(1024);  // 图片高度
```

### 1.2 图片尺寸组件引入
```javascript
import ImageSizeSelector from '../../../components/ImageSizeSelector';
```

### 1.3 任务数据结构中的尺寸字段
```javascript
// 标准任务对象包含以下尺寸相关字段:
{
  id: 'TASK001',
  settings: {
    // 尺寸设置对象
    size: {
      useDefault: true,  // 是否使用默认尺寸
      width: 1024,       // 宽度值，仅当useDefault为false时使用
      height: 1024       // 高度值，仅当useDefault为false时使用
    },
    // 其他设置...
  },
  // 图片数组中每张图片的fileInfo属性包含尺寸
  images: [
    {
      url: '/path/to/image.jpg',
      imageIndex: 0,
      fileInfo: {
        size: 2100000,
        width: 1024,      // 图片实际宽度
        height: 1536,     // 图片实际高度
        format: 'image/jpeg'
      }
      // 其他图片属性...
    }
  ]
}
```

## 2. 组件集成与默认尺寸更新

### 2.1 组件使用
```javascript
{/* 图片尺寸选择器 */}
<ImageSizeSelector
  onUseDefaultChange={setUseDefaultSize}
  onWidthChange={setImageWidth}
  onHeightChange={setImageHeight}
  defaultUseDefault={useDefaultSize}
  defaultWidth={imageWidth}
  defaultHeight={imageHeight}
  pageType="background"
  imageData={{
    uploadedImages: foregroundPanels
  }}
/>
```

### 2.2 上传图片后更新默认尺寸
```javascript
// 在前景图上传成功后更新默认尺寸
useEffect(() => {
  if (foregroundPanels.length > 0 && foregroundPanels[0].fileInfo) {
    const panel = foregroundPanels[0];
    if (panel.fileInfo.width && panel.fileInfo.height) {
      setImageWidth(panel.fileInfo.width);
      setImageHeight(panel.fileInfo.height);
    }
  }
}, [foregroundPanels]);
```

## 3. 测试数据生成

```javascript
// 任务列表状态 - 开发环境使用mock数据，生产环境初始为空
const [generationTasks, setGenerationTasks] = useState(() => {
  // 开发环境使用mock数据
  if (process.env.NODE_ENV !== 'production') {
    console.warn('警告: 当前使用的是测试数据，上线前必须删除');
    return Array.from({ length: 50 }, (_, index) => {
      // 为每个任务生成一个随机种子值
      const generatedSeed = Math.floor(Math.random() * 1000000);
      
      return {
        id: `TASK${String(index + 1).padStart(3, '0')}`,
        // 其他任务属性...
        settings: {
          // 其他设置属性...
          
          // 添加尺寸设置
          size: {
            // 奇数索引使用默认尺寸，偶数索引使用自定义尺寸
            useDefault: index % 2 === 1,
            width: index % 2 === 0 ? 1280 : 800,
            height: index % 2 === 0 ? 720 : 1200
          },
          
          // 其他设置...
        },
        images: Array(4).fill(null).map((_, imgIndex) => ({
          url: `/images/test/background/result${((index + imgIndex) % 4) + 1}.jpg`,
          imageIndex: imgIndex,
          fileInfo: {
            size: 2100000,
            // 自定义尺寸任务的图片使用设置的尺寸值，否则使用默认值
            width: index % 2 === 0 ? 1280 : 800,
            height: index % 2 === 0 ? 720 : 1200,
            format: 'image/jpeg'
          }
        }))
      };
    });
  }
  
  // 生产环境初始为空数组，稍后会通过API获取
  return [];
});
```

## 4. 任务生成逻辑

```javascript
// 处理开始生成按钮点击
const handleGenerate = async () => {
  // 验证必要条件...
  
  // 获取图片尺寸设置
  let sizeSetting = {};
  if (!useDefaultSize) {
    // 使用自定义尺寸
    sizeSetting = {
      width: imageWidth,
      height: imageHeight
    };
  }
  
  // 构建任务对象
  const taskData = {
    id: generateId(ID_TYPES.TASK),
    userId: currentUserId,
    createdAt: new Date(),
    status: 'processing',
    imageCount: imageQuantity,
    taskType: 'background', // 指定任务类型为背景替换
    settings: {
      // 其他设置...
      
      // 添加尺寸设置
      size: {
        useDefault: useDefaultSize,
        ...sizeSetting
      },
      
      // 其他设置...
    },
    // 初始状态下的空图像数组
    images: Array(imageQuantity).fill(null).map((_, index) => ({
      imageIndex: index,
      status: 'processing'
    }))
  };
  
  try {
    // 先添加到本地状态，使UI立即响应
    setGenerationTasks(prev => [taskData, ...prev]);
    
    // 调用API创建任务
    if (process.env.NODE_ENV === 'production') {
      // 生产环境API调用代码...
    } else {
      // 开发环境模拟生成过程
      // 模拟生成完成，更新任务状态
      setGenerationTasks(prev => 
        prev.map(task => 
          task.id === taskData.id 
            ? { 
                ...task, 
                status: 'completed',
                images: Array(imageQuantity).fill(null).map((_, index) => ({
                  url: `/images/test/background/result/result${(index % 4) + 1}.jpg`,
                  imageIndex: index,
                  fileInfo: {
                    size: 2100000, // 约2.1MB
                    // 使用用户设置的尺寸或默认尺寸
                    width: task.settings.size && !task.settings.size.useDefault ? task.settings.size.width : 1024,
                    height: task.settings.size && !task.settings.size.useDefault ? task.settings.size.height : 1536,
                    format: 'image/jpeg'
                  }
                }))
              } 
            : task
        )
      );
    }
  } catch (error) {
    // 错误处理...
  }
};
```

## 5. 查看图片详情逻辑

```javascript
const handleViewDetails = (image, task) => {
  // 先预加载图片...
  
  // 准备符合ImageDetailsModal组件期望的数据结构
  const adaptedSettings = {
    // 其他设置...
    
    // 添加尺寸设置
    size: {
      useDefault: task.settings?.size?.useDefault || true,
      width: task.settings?.size?.width || image.fileInfo?.width || 1024,
      height: task.settings?.size?.height || image.fileInfo?.height || 1024
    },
    
    // 其他设置...
  };
  
  // 设置基础数据
  setSelectedImage({
    ...image,
    taskId: task.id,
    createdAt: task.createdAt,
    settings: adaptedSettings,
    // 其他属性...
  });
  
  // 设置任务信息
  setImageDetailsTask(task);
  
  // 直接打开弹窗
  setShowImageDetails(true);
  
  // 初始化其他状态...
};
```

## 6. 图片详情弹窗中尺寸显示逻辑

```javascript
// ImageDetailsModal组件内部尺寸显示逻辑 (src/components/ImageDetailsModal/index.jsx)
<div className="info-item">
  <span className="label">图片尺寸：</span>
  <span className="value">
    {selectedImage.settings?.size && selectedImage.settings.size.useDefault === false ? 
      `自定义尺寸：${selectedImage.settings.size.width} × ${selectedImage.settings.size.height}` : 
      (selectedImage.fileInfo?.width && selectedImage.fileInfo?.height ? 
        `${selectedImage.fileInfo.width} × ${selectedImage.fileInfo.height}` : 
        '未知')
    }
  </span>
</div>
```

## 7. 编辑任务时尺寸设置回填逻辑

```javascript
// 处理编辑任务
const handleEditTask = (task) => {
  // 其他回填逻辑...
  
  // 恢复尺寸设置
  if (task.settings.size) {
    setUseDefaultSize(task.settings.size.useDefault);
    if (task.settings.size.width) setImageWidth(task.settings.size.width);
    if (task.settings.size.height) setImageHeight(task.settings.size.height);
  }
  
  // 其他回填逻辑...
  
  // 添加尺寸回填相关日志
  console.log('重新编辑任务:', {
    foregroundPanel: task.settings.foreground,
    scene: task.settings.scene,
    seed: task.settings.seed || task.seed,
    size: task.settings.size,
    activeTab: 'result'
  });
  
  // 其他回填逻辑...
};
```

## 8. 尺寸设置一致性最佳实践原则

1. **数据结构一致性**：
   - 确保任务对象的`settings`中有`size`属性对象
   - 确保`settings.size`对象有`useDefault`、`width`和`height`属性
   - 当`useDefault`为`true`时，`width`和`height`属性可能为空或不被使用
   - 当`useDefault`为`false`时，必须指定有效的`width`和`height`值

2. **尺寸值一致性**：
   - 当使用自定义尺寸时，生成的图片尺寸应与设置的尺寸一致
   - 当使用默认尺寸时，生成的图片尺寸应与原图尺寸一致

3. **默认尺寸更新逻辑**：
   - 在不同页面类型下使用不同的默认尺寸数据来源：
     - 服装复色、换背景、款式优化、时尚大片等单图片上传页面：使用上传图片的原始尺寸
     - 模特换装页面：使用模特原图原始尺寸
     - 换面料页面：使用服装图片原始尺寸
     - 爆款开发页面：使用版型图片原始尺寸

4. **自定义尺寸处理**：
   - 自定义尺寸模式下默认显示的数值应与默认尺寸判断逻辑一致
   - 默认锁定长宽比，用户改写一个数值时另一个同步变化
   - 用户可以解锁长宽比进行独立填写
   - 数值范围限制为512-2048px

5. **模拟数据生成**：
   - 测试数据中应包含使用默认尺寸和自定义尺寸的任务示例
   - 确保图片的`fileInfo`中的尺寸信息与任务设置一致

6. **图片详情显示**：
   - 当使用默认尺寸时，显示图片实际尺寸
   - 当使用自定义尺寸时，显示"自定义尺寸"并标明尺寸值

7. **回填逻辑**：
   - 编辑任务时，应正确回填尺寸设置和模式
   - 无论是否使用自定义尺寸，都应显示正确的尺寸数值 