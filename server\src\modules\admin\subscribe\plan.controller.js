const Plan = require('./plan.model');
const { createError } = require('../../../utils/error');

/**
 * 获取所有订阅计划
 */
exports.getAllPlans = async (req, res, next) => {
  try {
    const { isPublic } = req.query;
    
    const query = {};
    if (isPublic !== undefined) {
      query.isPublic = isPublic === 'true';
    }
    
    const plans = await Plan.find(query).sort({ sortOrder: 1 });
    
    res.json(plans);
  } catch (error) {
    next(createError(500, '获取订阅计划失败', error));
  }
};

/**
 * 获取单个订阅计划
 */
exports.getPlanById = async (req, res, next) => {
  try {
    const plan = await Plan.findById(req.params.id);
    
    if (!plan) {
      return next(createError(404, '订阅计划不存在'));
    }
    
    res.json(plan);
  } catch (error) {
    next(createError(500, '获取订阅计划详情失败', error));
  }
};

/**
 * 获取订阅计划（通过代码）
 */
exports.getPlanByCode = async (req, res, next) => {
  try {
    const plan = await Plan.findOne({ code: req.params.code });
    
    if (!plan) {
      return next(createError(404, '订阅计划不存在'));
    }
    
    res.json(plan);
  } catch (error) {
    next(createError(500, '获取订阅计划详情失败', error));
  }
};

/**
 * 创建订阅计划
 */
exports.createPlan = async (req, res, next) => {
  try {
    const {
      code,
      name,
      description,
      price,
      features,
      usageQuota,
      isPublic,
      sortOrder,
      isRecommended
    } = req.body;
    
    // 检查必要字段
    if (!code || !name) {
      return next(createError(400, '计划代码和名称为必填项'));
    }
    
    // 检查代码是否已存在
    const existingPlan = await Plan.findOne({ code });
    if (existingPlan) {
      return next(createError(400, '订阅计划代码已存在'));
    }
    
    // 创建计划对象，设置默认值
    const newPlan = new Plan({
      code,
      name,
      description: description || '',
      price: price || { monthly: 0, yearly: 0, discount: 0 },
      features: features || {
        design: { enabled: false },
        model: { enabled: false },
        tools: { enabled: false },
        support: { level: 'standard', responseTime: '5x8' }
      },
      usageQuota: usageQuota || { totalRequests: -1, dailyRequests: -1 },
      isPublic: isPublic !== undefined ? isPublic : true,
      sortOrder: sortOrder || 0,
      isRecommended: isRecommended || false
    });
    
    await newPlan.save();
    
    res.status(201).json({
      message: '订阅计划创建成功',
      plan: newPlan
    });
  } catch (error) {
    console.error('创建订阅计划错误:', error);
    next(createError(500, '创建订阅计划失败', error));
  }
};

/**
 * 更新订阅计划
 */
exports.updatePlan = async (req, res, next) => {
  try {
    const {
      name,
      description,
      price,
      features,
      usageQuota,
      isPublic,
      sortOrder,
      isRecommended
    } = req.body;
    
    const plan = await Plan.findById(req.params.id);
    
    if (!plan) {
      return next(createError(404, '订阅计划不存在'));
    }
    
    // 更新字段
    if (name) plan.name = name;
    if (description !== undefined) plan.description = description;
    if (price) plan.price = price;
    if (features) {
      // 合并特性，保留原有结构
      if (features.design) plan.features.design = { ...plan.features.design, ...features.design };
      if (features.model) plan.features.model = { ...plan.features.model, ...features.model };
      if (features.tools) plan.features.tools = { ...plan.features.tools, ...features.tools };
      if (features.support) plan.features.support = { ...plan.features.support, ...features.support };
    }
    if (usageQuota) plan.usageQuota = { ...plan.usageQuota, ...usageQuota };
    if (isPublic !== undefined) plan.isPublic = isPublic;
    if (sortOrder !== undefined) plan.sortOrder = sortOrder;
    if (isRecommended !== undefined) plan.isRecommended = isRecommended;
    
    await plan.save();
    
    res.json({
      message: '订阅计划更新成功',
      plan
    });
  } catch (error) {
    next(createError(500, '更新订阅计划失败', error));
  }
};

/**
 * 删除订阅计划
 */
exports.deletePlan = async (req, res, next) => {
  try {
    const plan = await Plan.findById(req.params.id);
    
    if (!plan) {
      return next(createError(404, '订阅计划不存在'));
    }
    
    // 检查是否有活跃订阅使用此计划
    const Subscription = require('./subscription.model');
    const activeSubscriptions = await Subscription.countDocuments({
      plan: plan.code,
      status: 'active'
    });
    
    if (activeSubscriptions > 0) {
      return next(createError(400, '无法删除正在使用的订阅计划'));
    }
    
    await Plan.findByIdAndDelete(req.params.id);
    
    res.json({
      message: '订阅计划删除成功'
    });
  } catch (error) {
    next(createError(500, '删除订阅计划失败', error));
  }
}; 