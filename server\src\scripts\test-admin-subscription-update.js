const mongoose = require('mongoose');
const Subscription = require('../modules/admin/subscribe/subscription.model');
const User = require('../modules/auth/user.model');

// 连接数据库
mongoose.connect('mongodb://localhost:27017/aibikini', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// 测试管理后台订阅状态自动更新
async function testAdminSubscriptionUpdate() {
  try {
    console.log('=== 测试管理后台订阅状态自动更新 ===');
    
    // 创建一个测试用户
    const testUser = new User({
      username: 'testadmin123',
      name: '测试管理员用户',
      email: '<EMAIL>',
      phone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      password: 'testpassword123'
    });
    await testUser.save();
    console.log(`创建测试用户: ${testUser._id}`);
    
    // 创建一个已过期的订阅
    const expiredSubscription = new Subscription({
      user: testUser._id,
      plan: 'basic',
      status: 'expired',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前开始
      endDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),   // 1天前结束
      price: 99.00,
      autoRenew: false
    });
    await expiredSubscription.save();
    console.log(`创建过期订阅: ${expiredSubscription._id}, 状态: ${expiredSubscription.status}`);
    
    // 模拟管理后台查询订阅列表
    console.log('\n--- 模拟管理后台查询订阅列表 ---');
    let subscriptions = await Subscription.find({ user: testUser._id })
      .populate('user', 'username name email phone')
      .sort({ createdAt: -1 });
    
    console.log(`查询到 ${subscriptions.length} 个订阅`);
    console.log(`订阅状态: ${subscriptions[0].status} (期望: expired)`);
    
    // 手动修改结束日期为未来时间
    console.log('\n--- 修改结束日期为未来时间 ---');
    const newEndDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30天后结束
    await Subscription.findByIdAndUpdate(expiredSubscription._id, {
      endDate: newEndDate
    });
    console.log(`已将结束日期修改为: ${newEndDate.toISOString()}`);
    
    // 再次查询，应该自动更新状态
    console.log('\n--- 再次查询订阅列表（应该自动更新状态） ---');
    subscriptions = await Subscription.find({ user: testUser._id })
      .populate('user', 'username name email phone')
      .sort({ createdAt: -1 });
    
    console.log(`查询到 ${subscriptions.length} 个订阅`);
    console.log(`订阅状态: ${subscriptions[0].status} (期望: active)`);
    
    // 验证状态是否正确
    const expectedStatus = 'active';
    const actualStatus = subscriptions[0].status;
    console.log(`状态验证: ${actualStatus === expectedStatus ? '✅ 通过' : '❌ 失败'}`);
    
    // 清理测试数据
    await Subscription.findByIdAndDelete(expiredSubscription._id);
    await User.findByIdAndDelete(testUser._id);
    console.log('\n清理测试数据完成');
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    mongoose.connection.close();
  }
}

// 运行测试
testAdminSubscriptionUpdate(); 