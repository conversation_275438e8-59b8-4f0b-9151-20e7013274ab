import { DeleteOutlined, DesktopOutlined, EditOutlined, LogoutOutlined, MessageOutlined, PlusOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Badge, Button, Card, Col, Form, Input, Modal, Row, Select, Space, Table, message } from 'antd';
import { useEffect, useState } from 'react';
import api from '../../../api';

const { Option } = Select;

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState({
    page: 1,
    limit: 10
  });
  const [deviceModalVisible, setDeviceModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userDevices, setUserDevices] = useState([]);
  const [remarkModalVisible, setRemarkModalVisible] = useState(false);
  const [remarkUser, setRemarkUser] = useState(null);
  const [remarkValue, setRemarkValue] = useState('');

  const fetchUsers = async (params = queryParams) => {
    setLoading(true);
    try {
      // 构建查询参数
      const queryString = Object.entries(params)
        .filter(([_, value]) => value !== undefined && value !== '')
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&');

      const data = await api.get(`/admin/user?${queryString}`);
      setUsers(data.data || []);
      setTotal(data.total || 0);
      setQueryParams(params);
    } catch (error) {
      message.error('获取用户列表失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleEdit = (user) => {
    setEditingUser(user);
    form.setFieldsValue({
      username: user.username,
      email: user.email,
      role: user.role,
      phone: user.phone,
      name: user.name,
      status: user.status || 'active'
    });
    setModalVisible(true);
  };

  const handleDelete = async (userId) => {
    try {
      await api.delete(`/admin/user/${userId}`);
      message.success('用户删除成功');
      fetchUsers();
    } catch (error) {
      message.error('删除用户失败');
      console.error(error);
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      if (editingUser) {
        await api.put(`/admin/user/${editingUser._id}`, values);
        message.success('用户更新成功');
      } else {
        await api.post('/admin/user', values);
        message.success('用户创建成功');
      }
      setModalVisible(false);
      form.resetFields();
      setEditingUser(null);
      fetchUsers();
    } catch (error) {
      message.error('保存用户失败');
      console.error(error);
    }
  };

  const handleSearch = (values) => {
    fetchUsers({
      ...values,
      page: 1,
      limit: queryParams.limit
    });
  };

  const handleReset = () => {
    searchForm.resetFields();
    fetchUsers({
      page: 1,
      limit: 10
    });
  };

  const handleTableChange = (pagination) => {
    fetchUsers({
      ...queryParams,
      page: pagination.current,
      limit: pagination.pageSize
    });
  };

  // 获取用户设备列表
  const fetchUserDevices = async (userId) => {
    try {
      const response = await api.get(`/admin/user/${userId}/devices`);
      setUserDevices(response.data.devices || []);
    } catch (error) {
      message.error('获取设备列表失败');
      console.error(error);
    }
  };

  // 踢出设备
  const handleKickDevice = async (userId, token) => {
    try {
      await api.post(`/admin/user/${userId}/kick-device`, { token });
      message.success('设备已踢出');
      fetchUserDevices(userId);
    } catch (error) {
      message.error('踢出设备失败');
      console.error(error);
    }
  };

  // 查看设备列表
  const handleViewDevices = async (user) => {
    setSelectedUser(user);
    await fetchUserDevices(user._id);
    setDeviceModalVisible(true);
  };

  // 更新最大登录设备数
  const handleUpdateMaxSessions = async (userId, maxSessions) => {
    try {
      await api.put(`/admin/user/${userId}/max-sessions`, { maxSessions });
      message.success('最大登录设备数更新成功');
      fetchUsers();
    } catch (error) {
      message.error('更新最大登录设备数失败');
      console.error(error);
    }
  };

  const handleRemark = (user) => {
    setRemarkUser(user);
    setRemarkValue(user.remark || '');
    setRemarkModalVisible(true);
  };

  const handleSaveRemark = async () => {
    if (!remarkUser) return;
    try {
      await api.put(`/admin/user/${remarkUser._id}/remark`, { remark: remarkValue });
      message.success('备注已保存');
      setRemarkModalVisible(false);
      setRemarkUser(null);
      setRemarkValue('');
      fetchUsers();
    } catch (error) {
      message.error('保存备注失败');
      console.error(error);
    }
  };

  const columns = [
    {
      title: '用户ID',
      dataIndex: '_id',
      key: '_id',
      width: 220,
      render: (id) => (
        <span style={{
          fontFamily: 'monospace',
          fontSize: '12px',
          color: '#666',
          backgroundColor: '#f5f5f5',
          padding: '2px 6px',
          borderRadius: '3px'
        }}>
          {id}
        </span>
      )
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      sorter: (a, b) => a.username.localeCompare(b.username),
      // 可选：支持筛选
      // filters: [...new Set(users.map(u => u.username))].map(name => ({ text: name, value: name })),
      // onFilter: (value, record) => record.username === value,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      render: (text) => text ? text : <span style={{ color: '#aaa' }}>无</span>
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      sorter: (a, b) => (a.phone || '').localeCompare(b.phone || ''),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 100,
      filters: [
        { text: '管理员', value: 'admin' },
        { text: '普通用户', value: 'user' }
      ],
      onFilter: (value, record) => record.role === value,
      render: (text) => {
        const roleMap = {
          'admin': '管理员',
          'user': '普通用户'
        };
        return roleMap[text] || text;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      filters: [
        { text: '正常', value: 'active' },
        { text: '未激活', value: 'inactive' },
        { text: '已禁用', value: 'suspended' }
      ],
      onFilter: (value, record) => record.status === value,
      render: (text) => {
        const statusMap = {
          'active': '正常',
          'inactive': '未激活',
          'suspended': '已禁用'
        };
        return statusMap[text] || '正常';
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      render: (text) => text ? new Date(text).toLocaleString() : '',
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      sorter: (a, b) => new Date(a.lastLoginAt || 0).getTime() - new Date(b.lastLoginAt || 0).getTime(),
      render: (text) => text ? new Date(text).toLocaleString() : '从未登录',
    },
    {
      title: '最大登录设备数',
      dataIndex: 'maxSessions',
      key: 'maxSessions',
      render: (text, record) => (
        <Select
          value={text}
          style={{ width: 80 }}
          onChange={(value) => handleUpdateMaxSessions(record._id, value)}
        >
          <Option value={1}>1</Option>
          <Option value={2}>2</Option>
          <Option value={3}>3</Option>
          <Option value={4}>4</Option>
          <Option value={5}>5</Option>
        </Select>
      )
    },
    {
      title: '当前登录设备',
      key: 'activeDevices',
      render: (_, record) => (
        <Space>
          <Badge count={record.activeSessions?.length || 0} showZero>
            <Button
              type="link"
              icon={<DesktopOutlined />}
              onClick={() => handleViewDevices(record)}
            >
              查看设备
            </Button>
          </Badge>
        </Space>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={<MessageOutlined />}
            onClick={() => handleRemark(record)}
          >
            备注
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              Modal.confirm({
                title: '确认删除',
                content: `确定要删除用户 ${record.username} 吗？`,
                onOk: () => handleDelete(record._id),
              });
            }}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="user-management" style={{ display: 'flex', flexDirection: 'column', height: '100%', minHeight: 0 }}>
      <Card style={{ marginBottom: 16 }}>
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
        >
          <Form.Item name="username" label="用户名">
            <Input placeholder="请输入用户名" allowClear />
          </Form.Item>
          <Form.Item name="role" label="角色">
            <Select placeholder="请选择角色" allowClear style={{ width: 120 }}>
              <Option value="admin">管理员</Option>
              <Option value="user">普通用户</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              搜索
            </Button>
          </Form.Item>
          <Form.Item>
            <Button onClick={handleReset} icon={<ReloadOutlined />}>
              重置
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <div className="admin-header-actions" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <h1>用户管理</h1>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingUser(null);
            form.resetFields();
            setModalVisible(true);
          }}
        >
          添加用户
        </Button>
      </div>

      <div style={{ flex: 1, minHeight: 0, display: 'flex', flexDirection: 'column' }}>
        <Table
          dataSource={users}
          columns={columns}
          rowKey="_id"
          loading={loading}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.limit,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          scroll={{ y: '100%' }}
          onChange={handleTableChange}
          style={{ flex: 1 }}
        />
      </div>

      <Modal
        title={editingUser ? '编辑用户' : '添加用户'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        destroyOnClose
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="姓名"
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          {!editingUser && (
            <Form.Item
              name="password"
              label="密码"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password />
            </Form.Item>
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role"
                label="角色"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select>
                  <Option value="user">普通用户</Option>
                  <Option value="admin">管理员</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              {editingUser && (
                <Form.Item
                  name="status"
                  label="状态"
                  rules={[{ required: true, message: '请选择状态' }]}
                >
                  <Select>
                    <Option value="active">正常</Option>
                    <Option value="inactive">未激活</Option>
                    <Option value="suspended">已禁用</Option>
                  </Select>
                </Form.Item>
              )}
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 设备管理模态框 */}
      <Modal
        title={`${selectedUser?.username || ''} 的登录设备`}
        open={deviceModalVisible}
        onCancel={() => setDeviceModalVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Table
          dataSource={userDevices}
          rowKey="token"
          columns={[
            {
              title: '设备信息',
              dataIndex: 'deviceInfo',
              key: 'deviceInfo',
              render: (deviceInfo) => (
                <div>
                  <div>IP: {deviceInfo.ip}</div>
                  <div>设备: {deviceInfo.userAgent}</div>
                </div>
              )
            },
            {
              title: '最后活动时间',
              dataIndex: 'lastActive',
              key: 'lastActive',
              render: (text) => new Date(text).toLocaleString()
            },
            {
              title: '操作',
              key: 'action',
              render: (_, record) => (
                <Button
                  type="link"
                  danger
                  icon={<LogoutOutlined />}
                  onClick={() => handleKickDevice(selectedUser._id, record.token)}
                >
                  踢出设备
                </Button>
              )
            }
          ]}
        />
      </Modal>

      <Modal
        title={`备注 - ${remarkUser?.username || ''}`}
        open={remarkModalVisible}
        onOk={handleSaveRemark}
        onCancel={() => setRemarkModalVisible(false)}
        destroyOnClose
      >
        <Input.TextArea
          rows={4}
          value={remarkValue}
          onChange={e => setRemarkValue(e.target.value)}
          placeholder="请输入备注信息"
        />
      </Modal>
    </div>
  );
};

export default UserManagement; 