.image-navigator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 76px; /* 与缩放控制组件保持一致的宽度 */
  padding: 0px;
  background: transparent;
}

.nav-button {
  width: 76px;
  height: 30px;
  border: 1px solid var(--border-light, #e8e8e8);
  border-radius: 6px;
  background: var(--bg-primary, #f5f5f5);
  color: var(--text-secondary, #666);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0 2px;
}

.nav-button:hover:not(:disabled) {
  background: var(--bg-hover, #eee);
  color: var(--text-primary, #333);
  border-color: var(--border-hover, #d0d0d0);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-button svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
  fill: none;
} 