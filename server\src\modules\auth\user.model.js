const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    validate: {
      validator: function(v) {
        // 检查长度
        if (v.length < 4 || v.length > 12) return false;
        // 检查是否为纯数字
        if (/^\d+$/.test(v)) return false;
        // 检查字符类型
        return /^[\u4e00-\u9fa5a-zA-Z0-9]*$/.test(v);
      },
      message: '用户名必须是4-12位的中文、英文或数字组合，且不能为纯数字'
    }
  },
  phone: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    match: /^1[3-9]\d{9}$/, // 中国大陆手机号格式
  },
  password: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return v.length >= 8 && v.length <= 32;
      },
      message: '密码长度必须在8-32位之间'
    }
  },
  name: {
    type: String,
    required: true,
    trim: true,
  },
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  developer: {
    isVerified: {
      type: Boolean,
      default: false,
    },
    apiKey: String,
    apiSecret: String,
    allowedOrigins: [String],
    rateLimit: {
      requests: { type: Number, default: 1000 },
      interval: { type: Number, default: 3600 }, // 每小时请求限制
    },
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  lastLoginAt: {
    type: Date,
  },
  lastLoginIp: {
    type: String,
  },
  loginHistory: [{
    ip: String,
    timestamp: Date,
    userAgent: String
  }],
  activeSessions: [{
    token: String,
    deviceInfo: {
      userAgent: String,
      ip: String
    },
    lastActive: {
      type: Date,
      default: Date.now
    }
  }],
  maxSessions: {
    type: Number,
    default: 1  // 默认最多允许3个活跃会话
  },
  remark: {
    type: String,
    default: ''
  }
}, {
  timestamps: true
});

// 密码加密中间件
userSchema.pre('save', async function(next) {
  // 只有在密码被修改时才重新加密
  if (!this.isModified('password')) return next();
  
  try {
    // 生成盐值
    const salt = await bcrypt.genSalt(10);
    // 使用盐值加密密码
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 验证密码方法
userSchema.methods.comparePassword = async function(candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw error;
  }
};

// 生成开发者密钥
userSchema.methods.generateDeveloperKeys = async function() {
  if (this.role !== 'developer') {
    throw new Error('User is not a developer');
  }
  
  this.developer.apiKey = require('crypto').randomBytes(16).toString('hex');
  this.developer.apiSecret = require('crypto').randomBytes(32).toString('hex');
  await this.save();
  
  return {
    apiKey: this.developer.apiKey,
    apiSecret: this.developer.apiSecret,
  };
};

// 添加会话管理方法
userSchema.methods.addSession = async function(token, deviceInfo) {
  // 如果达到最大会话数，删除最旧的会话
  if (this.activeSessions.length >= this.maxSessions) {
    this.activeSessions.sort((a, b) => a.lastActive - b.lastActive);
    this.activeSessions.shift();
  }
  
  this.activeSessions.push({
    token,
    deviceInfo,
    lastActive: new Date()
  });
  
  await this.updateOne({activeSessions: this.activeSessions});
};

userSchema.methods.removeSession = async function(token) {
  this.activeSessions = this.activeSessions.filter(session => session.token !== token);
  await this.updateOne({activeSessions: this.activeSessions});
};

userSchema.methods.updateSessionActivity = async function(token) {
  const session = this.activeSessions.find(s => s.token === token);
  session.lastActive = new Date();
  if (session) {
    await this.updateOne({activeSessions: this.activeSessions});
  }
};

const User = mongoose.model('User', userSchema);

module.exports = User; 