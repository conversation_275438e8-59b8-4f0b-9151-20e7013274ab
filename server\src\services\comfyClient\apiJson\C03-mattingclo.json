{"54": {"inputs": {"threshold": 0.3, "detail_method": "VITMatte", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.15, "needInput": true, "white_point": 0.99, "process_detail": true, "prompt": "swimsuit", "device": "cuda", "max_megapixels": 2, "image": ["58", 0], "sam_models": ["55", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V3", "_meta": {"title": "款式描述"}}, "55": {"inputs": {"sam_model": "sam_vit_h (2.56GB)", "grounding_dino_model": "GroundingDINO_SwinB (938MB)"}, "class_type": "LayerMask: LoadSegmentAnythingModels", "_meta": {"title": "LayerMask: Load SegmentAnything Models(Advance)"}}, "57": {"inputs": {"filename_prefix": "<PERSON><PERSON><PERSON><PERSON>", "images": ["54", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "58": {"inputs": {"needInput": true, "url": "", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "原始图片上传"}}}