/* 全局分页器样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-xs);
  margin-top: auto;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  height: auto;
}

.pagination-info {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  flex-shrink: 0;
}

.pagination-group {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xxs);
  padding: 0 var(--spacing-xxs);
}

.pagination-button {
  min-width: 28px;
  height: 28px;
  border: 1px solid var(--border-light);
  background: var(--bg-primary);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  border-radius: var(--radius-sm);
  padding: 0;
  font-size: var(--font-size-xs);
}

.pagination-button:hover:not(:disabled),
.pagination-button:focus-visible:not(:disabled) {
  color: var(--brand-primary);
  border-color: var(--brand-primary);
}

.pagination-button:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.8;
}

.pagination-button.active {
  background: var(--bg-primary);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
  cursor: default;
}

.pagination-button.active:hover {
  border-color: var(--brand-primary);
}

.pagination-button svg {
  width: var(--font-size-md);
  height: var(--font-size-md);
}

.pagination-button:hover:not(:disabled) svg,
.pagination-button:focus-visible:not(:disabled) svg {
  color: var(--brand-primary) !important;
  stroke: var(--brand-primary) !important;
}

.pagination-button.active svg {
  color: var(--brand-primary) !important;
  stroke: var(--brand-primary) !important;
}

.page-numbers {
  display: flex;
  gap: var(--spacing-xxs);
  margin: 0 var(--spacing-xs);
}

.pagination-jumper {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-left: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  flex-shrink: 0;
}

.pagination-jumper input {
  width: 40px;
  height: 28px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  text-align: center;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  padding: 0 var(--spacing-xs);
  background: var(--bg-primary);
}

.pagination-jumper input:focus {
  outline: none;
  border-color: var(--brand-primary);
  background: var(--bg-primary);
}

.pagination-jumper input:hover {
  border-color: var(--border-color);
  background: var(--bg-primary);
}

.pagination-jumper input::-webkit-inner-spin-button,
.pagination-jumper input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.jump-button {
  height: 28px;
  padding: 0 var(--spacing-xs);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.jump-button:hover {
  color: var(--brand-primary);
  border-color: var(--brand-primary);
}

/* 特定组件分页容器的修改 */
.virtual-model-manager .pagination-container {
  margin-top: 16px;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column !important;
    align-items: center !important;
    gap: 4px !important;
    padding: var(--spacing-xs) var(--spacing-xs) !important;
  }
  .pagination-info {
    margin-bottom: 2px !important;
    text-align: center !important;
    width: 100% !important;
    justify-content: center !important;
    display: flex !important;
  }
  .pagination-group {
    justify-content: center !important;
    width: 100% !important;
    margin-bottom: 2px !important;
  }
  .pagination-jumper {
    justify-content: center !important;
    width: 100% !important;
    margin-bottom: 0 !important;
  }
  .pagination-group .pagination-button,
  .pagination-group .pagination-ellipsis {
    min-width: 22px !important;
    height: 22px !important;
    font-size: 12px !important;
  }
  .pagination-jumper,
  .pagination-jumper input,
  .pagination-jumper .jump-button {
    font-size: 12px !important;
    height: 22px !important;
  }
  .pagination-jumper input {
    width: 32px !important;
    padding: 0 4px !important;
  }
  .pagination-jumper .jump-button {
    padding: 0 8px !important;
  }
} 