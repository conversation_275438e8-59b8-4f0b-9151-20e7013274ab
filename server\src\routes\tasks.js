const express = require('express');
const router = express.Router();
const Task = require('../models/Task');
const { auth, selectAuth } = require('../modules/auth');
const path = require('path');
const fs = require('fs');
const { generateId, ID_TYPES } = require('../utils/idGenerator');
const PAGE_COMPONENT_MAP = require('../config/pageComponentMap');

// 仅在开发环境中添加调试路由
if (process.env.NODE_ENV === 'development') {
  // 查看最近创建的任务
  router.get('/debug/recent', async (req, res) => {
    try {
      const recentTasks = await Task.find()
        .sort({ createdAt: -1 })
        .limit(10)
        .select('_id taskId userId status createdAt');
      
      res.json({
        success: true,
        count: recentTasks.length,
        data: recentTasks
      });
    } catch (error) {
      console.error('获取最近任务失败:', error);
      res.status(500).json({
        success: false,
        message: '获取最近任务失败'
      });
    }
  });
}

// 获取用户的所有任务
router.get('/', selectAuth, async (req, res) => {
  try {
    // 获取当前用户ID
    const userId = req.user._id;
    console.log(`获取用户任务列表，用户ID: ${userId}`);
    
    // 创建查询条件 - 直接使用用户ID
    const query = { userId: userId };
    
    console.log('查询条件:', query);
    const tasks = await Task.find(query).sort({ createdAt: -1 });  // 按创建时间倒序排列
    
    // 处理tasks数组，确保每个任务有imageCount字段
    const processedTasks = tasks.map(task => {
      const taskObj = task.toObject ? task.toObject() : task;
      
      // 如果没有imageCount字段或值为0
      if (!taskObj.imageCount) {
        // 如果有images数组，使用其长度
        if (taskObj.images && Array.isArray(taskObj.images) && taskObj.images.length > 0) {
          taskObj.imageCount = taskObj.images.length;
        } 
        // 否则从组件中获取数量信息
        else if (taskObj.components && taskObj.components.quantityPanel) {
          taskObj.imageCount = taskObj.components.quantityPanel.quantity || 1;
        }
        // 如果都没有，则使用默认值1
        else {
          taskObj.imageCount = 1;
        }
      }
      
      // 确保任务有images字段
      if (!taskObj.images || !Array.isArray(taskObj.images) || taskObj.images.length === 0) {
        // 首先尝试从generatedImages字段获取
        if (taskObj.generatedImages && Array.isArray(taskObj.generatedImages) && taskObj.generatedImages.length > 0) {
          console.log(`任务 ${taskObj.id || taskObj.taskId} 从generatedImages字段转换为images字段`);
          
          // 转换generatedImages为标准格式的images数组
          taskObj.images = taskObj.generatedImages.map(img => {
            // 如果已经是对象格式
            if (typeof img === 'object') {
              const baseUrl = process.env.REACT_APP_BACKEND_URL;
              return {
                url: img.path ? (img.path.startsWith('http') ? img.path : `${baseUrl}${img.path}`) : null,
                path: img.path || null,
                filename: img.filename || null,
                serverFileName: img.filename || null,
                createdAt: img.createdAt || null
              };
            }
            // 如果是字符串（可能是URL或路径）
            return { url: img };
          }).filter(img => img.url); // 确保只保留有url的图片
        } 
        // 如果还没有图片，并且有serverFileName，尝试构建
        else if (taskObj.serverFileName) {
          console.log(`任务 ${taskObj.id || taskObj.taskId} 使用serverFileName构建images字段`);
          
          const baseUrl = process.env.REACT_APP_BACKEND_URL;
          const userId = taskObj.userId || 'developer';
          let path = '';
          
          // 根据不同页面类型构建不同的路径
          if (taskObj.pageType === 'try-on') {
            path = `/developer/model/try-on/generated/${taskObj.serverFileName}`;
          } else if (taskObj.pageType === 'matting') {
            path = `/developer/tools/matting/generated/${taskObj.serverFileName}`;
          } else {
            path = `/storage/${userId}/uploads/${taskObj.serverFileName}`;
          }
          
          taskObj.images = [{
            url: `${baseUrl}${path}`,
            path: path,
            filename: taskObj.serverFileName,
            serverFileName: taskObj.serverFileName
          }];
        }
      }
      
      return taskObj;
    });
    
    res.json({
      success: true,
      data: processedTasks
    });
  } catch (error) {
    console.error('获取任务列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务列表失败'
    });
  }
});

// 获取单个任务详情
router.get('/:taskId', selectAuth, async (req, res) => {
  try {
    console.log(`===== 【数据跟踪】开始获取任务 ${req.params.taskId} =====`);
    
    // 获取当前用户ID
    const userId = req.user._id;
    
    // 创建查询条件 - 直接使用用户ID
    const query = { 
      taskId: req.params.taskId,
      userId: userId
    };
    
    console.log('【数据跟踪】查询单个任务条件:', query);
    const task = await Task.findOne(query);

    if (!task) {
      console.log(`【数据跟踪】未找到任务 ${req.params.taskId}`);
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }
    
    console.log(`【数据跟踪】找到任务 ${req.params.taskId}`);
    
    // 数据库原始数据检查
    console.log(`【数据跟踪】任务原始数据结构:`);
    console.log(`- primaryImageFileName: ${task.primaryImageFileName || '未设置'}`);
    console.log(`- serverFileName: ${task.serverFileName || '未设置'}`);
    console.log(`- components类型: ${typeof task.components}`);
    
    // 检查组件数据格式
    let sourceImagePanelComponent = null;
    
    // 标准化组件结构，无论是数组还是Map
    let componentsArray = [];
    
    if (Array.isArray(task.components)) {
      // 新格式：数组格式的组件
      console.log(`【数据跟踪】components是数组类型，长度: ${task.components.length}`);
      componentsArray = task.components;
    } else if (task.components instanceof Map) {
      // 旧格式：Map类型的组件
      console.log(`【数据跟踪】components是MongoDB Map类型，大小: ${task.components.size}`);
      
      // 将Map转换为数组
      for (const [key, value] of task.components.entries()) {
        if (value) {
          // 确保组件有componentType字段
          if (!value.componentType && key) {
            value.componentType = key;
          }
          componentsArray.push(value);
        }
      }
    } else if (typeof task.components === 'object' && task.components !== null) {
      // 处理普通对象
      console.log(`【数据跟踪】components是普通对象`);
      
      for (const key in task.components) {
        if (Object.prototype.hasOwnProperty.call(task.components, key)) {
          const comp = task.components[key];
          if (comp && typeof comp === 'object') {
            // 确保组件有componentType字段
            if (!comp.componentType && key) {
              comp.componentType = key;
            }
            componentsArray.push(comp);
          }
        }
      }
    } else {
      console.log(`【数据跟踪】components不是有效类型: ${typeof task.components}`);
      componentsArray = [];
    }
    
    // 查找sourceImagePanel组件 - 同时检查componentType和type字段
    sourceImagePanelComponent = componentsArray.find(comp => 
      comp && (comp.componentType === 'sourceImagePanel' || comp.type === 'sourceImagePanel')
    );
    
    if (sourceImagePanelComponent) {
      console.log(`【数据跟踪】找到sourceImagePanel组件:`, {
        componentId: sourceImagePanelComponent.componentId || '未设置',
        serverFileName: sourceImagePanelComponent.serverFileName || '未设置',
        isMainImage: sourceImagePanelComponent.isMainImage || false
      });
    } else {
      console.log(`【数据跟踪】在组件数组中未找到sourceImagePanel组件`);
    }
    
    // 处理task对象，确保有imageCount字段
    console.log(`【数据跟踪】开始转换任务数据为普通JS对象`);
    const taskObj = task.toObject ? task.toObject() : task;
    
    console.log(`【数据跟踪】转换后的数据结构:`);
    console.log(`- primaryImageFileName: ${taskObj.primaryImageFileName || '未设置'}`);
    console.log(`- serverFileName: ${taskObj.serverFileName || '未设置'}`);
    console.log(`- components类型: ${typeof taskObj.components}, 是否数组: ${Array.isArray(taskObj.components)}`);
    
    // 保存找到的sourceImagePanel组件，以防转换过程中丢失
    const savedSourceImagePanel = sourceImagePanelComponent;
    
    // 确保taskObj.components是数组
    if (!Array.isArray(taskObj.components)) {
      // 使用已经转换好的数组
      taskObj.components = componentsArray;
      console.log(`【数据跟踪】已将components转换为数组，长度: ${componentsArray.length}`);
    }
    
    // 检查是否有sourceImagePanel组件
    let hasSourceImagePanel = taskObj.components.some(comp => 
      comp && (comp.componentType === 'sourceImagePanel')
    );
    
    // 如果没有sourceImagePanel组件但有保存的组件数据，尝试添加它
    if (!hasSourceImagePanel && savedSourceImagePanel) {
      console.log(`【数据跟踪】检测到转换过程中丢失了sourceImagePanel组件，正在恢复`);
      
      // 添加sourceImagePanel组件到数组
      taskObj.components.push(savedSourceImagePanel);
      console.log(`【数据跟踪】已恢复sourceImagePanel组件到组件数组`);
      hasSourceImagePanel = true;
    }
    
    // 如果没有imageCount字段或值为0
    if (!taskObj.imageCount) {
      // 如果有images数组，使用其长度
      if (taskObj.images && Array.isArray(taskObj.images) && taskObj.images.length > 0) {
        taskObj.imageCount = taskObj.images.length;
      } 
      // 如果有sourceImagePanel组件，计算数量
      else {
        const sourceImagePanelCount = taskObj.components.filter(comp => 
          comp && (comp.componentType === 'sourceImagePanel')
        ).length;
        
        if (sourceImagePanelCount > 0) {
          taskObj.imageCount = sourceImagePanelCount;
        } else {
          taskObj.imageCount = 1;
        }
      }
    }
    
    // 确保任务有images字段
    if (!taskObj.images || !Array.isArray(taskObj.images) || taskObj.images.length === 0) {
      // 首先尝试从generatedImages字段获取
      if (taskObj.generatedImages && Array.isArray(taskObj.generatedImages) && taskObj.generatedImages.length > 0) {
        console.log(`【数据跟踪】任务 ${taskObj.id || taskObj.taskId} 从generatedImages字段转换为images字段`);
        
        // 转换generatedImages为标准格式的images数组
        taskObj.images = taskObj.generatedImages.map(img => {
          // 如果已经是对象格式
          if (typeof img === 'object') {
            const baseUrl = process.env.REACT_APP_BACKEND_URL;
            return {
              url: img.url ? (img.url.startsWith('http') ? img.url : `${baseUrl}${img.url}`) : 
                   img.path ? (img.path.startsWith('http') ? img.path : `${baseUrl}${img.path}`) : null,
              path: img.path || img.url || null,
              filename: img.filename || null,
              serverFileName: img.filename || null,
              createdAt: img.createdAt || null
            };
          }
          // 如果是字符串（可能是URL或路径）
          return { url: img };
        }).filter(img => img.url); // 确保只保留有url的图片
      } 
      // 如果还没有图片，并且有serverFileName，尝试构建
      else if (taskObj.serverFileName) {
        console.log(`【数据跟踪】任务 ${taskObj.id || taskObj.taskId} 使用serverFileName构建images字段`);
        
        const baseUrl = process.env.REACT_APP_BACKEND_URL;
        const userId = taskObj.userId || 'developer';
        let path = '';
        
        // 根据不同页面类型构建不同的路径
        if (taskObj.pageType === 'try-on') {
          path = `/developer/model/try-on/generated/${taskObj.serverFileName}`;
        } else if (taskObj.pageType === 'matting') {
          path = `/developer/tools/matting/generated/${taskObj.serverFileName}`;
        } else {
          path = `/storage/${userId}/uploads/${taskObj.serverFileName}`;
        }
        
        taskObj.images = [{
          url: `${baseUrl}${path}`,
          path: path,
          filename: taskObj.serverFileName,
          serverFileName: taskObj.serverFileName
        }];
      }
    }

    // 对于抠图页面（matting），确保sourceImagePanel组件存在
    if (taskObj.pageType === 'matting') {
      // 找到所有sourceImagePanel组件，兼容type和componentType两种字段
      const sourceImagePanels = taskObj.components.filter(comp => 
        comp && (comp.componentType === 'sourceImagePanel')
      );
      
      // 如果没有sourceImagePanel组件，创建一个
      if (sourceImagePanels.length === 0) {
        console.error(`【数据错误】matting页面缺少必要的sourceImagePanel组件，任务ID: ${taskObj.taskId}`);
        
        // 最后一次尝试：使用primaryImageFileName创建默认组件
        const primaryImageFileName = taskObj.primaryImageFileName || taskObj.serverFileName;
        
        if (primaryImageFileName) {
          // 构建完整URL
          const baseUrl = process.env.REACT_APP_BACKEND_URL;
          const userId = taskObj.userId || 'developer';
          const url = `${baseUrl}/storage/${userId}/uploads/${primaryImageFileName}`;
          
          // 生成临时componentId
          const componentId = `${Date.now()}_${Math.floor(Math.random() * 1000)}`;
          
          // 创建sourceImagePanel组件
          const newSourceImagePanel = {
            componentType: 'sourceImagePanel',
            componentId: componentId,
            isMainImage: true,
            serverFileName: primaryImageFileName,
            url: url,
            name: '原始图片',
            status: 'completed',
            fileInfo: {
              type: 'sourceImage'
            }
          };
          
          // 添加到组件数组
          taskObj.components.push(newSourceImagePanel);
          
          console.log(`【数据跟踪】创建了sourceImagePanel组件，使用primaryImageFileName: ${primaryImageFileName}`);
          
          // 同时更新任务级别的serverFileName，确保它不为空
          if (!taskObj.serverFileName && primaryImageFileName) {
            taskObj.serverFileName = primaryImageFileName;
            console.log(`【数据跟踪】更新任务级serverFileName: ${primaryImageFileName}`);
          }
        } else {
          // 开发环境下未找到sourceImagePanel组件且无法创建时返回错误
          if (process.env.NODE_ENV === 'development') {
            return res.status(400).json({
              success: false,
              message: '数据不完整: 缺少sourceImagePanel组件',
              errorCode: 'MISSING_COMPONENT',
              taskId: taskObj.taskId
            });
          }
          console.log(`【数据跟踪】无法为matting页面创建sourceImagePanel组件，缺少primaryImageFileName`);
        }
      } else {
        // 确保至少有一个sourceImagePanel被标记为主图片
        let hasMainImage = sourceImagePanels.some(panel => panel.isMainImage);
        
        if (!hasMainImage && sourceImagePanels.length > 0) {
          sourceImagePanels[0].isMainImage = true;
          console.log(`【数据跟踪】已将第一个sourceImagePanel组件标记为主图片`);
        }
        
        // 更新任务级别的serverFileName和primaryImageFileName（如果需要）
        const mainPanel = sourceImagePanels.find(panel => panel.isMainImage) || sourceImagePanels[0];
        if (mainPanel && mainPanel.serverFileName) {
          // 优先使用现有值，如果不存在则从组件更新
          if (!taskObj.serverFileName) {
            taskObj.serverFileName = mainPanel.serverFileName;
            console.log(`【数据跟踪】从主sourceImagePanel组件更新任务级serverFileName: ${mainPanel.serverFileName}`);
          }
          
          if (!taskObj.primaryImageFileName) {
            taskObj.primaryImageFileName = mainPanel.serverFileName;
            console.log(`【数据跟踪】从主sourceImagePanel组件更新任务级primaryImageFileName: ${mainPanel.serverFileName}`);
          }
        }
      }
    }

    // 最终确认matting页面有sourceImagePanel组件
    if (taskObj.pageType === 'matting') {
      const finalSourceImagePanels = taskObj.components.filter(comp => 
        comp && (comp.componentType === 'sourceImagePanel')
      );
      
      if (finalSourceImagePanels.length === 0) {
        console.error(`【严重错误】matting任务最终检查时仍然缺少sourceImagePanel组件，任务ID: ${taskObj.taskId}`);
        
        // 开发环境下应该立即知道这个问题
        if (process.env.NODE_ENV === 'development') {
          console.trace(`【堆栈跟踪】`);
          
          // 开发环境下返回错误
          return res.status(400).json({
            success: false,
            message: '最终检查数据不完整: 缺少sourceImagePanel组件',
            errorCode: 'MISSING_COMPONENT',
            taskId: taskObj.taskId,
            components: taskObj.components
          });
        }
      } else {
        console.log(`【数据跟踪】最终检查通过，找到${finalSourceImagePanels.length}个sourceImagePanel组件`);
      }
    }

    // 最终确保返回的数据包含serverFileName和primaryImageFileName字段
    if (!taskObj.serverFileName || !taskObj.primaryImageFileName) {
      console.log(`【数据跟踪】最终检查: 任务缺少顶层文件名字段，尝试从组件同步`);
      
      // 查找主图片组件或第一个图片组件
      let mainComponent = null;
      if (Array.isArray(taskObj.components) && taskObj.components.length > 0) {
        // 先找标记为主图片的组件
        mainComponent = taskObj.components.find(comp => comp && comp.isMainImage);
        
        // 如果没有找到，尝试找sourceImagePanel组件
        if (!mainComponent) {
          mainComponent = taskObj.components.find(comp => 
            comp && comp.componentType === 'sourceImagePanel'
          );
        }
        
        // 如果还没找到，使用第一个组件
        if (!mainComponent) {
          mainComponent = taskObj.components[0];
        }
        
        // 如果找到了主组件且有serverFileName，同步到顶层字段
        if (mainComponent && mainComponent.serverFileName) {
          if (!taskObj.serverFileName) {
            taskObj.serverFileName = mainComponent.serverFileName;
            console.log(`【数据跟踪】同步主组件serverFileName到顶层: ${mainComponent.serverFileName}`);
          }
          
          if (!taskObj.primaryImageFileName) {
            taskObj.primaryImageFileName = mainComponent.serverFileName;
            console.log(`【数据跟踪】同步主组件serverFileName到primaryImageFileName: ${mainComponent.serverFileName}`);
          }
        }
      }
    }

    // 响应前的最终数据检查
    console.log(`【数据跟踪】响应前的最终数据检查:`);
    console.log(`- primaryImageFileName: ${taskObj.primaryImageFileName || '未设置'}`);
    console.log(`- serverFileName: ${taskObj.serverFileName || '未设置'}`);
    console.log(`- 任务图片数量: ${taskObj.images ? taskObj.images.length : 0}`);
    console.log(`- components类型: ${typeof taskObj.components}, 是否数组: ${Array.isArray(taskObj.components)}`);

    console.log(`===== 【数据跟踪】返回任务数据 =====`);
    
    return res.json({
      success: true,
      data: taskObj
    });
  } catch (error) {
    console.error('获取任务详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务详情失败'
    });
  }
});

// 删除任务
router.delete('/:taskId', selectAuth, async (req, res) => {
  try {
    console.log(`尝试删除任务: ${req.params.taskId}, 用户: ${req.user._id}`);
    console.log(`请求查询参数:`, req.query);
    
    // 检查是否为调试模式
    const isDebugMode = req.query.debug === 'true' && process.env.NODE_ENV === 'development';
    console.log(`调试模式: ${isDebugMode}`);
    
    // 先检查数据库中是否存在此任务（不带用户ID限制）
    const allTasksWithId = await Task.find({ taskId: req.params.taskId });
    console.log(`数据库中总共找到 ${allTasksWithId.length} 个匹配taskId的任务`);
    
    if (allTasksWithId.length > 0) {
      console.log(`匹配的任务详情:`, JSON.stringify(allTasksWithId.map(t => ({
        id: t._id.toString(),
        taskId: t.taskId,
        userId: t.userId ? t.userId.toString() : 'undefined',
        status: t.status
      }))));
    }
    
    // 构建查询条件，使用当前用户ID
    const query = { 
      taskId: req.params.taskId,
      userId: req.user._id 
    };
    
    // 在调试模式下，可以不限制用户ID
    if (isDebugMode && process.env.NODE_ENV === 'development') {
      delete query.userId;
      console.log('调试模式下不限制用户ID');
    }
    
    console.log('最终任务查询条件:', JSON.stringify(query));
    const task = await Task.findOne(query);
    
    // 如果找不到任务但在调试模式下，尝试放宽条件
    if (!task && isDebugMode && allTasksWithId.length > 0) {
      console.log('在调试模式下尝试放宽查询条件');
      // 直接使用第一个找到的任务
      const firstTask = allTasksWithId[0];
      console.log(`使用找到的第一个任务: ${firstTask.taskId}`);
      return await handleTaskDeletion(firstTask, req, res);
    }
    
    if (!task) {
      console.log(`未找到任务: ${req.params.taskId}`);
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }
    
    console.log(`找到任务: ${task.taskId}`);
    return await handleTaskDeletion(task, req, res);
    
  } catch (error) {
    console.error('删除任务失败:', error);
    res.status(500).json({
      success: false,
      message: '删除任务失败'
    });
  }
});

// 路径标准化函数，确保正确的格式
function normalizePath(path, userId) {
  if (!path) return null;
  
  // 使用传入的userId或null
  // 注意：这里不能用req.user._id，因为函数可能在多个上下文中调用
  
  // 多重替换，统一路径格式
  let normalizedPath = path.replace(/\\/g, '/');
  
  // 如果路径以/storage开头但没有用户ID，插入用户ID
  if (userId && normalizedPath.startsWith('/storage/') && !normalizedPath.includes(`/storage/${userId}/`)) {
    normalizedPath = normalizedPath.replace('/storage/', `/storage/${userId}/`);
  }
  
  // 路径映射规则，根据任务类型标识符修正错误的目录
  const pathMappings = [
    // 模特相关服务路径修正
    { marker: ['tryonauto_', '/try-on/'], wrongPath: '/tools/matting/generated/', correctPath: '/model/try-on/generated/' },
    { marker: ['fashion_', '/fashion/'], wrongPath: '/tools/matting/generated/', correctPath: '/model/fashion/generated/' },
    { marker: ['recolor_', '/recolor/'], wrongPath: '/tools/matting/generated/', correctPath: '/model/recolor/generated/' },
    { marker: ['fabric_', '/fabric/'], wrongPath: '/tools/matting/generated/', correctPath: '/model/fabric/generated/' },
    { marker: ['background_', '/background/'], wrongPath: '/tools/matting/generated/', correctPath: '/model/background/generated/' },
    { marker: ['virtual_', '/virtual/'], wrongPath: '/tools/matting/generated/', correctPath: '/model/virtual/generated/' },
    
    // 款式服务相关路径修正
    { marker: ['optimize_', '/optimize/'], wrongPath: '/tools/matting/generated/', correctPath: '/style/optimize/generated/' },
    { marker: ['inspiration_', '/inspiration/'], wrongPath: '/tools/matting/generated/', correctPath: '/style/inspiration/generated/' },
    { marker: ['trending_', '/trending/'], wrongPath: '/tools/matting/generated/', correctPath: '/style/trending/generated/' },
    
    // 工具相关服务路径修正
    { marker: ['extract_', '/extract/'], wrongPath: '/tools/matting/generated/', correctPath: '/tools/extract/generated/' },
    { marker: ['upscale_', '/upscale/'], wrongPath: '/tools/matting/generated/', correctPath: '/tools/upscale/generated/' },
    { marker: ['extend_', '/extend/'], wrongPath: '/tools/matting/generated/', correctPath: '/tools/extend/generated/' }
  ];
  
  // 遍历路径映射规则，检查并修正路径
  for (const { marker, wrongPath, correctPath } of pathMappings) {
    // 检查路径是否包含标识符之一
    const hasMarker = marker.some(mark => normalizedPath.includes(mark));
    
    // 如果包含标识符并且路径错误，进行修正
    if (hasMarker && normalizedPath.includes(wrongPath)) {
      console.log(`修正路径: 从${wrongPath}改为${correctPath}`);
      normalizedPath = normalizedPath.replace(wrongPath, correctPath);
      break; // 找到匹配项后停止遍历
    }
  }
  
  return normalizedPath;
}

// 抽取任务删除逻辑到单独的函数
async function handleTaskDeletion(task, req, res) {
  try {
    // 导入Upload模型
    const Upload = require('../models/Upload');
    
    // 查找与此任务关联的所有上传文件
    const relatedFiles = await Upload.find({
      taskId: task.taskId
    });
    
    console.log(`找到与任务[${task.taskId}]关联的文件: ${relatedFiles.length}个`);
    
    // 删除关联的文件
    for (const file of relatedFiles) {
      try {
        // 构建可能的文件路径
        const userId = process.env.NODE_ENV === 'development' && file.userId === 'developer' 
          ? 'developer' 
          : file.userId.toString();
        const baseDir = path.resolve(__dirname, '../../storage');
        
        // 获取任务类型和页面类型
        const taskType = task.taskType || '';
        const pageType = task.pageType || '';
        
        // 基于不同功能页面和任务类型构建可能的文件路径
        const possiblePaths = [
          // 基本上传目录
          path.join(baseDir, userId, 'uploads', file.filename),
          
          // 模特相关目录
          path.join(baseDir, userId, 'model', 'fashion', 'generated', file.filename),
          path.join(baseDir, userId, 'model', 'try-on', 'mask', file.filename),
          path.join(baseDir, userId, 'model', 'try-on', 'generated', file.filename),
          path.join(baseDir, userId, 'model', 'recolor', 'generated', file.filename),
          path.join(baseDir, userId, 'model', 'fabric', 'generated', file.filename),
          path.join(baseDir, userId, 'model', 'background', 'generated', file.filename),
          path.join(baseDir, userId, 'model', 'virtual', 'generated', file.filename),
          
          // 款式服务相关目录
          path.join(baseDir, userId, 'style', 'optimize', 'mask', file.filename),
          path.join(baseDir, userId, 'style', 'optimize', 'generated', file.filename),
          path.join(baseDir, userId, 'style', 'inspiration', 'generated', file.filename),
          path.join(baseDir, userId, 'style', 'trending', 'generated', file.filename),
          
          // 工具相关目录
          path.join(baseDir, userId, 'tools', 'extract', 'generated', file.filename),
          path.join(baseDir, userId, 'tools', 'upscale', 'generated', file.filename),
          path.join(baseDir, userId, 'tools', 'matting', 'generated', file.filename),
          path.join(baseDir, userId, 'tools', 'extend', 'generated', file.filename)
        ];
        
        // 尝试删除文件
        let fileDeleted = false;
        for (const filePath of possiblePaths) {
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`已删除关联文件: ${filePath}`);
            fileDeleted = true;
          }
        }
        
        if (!fileDeleted) {
          console.log(`未找到文件: ${file.filename}`);
        }
        
        // 从数据库中删除记录
        await Upload.deleteOne({ _id: file._id });
      } catch (err) {
        console.error(`删除关联文件失败: ${file.filename}`, err);
        // 继续处理其他文件
      }
    }
    
    // 从任务的components中获取上传文件信息并删除
    if (task.components) {
      try {
        // 从组件中查找上传文件
        let serverFileName = '';
        
        // 查找包含serverFileName的组件
        for (const [key, component] of Object.entries(task.components)) {
          if (component.serverFileName) {
            serverFileName = component.serverFileName;
            console.log(`任务中发现上传文件: ${serverFileName}`);
            break;
          }
        }
        
        // 如果没有从组件找到，尝试使用任务的primaryImageFileName
        if (!serverFileName && task.primaryImageFileName) {
          serverFileName = task.primaryImageFileName;
          console.log(`使用任务primaryImageFileName: ${serverFileName}`);
        }
        
        if (serverFileName) {
          // 构建可能的文件路径
          const userId = process.env.NODE_ENV === 'development' && task.userId === 'developer' 
            ? 'developer' 
            : task.userId.toString();
          const baseDir = path.resolve(__dirname, '../../storage');
          
          // 尝试在uploads目录和其他可能的目录中删除文件
          const possiblePaths = [
            // 基本上传目录
            path.join(baseDir, userId, 'uploads', serverFileName),
            
            // 如果文件被移动到了其他目录
            path.join(baseDir, userId, 'model', 'try-on', 'mask', serverFileName)
          ];
          
          // 尝试删除文件
          let fileDeleted = false;
          for (const filePath of possiblePaths) {
            if (fs.existsSync(filePath)) {
              fs.unlinkSync(filePath);
              console.log(`已删除任务关联的上传文件: ${filePath}`);
              fileDeleted = true;
            }
          }
          
          if (!fileDeleted) {
            console.log(`未找到上传文件: ${serverFileName}`);
          }
        }
      } catch (err) {
        console.error('删除任务关联的上传文件失败:', err);
      }
    }

    // 删除生成的图片文件
    if (task.results && Array.isArray(task.results)) {
      for (const result of task.results) {
        try {
          // 构建可能的文件路径
          const userId = process.env.NODE_ENV === 'development' && task.userId === 'developer' 
            ? 'developer' 
            : task.userId.toString();
          const baseDir = path.resolve(__dirname, '../../storage');
          
          // 处理绝对路径和相对路径
          if (result.path) {
            deleteFileWithPath(result.path, baseDir, userId);
          } else if (result.filename) {
            // 基于不同功能页面和任务类型构建可能的文件路径
            tryDeleteFileInAllPossibleLocations(baseDir, userId, task.pageType, task.taskType, result.filename);
          }
        } catch (err) {
          console.error('删除图片文件失败:', err);
        }
      }
    }
    
    // 兼容旧版本task.generatedImages
    if (task.generatedImages && Array.isArray(task.generatedImages)) {
      console.log(`找到与任务[${task.taskId}]关联的生成图片: ${task.generatedImages.length}个`);
      
      // 记录需要删除的文件总数和已删除数量
      let totalFilesToDelete = task.generatedImages.length;
      let filesDeleted = 0;
      
      for (const image of task.generatedImages) {
        try {
          // 构建可能的文件路径
          const userId = process.env.NODE_ENV === 'development' && task.userId === 'developer' 
            ? 'developer' 
            : task.userId.toString();
          const baseDir = path.resolve(__dirname, '../../storage');
          
          // 处理文件路径 - 先尝试使用path属性
          if (image.path) {
            const deleted = deleteFileWithPath(image.path, baseDir, userId);
            if (deleted) {
              filesDeleted++;
              continue;
            }
          }
          
          // 如果path不存在或删除失败，尝试使用filename
          if (image.filename) {
            const deleted = tryDeleteFileInAllPossibleLocations(baseDir, userId, task.pageType, task.taskType, image.filename);
            if (deleted) {
              filesDeleted++;
              continue;
            }
          }
          
          console.log(`未找到生成的图片文件: ${image.filename || '未知文件名'} (path: ${image.path || '无路径'})`);
        } catch (err) {
          console.error('删除图片文件失败:', err);
        }
      }
      
      console.log(`任务[${task.taskId}]生成文件删除结果: ${filesDeleted}/${totalFilesToDelete}`);
    }

    // 删除任务记录
    try {
      await Task.deleteOne({ _id: task._id });
      console.log(`已从数据库中删除任务: ${task.taskId}`);
    } catch (dbError) {
      console.error('从数据库删除任务记录失败:', dbError);
      throw dbError;
    }

    res.json({
      success: true,
      message: '任务及其关联文件已删除'
    });
  } catch (error) {
    console.error('处理任务删除过程中出错:', error);
    res.status(500).json({
      success: false,
      message: '删除任务失败'
    });
  }
}

// 辅助函数：删除指定路径的文件
function deleteFileWithPath(filePath, baseDir, userId) {
  if (!filePath) return false;
  
  let absolutePath = '';
  
  // 标准化路径
  const normalizedPath = normalizePath(filePath, userId);
  
  // 处理标准化后的路径
  if (normalizedPath.startsWith('/storage/')) {
    // 将相对路径转换为绝对路径
    const relativePath = normalizedPath.slice('/storage/'.length);
    absolutePath = path.join(baseDir, relativePath);
  } else {
    // 尝试直接使用路径
    absolutePath = filePath;
  }
  
  // 尝试删除文件
  if (fs.existsSync(absolutePath)) {
    fs.unlinkSync(absolutePath);
    console.log(`已删除生成的图片文件(基于path): ${absolutePath}`);
    return true;
  } else {
    console.log(`未找到生成的图片文件(基于path): ${absolutePath}`);
  }
  
  // 如果原始路径和标准化路径不同，再尝试原始路径
  if (filePath !== normalizedPath && fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`已删除生成的图片文件(使用原始路径): ${filePath}`);
    return true;
  }
  
  return false;
}

// 辅助函数：在所有可能的位置尝试删除文件
function tryDeleteFileInAllPossibleLocations(baseDir, userId, pageType, taskType, filename) {
  if (!filename) return false;
  
  // 根据页面类型确定优先路径
  let priorityPath = '';
  
  if (pageType === 'matting' || pageType?.includes('matting')) {
    priorityPath = path.join(baseDir, userId, 'tools', 'matting', 'generated', filename);
  } else if (pageType === 'fashion' || pageType?.includes('fashion')) {
    priorityPath = path.join(baseDir, userId, 'model', 'fashion', 'generated', filename);
  } else if (pageType === 'try-on' || pageType?.includes('try-on')) {
    priorityPath = path.join(baseDir, userId, 'model', 'try-on', 'generated', filename);
  } else if (pageType === 'recolor' || pageType?.includes('recolor')) {
    priorityPath = path.join(baseDir, userId, 'model', 'recolor', 'generated', filename);
  } else if (pageType === 'fabric' || pageType?.includes('fabric')) {
    priorityPath = path.join(baseDir, userId, 'model', 'fabric', 'generated', filename);
  } else if (pageType === 'background' || pageType?.includes('background')) {
    priorityPath = path.join(baseDir, userId, 'model', 'background', 'generated', filename);
  } else if (pageType === 'virtual' || pageType?.includes('virtual')) {
    priorityPath = path.join(baseDir, userId, 'model', 'virtual', 'generated', filename);
  } else if (pageType === 'optimize' || pageType?.includes('optimize')) {
    priorityPath = path.join(baseDir, userId, 'style', 'optimize', 'generated', filename);
  } else if (pageType === 'inspiration' || pageType?.includes('inspiration')) {
    priorityPath = path.join(baseDir, userId, 'style', 'inspiration', 'generated', filename);
  } else if (pageType === 'trending' || pageType?.includes('trending')) {
    priorityPath = path.join(baseDir, userId, 'style', 'trending', 'generated', filename);
  } else if (pageType === 'extract' || pageType?.includes('extract')) {
    priorityPath = path.join(baseDir, userId, 'tools', 'extract', 'generated', filename);
  } else if (pageType === 'upscale' || pageType?.includes('upscale')) {
    priorityPath = path.join(baseDir, userId, 'tools', 'upscale', 'generated', filename);
  } else if (pageType === 'extend' || pageType?.includes('extend')) {
    priorityPath = path.join(baseDir, userId, 'tools', 'extend', 'generated', filename);
  }
  
  // 优先尝试基于当前任务类型构建的路径
  if (priorityPath && fs.existsSync(priorityPath)) {
    fs.unlinkSync(priorityPath);
    console.log(`已删除生成的图片文件(基于任务类型): ${priorityPath}`);
    return true;
  }
  
  // 构建所有可能的路径
  const possiblePaths = [
    // 模特相关目录
    path.join(baseDir, userId, 'model', 'fashion', 'generated', filename),
    path.join(baseDir, userId, 'model', 'try-on', 'generated', filename),
    path.join(baseDir, userId, 'model', 'recolor', 'generated', filename),
    path.join(baseDir, userId, 'model', 'fabric', 'generated', filename),
    path.join(baseDir, userId, 'model', 'background', 'generated', filename),
    path.join(baseDir, userId, 'model', 'virtual', 'generated', filename),
    
    // 款式服务相关目录
    path.join(baseDir, userId, 'style', 'optimize', 'generated', filename),
    path.join(baseDir, userId, 'style', 'inspiration', 'generated', filename),
    path.join(baseDir, userId, 'style', 'trending', 'generated', filename),
    
    // 工具相关目录
    path.join(baseDir, userId, 'tools', 'extract', 'generated', filename),
    path.join(baseDir, userId, 'tools', 'upscale', 'generated', filename),
    path.join(baseDir, userId, 'tools', 'matting', 'generated', filename),
    path.join(baseDir, userId, 'tools', 'extend', 'generated', filename)
  ];
  
  // 尝试删除其他可能的路径
  for (const filePath of possiblePaths) {
    if (filePath && fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`已删除生成的图片文件: ${filePath}`);
      return true;
    }
  }
  
  return false;
}

// 添加保存任务的路由
router.post('/save', selectAuth, async (req, res) => {
  try {
    const { 
      taskId, userId, status, taskType, pageType, imageType, 
      results, imageCount, components, requiresImage, primaryImageFileName, mainServerFileName, serverFileName
    } = req.body;
    
    // 显式移除req.body中的空id字段，防止MongoDB唯一索引冲突
    if (req.body.id === null || req.body.id === undefined) {
      console.log('检测到空的id字段，将其移除以避免MongoDB唯一索引冲突');
      delete req.body.id;
    }
    
    console.log('==== 接收到保存任务请求 ====');
    console.log('任务基本信息:', { taskId, userId, status, taskType, pageType, imageType, imageCount, requiresImage });
    console.log('图片文件名:', { primaryImageFileName, mainServerFileName, serverFileName });
    
    // 检查是否提供了组件数据
    if (!components) {
      return res.status(400).json({
        success: false,
        message: '缺少必要的组件数据'
      });
    }
    
    // 处理组件数据，确保是平坦的对象数组结构
    let processedComponents = components;
    
    // 如果components是对象而不是数组，转换为数组
    if (!Array.isArray(components) && typeof components === 'object') {
      console.log('将组件对象转换为数组格式');
      processedComponents = Object.values(components);
    }
    
    // 如果数组中存在嵌套对象，将其扁平化
    if (Array.isArray(processedComponents)) {
      console.log('处理组件数组，确保是扁平结构');
      processedComponents = processedComponents.map(item => {
        // 如果是对象，且有数字键指向嵌套对象
        if (item && typeof item === 'object') {
          // 检查是否有数字键，如果有，取第一个数字键的值
          const numericKeys = Object.keys(item).filter(key => !isNaN(Number(key)));
          if (numericKeys.length > 0 && typeof item[numericKeys[0]] === 'object') {
            console.log(`检测到嵌套组件结构，对组件项进行扁平化处理`);
            return item[numericKeys[0]];
          }
        }
        return item;
      }).filter(item => item && typeof item === 'object'); // 确保所有项都是有效对象
    } else {
      // 如果不是数组也不是对象，初始化为空数组
      console.log('组件数据格式无效，初始化为空数组');
      processedComponents = [];
    }
    
    console.log('组件数据:', {
      componentCount: processedComponents.length,
      componentTypes: processedComponents.map(comp => comp.componentType || 'unknown')
    });
    
    // 验证必要参数
    if (!taskId || !status) {
      console.error('缺少必要参数:', { taskId, status });
      return res.status(400).json({
        success: false, 
        message: '缺少必要参数'
      });
    }
    
    // 检查任务是否已存在
    let task = await Task.findOne({ taskId });
    
    if (task) {
      console.log(`任务已存在，更新任务: ${taskId}`);
      // 更新任务
      task.status = status;
      
      // 更新任务类型相关字段（只在有值的情况下更新）
      if (taskType) task.taskType = taskType;
      if (pageType) task.pageType = pageType;
      if (imageType) task.imageType = imageType;
      
      // 更新是否需要图片标志
      if (requiresImage !== undefined) {
        task.requiresImage = requiresImage;
      }
      
      // 如果提供了imageCount，更新它
      if (imageCount) {
        task.imageCount = imageCount;
      }
      
      // 保存图片文件名信息，优先使用primaryImageFileName
      if (primaryImageFileName) {
        console.log(`更新任务primaryImageFileName: ${primaryImageFileName}`);
        task.primaryImageFileName = primaryImageFileName;
        // 同时更新serverFileName
        task.serverFileName = primaryImageFileName;
      } else if (mainServerFileName) {
        console.log(`使用mainServerFileName更新primaryImageFileName: ${mainServerFileName}`);
        task.primaryImageFileName = mainServerFileName;
        // 同时更新serverFileName
        task.serverFileName = mainServerFileName;
      } else if (serverFileName) {
        console.log(`使用serverFileName更新primaryImageFileName: ${serverFileName}`);
        task.primaryImageFileName = serverFileName;
        // serverFileName已存在，无需再次更新
      }
      
      // 使用处理后的组件数据
      if (processedComponents.length > 0) {
        console.log('更新任务组件数据，使用单层数组结构');
        task.components = processedComponents;
        
        // 确保至少有一个组件被标记为主图片
        if (task.requiresImage !== false) {
          let hasMainImage = false;
          
          // 检查是否已有主图片
          for (const component of task.components) {
            if (component.isMainImage) {
              hasMainImage = true;
              // 如果这个组件有serverFileName，更新task.primaryImageFileName和serverFileName
              if (component.serverFileName) {
                task.primaryImageFileName = component.serverFileName;
                task.serverFileName = component.serverFileName;
              }
              break;
            }
          }
          
          // 如果没有主图片但有组件，将第一个适合的组件设为主图片
          if (!hasMainImage && task.components.length > 0) {
            // 寻找第一个图片相关组件
            for (let i = 0; i < task.components.length; i++) {
              const component = task.components[i];
              if (component.componentType && 
                 (component.componentType.includes('Panel') || 
                  component.url || 
                  component.serverFileName)) {
                console.log(`设置组件索引 ${i} 为主图片`);
                task.components[i] = {
                  ...component,
                  isMainImage: true
                };
                
                // 如果主图片有serverFileName，确保更新primaryImageFileName
                if (component.serverFileName && !task.primaryImageFileName) {
                  task.primaryImageFileName = component.serverFileName;
                  task.serverFileName = component.serverFileName;
                }
                break;
              }
            }
          }
        }
      }
      
      console.log('更新后的任务详情:', {
        taskId,
        taskType: task.taskType,
        pageType: task.pageType,
        imageType: task.imageType,
        primaryImageFileName: task.primaryImageFileName,
        serverFileName: task.serverFileName,
        componentsCount: task.components ? task.components.length : 0
      });
      
      // 处理生成的图像结果
      if (results && Array.isArray(results)) {
        console.log(`添加${results.length}个生成图像结果`);
        // 如果有新的结果，添加到生成图像数组
        results.forEach((result, index) => {
          if (result.path || result.filename) {
            console.log(`添加结果 ${index+1}:`, {
              filename: result.filename || result.path?.split('/').pop(),
              path: result.path
            });
            
            // 标准化路径以确保一致性
            const normalizedPath = normalizePath(result.path, req.user._id);
            
            task.generatedImages.push({
              filename: result.filename || result.path?.split('/').pop(),
              path: normalizedPath, // 使用标准化路径
              createdAt: new Date()
            });
          }
        });
      }
      
      task.updatedAt = new Date();
      await task.save();
      console.log(`任务 ${taskId} 已成功更新并保存到数据库`);
      
      return res.json({
        success: true,
        message: '任务更新成功',
        data: task
      });
    } else {
      console.log(`创建新任务: ${taskId}`);
      
      // 获取正确的图片文件名
      const finalImageFileName = primaryImageFileName || mainServerFileName || serverFileName;
      
      // 创建任务基本结构
      const taskBase = {
        taskId,
        userId: req.user._id,  // 使用当前用户ID
        status,
        taskType: taskType,  // 保存taskType
        pageType: pageType || 'matting',  // 保存pageType，默认为matting
        imageType: imageType || 'original',  // 保存imageType，默认为original
        requiresImage: requiresImage !== undefined ? requiresImage : true, // 默认需要图片
        primaryImageFileName: finalImageFileName, // 使用整合后的图片文件名
        serverFileName: finalImageFileName, // 同步设置serverFileName
        imageCount: imageCount || 1, // 如果没有提供，默认为1
        generatedImages: [],
        components: processedComponents // 使用处理后的单层数组组件数据
      };
      
      // 如果请求中提供了有效的id字段，也保存它
      if (req.body.id && typeof req.body.id === 'string' && req.body.id.trim() !== '') {
        console.log(`使用请求中提供的id字段: ${req.body.id}`);
        taskBase.id = req.body.id;
      }
      
      // 当有组件数据但没有主图片时，尝试设置一个主图片
      if (taskBase.components.length > 0 && taskBase.requiresImage) {
        let hasMainImage = false;
        
        // 检查是否已有主图片
        for (const component of taskBase.components) {
          if (component.isMainImage) {
            hasMainImage = true;
            // 如果主图片有serverFileName，确保更新primaryImageFileName
            if (component.serverFileName && !taskBase.primaryImageFileName) {
              taskBase.primaryImageFileName = component.serverFileName;
              taskBase.serverFileName = component.serverFileName;
            }
            break;
          }
        }
        
        // 如果没有主图片，将第一个图片组件设为主图片
        if (!hasMainImage) {
          // 寻找第一个图片组件
          for (let i = 0; i < taskBase.components.length; i++) {
            const component = taskBase.components[i];
            if (component.componentType && 
               (component.componentType.includes('Panel') || 
                component.url || 
                component.serverFileName)) {
              console.log(`将组件索引 ${i} 设置为主图片`);
              taskBase.components[i] = {
                ...component,
                isMainImage: true
              };
              
              // 如果主图片有serverFileName，确保更新primaryImageFileName
              if (component.serverFileName && !taskBase.primaryImageFileName) {
                taskBase.primaryImageFileName = component.serverFileName;
                taskBase.serverFileName = component.serverFileName;
              }
              break;
            }
          }
        }
      }
      
      // 创建新任务对象，不再包含settings字段
      const newTask = new Task(taskBase);
      
      console.log('新任务详情:', {
        taskId,
        userId: req.user._id,
        taskType,
        pageType,
        imageType,
        primaryImageFileName: newTask.primaryImageFileName,
        componentsCount: newTask.components ? newTask.components.length : 0
      });
      
      // 处理生成的图像结果
      if (results && Array.isArray(results)) {
        console.log(`添加${results.length}个生成图像结果`);
        results.forEach((result, index) => {
          if (result.path || result.filename) {
            console.log(`添加结果 ${index+1}:`, {
              filename: result.filename || result.path?.split('/').pop(),
              path: result.path
            });
            
            // 标准化路径以确保一致性
            const normalizedPath = normalizePath(result.path, req.user._id);
            
            newTask.generatedImages.push({
              filename: result.filename || result.path?.split('/').pop(),
              path: normalizedPath, // 使用标准化路径
              createdAt: new Date()
            });
          }
        });
      }
      
      try {
        await newTask.save();
        console.log(`新任务已保存到数据库: ${taskId}`);
        
        return res.json({
          success: true,
          message: '任务创建成功',
          data: newTask
        });
      } catch (saveError) {
        // 如果是唯一键冲突错误，提供更详细的错误信息
        if (saveError.code === 11000) {
          console.error('保存任务时发生唯一键冲突:', saveError);
          // 尝试确定是哪个字段冲突
          const errorMessage = saveError.message || '';
          const conflictField = errorMessage.includes('id') ? 'id' : 
                               errorMessage.includes('taskId') ? 'taskId' : '未知字段';
          
          return res.status(409).json({
            success: false,
            message: `任务保存失败：${conflictField}字段冲突`,
            error: `唯一键冲突：${conflictField}已存在`,
            code: 11000,
            field: conflictField
          });
        }
        
        // 其他错误直接抛出，由下面的catch处理
        throw saveError;
      }
    }
  } catch (error) {
    console.error('保存任务失败:', error);
    res.status(500).json({
      success: false,
      message: '保存任务失败',
      error: error.message
    });
  }
});

module.exports = router; 