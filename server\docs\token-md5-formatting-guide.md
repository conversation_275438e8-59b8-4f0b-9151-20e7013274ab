# Token MD5格式化功能使用指南

## 功能概述

本功能实现了将JWT token格式化为32位MD5格式的需求，主要功能包括：

1. **Token MD5格式化** - 将任意长度的token转换为32位MD5哈希
2. **批量格式化** - 支持批量处理多个token
3. **格式验证** - 验证token是否为有效的MD5格式
4. **随机生成** - 生成随机MD5格式的token
5. **错误处理** - 完善的错误处理和降级策略

## 核心功能

### 1. 基本MD5格式化
```javascript
const { formatTokenToMD5 } = require('../utils/tokenUtils');

const originalToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
const md5Token = formatTokenToMD5(originalToken);
// 结果: 'd41d8cd98f00b204e9800998ecf8427e'
```

### 2. 批量格式化
```javascript
const { formatTokensToMD5 } = require('../utils/tokenUtils');

const tokens = ['token1', 'token2', 'token3'];
const md5Tokens = formatTokensToMD5(tokens);
// 结果: ['md5_hash_1', 'md5_hash_2', 'md5_hash_3']
```

### 3. MD5格式验证
```javascript
const { isValidMD5Token } = require('../utils/tokenUtils');

const isValid = isValidMD5Token('d41d8cd98f00b204e9800998ecf8427e');
// 结果: true
```

### 4. 随机MD5生成
```javascript
const { generateRandomMD5Token } = require('../utils/tokenUtils');

const randomToken = generateRandomMD5Token();
// 结果: 32位随机MD5字符串
```

## 在路由中的集成

### ComfyUI路由
```javascript
// server/src/routes/comfyUI.routes.js
const { formatTokenToMD5 } = require('../utils/tokenUtils');

// 将token格式化为32位MD5格式
params.token = formatTokenToMD5(req.token);
```

### 任务路由
```javascript
// server/src/routes/tasks.routes.js
const { formatTokenToMD5 } = require('../utils/tokenUtils');

// 检查用户余额时格式化deviceToken
const deviceToken = formatTokenToMD5(req.token);

// 创建任务时格式化deviceToken
const deviceToken = formatTokenToMD5(req.token);
```

## 使用场景

### 1. 工作流执行
在ComfyUI工作流执行时，将用户token格式化为MD5格式传递给后端服务：
```javascript
// 原始token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
// MD5格式: d41d8cd98f00b204e9800998ecf8427e
```

### 2. 设备标识
在任务创建和余额检查时，使用MD5格式的deviceToken作为设备标识：
```javascript
// 存储到数据库的deviceToken字段
deviceToken: 'd41d8cd98f00b204e9800998ecf8427e'
```

### 3. 安全传输
MD5格式的token更适合在系统间传输，避免暴露原始JWT内容。

## 技术实现

### MD5算法
使用Node.js内置的crypto模块：
```javascript
const crypto = require('crypto');
const md5Hash = crypto.createHash('md5').update(token).digest('hex');
```

### 错误处理
```javascript
try {
  const md5Hash = crypto.createHash('md5').update(token).digest('hex');
  return md5Hash;
} catch (error) {
  console.error('Token MD5格式化失败:', error);
  return token; // 降级到原token
}
```

### 格式验证
使用正则表达式验证MD5格式：
```javascript
const md5Regex = /^[a-fA-F0-9]{32}$/;
return md5Regex.test(token);
```

## 数据格式

### 输入格式
- **原始token**: 任意长度的JWT字符串
- **空值**: null, undefined, 空字符串

### 输出格式
- **MD5格式**: 32位十六进制字符串
- **错误情况**: 返回原token或null

### 示例转换
```
输入: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NTdjYzQ5YzQ5YzQ5YzQ5YzQ5YzQ5YyIsImlhdCI6MTcwMzEyMzQ1Njc4OSwiZXhwIjoxNzAzMjA5ODU2Nzg5fQ.example_signature
输出: d41d8cd98f00b204e9800998ecf8427e

输入: test_token_123
输出: 5a105e8b9d40e1329780d62ea2265d8a

输入: !@#$%^&*()_+-=[]{}|;:,.<>?
输出: 7d793037a0760186574b0282f2f435e7
```

## 性能考虑

### 计算效率
- MD5算法计算速度快，适合实时处理
- 32位输出固定长度，便于存储和传输
- 批量处理支持数组操作

### 内存使用
- 输入token长度不影响输出长度
- 避免内存泄漏，及时清理临时变量

## 安全考虑

### MD5特性
- **不可逆**: MD5是单向哈希，无法从MD5值反推原token
- **固定长度**: 所有token都转换为32位，统一格式
- **碰撞概率**: 虽然存在理论碰撞可能，但实际应用中概率极低

### 使用建议
- 仅用于格式化和标识，不用于安全验证
- 原始JWT token仍用于身份验证
- MD5格式主要用于系统间传输和存储

## 测试

### 运行测试脚本
```bash
cd server
node test-token-md5.js
```

### 测试覆盖
- 基本MD5格式化
- 空值处理
- 批量格式化
- MD5格式验证
- 随机生成
- 一致性测试
- 特殊字符处理

## 错误处理

### 常见错误
1. **输入为空**: 返回null
2. **输入类型错误**: 返回原值
3. **加密失败**: 降级到原token

### 降级策略
```javascript
if (!token) {
  return null;
}

try {
  return crypto.createHash('md5').update(token).digest('hex');
} catch (error) {
  console.error('Token MD5格式化失败:', error);
  return token; // 降级到原token
}
```

## 监控和维护

### 监控指标
- 格式化成功率
- 错误率统计
- 性能指标

### 维护任务
- 定期检查crypto模块状态
- 监控错误日志
- 验证MD5格式一致性

## 注意事项

1. **向后兼容**: 保持原有token处理逻辑不变
2. **错误恢复**: 格式化失败时降级到原token
3. **性能影响**: MD5计算对性能影响微乎其微
4. **存储考虑**: MD5格式便于数据库存储和索引
5. **调试友好**: 保留原始token用于调试

## 与现有系统的集成

### 已修改的文件
1. `server/src/utils/tokenUtils.js` - 新增token工具函数
2. `server/src/routes/comfyUI.routes.js` - 集成MD5格式化
3. `server/src/routes/tasks.routes.js` - 集成MD5格式化

### 向后兼容性
- 所有修改都保持向后兼容
- 格式化失败时使用原token
- 不影响现有的认证逻辑 