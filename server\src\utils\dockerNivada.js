const axios = require('axios');
const fs = require('fs');
const { promisify } = require('util');

// Promisify fs.readFile for async/await usage
const readFile = promisify(fs.readFile);

// Constants
const BASE_URL = "https://pandora-server-cf.onethingai.com";

/**
 * Request ComfyOne API
 * @param {string} api - API path
 * @param {object|null} payload - Request payload
 * @param {string} method - HTTP method (GET, POST, etc.)
 * @param {string} apiKey - OnethingAI developer key
 * @returns {Promise<object|null>} API response data or null if failed
 */
async function requestComfyoneApi(api, payload, method, apiKey) {
    const url = `${BASE_URL}/${api}`;
    const headers = {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`
    };

    try {
        let retryCount = 0;
        const maxRetries = 3;
        
        while (retryCount < maxRetries) {
            try {
                const config = {
                    method,
                    url,
                    headers,
                    timeout: 5000
                };
                
                if (payload) {
                    config.data = payload;
                }
                
                const response = await axios(config);
                return response.data;
            } catch (error) {
                if (error.code === 'ECONNABORTED' || error.response?.status >= 500) {
                    retryCount++;
                    if (retryCount === maxRetries) {
                        throw error;
                    }
                    await new Promise(resolve => setTimeout(resolve, 2 ** retryCount * 1000));
                    continue;
                }
                
                console.error(`Error requesting ${api}:`, error.message);
                return { error: error.message };
            }
        }
    } catch (error) {
        console.error(`Error requesting ${api}:`, error);
        return null;
    }
}

/**
 * Get available backends
 * @param {string} apiKey - OnethingAI developer key
 * @returns {Promise<object|null>} Available backends or null if failed
 */
async function getAvailableBackends(apiKey) {
    const api = "v1/backends";
    return requestComfyoneApi(api, null, "GET", apiKey);
}

/**
 * Register a new backend instance
 * @param {string} instanceId - Instance ID to register
 * @param {string} apiKey - OnethingAI developer key
 * @returns {Promise<string|null>} Instance ID if successful, null otherwise
 */
async function registerComfyoneBackend(instanceId, apiKey) {
    const api = "v1/backends";
    const payload = {
        instance_id: instanceId
    };
    return requestComfyoneApi(api, payload, "POST", apiKey);
}

/**
 * Create a new workflow
 * @param {string} apiKey - OnethingAI developer key
 * @param {string} name - Workflow name
 * @param {object} inputs - Workflow input parameters
 * @param {object} outputs - Workflow output nodes
 * @param {string} workflowFile - Path to workflow definition file
 * @returns {Promise<object|null>} API response data or null if failed
 */
async function createWorkflow(apiKey, name, inputs, outputs, workflowFile) {
    const api = "v1/workflows";
    try {
        const workflowData = JSON.parse(await readFile(workflowFile, 'utf8'));
        const payload = {
            name,
            inputs,
            outputs,
            workflow: workflowData
        };
        return requestComfyoneApi(api, payload, "POST", apiKey);
    } catch (error) {
        console.error('Error reading workflow file:', error);
        return null;
    }
}

/**
 * Send prompt request to ComfyOne API
 * @param {object} payload - Prompt request parameters
 * @param {string} apiKey - OnethingAI developer key
 * @returns {Promise<string|null>} Task ID if successful, null otherwise
 */
async function promptComfyone(payload, apiKey) {
    const api = "v1/prompts";
    return requestComfyoneApi(api, payload, "POST", apiKey);
}

// Main execution
(async () => {
    try {
        // Replace these with your actual credentials
        const API_KEY = "onethingai-api-key";
        const INSTANCE_ID = "onethingai-instance-id";

        const inputs = [
            { id: '5', type: 'number', name: 'height' },
            { id: '5', type: 'number', name: 'width' }
        ];
        
        const outputs = ['9'];

        // Query available backends
        const backends = await getAvailableBackends(API_KEY);
        console.log(backends);

        // Check if instance is registered
        let isRegistered = false;
        if (backends && backends.data) {
            isRegistered = backends.data.some(backend => backend.name === INSTANCE_ID);
        }

        // Register instance if not already registered
        if (!isRegistered) {
            const registerResult = await registerComfyoneBackend(INSTANCE_ID, API_KEY);
            if (registerResult && registerResult.code === 0) {
                console.log(`Registered instance successfully: ${registerResult.data.name}`);
            } else {
                console.log(`Failed to register instance: ${registerResult?.code}, ${registerResult?.msg}`);
                process.exit(1);
            }
        }

        // Create workflow
        const createWorkflowResult = await createWorkflow(API_KEY, "test", inputs, outputs, "test_flow.json");
        
        if (createWorkflowResult && createWorkflowResult.code === 0) {
            const promptParams = {
                workflow_id: createWorkflowResult.data.id,
                inputs: [
                    {
                        id: "5",
                        params: {
                            width: 1024,
                            height: 1024
                        }
                    }
                ],
                free_cache: false
            };

            const promptResult = await promptComfyone(promptParams, API_KEY);
            if (promptResult && promptResult.code === 0) {
                console.log(promptResult);
            } else {
                console.log(`Failed to generate image: ${promptResult?.code}, ${promptResult?.msg}`);
                process.exit(1);
            }
        } else {
            console.log(`Failed to create workflow: ${createWorkflowResult?.code}, ${createWorkflowResult?.msg}`);
            process.exit(1);
        }
    } catch (error) {
        console.error('An error occurred:', error);
        process.exit(1);
    }
})();