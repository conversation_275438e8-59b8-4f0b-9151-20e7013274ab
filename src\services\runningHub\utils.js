/**
 * RunningHub工具函数
 * 提供各种实用功能
 */

import { TASK_STATUS } from './taskManager.js';

/**
 * 验证API密钥格式
 * @param {string} apiKey - API密钥
 * @returns {boolean} 是否有效
 */
export function validateApiKey(apiKey) {
  if (!apiKey || typeof apiKey !== 'string') {
    return false;
  }
  
  // RunningHub API密钥通常是32位字符串
  return apiKey.length === 32 && /^[a-zA-Z0-9]+$/.test(apiKey);
}

/**
 * 验证工作流ID格式
 * @param {string} workflowId - 工作流ID
 * @returns {boolean} 是否有效
 */
export function validateWorkflowId(workflowId) {
  if (!workflowId || typeof workflowId !== 'string') {
    return false;
  }
  
  // 工作流ID通常是数字字符串
  return /^\d+$/.test(workflowId);
}

/**
 * 格式化任务状态
 * @param {string} status - 原始状态
 * @returns {Object} 格式化后的状态信息
 */
export function formatTaskStatus(status) {
  const statusMap = {
    [TASK_STATUS.CREATE]: {
      label: '已创建',
      color: 'blue',
      description: '任务已创建，等待处理'
    },
    [TASK_STATUS.QUEUED]: {
      label: '排队中',
      color: 'orange',
      description: '任务在队列中等待执行'
    },
    [TASK_STATUS.RUNNING]: {
      label: '运行中',
      color: 'green',
      description: '任务正在执行中'
    },
    [TASK_STATUS.SUCCESS]: {
      label: '成功',
      color: 'success',
      description: '任务执行成功'
    },
    [TASK_STATUS.FAILED]: {
      label: '失败',
      color: 'error',
      description: '任务执行失败'
    }
  };

  return statusMap[status] || {
    label: '未知',
    color: 'default',
    description: '未知状态'
  };
}

/**
 * 计算任务执行时间
 * @param {Date} startTime - 开始时间
 * @param {Date} endTime - 结束时间（可选，默认为当前时间）
 * @returns {Object} 时间信息
 */
export function calculateTaskDuration(startTime, endTime = new Date()) {
  if (!startTime) {
    return { duration: 0, formatted: '0秒' };
  }

  const duration = endTime.getTime() - startTime.getTime();
  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  let formatted = '';
  if (hours > 0) {
    formatted += `${hours}小时`;
  }
  if (minutes % 60 > 0) {
    formatted += `${minutes % 60}分钟`;
  }
  if (seconds % 60 > 0 || formatted === '') {
    formatted += `${seconds % 60}秒`;
  }

  return {
    duration,
    seconds,
    minutes,
    hours,
    formatted
  };
}

/**
 * 解析WebSocket URL
 * @param {string} wssUrl - WebSocket URL
 * @returns {Object} 解析后的URL信息
 */
export function parseWebSocketUrl(wssUrl) {
  try {
    const url = new URL(wssUrl);
    const params = new URLSearchParams(url.search);
    
    return {
      protocol: url.protocol,
      host: url.host,
      pathname: url.pathname,
      clientId: params.get('clientId'),
      workflowId: params.get('workflowId'),
      cHost: params.get('c_host'),
      cPort: params.get('c_port'),
      auth: params.get('Rh-Comfy-Auth'),
      target: params.get('target'),
      params: Object.fromEntries(params.entries())
    };
  } catch (error) {
    console.error('解析WebSocket URL失败:', error);
    return null;
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 生成节点信息列表
 * @param {Object} nodeConfig - 节点配置
 * @returns {Array} 节点信息列表
 */
export function generateNodeInfoList(nodeConfig) {
  if (!nodeConfig || typeof nodeConfig !== 'object') {
    return [];
  }

  const nodeInfoList = [];
  
  for (const [nodeId, fields] of Object.entries(nodeConfig)) {
    if (fields && typeof fields === 'object') {
      for (const [fieldName, fieldValue] of Object.entries(fields)) {
        nodeInfoList.push({
          nodeId,
          fieldName,
          fieldValue
        });
      }
    }
  }

  return nodeInfoList;
}

/**
 * 验证节点信息列表
 * @param {Array} nodeInfoList - 节点信息列表
 * @returns {Object} 验证结果
 */
export function validateNodeInfoList(nodeInfoList) {
  if (!Array.isArray(nodeInfoList)) {
    return {
      valid: false,
      error: '节点信息列表必须是数组'
    };
  }

  for (let i = 0; i < nodeInfoList.length; i++) {
    const node = nodeInfoList[i];
    
    if (!node.nodeId) {
      return {
        valid: false,
        error: `第${i + 1}个节点缺少nodeId`
      };
    }
    
    if (!node.fieldName) {
      return {
        valid: false,
        error: `第${i + 1}个节点缺少fieldName`
      };
    }
    
    if (node.fieldValue === undefined || node.fieldValue === null) {
      return {
        valid: false,
        error: `第${i + 1}个节点缺少fieldValue`
      };
    }
  }

  return {
    valid: true
  };
}

/**
 * 创建任务进度跟踪器
 * @param {string} taskId - 任务ID
 * @returns {Object} 进度跟踪器
 */
export function createProgressTracker(taskId) {
  const tracker = {
    taskId,
    startTime: new Date(),
    endTime: null,
    status: TASK_STATUS.CREATE,
    progress: 0,
    logs: [],
    
    updateStatus(newStatus) {
      this.status = newStatus;
      this.addLog(`状态更新: ${formatTaskStatus(newStatus).label}`);
      
      if (newStatus === TASK_STATUS.SUCCESS || newStatus === TASK_STATUS.FAILED) {
        this.endTime = new Date();
        this.progress = 100;
      }
    },
    
    updateProgress(progress) {
      this.progress = Math.max(0, Math.min(100, progress));
      this.addLog(`进度更新: ${this.progress}%`);
    },
    
    addLog(message) {
      this.logs.push({
        timestamp: new Date(),
        message
      });
    },
    
    getDuration() {
      return calculateTaskDuration(this.startTime, this.endTime);
    },
    
    getStatusInfo() {
      return formatTaskStatus(this.status);
    }
  };
  
  tracker.addLog('任务创建');
  return tracker;
}

/**
 * 处理任务结果
 * @param {Array} results - 原始结果数组
 * @returns {Array} 处理后的结果
 */
export function processTaskResults(results) {
  if (!Array.isArray(results)) {
    return [];
  }

  return results.map(result => ({
    ...result,
    fileSize: result.fileSize ? formatFileSize(result.fileSize) : null,
    downloadUrl: result.fileUrl,
    fileName: result.fileUrl ? result.fileUrl.split('/').pop() : null,
    fileExtension: result.fileType || (result.fileUrl ? result.fileUrl.split('.').pop() : null)
  }));
}

/**
 * 创建错误处理器
 * @param {string} context - 错误上下文
 * @returns {Function} 错误处理函数
 */
export function createErrorHandler(context) {
  return (error) => {
    const errorInfo = {
      context,
      message: error.message || '未知错误',
      timestamp: new Date(),
      stack: error.stack
    };

    console.error(`[${context}] 错误:`, errorInfo);
    
    return {
      success: false,
      error: errorInfo.message,
      details: errorInfo
    };
  };
}

/**
 * 延迟函数
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise} Promise对象
 */
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试函数
 * @param {Function} fn - 要重试的函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delayMs - 重试间隔
 * @returns {Promise} Promise对象
 */
export async function retry(fn, maxRetries = 3, delayMs = 1000) {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (i < maxRetries) {
        await delay(delayMs * Math.pow(2, i)); // 指数退避
      }
    }
  }
  
  throw lastError;
}
