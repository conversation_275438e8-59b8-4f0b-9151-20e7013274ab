/* ImageSizeSelector组件独立样式 */
.image-size-selector {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  min-height: 88px; /* 最小高度88px，与其他组件一致 */
  height: auto; /* 允许内容需要时自动扩展 */
  display: flex;
  margin-bottom: var(--spacing-sm);
  overflow: hidden; /* 修复圆角问题：确保内部元素被圆角正确覆盖 */
  box-shadow: var(--shadow-sm);
}

.image-size-selector .selector-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  min-height: 88px; /* 确保内容区域最小高度 */
  gap: var(--spacing-sm);
}

.image-size-selector .selector-label {
  width: 88px;
  min-width: 88px; /* 防止压缩 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
}

.image-size-selector .selector-label span {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

.image-size-selector .selector-area {
  flex: 1;
  padding: 0;  /* 移除额外的内边距 */
  display: flex;
  align-items: center; /* 恢复垂直居中对齐，与TipsPanel保持一致 */
  justify-content: flex-start;
  overflow: visible; /* 内容区域允许溢出，以支持换行 */
  min-height: 88px; /* 确保内容区域最小高度 */
}

/* 组件文本容器 */
.image-size-selector .component-text {
  width: 100%;
}

/* 为开关设置样式 */
.image-size-selector h3 .toggle-switch {
  margin-left: 12px; /* 开关与标题的间距 */
}

/* 确保内容区域高度自适应 */
.image-size-selector .component-content {
  min-height: 28px; /* 保持与输入框相同的最小高度 */
  height: auto; /* 高度自适应 */
  width: 100%;
}

/* 确保文本和输入框具有相同的垂直空间 */
.image-size-selector .component-content p {
  margin: 4px 10px; /* 恢复左右边距，与原始设计保持一致 */
  line-height: 20px; /* 匹配输入框内容高度 */
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* 尺寸输入容器样式 */
.image-size-selector .component-content .size-input-container {
  display: flex;
  align-items: center;
  margin: 4px 10px; /* 恢复左右边距，与其他元素保持一致 */
  width: calc(100% - 20px); /* 考虑左右边距后的宽度 */
  flex-wrap: wrap; /* 换行功能 */
  column-gap: 8px; /* 水平间距 */
  row-gap: 8px; /* 垂直间距 */
  max-width: none; /* 移除最大宽度限制 */
  padding-bottom: 4px; /* 底部间距 */
}

/* 尺寸输入组样式 */
.image-size-selector .size-input-group {
  display: flex;
  align-items: center;
  flex: 0 0 auto; /* 不伸缩，使用自然宽度 */
  width: 110px; /* 从90px增加到110px，为更宽的输入框留出空间 */
}

/* 尺寸输入框包装器样式 */
.image-size-selector .size-input-wrapper {
  position: relative;
  display: inline-block;
}

/* 尺寸标签样式 */
.image-size-selector .size-label {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin-right: 4px; /* 右边距 */
  white-space: nowrap; /* 防止标签换行 */
  flex-shrink: 0; /* 防止标签被压缩 */
  width: 20px; /* 固定宽度 */
  text-align: left; /* 左对齐文本 */
}

/* 尺寸单位样式 */
.image-size-selector .size-unit {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin-left: 4px; /* 从6px减少到4px，减小与输入框的间距 */
  margin-right: 2px; /* 保持右侧外边距不变 */
  white-space: nowrap; /* 防止标签换行 */
  flex-shrink: 0; /* 防止标签被压缩 */
}

/* 尺寸输入框样式 */
.image-size-selector .size-input {
  width: 72px; /* 从58px增加到70px，提供更多显示空间 */
  flex: 0 0 auto; /* 不伸缩，使用固定宽度 */
  height: 24px; /* 修改输入框高度为24px */
  line-height: 24px; /* 行高确保文字垂直居中 */
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: 0 24px 0 4px; /* 右侧留出空间给数值调节按钮 */
  margin-left: -6px; /* 添加负左边距，缩小与标签的间距 */
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  background: var(--bg-primary);
  text-align: center;
  min-width: 50px; /* 从40px增加到50px，确保最小显示空间 */
  box-sizing: border-box; /* 确保padding和border包含在高度内 */
  /* 移除number类型输入框的上下箭头 */
  -webkit-appearance: none;
  -moz-appearance: textfield;
  appearance: textfield;
  /* 过渡效果 */
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 数值调节按钮位置样式 - 适应ImageSizeSelector输入框 */
.image-size-selector .number-controls {
  position: absolute;
  right: 8px; /* 从4px增加到8px，增加与输入框右侧的间距 */
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 0;
  height: 24px; /* 与输入框高度一致 */
  justify-content: center;
}

/* ImageSizeSelector特定的数值调节按钮样式 */
.image-size-selector .number-control-btn {
  width: 12px;
  height: 10px;
  padding: 0;
  border: none;
  background: transparent;
  color: var(--text-tertiary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
  line-height: 1;
  margin: 0;
}

.image-size-selector .number-control-btn:hover {
  color: var(--brand-primary);
}

.image-size-selector .number-control-btn svg {
  width: 10px;
  height: 10px;
}

/* 兼容Firefox */
.image-size-selector .size-input::-webkit-outer-spin-button,
.image-size-selector .size-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* 输入框激活样式 */
.image-size-selector .size-input:hover {
  border-color: var(--brand-primary);
}

.image-size-selector .size-input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(255, 60, 106, 0.1);
}

[data-theme="dark"] .image-size-selector .size-input:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

/* 长宽比锁定按钮样式 */
.image-size-selector .aspect-ratio-btn {
  width: 24px;
  height: 24px;
  min-width: 24px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  padding: 0;
  flex-shrink: 0; /* 防止按钮被压缩 */
  margin: 0 6px 0 -4px; /* 从-16px改为-4px，向右移动按钮位置 */
  box-sizing: border-box; /* 确保padding和border包含在高度内 */
}

.image-size-selector .aspect-ratio-btn:hover {
  color: var(--brand-primary);
  border-color: var(--brand-primary);
}

.image-size-selector .aspect-ratio-btn svg {
  width: 14px;
  height: 14px;
}

.image-size-selector .aspect-ratio-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.image-size-selector .size-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式布局：当容器宽度不够时，输入框组换行显示 */
@media (max-width: 600px) {

} 