/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/tabs.css';
@import '../../styles/buttons.css';
@import '../../styles/close-buttons.css';

/* 放大倍数与尺寸弹窗包装器样式 */
.magnification-size-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100dvh;
  z-index: 99999; /* 大幅提高z-index，确保在所有内容之上，包括移动端导航栏 */
  pointer-events: auto; /* 改为auto，使其可以接收点击事件，实现点击外部关闭功能 */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 放大倍数与尺寸弹窗样式 */
.magnification-size-modal {
  position: fixed;
  width: 480px;
  max-width: 90vw;
  max-height: 90vh;
  background: var(--bg-primary);
  pointer-events: auto; /* 保持弹窗本身可点击 */
  box-shadow: var(--shadow-md);
  border-radius: 12px;
  user-select: none; /* 防止拖动时选中文本 */
  z-index: 100000; /* 确保弹窗内容在包装器之上 */
  /* 位置由传入的style决定，不在这里设置默认位置 */
}

/* 拖动状态下的样式 */
.magnification-size-modal.dragging {
  user-select: none;
  pointer-events: auto;
}

/* 修复弹窗拖动时手型光标 */
.magnification-size-modal.dragging .modal-header {
  cursor: grabbing !important;
}

/* 标题区域可拖动的提示 */
.magnification-size-modal .modal-header:hover {
  background: rgba(0, 0, 0, 0.02);
}

[data-theme="dark"] .magnification-size-modal .modal-header:hover {
  background: rgba(255, 255, 255, 0.02);
}

/* 拖动时禁止全局文本选择 */
body.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 暗色主题下添加白边 */
[data-theme="dark"] .magnification-size-modal {
  background: var(--bg-primary);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
}

/* 修改modal-header样式，确保与ColorPickerModal一致 */
.magnification-size-modal .modal-header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: var(--spacing-lg) 0 0;
  margin: 0;
  border-bottom: 1px solid var(--border-light); /* 添加底部边框作为分割线 */
  position: relative;
}

/* 标签页样式覆盖，与ColorPickerModal保持一致 */
.magnification-size-modal .modal-tabs {
  width: auto !important;
  margin-left: 20px !important;
  flex-shrink: 0;
}

.magnification-size-modal .tab-group {
  width: auto !important;
  margin-left: 20px !important;
  flex-shrink: 0;
}

.magnification-size-modal .tab-btn {
  flex-shrink: 0;
  min-width: auto;
}

/* 修改modal-body样式，与ColorPickerModal保持一致 */
.magnification-size-modal .modal-body {
  padding-top: var(--spacing-md);
  margin-top: 0;
}

/* 模态框内容样式调整 */
.magnification-size-content {
  padding: 0 var(--spacing-sx);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* 标签页内容样式 */
.magnification-size-modal .tab-content {
  padding: 0 var(--spacing-sx);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xxs);
  min-height: 150px;
}

.magnification-size-modal .modal-footer {
  position: relative;
  height: 60px;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-md);
  border-top: 1px solid var(--border-lighter);
}

/* 标签页内容样式 */
.scale-tab, .custom-tab {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xxs); /* 减小标签页内各元素的间距 */
}

/* 尺寸信息容器 */
.size-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding: 0; /* 移除内边距，让子元素控制 */
}

.original-size, .upscaled-size {
  display: flex;
  flex-direction: column;
  gap: 2px; /* 减小内部间距 */
  padding: 6px 8px 6px 16px; /* 减小上下内边距 */
}

.size-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.size-value {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
}

/* 放大倍数按钮组 */
.scale-buttons {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  margin: 0 0 var(--spacing-md) 16px; /* 添加左边距与原图尺寸对齐 */
  width: calc(100% - 32px); /* 考虑左右边距 */
}

.scale-btn {
  flex: 1;
  padding: 10px 0;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-normal);
  text-align: center;
}

.scale-btn:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.scale-btn.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 滑块样式 */
.scale-slider {
  position: relative;
  margin: var(--spacing-md) 0 0 16px; /* 添加左边距与原图尺寸对齐 */
  padding: 0;
  width: calc(100% - 32px); /* 考虑左右边距 */
}

.slider-track {
  position: relative;
  height: 2px;
  background: var(--border-light);
  border-radius: var(--radius-sm);
  margin: 16px 0;
  width: 100%;
}

/* 移除禁用样式差异 */
.slider-input:disabled ~ .slider-track {
  background: var(--border-light);
  opacity: 1;
}

.slider-input:disabled ~ .slider-track .slider-fill {
  background: var(--brand-gradient);
  opacity: 1;
}

/* 移除滑块禁用时的视觉差异 */
.slider-input:disabled {
  opacity: 1;
  cursor: not-allowed;
}

.slider-input:disabled::-webkit-slider-thumb {
  opacity: 1;
  background: var(--brand-primary);
  cursor: not-allowed;
}

.slider-input:disabled::-moz-range-thumb {
  opacity: 1;
  background: var(--brand-primary);
  cursor: not-allowed;
}

.slider-fill {
  position: absolute;
  height: 2px;
  background: var(--brand-gradient);
  border-radius: var(--radius-sm);
  z-index: 0;
  top: 0;
  left: 0;
}

.slider-input {
  position: absolute;
  top: -6px; /* 调整top值使滑块中心与轨道对齐 */
  left: 0;
  width: 100%;
  height: 2px;
  -webkit-appearance: none;
  margin: 0;
  padding: 0;
  z-index: 1;
  cursor: pointer;
  background: transparent;
}

.slider-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: var(--brand-primary);
  cursor: pointer;
  position: relative;
  z-index: 2;
  margin-top: 0; /* 移除margin-top，因为我们已经通过调整input位置来居中 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.slider-input::-moz-range-thumb {
  width: 14px;
  height: 14px;
  border: none;
  border-radius: 50%;
  background: var(--brand-primary);
  cursor: pointer;
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.slider-input::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.slider-input::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.scale-value {
  font-size: var(--font-size-md);
  color: var(--brand-primary);
  font-weight: 500;
  margin-top: var(--spacing-xs);
  text-align: right;
}

/* 自定义倍数标题 */
.custom-scale-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  width: 100%;
}

.custom-scale-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

.custom-scale-header .scale-value {
  font-size: var(--font-size-md);
  color: var(--brand-primary);
  font-weight: 500;
  line-height: 1.5;
  margin-top: 0;
}

/* 无原图警告样式 */
.no-image-warning {
  background-color: var(--bg-warning-light);
  color: var(--text-warning);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  text-align: left;
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-md);
  margin-left: 16px; /* 左对齐 */
  width: calc(100% - 32px); /* 考虑左右边距 */
}

/* 禁用状态的按钮样式 - 保持与启用状态相同 */
.scale-btn:disabled {
  opacity: 1;
  cursor: not-allowed;
  border-color: var(--border-light);
  background: var(--bg-primary);
  color: var(--text-secondary);
}

.scale-btn:disabled:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.scale-btn:disabled.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 恢复禁用输入框的样式 */
.size-input:disabled {
  opacity: 1;
  cursor: not-allowed;
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-light);
}

.size-input:disabled:hover {
  border-color: var(--border-hover);
}

/* 检查未激活标签文字颜色 */
.magnification-size-modal .tab-btn:not(.active) {
  /* 这会打印出当前应用的样式，便于调试 */
  color: var(--text-secondary);
}

/* 使用直接的颜色值 */
.size-limit-notice {
  font-size: var(--font-size-sm);
  color: #888888; /* 明亮主题下的深灰色，与未激活标签一致 */
  margin-top: 28px; /* 进一步增加与上方输入框的间距 */
  margin-left: 16px; /* 默认左对齐 */
  text-align: left;
  width: calc(100% - 32px); /* 考虑左右边距 */
}

/* 尺寸超出限制时的提示样式 */
.size-limit-notice.size-limit-exceeded {
  color: var(--brand-primary); /* 使用品牌色显示警告 */
  font-weight: 500; /* 稍微加粗文字 */
}

/* 暗黑主题下的样式 */
:root[data-theme='dark'] .size-limit-notice {
  color: #aaaaaa; /* 暗黑主题下的浅灰色，与未激活标签一致 */
}

:root[data-theme='dark'] .size-limit-notice.size-limit-exceeded {
  color: var(--brand-primary); /* 暗黑模式下也使用品牌色 */
}

/* 自定义尺寸输入相关样式 */
.custom-size-inputs {
  margin-top: -4px; /* 添加负上边距进一步缩小空间 */
  margin-bottom: 8px; /* 增加下边距 */
  padding: 0; /* 移除所有内边距 */
  width: 100%;
}

/* 尺寸输入容器样式 */
.size-input-container {
  display: flex;
  align-items: center;
  margin: 4px 0 4px 16px; /* 左边距与原图尺寸文本对齐 */
  width: calc(100% - 32px); /* 考虑左右边距 */
  justify-content: flex-start;
  gap: var(--spacing-md); /* 增加输入框之间的间距 */
}

/* 尺寸输入组样式 */
.size-input-group {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
  min-width: 120px; /* 增加最小宽度 */
}

/* 尺寸标签样式 */
.size-input-group .size-label {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin-right: 4px;
  white-space: nowrap;
  flex-shrink: 0;
  width: 24px;
  text-align: left;
}

/* 尺寸单位样式 */
.size-unit {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin-left: 8px;
  white-space: nowrap;
  flex-shrink: 0;
  text-transform: lowercase; /* 确保px单位为小写 */
}

.size-input {
  width: 75px; /* 增加宽度 */
  flex: 0 0 auto;
  height: 32px; /* 增加高度匹配字号 */
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: 0 4px;
  font-size: var(--font-size-md); /* 从sm改为md，与原图尺寸文本一致 */
  color: var(--text-primary);
  background: var(--bg-primary);
  text-align: center;
  min-width: 65px; /* 增加最小宽度 */
  /* 移除number类型输入框的上下箭头 */
  -webkit-appearance: none;
  -moz-appearance: textfield;
  appearance: textfield;
  /* 过渡效果 */
  transition: var(--transition-normal);
}

/* 兼容Firefox */
.size-input::-webkit-outer-spin-button,
.size-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* 添加激活样式 */
.size-input:hover {
  border-color: var(--brand-primary);
}

.size-input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(255, 60, 106, 0.1);
}

[data-theme="dark"] .size-input:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

/* 修改长宽比锁定图标样式 - 去除按钮样式，只显示图标 */
.aspect-ratio-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  width: 28px; /* 增加宽度 */
  height: 28px; /* 增加高度 */
  flex-shrink: 0;
}

.aspect-ratio-icon svg {
  width: 16px;
  height: 16px;
}

/* 放大倍数标签页的说明文字左对齐 */
.scale-tab .size-limit-notice {
  margin-top: var(--spacing-md)+30px;
  margin-left: 16px; /* 与输入区域左对齐 */
  text-align: left;
  width: calc(100% - 32px); /* 考虑左右边距 */
}

/* 添加移动端适配样式 */
@media (max-width: 768px) {
  .magnification-size-modal {
    width: 95%;
    max-width: none;
    max-height: calc(100vh - 40px);
    max-height: calc(100dvh - 40px);
  }

  /* 移动端标签页样式优化 */
  .magnification-size-modal .modal-tabs {
    width: auto !important;
    margin-left: 20px !important;
    flex-shrink: 0;
  }

  .magnification-size-modal .tab-group {
    width: auto !important;
    margin-left: 20px !important;
    flex-shrink: 0;
  }

  .magnification-size-modal .tab-btn {
    flex-shrink: 0;
    min-width: auto;
  }

  /* 移动端边距优化 */
  .magnification-size-modal .modal-body {
    padding: 12px !important;
  }
  .magnification-size-modal .tab-content,
  .magnification-size-modal .custom-tab,
  .magnification-size-modal .scale-tab {
    font-size: 13px !important;
    gap: 8px !important;
  }
  .magnification-size-modal input,
  .magnification-size-modal button:not(.medium-close-button):not(.tab-btn),
  .magnification-size-modal select {
    font-size: 13px !important;
    padding: 4px 8px !important;
  }
}

@media (max-width: 480px) {
  .magnification-size-modal {
    width: 98%;
    max-height: calc(100vh - 20px);
    max-height: calc(100dvh - 20px);
  }
}

@media (max-width: 360px) {
  .magnification-size-modal {
    width: 100%;
    max-height: 100vh;
    max-height: 100dvh;
    border-radius: 0;
  }
}

/* 真实移动设备适配 */
@media (hover: none) and (pointer: coarse) {
  .magnification-size-wrapper {
    padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  }

  .magnification-size-modal {
    max-height: calc(100dvh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  }

  /* 移动端标签页样式优化，与ColorPickerModal保持一致 */
  .magnification-size-modal .modal-tabs {
    width: auto !important;
    margin-left: 20px !important;
    flex-shrink: 0;
  }

  .magnification-size-modal .tab-group {
    width: auto !important;
    margin-left: 20px !important;
    flex-shrink: 0;
  }

  .magnification-size-modal .tab-btn {
    flex-shrink: 0;
    min-width: auto;
  }
}

/* 横屏模式适配 */
@media (hover: none) and (pointer: coarse) and (orientation: landscape) {
  .magnification-size-modal {
    max-height: calc(100dvh - 20px);
  }
} 

.magnification-size-modal .modal-footer button {
  min-width: 104px;
  width: auto;
  max-width: 100%;
} 