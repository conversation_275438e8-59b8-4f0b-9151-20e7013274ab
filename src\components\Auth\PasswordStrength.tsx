import React from 'react';

interface PasswordFeedback {
  text: string;
  met: boolean;
}

interface PasswordStrengthProps {
  // 将在后续实现中添加具体属性
  strength?: {
    score: number;
    level: string;
    feedback: PasswordFeedback[];
  };
}

/**
 * 密码强度组件
 * 显示密码强度指示器和反馈提示
 */
const PasswordStrength: React.FC<PasswordStrengthProps> = ({ strength }) => {
  if (!strength) return null;

  const { level, feedback } = strength;
  
  // 根据强度级别显示对应的文本
  const getLevelText = (level: string) => {
    switch (level) {
      case '弱': return '弱';
      case '中': return '中等';
      case '较强': return '较强';
      case '强': return '强';
      default: return '';
    }
  };

  return (
    <div className="password-strength">
      <div className={`strength-bar ${level}`}>
        <span className="strength-level"></span>
        <span className="strength-level"></span>
        <span className="strength-level"></span>
        <span className="strength-level"></span>
      </div>
      <span className="strength-text">{getLevelText(level)}</span>
      
      {feedback && feedback.length > 0 && (
        <div className="strength-tips">
          {feedback.map((item, index) => (
            <div key={index} className={`tip ${item.met ? 'met' : ''}`}>
              {item.text}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

/**
 * 检查密码强度的工具函数
 * @param password 密码字符串
 * @returns 包含得分、级别和反馈的对象
 */
export const checkPasswordStrength = (password: string): {
  score: number;
  level: string;
  feedback: PasswordFeedback[];
} => {
  let score = 0;
  const feedback: PasswordFeedback[] = [];

  // 所有要求的检查结果
  const requirements = [
    {
      text: '密码长度8-32位',
      met: password.length >= 8 && password.length <= 32
    },
    {
      text: '需含数字',
      met: /[0-9]/.test(password)
    },
    {
      text: '需含英文字母',
      met: /[a-zA-Z]/.test(password)
    },
    {
      text: '需包含特殊字符',
      met: /[^A-Za-z0-9]/.test(password)
    }
  ];

  // 计算分数并收集反馈
  requirements.forEach(req => {
    if (req.met) {
      score += 1;
    }
    feedback.push({
      text: req.text,
      met: req.met
    });
  });

  // 根据满足的要求数确定等级
  let level = '无';
  if (score === 1) {
    level = '弱';
  } else if (score === 2) {
    level = '中';
  } else if (score === 3) {
    level = '较强';
  } else if (score >= 4) {
    level = '强';
  }

  return { score, level, feedback };
};

export default PasswordStrength;

