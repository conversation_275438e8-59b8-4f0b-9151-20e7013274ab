# AIBIKINI 订阅系统开发文档

## 目录
1. [系统概述](#系统概述)
2. [数据结构设计](#数据结构设计)
3. [代码结构](#代码结构)
4. [API接口文档](#api接口文档)
5. [计费系统](#计费系统)
6. [开发指南](#开发指南)
7. [部署说明](#部署说明)

## 系统概述

AIBIKINI 订阅系统是一个基于 MongoDB 的 SaaS 订阅管理系统，支持多种订阅计划、功能权限控制和使用量配额管理。

### 核心功能
- 多层级订阅计划管理
- 功能权限控制
- 使用量配额管理
- 自动续费支持
- 计费系统集成
- 管理员后台管理

### 订阅计划类型
- `free`: 免费版
- `design`: 设计版
- `model`: 模特版
- `full`: 完整版
- `enterprise`: 企业版

## 数据结构设计

### 1. 订阅计划模型 (Plan)

```javascript
{
  code: String,           // 计划代码，唯一标识
  name: String,           // 计划名称
  description: String,    // 计划描述
  price: {
    monthly: Number,      // 月付价格
    yearly: Number,       // 年付价格
    discount: Number      // 年付折扣百分比
  },
  features: {
    design: {
      enabled: Boolean,
      trendDesign: Boolean,
      styleOptimization: Boolean,
      inspirationExplore: Boolean
    },
    model: {
      enabled: Boolean,
      fashionShoot: Boolean,
      modelChange: Boolean,
      colorChange: Boolean,
      backgroundChange: Boolean,
      virtualModel: Boolean
    },
    tools: {
      enabled: Boolean,
      upscale: Boolean,
      matting: Boolean,
      extend: Boolean
    },
    support: {
      level: String,      // 'standard' | 'premium' | 'enterprise'
      responseTime: String // '5x8' | '7x24'
    }
  },
  usageQuota: {
    totalRequests: Number,  // -1 表示无限制
    dailyRequests: Number   // -1 表示无限制
  },
  isPublic: Boolean,      // 是否公开显示
  sortOrder: Number,      // 排序权重
  isRecommended: Boolean, // 是否推荐
  createdAt: Date,
  updatedAt: Date
}
```

### 2. 用户订阅模型 (Subscription)

```javascript
{
  user: ObjectId,         // 用户ID，关联User模型
  plan: String,           // 订阅计划代码
  status: String,         // 'active' | 'expired' | 'canceled' | 'pending'
  startDate: Date,        // 订阅开始时间
  endDate: Date,          // 订阅结束时间
  autoRenew: Boolean,     // 是否自动续费
  price: Number,          // 实际支付价格
  paymentMethod: String,  // 支付方式
  paymentId: String,      // 支付ID
  features: {
    // 与Plan模型相同的功能结构
  },
  customFeatures: [{
    name: String,
    description: String,
    enabled: Boolean
  }],
  usageQuota: {
    totalRequests: Number,
    dailyRequests: Number,
    remainingRequests: Number
  },
  metadata: Map,          // 扩展元数据
  createdAt: Date,
  updatedAt: Date
}
```

### 3. 用户模型扩展 (User)

```javascript
{
  // 基础用户信息
  username: String,
  phone: String,
  password: String,
  name: String,
  role: String,           // 'user' | 'admin'
  
  // 开发者信息
  developer: {
    isVerified: Boolean,
    apiKey: String,
    apiSecret: String,
    allowedOrigins: [String],
    rateLimit: {
      requests: Number,
      interval: Number
    }
  },
  
  // 用户状态
  status: String,         // 'active' | 'inactive' | 'suspended'
  
  // 会话管理
  activeSessions: [{
    token: String,
    deviceInfo: {
      userAgent: String,
      ip: String
    },
    lastActive: Date
  }],
  maxSessions: Number,
  
  // 时间戳
  createdAt: Date,
  updatedAt: Date,
  lastLoginAt: Date,
  lastLoginIp: String,
  loginHistory: [{
    ip: String,
    timestamp: Date,
    userAgent: String
  }]
}
```

## 代码结构

```
server/src/modules/admin/subscribe/
├── plan.model.js              # 订阅计划数据模型
├── plan.controller.js          # 订阅计划控制器
├── plan.routes.js             # 订阅计划路由
├── subscription.model.js       # 用户订阅数据模型
├── subscription.controller.js  # 用户订阅控制器
└── subscription.routes.js      # 用户订阅路由

server/src/config/
├── billing.js                 # 计费配置
└── constants.js               # 系统常量

server/src/services/
└── credits/
    └── creditService.js       # 积分/算力值服务

server/src/middleware/
└── auth.middleware.js         # 认证中间件
```

### 核心文件说明

#### 1. 数据模型 (Models)
- **plan.model.js**: 定义订阅计划的数据结构和验证规则
- **subscription.model.js**: 定义用户订阅的数据结构，包含预保存钩子自动设置功能权限

#### 2. 控制器 (Controllers)
- **plan.controller.js**: 处理订阅计划的 CRUD 操作
- **subscription.controller.js**: 处理用户订阅的创建、更新、查询等操作

#### 3. 路由 (Routes)
- **plan.routes.js**: 定义订阅计划相关的 API 路由
- **subscription.routes.js**: 定义用户订阅相关的 API 路由

#### 4. 配置文件
- **billing.js**: 定义各功能的算力值扣费标准
- **constants.js**: 定义系统常量，如订阅计划类型、状态等

## API接口文档

### 订阅计划管理 API

#### 获取公开订阅计划
```
GET /api/plans/public
```
**响应示例:**
```json
{
  "success": true,
  "plans": [
    {
      "code": "free",
      "name": "免费版",
      "description": "基础功能免费使用",
      "price": {
        "monthly": 0,
        "yearly": 0,
        "discount": 0
      },
      "features": {
        "design": { "enabled": false },
        "model": { "enabled": false },
        "tools": { "enabled": true },
        "support": { "level": "standard" }
      }
    }
  ]
}
```

#### 根据代码获取计划
```
GET /api/plans/code/:code
```

#### 创建订阅计划 (管理员)
```
POST /api/plans
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "code": "premium",
  "name": "高级版",
  "description": "包含所有高级功能",
  "price": {
    "monthly": 99,
    "yearly": 999,
    "discount": 15
  },
  "features": {
    "design": { "enabled": true },
    "model": { "enabled": true },
    "tools": { "enabled": true },
    "support": { "level": "premium" }
  },
  "usageQuota": {
    "totalRequests": 1000,
    "dailyRequests": 50
  }
}
```

#### 更新订阅计划 (管理员)
```
PUT /api/plans/:id
Authorization: Bearer <admin_token>
```

#### 删除订阅计划 (管理员)
```
DELETE /api/plans/:id
Authorization: Bearer <admin_token>
```

### 用户订阅管理 API

#### 检查功能访问权限
```
POST /api/subscriptions/check-access
Content-Type: application/json

{
  "userId": "user_id",
  "feature": "fashion",
  "subType": "tab1"
}
```

#### 获取用户订阅信息
```
GET /api/subscriptions/user/:userId?
Authorization: Bearer <user_token>
```

#### 创建用户订阅 (管理员)
```
POST /api/subscriptions
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "userId": "user_id",
  "plan": "premium",
  "status": "active",
  "startDate": "2024-01-01",
  "endDate": "2024-02-01",
  "price": 99,
  "autoRenew": true,
  "paymentMethod": "alipay",
  "paymentId": "payment_123"
}
```

#### 更新用户订阅 (管理员)
```
PUT /api/subscriptions/:id
Authorization: Bearer <admin_token>
```

#### 取消订阅 (管理员)
```
POST /api/subscriptions/:id/cancel
Authorization: Bearer <admin_token>
```

#### 获取订阅统计 (管理员)
```
GET /api/subscriptions/stats
Authorization: Bearer <admin_token>
```

## 计费系统

### 功能计费标准

系统使用算力值 (C) 作为计费单位，各功能的扣费标准定义在 `billing.js` 中：

```javascript
const BILLING_CONFIG = {
  // 款式设计相关功能
  'trending': 30,         // 爆款开发
  'optimize': 30,         // 款式优化
  'inspiration': 30,      // 灵感探索
 
  // 模特图相关功能
  'fashion': 150,          // 时尚大片
  'try-on': 110,           // 模特换装
  'change-model': 150,     // 换模特
  'recolor': 20,           // 服装复色
  'fabric': 35,            // 换面料
  'background': 25,        // 换背景
  'virtual': 100,          // 虚拟模特
  'detail-migration': 80, // 细节还原
  'hand-fix': 80,         // 手部修复
  
  // 快捷工具相关功能
  'extract': 25,           // 图片取词
  'upscale': 10,           // 高清放大
  'matting': {             // 自动抠图支持多标签页定价
    'tab1': 10,            // 去背景
    'tab2': 20             // 抠衣服
  },
  'extend': 60,            // 智能扩图
  'inpaint': 999,          // 消除笔
  
  // 视频工具相关功能
  'imgtextvideo': 300,    // 图文成片
  'mulimgvideo': 400,     // 多图成片
};
```

### 计费逻辑

1. **功能权限检查**: 首先检查用户订阅是否包含该功能
2. **使用量检查**: 检查用户是否超出使用配额
3. **算力值扣除**: 根据功能扣费标准扣除相应算力值
4. **记录使用**: 记录用户的使用历史

### 使用量配额管理

- `totalRequests`: 总请求次数限制 (-1 表示无限制)
- `dailyRequests`: 每日请求次数限制 (-1 表示无限制)
- `remainingRequests`: 剩余请求次数

## 开发指南

### 1. 添加新功能

#### 步骤 1: 更新计费配置
在 `server/src/config/billing.js` 中添加新功能的扣费标准：

```javascript
const BILLING_CONFIG = {
  // ... 现有配置
  'new-feature': 50,  // 新功能扣费标准
};
```

#### 步骤 2: 更新功能权限结构
在 `plan.model.js` 和 `subscription.model.js` 中添加新功能：

```javascript
features: {
  // ... 现有功能
  newFeature: {
    enabled: { type: Boolean, default: false },
    advancedOption: { type: Boolean, default: false }
  }
}
```

#### 步骤 3: 更新订阅计划预设
在 `subscription.model.js` 的预保存钩子中添加新功能的默认设置：

```javascript
SubscriptionSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('plan')) {
    switch (this.plan) {
      case 'premium':
        // ... 现有设置
        this.features.newFeature.enabled = true;
        this.features.newFeature.advancedOption = true;
        break;
      // ... 其他计划
    }
  }
  next();
});
```

### 2. 权限检查中间件

创建功能权限检查中间件：

```javascript
// middleware/featureAccess.js
const Subscription = require('../modules/admin/subscribe/subscription.model');
const { getBillingCost } = require('../config/billing');

const checkFeatureAccess = async (req, res, next) => {
  try {
    const { userId, feature, subType } = req.body;
    
    // 获取用户订阅
    const subscription = await Subscription.findOne({
      user: userId,
      status: 'active',
      endDate: { $gt: new Date() }
    });
    
    if (!subscription) {
      return res.status(403).json({
        success: false,
        message: '用户无有效订阅'
      });
    }
    
    // 检查功能权限
    const hasAccess = checkFeaturePermission(subscription, feature);
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: '当前订阅不包含此功能'
      });
    }
    
    // 检查使用量配额
    const cost = getBillingCost(feature, subType);
    if (subscription.usageQuota.remainingRequests < cost) {
      return res.status(403).json({
        success: false,
        message: '使用量配额不足'
      });
    }
    
    // 扣除算力值
    subscription.usageQuota.remainingRequests -= cost;
    await subscription.save();
    
    next();
  } catch (error) {
    next(error);
  }
};

module.exports = { checkFeatureAccess };
```

### 3. 订阅状态管理

#### 自动过期处理
创建定时任务处理订阅过期：

```javascript
// scripts/subscriptionExpiry.js
const cron = require('node-cron');
const Subscription = require('../src/modules/admin/subscribe/subscription.model');

// 每天凌晨检查过期订阅
cron.schedule('0 0 * * *', async () => {
  try {
    const expiredSubscriptions = await Subscription.find({
      status: 'active',
      endDate: { $lt: new Date() }
    });
    
    for (const subscription of expiredSubscriptions) {
      subscription.status = 'expired';
      await subscription.save();
      
      // 发送过期通知
      await sendExpiryNotification(subscription.user);
    }
  } catch (error) {
    console.error('处理订阅过期失败:', error);
  }
});
```

#### 自动续费处理
```javascript
// scripts/autoRenewal.js
const cron = require('node-cron');
const Subscription = require('../src/modules/admin/subscribe/subscription.model');

// 每天检查需要续费的订阅
cron.schedule('0 2 * * *', async () => {
  try {
    const renewals = await Subscription.find({
      status: 'active',
      autoRenew: true,
      endDate: { 
        $gte: new Date(),
        $lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天内到期
      }
    });
    
    for (const subscription of renewals) {
      await processAutoRenewal(subscription);
    }
  } catch (error) {
    console.error('自动续费处理失败:', error);
  }
});
```

### 4. 数据迁移脚本

#### 初始化订阅计划
```javascript
// scripts/initPlans.js
const Plan = require('../src/modules/admin/subscribe/plan.model');

const initPlans = async () => {
  const plans = [
    {
      code: 'free',
      name: '免费版',
      description: '基础功能免费使用',
      price: { monthly: 0, yearly: 0, discount: 0 },
      features: {
        design: { enabled: false },
        model: { enabled: false },
        tools: { enabled: true },
        support: { level: 'standard', responseTime: '5x8' }
      },
      usageQuota: { totalRequests: 10, dailyRequests: 2 },
      isPublic: true,
      sortOrder: 1,
      isRecommended: false
    },
    // ... 其他计划
  ];
  
  for (const planData of plans) {
    await Plan.findOneAndUpdate(
      { code: planData.code },
      planData,
      { upsert: true, new: true }
    );
  }
};

module.exports = { initPlans };
```

## 部署说明

### 1. 环境要求
- Node.js >= 16.0.0
- MongoDB >= 4.4
- Redis (可选，用于缓存)

### 2. 安装依赖
```bash
cd server
npm install
```

### 3. 环境配置
创建 `.env` 文件：
```env
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb://localhost:27017/aibikini
JWT_SECRET=your_jwt_secret
```

### 4. 数据库初始化
```bash
# 运行初始化脚本
node scripts/initPlans.js
node scripts/createAdmin.js
```

### 5. 启动服务
```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

### 6. 监控和维护

#### 日志监控
```javascript
// 添加订阅相关日志
const logger = require('../utils/logger');

logger.info('用户订阅创建', {
  userId: subscription.user,
  plan: subscription.plan,
  price: subscription.price
});
```

#### 性能优化
- 为常用查询添加数据库索引
- 使用 Redis 缓存热门订阅计划
- 实现订阅数据的读写分离

#### 备份策略
- 定期备份订阅数据
- 实现订阅数据的增量备份
- 建立数据恢复流程

## 常见问题

### Q1: 如何处理订阅升级/降级？
A: 在 `subscription.controller.js` 中实现升级/降级逻辑，包括价格调整、功能权限变更等。

### Q2: 如何实现试用期功能？
A: 在订阅模型中添加 `trialEndDate` 字段，并在权限检查时考虑试用期状态。

### Q3: 如何处理支付失败？
A: 实现支付回调处理，根据支付状态更新订阅状态，并发送相应通知。

### Q4: 如何实现批量操作？
A: 在控制器中添加批量操作方法，支持批量创建、更新、删除订阅。

---

**文档版本**: 1.0  
**最后更新**: 2024-01-01  
**维护者**: AIBIKINI 开发团队 