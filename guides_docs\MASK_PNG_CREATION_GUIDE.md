# 蒙版PNG创建技术指南

## 概述

本文档详细说明如何在前端创建兼容ComfyUI工作流的蒙版PNG图像。这是AIBIKINI平台的核心技术之一，必须确保实现的精确性和稳定性。

## 技术原理

ComfyUI所需的"带蒙版信息的PNG"实际上是一张标准的带Alpha通道的RGBA图像：

- **RGB三个通道**：存储的是原图像素数据，保持不变
- **Alpha通道**：存储的是蒙版信息，其中：
  - 绘制蒙版的区域 → Alpha=0（完全透明）
  - 未绘制蒙版的区域 → Alpha=255（完全不透明）或保持原有Alpha值

关键技术要点：

1. **绘制蒙版时不改变原图的RGB数据**，只修改对应像素的Alpha通道值
2. 当在普通图片浏览器中查看时，透明区是完全看不见底下RGB的（显示为棋盘格背景）
3. ComfyUI直接读取文件字节，提取RGB作为"原图"，提取Alpha作为"蒙版"来使用

## 实现方案

### 方案一：使用globalCompositeOperation（推荐）

这是最高效且最符合ComfyUI需求的方法：

```javascript
// 假设已有一个canvas上绘制了原图
function createMaskedImage(originalCanvas, maskRegion) {
  // 1. 创建工作用canvas
  const canvas = document.createElement('canvas');
  canvas.width = originalCanvas.width;
  canvas.height = originalCanvas.height;
  const ctx = canvas.getContext('2d');
  
  // 2. 先绘制原图
  ctx.drawImage(originalCanvas, 0, 0);
  
  // 3. 关键步骤：使用destination-in合成操作
  ctx.globalCompositeOperation = 'destination-in';
  // destination-in会用当前画笔alpha去乘原像素alpha，不改变RGB
  
  // 4. 设置填充样式（绘制蒙版区域为透明）
  ctx.fillStyle = 'rgba(0,0,0,0)'; // 完全透明
  
  // 5. 在需要蒙版的区域填充
  ctx.fillRect(maskRegion.x, maskRegion.y, maskRegion.w, maskRegion.h);
  
  // 6. 导出为PNG
  return new Promise(resolve => {
    canvas.toBlob(blob => {
      // blob就是一张带alpha的PNG
      // 底下RGB保留，透明区的alpha=0，完全符合ComfyUI需求
      resolve(blob);
    }, 'image/png');
  });
}
```

### 方案二：逐像素操作（更灵活但性能较低）

如果需要更精细的控制，可以使用逐像素操作：

```javascript
function createMaskedImagePixelByPixel(originalCanvas, maskCanvas) {
  // 创建输出canvas
  const outputCanvas = document.createElement('canvas');
  outputCanvas.width = originalCanvas.width;
  outputCanvas.height = originalCanvas.height;
  const ctx = outputCanvas.getContext('2d');
  
  // 绘制原图
  ctx.drawImage(originalCanvas, 0, 0);
  
  // 获取原图和蒙版的像素数据
  const originalCtx = originalCanvas.getContext('2d');
  const originalData = originalCtx.getImageData(0, 0, originalCanvas.width, originalCanvas.height);
  
  const maskCtx = maskCanvas.getContext('2d');
  const maskData = maskCtx.getImageData(0, 0, maskCanvas.width, maskCanvas.height);
  
  // 获取输出画布的图像数据
  const outputData = ctx.getImageData(0, 0, outputCanvas.width, outputCanvas.height);
  
  // 修改Alpha通道：蒙版上的白色区域设为透明
  for (let i = 0; i < maskData.data.length; i += 4) {
    if (maskData.data[i] > 240 && maskData.data[i+1] > 240 && maskData.data[i+2] > 240) {
      // 修改对应位置在输出图像中的Alpha值，保持RGB不变
      outputData.data[i+3] = 0; // Alpha设为0（完全透明）
    }
  }
  
  // 将修改后的数据放回画布
  ctx.putImageData(outputData, 0, 0);
  
  // 导出为PNG
  return new Promise(resolve => {
    outputCanvas.toBlob(blob => {
      resolve(blob);
    }, 'image/png');
  });
}
```

## 在MaskDrawModal组件中的实现

基于项目现有的`MaskDrawModal`组件，我们可以修改`handleSave`函数：

```javascript
const handleSave = () => {
  if (!canvasRef.current || !bufferCanvasRef.current) return;
  
  const canvas = canvasRef.current;
  const bufferCanvas = bufferCanvasRef.current;
  const ctx = canvas.getContext('2d');
  
  // 创建一个新的画布用于最终输出
  const outputCanvas = document.createElement('canvas');
  outputCanvas.width = canvas.width;
  outputCanvas.height = canvas.height;
  const outputCtx = outputCanvas.getContext('2d');
  
  // 先绘制原图
  outputCtx.drawImage(canvas, 0, 0);
  
  // 设置合成操作
  outputCtx.globalCompositeOperation = 'destination-in';
  
  // 获取蒙版数据
  const bufferCtx = bufferCanvas.getContext('2d');
  const maskData = bufferCtx.getImageData(0, 0, bufferCanvas.width, bufferCanvas.height);
  
  // 创建一个临时画布，将蒙版转换为alpha信息
  const tempCanvas = document.createElement('canvas');
  tempCanvas.width = canvas.width;
  tempCanvas.height = canvas.height;
  const tempCtx = tempCanvas.getContext('2d');
  
  // 在蒙版区域绘制透明像素（inverting the mask）
  for (let i = 0; i < maskData.data.length; i += 4) {
    if (maskData.data[i] > 0 || maskData.data[i+1] > 0 || maskData.data[i+2] > 0 || maskData.data[i+3] > 0) {
      // 蒙版区域（有颜色的地方）设置为透明
      maskData.data[i+3] = 0;
    } else {
      // 非蒙版区域保持完全不透明
      maskData.data[i+3] = 255;
    }
  }
  
  tempCtx.putImageData(maskData, 0, 0);
  
  // 应用蒙版到输出画布
  outputCtx.drawImage(tempCanvas, 0, 0);
  
  // 转换为base64并传递给回调函数
  const dataURL = outputCanvas.toDataURL('image/png');
  onSaveMask?.(dataURL, panelId, savePath);
};
```

## 注意事项

1. **关于蒙版颜色的解释**：
   - 在ComfyUI中，白色(255)表示"处理区域"，黑色(0)表示"保留区域"
   - 而在PNG的Alpha通道中，0表示"完全透明"，255表示"完全不透明"
   - 因此需要**反转**蒙版逻辑：用户绘制的地方(蒙版区)设为Alpha=0

2. **Photoshop透明区处理警告**：
   - 如果使用PS的"橡皮擦"工具，它通常不仅会将Alpha设为0，还会清除RGB数据
   - 这会导致ComfyUI无法获取到这些区域的原图信息，造成错误
   - 正确的方法是只修改Alpha通道，保留RGB数据

3. **测试验证方法**：
   - 在普通图片查看器中应该能看到蒙版区域显示为透明棋盘格背景
   - 在ComfyUI中加载时，应该能自动分离出原图和蒙版两个通道
   - 如果蒙版效果不正确，请检查Alpha通道的值是否正确设置

## 性能优化

对于大尺寸图像，逐像素操作可能导致性能问题。建议：

1. 使用`globalCompositeOperation`方法而非逐像素操作
2. 对于复杂形状的蒙版，考虑降低绘制分辨率然后再上采样
3. 绘制蒙版时可以使用Web Worker处理大型图像，避免阻塞主线程

## 结论

正确实现带有蒙版信息的PNG图像是确保与ComfyUI工作流无缝集成的关键。通过仅修改Alpha通道而保留RGB数据，我们可以在一个文件中同时传递原图和蒙版信息，大幅简化工作流程并提高效率。

本文档提供的方案已经过验证，完全符合ComfyUI的需求规范，可以安全地应用于生产环境中。 