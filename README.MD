# AIBIKINI 项目综合设计文档

## 目录
1. [项目概述](#项目概述)  
2. [整体功能结构](#整体功能结构)  
3. [功能模块说明](#功能模块说明)  
   - [模特图模块](#模特图模块)  
   - [款式设计模块](#款式设计模块)  
   - [快捷工具模块](#快捷工具模块)  
   - [教程模块](#教程模块)  
4. [人群 / 款式 / 风格 分类](#人群--款式--风格-分类)  
5. [导航与交互](#导航与交互)  
6. [视觉设计与交互风格](#视觉设计与交互风格)  
7. [系统架构与技术栈](#系统架构与技术栈)  
8. [其他业务逻辑补充](#其他业务逻辑补充)

---

## 项目概述

- **项目名称**：AIBIKINI  
- **核心定位**：使用 Stable Diffusion + ComfyUI 工作流辅助泳装设计与生成泳装模特图的 Web 平台。  
- **收费与会员系统**：提供收费会员功能，用户可订阅解锁高级功能或更高使用额度。

---

## 整体功能结构

├─ 顶部导航栏（全局固定）
│    ├─ LOGO（返回主页，无鼠标悬停/点击动效）
│    ├─ 首页
│    ├─ 款式设计
│    ├─ 模特图
│    ├─ AI视频
│    ├─ 快捷工具
│    ├─ 教程
│    ├─ 订阅（高亮/渐变动效）
│    └─ 登录 / 注册
│
├─ 左侧侧边栏（页面不同，内容不同）
│    ├─ 首页侧边栏：常用功能（可编辑）
│    │    ├─ 时尚大片
│    │    ├─ 模特换装
│    │    ├─ 换模特
│    │    ├─ 服装复色
│    │    ├─ 换背景
│    │    ├─ 换姿势
│    │    ├─ 爆款开发
│    │    ├─ 款式优化
│    │    ├─ 灵感探索
│    │    ├─ 换面料
│    │    ├─ 生成线稿
│    │    └─ [编辑按钮 -> 弹窗 -> 可选所有细分功能模块]
│    ├─ 模特图侧边栏：时尚大片、模特换装、换模特、服装复色、换背景、换姿势、虚拟模特、细节还原、手部修复
│    ├─ 款式设计侧边栏：爆款开发、款式优化、灵感探索、换面料、生成线稿
│    ├─ AI视频侧边栏：图文成片、多图成片
│    ├─ 快捷工具侧边栏：自动抠图、智能扩图、图片取词、高清放大、消除笔
│    └─ 教程侧边栏：显示教程目录
│
└─ 主体内容区域（根据所选功能进行展示）


> **说明**：  
> - 任何功能模块在添加、删除或修改时，需要更新顶部导航栏、侧边栏和所有引用它们的地方，保持全站结构一致。  
> - 图标均采用单色简约风格（SVG / iconfont），保持视觉统一。

---

## 功能模块说明

### 模特图模块

1. **时尚大片（创意模特图）**  
   - 场景：电商头图、详情图、海报等。  
   - 目标：模特 + 环境背景 + 服装完美融合，可上传人台图或使用专属/虚拟模特。  
   - 引用分类：可结合人群/款式/风格信息，丰富提示词。

2. **模特换装**  
   - 场景：已有模特图，需快速替换服装。  
   - 功能：自动/手动涂抹识别服装区域，避免再次拍摄。

3. **服装复色（服装换色）**  
   - 场景：对同款服装进行多色/多印花上架。  
   - 功能：快速P出不同配色或印花设计。

4. **换背景**  
   - 场景：替换模特背景，为图片提供更多场景。

5. **换姿势**  
   - 场景：智能调整模特姿势，生成不同动作姿势的专业展示效果。  
   - 功能：通过AI技术智能调整模特姿势，支持多种角度和姿态的转换。

6. **虚拟模特（专属模特）**  
   - 场景：一键生成多角度虚拟模特，可收藏或下载，用于后续时尚大片等。

7. **细节还原**  
   - 场景：智能还原服装细节，保持细节元素一致性。

8. **手部修复**  
   - 场景：智能修复图片中的手部瑕疵，提升图片质量。

> 后续可扩展：AI 换模特、AI 换脸、AI 视频等。

---

### 款式设计模块

1. **爆款开发**  
   - 融合「版型、面料(印花)、配饰」三要素 + 用户提供的参考图/描述，生成新款式。  
   - 可针对不同人群、款式、风格进行组合和灵感碰撞。
   - 引用分类：在提交信息时，需选择 人群/款式/风格信息。

2. **款式优化（改款大师）**  
   - 对已有款式图进行局部涂抹修改。  
   - 结合文字描述、随机种子等生成新的改款方案。
   - 引用分类：在描述欲改动方向时，也可选择调用统一的人群、款式、风格列表。

3. **灵感探索（创意工坊）**  
   - 为无明确思路的设计师提供大量随机设计图，帮助激发创意。  
   - 仅需选择 人群、款式、风格，可选填简单提示词。
   - 引用分类：严格调用统一的人群、款式、风格列表。

4. **换面料**  
   - 智能更换服装面料纹理，呈现不同材质效果。

5. **生成线稿**  
   - AI智能生成服装线稿，为设计提供精准的轮廓基础。
   - 通过先进的AI技术，将服装图片转换为精确的线稿，为设计师提供清晰的轮廓参考。

---

### 快捷工具模块

1. **自动抠图（背景移除 + 一键抠衣）**  
   - 快速去背景或仅保留服装。

2. **智能扩图**  
   - 自动延展原图背景或场景。

3. **图片取词**  
   - 提取图片中的文本信息。

4. **高清放大**  
   - AI放大图片，提升清晰度。

5. **消除笔**  
   - 去除水印、小瑕疵或杂物。

### AI视频模块

1. **图文成片**  
   - 场景：将文本描述和参考图像转换为产品视频。  
   - 功能：根据文字和图片生成专业泳装展示短视频。

2. **多图成片**  
   - 场景：将多张产品图片自动组合成视频。  
   - 功能：智能编排多张图片，添加转场效果，生成连贯的展示视频。

---

### 教程模块

- 展示平台操作指南、实例教学、常见问题等；侧边栏显示教程章节目录。

---

## 人群 / 款式 / 风格 分类

为了全站统一，在使用人群/款式/风格信息时，均使用下列分类：

### 人群信息分类
按性别划分：
- 女性
- 男性

按年龄划分：
- 成人
- 少年
- 大童
- 中童
- 小童
- 婴幼儿

按地区划分：
- 欧美
- 东亚
- 非洲
- 拉美
- 中东

按身材划分：
- 匀称型
- 运动型
- 丰满型

### 款式信息分类
- 分体泳衣（比基尼、套装）  
- 连体泳衣（一件式泳衣）  
- 坦基尼（背心式分体泳衣）  
- 和服罩衫  
- 套头罩衫  
- 罩裙

### 风格信息分类
- 度假  
- 优雅  
- 甜美  
- 性感  
- 节日派对  
- 简约  
- 黑人  
- 成熟  
- 复古  
- 街头  
- 运动  
- 民族  
- 暗黑  
- 废土  
- 中国风

> 在爆款开发、灵感探索、时尚大片等需要选择人群/款式/风格的地方，均从这份列表中提供可选项。

---

## 导航与交互

1. **顶部导航栏**  
   - LOGO：点击回主页，无 hover/点击动效。  
   - 首页 / 款式设计 / 模特图 / 快捷工具 / 教程：点击切换页面，高亮当前所在。  
   - 订阅：右上角渐变按钮，进入付费订阅页面。  
   - 登录 / 注册：进入用户登录注册流程。

2. **左侧侧边栏**  
   - 首页 → "常用功能"（默认包含爆款开发、款式优化、灵感探索、换面料、生成线稿、时尚大片、模特换装、换模特、服装复色、换背景、换姿势、虚拟模特、细节还原、手部修复、图文成片、多图成片、自动抠图、智能扩图、图片取词、高清放大）  
     - 编辑按钮 → 弹窗中可选所有细分功能模块，勾选后显示到首页侧边栏。  
   - 模特图 / 款式设计 / 快捷工具 → 显示对应细分功能。  
   - 教程 → 显示教程目录。

3. **主体内容区域**  
   - 显示与当前功能相关的表单、预览、结果等。  
   - 有关人群/款式/风格的选择器需调用同一套分类。

---

## 视觉设计与交互风格

- 现代简洁风（Apple / Google）  
- 单色线性图标（SVG / iconfont）  
- 按钮hover动效：微亮/阴影等  
- 适度留白、信息分块  
- ComfyUI 提示音：任务完成可播放提示音  
- 订阅按钮：使用渐变色，突出重要

---

## 系统架构与技术栈

- **前端**：HTML + CSS + JavaScript (React / Vue / 原生JS)  
- **后端**：对接 ComfyUI（Stable Diffusion）工作流  
- **数据库**：用户登录、订阅状态、图片存储、作品记录等
- **安全机制**：
  - 用户认证：基于JWT的认证系统，保护API访问
  - 文件访问控制：用户只能访问属于自己的文件，每个用户拥有独立的文件存储空间
  - CSRF保护：防止跨站请求伪造攻击
  - 数据验证：API输入验证和清理

> **注意**: 生产环境部署前的安全配置和改进事项请参考 [部署注意事项文档](./DEPLOYMENT_NOTES.md)，特别是关于图片访问安全性的改进建议。

---

## 其他业务逻辑补充

- **会员/订阅系统**：前端需根据用户是否付费会员限制高级功能或使用次数。  
- **图片处理与存储**：上传/生成图片的管理，考虑水印、版权等。  
- **图片反推提示词（爆款侦探）**：可整合到上传图片窗口，默认折叠，需要时展开。  
- **多语言**（若有需求）：可引入i18n国际化方案。

### 任务组件逻辑说明
- **组件构成**：一个任务组件由以下部分组成
  - 服装组件：用户上传的原图（始终只有一张）
  - 模特组件：选择或自定义的模特设置
  - 场景组件：选择或自定义的场景设置
  - 高级组件：整体效果的自定义设置

- **图片生成**：
  - 输入：使用同一套组件设置（原图+模特+场景+高级设置）
  - 输出：可以生成1-4张不同效果的图片

- **重新编辑处理**：
  - 使用最初上传的原图作为服装组件
  - 保留原有的模特、场景和高级设置
  - 避免重复抠图处理，节省资源

---

## 日志系统

项目现已集成了统一的日志系统，用于规范化日志记录和错误处理。

### 前端日志系统

前端日志系统位于 `src/utils/logger.js`，提供以下功能：

- 支持不同级别的日志：DEBUG、INFO、WARN、ERROR
- 根据环境自动调整日志级别
- 格式化日志输出，包含时间戳和日志级别
- 支持附加数据的记录
- 可配置自定义日志处理函数

#### 使用方法

```javascript
import logger from './utils/logger';

// 记录不同级别的日志
logger.debug('调试信息', { key: 'value' });
logger.info('普通信息');
logger.warn('警告信息');
logger.error('错误信息', error);

// 设置日志级别
logger.setLevel('INFO'); // 只显示INFO及以上级别的日志
```

### 服务器端日志系统

服务器端日志系统位于 `server/src/utils/logger.js`，除了前端日志系统的功能外，还提供：

- 日志文件记录（生产环境）
- HTTP请求日志记录
- 错误日志单独存储

#### 使用方法

```javascript
const logger = require('./utils/logger');

// 记录不同级别的日志
logger.debug('调试信息', { key: 'value' });
logger.info('普通信息');
logger.warn('警告信息');
logger.error('错误信息', error);

// 记录HTTP请求（通常通过中间件自动记录）
logger.httpRequest(req, res, responseTime);
```

### 错误处理

项目使用统一的错误处理机制：

- 前端：`src/services/errorHandler.js` 提供统一的错误处理函数
- 服务器端：`server/src/utils/error.js` 提供API错误类和错误处理中间件

#### 使用方法

前端：
```javascript
import handleError from '../services/errorHandler';

try {
  // 业务逻辑
} catch (error) {
  return handleError(error, '操作上下文');
}
```

服务器端：
```javascript
const { createError } = require('./utils/error');

// 创建业务错误
throw createError('资源不存在', 404);
```

### 环境配置

- 开发环境：显示所有级别的日志
- 测试环境：只显示WARN和ERROR级别的日志
- 生产环境：前端只显示ERROR级别的日志，服务器端记录INFO及以上级别的日志到文件

**至此，这份文档涵盖了 AIBIKINI 网站的核心功能框架和前端设计要点。**  

