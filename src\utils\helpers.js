// 防抖函数
export const debounce = (fn, delay) => {
  let timer = null;
  return (...args) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}; 


 export async function loadImage(imageUrl) {
  const img = new Image();
  // const response = await fetch(imageUrl);
  // const blob = await response.blob();
  // const imgUrl = URL.createObjectURL(blob);
  img.src = imageUrl;
  return img;
}
