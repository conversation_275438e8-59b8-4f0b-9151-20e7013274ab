/* 导入统一面板样式 */
@import '../../styles/panels.css';

/* 面板组件基础样式 */
.panel-component {
  margin-bottom: 10px;
}

/* 信息按钮样式 */
.info-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 0;
}

.info-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 信息图标样式 */
.info-icon {
  width: 16px;
  height: 16px;
  color: #666;
  opacity: 0.7;
}

.info-btn:hover .info-icon {
  opacity: 1;
  color: #1890ff;
}

.magnification-size-icon {
  display: none;
} 