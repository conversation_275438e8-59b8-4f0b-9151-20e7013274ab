/* 使用通用面板样式 */
@import '../../styles/panels.css';

/* 模特原图特有的样式可以在此处添加 */
.mask-status {
  margin-left: 8px !important;
  padding: 2px 8px !important;
  background-color: rgba(0, 200, 0, 0.1) !important;
  color: #42b983 !important;
  border-radius: 4px !important;
  font-size: 12px !important;
}

.mask-status.warning {
  background-color: rgba(255, 153, 0, 0.1) !important;
  color: #ff9900 !important;
  font-weight: 500 !important;
}

.selected-model-preview {
  position: relative;
  background-image: linear-gradient(45deg, #d1d1d1 25%, transparent 25%),
                    linear-gradient(-45deg, #d1d1d1 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #d1d1d1 75%),
                    linear-gradient(-45deg, transparent 75%, #d1d1d1 75%);
  background-size: 12px 12px;
  background-position: 0 0, 0 6px, 6px -6px, -6px 0px;
  background-color: var(--bg-primary);
}
[data-theme="dark"] .selected-model-preview {
  background-image: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                    linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
  background-size: 12px 12px;
  background-position: 0 0, 0 6px, 6px -6px, -6px 0px;
  background-color: var(--bg-primary);
} 