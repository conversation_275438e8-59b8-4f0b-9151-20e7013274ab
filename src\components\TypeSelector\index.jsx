import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { MdOutlineHelpOutline } from 'react-icons/md';
import './index.css';
import '../../../src/styles/buttons.css'; // 导入按钮样式，包括开关组件
import TipPopup from '../TipPopup'; // 导入提示弹窗组件

// 选项对应的数字值定义
// 1: 浅色服装
// 2: 中性色服装
// 3: 深色服装
// 这些数字值会直接传递给后端的comfyUI工作流节点

const TypeSelector = ({
  onSelect,
  defaultValue = 2, // 修改默认值为2，表示中性色服装
  pageType = 'recolor', // 添加页面类型参数，默认为复色页面
}) => {
  const [selectedType, setSelectedType] = useState(defaultValue);
  
  // 提示弹窗相关状态
  const [isTipVisible, setIsTipVisible] = useState(false);
  const [tipPosition, setTipPosition] = useState({ left: 0, top: 0 });
  const tipButtonRef = useRef(null);
  
  // 组件初始化时，确保有默认选中值
  useEffect(() => {
    if (!selectedType) {
      const initialType = 2; // 默认选择第二个选项：中性色服装
      setSelectedType(initialType);
      onSelect && onSelect(initialType);
    }
  }, []);

  // 添加新的useEffect，监听defaultValue的变化
  useEffect(() => {
    if (defaultValue && defaultValue !== selectedType) {
      console.log('TypeSelector - defaultValue变化:', defaultValue);
      setSelectedType(defaultValue);
    }
  }, [defaultValue]);

  const handleSelect = (typeValue, currentValue) => {
    // 如果当前已经选中，且要取消选中，则不允许操作
    // 确保必须有一个选项被选中
    if (currentValue === typeValue) {
      return;
    }
    
    setSelectedType(typeValue);
    onSelect && onSelect(typeValue);
  };

  // 处理提示按钮点击
  const handleShowTip = () => {
    if (tipButtonRef.current) {
      const rect = tipButtonRef.current.getBoundingClientRect();
      setTipPosition({
        left: rect.left + rect.width + 28,
        top: rect.top - 8
      });
    }
    setIsTipVisible(true);
  };

  // 处理关闭提示
  const handleCloseTip = () => {
    setIsTipVisible(false);
  };

  // 根据页面类型获取不同的提示文字和按钮选项
  const getPageContent = () => {
    switch (pageType) {
      case 'recolor':
        return {
          tip: [
            '• 务必根据上传图片的服装颜色选择合适的类型，否则效果不佳',
            '• 黑色和颜色太深的服装由于色彩明度太低，很难被更换颜色，不建议尝试！'
          ].join('\n'),
          options: [
            { value: 1, label: '浅色服装' },
            { value: 2, label: '中性色服装' },
            { value: 3, label: '深色服装' }
          ]
        };
      case 'change-posture':
        return {
          tip: [
            '• "换姿势表情"：仅更换模特的姿势表情，背景保持不变。',
            '• "换姿势表情+换背景"：同时更换模特姿势表情和背景，适合需要整体风格变化的场景。',
            '• 请选择与您的需求相符的类型，类型不同生成效果会有较大差异。'
          ].join('\n'),
          options: [
            { label: '换姿势表情', value: 1 },
            { label: '换姿势表情+换背景', value: 2 }
          ]
        };
      case 'divergent':
        return {
          tip: [
            '• "自动生成"：系统将根据上传的款式图自动分析并生成延伸款式。',
            '• "自定义描述"：您可以输入具体的描述要求，系统将根据您的描述生成延伸款式。',
            '• 建议先尝试自动生成，如需更精确的控制可选择自定义描述。'
          ].join('\n'),
          options: [
            { label: '自动生成', value: 1 },
            { label: '自定义描述', value: 2 }
          ]
        };
      // 可以添加其他页面类型的内容
      default:
        return {
          tip: '',
          options: [
            { value: 1, label: '选项1' },
            { value: 2, label: '选项2' },
            { value: 3, label: '选项3' }
          ]
        };
    }
  };

  const { tip, options } = getPageContent();

  return (
    <>
      <div className="type-selector">
        <div className="type-selector-content">
          <div className="type-selector-label">
            <span>类型选择</span>
          </div>
          <div className="type-selector-main">
            <div className="type-selector-switches">
              {options.map((option) => (
                <div key={option.value} className="switch-item">
                  <span className="switch-label">{option.label}</span>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={selectedType === option.value}
                      onChange={() => handleSelect(option.value, selectedType)}
                    />
                    <span className="toggle-track"></span>
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* 提示按钮 */}
        <button 
          className="tip-button-common type-selector-tip-button"
          onClick={handleShowTip}
          ref={tipButtonRef}
          title="查看使用提示"
        >
          <span className="tip-text">点我</span>
          <MdOutlineHelpOutline />
        </button>
      </div>
      
      {/* 提示弹窗 */}
      <TipPopup 
        type="type-selector"
        position={tipPosition}
        isVisible={isTipVisible}
        onClose={handleCloseTip}
        content={tip}
      />
    </>
  );
};

TypeSelector.propTypes = {
  onSelect: PropTypes.func,
  defaultValue: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.oneOf([null])
  ]),
  pageType: PropTypes.string, // 添加页面类型属性
};

export default TypeSelector; 