/**
 * RunningHub任务监控组件
 * 用于监控和管理RunningHub任务状态
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Alert,
  Snackbar,
  Tooltip,
  Grid,
  Avatar
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import taskManager from '../../services/runningHub/taskManager.js';
import { formatTaskStatus, calculateTaskDuration, processTaskResults } from '../../services/runningHub/utils.js';

const TaskMonitor = () => {
  const [tasks, setTasks] = useState([]);
  const [selectedTask, setSelectedTask] = useState(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [resultDialogOpen, setResultDialogOpen] = useState(false);
  const [taskResults, setTaskResults] = useState([]);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  const [refreshing, setRefreshing] = useState(false);

  // 定期刷新任务列表
  useEffect(() => {
    const interval = setInterval(() => {
      refreshTasks();
    }, 5000); // 每5秒刷新一次

    // 初始加载
    refreshTasks();

    return () => clearInterval(interval);
  }, []);

  const refreshTasks = async () => {
    try {
      setRefreshing(true);
      const activeTasks = taskManager.getActiveTasks();
      setTasks(activeTasks);
    } catch (error) {
      console.error('刷新任务列表失败:', error);
      showSnackbar('刷新任务列表失败', 'error');
    } finally {
      setRefreshing(false);
    }
  };

  const handleCancelTask = async (taskId) => {
    try {
      if (window.confirm('确定要取消这个任务吗？')) {
        const result = await taskManager.cancelTask(taskId);
        if (result.success) {
          showSnackbar('任务取消成功', 'success');
          refreshTasks();
        } else {
          showSnackbar(`取消任务失败: ${result.error}`, 'error');
        }
      }
    } catch (error) {
      console.error('取消任务失败:', error);
      showSnackbar('取消任务失败', 'error');
    }
  };

  const handleViewDetails = (task) => {
    setSelectedTask(task);
    setDetailDialogOpen(true);
  };

  const handleViewResults = (task) => {
    if (task.results && task.results.length > 0) {
      setTaskResults(processTaskResults(task.results));
      setResultDialogOpen(true);
    } else {
      showSnackbar('任务暂无结果', 'info');
    }
  };

  const handleDownloadResult = (result) => {
    if (result.downloadUrl) {
      window.open(result.downloadUrl, '_blank');
    }
  };

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'SUCCESS':
        return <CheckCircleIcon color="success" />;
      case 'FAILED':
        return <ErrorIcon color="error" />;
      case 'RUNNING':
        return <PlayArrowIcon color="primary" />;
      case 'QUEUED':
        return <ScheduleIcon color="warning" />;
      default:
        return <ScheduleIcon />;
    }
  };

  const getStatusChip = (status) => {
    const statusInfo = formatTaskStatus(status);
    return (
      <Chip
        label={statusInfo.label}
        color={statusInfo.color}
        size="small"
        icon={getStatusIcon(status)}
      />
    );
  };

  const calculateProgress = (task) => {
    if (task.status === 'SUCCESS') return 100;
    if (task.status === 'FAILED') return 0;
    if (task.status === 'RUNNING') return 50; // 假设运行中为50%
    if (task.status === 'QUEUED') return 10;  // 排队中为10%
    return 0;
  };

  return (
    <Box>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">任务监控</Typography>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={refreshTasks}
              disabled={refreshing}
            >
              {refreshing ? '刷新中...' : '刷新'}
            </Button>
          </Box>

          {tasks.length === 0 ? (
            <Alert severity="info">
              暂无活跃任务
            </Alert>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>任务ID</TableCell>
                    <TableCell>状态</TableCell>
                    <TableCell>进度</TableCell>
                    <TableCell>创建时间</TableCell>
                    <TableCell>执行时长</TableCell>
                    <TableCell>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tasks.map((task) => (
                    <TableRow key={task.taskId}>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {task.taskId}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {getStatusChip(task.status)}
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          <LinearProgress
                            variant="determinate"
                            value={calculateProgress(task)}
                            sx={{ width: 100 }}
                          />
                          <Typography variant="caption">
                            {calculateProgress(task)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(task.createdAt).toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {calculateTaskDuration(task.createdAt, task.completedAt).formatted}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={1}>
                          <Tooltip title="查看详情">
                            <IconButton
                              size="small"
                              onClick={() => handleViewDetails(task)}
                            >
                              <VisibilityIcon />
                            </IconButton>
                          </Tooltip>
                          
                          {task.results && task.results.length > 0 && (
                            <Tooltip title="查看结果">
                              <IconButton
                                size="small"
                                onClick={() => handleViewResults(task)}
                                color="primary"
                              >
                                <DownloadIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          
                          {(task.status === 'RUNNING' || task.status === 'QUEUED') && (
                            <Tooltip title="取消任务">
                              <IconButton
                                size="small"
                                onClick={() => handleCancelTask(task.taskId)}
                                color="error"
                              >
                                <CancelIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* 任务详情对话框 */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>任务详情</DialogTitle>
        <DialogContent>
          {selectedTask && (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">任务ID</Typography>
                <Typography variant="body2" fontFamily="monospace">
                  {selectedTask.taskId}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">状态</Typography>
                {getStatusChip(selectedTask.status)}
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">创建时间</Typography>
                <Typography variant="body2">
                  {new Date(selectedTask.createdAt).toLocaleString()}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">执行时长</Typography>
                <Typography variant="body2">
                  {calculateTaskDuration(selectedTask.createdAt, selectedTask.completedAt).formatted}
                </Typography>
              </Grid>
              {selectedTask.netWssUrl && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2">WebSocket URL</Typography>
                  <Typography variant="body2" fontFamily="monospace" sx={{ wordBreak: 'break-all' }}>
                    {selectedTask.netWssUrl}
                  </Typography>
                </Grid>
              )}
              {selectedTask.promptTips && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2">提示信息</Typography>
                  <Typography variant="body2">
                    {selectedTask.promptTips}
                  </Typography>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialogOpen(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 任务结果对话框 */}
      <Dialog
        open={resultDialogOpen}
        onClose={() => setResultDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>任务结果</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            {taskResults.map((result, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <Avatar sx={{ width: 24, height: 24 }}>
                        {index + 1}
                      </Avatar>
                      <Typography variant="subtitle2">
                        {result.fileName || `结果 ${index + 1}`}
                      </Typography>
                    </Box>
                    
                    {result.fileType === 'png' || result.fileType === 'jpg' || result.fileType === 'jpeg' ? (
                      <Box
                        component="img"
                        src={result.downloadUrl}
                        alt={`结果 ${index + 1}`}
                        sx={{
                          width: '100%',
                          height: 200,
                          objectFit: 'cover',
                          borderRadius: 1,
                          mb: 1
                        }}
                      />
                    ) : (
                      <Box
                        sx={{
                          width: '100%',
                          height: 200,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          bgcolor: 'grey.100',
                          borderRadius: 1,
                          mb: 1
                        }}
                      >
                        <Typography variant="body2" color="textSecondary">
                          {result.fileExtension?.toUpperCase() || '文件'}
                        </Typography>
                      </Box>
                    )}
                    
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="caption" color="textSecondary">
                        {result.fileSize}
                      </Typography>
                      <Button
                        size="small"
                        startIcon={<DownloadIcon />}
                        onClick={() => handleDownloadResult(result)}
                      >
                        下载
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setResultDialogOpen(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TaskMonitor;
