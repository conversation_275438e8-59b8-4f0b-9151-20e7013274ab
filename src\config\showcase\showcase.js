// Base URL for showcase images on OSS
export const SHOWCASE_BASE_URL = 'https://file.aibikini.cn/config/showcase/';

// 案例分类标签配置
export const SHOWCASE_TAGS = [
  { id: 'all', name: '全部' },
  // 模特图模块
  { id: 'fashion', name: '时尚大片', icon: 'FiCamera', category: '模特图' },
  { id: 'tryon', name: '模特换装', icon: 'FiShirt', category: '模特图' },
  { id: 'recolor', name: '服装复色', icon: 'FiDroplet', category: '模特图' },
  { id: 'background', name: '换背景', icon: 'FiImage', category: '模特图' },
  { id: 'change-posture', name: '换姿势', icon: 'FiRotateCw', category: '模特图' },
  { id: 'virtual', name: '虚拟模特', icon: 'FiUsers', category: '模特图' },
  { id: 'change-model', name: '换模特', icon: 'FiUserCheck', category: '模特图' },
  { id: 'detail-migration', name: '细节还原', icon: 'FiZoomIn', category: '模特图' },
  { id: 'hand-fix', name: '手部修复', icon: 'FiTool', category: '模特图' },
  
  // 款式设计模块
  { id: 'trending', name: '爆款开发', icon: 'FiTrendingUp', category: '款式设计' },
  { id: 'divergent', name: '爆款延伸', icon: 'FiGitBranch', category: '款式设计' },
  { id: 'optimize', name: '款式优化', icon: 'FiEdit2', category: '款式设计' },
  { id: 'inspiration', name: '灵感探索', icon: 'FiShuffle', category: '款式设计' },
  { id: 'fabric', name: '换面料', icon: 'FiLayers', category: '款式设计' },
  { id: 'drawing', name: '生成线稿', icon: 'FiEdit', category: '款式设计' },
  
  // AI视频模块
  // { id: 'imgtextvideo', name: '图文成片', icon: 'MdOutlineOndemandVideo', category: 'AI视频' },
  // { id: 'mulimgvideo', name: '多图成片', icon: 'FiFilm', category: 'AI视频' },
  
  // 快捷工具模块
  { id: 'matting', name: '自动抠图', icon: 'FiScissors', category: '快捷工具' },
  { id: 'extend', name: '智能扩图', icon: 'FiMinimize2', category: '快捷工具' },
  { id: 'upscale', name: '高清放大', icon: 'FiMaximize2', category: '快捷工具' },
  // { id: 'inpaint', name: '消除笔', icon: 'FiEdit3', category: '快捷工具' }
];

// 功能页面配置 - now with URL paths
export const FUNCTION_CONFIGS = {
  // 模特图模块
  'fashion': { name: '时尚大片', urlPath: 'fashion', isVideo: false },
  'tryon': { name: '模特换装', urlPath: 'try-on', isVideo: false },
  'recolor': { name: '服装复色', urlPath: 'recolor', isVideo: false },
  'background': { name: '换背景', urlPath: 'background', isVideo: false },
  'change-posture': { name: '换姿势', urlPath: 'change-posture', isVideo: false },
  'virtual': { name: '虚拟模特', urlPath: 'virtual', isVideo: false },
  'change-model': { name: '换模特', urlPath: 'change-model', isVideo: false },
  'detail-migration': { name: '细节还原', urlPath: 'detail-migration', isVideo: false },
  'hand-fix': { name: '手部修复', urlPath: 'hand-fix', isVideo: false },
  
  // 款式设计模块
  'trending': { name: '爆款开发', urlPath: 'trending', isVideo: false },
  'divergent': { name: '爆款延伸', urlPath: 'divergent', isVideo: false },
  'optimize': { name: '款式优化', urlPath: 'optimize', isVideo: false },
  'inspiration': { name: '灵感探索', urlPath: 'inspiration', isVideo: false },
  'fabric': { name: '换面料', urlPath: 'fabric', isVideo: false },
  'drawing': { name: '生成线稿', urlPath: 'drawing', isVideo: false },
  
  // AI视频模块
  // 'imgtextvideo': { name: '图文成片', urlPath: 'img-text-video', videoUrlPath: 'img-text-video', isVideo: true },
  // 'mulimgvideo': { name: '多图成片', urlPath: 'multi-img-video', videoUrlPath: 'multi-img-video', isVideo: true },
  
  // 快捷工具模块
  'matting': { name: '自动抠图', urlPath: 'matting', isVideo: false },
  'extend': { name: '智能扩图', urlPath: 'extend', isVideo: false },
  'upscale': { name: '高清放大', urlPath: 'upscale', isVideo: false },
  // 'inpaint': { name: '消除笔', urlPath: 'inpaint', isVideo: false }
};

// Data is now loaded dynamically in the UI component.
// The static data generation and filtering functions are no longer needed here.

// 默认导出配置
export default {
  SHOWCASE_BASE_URL,
  SHOWCASE_TAGS,
  FUNCTION_CONFIGS,
}; 