import React, { useEffect, forwardRef, useRef } from 'react';
import './index.css';

const ResizeHandle = forwardRef(({
  onResize,
  minWidth = 20,
  maxWidth = 80,
  containerRef
}, ref) => {
  // 使用 useRef 跟踪拖动状态
  const resizingRef = useRef(false);
  
  useEffect(() => {
    if (!ref.current || !containerRef.current) return;

    const handle = ref.current;
    const container = containerRef.current;
    
    const startResizing = (e) => {
      e.preventDefault(); // 防止文本选择等默认行为
      
      // 使用 setPointerCapture 来确保即使鼠标移出元素也能继续接收事件
      handle.setPointerCapture(e.pointerId);
      
      resizingRef.current = true;
      document.body.style.cursor = 'col-resize';
      document.body.classList.add('resizing'); // 添加一个类用于防止文本选择等
    };

    const stopResizing = (e) => {
      if (!resizingRef.current) return;
      
      // 释放指针捕获
      if (e.pointerId !== undefined) {
        try {
          handle.releasePointerCapture(e.pointerId);
        } catch (error) {
          console.error('释放指针捕获失败:', error);
        }
      }
      
      resizingRef.current = false;
      document.body.style.cursor = '';
      document.body.classList.remove('resizing');
    };

    const resize = (e) => {
      if (!resizingRef.current) return;
      
      // 使用 requestAnimationFrame 确保更流畅的拖动
      requestAnimationFrame(() => {
        const containerWidth = container.offsetWidth;
        const containerRect = container.getBoundingClientRect();
        let newWidth = (e.clientX - containerRect.left) / containerWidth * 100;
        
        // 限制最小和最大宽度
        newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));
        
        onResize(newWidth);
      });
    };

    // 使用 pointer 事件而不是 mouse 事件，可以处理更广泛的输入设备
    handle.addEventListener('pointerdown', startResizing);
    handle.addEventListener('pointermove', resize);
    handle.addEventListener('pointerup', stopResizing);
    handle.addEventListener('pointercancel', stopResizing);
    
    // 鼠标可能在窗口外释放，因此仍需监听文档级事件
    document.addEventListener('pointerup', stopResizing);
    
    // 如果拖动过程中浏览器丢失焦点，也应释放捕获
    window.addEventListener('blur', () => {
      if (resizingRef.current) {
        resizingRef.current = false;
        document.body.style.cursor = '';
        document.body.classList.remove('resizing');
      }
    });

    return () => {
      handle.removeEventListener('pointerdown', startResizing);
      handle.removeEventListener('pointermove', resize);
      handle.removeEventListener('pointerup', stopResizing);
      handle.removeEventListener('pointercancel', stopResizing);
      document.removeEventListener('pointerup', stopResizing);
      window.removeEventListener('blur', stopResizing);
    };
  }, [onResize, minWidth, maxWidth, containerRef, ref]);

  return <div className="resize-handle" ref={ref} />;
});

export default ResizeHandle; 