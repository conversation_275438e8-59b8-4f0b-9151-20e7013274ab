/**
 * RunningHub路由
 * 处理RunningHub平台相关的API请求
 */

const express = require('express');
const router = express.Router();
const runningHubService = require('../services/runningHub/runningHubService');
const { auth } = require('../middleware/auth.middleware');
const { createError } = require('../utils/error');

/**
 * 创建简易任务
 * POST /api/runninghub/tasks/simple
 */
router.post('/tasks/simple', auth, async (req, res) => {
  try {
    const { apiKey, workflowId, addMetadata } = req.body;

    if (!apiKey || !workflowId) {
      return res.status(400).json({
        success: false,
        error: 'API密钥和工作流ID是必需的'
      });
    }

    const result = await runningHubService.createSimpleTask({
      apiKey,
      workflowId,
      addMetadata
    });

    res.json(result);
  } catch (error) {
    console.error('创建简易任务失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '创建任务失败'
    });
  }
});

/**
 * 创建高级任务
 * POST /api/runninghub/tasks/advanced
 */
router.post('/tasks/advanced', auth, async (req, res) => {
  try {
    const { apiKey, workflowId, nodeInfoList, addMetadata } = req.body;

    if (!apiKey || !workflowId) {
      return res.status(400).json({
        success: false,
        error: 'API密钥和工作流ID是必需的'
      });
    }

    const result = await runningHubService.createAdvancedTask({
      apiKey,
      workflowId,
      nodeInfoList,
      addMetadata
    });

    res.json(result);
  } catch (error) {
    console.error('创建高级任务失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '创建任务失败'
    });
  }
});

/**
 * 创建AI应用任务
 * POST /api/runninghub/tasks/app
 */
router.post('/tasks/app', auth, async (req, res) => {
  try {
    const { apiKey, appId, inputData } = req.body;

    if (!apiKey || !appId) {
      return res.status(400).json({
        success: false,
        error: 'API密钥和应用ID是必需的'
      });
    }

    const result = await runningHubService.createAIAppTask({
      apiKey,
      appId,
      inputData
    });

    res.json(result);
  } catch (error) {
    console.error('创建AI应用任务失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '创建任务失败'
    });
  }
});

/**
 * 查询任务状态
 * GET /api/runninghub/tasks/:taskId/status
 */
router.get('/tasks/:taskId/status', auth, async (req, res) => {
  try {
    const { taskId } = req.params;
    const { apiKey } = req.query;

    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: 'API密钥是必需的'
      });
    }

    const result = await runningHubService.getTaskStatus(taskId, apiKey);
    res.json(result);
  } catch (error) {
    console.error('查询任务状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '查询任务状态失败'
    });
  }
});

/**
 * 获取任务结果
 * GET /api/runninghub/tasks/:taskId/results
 */
router.get('/tasks/:taskId/results', auth, async (req, res) => {
  try {
    const { taskId } = req.params;
    const { apiKey } = req.query;

    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: 'API密钥是必需的'
      });
    }

    const result = await runningHubService.getTaskResults(taskId, apiKey);
    res.json(result);
  } catch (error) {
    console.error('获取任务结果失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '获取任务结果失败'
    });
  }
});

/**
 * 取消任务
 * DELETE /api/runninghub/tasks/:taskId
 */
router.delete('/tasks/:taskId', auth, async (req, res) => {
  try {
    const { taskId } = req.params;
    const { apiKey } = req.body;

    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: 'API密钥是必需的'
      });
    }

    const result = await runningHubService.cancelTask(taskId, apiKey);
    res.json(result);
  } catch (error) {
    console.error('取消任务失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '取消任务失败'
    });
  }
});

/**
 * 获取账户信息
 * GET /api/runninghub/account
 */
router.get('/account', auth, async (req, res) => {
  try {
    const { apiKey } = req.query;

    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: 'API密钥是必需的'
      });
    }

    const result = await runningHubService.getAccountInfo(apiKey);
    res.json(result);
  } catch (error) {
    console.error('获取账户信息失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '获取账户信息失败'
    });
  }
});

/**
 * 获取工作流JSON
 * GET /api/runninghub/workflows/:workflowId
 */
router.get('/workflows/:workflowId', auth, async (req, res) => {
  try {
    const { workflowId } = req.params;
    const { apiKey } = req.query;

    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: 'API密钥是必需的'
      });
    }

    const result = await runningHubService.getWorkflowJson(workflowId, apiKey);
    res.json(result);
  } catch (error) {
    console.error('获取工作流JSON失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '获取工作流JSON失败'
    });
  }
});

/**
 * 上传资源文件
 * POST /api/runninghub/upload
 */
router.post('/upload', auth, async (req, res) => {
  try {
    const { apiKey } = req.body;
    const file = req.file;

    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: 'API密钥是必需的'
      });
    }

    if (!file) {
      return res.status(400).json({
        success: false,
        error: '文件是必需的'
      });
    }

    const result = await runningHubService.uploadResource(file, apiKey);
    res.json(result);
  } catch (error) {
    console.error('上传资源失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '上传资源失败'
    });
  }
});

/**
 * 批量创建任务
 * POST /api/runninghub/tasks/batch
 */
router.post('/tasks/batch', auth, async (req, res) => {
  try {
    const { taskList } = req.body;

    if (!Array.isArray(taskList) || taskList.length === 0) {
      return res.status(400).json({
        success: false,
        error: '任务列表不能为空'
      });
    }

    const result = await runningHubService.createBatchTasks(taskList);
    res.json(result);
  } catch (error) {
    console.error('批量创建任务失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '批量创建任务失败'
    });
  }
});

/**
 * 等待任务完成
 * POST /api/runninghub/tasks/:taskId/wait
 */
router.post('/tasks/:taskId/wait', auth, async (req, res) => {
  try {
    const { taskId } = req.params;
    const { apiKey, timeout, interval } = req.body;

    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: 'API密钥是必需的'
      });
    }

    // 设置RunningHub服务的API密钥
    runningHubService.setApiKey(apiKey);

    const result = await runningHubService.waitForTaskCompletion(taskId, {
      timeout,
      interval,
      onProgress: (progress) => {
        // 这里可以通过WebSocket发送进度更新
        console.log(`任务 ${taskId} 进度:`, progress);
      }
    });

    res.json(result);
  } catch (error) {
    console.error('等待任务完成失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '等待任务完成失败'
    });
  }
});

module.exports = router;
