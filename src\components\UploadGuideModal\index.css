.upload-guide-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  /* 使用dvh确保在移动端正确显示 */
  height: 100dvh;
  z-index: 99999; /* 大幅提高z-index，确保在所有内容之上，包括移动端导航栏 */
  background-color: var(--bg-mask);
  pointer-events: auto;
}

.upload-guide-modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  position: fixed;
  left: 50%;
  top: 50%;
  width: 90%;
  max-width: 1200px;
  height: 80vh;
  max-height: 990px;
  min-height: 600px;
  transform: translate(-50%, -50%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: none !important;
  will-change: auto;
  pointer-events: auto;
  z-index: 100000; /* 确保弹窗内容在包装器之上 */
}

.upload-guide-modal-content .modal-header {
  cursor: grab;
}
.upload-guide-modal-content.dragging .modal-header {
  cursor: grabbing !important;
}

.modal-header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 16px 20px 16px;
  margin: 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  background: var(--bg-primary);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0 0px;
}

/* 移除局部标签页样式，使用全局统一样式 */
/*
.modal-tabs {
  display: flex;
  gap: 0;
  height: 36px;
  margin-bottom: -1px;
  margin-left: 16px;
}

.tab-button {
  padding: 0 24px;
  border: 1px solid transparent;
  background: transparent;
  color: var(--text-secondary);
  font-size: 13.5px;
  cursor: pointer;
  position: relative;
  transition: var(--transition-normal);
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  margin-right: 1px;
  user-select: none;
}

.tab-button:first-child {
  margin-left: 4px;
}

.tab-button:last-child {
  margin-right: 4px;
}

.tab-button:hover {
  background: transparent;
  color: var(--text-primary);
  position: relative;
}

.tab-button:not(.active):hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(100% - 2px);
  height: 28px;
  background: var(--bg-hover);
  border-radius: var(--radius-md);
}

.tab-button.active {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-color);
  border-bottom-color: var(--bg-primary);
  font-weight: 500;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--brand-primary);
  width: calc(100% - 48px);
  margin: 0 24px;
  transition: transform 0.2s ease;
}
*/

.modal-body {
  padding: 0;
  height: calc(100% - 52px);
  transform: none !important;
  transition: none !important;
  will-change: auto;
}

/* 上传区域样式 */
.upload-section {
  padding: 0px 32px 24px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
  transform: none !important;
  transition: none !important;
  will-change: auto;
}

.guide-upload-zone {
  display: block;
  width: 100%;
  min-height: 100px;
  border: 1px dashed var(--border-color);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: border-color 0.2s ease, background-color 0.2s ease;
  background: var(--bg-secondary);
  position: relative;
  transform: none !important;
  will-change: auto;
}

/* 确保圆角一致性 */
.guide-upload-zone,
.gallery-item,
.gallery-item.upload-entry {
  border-radius: var(--radius-lg) !important;
  overflow: hidden;
}

.support-tag,
.guide-upload-zone .support-tag,
.gallery-item.upload-entry .support-tag {
  position: absolute;
  top: 0;
  right: 0;
  border-top-right-radius: var(--radius-lg) !important;
  border-bottom-left-radius: var(--radius-sm) !important;
  border-top-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  overflow: hidden;
  z-index: 2;
  padding: 4px 8px;
  background: var(--brand-primary-light);
  font-size: 12px;
  color: var(--brand-primary);
  font-weight: 500;
  pointer-events: none;
  transition: var(--transition-normal);
}

/* 保持原有的样式差异 */
.gallery-item.upload-entry .support-tag {
  padding: 3px 6px;
  font-size: 11px;
}

.guide-upload-zone:hover .support-tag {
  background: var(--brand-primary-lighter);
}

.guide-upload-zone:hover {
  border-color: var(--brand-primary);
  background: var(--bg-primary);
}

.guide-upload-zone.dragging {
  border-color: var(--brand-primary);
  background: var(--bg-primary);
  box-shadow: 0 0 0 1px var(--brand-primary-light);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 16px;
  transform: none !important;
  transition: none !important;
  will-change: auto;
}

.upload-main {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  transform: none !important;
  transition: none !important;
}

.upload-main .upload-text {
  color: var(--text-secondary);
  font-size: 16px !important;
  font-weight: 400;
  line-height: 40px;
  display: inline-flex;
  align-items: center;
  margin-top: 5px;
}

.upload-requirements {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
}

.requirement-item {
  color: var(--text-secondary);
  font-size: 13px;
}

.divider {
  color: var(--border-color);
  font-size: 12px;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0 2rem;
  background: var(--brand-gradient);
  color: #fff;
  border: none;
  border-radius: var(--radius-md);
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: none !important;
  transform: none !important;
  will-change: opacity;
  position: relative;
  z-index: 1;
}

.upload-button:hover {
  opacity: 0.9;
  transform: none !important;
}

/* 暗黑模式下的上传按钮样式 */
[data-theme="dark"] .upload-button {
  color: var(--text-inverse); /* 暗黑模式下使用深色文本 */
  background: var(--brand-gradient);
}

/* 图文指导区样式 */
.guide-section {
  padding: 0px 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  background: var(--bg-primary);
}

.recommended-section {
  width: 100%;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-light);
  padding-top: 20px;
}

.error-section {
  width: 100%;
  text-align: right;
  padding-top: 0;
}

h3 {
  font-size: 16px;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.section-desc {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 12px 0;
}

.example-grid {
  display: grid;
  gap: 10px;
  grid-template-columns: repeat(7, 1fr);
}

.recommended-section .example-grid {
  justify-content: start;
}

.error-section h3 {
  text-align: right;
  color: var(--error-color);
}

.error-section .section-desc {
  text-align: right;
}

.error-section .example-grid {
  justify-content: end;
}

.error-section .example-grid .example-item {
  /* 移除justify-self: end */
}

.error-section .example-info h4 {
  text-align: center;
}

.example-item {
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
  width: 100%;
  max-width: none;
}

.example-item.recommended {
  cursor: pointer;
  position: relative;
  will-change: box-shadow;
  backface-visibility: hidden;
}

.example-item.recommended:hover {
  box-shadow: var(--shadow-md);
}

.example-item img {
  width: 100%;
  aspect-ratio: 2/3;
  object-fit: cover;
  display: block;
}

.example-info {
  padding: 4px;
}

.example-info h4 {
  font-size: 12px;
  margin: 0;
  color: var(--text-primary);
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.example-info p {
  display: none;
}

.example-info .suggestion {
  display: none;
}

/* 推荐图片悬停效果 */
.example-hover {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-mask);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.15s ease;
  will-change: opacity;
  backface-visibility: hidden;
}

.example-item.recommended:hover .example-hover {
  opacity: 1;
}

.hover-content {
  padding: 8px;
  transform: translateZ(0);
}

.use-button {
  min-width: 48px;
  height: 24px;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition-normal);
  padding: 0 12px;
  font-weight: 500;
}

.use-button:hover {
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 错误示例样式 */
.example-item.error {
  border: 1px solid var(--error-light);
}

/* 动画效果 */
@keyframes modalSlideIn {
  from {
    transform: translate(-50%, -50%) translateY(20px);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%) translateY(0);
    opacity: 1;
  }
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .example-grid {
    grid-template-columns: repeat(7, 1fr);
  }
}

@media (max-width: 768px) {
  /* 移动端弹窗定位优化 */
  .upload-guide-modal-content {
    width: 95% !important;
    height: 85vh !important;
    max-height: none !important;
    min-height: 500px !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    /* 确保不被顶部导航栏遮挡 */
    margin-top: env(safe-area-inset-top, 0px);
    /* 添加额外的顶部间距 */
    max-height: calc(100vh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px) - 20px) !important;
  }
  
  .example-grid {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .guide-section {
    padding: 12px 8px;
  }
  
  /* 移动端缩小上传区域边距 */
  .upload-section {
    padding: 0px 8px 12px;
  }
  
  /* 移动端保持模态框头部边距 */
  .modal-header {
    padding: 16px 20px 16px;  /* 保持与PC端一致的padding */
  }
  
  /* 移动端缩小画廊网格边距 */
  .gallery-grid {
    padding: 8px 8px;
  }
  
  /* 移动端缩小上传内容区域内边距 */
  .upload-content {
    padding: 8px;
    gap: 8px;
  }
  
  /* 移动端缩小上传按钮和文字 */
  .upload-button {
    font-size: 14px !important;
    padding: 0 1.5rem !important;
    height: 36px !important;
  }
  
  .upload-main .upload-text {
    font-size: 14px !important;
    line-height: 36px !important;
  }
  
  .requirement-item {
    font-size: 12px !important;
  }
  
  .divider {
    font-size: 11px !important;
  }
  
  .file-count,
  .upload-selection-info {
    display: none !important;
  }
  
  /* 768px以下移动端示例卡片圆角优化 */
  .example-item {
    border-radius: var(--radius-md);
  }
  
  /* 768px以下移动端标题和副标题文字优化 */
  h3 {
    font-size: 14px;
  }
  
  .section-desc {
    font-size: 12px;
  }
  
  /* 768px以下移动端隐藏上传区域的拖拽文字 */
  .upload-main .upload-text {
    display: none;
  }
}

@media (max-width: 480px) {
  /* 小屏幕移动端弹窗进一步优化 */
  .upload-guide-modal-content {
    width: 98% !important;
    height: 90vh !important;
    min-height: 450px !important;
    /* 确保在小屏幕上有足够的顶部间距 */
    max-height: calc(100vh - 40px) !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
  }
  
  .example-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }
  
  .example-item img {
    aspect-ratio: 2/3;
  }
  
  /* 移动端缩小示例卡片圆角 */
  .example-item {
    border-radius: var(--radius-sm);
  }
  
  .example-info h4 {
    font-size: 12px;
  }
  
  /* 480px以下进一步缩小上传按钮和文字 */
  .upload-button {
    font-size: 13px !important;
    padding: 0 1.2rem !important;
    height: 32px !important;
  }
  
  .upload-main .upload-text {
    display: none;
  }
  
  .requirement-item {
    font-size: 11px !important;
  }
  
  .divider {
    font-size: 10px !important;
  }
  
  /* 480px以下进一步缩小边距 */
  .guide-section {
    padding: 2px 0px;
  }
  
  .upload-section {
    padding: 0px 0px 6px;
  }
  
  .modal-header {
    padding: 16px 20px 16px;  /* 保持与PC端一致的padding */
  }
  
  .gallery-grid {
    padding: 0px 0px;
  }
  
  /* 480px以下进一步缩小上传内容区域 */
  .upload-content {
    padding: 2px;
    gap: 2px;
  }
  
  /* 480px以下进一步缩小标题和副标题文字 */
  h3 {
    font-size: 13px;
  }
  
  .section-desc {
    font-size: 11px;
  }
}

/* 360px以下极致优化 */
@media (max-width: 360px) {
  /* 超小屏幕移动端弹窗最大化利用空间 */
  .upload-guide-modal-content {
    width: 100% !important;
    height: 95vh !important;
    min-height: 400px !important;
    max-height: calc(100vh - 20px) !important;
    border-radius: var(--radius-md) !important; /* 稍微减小圆角 */
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
  }
  
  .upload-button {
    font-size: 12px !important;
    padding: 0 1rem !important;
    height: 30px !important;
  }
  
  .upload-main .upload-text {
    display: none;
  }
  
  .requirement-item {
    font-size: 10px !important;
  }
  
  .divider {
    font-size: 9px !important;
  }
  
  /* 360px以下最大化利用空间 */
  .guide-section {
    padding: 0px 0px;
  }
  
  .upload-section {
    padding: 0px 0px 6px;
  }
  
  .modal-header {
    padding: 16px 20px 16px;  /* 保持与PC端一致的padding */
  }
  
  .gallery-grid {
    padding: 0px 0px;
  }
  
  /* 历史视图也适用相同的边距优化 */
  .history-view .gallery-grid {
    padding: 0px 0px;
  }
  
  /* 360px以下最小化上传内容区域边距 */
  .upload-content {
    padding: 0px;
    gap: 0px;
  }
  
  /* 360px以下进一步缩小示例卡片圆角 */
  .example-item {
    border-radius: var(--radius-sm);
  }
  
  /* 360px以下最小化标题和副标题文字 */
  h3 {
    font-size: 12px;
  }
  
  .section-desc {
    font-size: 10px;
  }
}

/* 真实移动设备特殊适配 - 使用触摸媒体查询 */
@media (hover: none) and (pointer: coarse) {
  /* 真实移动设备：确保弹窗不被系统UI遮挡 */
  .upload-guide-modal {
    /* 使用动态视口单位避免被地址栏等遮挡 */
    height: 100dvh !important;
    padding: env(safe-area-inset-top, 0) env(safe-area-inset-right, 0) env(safe-area-inset-bottom, 0) env(safe-area-inset-left, 0);
  }
  
  .upload-guide-modal-content {
    width: 95% !important;
    height: 85dvh !important;
    max-height: calc(100dvh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px) - 60px) !important;
    /* 确保有足够的顶部间距避开导航栏 */
    top: calc(50% + env(safe-area-inset-top, 0px) / 2) !important;
    transform: translate(-50%, -50%) !important;
    /* 确保在任何情况下都不会被遮挡 */
    margin: 30px 0 !important;
  }
}

/* 横屏模式的特殊处理 */
@media (hover: none) and (pointer: coarse) and (orientation: landscape) {
  .upload-guide-modal-content {
    height: 90dvh !important;
    max-height: calc(100dvh - 40px) !important;
    margin: 20px 0 !important;
  }
}

.history-section {
  padding: 40px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background: var(--bg-primary);
}

.empty-history {
  text-align: center;
  color: var(--text-secondary);
  background: var(--bg-primary);
}

.empty-history p {
  font-size: 14px;
  margin: 0;
}

/* 画廊视图样式 */
.gallery-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  background: var(--bg-primary);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  width: 100%;
  padding: 20px 20px;
  margin-top: 16px;
}

.gallery-item {
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  cursor: pointer;
}

.gallery-item.upload-entry {
  border: 1px dashed var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  background: var(--bg-primary);
  position: relative;
}

.gallery-item.upload-entry:hover .support-tag {
  background: var(--brand-primary-lighter);
}

.gallery-item.upload-entry.disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: var(--bg-primary);
}

.gallery-item.upload-entry.disabled:hover {
  border-color: var(--border-color);
  background: var(--bg-primary);
}

.gallery-item.upload-entry:hover {
  border-color: var(--brand-primary);
  background: var(--brand-primary-light);
}

.gallery-item.upload-entry.dragging {
  border-color: var(--brand-primary);
  background: var(--brand-primary-light);
}

.gallery-item .upload-label {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.gallery-item .upload-icon {
  font-size: 24px;
  color: var(--brand-primary);
  margin-bottom: 16px;
  opacity: 0.75;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
}

.gallery-item.upload-entry:hover .upload-icon {
  opacity: 0.9;
}

.gallery-item .upload-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: 14px;
}

.gallery-item .upload-text .or {
  color: var(--text-tertiary);
  font-size: 12px;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.gallery-item.uploading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  overflow: hidden;
  position: relative;
  padding: 20px;
}

.progress-number {
  font-size: 16px;
  font-weight: normal;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-primary);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  letter-spacing: 0.5px;
}

.upload-status-text {
  font-size: 14px;
  color: var(--text-secondary);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.upload-progress {
  width: 90%;
  height: 4px;
  background: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  position: absolute;
  bottom: 15px;
  left: 5%;
}

.progress-bar {
  height: 100%;
  background: var(--brand-gradient);
  border-radius: 4px;
  transition: width 0.1s linear; /* 平滑过渡效果 */
}

.item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-mask);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 8px;
}

.gallery-item:hover .item-overlay {
  opacity: 1;
}

.remove-btn {
  width: 30px;
  height: 30px;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(0, 0, 0, 0.12);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
  transition: var(--transition-normal);
  font-size: 16px;
  line-height: 1;
  padding: 0;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.remove-btn:hover {
  background: #ffffff;
  color: #333;
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

@media (max-width: 1200px) {
  .gallery-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 画廊底边条样式 */
.gallery-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
  z-index: 10;
}

/* 移动端当file-count被隐藏时，让gallery-actions靠右对齐 */
@media (max-width: 768px) {
  .gallery-footer {
    justify-content: flex-end;
  }
}

/* 上传选择信息文字样式 */
.upload-selection-info {
  margin-right: auto;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 400;
  line-height: 1.4;
}

/* 保持原有的file-count样式以防其他地方使用 */
.file-count {
  margin-right: auto;
  font-size: 10px ;
  color: var(--text-secondary);
}

/* 添加按钮组样式 */
.gallery-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.confirm-button {
  height: 32px;
  padding: 0 var(--spacing-lg);
  background: var(--brand-gradient);
  color: #fff;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.confirm-button:hover {
  filter: brightness(1.1);
}

/* 禁用状态的确认按钮 - 与模特选择弹窗统一 */
.confirm-button:disabled {
  background: var(--brand-gradient);
  opacity: 0.6;
  cursor: not-allowed;
  filter: none;
  box-shadow: none;
  border: none;
}

/* 暗色模式下的禁用按钮 */
[data-theme="dark"] .confirm-button:disabled {
  background: var(--brand-gradient);
  opacity: 0.6;
}

/* 添加新的样式声明，确保角标正确应用 */
.gallery-item.upload-entry,
.guide-upload-zone {
  position: relative;
  overflow: hidden !important;
  border-radius: var(--radius-lg) !important;
}

/* 添加专门针对角标的强制样式 */
.support-tag {
  border-top-right-radius: inherit !important;
}

/* 历史记录视图样式 */
.history-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  background: var(--bg-primary);
}

.history-view .gallery-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  width: 100%;
  padding: 20px 20px;
  margin-top: 16px;
}

.history-view .gallery-item {
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
}

.history-view .gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.history-view .item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-mask);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 8px;
}

.history-view .gallery-item:hover .item-overlay {
  opacity: 1;
}

.history-view .use-button {
  width: 30px;
  height: 30px;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(0, 0, 0, 0.12);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
  transition: var(--transition-normal);
  font-size: 16px;
  line-height: 1;
  padding: 0;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

/* 添加按钮居中 */
.history-view .use-button.centered {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  font-size: 14px;
  width: 60px;
  height: 32px;
}

/* 删除按钮右上角 */
.history-view .remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  width: 30px;
  height: 30px;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(0, 0, 0, 0.12);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
  transition: var(--transition-normal);
  font-size: 16px;
  line-height: 1;
  padding: 0;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.history-view .use-button:hover {
  background: #ffffff;
  color: #333;
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

.history-view .remove-btn:hover {
  background: #ffffff;
  color: #333;
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

.history-view .no-history {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  font-style: italic;
}

@media (max-width: 1200px) {
  .history-view .gallery-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .history-view .gallery-grid {
    grid-template-columns: repeat(3, 1fr);
    padding: 8px 8px;
  }
}

@media (max-width: 480px) {
  .history-view .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 6px 6px;
  }
}

/* 标签页样式 */
.modal-tabs {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.tab-button {
  padding: 8px 16px;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
}

.tab-button::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--brand-primary);
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.tab-button:hover {
  color: var(--text-primary);
}

.tab-button.active {
  color: var(--text-primary);
  font-weight: 500;
}

.tab-button.active::after {
  transform: scaleX(1);
}

/* 添加选中状态的样式 */
.history-view .gallery-item.selected {
  position: relative;
  outline: 3px solid var(--brand-primary);
  outline-offset: 3px;
  border-radius: var(--radius-lg);
}

/* 文本按钮样式 */
.text-button {
  background: none;
  border: none;
  color: var(--brand-primary);
  font-size: 14px;
  cursor: pointer;
  padding: 0 4px;
  margin-left: 8px;
  text-decoration: underline;
}

.text-button:hover {
  color: var(--brand-hover);
}

/* 历史记录项目点击样式 */
.history-view .gallery-item {
  cursor: pointer;
}

/* 保持底边条中的文件计数中的文本垂直居中 */
.gallery-footer .file-count,
.gallery-footer .upload-selection-info {
  display: flex;
  align-items: center;
}

/* 彻底隐藏选中状态指示器 */
.history-view .selected-indicator {
  display: none !important;
}

/* 修改卡片选中状态的样式 - 双层边框效果 */
.history-view .gallery-item.selected {
  position: relative;
  outline: 3px solid var(--brand-primary);
  outline-offset: 3px;
  border-radius: var(--radius-lg);
}

/* 添加时间角标样式 */
.gallery-item .time-badge {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 10px;
  padding: 2px 5px;
  text-align: center;
  border-bottom-left-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  z-index: 2;
}

.empty-grid-item {
  visibility: hidden;
} 