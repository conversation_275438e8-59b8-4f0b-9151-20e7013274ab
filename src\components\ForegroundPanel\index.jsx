import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

/**
 * 前景图面板组件 - 用于背景页面中展示上传的前景图片和状态
 * 
 * 此组件专门用于background(换背景)页面，解决了与服装组件共用的问题
 */
const ForegroundPanel = ({
  panel,
  onExpandClick,
  onDelete,
  onReupload,
  onStatusChange,
  isActive,
  onPanelsChange
}) => {
  // 处理上传结果
  const handleUploadResult = (results) => {
    console.log('处理上传结果:', JSON.stringify(results, null, 2));
    
    try {
      // 如果是标准的success/results格式
      if (results.success && Array.isArray(results.results)) {
        const newPanels = results.results.map((result, index) => {
          console.log(`处理结果项 #${index+1}:`, result);
          
          // 检查是否有relativePath字段
          let processedImageUrl = null;
          if (result.processedFile) {
            if (result.relativePath) {
              processedImageUrl = `http://localhost:3002${result.relativePath}`;
              console.log('使用相对路径构建URL:', processedImageUrl);
            } else {
              // 直接使用上传图片，不获取处理后的图片
              console.log('使用默认路径构建URL:', processedImageUrl);
            }
          }
          
          return {
            componentId: `foreground-${index + 1}`,
            title: results.results.length === 1 ? '前景' : `前景 ${index + 1}`,
            status: result.error ? 'error' : 'completed',
            error: result.error || null,
            serverFileName: result.serverFileName,
            processedFile: result.processedFile,
            // 保存完整的processInfo以便后续使用
            processInfo: {
              serverFileName: result.serverFileName,
              processedFile: result.processedFile,
              relativePath: result.relativePath,
              url: result.url
            },
            fileInfo: {
              ...(result.fileInfo || {}),
              format: result.fileInfo?.type || 'image/png',
              serverFileName: result.serverFileName
            },
            showOriginal: true
          };
        });
        console.log('更新后的面板数据:', newPanels);
        onPanelsChange(newPanels);
        return;
      }

      // 处理初始的panels类型数据
      if (results.type === 'panels' && Array.isArray(results.panels)) {
        const panels = results.panels.map((panel, index) => ({
          ...panel,
          title: results.panels.length === 1 ? '前景' : `前景 ${index + 1}`,
          // 确保从上传结果中保存processInfo
          processInfo: panel.processInfo || null,
          // 确保保存服务器文件名
          serverFileName: panel.serverFileName || panel.processInfo?.serverFileName || panel.componentId
        }));
        onPanelsChange(panels);
        return;
      }

      throw new Error('无法识别的响应格式');

    } catch (error) {
      console.error('处理上传结果时出错:', error);
      const errorPanel = {
        componentId: `foreground-${Date.now()}`,
        title: '前景',
        status: 'error',
        error: error.message || '处理失败',
        serverFileName: results?.serverFileName || results?.filename,
        showOriginal: true
      };
      onPanelsChange([errorPanel]);
    }
  };

  const handleExpandClick = (e) => {
    const buttonRect = e.currentTarget.getBoundingClientRect();
    onExpandClick(panel, {
      top: buttonRect.top,
      left: buttonRect.left + buttonRect.width
    });
  };

  const handleDelete = () => {
    if (panel && panel.componentId) {
      onDelete?.(panel.componentId);
      message.success('已删除前景图面板');
    }
  };

  const handleReupload = () => {
    if (panel && panel.componentId) {
      onReupload?.(panel);
    }
  };

  const getImageInfo = () => {
    if (!panel || !panel.processedFile) return null;

    if (panel.fileInfo) {
      return {
        size: `${(panel.fileInfo.size / (1024 * 1024)).toFixed(2)} MB`,
        resolution: `${panel.fileInfo.width} x ${panel.fileInfo.height} px`,
        format: panel.fileInfo.format
      };
    }

    return {
      size: '--',
      resolution: '--',
      format: '--'
    };
  };

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          <ThumbnailWithPreview
            imageUrl={panel.processedFile || panel.url}
            alt="前景图上传结果"
            status={panel.status}
            error={panel.error}
            onStatusChange={onStatusChange}
            featureName="前景图预览"
            transparentBg={true}
          />
          <div className="component-text">
            <h3>前景</h3>
            <div className="component-content">
              <p>
                {panel.status === 'completed' && '上传完成'}
                {panel.status === 'processing' && '自动抠图中...'}
                {panel.status === 'error' && panel.error}
              </p>
            </div>
          </div>
        </div>
        <button 
          className={`expand-btn ${isActive ? 'active' : ''}`}
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

ForegroundPanel.propTypes = {
  panel: PropTypes.shape({
    componentId: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    status: PropTypes.oneOf(['processing', 'completed', 'error']).isRequired,
    error: PropTypes.string,
    processedFile: PropTypes.string,
    url: PropTypes.string,
    serverFileName: PropTypes.string,
    originalImage: PropTypes.string,
    type: PropTypes.string,
    source: PropTypes.string,
    fileInfo: PropTypes.shape({
      size: PropTypes.number,
      width: PropTypes.number,
      height: PropTypes.number,
      format: PropTypes.string,
      serverFileName: PropTypes.string
    }),
    showOriginal: PropTypes.bool,
  }).isRequired,
  onExpandClick: PropTypes.func.isRequired,
  onDelete: PropTypes.func,
  onReupload: PropTypes.func,
  onStatusChange: PropTypes.func,
  isActive: PropTypes.bool,
  onPanelsChange: PropTypes.func.isRequired,
};

export default ForegroundPanel; 