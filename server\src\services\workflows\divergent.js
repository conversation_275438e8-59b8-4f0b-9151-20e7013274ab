/**
 * 爆款延伸工作流配置
 * 基于现有爆款进行延伸设计
 */

const divergent = {
  prompt: {
    // 工作流节点配置
    "1": {
      "inputs": {
        "image": "example.png",
        "choose file to upload": "image"
      },
      "class_type": "LoadImage",
      "_meta": {
        "title": "LoadImage"
      }
    },
    "2": {
      "inputs": {
        "text": "爆款延伸设计，基于现有款式进行创新延伸",
        "clip": ["4", 1]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIPTextEncode"
      }
    },
    "3": {
      "inputs": {
        "text": "低质量, 模糊, 变形, 扭曲",
        "clip": ["4", 1]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIPTextEncode (negative)"
      }
    },
    "4": {
      "inputs": {
        "ckpt_name": "divergent_model.safetensors"
      },
      "class_type": "CheckpointLoaderSimple",
      "_meta": {
        "title": "CheckpointLoaderSimple"
      }
    },
    "5": {
      "inputs": {
        "samples": ["1", 0],
        "vae": ["4", 2]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAEDecode"
      }
    },
    "6": {
      "inputs": {
        "samples": ["5", 0],
        "vae": ["4", 2]
      },
      "class_type": "VAEEncode",
      "_meta": {
        "title": "VAEEncode"
      }
    },
    "7": {
      "inputs": {
        "seed": 123456789,
        "steps": 20,
        "cfg": 7,
        "sampler_name": "euler",
        "scheduler": "normal",
        "denoise": 1,
        "model": ["4", 0],
        "positive": ["2", 0],
        "negative": ["3", 0],
        "latent_image": ["6", 0]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "KSampler"
      }
    },
    "8": {
      "inputs": {
        "samples": ["7", 0],
        "vae": ["4", 2]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAEDecode"
      }
    },
    "9": {
      "inputs": {
        "filename_prefix": "divergent_output",
        "images": ["8", 0]
      },
      "class_type": "SaveImage",
      "_meta": {
        "title": "SaveImage"
      }
    }
  },
  metadata: {
    name: "爆款延伸工作流",
    description: "基于现有爆款进行延伸设计，创造更多可能性",
    category: "款式设计",
    version: "1.0.0",
    author: "AI Bikini Team",
    requiresInputFolder: false
  }
};

module.exports = divergent; 