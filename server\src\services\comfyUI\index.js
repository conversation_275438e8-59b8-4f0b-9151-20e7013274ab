/**
 * ComfyUI客户端集成
 * 提供与ComfyUI服务通信的功能，包括：
 * - 基本客户端功能（client.js）
 * - 文件上传和管理功能（upload.js）
 * - 工作流执行和结果处理功能（workflow.js）
 */

const Client = require('./client');
const extendWithUpload = require('./upload');
const extendWithWorkflow = require('./workflow');

/**
 * 创建一个完整的ComfyUI客户端实例
 * @param {Object} options 配置选项
 * @param {string} options.baseURL ComfyUI服务的基础URL
 * @param {string} options.apiKey API密钥 (可选)
 * @param {string} options.instanceId 实例ID (可选)
 * @param {string} options.userId 用户ID
 * @returns {Object} 增强的ComfyUI客户端实例
 */
function createComfyUIClient(options) {
  // 创建基础客户端
  const client = new Client(options);
  
  // 扩展客户端功能
  const uploadExtension = extendWithUpload(client);
  const workflowExtension = extendWithWorkflow(client);
  
  // 融合所有功能到客户端实例
  return Object.assign(client, {
    // 上传相关功能
    uploadImage: uploadExtension.uploadImage.bind(uploadExtension),
    uploadFolder: uploadExtension.uploadFolder.bind(uploadExtension),
    checkOutputFile: uploadExtension.checkOutputFile.bind(uploadExtension),
    copyOutputFile: uploadExtension.copyOutputFile.bind(uploadExtension),
    saveImageFile: uploadExtension.saveImageFile.bind(uploadExtension),
    
    // 工作流相关功能
    executeWorkflow: workflowExtension.executeWorkflow.bind(workflowExtension),
    waitForResult: workflowExtension.waitForResult.bind(workflowExtension),
    testWorkflow: workflowExtension.testWorkflow.bind(workflowExtension),
    
    /**
     * 获取工作流并执行
     * @param {string} workflowType 工作流类型
     * @param {string} pageType 页面类型
     * @param {string} imageType 图片类型
     * @param {object} params 工作流参数
     * @returns {Promise<object>} 执行结果
     */
    executeWorkflowByType: async function(workflowType, pageType, imageType, params = {}) {
      // 获取工作流配置
      const { getWorkflow } = require('../workflows');
      const workflow = getWorkflow(workflowType, pageType, imageType);
      
      // 执行工作流并返回结果
      return await workflowExtension.executeWorkflow(workflow, params);
    }
  });
}

// 直接导出创建客户端的方法
module.exports = createComfyUIClient;

// 同时提供单独的组件以便需要时单独使用
module.exports.Client = Client;
module.exports.Upload = extendWithUpload;
module.exports.Workflow = extendWithWorkflow; 