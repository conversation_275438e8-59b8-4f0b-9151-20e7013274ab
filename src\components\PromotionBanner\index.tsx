import React from 'react';
import './styles.css';

interface PromotionBannerProps {
  isLoggedIn: boolean;
  isSubscribed?: boolean;
  title?: string;
  subtitle?: string;
  buttonText?: string;
  onButtonClick?: () => void;
  onLoginClick?: () => void;
  onSubscribeClick?: () => void;
  backgroundImage?: string;
  showMask?: boolean;
  showTitle?: boolean;
  showSubtitle?: boolean;
}

const PromotionBanner: React.FC<PromotionBannerProps> = ({
  isLoggedIn,
  isSubscribed = false,
  title = "使用AIBIKINI，开启 AI 泳装新时代",
  subtitle = "",
  buttonText = "立即开始",
  onButtonClick,
  onLoginClick,
  onSubscribeClick,
  backgroundImage,
  showMask = true,
  showTitle = true,
  showSubtitle = true
}) => {
  const handleButtonClick = () => {
    if (isLoggedIn) {
      onSubscribeClick?.();
    } else {
      onLoginClick?.();
    }
  };

  const bannerStyle = {
    ...(backgroundImage && {
      backgroundImage: `url(${backgroundImage})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    })
  };

  return (
    <div 
      className={`promotion-banner ${isLoggedIn ? 'logged-in' : 'logged-out'} ${!showMask ? 'no-mask' : ''}`}
      style={bannerStyle}
    >
      <div className={`promotion-content ${isLoggedIn ? 'logged-in-content' : 'logged-out-content'}`}>
        {showTitle && <h1>{title}</h1>}
        {showSubtitle && subtitle && <p className="promotion-subtitle">{subtitle}</p>}
        {!isSubscribed && (
          <button className="cta-button" onClick={handleButtonClick}>
            <span>{buttonText}</span>
          </button>
        )}
      </div>
      {!backgroundImage && <div className="dynamic-background"></div>}
    </div>
  );
};

export default PromotionBanner; 