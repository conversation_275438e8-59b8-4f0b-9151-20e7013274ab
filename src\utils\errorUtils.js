/**
 * 将错误对象映射到错误类型
 * @param {Error} error - 错误对象
 * @returns {string} 错误类型 (network, validation, server, auth, timeout, unknown)
 */
export const mapErrorType = (error) => {
  if (!error) return 'unknown';

  // 检查错误代码
  if (error.code) {
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return 'timeout';
    }
    if (['ERR_NETWORK', 'ERR_CONNECTION_REFUSED', 'ERR_NAME_NOT_RESOLVED'].includes(error.code)) {
      return 'network';
    }
  }

  // 检查HTTP状态码
  if (error.status || error.response?.status) {
    const status = error.status || error.response?.status;
    
    if (status === 401 || status === 403) {
      return 'auth';
    }
    if (status === 400 || status === 422) {
      return 'validation';
    }
    if (status >= 500) {
      return 'server';
    }
  }

  // 检查错误消息
  if (error.message) {
    if (error.message.includes('网络') || error.message.includes('连接') || 
        error.message.includes('Network') || error.message.includes('connection')) {
      return 'network';
    }
    if (error.message.includes('超时') || error.message.includes('timeout')) {
      return 'timeout';
    }
    if (error.message.includes('验证') || error.message.includes('格式') || 
        error.message.includes('valid') || error.message.includes('format')) {
      return 'validation';
    }
    if (error.message.includes('服务器') || error.message.includes('server')) {
      return 'server';
    }
    if (error.message.includes('登录') || error.message.includes('认证') || 
        error.message.includes('auth') || error.message.includes('login')) {
      return 'auth';
    }
  }

  // 默认为未知错误
  return 'unknown';
};

/**
 * 从错误对象中提取错误消息
 * @param {Error} error - 错误对象
 * @returns {string} 错误消息
 */
export const getErrorMessage = (error) => {
  if (!error) return '发生未知错误';
  
  if (typeof error === 'string') return error;
  
  return error.message || 
         error.response?.data?.message || 
         error.response?.statusText || 
         '发生未知错误';
};

/**
 * 创建用于错误组件的错误信息对象
 * @param {Error} error - 错误对象
 * @returns {Object} 错误信息对象 { message, type }
 */
export const createErrorInfo = (error) => {
  return {
    message: getErrorMessage(error),
    type: mapErrorType(error)
  };
}; 