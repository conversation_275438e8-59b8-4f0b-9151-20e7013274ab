import React, { useState, useRef, useCallback, useEffect } from 'react';
import { MdClose } from 'react-icons/md';
import { IconType } from 'react-icons';
import { LoginForm, RegisterForm, ResetPasswordForm } from './index';
import { useAuth } from '../../contexts/AuthContext';
import Captcha from '../Captcha/index';
// 导入样式
import './styles/index.css';
import '../../styles/close-buttons.css';

// 通知类型
interface Notification {
  type: string;
  message: string;
}

interface AuthModalsProps {
  // 模态窗口显示状态
  showLoginModal: boolean;
  showResetPassword: boolean;
  // 模态窗口状态控制函数
  setShowLoginModal: (show: boolean) => void;
  setShowResetPassword: (show: boolean) => void;
  // 通知回调函数
  onNotification?: (notification: Notification) => void;
  // 自定义类名
  className?: string;
  // 图标渲染函数
  renderIcon?: (Icon: IconType) => React.ReactNode;
}

/**
 * 认证模态窗口组件
 * 包含登录、注册和修改密码三个模态窗口
 */
const AuthModals: React.FC<AuthModalsProps> = ({
  showLoginModal,
  showResetPassword,
  setShowLoginModal,
  setShowResetPassword,
  onNotification,
  className = '',
  renderIcon = (Icon: IconType) => {
    const IconComponent = Icon as React.ComponentType<React.SVGProps<SVGSVGElement>>;
    return <IconComponent />;
  }
}) => {
  const auth = useAuth();
  
  // 登录/注册标签状态
  const [isLogin, setIsLogin] = useState<boolean>(true);
  
  // 验证码模态窗状态
  const [showCaptchaModal, setShowCaptchaModal] = useState<boolean>(false);
  const [pendingPhone, setPendingPhone] = useState<string>('');
  const [captchaType, setCaptchaType] = useState<'register' | 'reset'>('register');
  
  // 拖拽相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [modalPosition, setModalPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const modalRef = useRef<HTMLDivElement>(null);

  // 修改居中弹窗逻辑
  const getCenteredPosition = useCallback(() => {
    if (!modalRef.current) return { x: 0, y: 0 };
    const modal = modalRef.current;
    const width = modal.offsetWidth;
    const height = modal.offsetHeight;
    
    // 计算垂直居中位置，考虑滚动位置
    const x = Math.max((window.innerWidth - width) / 2, 0);
    const y = Math.max((window.innerHeight - height) / 2 + window.scrollY, 0);
    
    return { x, y };
  }, []);

  // 修改弹窗打开时的位置设置
  useEffect(() => {
    if (showLoginModal || showResetPassword) {
      // 使用 requestAnimationFrame 确保在下一帧更新位置
      requestAnimationFrame(() => {
        setModalPosition(getCenteredPosition());
      });
    }
  }, [showLoginModal, showResetPassword, getCenteredPosition]);

  // 添加滚动监听以保持垂直居中
  useEffect(() => {
    const handleScroll = () => {
      if (showLoginModal || showResetPassword) {
        requestAnimationFrame(() => {
          const { x } = modalPosition;
          const modal = modalRef.current;
          if (modal) {
            const height = modal.offsetHeight;
            const y = Math.max((window.innerHeight - height) / 2 + window.scrollY, 0);
            setModalPosition({ x, y });
          }
        });
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [showLoginModal, showResetPassword, modalPosition]);

  // 拖拽事件
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button !== 0) return;
    if (e.target instanceof HTMLElement && e.target.closest('.modal-header')) {
      setIsDragging(true);
      const modal = modalRef.current;
      if (modal) {
        const rect = modal.getBoundingClientRect();
        setDragOffset({ x: e.clientX - rect.left, y: e.clientY - rect.top });
        document.body.classList.add('no-select');
        // 拖动开始时加 dragging 类
        modal.classList.add('dragging');
      }
      e.preventDefault();
    }
  };

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging) {
      const modal = modalRef.current;
      if (!modal) return;
      const width = modal.offsetWidth;
      const height = modal.offsetHeight;
      const maxX = window.innerWidth - width;
      const maxY = window.innerHeight - height;
      const newX = Math.max(0, Math.min(e.clientX - dragOffset.x, maxX));
      const newY = Math.max(0, Math.min(e.clientY - dragOffset.y, maxY));
      setModalPosition({ x: newX, y: newY });
    }
  }, [isDragging, dragOffset]);

  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      document.body.classList.remove('no-select');
      // 拖动结束时移除 dragging 类
      const modal = modalRef.current;
      if (modal) {
        modal.classList.remove('dragging');
      }
    }
  }, [isDragging]);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);
  
  // 处理标签切换
  const handleTabChange = (isLoginTab: boolean) => {
    setIsLogin(isLoginTab);
  };
  
  // 关闭模态窗口
  const handleCloseModal = (type: 'login' | 'reset') => {
    if (type === 'login') {
      setShowLoginModal(false);
    } else if (type === 'reset') {
      setShowResetPassword(false);
    }
  };
  
  // 处理忘记密码
  const handleForgotPassword = () => {
    setShowLoginModal(false);
    setShowResetPassword(true);
  };
  
  // 处理通知
  const handleNotification = (notification: Notification) => {
    if (onNotification) {
      onNotification(notification);
    }
  };
  
  // 处理登录成功
  const handleLoginSuccess = (notification: Notification) => {
    handleNotification(notification);
    setShowLoginModal(false);
  };
  
  // 处理注册成功
  const handleRegisterSuccess = (notification: Notification) => {
    handleNotification(notification);
    setIsLogin(true); // 切换到登录标签
  };
  
  // 处理修改密码成功
  const handleResetSuccess = () => {
    setShowResetPassword(false);
    handleNotification({
      type: 'success',
      message: '密码修改成功，请使用新密码登录'
    });
    setTimeout(() => {
      auth.logout();
    }, 1000);
  };
  
  // 处理验证码请求
  const handleCaptchaRequest = (phone: string, type: 'register' | 'reset') => {
    // 保存手机号和验证类型
    setPendingPhone(phone);
    setCaptchaType(type);
    
    // 打开验证码模态框
    setShowCaptchaModal(true);
  };
  
  // 验证成功处理
  const handleVerifySuccess = () => {
    setShowCaptchaModal(false);
    
    // 发送验证码
    auth.sendVerificationCode(pendingPhone).then((result: any) => {
      if (result.success) {
        // 显示成功通知
        handleNotification({
          type: 'success',
          message: '验证码已发送到您的手机'
        });
      }
    });
  };
  
  return (
    <>
      {/* 登录/注册模态框 */}
      {showLoginModal && (
        <div 
          className="modal-overlay" 
          onClick={() => setShowLoginModal(false)}
          style={{ 
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1000
          }}
        >
          <div
            className={`modal-content login-modal ${className}`}
            ref={modalRef}
            style={{
              position: 'absolute',
              left: modalPosition.x,
              top: modalPosition.y,
              transform: 'translate(0, 0)',
              cursor: isDragging ? 'grabbing' : 'default',
              zIndex: 1100
            }}
            onMouseDown={handleMouseDown}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="modal-header" style={{ cursor: 'grab' }}>
              <div className="tab-group">
                <button 
                  className={`tab-btn ${isLogin ? 'active' : ''}`}
                  onClick={() => handleTabChange(true)}
                >
                  登录
                </button>
                <button 
                  className={`tab-btn ${!isLogin ? 'active' : ''}`}
                  onClick={() => handleTabChange(false)}
                >
                  注册
                </button>
              </div>
              <button 
                className="medium-close-button"
                onClick={() => handleCloseModal('login')}
              >
                {renderIcon(MdClose)}
              </button>
            </div>
            <div className="modal-body">
              {isLogin ? (
                <LoginForm 
                  onClose={() => setShowLoginModal(false)}
                  onForgotPassword={handleForgotPassword}
                  onLoginSuccess={handleLoginSuccess}
                />
              ) : (
                <RegisterForm
                  onClose={() => setShowLoginModal(false)}
                  onRegisterSuccess={handleRegisterSuccess}
                  onCaptchaRequest={(phone) => handleCaptchaRequest(phone, 'register')}
                />
              )}
            </div>
          </div>
        </div>
      )}

      {/* 修改密码模态框 */}
      {showResetPassword && (
        <div className="modal-overlay" onClick={() => handleCloseModal('reset')}>
          <div
            className={`modal-content reset-modal ${className}`}
            ref={modalRef}
            style={{
              position: 'fixed',
              left: modalPosition.x,
              top: modalPosition.y,
              cursor: isDragging ? 'grabbing' : 'default',
              zIndex: 1100
            }}
            onMouseDown={handleMouseDown}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="modal-header" style={{ cursor: 'grab' }}>
              <div className="tab-group">
                <button className="tab-btn active">
                  修改密码
                </button>
              </div>
              <button 
                className="medium-close-button"
                onClick={() => handleCloseModal('reset')}
              >
                {renderIcon(MdClose)}
              </button>
            </div>
            <div className="modal-body">
              <ResetPasswordForm 
                onClose={() => setShowResetPassword(false)}
                onResetSuccess={handleResetSuccess}
                onCaptchaRequest={(phone) => handleCaptchaRequest(phone, 'reset')}
              />
            </div>
          </div>
        </div>
      )}
      
      {/* 验证码模态框 */}
      {showCaptchaModal && (
        <div className="modal-overlay" onClick={() => setShowCaptchaModal(false)}>
          <div
            className="modal-content captcha-modal"
            ref={modalRef}
            style={{
              position: 'fixed',
              left: modalPosition.x,
              top: modalPosition.y,
              cursor: isDragging ? 'grabbing' : 'default',
              zIndex: 1100
            }}
            onMouseDown={handleMouseDown}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="modal-header" style={{ cursor: 'grab' }}>
              <div className="tab-group">
                <button className="tab-btn active">
                  手机验证
                </button>
              </div>
              <button 
                className="medium-close-button"
                onClick={() => {
                  setShowCaptchaModal(false);
                  handleCloseModal('login');
                }}
              >
                {renderIcon(MdClose)}
              </button>
            </div>
            <div className="modal-body">
              <Captcha 
                onVerify={(success: boolean) => {
                  if (success) {
                    handleVerifySuccess();
                  }
                }} 
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AuthModals;
