/**
 * 统一面板组件样式定义
 * 
 * 包含以下组件：
 * 1. 面板容器 (.panel-component) - 基础面板布局
 * 2. 面板头部 (.component-header) - 包含图标和文本信息
 * 3. 信息区域 (.component-info) - 展示主要内容
 * 4. 展开按钮 (.expand-btn) - 控制面板展开/收起
 * 5. 预览图片 (.selected-model-preview, .selected-scene-preview) - 图片预览
 * 6. 服装组件 (.component-images, .result-image) - 服装相关的特殊样式
 * 
 * 特点：
 * - 统一的卡片式设计
 * - 优雅的悬停效果
 * - 支持展开/收起状态
 * - 灵活的图片预览
 * - 清晰的层级结构
 * - 响应式布局
 * 
 * 使用示例：
 * <div class="panel-component">
 *   <div class="component-header">
 *     <div class="component-info">
 *       <div class="component-images">
 *         <div class="result-image">
 *           <img src="image.jpg" alt="预览图" />
 *         </div>
 *       </div>
 *       <div class="component-text">
 *         <h3>标题</h3>
 *         <div class="component-content">
 *           <p>描述文本</p>
 *         </div>
 *       </div>
 *     </div>
 *     <button class="expand-btn">
 *       <span></span>
 *     </button>
 *   </div>
 * </div>
 */

@import './theme.css';

/* 面板组件基础样式 */
.panel-component {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  height: 88px;
  display: flex;
  position: relative;
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-xxs);
  transition: var(--transition-normal);
}

.panel-component:hover {
  box-shadow: var(--shadow-md);
  transform: none;
}

/* 面板头部 */
.component-header {
  display: flex;
  align-items: stretch;
  width: 100%;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 0;
}

/* 面板信息区 */
.component-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.component-icon {
  width: 88px;
  height: 88px;
  object-fit: cover;
  border-right: 1px solid var(--border-light);
  background: var(--bg-tertiary);
}

/* 文本内容区 */
.component-text {
  flex: 1;
  min-width: 0;
}

.component-text h3 {
  margin: 12px 10px;
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

.component-content {
  margin-top: var(--spacing-xxs);
}

.component-content p {
  margin: 10px;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 展开按钮样式 */
.expand-btn {
  width: 48px;
  height: 88px;
  border: none;
  background: none;
  cursor: pointer;
  position: relative;
  padding: 0;
  border-left: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
}

.expand-btn:hover {
  background-color: var(--bg-tertiary);
}

.expand-btn span {
  display: block;
  width: 10px;
  height: 10px;
  border-right: 2px solid var(--text-secondary);
  border-bottom: 2px solid var(--text-secondary);
  transform: rotate(-45deg);
  transition: var(--transition-normal);
}

.expand-btn:hover span {
  border-color: var(--brand-primary);
  transform: rotate(-45deg);
}

/* 完成状态的展开按钮样式 */
.panel-component:has(.selected-model-preview) .expand-btn,
.panel-component:has(.selected-scene-preview) .expand-btn,
.panel-component:has(.component-icon[src*="custom-active"]) .expand-btn,
.panel-component:has(.component-icon[src*="model-custom"]) .expand-btn,
.panel-component:has(.component-icon[src*="scene-custom"]) .expand-btn {
  background: transparent;
}

.panel-component:has(.selected-model-preview) .expand-btn span,
.panel-component:has(.selected-scene-preview) .expand-btn span,
.panel-component:has(.component-icon[src*="custom-active"]) .expand-btn span,
.panel-component:has(.component-icon[src*="model-custom"]) .expand-btn span,
.panel-component:has(.component-icon[src*="scene-custom"]) .expand-btn span {
  border-color: var(--brand-primary);
}

.panel-component:has(.selected-model-preview) .expand-btn:hover,
.panel-component:has(.selected-scene-preview) .expand-btn:hover,
.panel-component:has(.component-icon[src*="custom-active"]) .expand-btn:hover,
.panel-component:has(.component-icon[src*="model-custom"]) .expand-btn:hover,
.panel-component:has(.component-icon[src*="scene-custom"]) .expand-btn:hover {
  background: transparent;
}

/* 预览图片样式 */
.selected-model-preview,
.selected-scene-preview,
.component-images {
  width: 88px;
  height: 88px;
  border-radius: 0;
  overflow: hidden;
  flex-shrink: 0;
  background: var(--bg-secondary);
}

.selected-model-thumbnail,
.selected-scene-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top center;
}

/* 服装组件特有的图片区域样式 */
.component-images {
  display: flex;
  gap: 1px;
  background: var(--border-light);
  height: 88px;
  width: 88px;
  flex-shrink: 0;
  border-radius: 0;
  border-right: 1px solid var(--border-light);
}

.result-image {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 0;
  background-color: transparent;
}

.result-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top center;
  transition: var(--transition-normal);
} 