{"1": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "2": {"inputs": {"text": "", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "3": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "4": {"inputs": {"noise_seed": 428559452660555, "needInput": true}, "class_type": "RandomNoise", "_meta": {"title": "随机种子"}}, "5": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "6": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "7": {"inputs": {"model": ["10", 0], "conditioning": ["8", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "8": {"inputs": {"conditioning_to_strength": ["28", 1], "conditioning_to": ["26", 0], "conditioning_from": ["17", 0]}, "class_type": "ConditioningAverage", "_meta": {"title": "ConditioningAverage"}}, "9": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "10": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["22", 0], "height": ["23", 0], "model": ["30", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "11": {"inputs": {"text": ["14", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "12": {"inputs": {"String": "realistic", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "String", "_meta": {"title": "String"}}, "13": {"inputs": {"noise": ["4", 0], "guider": ["7", 0], "sampler": ["3", 0], "sigmas": ["16", 0], "latent_image": ["25", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "14": {"inputs": {"delimiter": ", ", "clean_whitespace": "true", "text_a": ["21", 0], "text_b": ["12", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "15": {"inputs": {"samples": ["13", 0], "vae": ["1", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "16": {"inputs": {"scheduler": "normal", "steps": 30, "denoise": 1, "model": ["10", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "17": {"inputs": {"guidance": 4.2, "conditioning": ["11", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "18": {"inputs": {"filename_prefix": "Fashion", "images": ["15", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "19": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "21": {"inputs": {"needInput": true, "from_translate": "auto", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "GoogleTranslator", "text": "一个穿着绿色连体泳衣的女人，站在深海里的一个巨大贝壳上面。贝壳附近散落着几颗珍珠。这个女人的头发随着海水的流动而凌乱，她侧身站立，单手托举一颗明亮的珍珠，并注视着这颗珍珠。阳光从右上方射入海底，整个海底明亮，有许多的珊瑚和颜色艳丽的小鱼。", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "描述词输入"}}, "22": {"inputs": {"needInput": true, "value": 1024}, "class_type": "PrimitiveInt", "_meta": {"title": "图片尺寸-宽"}}, "23": {"inputs": {"needInput": true, "value": 1536}, "class_type": "PrimitiveInt", "_meta": {"title": "图片尺寸-高"}}, "24": {"inputs": {"crop": "none", "clip_vision": ["6", 0], "image": ["31", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "25": {"inputs": {"needInput": true, "width": ["22", 0], "height": ["23", 0], "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "图片数量"}}, "26": {"inputs": {"needInput": true, "strength": 0.3500000000000001, "strength_type": "multiply", "conditioning": ["17", 0], "style_model": ["5", 0], "clip_vision_output": ["24", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "参考图强度调节"}}, "27": {"inputs": {"needInput": true, "Number": "0.35"}, "class_type": "Float", "_meta": {"title": "描述词强度调节"}}, "28": {"inputs": {"expression": "1-a", "speak_and_recognation": {"__value__": [false, true]}, "a": ["27", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "29": {"inputs": {"conditioning": ["2", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "30": {"inputs": {"lora_name": "Flux/细节加强aidmaImageUprader-FLUX-v0.3.safetensors", "strength_model": 0.3500000000000001, "model": ["19", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "31": {"inputs": {"url": "https://", "needInput": true, "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "参考图片上传"}}}