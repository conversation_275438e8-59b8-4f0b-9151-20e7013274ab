.tip-popup {
  position: fixed;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  width: 280px;
  z-index: 100000000;
  opacity: 0;
  transform: translateY(10px);
  animation: tipPopupFadeIn var(--transition-normal);
  pointer-events: none;
  border: 1px solid var(--border-light);
  user-select: none; /* 防止文本选择 */
  overflow: hidden; /* 确保圆角效果 */
}

/* 移动端居中显示样式 */
@media (max-width: 768px) {
  .tip-popup {
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 90vw !important;
    max-width: 320px !important;
    min-width: unset !important;
    min-height: unset !important;
    box-sizing: border-box !important;
  }
}

.tip-popup.show {
  pointer-events: auto;
  opacity: 1;
  transform: translateY(0);
}

/* 拖动状态样式 */
.tip-popup.dragging {
  transition: none; /* 拖动时禁用过渡动画 */
  box-shadow: var(--shadow-lg); /* 拖动时增加阴影 */
}

.tip-popup.dragging .tip-popup-header {
  cursor: grabbing !important; /* 拖动时强制显示抓取光标 */
}

@keyframes tipPopupFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标题区域样式 */
.tip-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 8px 8px var(--spacing-md); /* 改为8px，使关闭按钮的上下左右边距都一致 */
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
  cursor: grab;
  position: relative; /* 为绝对定位的关闭按钮提供参考 */
}

.tip-popup-header:active {
  cursor: grabbing;
}

.tip-popup-title {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

/* 覆盖关闭按钮的绝对定位，使其在标题区域内正确定位 */
.tip-popup .tiny-close-button {
  position: relative;
  top: auto;
  right: auto;
  margin-left: auto; /* 推到右侧 */
  margin-right: 0; /* 移除右侧边距，与上方边距保持一致 */
}

.tip-popup-content {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.6;
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-md) var(--spacing-md);
  background: var(--bg-primary);
}

.tip-popup-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  padding-left: 0;
}

/* 添加列表样式 */
.tip-popup-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tip-popup-content li {
  position: relative;
  padding-left: 16px;
  margin-bottom: 6px;
  text-indent: 0;
}

.tip-popup-content li:last-child {
  margin-bottom: 0;
}

.tip-popup-content li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--text-secondary);
}

/* 防止文本选择的全局样式 */
.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* =============== 提示项链接按钮样式 =============== */
.tip-item-with-link {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.tip-item-with-link span {
  flex: 1;
}

.tip-link-btn {
  width: 22px !important;
  height: 22px !important;
  min-width: 22px !important;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--brand-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  padding: 0;
  margin: 0;
  flex-shrink: 0;
}

.tip-link-btn svg {
  width: 14px !important;
  height: 14px !important;
}

.tip-link-btn:hover {
  color: var(--text-secondary);
}

.tip-link-btn:active {
  transform: scale(0.95);
} 