const express = require('express');
const router = express.Router();
const subscriptionController = require('./subscription.controller');
const { auth, requireRole } = require('../../../middleware/auth.middleware');

// 公共路由
router.post('/check-access', subscriptionController.checkFeatureAccess);

// 需要用户登录的路由
router.get('/user/:userId?', auth, subscriptionController.getUserSubscription);

// 需要管理员权限的路由
router.get('/', auth, requireRole('admin'), subscriptionController.getAllSubscriptions);
router.get('/stats', auth, requireRole('admin'), subscriptionController.getSubscriptionStats);
router.post('/batch-update-expired', auth, requireRole('admin'), subscriptionController.batchUpdateExpiredSubscriptions);
router.get('/:id', auth, requireRole('admin'), subscriptionController.getSubscriptionById);
router.post('/', auth, requireRole('admin'), subscriptionController.createSubscription);
router.put('/:id', auth, requireRole('admin'), subscriptionController.updateSubscription);
router.post('/:id/cancel', auth, requireRole('admin'), subscriptionController.cancelSubscription);
router.delete('/:id', auth, requireRole('admin'), subscriptionController.deleteSubscription);

module.exports = router; 