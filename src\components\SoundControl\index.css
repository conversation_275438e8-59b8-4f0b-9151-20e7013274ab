.sound-control {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.sound-toggle-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.sound-toggle-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.sound-toggle-btn.muted {
  opacity: 0.7;
}

.sound-icon {
  font-size: 18px;
}

/* 侧边栏声音控制按钮样式 */
.sound-toggle-sidebar {
  margin-top: 0.25rem !important;
  margin-bottom: 0 !important;
}

.sound-toggle-sidebar svg {
  font-size: 1.15rem !important;
  flex-shrink: 0;
  width: 1.15rem !important;
  margin-right: 0.25rem;
  color: var(--text-secondary);
  transition: none;
}

.sound-toggle-sidebar:hover svg {
  color: var(--brand-primary);
}

/* 针对折叠侧边栏的样式适配 */
.sidebar.collapsed .sound-toggle-sidebar span:not(svg) {
  display: none;
}

.sidebar.collapsed .sound-toggle-sidebar {
  justify-content: center;
  padding: 0.85rem 0;
} 