import { createTheme } from '@mui/material/styles';
import { 
  primaryEndColor, 
  primaryStartColor,
  buttonTextLight,
  buttonTextDark,
  textPrimary,
  textSecondary,
  bgWhite,
  bgGray
} from './colors';

// 创建符合项目品牌色的Material-UI主题
const theme = createTheme({
  palette: {
    primary: {
      main: primaryEndColor, // #FF3C6A 玫红色（使用项目品牌色）
      light: '#ff6b8f',
      dark: '#c4003f',
      contrastText: buttonTextLight,
    },
    secondary: {
      main: primaryStartColor, // #FF8C42 橙色
      light: '#ffc06f',
      dark: '#c75f00',
      contrastText: buttonTextLight,
    },
    text: {
      primary: textPrimary,
      secondary: textSecondary,
    },
    background: {
      default: bgGray,
      paper: bgWhite,
    },
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
        },
      },
    },
  },
});

export default theme; 