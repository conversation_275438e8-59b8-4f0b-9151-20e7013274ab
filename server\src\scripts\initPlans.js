const mongoose = require('mongoose');
const Plan = require('../modules/admin/subscribe/plan.model');
require('dotenv').config();

// 连接数据库
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected'))
.catch(err => console.error('MongoDB connection error:', err));

// 默认计划数据
const defaultPlans = [
  {
    code: 'free',
    name: '免费版',
    description: '基础功能，适合初次体验',
    price: {
      monthly: 0,
      yearly: 0,
      discount: 0
    },
    features: {
      design: {
        enabled: false,
        trendDesign: false,
        styleOptimization: false,
        inspirationExplore: false
      },
      model: {
        enabled: false,
        fashionShoot: false,
        modelChange: false,
        colorChange: false,
        backgroundChange: false,
        virtualModel: false
      },
      tools: {
        enabled: true,
        upscale: true,
        matting: true,
        extend: true
      },
      support: {
        level: 'standard',
        responseTime: '5x8'
      }
    },
    usageQuota: {
      totalRequests: 10,
      dailyRequests: 5
    },
    isPublic: true,
    sortOrder: 0,
    isRecommended: false
  },
  {
    code: 'design',
    name: '款式设计版',
    description: '专为服装设计师打造的AI助手',
    price: {
      monthly: 199,
      yearly: 1990,
      discount: 17
    },
    features: {
      design: {
        enabled: true,
        trendDesign: true,
        styleOptimization: true,
        inspirationExplore: true
      },
      model: {
        enabled: false,
        fashionShoot: false,
        modelChange: false,
        colorChange: false,
        backgroundChange: false,
        virtualModel: false
      },
      tools: {
        enabled: true,
        upscale: true,
        matting: true,
        extend: true
      },
      support: {
        level: 'standard',
        responseTime: '5x8'
      }
    },
    usageQuota: {
      totalRequests: -1,
      dailyRequests: -1
    },
    isPublic: true,
    sortOrder: 1,
    isRecommended: false
  },
  {
    code: 'model',
    name: '模特图版',
    description: '专为电商运营打造的AI助手',
    price: {
      monthly: 299,
      yearly: 2990,
      discount: 17
    },
    features: {
      design: {
        enabled: false,
        trendDesign: false,
        styleOptimization: false,
        inspirationExplore: false
      },
      model: {
        enabled: true,
        fashionShoot: true,
        modelChange: true,
        colorChange: true,
        backgroundChange: true,
        virtualModel: true
      },
      tools: {
        enabled: true,
        upscale: true,
        matting: true,
        extend: true
      },
      support: {
        level: 'standard',
        responseTime: '5x8'
      }
    },
    usageQuota: {
      totalRequests: -1,
      dailyRequests: -1
    },
    isPublic: true,
    sortOrder: 2,
    isRecommended: false
  },
  {
    code: 'full',
    name: '全能版',
    description: '全功能解决方案，满足所有需求',
    price: {
      monthly: 399,
      yearly: 3990,
      discount: 17
    },
    features: {
      design: {
        enabled: true,
        trendDesign: true,
        styleOptimization: true,
        inspirationExplore: true
      },
      model: {
        enabled: true,
        fashionShoot: true,
        modelChange: true,
        colorChange: true,
        backgroundChange: true,
        virtualModel: true
      },
      tools: {
        enabled: true,
        upscale: true,
        matting: true,
        extend: true
      },
      support: {
        level: 'premium',
        responseTime: '7x24'
      }
    },
    usageQuota: {
      totalRequests: -1,
      dailyRequests: -1
    },
    isPublic: true,
    sortOrder: 3,
    isRecommended: true
  }
];

// 初始化计划
const initPlans = async () => {
  try {
    // 清空现有计划
    await Plan.deleteMany({});
    console.log('Existing plans deleted');
    
    // 插入默认计划
    await Plan.insertMany(defaultPlans);
    console.log('Default plans inserted');
    
    // 关闭数据库连接
    mongoose.connection.close();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error initializing plans:', error);
    mongoose.connection.close();
  }
};

// 执行初始化
initPlans(); 