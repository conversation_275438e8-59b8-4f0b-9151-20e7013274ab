import React, { useState, useEffect } from 'react';
import './Subscribe.css';
import ContactSupport from '../../components/ContactSupport';
import { getUserSubscription } from '../../api/credits';
import dayjs from 'dayjs';
import { BILLING_CONFIG } from '../../config/billing';

// 价格显示控制函数 - 上线时将false改为 true 即可显示价格
const SHOW_PRICES = true;

// 免费试用显示控制 - 设置为false隐藏免费试用相关内容，设置为true显示
const SHOW_FREE_TRIAL = false;

// 订阅计划名称映射（作为后备方案）
const getPlanDisplayName = (planCode, planName) => {
  // 优先使用后端返回的计划名称
  if (planName) {
    return planName;
  }
  
  // 后备方案：使用硬编码映射
  const planNameMap = {
    'free': '免费版',
    'design': '设计版',
    'model': '模特版',
    'full': '完整版',
    'enterprise': '企业版',
    'basic': '轻量版',
    'premium': '标准版'
  };
  return planNameMap[planCode] || '免费版';
};

const Subscribe = () => {
  const [showContactModal, setShowContactModal] = useState(false);
  const [userSubscription, setUserSubscription] = useState(null);
  const [loading, setLoading] = useState(true);
  
  // 获取用户订阅信息
  const fetchUserSubscription = async () => {
    try {
      const response = await getUserSubscription();
      console.log('获取到的用户订阅信息:', response);
      setUserSubscription(response);
    } catch (error) {
      console.error('获取用户订阅信息失败:', error);
      // 不显示错误消息，因为免费用户可能没有订阅
    } finally {
      setLoading(false);
    }
  };

  // 初始化时获取用户订阅信息
  useEffect(() => {
    // 新增：未登录时不请求API，直接结束加载
    if (!localStorage.getItem('token')) {
      setLoading(false);
      return;
    }
    fetchUserSubscription();
  }, []);

  // 隐藏价格和算力的函数
  const hideSensitiveInfo = (text) => {
    if (!SHOW_PRICES) {
      // 隐藏价格相关
      if (text.includes('￥')) {
        return text.replace(/￥[\d,\.]+/g, '￥***');
      }
      // 隐藏算力值
      if (text.includes('C') && /\d/.test(text)) {
        return text.replace(/[\d,]+ C/g, '*** C');
      }
      // 隐藏百分比
      if (text.includes('%')) {
        return text.replace(/[\d]+%/g, '***%');
      }
      // 隐藏具体数字
      if (text.includes('台') && /\d/.test(text)) {
        return text.replace(/\d+台/g, '***台');
      }
    }
    return text;
  };
  
  const subscriptionPlans = [
    {
      title: '免费试用',
      description: '新用户尝鲜体验',
      monthlyPrice: '免费',
      price: '免费',
      features: [
        '赠送2,000 C 算力值',
        '- 3天有效期',
        '全部功能模块',
        '- * 款式设计',
        '- * 模特图',
        // '- * AI视频', // 临时隐藏"AI视频"相关内容，如需恢复请取消本行注释
        '- * 快捷工具',
        '<span class="highlight-price">1台</span>设备在线',
        '每台最多<span class="highlight-price">1个</span>任务同时运行',
        '基础客服支持',
      ],
      recommended: false
    },
    // 新增基础版
    {
      title: '友情版',
      description: '适合尝新体验用户',
      monthlyPrice: '￥165 /月',
      price: '￥1,980/年',
      features: [
        '兑换 <span class="highlight-price">198,000 C.</span> 算力值',
        '- * 1:100比例兑换 198,000 C ',
        '- * 永久有效',
        '全部功能模块',
        '- * 款式设计',
        '- * 模特图',
        // '- * AI视频', // 临时隐藏"AI视频"相关内容，如需恢复请取消本行注释
        '- * 快捷工具',
        '免安装 任何设备登录即用',
        '<span class="highlight-price">1台</span>设备同时在线',
        '每台最多<span class="highlight-price">1个</span>任务同时运行',
        '标准客服支持',
        '定期培训',
      ],
      recommended: false
    },
    {
      title: '轻量版',
      description: '适合普通卖家',
      monthlyPrice: '￥483 /月',
      price: '￥5,800/年',
      features: [
        '兑换 <span class="highlight-price">580,000 C.</span> 算力值',
        '- * 1:100比例兑换 580,000 C ',
        '- * 永久有效',
        '全部功能模块',
        '- * 款式设计',
        '- * 模特图',
        // '- * AI视频', // 临时隐藏"AI视频"相关内容，如需恢复请取消本行注释
        '- * 快捷工具',
        '免安装 任何设备登录即用',
        '<span class="highlight-price">1台</span>设备同时在线',
        '每台最多<span class="highlight-price">2个</span>任务同时运行',
        '辅助指导生成<span class="highlight-price">10张</span>图片',
        '标准客服支持',
        '定期培训',
      ],
      recommended: false
    },
    {
      title: '标准版',
      description: '适合专业卖家 共享订阅额度',
      monthlyPrice: '￥533 /人/月',
      price: '￥12,800/年',
      features: [
        '兑换 <span class="highlight-price">1,348,000 C.</span> 算力值',
        '- * 1:100比例兑换 1,280,000 C ',
        '- * +赠送<span class="highlight-price">680元</span>算力 68,000 C',
        '- * 永久有效',
        '全部功能模块',
        '- * 款式设计',
        '- * 模特图',
        // '- * AI视频', // 临时隐藏"AI视频"相关内容，如需恢复请取消本行注释
        '- * 快捷工具',
        '免安装 任何设备登录即用',
        '<span class="highlight-price">2台</span>设备同时在线 共同协作',
        '每台最多<span class="highlight-price">4个</span>任务同时运行',
        '辅助指导生成<span class="highlight-price">20张</span>图片',
        '标准客服支持',
        '定期培训',
      ],
      recommended: false
    },
    {
      title: '企业版',
      description: '企业级专业服务 共享订阅额度',
      monthlyPrice: '￥496 /人/月',
      price: '￥29,800/年',
      features: [
        '兑换 <span class="highlight-price">3,278,000 C.</span> 算力值',
        '- * 1:100比例兑换 2,980,000 C',
        '- * +赠送<span class="highlight-price">2980元</span>算力 298,000 C',
        '- * 永久有效',
        '全部功能模块',
        '- * 款式设计',
        '- * 模特图',
        // '- * AI视频', // 临时隐藏"AI视频"相关内容，如需恢复请取消本行注释
        '- * 快捷工具',
        '免安装 任何设备登录即用' ,
        '<span class="highlight-price">5台</span>设备同时在线 共同协作',
        '每台最多<span class="highlight-price">8个</span>任务同时运行',
        '辅助指导生成<span class="highlight-price">50张</span>图片',
        '专属客服支持',
        '高级顾问支持',
        '定期培训',
        '上门服务支持',
        '优先体验内测功能',
      ],
      recommended: false
    }
  ];

  return (
    <div className="subscribe-container">
      <div className="subscribe-header">
        <h1>会员订阅计划</h1>
        {!loading && (
          <>
            {userSubscription && userSubscription.hasActiveSubscription ? (
              <p className="subscribe-plan-info">
                <span className="subscribe-plan-info-label">已订阅：</span>
                <span className="subscribe-plan-info-name">
                  {getPlanDisplayName(userSubscription.plan, userSubscription.planName)}
                </span>
                <span className={`subscribe-plan-info-dot ${userSubscription.status || 'active'}`}></span>
                <span className="subscribe-plan-info-status">
                  {userSubscription.status === 'active' && '活跃'}
                  {userSubscription.status === 'expired' && '已过期'}
                  {userSubscription.status === 'pending' && '待激活'}
                  {userSubscription.status === 'canceled' && '已取消'}
                  {userSubscription.status === 'not_started' && '未开始'}
                  {!userSubscription.status && '活跃'}
                </span>
                {userSubscription.endDate && (
                  <span className="subscribe-plan-info-expiry">
                    到期时间：{dayjs(userSubscription.endDate).format('YYYY-MM-DD')}
                  </span>
                )}
              </p>
            ) : (
              <p>选择您的适配版本，开启泳装行业新纪元！</p>
            )}
          </>
        )}
      </div>
      
      <div className="subscription-plans">
        {subscriptionPlans
          .filter(plan => SHOW_FREE_TRIAL || plan.title !== '免费试用')
          .map((plan, index) => (
          <div key={index} className={'plan-card ' + (plan.recommended ? 'recommended' : '')}>
            {/* 新增：友情版角标 */}
            {plan.title === '友情版' && (
              <div className="limited-badge">限时</div>
            )}
            {plan.recommended && <div className="recommended-badge">推荐</div>}
            <h2>{plan.title}</h2>
            {/* 年费合计作为大字部分 */}
            <div className="monthly-price">
              {plan.title === '免费试用'
                ? <span style={{visibility:'visible',display:'inline-block',minHeight:'1em',color:'var(--text-secondary)',fontWeight:'bold'}}>-</span>
                : <><span className="monthly-price-value">{hideSensitiveInfo(plan.price.replace('/年',''))}</span><span className="price-unit">/年</span></>
              }
            </div>
            {/* 平均每人每月作为小字部分 */}
            <div className="price">
              {plan.title === '免费试用'
                ? null
                : <>平均 {(() => {
                  if (plan.monthlyPrice && plan.monthlyPrice.startsWith('￥')) {
                    const match = plan.monthlyPrice.match(/^￥([\d,\.]+)(.*)$/);
                    if (match) {
                      return <><span className="highlight-price">￥{match[1]}</span>{match[2]}</>;
                    }
                  }
                  return <span className="highlight-price">{hideSensitiveInfo(plan.monthlyPrice)}</span>;
                })()}</>
              }
            </div>
            <div className="plan-description">{plan.description}</div>
            <ul className="features">
              {plan.features.map((feature, idx) => {
                const subTitles = ['款式设计', '模特图生成', '快捷工具'];
                const isSubTitle = subTitles.includes(feature);
                const featureContent = isSubTitle ? feature : feature.replace(/^-\s*/, '');

                const liProps = {
                  key: idx,
                  className: isSubTitle
                    ? 'sub-title'
                    : feature.startsWith('-')
                    ? 'sub-feature'
                    : '',
                };

                if (featureContent.includes('<span')) {
                  // 处理包含HTML标签的内容
                  const processedContent = hideSensitiveInfo(featureContent);
                  return <li {...liProps} dangerouslySetInnerHTML={{ __html: processedContent }} />;
                }

                return <li {...liProps}>{hideSensitiveInfo(featureContent)}</li>;
              })}
            </ul>
            <button
              className={`subscribe-button${plan.title === '免费试用' ? ' special-trial-btn' : ''}`}
              onClick={() => {
                setShowContactModal(true);
              }}
            >
              {plan.title === '免费试用' ? '限时开放申请' : '立即订阅'}
            </button>
            {plan.title !== '免费试用' && (
              <div style={{
                fontSize: '12px',
                color: 'var(--text-secondary)',
                textAlign: 'center',
                marginTop: '8px',
                lineHeight: '1.4'
              }}>
                当前仅支持整年支付
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 加油包横幅 */}
      <div className="banner-section">
        <div className="banner-card energy-pack-banner">
          <div className="banner-content">
            <div className="banner-left">
              <div className="banner-info">
                <h3><span className="banner-icon-text">⚡</span>算力加油包</h3>
                <p>按照1:100比例兑换，随时充值算力值，按需购买更灵活</p>
              </div>
            </div>
            <div className="banner-right">
              <div className="banner-pricing">
                <span className="banner-price">{hideSensitiveInfo('￥1,000/个')}</span>
                <div className="banner-computing-power">
                  <span>{hideSensitiveInfo('100,000 C 算力值')}</span>
                </div>
              </div>
              <button className="banner-button energy-pack-btn" onClick={() => setShowContactModal(true)}>
                立即购买
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 优惠活动区域 */}
      <div className="promotions-section">
        <div className="promotions-container">
          {/* 老用户续费优惠 */}
          <div className="promotion-card">
            <div className="promotion-left">
              <div className="promotion-info">
                <h3><span className="promo-icon renewal-icon">💝</span>老用户续费福利</h3>
                <p>感谢您的持续支持，再次订阅即可享受额外优惠</p>
              </div>
            </div>
            <div className="promotion-right">
              <div className="promo-benefit">
                <span className="benefit-label">额外赠送</span>
                <span className="benefit-value">{hideSensitiveInfo('5%')}</span>
              </div>
              <button className="promo-button" onClick={() => setShowContactModal(true)}>继续订阅</button>
            </div>
          </div>

          {/* 推荐新用户奖励 */}
          <div className="promotion-card">
            <div className="promotion-left">
              <div className="promotion-info">
                <h3><span className="promo-icon referral-icon">🎁</span>邀请新用户奖励</h3>
                <p>邀请好友加入，双方均可获得额外算力奖励</p>
              </div>
            </div>
            <div className="promotion-right">
              <div className="promo-benefit">
                <span className="benefit-label">双方额外赠送</span>
                <span className="benefit-value">{hideSensitiveInfo('5%')}</span>
              </div>
              <button className="promo-button" onClick={() => setShowContactModal(true)}>立即申请</button>
            </div>
          </div>
        </div>
      </div>

      <div className="plan-comparison">
        <h2>版本详细对比</h2>
        <div className="comparison-table-wrapper">
          <table className="comparison-table">
            <thead>
              <tr>
                <th>功能特性</th>
                {SHOW_FREE_TRIAL && <th>免费试用</th>}
                <th>友情版</th>
                <th>轻量版</th>
                <th className="highlight">标准版</th>
                <th>企业版</th>
              </tr>
            </thead>
            <tbody>
              <tr className="category">
                <td>款式设计</td>
                <td colSpan={SHOW_FREE_TRIAL ? "5" : "4"}></td>
              </tr>
              <tr><td className="sub-feature">爆款开发（款式融合+款式联想+线稿）</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">爆款延伸</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">款式优化</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">灵感探索（创款+创意）</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">换面料</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">生成线稿</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>

              <tr className="category">
                <td>模特图</td>
                <td colSpan={SHOW_FREE_TRIAL ? "5" : "4"}></td>
              </tr>
              <tr><td className="sub-feature">时尚大片</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">模特换装</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">换模特</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">服装复色</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">换背景</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">换姿势</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">虚拟模特</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">细节还原</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">手部修复</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>

              <tr className="category">
                <td>快捷工具</td>
                <td colSpan={SHOW_FREE_TRIAL ? "5" : "4"}></td>
              </tr>
              <tr><td className="sub-feature">自动抠图（去背景+抠衣服）</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">智能扩图</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">图片取词</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr><td className="sub-feature">高清放大</td>{SHOW_FREE_TRIAL && <td>✓</td>}<td>✓</td><td>✓</td><td>✓</td><td>✓</td></tr>
              <tr className="category">
                <td>兑换算力值</td>
                <td colSpan={SHOW_FREE_TRIAL ? "5" : "4"}></td>
              </tr>
              <tr>
                <td className="sub-feature">算力值总额</td>
                {SHOW_FREE_TRIAL && <td>{hideSensitiveInfo('2,000 C')}</td>}
                <td>{hideSensitiveInfo('198,000 C')}</td>
                <td>{hideSensitiveInfo('580,000 C')}</td>
                <td>{hideSensitiveInfo('1,348,000 C')}</td>
                <td>{hideSensitiveInfo('3,278,000 C')}</td>
              </tr>
              <tr>
                <td className="sub-feature">/ 1:100比例兑换</td>
                {SHOW_FREE_TRIAL && <td>{hideSensitiveInfo('2,000 C')}</td>}
                <td>{hideSensitiveInfo('198,000 C')}</td>
                <td>{hideSensitiveInfo('580,000 C')}</td>
                <td>{hideSensitiveInfo('1,280,000 C')}</td>
                <td>{hideSensitiveInfo('2,980,000 C')}</td>
              </tr>
              <tr>
                <td className="sub-feature">/ 额外赠送</td>
                {SHOW_FREE_TRIAL && <td>-</td>}
                <td>-</td>
                <td>-</td>
                <td>{hideSensitiveInfo('68,000 C（+5%）')}</td>
                <td>{hideSensitiveInfo('298,000 C（+10%）')}</td>
              </tr>
              <tr>
                <td className="sub-feature">有效期</td>
                {SHOW_FREE_TRIAL && <td>3天</td>}
                <td>永久有效</td>
                <td>永久有效</td>
                <td>永久有效</td>
                <td>永久有效</td>
              </tr>
              <tr className="category">
                <td>设备限制</td>
                <td colSpan={SHOW_FREE_TRIAL ? "5" : "4"}></td>
              </tr>
              <tr>
                <td className="sub-feature">设备使用限制</td>
                <td colSpan={SHOW_FREE_TRIAL ? "5" : "4"}>免安装 任何设备登录即用</td>
              </tr>
              <tr>
                <td className="sub-feature">设备同时登录数量</td>
                {SHOW_FREE_TRIAL && <td>1台</td>}
                <td>1台</td>
                <td>1台</td>
                <td>2台</td>
                <td>5台</td>
              </tr>
              <tr>
                <td className="sub-feature">每台设备最多同时生成任务数量</td>
                {SHOW_FREE_TRIAL && <td>1个任务</td>}
                <td>1个任务</td>
                <td>2个任务</td>
                <td>4个任务</td>
                <td>8个任务</td>
              </tr>
              <tr className="category">
                <td>服务支持</td>
                <td colSpan={SHOW_FREE_TRIAL ? "5" : "4"}></td>
              </tr>
              <tr>
                <td className="sub-feature">辅助指导生成图片</td>
                {SHOW_FREE_TRIAL && <td>-</td>}
                <td>-</td>
                <td>{hideSensitiveInfo('10张')}</td>
                <td>{hideSensitiveInfo('20张')}</td>
                <td>{hideSensitiveInfo('50张')}</td>
              </tr>
              <tr>
                <td className="sub-feature">客服支持</td>
                {SHOW_FREE_TRIAL && <td>基础</td>}
                <td>标准</td>
                <td>标准</td>
                <td>标准</td>
                <td>专属</td>
              </tr>
              <tr>
                <td className="sub-feature">高级技术顾问</td>
                {SHOW_FREE_TRIAL && <td>-</td>}
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>有</td>
              </tr>
              <tr>
                <td className="sub-feature">定期培训</td>
                {SHOW_FREE_TRIAL && <td>-</td>}
                <td>有</td>
                <td>有</td>
                <td>有</td>
                <td>有</td>
              </tr>
              <tr>
                <td className="sub-feature">上门服务</td>
                {SHOW_FREE_TRIAL && <td>-</td>}
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>有</td>
              </tr>
              <tr>
                <td className="sub-feature">优先体验内测功能</td>
                {SHOW_FREE_TRIAL && <td>-</td>}
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>有</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 功能价格明细区域 */}
      <div className="feature-pricing-section">
        <h2>功能价格明细</h2>
        <div className="pricing-subtitle">以下为各功能生成一张图片的单次价格</div>
        <div className="pricing-grid">
          {/* 款式设计模块 */}
          <div className="pricing-category">
            <div className="pricing-title">款式设计</div>
            <div className="pricing-table">
              <div className="pricing-row"><span className="pricing-feature">爆款开发</span><span className="pricing-value">35 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">爆款延伸</span><span className="pricing-value">38 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">款式优化</span><span className="pricing-value">30 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">灵感探索</span><span className="pricing-value">30 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">换面料</span><span className="pricing-value">35 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">生成线稿</span><span className="pricing-value">40 C</span></div>
            </div>
          </div>
          {/* 模特图模块 */}
          <div className="pricing-category">
            <div className="pricing-title">模特图</div>
            <div className="pricing-table">
              <div className="pricing-row"><span className="pricing-feature">时尚大片</span><span className="pricing-value">150 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">模特换装</span><span className="pricing-value">110 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">换模特</span><span className="pricing-value">150 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">服装复色</span><span className="pricing-value">20 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">换背景</span><span className="pricing-value">25 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">换姿势</span><span className="pricing-value">50 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">虚拟模特</span><span className="pricing-value">100 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">细节还原</span><span className="pricing-value">80 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">手部修复</span><span className="pricing-value">80 C</span></div>
            </div>
          </div>
          {/* 快捷工具模块 */}
          <div className="pricing-category">
            <div className="pricing-title">快捷工具</div>
            <div className="pricing-table">
              <div className="pricing-row"><span className="pricing-feature">图片取词</span><span className="pricing-value">25 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">高清放大</span><span className="pricing-value">10 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">自动抠图 - 去背景</span><span className="pricing-value">10 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">自动抠图 - 抠衣服</span><span className="pricing-value">20 C</span></div>
              <div className="pricing-row"><span className="pricing-feature">智能扩图</span><span className="pricing-value">100 C</span></div>
            </div>
          </div>
        </div>
      </div>

      <div className="faq-section">
        <h2>常见问题</h2>
        <div className="faq-grid">
        <div className="faq-item">
            <h3>如何选择适合我的版本？</h3>
            <p>如果您是个人用户或小型工作室，建议选择轻量版或标准版；如果您是企业用户，需要更多支持和上门服务，建议选择企业版。</p>
          </div>
          <div className="faq-item">
            <h3>什么是算力值C？</h3>
            <p>算力值C是我们的虚拟货币单位，用于计算AI生成服务的消耗。采用与人民币1:100的兑换比例，不同的版本或者活动会有额外的赠送。</p>
          </div>
          <div className="faq-item">
            <h3>会员到期后算力值会清零吗？</h3>
            <p>轻量版、标准版和企业版的算力值为永久有效，不会因会员到期而清零，续费订阅会员后可结转继续使用。{SHOW_FREE_TRIAL && '免费试用赠送的算力值有3天有效期，到期后自动失效。'}</p>
          </div>
          <div className="faq-item">
            <h3>是否支持退款？</h3>
            <p>由于会员服务及算力值充值一经开通，即实时生效并为您分配相应算力硬件资源，因此暂不支持开通后的退款。为了保障您的权益，建议您先
              {SHOW_FREE_TRIAL && (
                <a href="#" className="try-free-link" onClick={e => {
                  e.preventDefault();
                  setShowContactModal(true);
                }}>申请试用</a>
              )}
              {SHOW_FREE_TRIAL ? '，体验满意后再正式开通。' : '联系客服了解详情后再正式开通。'}
            </p>
          </div>
        </div>
      </div>

      {/* 法律条款链接 */}
      <div className="legal-section">
        <div className="subscribe-legal-links">
          <span className="legal-text">订阅即表示同意</span>
          <a href="/terms" target="_blank" rel="noopener noreferrer" className="legal-link">《用户协议》</a>
          <span className="legal-separator">和</span>
          <a href="/privacy" target="_blank" rel="noopener noreferrer" className="legal-link">《隐私政策》</a>
        </div>
      </div>

      {showContactModal && (
        <div className="modal-overlay" onClick={() => setShowContactModal(false)}>
          <div className="modal-content contact-modal" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h2>联系客服</h2>
              <button className="medium-close-button" onClick={() => setShowContactModal(false)}>×</button>
            </div>
            <div className="modal-body">
              <ContactSupport />
            </div>
          </div>
        </div>
      )}

      {/* 页面底部常驻客服组件 */}
      <div className="contact-section">
        <ContactSupport />
      </div>
    </div>
  );
};

export default Subscribe; 