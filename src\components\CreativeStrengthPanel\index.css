.creative-setting {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  height: 88px;
  display: flex;
  margin-bottom: var(--spacing-xxs);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.creative-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
}

.creative-label {
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
}

.creative-label span:first-child {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.creative-value {
  font-size: var(--font-size-md);
  color: var(--brand-primary);
  font-weight: 500;
}

.creative-slider {
  flex: 1;
  padding: 0 33px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.slider-track {
  position: relative;
  height: 2px;
  background: var(--border-light);
  border-radius: var(--radius-sm);
  margin: 8px 0;
  width: 100%;
}

.slider-fill {
  position: absolute;
  height: 100%;
  background: var(--brand-gradient);
  border-radius: var(--radius-sm);
  transition: width 0s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 推荐值标记样式 */
.recommended-mark {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  top: -18px;
  cursor: pointer;
}

.mark-arrow {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid var(--brand-primary);
  margin: 0 auto;
}

.mark-label {
  font-size: 10px;
  color: var(--brand-primary);
  white-space: nowrap;
  text-align: center;
  opacity: 0.9;
  font-weight: 500;
  margin-top: 28px;
  position: static;
  display: block;
  width: 100%;
}

.slider-input {
  position: absolute;
  top: 50%;
  left: 0%;
  width: 100%;
  height: 20px;
  transform: translateY(-50%);
  -webkit-appearance: none;
  background: transparent;
  cursor: pointer;
  margin: 0;
  padding: 0;
}

.slider-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  border: 3px solid var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  z-index: 1;
}

.slider-input::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  border: 3px solid var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: pointer;
  transition: var(--transition-normal);
}

.slider-input::-webkit-slider-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
}

.slider-input::-moz-range-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
} 