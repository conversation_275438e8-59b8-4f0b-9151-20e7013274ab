# 选色组件 (ColorPickerModal)

一个可重用的颜色选择器弹窗组件，支持自定义颜色选择和预设颜色选择。

## 功能特点

- 显示预设颜色供用户选择
- 支持使用原生颜色选择器选择自定义颜色
- 实时预览所选颜色并显示颜色值
- 响应式设计，在各种设备上表现良好
- 遵循项目现有的设计风格

## 使用方法

### 基本用法

```jsx
import React, { useState } from 'react';
import ColorPickerModal from '../components/ColorPickerModal';

const YourComponent = () => {
  const [isColorPickerOpen, setIsColorPickerOpen] = useState(false);
  const [selectedColor, setSelectedColor] = useState('#FF3C6A');

  const handleOpenColorPicker = () => {
    setIsColorPickerOpen(true);
  };

  const handleCloseColorPicker = () => {
    setIsColorPickerOpen(false);
  };

  const handleColorSelect = (color) => {
    setSelectedColor(color);
    // 可以在这里处理颜色变更后的逻辑
  };

  return (
    <div>
      <button onClick={handleOpenColorPicker}>
        选择颜色
      </button>
      
      {/* 显示当前选中的颜色 */}
      <div style={{ backgroundColor: selectedColor, width: '50px', height: '50px' }} />
      
      {/* 选色组件 */}
      <ColorPickerModal
        isOpen={isColorPickerOpen}
        onClose={handleCloseColorPicker}
        initialColor={selectedColor}
        onColorSelect={handleColorSelect}
      />
    </div>
  );
};
```

### 自定义预设颜色

```jsx
// 自定义预设颜色列表
const customPresetColors = [
  '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'
];

<ColorPickerModal
  isOpen={isOpen}
  onClose={handleClose}
  initialColor={selectedColor}
  onColorSelect={handleColorSelect}
  presetColors={customPresetColors}
/>
```

## 属性说明

| 属性名 | 类型 | 是否必须 | 默认值 | 描述 |
|--------|------|----------|--------|------|
| isOpen | boolean | 是 | - | 控制弹窗是否显示 |
| onClose | function | 是 | - | 关闭弹窗的回调函数 |
| initialColor | string | 否 | '#FF3C6A' | 初始颜色值，格式为 #RRGGBB |
| onColorSelect | function | 是 | - | 颜色选择后的回调函数，参数为所选颜色值 |
| presetColors | array | 否 | 见下文 | 预设颜色列表，格式为 ['#RRGGBB', ...] |

### 默认预设颜色

```js
[
  '#FF3C6A', '#FF8C42', '#4CAF50', 
  '#2196F3', '#9C27B0', '#F44336', 
  '#FFEB3B', '#795548', '#607D8B', 
  '#000000', '#FFFFFF'
]
```

## 样式定制

组件使用 CSS 文件进行样式定义，您可以根据需要修改 `src/components/ColorPickerModal/index.css` 文件来自定义组件的外观。 