const workflow = {
  prompt: {
    "439": {
      "inputs": {
        "samples": [
          "442",
          0
        ],
        "vae": [
          "509",
          4
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE解码"
      }
    },
    "442": {
      "inputs": {
        "seed": 997602830768682,
        "steps": 12,
        "cfg": 5,
        "sampler_name": "dpmpp_2m_sde_gpu",
        "scheduler": "karras",
        "denoise": 0.8,
        "model": [
          "516",
          0
        ],
        "positive": [
          "516",
          1
        ],
        "negative": [
          "516",
          2
        ],
        "latent_image": [
          "520",
          0
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "种子调用"
      }
    },
    "444": {
      "inputs": {
        "seed": 657513436116965,
        "inpaint_model": [
          "445",
          0
        ],
        "image": [
          "479",
          0
        ],
        "mask": [
          "482",
          0
        ]
      },
      "class_type": "INPAINT_InpaintWithModel",
      "_meta": {
        "title": "Inpaint (using Model)"
      }
    },
    "445": {
      "inputs": {
        "model_name": "big-lama.pt"
      },
      "class_type": "INPAINT_LoadInpaintModel",
      "_meta": {
        "title": "Load Inpaint Model"
      }
    },
    "451": {
      "inputs": {
        "kernel": 5,
        "sigma": 5,
        "inpaint": [
          "439",
          0
        ],
        "original": [
          "479",
          0
        ],
        "mask": [
          "482",
          0
        ]
      },
      "class_type": "BlendInpaint",
      "_meta": {
        "title": "Blend Inpaint"
      }
    },
    "460": {
      "inputs": {
        "noise_mask": true,
        "positive": [
          "464",
          0
        ],
        "negative": [
          "470",
          0
        ],
        "vae": [
          "506",
          0
        ],
        "pixels": [
          "488",
          0
        ],
        "mask": [
          "474",
          0
        ]
      },
      "class_type": "InpaintModelConditioning",
      "_meta": {
        "title": "内补模型条件"
      }
    },
    "461": {
      "inputs": {
        "seed": 791279078836886,
        "steps": 20,
        "cfg": 1,
        "sampler_name": "euler",
        "scheduler": "normal",
        "denoise": 0.4,
        "model": [
          "499",
          0
        ],
        "positive": [
          "460",
          0
        ],
        "negative": [
          "460",
          1
        ],
        "latent_image": [
          "460",
          2
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "K采样器"
      }
    },
    "462": {
      "inputs": {
        "kernel": 30,
        "sigma": 30,
        "inpaint": [
          "463",
          0
        ],
        "original": [
          "515",
          0
        ],
        "mask": [
          "474",
          0
        ]
      },
      "class_type": "BlendInpaint",
      "_meta": {
        "title": "Blend Inpaint"
      }
    },
    "463": {
      "inputs": {
        "samples": [
          "461",
          0
        ],
        "vae": [
          "506",
          0
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE解码"
      }
    },
    "464": {
      "inputs": {
        "text": [
          "468",
          2
        ],
        "clip": [
          "505",
          0
        ]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIP文本编码"
      }
    },
    "468": {
      "inputs": {
        "text_input": "",
        "task": "more_detailed_caption",
        "fill_mask": true,
        "keep_model_loaded": false,
        "max_new_tokens": 1024,
        "num_beams": 3,
        "do_sample": true,
        "output_mask_select": "",
        "seed": 666,
        "image": [
          "521",
          0
        ],
        "florence2_model": [
          "469",
          0
        ]
      },
      "class_type": "Florence2Run",
      "_meta": {
        "title": "Florence2Run"
      }
    },
    "469": {
      "inputs": {
        "model": "Florence-2-base",
        "precision": "fp16",
        "attention": "sdpa"
      },
      "class_type": "Florence2ModelLoader",
      "_meta": {
        "title": "Florence2ModelLoader"
      }
    },
    "470": {
      "inputs": {
        "conditioning": [
          "464",
          0
        ]
      },
      "class_type": "ConditioningZeroOut",
      "_meta": {
        "title": "条件零化"
      }
    },
    "474": {
      "inputs": {
        "expand": 30,
        "tapered_corners": false,
        "mask": [
          "515",
          1
        ]
      },
      "class_type": "GrowMask",
      "_meta": {
        "title": "扩展遮罩"
      }
    },
    "479": {
      "inputs": {
        "size": 1024,
        "mode": true,
        "images": [
          "515",
          0
        ]
      },
      "class_type": "easy imageScaleDownToSize",
      "_meta": {
        "title": "Image Scale Down To Size"
      }
    },
    "482": {
      "inputs": {
        "channel": "red",
        "image": [
          "484",
          0
        ]
      },
      "class_type": "ImageToMask",
      "_meta": {
        "title": "图像转换为遮罩"
      }
    },
    "483": {
      "inputs": {
        "mask": [
          "515",
          1
        ]
      },
      "class_type": "MaskToImage",
      "_meta": {
        "title": "遮罩转换为图像"
      }
    },
    "484": {
      "inputs": {
        "size": 1024,
        "mode": true,
        "images": [
          "483",
          0
        ]
      },
      "class_type": "easy imageScaleDownToSize",
      "_meta": {
        "title": "Image Scale Down To Size"
      }
    },
    "488": {
      "inputs": {
        "upscale_method": "lanczos",
        "width": [
          "495",
          0
        ],
        "height": [
          "495",
          1
        ],
        "crop": "disabled",
        "image": [
          "451",
          0
        ]
      },
      "class_type": "ImageScale",
      "_meta": {
        "title": "缩放图像"
      }
    },
    "495": {
      "inputs": {
        "image": [
          "515",
          0
        ]
      },
      "class_type": "easy imageSize",
      "_meta": {
        "title": "ImageSize"
      }
    },
    "499": {
      "inputs": {
        "model": [
          "508",
          0
        ]
      },
      "class_type": "DifferentialDiffusion",
      "_meta": {
        "title": "差异扩散DifferentialDiffusion"
      }
    },
    "505": {
      "inputs": {
        "clip_name1": "t5xxl_fp8_e4m3fn.safetensors",
        "clip_name2": "clip_l.safetensors",
        "type": "flux",
        "device": "default"
      },
      "class_type": "DualCLIPLoader",
      "_meta": {
        "title": "双CLIP加载器"
      }
    },
    "506": {
      "inputs": {
        "vae_name": "ae.sft"
      },
      "class_type": "VAELoader",
      "_meta": {
        "title": "加载VAE"
      }
    },
    "507": {
      "inputs": {
        "filename_prefix": "Optimize",
        "images": [
          "462",
          0
        ]
      },
      "class_type": "SaveImage",
      "_meta": {
        "title": "保存图像"
      }
    },
    "508": {
      "inputs": {
        "unet_name": "flux1-fill-dev.safetensors",
        "weight_dtype": "fp8_e4m3fn_fast"
      },
      "class_type": "UNETLoader",
      "_meta": {
        "title": "UNet加载器"
      }
    },
    "509": {
      "inputs": {
        "ckpt_name": "dreamshaperXL_v21TurboDPMSDE.safetensors",
        "vae_name": "sdxl_vae.safetensors",
        "clip_skip": -2,
        "lora_name": "None",
        "lora_model_strength": 1,
        "lora_clip_strength": 1,
        "positive": [
          "514",
          0
        ],
        "negative": "CLIP_NEGATIVE",
        "token_normalization": "none",
        "weight_interpretation": "comfy",
        "empty_latent_width": 512,
        "empty_latent_height": 512,
        "batch_size": 1
      },
      "class_type": "Efficient Loader",
      "_meta": {
        "title": "Efficient Loader"
      }
    },
    "514": {
      "inputs": {
        "from_translate": "auto",
        "to_translate": "english",
        "add_proxies": false,
        "proxies": "",
        "auth_data": "",
        "service": "GoogleTranslator",
        "text": "裸露的肚子，光滑的裸露的皮肤",
        "Show proxy": "proxy_hide",
        "Show authorization": "authorization_hide"
      },
      "class_type": "DeepTranslatorTextNode",
      "_meta": {
        "title": "描述词"
      }
    },
    "515": {
      "inputs": {
        "aspect_ratio": "original",
        "proportional_width": 1,
        "proportional_height": 1,
        "fit": "letterbox",
        "method": "lanczos",
        "round_to_multiple": "32",
        "scale_to_side": "longest",
        "scale_to_length": 1024,
        "background_color": "#000000",
        "image": [
          "523",
          0
        ],
        "mask": [
          "523",
          1
        ]
      },
      "class_type": "LayerUtility: ImageScaleByAspectRatio V2",
      "_meta": {
        "title": "LayerUtility: ImageScaleByAspectRatio V2"
      }
    },
    "516": {
      "inputs": {
        "scale": 1,
        "start_at": 0,
        "end_at": 10000,
        "model": [
          "509",
          0
        ],
        "vae": [
          "509",
          4
        ],
        "image": [
          "444",
          0
        ],
        "mask": [
          "482",
          0
        ],
        "brushnet": [
          "517",
          0
        ],
        "positive": [
          "509",
          1
        ],
        "negative": [
          "509",
          2
        ]
      },
      "class_type": "BrushNet",
      "_meta": {
        "title": "BrushNet"
      }
    },
    "517": {
      "inputs": {
        "brushnet": "segmentation_mask_brushnet_ckpt_sdxl_v1/segmentation_mask_brushnet_ckpt_sdxl_v1.safetensors",
        "dtype": "float16"
      },
      "class_type": "BrushNetLoader",
      "_meta": {
        "title": "BrushNet Loader"
      }
    },
    "520": {
      "inputs": {
        "amount": 2,
        "samples": [
          "516",
          3
        ]
      },
      "class_type": "RepeatLatentBatch",
      "_meta": {
        "title": "图片数量"
      }
    },
    "521": {
      "inputs": {
        "split_count": 1,
        "images": [
          "451",
          0
        ]
      },
      "class_type": "ImageBatchSplitter //Inspire",
      "_meta": {
        "title": "Image Batch Splitter (Inspire)"
      }
    },
    "523": {
      "inputs": {
        "url": "https://"
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "款式图上传图片 手动图蒙版"
      }
    }
  }
};

module.exports = workflow; 