/**
 * 移动端弹窗层级修复
 * 确保所有弹窗在移动端都能正确显示在导航栏之上
 * 同时确保导航栏显示在侧边栏之上
 */

/* 移动端弹窗层级修复 */
@media (max-width: 888px) {
  /* 确保导航栏显示在侧边栏之上 */
  .navbar {
    z-index: 100 !important; /* 确保导航栏在侧边栏之上 */
  }
  
  .sub-navbar {
    z-index: 99 !important; /* 确保副导航栏在侧边栏之上 */
  }
  
  .sidebar,
  .sidebar.collapsed {
    z-index: 95 !important; /* 确保侧边栏在导航栏之下 */
  }
  
  .sidebar-section {
    z-index: 94 !important; /* 确保侧边栏区域在导航栏之下 */
  }
  
  /* 确保所有弹窗包装器都有足够高的z-index */
  .clothing-attributes-modal,
  .color-picker-wrapper,
  .upload-guide-modal,
  .scene-select-modal-wrapper,
  .model-registration-wrapper,
  .magnification-size-wrapper,
  .extend-size-wrapper,
  .mask-draw-modal,
  .mask-draw-overlay {
    z-index: 999999 !important; /* 强制设置最高层级 */
  }

  /* 确保所有弹窗内容都有足够高的z-index */
  .clothing-attributes-modal .modal-content,
  .color-picker-modal,
  .upload-guide-modal-content,
  .scene-select-modal .modal-content,
  .model-registration-modal,
  .magnification-size-modal,
  .extend-size-modal,
  .mask-draw-content {
    z-index: 1000000 !important; /* 强制设置最高层级 */
  }

  /* 确保Ant Design弹窗也有足够高的z-index */
  .ant-modal,
  .ant-modal-mask,
  .ant-modal-wrap {
    z-index: 1000001 !important;
  }

  .ant-modal-content {
    z-index: 1000002 !important;
  }

  /* 确保确认弹窗显示在最上层 */
  .ant-modal-confirm,
  .ant-modal-confirm .ant-modal,
  .ant-modal-confirm .ant-modal-mask,
  .ant-modal-confirm .ant-modal-wrap {
    z-index: 1000003 !important;
  }

  .ant-modal-confirm .ant-modal-content {
    z-index: 1000004 !important;
  }
}

/* 针对超小屏幕的额外修复 */
@media (max-width: 480px) {
  /* 进一步提高z-index确保在所有移动端浏览器中都能正确显示 */
  .clothing-attributes-modal,
  .color-picker-wrapper,
  .upload-guide-modal,
  .scene-select-modal-wrapper,
  .model-registration-wrapper,
  .magnification-size-wrapper,
  .extend-size-wrapper,
  .mask-draw-modal,
  .mask-draw-overlay {
    z-index: 2147483647 !important; /* 使用最大可能的z-index值 */
  }

  .clothing-attributes-modal .modal-content,
  .color-picker-modal,
  .upload-guide-modal-content,
  .scene-select-modal .modal-content,
  .model-registration-modal,
  .magnification-size-modal,
  .extend-size-modal,
  .mask-draw-content {
    z-index: 2147483647 !important; /* 使用最大可能的z-index值 */
  }
}

/* 确保弹窗在移动端浏览器地址栏隐藏时也能正确显示 */
@media (max-width: 888px) and (orientation: portrait) {
  .clothing-attributes-modal,
  .color-picker-wrapper,
  .upload-guide-modal,
  .scene-select-modal-wrapper,
  .model-registration-wrapper,
  .magnification-size-wrapper,
  .extend-size-wrapper,
  .mask-draw-modal {
    height: 100vh !important;
    height: 100dvh !important; /* 使用动态视口高度 */
  }
}

@media (max-width: 888px) and (orientation: landscape) {
  .clothing-attributes-modal,
  .color-picker-wrapper,
  .upload-guide-modal,
  .scene-select-modal-wrapper,
  .model-registration-wrapper,
  .magnification-size-wrapper,
  .extend-size-wrapper,
  .mask-draw-modal {
    height: 100vh !important;
    height: 100dvh !important; /* 使用动态视口高度 */
  }
} 