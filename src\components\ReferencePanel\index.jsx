import React from 'react';
import PropTypes from 'prop-types';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

/**
 * 参考图面板组件
 * 用于显示已上传的参考图片
 * 使用统一的panels.css样式
 */
const ReferencePanel = ({ panel, onDelete, onReupload, onExpandClick, isActive }) => {
  const { componentId, title, status, serverFileName, originalImage, url, fileInfo, error } = panel;
  
  const handleExpandClick = (e) => {
    if (onExpandClick) {
      const panelWithType = {
        ...panel,
        type: 'reference'
      };
      
      const buttonRect = e.currentTarget.getBoundingClientRect();
      onExpandClick(panelWithType, {
        top: buttonRect.top,
        left: buttonRect.left + buttonRect.width
      });
    }
  };
  
  return (
    <div className="panel-component" key={componentId}>
      <div className="component-header">
        <div className="component-info">
          <ThumbnailWithPreview
            imageUrl={url}
            alt="参考图片"
            status={status}
            error={error}
            featureName="参考图预览"
            transparentBg={true}
          />
          <div className="component-text">
            <h3>{title || "参考图"}</h3>
            <div className="component-content">
              <p>
                {status === 'completed' && '上传完成'}
                {status === 'processing' && '处理中...'}
                {status === 'error' && error}
              </p>
            </div>
          </div>
        </div>
        <button 
          className={`expand-btn ${isActive ? 'active' : ''}`}
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

ReferencePanel.propTypes = {
  panel: PropTypes.shape({
    componentId: PropTypes.string.isRequired,
    title: PropTypes.string,
    status: PropTypes.oneOf(['processing', 'completed', 'error']),
    serverFileName: PropTypes.string,
    originalImage: PropTypes.string,
    url: PropTypes.string,
    fileInfo: PropTypes.shape({
      size: PropTypes.number,
      width: PropTypes.number,
      height: PropTypes.number,
      format: PropTypes.string,
      serverFileName: PropTypes.string
    }),
    error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
    type: PropTypes.string
  }).isRequired,
  onDelete: PropTypes.func,
  onReupload: PropTypes.func,
  onExpandClick: PropTypes.func,
  isActive: PropTypes.bool
};

export default ReferencePanel; 