.profile-page {
  width: 100%;
  height: calc(100vh - 68px); /* 减去导航栏高度 */
  background: var(--bg-secondary, #f5f5f7);
  overflow: auto;
  padding: 0;
}

.profile-container {
  display: flex;
  height: 100%;
  padding: 12px;
  user-select: none;
  overflow: auto;
}

.profile-content-area {
  flex: 1;
  background: var(--bg-primary, white);
  border-radius: var(--radius-lg, 12px);
  box-shadow: var(--shadow-sm, 0 2px 8px rgba(0, 0, 0, 0.05));
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  margin-left: 0px; /* 增加左侧间距，与侧边栏保持一致 */
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color, #e8e8e8);
}

.content-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary, #333);
}

.content-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.profile-form {
  max-width: 600px;
}

.form-group {
  margin-bottom: 36px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary, #333);
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color, #e8e8e8);
  border-radius: 6px;
  font-size: 14px;
  background-color: var(--bg-primary, white);
  color: var(--text-primary, #333);
}

.form-group input.username-input,
.form-group input.phone-input {
  width: 300px;
}

.form-group input:disabled {
  background-color: var(--bg-secondary, #f5f5f7);
  color: var(--text-secondary, #666);
  cursor: not-allowed;
}

.input-with-button {
  display: flex;
  align-items: center;
  gap: 8px;
  width: fit-content;
}

.input-with-button input {
  flex: 0 0 auto;
}

.input-with-button .edit-button {
  flex-shrink: 0;
}

.input-wrapper {
  position: relative;
  flex: 1;
}

.error-input {
  border-color: #ff4d4f !important;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  position: absolute;
  bottom: -20px;
}

.username-rules {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
  line-height: 1.5;
}

.disabled-input {
  opacity: 0.8;
}

.form-actions {
  margin-top: 24px;
}

.edit-button {
  padding: 6px 16px;
  border: 1px solid var(--border-color, #e8e8e8);
  border-radius: 6px;
  background: var(--bg-primary, white);
  color: var(--text-secondary, #666);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-button:hover {
  border-color: var(--brand-primary, #4f5bd5);
  color: var(--brand-primary, #4f5bd5);
}

.save-button {
  padding: 10px 24px;
  background: linear-gradient(45deg, #4f5bd5, #962fbf);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-button:hover {
  filter: brightness(1.1);
}

.logout-button {
  background: linear-gradient(45deg, #FF8C42, #FF3C6A);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.logout-button:hover {
  filter: brightness(1.1);
}

.login-tip {
  font-size: 12px;
  color: var(--text-secondary, #666);
  margin-top: 4px;
  line-height: 1.5;
}

.phone-text {
  font-size: 14px;
  color: var(--text-primary, #333);
  margin-top: 4px;
  margin-bottom: 8px;
  letter-spacing: 2px;
}

/* 保证所有表单内容左对齐 */
.form-group > label,
.form-group > .phone-text,
.form-group > .input-with-button,
.form-group > .login-tip,
.form-group > .username-rules {
  margin-left: 0;
} 