const wrapper = require("ws");


class MessageProcessor {
    constructor(workflowManager) {

        this.workflowManager = workflowManager;
    }

    /**
     * 智能填充工作流模板
     * @param {string} workflowName 工作流名称
     * @param {object} inputData 输入数据
     * @returns {object} 填充后的工作流
     */
    populateWorkflow(workflowName, inputData) {
        const workflowTemplate = this.workflowManager.getWorkflow(workflowName);

        // 深度遍历工作流节点并智能填充数据
        for (const nodeId in workflowTemplate) {
            const node = workflowTemplate[nodeId];
            if (node.inputs && node.inputs.needInput && inputData[nodeId]) {
                this._processNodeInputs(node.inputs, inputData[nodeId]);
            }
        }

        return workflowTemplate;
    }

    /**
     * 处理节点输入
     * @param {object} nodeInputs 节点输入定义
     * @param {object} inputValues 输入值
     */
    _processNodeInputs(nodeInputs, inputValues) {
        for (const inputName in inputValues) {
            // 检查输入是否存在且值不为undefined
            nodeInputs[inputName] = inputValues[inputName];
        }
    }



    // 处理ComfyUI响应
    processResponse(response, workflowName) {
        const standardResponse = {
            workflow: workflowName,
            status: 'completed',
            timestamp: new Date().toISOString(),
            outputs: {}
        };
        const responseInfo = Object.values(response)[0];
        if(!responseInfo.status.completed) {
            throw new Error(`Processing ${JSON.stringify(responseInfo.status.messages)} failed`);
        }
        console.log(Object.values(response).length > 1 ? "返回结果数量大于1 检查解析" : "")
        const outputs = responseInfo.outputs
        if (response && outputs) {
            for (const nodeId in outputs) {
                if (outputs[nodeId].images) {
                    standardResponse.outputs.images = outputs[nodeId].images.map(img => ({
                        filename: img.filename,
                        type: img.type,
                        subfolder: img.subfolder
                    }));
                }
            }
        }else {
            throw new Error(`No images found for ${response}`);
        }

        return standardResponse;
    }

}

module.exports = MessageProcessor;