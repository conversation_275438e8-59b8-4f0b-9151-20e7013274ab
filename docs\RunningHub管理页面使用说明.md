# RunningHub管理页面使用说明

## 概述

RunningHub管理页面是为管理员提供的综合管理界面，用于管理RunningHub平台的配置、任务、用户和系统设置。该页面位于管理后台的"RunningHub管理"菜单项下。

## 功能模块

### 1. 概览页面

概览页面提供了RunningHub平台的整体运行状况：

- **统计数据**：显示总配置数、总任务数、运行中任务数、成功任务数等关键指标
- **系统状态**：展示平台的基本信息和使用说明
- **快速导航**：提供到其他功能模块的快速链接

### 2. 配置管理

配置管理模块允许管理员管理RunningHub API配置：

#### 功能特性
- **添加配置**：创建新的API配置，包括配置名称、API密钥、工作流ID等
- **编辑配置**：修改现有配置的信息
- **删除配置**：移除不需要的配置
- **测试配置**：验证API密钥的有效性和账户信息
- **默认配置**：设置默认使用的配置

#### 配置字段说明
- **配置名称**：用于识别配置的友好名称
- **API密钥**：32位RunningHub API密钥
- **默认工作流ID**：该配置默认使用的工作流ID
- **描述**：配置的详细说明
- **设为默认配置**：是否将此配置设为默认

### 3. 任务管理

任务管理模块提供任务的创建、监控和管理功能：

#### 功能特性
- **创建任务**：支持创建简易任务和高级任务
- **任务列表**：显示所有任务的状态和基本信息
- **任务详情**：查看任务的详细信息、参数和结果
- **任务控制**：取消正在运行的任务
- **结果下载**：下载任务生成的文件

#### 任务类型
- **简易任务**：使用默认参数快速创建任务
- **高级任务**：支持自定义节点参数的复杂任务

#### 任务状态
- **已创建**：任务已创建，等待处理
- **排队中**：任务在队列中等待执行
- **运行中**：任务正在执行中
- **成功**：任务执行成功
- **失败**：任务执行失败
- **已取消**：任务被用户取消

### 4. 账户信息

账户信息模块显示RunningHub账户的详细信息：

#### 显示内容
- **用户ID**：RunningHub平台的用户ID
- **用户名**：账户用户名
- **会员类型**：普通会员或高级会员
- **余额**：账户剩余积分
- **API调用次数**：累计API调用次数
- **最后更新时间**：信息最后更新的时间

#### 使用统计
- **今日任务数**：当天创建的任务数量
- **本月任务数**：当月创建的任务数量
- **总任务数**：历史总任务数量

### 5. 工作流管理

工作流管理模块提供工作流信息的查看和管理：

#### 功能特性
- **获取工作流信息**：通过工作流ID获取详细信息
- **工作流详情**：显示工作流的JSON配置和节点信息
- **配置查看**：以格式化的方式显示工作流配置

#### 使用方法
1. 选择一个API配置
2. 输入要查询的工作流ID
3. 点击"获取工作流信息"按钮
4. 查看返回的工作流详情

## 使用流程

### 初始设置

1. **添加API配置**
   - 进入"配置管理"标签页
   - 点击"添加配置"按钮
   - 填写配置信息（名称、API密钥、工作流ID等）
   - 保存配置

2. **测试配置**
   - 在配置列表中找到新添加的配置
   - 点击"测试"按钮验证配置有效性
   - 查看返回的账户信息

### 任务管理

1. **创建任务**
   - 进入"任务管理"标签页
   - 点击"创建任务"按钮
   - 选择配置和任务类型
   - 填写任务参数
   - 提交任务

2. **监控任务**
   - 在任务列表中查看任务状态
   - 点击"详情"查看任务详细信息
   - 对于运行中的任务，可以选择取消
   - 对于成功的任务，可以下载结果

### 账户监控

1. **查看账户信息**
   - 进入"账户信息"标签页
   - 点击"刷新"按钮获取最新信息
   - 查看账户余额和使用统计

2. **监控使用情况**
   - 查看今日、本月和总任务数量
   - 监控API调用频率
   - 评估账户使用效率

## 注意事项

### 安全性
- API密钥是敏感信息，请妥善保管
- 不要在客户端代码中硬编码API密钥
- 定期更换API密钥以提高安全性

### 使用限制
- 免费用户有并发限制
- 企业用户可联系商务开通更高并发
- 注意账户余额，及时充值

### 最佳实践
- 为不同用途创建不同的配置
- 定期清理过期的任务记录
- 监控任务成功率，及时处理失败任务
- 合理设置任务超时时间

## 故障排除

### 常见问题

1. **配置测试失败**
   - 检查API密钥是否正确（32位字符）
   - 确认账户是否有足够余额
   - 验证网络连接是否正常

2. **任务创建失败**
   - 检查工作流ID是否存在
   - 确认工作流在RunningHub平台上能正常运行
   - 验证节点参数格式是否正确

3. **任务执行失败**
   - 查看任务详情中的错误信息
   - 在RunningHub网页端查看具体错误
   - 检查工作流参数是否正确

### 联系支持

如果遇到无法解决的问题，请：
1. 记录详细的错误信息
2. 提供相关的配置和任务ID
3. 联系技术支持团队

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的配置管理
- 支持任务创建和监控
- 提供账户信息查看
- 支持工作流信息获取
