/**
 * RunningHub API客户端
 * 提供与RunningHub平台交互的API接口
 */

import api from './index';

/**
 * RunningHub API类
 */
class RunningHubAPI {
  constructor() {
    this.baseURL = '/api/runninghub';
  }

  /**
   * 创建简易任务
   * @param {Object} taskData - 任务数据
   * @param {string} taskData.apiKey - API密钥
   * @param {string} taskData.workflowId - 工作流ID
   * @param {boolean} taskData.addMetadata - 是否添加元数据
   * @returns {Promise<Object>} 任务创建结果
   */
  async createSimpleTask(taskData) {
    try {
      const response = await api.post(`${this.baseURL}/tasks/simple`, taskData);
      return response.data;
    } catch (error) {
      console.error('创建简易任务失败:', error);
      throw error;
    }
  }

  /**
   * 创建高级任务
   * @param {Object} taskData - 任务数据
   * @param {string} taskData.apiKey - API密钥
   * @param {string} taskData.workflowId - 工作流ID
   * @param {Array} taskData.nodeInfoList - 节点信息列表
   * @param {boolean} taskData.addMetadata - 是否添加元数据
   * @returns {Promise<Object>} 任务创建结果
   */
  async createAdvancedTask(taskData) {
    try {
      const response = await api.post(`${this.baseURL}/tasks/advanced`, taskData);
      return response.data;
    } catch (error) {
      console.error('创建高级任务失败:', error);
      throw error;
    }
  }

  /**
   * 创建AI应用任务
   * @param {Object} taskData - 任务数据
   * @param {string} taskData.apiKey - API密钥
   * @param {string} taskData.appId - 应用ID
   * @param {Object} taskData.inputData - 输入数据
   * @returns {Promise<Object>} 任务创建结果
   */
  async createAIAppTask(taskData) {
    try {
      const response = await api.post(`${this.baseURL}/tasks/app`, taskData);
      return response.data;
    } catch (error) {
      console.error('创建AI应用任务失败:', error);
      throw error;
    }
  }

  /**
   * 查询任务状态
   * @param {string} taskId - 任务ID
   * @param {string} apiKey - API密钥
   * @returns {Promise<Object>} 任务状态
   */
  async getTaskStatus(taskId, apiKey) {
    try {
      const response = await api.get(`${this.baseURL}/tasks/${taskId}/status`, {
        params: { apiKey }
      });
      return response.data;
    } catch (error) {
      console.error('查询任务状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取任务结果
   * @param {string} taskId - 任务ID
   * @param {string} apiKey - API密钥
   * @returns {Promise<Object>} 任务结果
   */
  async getTaskResults(taskId, apiKey) {
    try {
      const response = await api.get(`${this.baseURL}/tasks/${taskId}/results`, {
        params: { apiKey }
      });
      return response.data;
    } catch (error) {
      console.error('获取任务结果失败:', error);
      throw error;
    }
  }

  /**
   * 取消任务
   * @param {string} taskId - 任务ID
   * @param {string} apiKey - API密钥
   * @returns {Promise<Object>} 取消结果
   */
  async cancelTask(taskId, apiKey) {
    try {
      const response = await api.delete(`${this.baseURL}/tasks/${taskId}`, {
        data: { apiKey }
      });
      return response.data;
    } catch (error) {
      console.error('取消任务失败:', error);
      throw error;
    }
  }

  /**
   * 获取账户信息
   * @param {string} apiKey - API密钥
   * @returns {Promise<Object>} 账户信息
   */
  async getAccountInfo(apiKey) {
    try {
      const response = await api.get(`${this.baseURL}/account`, {
        params: { apiKey }
      });
      return response.data;
    } catch (error) {
      console.error('获取账户信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取工作流JSON
   * @param {string} workflowId - 工作流ID
   * @param {string} apiKey - API密钥
   * @returns {Promise<Object>} 工作流JSON
   */
  async getWorkflowJson(workflowId, apiKey) {
    try {
      const response = await api.get(`${this.baseURL}/workflows/${workflowId}`, {
        params: { apiKey }
      });
      return response.data;
    } catch (error) {
      console.error('获取工作流JSON失败:', error);
      throw error;
    }
  }

  /**
   * 上传资源文件
   * @param {File} file - 文件对象
   * @param {string} apiKey - API密钥
   * @returns {Promise<Object>} 上传结果
   */
  async uploadResource(file, apiKey) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('apiKey', apiKey);

      const response = await api.post(`${this.baseURL}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('上传资源失败:', error);
      throw error;
    }
  }

  /**
   * 批量创建任务
   * @param {Array} taskList - 任务列表
   * @returns {Promise<Object>} 批量创建结果
   */
  async createBatchTasks(taskList) {
    try {
      const response = await api.post(`${this.baseURL}/tasks/batch`, {
        taskList
      });
      return response.data;
    } catch (error) {
      console.error('批量创建任务失败:', error);
      throw error;
    }
  }

  /**
   * 等待任务完成
   * @param {string} taskId - 任务ID
   * @param {string} apiKey - API密钥
   * @param {Object} options - 选项
   * @param {number} options.timeout - 超时时间（毫秒）
   * @param {number} options.interval - 轮询间隔（毫秒）
   * @returns {Promise<Object>} 任务结果
   */
  async waitForTaskCompletion(taskId, apiKey, options = {}) {
    try {
      const response = await api.post(`${this.baseURL}/tasks/${taskId}/wait`, {
        apiKey,
        ...options
      });
      return response.data;
    } catch (error) {
      console.error('等待任务完成失败:', error);
      throw error;
    }
  }

  /**
   * 轮询任务状态直到完成
   * @param {string} taskId - 任务ID
   * @param {string} apiKey - API密钥
   * @param {Object} options - 选项
   * @param {number} options.timeout - 超时时间（毫秒）
   * @param {number} options.interval - 轮询间隔（毫秒）
   * @param {Function} options.onProgress - 进度回调
   * @returns {Promise<Object>} 任务结果
   */
  async pollTaskUntilComplete(taskId, apiKey, options = {}) {
    const {
      timeout = 300000, // 5分钟默认超时
      interval = 3000,  // 3秒轮询间隔
      onProgress = null
    } = options;

    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      try {
        const statusResult = await this.getTaskStatus(taskId, apiKey);
        
        if (!statusResult.success) {
          throw new Error(statusResult.error || '获取任务状态失败');
        }

        const status = statusResult.status;

        // 触发进度回调
        if (onProgress) {
          onProgress({
            taskId,
            status,
            elapsed: Date.now() - startTime
          });
        }

        // 检查任务是否完成
        if (status === 'SUCCESS') {
          const results = await this.getTaskResults(taskId, apiKey);
          return {
            success: true,
            status: 'SUCCESS',
            results: results.results || [],
            elapsed: Date.now() - startTime
          };
        } else if (status === 'FAILED') {
          return {
            success: false,
            status: 'FAILED',
            error: '任务执行失败',
            elapsed: Date.now() - startTime
          };
        }

        // 等待下次轮询
        await new Promise(resolve => setTimeout(resolve, interval));

      } catch (error) {
        console.error('轮询任务状态时发生错误:', error);
        throw error;
      }
    }

    // 超时
    throw new Error('任务执行超时');
  }
}

// 创建API实例
const runningHubAPI = new RunningHubAPI();

// 导出API方法
export const {
  createSimpleTask,
  createAdvancedTask,
  createAIAppTask,
  getTaskStatus,
  getTaskResults,
  cancelTask,
  getAccountInfo,
  getWorkflowJson,
  uploadResource,
  createBatchTasks,
  waitForTaskCompletion,
  pollTaskUntilComplete
} = runningHubAPI;

export default runningHubAPI;
