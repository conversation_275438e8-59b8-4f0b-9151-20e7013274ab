# 工作流映射配置说明

## 概述

本文档说明如何配置系统工作流与RunningHub平台工作流的映射关系，实现动态工作流配置管理。

## 功能特性

### 1. 动态工作流加载
- ✅ 从系统配置中自动获取工作流列表
- ✅ 支持工作流分类显示（款式设计、模特图、工具）
- ✅ 工作流描述和详细信息展示

### 2. 映射配置管理
- ✅ 可视化工作流映射配置界面
- ✅ 支持批量配置和单独配置
- ✅ 映射状态实时显示

### 3. 导入导出功能
- ✅ 工作流映射配置导出为JSON文件
- ✅ 支持从JSON文件导入映射配置
- ✅ 配置备份和迁移

### 4. 映射状态监控
- ✅ 工作流映射状态统计
- ✅ 已映射/未映射工作流分类显示
- ✅ 多配置映射关系查看

## 使用说明

### 1. 访问管理页面

1. 登录系统（管理员权限）
2. 进入管理后台：`/admin`
3. 点击"RunningHub管理"

### 2. 配置工作流映射

#### 2.1 在配置创建/编辑时配置
1. 点击"添加配置"或"编辑"按钮
2. 在"工作流映射配置"部分：
   - 每个工作流显示：名称、ID、分类、描述
   - 输入对应的RunningHub工作流ID
   - 支持部分配置（不需要配置所有工作流）

#### 2.2 使用快速配置功能
1. 点击"填充示例映射"按钮快速填充常用映射
2. 点击"清空映射"按钮清空所有映射
3. 根据实际情况修改映射ID

### 3. 查看映射状态

#### 3.1 概览页面
- 显示工作流映射状态标签
- 绿色标签：已配置映射
- 灰色标签：未配置映射

#### 3.2 工作流映射标签页
- 显示所有工作流的详细信息
- 映射状态统计（总数、已映射、未映射）
- 每个工作流的映射配置详情

### 4. 导入导出映射

#### 4.1 导出映射
1. 在配置列表中点击"导出映射"按钮
2. 自动下载JSON格式的映射文件
3. 文件包含配置名称、导出时间、映射关系

#### 4.2 导入映射
1. 在配置列表中点击"导入映射"按钮
2. 选择之前导出的JSON文件
3. 系统自动合并映射配置

## 工作流分类

### 款式设计类
- **A01-trending**: 爆款开发 - 基于趋势分析生成爆款设计
- **A02-optimize**: 款式优化 - 优化现有款式设计
- **A02b-optimizetext**: 文本优化 - 优化设计文本描述
- **A03-inspiration**: 灵感探索 - 探索设计灵感和创意
- **A05-drawing**: 生成线稿 - 生成设计线稿图
- **A06-divergent**: 爆款延伸 - 基于爆款进行延伸设计

### 模特图类
- **B01-fashion**: 时尚大片 - 生成时尚大片效果
- **B02-tryonauto**: 自动换装 - 自动换装效果
- **B02-tryonmanual**: 手动换装 - 手动控制换装
- **B02-tryonother**: 其他换装 - 其他换装方式
- **B03-changemodel**: 换模特 - 更换模特
- **B04-recolor**: 服装复色 - 服装颜色调整
- **B05-fabric**: 换面料 - 更换服装面料
- **B06-background**: 换背景 - 更换背景环境
- **B07-virtual**: 虚拟模特 - 虚拟模特生成
- **B08-detailmigration**: 细节还原 - 细节迁移和还原
- **B09-handfix**: 手部修复 - 手部细节修复
- **B10-changeposture**: 换姿势 - 更换模特姿势

### 工具类
- **C01-extract**: 图片取词 - 从图片中提取关键词
- **C02-upscale**: 高清放大 - 图片高清放大
- **C03-mattingbg**: 背景抠图 - 背景自动抠图
- **C03-mattingbgfile**: 文件背景抠图 - 文件背景抠图
- **C03-mattingclo**: 服装抠图 - 服装自动抠图
- **C04-extend**: 智能扩图 - 智能图片扩展

## 配置示例

### 示例映射配置
```json
{
  "configName": "生产环境配置",
  "exportTime": "2024-01-15T10:30:00.000Z",
  "workflowMappings": {
    "A01-trending": "1850925505116598274",
    "B01-fashion": "1851234567890123456",
    "C01-extract": "1851234567890123457",
    "A02-optimize": "1851234567890123458",
    "B02-tryonauto": "1851234567890123459"
  }
}
```

### 配置文件结构
```javascript
// config/default.json 中的工作流配置
{
  "comfyUi": {
    "workflows": [
      {
        "id": "A01-trending",
        "name": "爆款开发",
        "category": "款式设计",
        "description": "基于趋势分析生成爆款设计",
        "displayName": "爆款开发"
      }
      // ... 更多工作流
    ]
  }
}
```

## API接口

### 获取工作流列表
```http
GET /api/runninghub/admin/workflows
```

### 获取工作流平台信息
```http
GET /api/runninghub/admin/workflow-info
```

### 配置管理
```http
POST /api/runninghub/admin/configs
PUT /api/runninghub/admin/configs/:id
GET /api/runninghub/admin/configs
DELETE /api/runninghub/admin/configs/:id
```

## 最佳实践

### 1. 映射配置建议
- 优先配置常用工作流的映射
- 为不同环境创建不同的配置
- 定期备份映射配置

### 2. 命名规范
- 配置名称应该清晰标识用途（如"生产环境"、"测试环境"）
- 描述应该包含配置的具体用途和注意事项

### 3. 测试流程
1. 创建配置后立即进行测试
2. 确认API密钥有效性
3. 验证关键工作流的映射是否正确

### 4. 维护建议
- 定期检查映射配置的有效性
- 及时更新失效的工作流ID
- 保持配置文档的更新

## 故障排除

### 1. 工作流列表为空
- 检查系统配置文件中的工作流定义
- 确认API接口正常响应
- 查看服务器日志

### 2. 映射配置不生效
- 确认配置已保存到数据库
- 检查工作流ID是否正确
- 验证配置是否设为默认配置

### 3. 导入导出失败
- 检查文件格式是否正确
- 确认文件权限设置
- 查看浏览器控制台错误

## 技术实现

### 前端组件
- 动态工作流列表渲染
- 响应式表单布局
- 文件导入导出处理

### 后端API
- 工作流配置动态加载
- 数据库映射关系存储
- 配置验证和测试

### 数据模型
- workflowMappings字段使用Map类型
- 支持动态键值对存储
- 自动序列化和反序列化

通过这套完整的工作流映射配置系统，管理员可以灵活地配置和管理系统工作流与RunningHub平台的映射关系，实现真正的动态配置管理。
