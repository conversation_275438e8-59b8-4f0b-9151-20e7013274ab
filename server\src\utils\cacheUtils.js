const { MongoClient } = require('mongodb');

class MongoDBCache {
  /**
   * 构造函数
   * @param {Object} options 配置选项
   * @param {string} options.uri MongoDB连接字符串
   * @param {string} options.dbName 数据库名
   * @param {string} options.collectionName 集合名
   * @param {number} options.defaultTTL 默认缓存时间(毫秒)
   */
  constructor(options = {}) {
    this.uri = options.uri || process.env.MONGODB_URI;
    this.dbName = options.dbName || 'app_cache';
    this.collectionName = options.collectionName || 'cache_entries';
    this.defaultTTL = options.defaultTTL || 5 * 60 * 1000; // 默认5分钟
    
    this.client = null;
    this.db = null;
    this.collection = null;
    this.isConnected = false;
  }

  /**
   * 连接数据库
   */
  async connect() {
    if (this.isConnected) return;
    
    this.client = new MongoClient(this.uri, {
      connectTimeoutMS: 5000,
      socketTimeoutMS: 30000,
      serverSelectionTimeoutMS: 5000,
      maxPoolSize: 10
    });
    
    try {
      await this.client.connect();
      this.db = this.client.db(this.dbName);
      this.collection = this.db.collection(this.collectionName);
      
      // 创建TTL索引
      await this.collection.createIndex(
        { expiresAt: 1 },
        { expireAfterSeconds: 0 }
      );
      
      this.isConnected = true;
    } catch (err) {
      console.error('MongoDB连接失败:', err);
      throw err;
    }
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} value 缓存值
   * @param {number} ttl 缓存时间(毫秒)，可选
   */
  async set(key, value, ttl) {
    if (!this.isConnected) await this.connect();
    let expiresAt = null;
    if (ttl <= 0){
      expiresAt = new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 100);
    }else{
      expiresAt = new Date(Date.now() + (ttl || this.defaultTTL));
    }

    
    await this.collection.updateOne(
      { _id: key },
      { $set: { value, expiresAt } },
      { upsert: true }
    );
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @returns {Promise<any>} 缓存值或null
   */
  async get(key) {
    if (!this.isConnected) await this.connect();
    
    const doc = await this.collection.findOne({ _id: key });
    
    if (!doc || (doc.expiresAt && doc.expiresAt < new Date())) {
      return null;
    }
    
    return doc.value;
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   */
  async del(key) {
    if (!this.isConnected) await this.connect();
    
    await this.collection.deleteOne({ _id: key });
  }

  /**
   * 检查缓存是否存在
   * @param {string} key 缓存键
   * @returns {Promise<boolean>} 是否存在
   */
  async has(key) {
    if (!this.isConnected) await this.connect();
    
    const doc = await this.collection.findOne({ _id: key });
    return !!doc && (!doc.expiresAt || doc.expiresAt >= new Date());
  }

  /**
   * 获取剩余存活时间(毫秒)
   * @param {string} key 缓存键
   * @returns {Promise<number>} 剩余时间(毫秒)，-1表示永久，-2表示不存在
   */
  async ttl(key) {
    if (!this.isConnected) await this.connect();
    
    const doc = await this.collection.findOne({ _id: key });
    
    if (!doc) return -2;
    if (!doc.expiresAt) return -1;
    
    const remaining = doc.expiresAt.getTime() - Date.now();
    return remaining > 0 ? remaining : -2;
  }

  /**
   * 关闭连接
   */
  async close() {
    if (this.client && this.isConnected) {
      await this.client.close();
      this.isConnected = false;
    }
  }
}


module.exports = MongoDBCache;
