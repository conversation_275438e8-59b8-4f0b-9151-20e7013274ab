import React from 'react';
import PropTypes from 'prop-types';
import './index.css';

const QuantityPanel = ({
  imageQuantity,
  onChange,
  min = 1,
  max = 4
}) => {
  // 计算填充宽度的百分比
  const calculateFillWidth = () => {
    const range = max - min;
    const position = imageQuantity - min;
    return position * (100 / range);
  };

  // 生成标记点
  const generateMarks = () => {
    const marks = [];
    for (let i = min; i <= max; i++) {
      marks.push(
        <span key={i} className={imageQuantity >= i ? 'active' : ''}>{i}</span>
      );
    }
    return marks;
  };

  return (
    <div className="quantity-setting">
      <div className="quantity-content">
        <div className="quantity-label">
          <span>生成数量</span>
          <span className="quantity-value">{imageQuantity} 张</span>
        </div>
        <div className="quantity-slider">
          <div className="slider-track">
            <div 
              className="slider-fill" 
              style={{ width: `${calculateFillWidth()}%` }}
            ></div>
            <input 
              type="range" 
              min={min} 
              max={max} 
              value={imageQuantity}
              onChange={(e) => onChange(parseInt(e.target.value))}
              className="slider-input"
            />
          </div>
          <div className="slider-marks">
            {generateMarks()}
          </div>
        </div>
      </div>
    </div>
  );
};

QuantityPanel.propTypes = {
  imageQuantity: PropTypes.number.isRequired,
  onChange: PropTypes.func.isRequired,
  min: PropTypes.number,
  max: PropTypes.number
};

export default QuantityPanel; 