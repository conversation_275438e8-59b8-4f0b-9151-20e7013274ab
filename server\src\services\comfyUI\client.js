const axios = require('axios');
const fs = require('fs');
const path = require('path');
const WebSocket = require('ws');

// 确保上传目录存在并可写
const ensureUploadDir = (userId = 'developer') => {
  // 确保userId有值（现在总是有值，因为提供了默认值）
  const uploadDir = path.resolve(__dirname, `../../../storage/${userId}/uploads`);
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true, mode: 0o755 });
  }
  // 检查目录权限
  try {
    fs.accessSync(uploadDir, fs.constants.W_OK);
  } catch (error) {
    console.error('上传目录权限错误:', error);
    throw new Error('上传目录没有写入权限');
  }
  return uploadDir;
};

class ComfyUIClient {
  constructor(baseURL = process.env.COMFYUI_URL || 'http://htc-ksifabooi78t9jm6i-ewrdtowe-custom.service.onethingrobot.com', userId = 'developer') {
    // 打印环境变量值以调试
    console.log('环境变量配置信息:');
    console.log('- COMFYUI_URL:', process.env.COMFYUI_URL);
    
    // 设置基础URL
    this.baseURL = baseURL;
    console.log('ComfyUI基础URL:', this.baseURL);
    
    // 从环境变量读取API密钥和实例ID
    this.apiKey = process.env.COMFYUI_API_KEY || '';
    this.instanceId = process.env.COMFYUI_INSTANCE_ID || '';
    
    // 打印API密钥和实例ID状态（不打印具体值，保护安全）
    console.log('ComfyUI API密钥状态:', this.apiKey ? '已配置' : '未配置');
    console.log('ComfyUI 实例ID状态:', this.instanceId ? '已配置' : '未配置');
    
    // 配置API路径前缀
    this.apiPathPrefix = process.env.COMFYUI_API_PATH_PREFIX || '';
    console.log('ComfyUI API路径前缀:', this.apiPathPrefix || '(无)');
    
    // 设置WebSocket URL
    this.wsURL = process.env.COMFYUI_WS_URL || this.baseURL;
    console.log('ComfyUI WebSocket URL:', this.wsURL);
    
    // 设置用户ID
    this.userId = userId;
    console.log('用户ID:', this.userId);
    
    // 初始化成功路径格式缓存
    this.lastSuccessfulPathFormat = '/view?filename=${filename}'; // 默认使用已知高成功率格式
    
    this.clientId = null;
    this.uploadDir = ensureUploadDir(userId); // 使用用户特定的上传目录
    
    // 建立存储目录结构
    this.storagePath = path.resolve(__dirname, '../../../storage');
    this.modelPath = path.resolve(this.storagePath, `${userId}/model`);
    this.toolsPath = path.resolve(this.storagePath, `${userId}/tools`);
    
    // 确保上传目录存在
    this.ensureDirectoryExists(this.uploadDir);
    
    // 设置各种页面的专用目录
    // 时尚大片页面目录
    this.fashionDir = path.resolve(this.modelPath, 'fashion');
    this.fashionGeneratedDir = path.resolve(this.fashionDir, 'generated');
    
    // 模特换装页面目录
    this.tryOnDir = path.resolve(this.modelPath, 'try-on');
    this.tryOnMaskDir = path.resolve(this.tryOnDir, 'mask');
    this.tryOnGeneratedDir = path.resolve(this.tryOnDir, 'generated');
    
    // 服装复色页面
    this.recolorGeneratedDir = path.resolve(this.modelPath, 'recolor/generated');
    
    // 换面料页面
    this.fabricGeneratedDir = path.resolve(this.modelPath, 'fabric/generated');
    
    // 换背景页面
    this.backgroundGeneratedDir = path.resolve(this.modelPath, 'background/generated');
    
    // 虚拟模特页面
    this.virtualGeneratedDir = path.resolve(this.modelPath, 'virtual/generated');
    
    // 款式优化页面
    this.stylePath = path.resolve(this.storagePath, `${userId}/style`);
    this.optimizeMaskDir = path.resolve(this.stylePath, 'optimize/mask');
    this.optimizeGeneratedDir = path.resolve(this.stylePath, 'optimize/generated');
    
    // 灵感探索页面
    this.inspirationDir = path.resolve(this.stylePath, 'inspiration');
    this.inspirationGeneratedDir = path.resolve(this.inspirationDir, 'generated');
    
    // 爆款开发页面
    this.trendingDir = path.resolve(this.stylePath, 'trending');
    this.trendingGeneratedDir = path.resolve(this.trendingDir, 'generated');
  
    // 爆款延伸页面
    this.divergentDir = path.resolve(this.stylePath, 'divergent');
    this.divergentGeneratedDir = path.resolve(this.divergentDir, 'generated');

    // 图片取词页面
    this.extractDir = path.resolve(this.toolsPath, 'extract');
    this.extractGeneratedDir = path.resolve(this.extractDir, 'generated');

    // 高清放大页面
    this.upscaleGeneratedDir = path.resolve(this.toolsPath, 'upscale/generated');
    
    // 自动抠图页面
    this.mattingGeneratedDir = path.resolve(this.toolsPath, 'matting/generated');

    // 智能扩图页面
    this.extendGeneratedDir = path.resolve(this.toolsPath, 'extend/generated');
    
    // 确保所有目录存在
    this.ensureDirectoryExists(this.fashionDir);
    this.ensureDirectoryExists(this.fashionGeneratedDir);
    this.ensureDirectoryExists(this.tryOnDir);
    this.ensureDirectoryExists(this.tryOnMaskDir);
    this.ensureDirectoryExists(this.tryOnGeneratedDir);
    this.ensureDirectoryExists(this.recolorGeneratedDir);
    this.ensureDirectoryExists(this.fabricGeneratedDir);
    this.ensureDirectoryExists(this.backgroundGeneratedDir);
    this.ensureDirectoryExists(this.virtualGeneratedDir);
    this.ensureDirectoryExists(this.optimizeMaskDir);
    this.ensureDirectoryExists(this.optimizeGeneratedDir);
    this.ensureDirectoryExists(this.inspirationDir);
    this.ensureDirectoryExists(this.inspirationGeneratedDir);
    this.ensureDirectoryExists(this.trendingDir);
    this.ensureDirectoryExists(this.trendingGeneratedDir);
    this.ensureDirectoryExists(this.divergentDir);
    this.ensureDirectoryExists(this.divergentGeneratedDir);
    this.ensureDirectoryExists(this.extractDir);
    this.ensureDirectoryExists(this.extractGeneratedDir);
    this.ensureDirectoryExists(this.upscaleGeneratedDir);
    this.ensureDirectoryExists(this.mattingGeneratedDir);
    this.ensureDirectoryExists(this.extendGeneratedDir);

    // 确保从环境变量中读取ComfyUI根目录
    this.comfyuiRoot = process.env.COMFYUI_ROOT || '';
    if (!this.comfyuiRoot) {
      console.warn('警告: 未配置COMFYUI_ROOT环境变量');
    } else {
      // 规范化路径分隔符
      this.comfyuiRoot = this.comfyuiRoot.replace(/\\/g, '/');
    }
    
    // 输出配置信息
    console.log('ComfyUI服务地址:', this.baseURL);
    console.log('WebSocket地址:', this.wsURL);
    console.log('上传目录:', this.uploadDir);
    console.log('ComfyUI根目录:', this.comfyuiRoot);

    // 配置输出目录
    this.outputDir = this.comfyuiRoot ? path.join(this.comfyuiRoot, 'output') : '';
    if (this.outputDir) {
      // 规范化路径分隔符
      this.outputDir = this.outputDir.replace(/\\/g, '/');
      console.log('ComfyUI输出目录:', this.outputDir);
      
      // 确保输出目录存在
      if (!fs.existsSync(this.outputDir)) {
        console.log('创建ComfyUI输出目录');
        fs.mkdirSync(this.outputDir, { recursive: true });
      }
      
      // 验证输出目录权限
      try {
        fs.accessSync(this.outputDir, fs.constants.R_OK | fs.constants.W_OK);
        console.log('ComfyUI输出目录权限验证成功');
      } catch (error) {
        console.error('ComfyUI输出目录权限错误:', error);
        throw new Error('ComfyUI输出目录权限配置错误，请确保目录具有读写权限');
      }
      
      // 监控输出目录变化
      fs.watch(this.outputDir, (eventType, filename) => {
        if (eventType === 'rename' && filename) {
          console.log('检测到新文件生成:', filename);
          // 输出文件的完整路径
          const filePath = path.join(this.outputDir, filename);
          if (fs.existsSync(filePath)) {
            console.log('新文件完整路径:', filePath);
            // 获取文件信息
            const stats = fs.statSync(filePath);
            console.log('文件信息:', {
              size: stats.size,
              created: stats.birthtime,
              modified: stats.mtime
            });
          }
        }
      });
    }
    
    // 创建axios实例
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: 60000,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Instance-ID': this.instanceId
      }
    });

    // 添加响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        if (response.headers['content-type']?.includes('text/html')) {
          console.error('收到HTML响应:', response.data.substring(0, 200));
          throw new Error('ComfyUI服务返回了HTML页面');
        }
        return response;
      },
      (error) => {
        console.error('请求失败:', {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          error: error.message
        });
        throw error;
      }
    );
  }

  // 添加辅助方法：确保目录存在并检查权限
  ensureDirectoryExists(dir) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
    }
    
    try {
      fs.accessSync(dir, fs.constants.W_OK);
    } catch (error) {
      console.error('目录权限错误:', error);
      throw new Error(`目录${dir}没有写入权限`);
    }
    
    return dir;
  }
  
  // 获取上传目录
  getUploadDir() {
    return this.uploadDir;
  }

  // 添加方法：根据页面类型获取正确的生成目录
  getTargetGeneratedDir(pageType = 'fashion', imageType = 'clothing') {
    console.log(`获取生成目录: pageType=${pageType}, imageType=${imageType}`);
    
    switch (pageType) {
      case 'fashion':
        console.log('返回fashion生成目录');
        return this.fashionGeneratedDir;
      case 'try-on':
        console.log('返回try-on生成目录');
        return this.tryOnGeneratedDir;
      case 'recolor':
        console.log('返回recolor生成目录');
        return this.recolorGeneratedDir;
      case 'fabric':
        console.log('返回fabric生成目录');
        return this.fabricGeneratedDir;
      case 'background':
        console.log('返回background生成目录');
        return this.backgroundGeneratedDir;
      case 'virtual':
        console.log('返回virtual生成目录');
        return this.virtualGeneratedDir;
      case 'optimize':
        console.log('返回optimize生成目录');
        return this.optimizeGeneratedDir;
      case 'inspiration':
        console.log('返回inspiration生成目录');
        return this.inspirationGeneratedDir;
      case 'trending':
        console.log('返回trending生成目录');
        return this.trendingGeneratedDir;
      case 'divergent':
        console.log('返回divergent生成目录');
        return this.divergentGeneratedDir;
      case 'upscale':
        console.log('返回upscale页面生成目录');
        return this.upscaleGeneratedDir;
      case 'extract':
        console.log('返回extract页面生成目录');
        return this.extractGeneratedDir;
      case 'matting':
        console.log('返回matting生成目录');
        return this.mattingGeneratedDir;
      case 'extend':
        console.log('返回extend生成目录');
        return this.extendGeneratedDir;
      default:
        // 不明确的类型使用fashion生成目录
        console.log('默认返回fashion生成目录');
        return this.fashionGeneratedDir;
    }
  }

  // 添加方法：获取蒙版目录
  getTargetMaskDir(pageType = 'try-on') {
    if (pageType === 'try-on') {
      return this.tryOnMaskDir;
    } else if (pageType === 'optimize') {
      return this.optimizeMaskDir;
    }
    // 其他类型暂不支持蒙版
    return null;
  }

  // 检查服务状态
  async checkService() {
    try {
      console.log('正在检查ComfyUI服务状态...');
      
      // 尝试不同的可能API路径
      const possiblePaths = [
        '/history',        // 标准路径
        '/api/history',    // 可能的API路径1
        '/comfyui/history',// 可能的API路径2
        '/v1/history'      // 可能的API路径3
      ];
      
      let historyResponse = null;
      let successPath = null;
      
      // 依次尝试不同路径
      for (const apiPath of possiblePaths) {
        try {
          console.log(`尝试检查API路径: ${apiPath}`);
          historyResponse = await this.axiosInstance.get(apiPath, {
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'X-Instance-ID': this.instanceId
            },
            timeout: 10000 // 设置更短的超时时间以加速测试
          });
          
          if (historyResponse.status === 200) {
            console.log(`成功访问API路径: ${apiPath}`);
            successPath = apiPath;
            break;
          }
        } catch (error) {
          console.log(`API路径 ${apiPath} 测试失败:`, error.message);
        }
      }
      
      if (!successPath) {
        console.error('所有API路径测试均失败');
        return false;
      }
      
      // 记录成功的API路径前缀，用于后续请求
      this.apiPathPrefix = successPath.replace('/history', '');
      console.log(`设置API路径前缀: "${this.apiPathPrefix}"`);
      
      console.log('ComfyUI服务响应状态:', historyResponse.status);
      
      // 尝试检查可用节点
      try {
        const objectInfoPath = `${this.apiPathPrefix}/object_info`;
        console.log(`尝试获取节点信息: ${objectInfoPath}`);
        const objectInfoResponse = await this.axiosInstance.get(objectInfoPath);
        const availableNodes = objectInfoResponse.data;
        
        console.log('所有可用节点类型:', Object.keys(availableNodes));
        
        // 检查工作流中使用的节点类型
        const requiredNodeTypes = ['LoadImage', 'easy imageRemBg', 'PreviewImage', 'SaveImage'];
        const missingNodeTypes = requiredNodeTypes.filter(nodeType => !availableNodes[nodeType]);
        
        if (missingNodeTypes.length > 0) {
          console.error('缺少工作流所需的节点类型:', missingNodeTypes);
          return false;
        }
        
        // 检查节点的输入参数
        console.log('easy imageRemBg 节点的输入参数:', availableNodes['easy imageRemBg']?.input);
      } catch (error) {
        console.error('获取节点信息失败:', error.message);
        // 继续执行，不中断服务检查
      }
      
      console.log('ComfyUI服务正常运行，API路径已确认');
      return true;
    } catch (error) {
      console.error('ComfyUI服务检查失败:', {
        message: error.message,
        code: error.code,
        response: error.response?.data,
        stack: error.stack
      });
      if (error.code === 'ECONNREFUSED') {
        console.error('无法连接到ComfyUI服务，请确保服务已启动且地址正确');
      }
      return false;
    }
  }

  // 获取结果方法
  async getResult(promptId) {
    try {
      // 使用确认的API路径前缀
      const apiPath = `${this.apiPathPrefix || ''}/history/${promptId}`;
      console.log(`从云实例获取结果，路径: ${apiPath}`);
      
      const response = await this.axiosInstance.get(apiPath, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'X-Instance-ID': this.instanceId
        }
      });
      console.log('云实例响应结果:', response.data);
      return response.data;
    } catch (error) {
      console.error('从云实例获取结果时出错:', error);
      throw new Error(`从云实例获取结果失败: ${error.message}`);
    }
  }
}

module.exports = ComfyUIClient; 