import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd'; // 导入antd的message组件用于显示提示
import './index.css';
import '../../../src/styles/buttons.css'; // 导入按钮样式，包括开关组件
import { MdContentCopy, MdExpandLess, MdExpandMore } from 'react-icons/md';
import { FaDice } from 'react-icons/fa';

const RandomSeedSelector = ({
  onRandomChange,
  onSeedChange,
  defaultRandom = true,
  defaultSeed = 42,
  // 编辑模式下，isEdit应为true且传入历史种子值
  isEdit = false,
  // 历史种子值，只在编辑模式下使用
  editSeed = null,
}) => {
  // 只用Math.random()生成0~Number.MAX_SAFE_INTEGER之间的等概率随机整数
  const generateRandomSeed = () => {
    return Math.floor(Math.random() * (Number.MAX_SAFE_INTEGER + 1)).toString();
  };

  // 比较两个字符串数字的大小
  const compareStrings = (str1, str2) => {
    if (str1.length !== str2.length) {
      return str1.length - str2.length;
    }
    return str1.localeCompare(str2);
  };

  // 前端UI状态
  const [isRandom, setIsRandom] = useState(defaultRandom);
  const [seed, setSeed] = useState(isEdit && editSeed !== null ? editSeed : defaultSeed);
  const [copied, setCopied] = useState(false);
  const [activeInput, setActiveInput] = useState(null);
  
  // 用于跟踪是否是初始化
  const isInitialMount = useRef(true);

  // 组件初始化时，确保有默认设置
  useEffect(() => {
    if (defaultRandom !== isRandom) {
      setIsRandom(defaultRandom);
    }
    
    // 只在编辑模式下使用历史种子
    if (isEdit && editSeed !== null) {
      setSeed(editSeed);
    } else if (defaultSeed !== seed) {
      setSeed(defaultSeed);
    }
  }, [defaultRandom, defaultSeed, isEdit, editSeed]);

  // 当 defaultRandom 改变时，强制重新生成随机数
  // useEffect(() => {
  //   // 初始化时不执行，只在后续 defaultRandom 改变时执行
  //   if (isInitialMount.current) {
  //     isInitialMount.current = false;
  //     return;
  //   }
    
  //   console.log('RandomSeedSelector: defaultRandom 改变，重新生成随机数', {
  //     defaultRandom,
  //     currentSeed: seed
  //   });
    
  //   // 重新生成随机数并重置状态
  //   const newSeed = generateRandomSeed();
  //   setIsRandom(defaultRandom);
  //   setSeed(newSeed);
    
  //   // 通知父组件新的种子值
  //   if (onSeedChange) {
  //     onSeedChange(newSeed);
  //   }
    
  //   console.log('RandomSeedSelector: 强制重置完成', {
  //     isRandom: defaultRandom,
  //     newSeed: newSeed
  //   });
  // }, [defaultRandom]);

  // 切换随机/固定种子
  const handleRandomToggle = () => {
    const newValue = !isRandom;
    console.log('RandomSeedSelector: 开关切换', {
      from: isRandom,
      to: newValue,
      currentSeed: seed
    });
    
    setIsRandom(newValue);
    
    // 每次切换开关时都重新生成随机数，无论切换到哪个状态
    const newSeed = generateRandomSeed();
    setSeed(newSeed);
    
    console.log('RandomSeedSelector: 状态重置完成', {
      isRandom: newValue,
      newSeed: newSeed
    });
    
    // 通知父组件状态变化
    if (onRandomChange) {
      onRandomChange(newValue);
    }
    
    // 通知父组件新的种子值
    if (onSeedChange) {
      onSeedChange(newSeed);
    }
  };

  const handleSeedChange = (e) => {
    // 只允许输入数字
    let value = e.target.value.replace(/[^0-9]/g, '');
    const maxValue = "18446744073709552000";
    // 如果输入为空，设置为0
    if (value === '') {
      value = '0';
    }
    // 检查是否超过最大值
    if (
      value.length > maxValue.length ||
      (value.length === maxValue.length && value > maxValue)
    ) {
      value = maxValue;
    }
    setSeed(value);
    if (onSeedChange) {
      onSeedChange(value);
    }
  };
  
  // 字符串加法函数
  const addStrings = (str1, str2) => {
    let result = '';
    let carry = 0;
    let i = str1.length - 1;
    let j = str2.length - 1;

    while (i >= 0 || j >= 0 || carry > 0) {
      const digit1 = i >= 0 ? parseInt(str1[i]) : 0;
      const digit2 = j >= 0 ? parseInt(str2[j]) : 0;
      const sum = digit1 + digit2 + carry;
      carry = Math.floor(sum / 10);
      result = (sum % 10) + result;
      i--;
      j--;
    }
    return result;
  };

  // 字符串减法函数
  const subtractStrings = (str1, str2) => {
    if (str1 === str2) return '0';
    if (str1 === '0') return '0';
    if (str1 === '1') return '0';

    let result = '';
    let borrow = 0;
    let i = str1.length - 1;
    let j = str2.length - 1;

    while (i >= 0) {
      const digit1 = parseInt(str1[i]);
      const digit2 = j >= 0 ? parseInt(str2[j]) : 0;
      let diff = digit1 - digit2 - borrow;

      if (diff < 0) {
        diff += 10;
        borrow = 1;
      } else {
        borrow = 0;
      }

      result = diff + result;
      i--;
      j--;
    }

    // 移除前导零
    result = result.replace(/^0+/, '');
    return result || '0';
  };

  // 调节数值的函数
  const adjustSeedValue = (delta) => {
    const currentValue = seed.toString();
    const maxValue = "18446744073709552000";
    
    if (delta < 0 && currentValue !== '0') {
      // 减1
      const newValue = subtractStrings(currentValue, '1');
      setSeed(newValue);
      if (onSeedChange) onSeedChange(newValue);
    } else if (delta > 0) {
      // 加1
      // 判断是否已到最大值
      if (
        currentValue.length > maxValue.length ||
        (currentValue.length === maxValue.length && currentValue >= maxValue)
      ) {
        setSeed(maxValue);
        if (onSeedChange) onSeedChange(maxValue);
        return;
      }
      const newValue = addStrings(currentValue, '1');
      // 加1后不能超过最大值
      if (
        newValue.length > maxValue.length ||
        (newValue.length === maxValue.length && newValue > maxValue)
      ) {
        setSeed(maxValue);
        if (onSeedChange) onSeedChange(maxValue);
      } else {
        setSeed(newValue);
        if (onSeedChange) onSeedChange(newValue);
      }
    }
  };

  const handleCopySeed = () => {
    navigator.clipboard.writeText(seed.toString());
    message.success('已复制种子值');
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleRegenerateSeed = () => {
    const newSeed = generateRandomSeed();
    setSeed(newSeed);
    if (onSeedChange) {
      onSeedChange(newSeed);
    }
  };

  return (
    <div className="random-seed-selector">
      <div className="selector-content">
        <div className="selector-label">
          <span>随机种子</span>
        </div>
        <div className="selector-area">
          <div className="component-text">
            <h3>
              {isRandom ? "随机种子" : "固定种子"}
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={isRandom}
                  onChange={handleRandomToggle}
                />
                <span className="toggle-track"></span>
              </label>
            </h3>
            <div className="component-content">
              {isRandom ? (
                <p>
                  每次使用不同的种子值产生更多变化
                </p>
              ) : (
                <div className="seed-input-container">
                  <span className="seed-label">种子值</span>
                  <div 
                    className="seed-input-wrapper"
                    onMouseEnter={() => setActiveInput('seed')}
                    onMouseLeave={() => setActiveInput(null)}
                  >
                    <input 
                      type="text" 
                      className="seed-input" 
                      value={seed}
                      onChange={handleSeedChange}
                      disabled={false}
                      maxLength={20}
                      pattern="[0-9]*"
                      inputMode="numeric"
                    />
                    {activeInput === 'seed' && (
                      <div className="number-controls">
                        <button 
                          className="number-control-btn" 
                          onClick={() => adjustSeedValue(1)}
                        >
                          <MdExpandLess />
                        </button>
                        <button 
                          className="number-control-btn" 
                          onClick={() => adjustSeedValue(-1)}
                        >
                          <MdExpandMore />
                        </button>
                      </div>
                    )}
                  </div>
                  <button 
                    className="copy-seed-btn"
                    onClick={handleCopySeed}
                    title={copied ? "已复制" : "复制种子"}
                  >
                    <MdContentCopy />
                  </button>
                  <button 
                    className="dice-seed-btn"
                    onClick={handleRegenerateSeed}
                    title="重新生成种子"
                  >
                    <FaDice />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

RandomSeedSelector.propTypes = {
  onRandomChange: PropTypes.func,
  onSeedChange: PropTypes.func,
  defaultRandom: PropTypes.bool,
  defaultSeed: PropTypes.number,
  isEdit: PropTypes.bool,
  editSeed: PropTypes.number,
};

export default RandomSeedSelector; 