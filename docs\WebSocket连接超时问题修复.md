# WebSocket连接超时问题修复

## 问题描述

从日志可以看到RunningHub WebSocket连接出现超时问题：
```
为任务 1949676822069383170 创建RunningHub连接失败: timed out during opening handshake
无法连接到RunningHub实例: wss://www.runninghub.cn:443/ws/c_instance?...
```

## 问题分析

### 原有代码问题：
1. **没有设置连接超时时间** - 默认超时可能太短或太长
2. **没有设置ping/pong参数** - 连接可能因为网络问题断开
3. **错误处理不够详细** - 无法准确诊断连接失败原因
4. **没有URL格式验证** - 可能传入了格式错误的URL

### 网络环境因素：
- RunningHub服务器可能在海外，网络延迟较高
- 防火墙或代理可能阻止WebSocket连接
- SSL证书验证问题（wss://协议）

## 修复内容

### 1. 增强连接参数配置

```python
# 设置连接参数
connect_timeout = 30  # 30秒连接超时（增加到30秒）
ping_interval = 20    # 20秒ping间隔
ping_timeout = 10     # 10秒ping超时

# 创建连接，增加超时和ping设置
runninghub_ws = await asyncio.wait_for(
    websockets.connect(
        netWssUrl,
        ping_interval=ping_interval,
        ping_timeout=ping_timeout,
        close_timeout=10,
        max_size=2**23,  # 8MB max message size
        compression=None  # 禁用压缩以提高性能
    ),
    timeout=connect_timeout
)
```

### 2. 添加URL格式验证

```python
def validate_websocket_url(self, url, platform="WebSocket"):
    """验证WebSocket URL格式"""
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        
        # 检查协议
        if parsed.scheme not in ['ws', 'wss']:
            print(f"❌ {platform} URL协议错误: {parsed.scheme}")
            return False
            
        # 检查主机名
        if not parsed.hostname:
            print(f"❌ {platform} URL缺少主机名")
            return False
            
        # 检查端口范围
        if parsed.port and (parsed.port < 1 or parsed.port > 65535):
            print(f"❌ {platform} URL端口无效: {parsed.port}")
            return False
            
        return True
    except Exception as e:
        print(f"❌ {platform} URL验证失败: {e}")
        return False
```

### 3. 详细的错误处理

```python
except asyncio.TimeoutError:
    print(f"❌ RunningHub连接超时 (task_id={task_id})")
    print(f"   URL: {netWssUrl[:100]}...")
    return None
except websockets.exceptions.InvalidURI as e:
    print(f"❌ RunningHub URL格式错误 (task_id={task_id}): {e}")
    return None
except websockets.exceptions.WebSocketException as e:
    print(f"❌ RunningHub WebSocket错误 (task_id={task_id}): {e}")
    return None
except Exception as e:
    print(f"❌ RunningHub连接失败 (task_id={task_id}): {e}")
    print(f"   错误类型: {type(e).__name__}")
    return None
```

### 4. 改进的日志输出

```python
print(f"正在连接到RunningHub实例: {netWssUrl[:100]}...")
print(f"✅ 成功为任务 {task_id} 创建RunningHub连接")
print(f"🔄 复用现有RunningHub连接 (task_id={task_id})")
```

## 修复后的连接流程

### 1. URL验证阶段
```
✅ RunningHub URL格式验证通过
   协议: wss
   主机: www.runninghub.cn
   端口: 443
```

### 2. 连接建立阶段
```
正在连接到RunningHub实例: wss://www.runninghub.cn:443/ws/c_instance?c_host=**********&c_port=81...
✅ 成功为任务 1949676822069383170 创建RunningHub连接
```

### 3. 连接复用阶段
```
🔄 复用现有RunningHub连接 (task_id=1949676822069383170)
```

## 故障排除指南

### 如果仍然出现连接超时：

#### **1. 检查网络连接**
```bash
# 测试域名解析
nslookup www.runninghub.cn

# 测试端口连通性
telnet www.runninghub.cn 443
```

#### **2. 检查防火墙设置**
- 确保允许出站HTTPS连接（端口443）
- 检查是否有代理服务器阻止WebSocket连接

#### **3. 检查SSL证书**
```bash
# 检查SSL证书
openssl s_client -connect www.runninghub.cn:443 -servername www.runninghub.cn
```

#### **4. 增加连接超时时间**
如果网络环境较差，可以临时增加超时时间：
```python
connect_timeout = 60  # 增加到60秒
```

#### **5. 使用代理连接**
如果需要通过代理连接：
```python
# 在websockets.connect中添加代理参数
proxy_uri = "http://proxy.example.com:8080"
runninghub_ws = await websockets.connect(
    netWssUrl,
    proxy=proxy_uri,
    # ... 其他参数
)
```

### 常见错误类型及解决方案：

#### **TimeoutError**
- **原因**: 网络延迟高或服务器响应慢
- **解决**: 增加`connect_timeout`时间

#### **InvalidURI**
- **原因**: URL格式错误
- **解决**: 检查URL编码和格式

#### **ConnectionRefused**
- **原因**: 服务器拒绝连接或端口未开放
- **解决**: 检查服务器状态和端口配置

#### **SSLError**
- **原因**: SSL证书验证失败
- **解决**: 检查证书有效性或禁用SSL验证（仅测试环境）

## 测试验证

### 预期的成功日志：
```
收到订阅消息: {'type': 'subscribe_task', 'task_id': '1949676822069383170', 'platform': 'runninghub', ...}
订阅任务: task_id=1949676822069383170, platform=runninghub
✅ RunningHub URL格式验证通过
   协议: wss
   主机: www.runninghub.cn
   端口: 443
正在连接到RunningHub实例: wss://www.runninghub.cn:443/ws/c_instance?c_host=**********...
✅ 成功为任务 1949676822069383170 创建RunningHub连接
开始监听RunningHub实例: wss://www.runninghub.cn:443/ws/c_instance?... (task_id=1949676822069383170)
从RunningHub收到消息 (task_id=1949676822069383170): {'type': 'progress', ...}
```

### 如果仍然失败的调试步骤：
1. **检查URL完整性** - 确保netWssUrl包含所有必要参数
2. **测试简化URL** - 尝试连接到基础WebSocket端点
3. **检查认证参数** - 验证Rh-Comfy-Auth等认证信息是否有效
4. **联系RunningHub支持** - 确认服务器端WebSocket服务状态

通过这些改进，WebSocket连接应该更加稳定和可靠。🔧
