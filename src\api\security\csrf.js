/**
 * CSRF防护工具类
 * 提供CSRF令牌的生成、验证和刷新功能
 */

// 存储令牌的键名
const CSRF_TOKEN_KEY = 'csrf_token';

/**
 * 生成随机令牌
 * @returns {string} 生成的随机令牌
 */
const generateToken = () => {
  // 生成随机字符串
  const randomString = Math.random().toString(36).substring(2, 15) + 
                       Math.random().toString(36).substring(2, 15);
  
  // 添加时间戳，增加唯一性
  return `${randomString}_${Date.now()}`;
};

/**
 * 获取当前的CSRF令牌
 * 如果不存在则生成新的令牌
 * @returns {string} CSRF令牌
 */
export const getCSRFToken = () => {
  let token = localStorage.getItem(CSRF_TOKEN_KEY);
  
  // 如果令牌不存在，生成新令牌
  if (!token) {
    token = generateToken();
    localStorage.setItem(CSRF_TOKEN_KEY, token);
  }
  
  return token;
};

/**
 * 验证CSRF令牌是否有效
 * @param {string} token - 要验证的令牌
 * @returns {boolean} 令牌是否有效
 */
export const validateCSRFToken = (token) => {
  const storedToken = localStorage.getItem(CSRF_TOKEN_KEY);
  return storedToken === token;
};

/**
 * 刷新CSRF令牌
 * @returns {string} 新生成的令牌
 */
export const refreshCSRFToken = () => {
  const newToken = generateToken();
  localStorage.setItem(CSRF_TOKEN_KEY, newToken);
  return newToken;
};

/**
 * 清除CSRF令牌
 */
export const clearCSRFToken = () => {
  localStorage.removeItem(CSRF_TOKEN_KEY);
};

export default {
  getCSRFToken,
  validateCSRFToken,
  refreshCSRFToken,
  clearCSRFToken
}; 