const mongoose = require('mongoose');
const User = require('../modules/auth/user.model');
require('dotenv').config();

async function createAdminUser() {
  try {
    console.log('开始创建管理员用户...');
    console.log('连接到数据库:', process.env.MONGODB_URI);
    
    // 连接数据库
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('数据库连接成功');

    // 检查是否已存在管理员用户
    const existingAdmin = await User.findOne({ role: 'admin' });
    if (existingAdmin) {
      console.log('管理员用户已存在:', existingAdmin.username);
      console.log('重置管理员密码...');
      
      // 更新密码
      existingAdmin.password = 'Admin@123';
      await existingAdmin.save();
      
      console.log('管理员密码已重置为: Admin@123');
      await mongoose.connection.close();
      process.exit(0);
    }

    // 创建管理员用户
    const adminUser = new User({
      username: 'admin',
      phone: '13800138000',
      password: 'Admin@123',
      name: '系统管理员',
      role: 'admin',
      status: 'active'
    });

    // 保存到数据库
    await adminUser.save();
    console.log('管理员用户创建成功');
    console.log('用户名: admin');
    console.log('密码: Admin@123');
    console.log('角色: admin');
    
    await mongoose.connection.close();
    process.exit(0);
  } catch (error) {
    console.error('创建管理员用户失败:', error);
    await mongoose.connection.close();
    process.exit(1);
  }
}

createAdminUser(); 