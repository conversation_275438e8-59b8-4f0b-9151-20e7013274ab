# RunningHub 数据转换和异步处理适配

## 概述

本文档说明了如何正确处理RunningHub平台返回的信息，以及如何适配新的异步处理机制。

## 1. 数据转换机制

### 1.1 输入参数转换

#### **ComfyUI格式 → RunningHub格式**

**输入示例：**
```json
{
  "53": {
    "url": "https://file.aibikini.cn/images//1753623365678-ac0542372257dfe2d63e373573cbd159.jpg"
  },
  "subInfo": {
    "type": "matting.tab1",
    "title": "自动抠图",
    "count": 1
  },
  "token": "a29a2031dcaa43123e78eb45f22c420d"
}
```

**转换后的RunningHub格式：**
```json
{
  "nodeInfoList": [
    {
      "nodeId": "53",
      "fieldName": "url",
      "fieldValue": "https://file.aibikini.cn/images//1753623365678-ac0542372257dfe2d63e373573cbd159.jpg"
    }
  ]
}
```

#### **转换逻辑：**
```javascript
function convertComfyUIToRunningHub(params, workflowName) {
  const nodeInfoList = [];
  
  // 遍历参数，排除系统参数
  Object.keys(params).forEach(key => {
    if (!['token', 'subInfo', 'forcePlatform'].includes(key)) {
      const value = params[key];
      
      // 如果值是对象，遍历其属性
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        Object.keys(value).forEach(fieldName => {
          nodeInfoList.push({
            nodeId: key,
            fieldName: fieldName,
            fieldValue: value[fieldName]
          });
        });
      } else {
        // 如果值是基本类型，直接作为fieldValue
        nodeInfoList.push({
          nodeId: key,
          fieldName: 'value',
          fieldValue: value
        });
      }
    }
  });
  
  return { nodeInfoList };
}
```

### 1.2 复杂参数转换示例

#### **多节点参数：**
```json
// 输入
{
  "53": {
    "url": "https://example.com/image1.jpg",
    "width": 512,
    "height": 512
  },
  "54": {
    "prompt": "a beautiful landscape",
    "steps": 20
  }
}

// 转换后
{
  "nodeInfoList": [
    {
      "nodeId": "53",
      "fieldName": "url",
      "fieldValue": "https://example.com/image1.jpg"
    },
    {
      "nodeId": "53",
      "fieldName": "width",
      "fieldValue": 512
    },
    {
      "nodeId": "53",
      "fieldName": "height",
      "fieldValue": 512
    },
    {
      "nodeId": "54",
      "fieldName": "prompt",
      "fieldValue": "a beautiful landscape"
    },
    {
      "nodeId": "54",
      "fieldName": "steps",
      "fieldValue": 20
    }
  ]
}
```

## 2. RunningHub返回信息处理

### 2.1 任务创建返回格式

**RunningHub API返回：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "netWssUrl": "wss://www.runninghub.cn:443/ws/c_instance?c_host=***************&c_port=87&clientId=88f6947dc37e94b20654a5c5e67fce69&workflowId=1948587667540836353&Rh-Comfy-Auth=eyJ1c2VySWQiOiI2NTRmNWY1OTU2YTM1MWE0OWY4ZjUyYjQ5Y2E3NmI4ZiIsInNpZ25FeHBpcmUiOjE3NTQyMjg2MTExNTMsInRzIjoxNzUzNjIzODExMTUzLCJzaWduIjoiZmU3YThkNWIxMGM2NmVmMDNhZjliYjg4NGFkZWQwN2YifQ%3D%3D&target=http://myfj.runninghub.cn",
    "taskId": "1949465676051324929",
    "clientId": "88f6947dc37e94b20654a5c5e67fce69",
    "taskStatus": "RUNNING",
    "promptTips": "{\"result\": true, \"error\": null, \"outputs_to_execute\": [\"62\", \"37\"], \"node_errors\": {}}"
  }
}
```

### 2.2 处理逻辑

#### **解析promptTips：**
```javascript
let promptTipsData = null;
try {
  if (response.data.data.promptTips) {
    promptTipsData = JSON.parse(response.data.data.promptTips);
  }
} catch (error) {
  console.warn('解析promptTips失败:', error);
}
```

#### **返回结构化信息：**
```javascript
return {
  success: true,
  data: response.data.data,
  taskId: response.data.data.taskId,
  taskStatus: response.data.data.taskStatus,
  netWssUrl: response.data.data.netWssUrl,
  clientId: response.data.data.clientId,
  promptTips: response.data.data.promptTips,
  promptTipsData: promptTipsData,
  platform: 'runninghub',
  url: 'https://www.runninghub.cn',
  runningHubService: this
};
```

## 3. 异步处理适配

### 3.1 任务状态映射

#### **RunningHub状态 → 统一状态：**
```javascript
const statusMapping = {
  'RUNNING': '执行中',
  'QUEUED': '排队中',
  'PENDING': '等待中',
  'SUCCESS': '成功',
  'COMPLETED': '完成',
  'FAILED': '失败',
  'ERROR': '错误'
};
```

### 3.2 异步处理流程

#### **1. 任务创建阶段：**
```javascript
// 创建RunningHub任务
const createResult = await runningHubService.createAdvancedTask({
  apiKey: config.getFullApiKey(),
  workflowId: runningHubWorkflowId,
  addMetadata: false,
  ...runningHubParams
});

// 返回执行结果，包含异步处理所需信息
return {
  success: true,
  platform: 'runninghub',
  promptId: runningHubTaskId,
  taskStatus: createResult.taskStatus,
  netWssUrl: createResult.netWssUrl,
  clientId: createResult.clientId,
  promptTipsData: createResult.promptTipsData,
  runningHubService: runningHubService
};
```

#### **2. 异步结果处理阶段：**
```javascript
async processRunningHubResult(runningHubTaskId, workflowName, taskId, executeResult) {
  // 检查初始状态
  let currentStatus = executeResult.taskStatus || 'UNKNOWN';
  
  // 验证promptTips
  if (executeResult.promptTipsData) {
    const promptData = executeResult.promptTipsData;
    if (!promptData.result) {
      throw new Error(`任务创建失败: ${promptData.error || '未知错误'}`);
    }
  }

  // 根据状态处理
  if (currentStatus === 'RUNNING' || currentStatus === 'QUEUED') {
    // 轮询等待完成
    const result = await runningHubService.waitForTaskCompletion(runningHubTaskId);
    const resultsResponse = await runningHubService.getTaskResults(runningHubTaskId);
    return this.convertRunningHubResult(resultsResponse, workflowName);
  } else if (currentStatus === 'SUCCESS') {
    // 直接获取结果
    const resultsResponse = await runningHubService.getTaskResults(runningHubTaskId);
    return this.convertRunningHubResult(resultsResponse, workflowName);
  }
}
```

### 3.3 结果转换

#### **RunningHub结果 → 统一格式：**
```javascript
convertRunningHubResult(runningHubResult, workflowName) {
  const result = {
    success: true,
    data: {
      outputs: {},
      platform: 'runninghub',
      taskId: runningHubResult.taskId
    }
  };

  const resultData = runningHubResult.data || runningHubResult.results || [];
  
  // 根据工作流类型转换
  if (workflowName.includes('matting')) {
    // 抠图结果
    result.data.outputs.images = resultData.map((item, index) => ({
      filename: item.filename || `result_${index}.png`,
      url: item.url || item.fileUrl,
      ossUrl: item.url || item.fileUrl,
      type: item.fileType || 'image',
      imageIndex: index,
      fileSize: item.fileSize
    }));
  }
  
  return result;
}
```

## 4. 错误处理机制

### 4.1 promptTips错误检查

```javascript
if (executeResult.promptTipsData) {
  const promptData = executeResult.promptTipsData;
  if (!promptData.result) {
    throw new Error(`RunningHub任务创建失败: ${promptData.error || '未知错误'}`);
  }
  
  // 记录输出节点信息
  if (promptData.outputs_to_execute) {
    console.log(`任务输出节点: ${promptData.outputs_to_execute.join(', ')}`);
  }
}
```

### 4.2 状态检查和重试

```javascript
// 未知状态处理
if (currentStatus === 'UNKNOWN') {
  console.log(`未知任务状态，查询最新状态...`);
  
  const statusResult = await runningHubService.getTaskStatus(runningHubTaskId);
  if (statusResult.success) {
    // 递归调用，使用最新状态
    const updatedExecuteResult = {
      ...executeResult,
      taskStatus: statusResult.status
    };
    return this.processRunningHubResult(runningHubTaskId, workflowName, taskId, updatedExecuteResult);
  }
}
```

## 5. 测试验证

### 5.1 参数转换测试

```javascript
// 测试输入
const testInput = {
  "53": {
    "url": "https://file.aibikini.cn/images/test.jpg"
  },
  "subInfo": {
    "type": "matting.tab1",
    "title": "自动抠图",
    "count": 1
  }
};

// 预期输出
const expectedOutput = {
  "nodeInfoList": [
    {
      "nodeId": "53",
      "fieldName": "url",
      "fieldValue": "https://file.aibikini.cn/images/test.jpg"
    }
  ]
};
```

### 5.2 异步处理测试

```javascript
// 模拟RunningHub返回
const mockResponse = {
  code: 0,
  msg: "success",
  data: {
    taskId: "1949465676051324929",
    taskStatus: "RUNNING",
    netWssUrl: "wss://www.runninghub.cn:443/ws/...",
    clientId: "88f6947dc37e94b20654a5c5e67fce69",
    promptTips: '{"result": true, "outputs_to_execute": ["62", "37"]}'
  }
};

// 验证处理逻辑
const executeResult = await unifiedWorkflowService.executeOnRunningHub(...);
expect(executeResult.taskStatus).toBe('RUNNING');
expect(executeResult.promptTipsData.result).toBe(true);
```

## 6. 总结

### 6.1 主要改进

1. **参数转换**：实现了ComfyUI格式到RunningHub `nodeInfoList`格式的完整转换
2. **返回信息处理**：正确解析和处理RunningHub返回的所有信息
3. **异步处理适配**：适配了新的任务状态格式和处理流程
4. **错误处理**：增强了错误检查和恢复机制

### 6.2 关键特性

- ✅ 支持复杂参数结构转换
- ✅ 正确处理RunningHub任务状态
- ✅ 解析promptTips中的执行信息
- ✅ 统一的异步处理接口
- ✅ 健壮的错误处理机制

### 6.3 兼容性

- ✅ 保持与现有ComfyUI处理逻辑的兼容
- ✅ 统一的结果格式输出
- ✅ 透明的平台切换机制

通过这些改进，系统现在能够正确处理RunningHub平台的数据转换和异步处理，提供与ComfyUI一致的用户体验。🎉
