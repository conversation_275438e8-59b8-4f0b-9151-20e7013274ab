# WebSocket 修复说明

## 问题描述

1. **WebSocket URL格式错误**：`714-kqbckjrsinn1281ff-8wqdoowe-custom.service.onethingrobot.com` 缺少协议前缀
2. **重复订阅和取消订阅**：同一个任务被重复订阅，导致无限循环
3. **任务完成后仍尝试连接**：已完成的任务仍然尝试连接ComfyUI实例

## 修复内容

### 1. WebSocket URL格式修复

在 `create_comfy_connection` 方法中添加了URL格式检查和修正：

```python
# 确保URL有正确的协议前缀
if not instance_ws_url.startswith(('ws://', 'wss://')):
    if instance_ws_url.startswith('http://'):
        instance_ws_url = instance_ws_url.replace('http://', 'ws://')
    elif instance_ws_url.startswith('https://'):
        instance_ws_url = instance_ws_url.replace('https://', 'wss://')
    else:
        instance_ws_url = f"ws://{instance_ws_url}"

# 确保URL以/ws结尾
if not instance_ws_url.endswith('/ws'):
    instance_ws_url = f"{instance_ws_url}/ws"
```

### 2. 任务状态缓存机制

添加了任务状态缓存，避免重复处理已完成的任务：

```python
# 任务状态缓存，避免重复处理已完成的任务
self.completed_task_cache = {}  # { task_id: { status, timestamp } }
```

### 3. 重复订阅防护

在 `subscribe_task` 方法中添加了多重检查：

- 缓存检查：检查任务是否在缓存中标记为已完成
- 状态检查：检查任务监听器中的状态
- 直接返回：如果任务已完成，直接发送完成消息给前端

### 4. 超时机制

添加了超时机制，防止无限等待：

```python
# 设置超时时间（5分钟）
timeout = time.time() + 300

# 60秒消息接收超时
message = await asyncio.wait_for(comfy_ws.recv(), timeout=60)
```

### 5. 自动清理机制

- 每60秒清理断开的连接
- 每小时清理过期的任务缓存
- 5分钟后清理完成的任务监听器

## 新增API端点

### 1. WebSocket统计信息
```
GET /api/websocket/stats
```

### 2. 任务状态查询
```
GET /api/task/status
GET /api/task/status?task_id=<task_id>
```

## 使用示例

### 测试修复
```bash
cd scripts
python test_fix.py
```

### 查看任务状态
```bash
curl http://localhost:5000/api/task/status
curl http://localhost:5000/api/task/status?task_id=your_task_id
```

### 查看WebSocket统计
```bash
curl http://localhost:5000/api/websocket/stats
```

## 日志输出示例

修复后的正常日志输出：
```
订阅任务 (connection_id=frontend_1751287013897_b7s8vgw8b, task_id=01175128743180131)
创建新的ComfyUI连接: ws://714-kqbckjrsinn1281ff-8wqdoowe-custom.service.onethingrobot.com/ws
开始监听ComfyUI实例: ws://714-kqbckjrsinn1281ff-8wqdoowe-custom.service.onethingrobot.com/ws (task_id=01175128743180131)
任务 01175128743180131 已完成
已发送完成消息给任务 01175128743180131
缓存已完成任务: 01175128743180131
关闭任务监听器 (task_id=01175128743180131)
```

## 注意事项

1. **URL格式**：现在会自动修正URL格式，添加协议前缀和/ws后缀
2. **任务缓存**：已完成的任务会被缓存24小时，避免重复处理
3. **超时处理**：添加了多层超时机制，防止无限等待
4. **错误处理**：完善的错误处理和日志记录
5. **资源清理**：自动清理断开的连接和过期的缓存

## 预期效果

修复后应该看到：
- 不再出现 "isn't a valid URI" 错误
- 不再出现重复订阅和取消订阅的循环
- 已完成的任务会立即返回完成消息
- 系统资源使用更加合理 