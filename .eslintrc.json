{"extends": ["react-app", "react-app/jest"], "rules": {"import/first": "off", "import/order": ["warn", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "no-unused-vars": "warn", "no-var": "error", "prefer-const": "warn", "react/jsx-uses-react": "error", "react/jsx-uses-vars": "error", "react/prop-types": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "no-multiple-empty-lines": ["warn", {"max": 1, "maxEOF": 0}], "no-trailing-spaces": "warn", "semi": ["warn", "always"], "quotes": ["warn", "single"], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off"}}