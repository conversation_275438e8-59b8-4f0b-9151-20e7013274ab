/* 导入统一样式 */
@import '../../styles/theme.css';
@import '../../styles/buttons.css';
@import '../../styles/inputs.css';
@import '../../styles/panels.css';

/* 复用面板组件的通用样式 */
.panel-component {
  margin-bottom: 10px;
}

/* 信息按钮样式 */
.info-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 0;
}

.info-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 信息图标样式 */
.info-icon {
  width: 16px;
  height: 16px;
  color: #666;
  opacity: 0.7;
}

.info-btn:hover .info-icon {
  opacity: 1;
  color: #1890ff;
}

/* 文本描述面板样式 - 使用统一的面板结构 */
.text-description-setting {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  /* 高度自动适应内容 */
  min-height: 150px;
  max-height: 380px;
  display: flex;
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  position: relative; /* 为提示按钮的绝对定位提供定位上下文 */
}

.text-description-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  gap: var(--spacing-sm);
}

.text-description-label {
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
  height: 100%;
}

.text-description-label span {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

/* 文本描述预览图片样式 */
.selected-description-preview {
  width: 88px;
  height: 100%; /* 保持动态高度，跟随组件高度 */
  border-radius: 0;
  overflow: hidden;
  flex-shrink: 0;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-light);
  position: relative; /* 为绝对定位的图片提供定位上下文 */
}

.selected-description-thumbnail {
  width: 100%;
  height: 380px; /* 图片高度固定为最大高度 */
  object-fit: cover;
  object-position: top center;
  position: absolute; /* 绝对定位 */
  top: 0;
  left: 0;
}

/* 输入框容器样式 */
.text-description-area {
  flex: 1;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: visible;
}

/* 确保component-content有正确的定位 */
.text-description-setting .component-content {
  position: relative; /* 为拖动按钮提供定位上下文 */
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0px 16px 16px 10px; /* 减少上边距和左内边距，与标题对齐 */
}

/* 输入框样式 */
.text-description-input {
  width: 100%;
  /* 高度由JS动态设置 */
  min-height: 120px;
  max-height: 300px;
  padding: 15px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: var(--transition-normal);
  resize: none;
  line-height: 1.5;
  box-sizing: border-box; /* 确保内边距不影响宽度计算 */
  margin: 0; /* 移除margin，因为父容器已经有padding */
}

.text-description-input:hover {
  border-color: var(--brand-primary);
}

.text-description-input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

.text-description-input::placeholder {
  color: var(--text-tertiary);
}

/* 适配暗色主题 */
[data-theme="dark"] .text-description-input {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .text-description-input:hover {
  border-color: var(--brand-primary);
}

[data-theme="dark"] .text-description-input:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(255, 60, 106, 0.15);
}

/* 覆盖全局拖动按钮样式 */
.component-content .textarea-resize-handle {
  position: absolute !important;
  bottom: 20px !important;
  right: 20px !important; /* 默认位置，与JS代码保持一致 */
  z-index: 5 !important; /* 降低z-index，确保不会显示在导航栏之上 */
  transition: var(--transition-normal) !important;
  opacity: 0.7;
  width: 12px !important;
  height: 12px !important;
  cursor: se-resize !important;
}

.component-content .textarea-resize-handle:hover {
  opacity: 1;
}
