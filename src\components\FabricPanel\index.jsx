import React from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

/**
 * 面料面板组件 - 用于展示面料图片和状态
 * 
 * 此组件从ModelMaskPanel组件中抽取，专门用于显示面料图片
 * 与ModelMaskPanel共用样式，但专门处理面料图片相关的逻辑
 */
const FabricPanel = ({
  panel,
  onExpandClick,
  onDelete,
  onReupload,
  onStatusChange,
  isActive,
  onPanelsChange,
  pageType = 'fabric' // 默认为换面料页面
}) => {
  const handleExpandClick = (e) => {
    const buttonRect = e.currentTarget.getBoundingClientRect();
    onExpandClick(panel, {
      top: buttonRect.top,
      left: buttonRect.left + buttonRect.width
    });
  };

  const handleDelete = () => {
    if (panel && panel.componentId) {
      onDelete?.(panel.componentId);
      message.success('已删除面料图片面板');
    }
  };

  const handleReupload = () => {
    if (panel && panel.componentId) {
      onReupload?.(panel);
    }
  };

  const getImageInfo = () => {
    if (!panel || !panel.processedFile) return null;

    if (panel.fileInfo) {
      return {
        size: `${(panel.fileInfo.size / (1024 * 1024)).toFixed(2)} MB`,
        resolution: `${panel.fileInfo.width} x ${panel.fileInfo.height} px`,
        format: panel.fileInfo.format
      };
    }

    return {
      size: '--',
      resolution: '--',
      format: '--'
    };
  };

  // 状态文本
  const getStatusText = () => {
    return '上传完成';
  };

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          <ThumbnailWithPreview
            imageUrl={panel.processedFile || panel.url}
            alt={`${panel.title} 上传结果`}
            status={panel.status}
            error={panel.error}
            onStatusChange={onStatusChange}
            featureName="面料预览"
          />
          <div className="component-text">
            <h3>面料</h3>
            <div className="component-content">
              <p>
                {panel.status === 'completed' && (
                  <>
                    {getStatusText()}
                  </>
                )}
                {panel.status === 'processing' && '处理中...'}
                {panel.status === 'error' && panel.error}
              </p>
            </div>
          </div>
        </div>
        <button 
          className={`expand-btn ${isActive ? 'active' : ''}`}
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

FabricPanel.propTypes = {
  panel: PropTypes.shape({
    componentId: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    status: PropTypes.oneOf(['processing', 'completed', 'error']).isRequired,
    error: PropTypes.string,
    processedFile: PropTypes.string,
    url: PropTypes.string,
    serverFileName: PropTypes.string,
    originalImage: PropTypes.string,
    type: PropTypes.string, // 业务类型，如'fabric', 'clothing'等
    source: PropTypes.string, // 图片来源，如'upload', 'preset', 'history'等
    fileInfo: PropTypes.shape({
      size: PropTypes.number,
      width: PropTypes.number,
      height: PropTypes.number,
      format: PropTypes.string,
      serverFileName: PropTypes.string
    }),
    showOriginal: PropTypes.bool,
  }).isRequired,
  onExpandClick: PropTypes.func.isRequired,
  onDelete: PropTypes.func,
  onReupload: PropTypes.func,
  onStatusChange: PropTypes.func,
  isActive: PropTypes.bool,
  onPanelsChange: PropTypes.func.isRequired,
  pageType: PropTypes.string,
};

export default FabricPanel; 