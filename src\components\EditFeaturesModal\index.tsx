import React from 'react';
import { FiList } from 'react-icons/fi';
import './styles.css';

interface EditFeaturesModalProps {
  isOpen: boolean;
  isSidebarCollapsed: boolean;
  allFeatures: { [key: string]: string[] };
  commonFeatures: string[];
  onClose: () => void;
  onFeatureChange: (featureName: string) => void;
  renderIcon: (IconComponent: any) => React.ReactNode;
}

const EditFeaturesModal: React.FC<EditFeaturesModalProps> = ({
  isOpen,
  isSidebarCollapsed,
  allFeatures,
  commonFeatures,
  onClose,
  onFeatureChange,
  renderIcon
}) => {
  if (!isOpen) return null;

  const isCommon = (featureName: string): boolean => {
    return commonFeatures.includes(featureName);
  };

  return (
    <div 
      className={`edit-modal ${isSidebarCollapsed ? 'sidebar-collapsed' : ''}`} 
      onClick={(e) => e.stopPropagation()}
    >
      <h3>选择常用功能</h3>
      <div className="feature-list">
        {Object.keys(allFeatures).map((parent) => (
          <div key={parent}>
            {/* 暂时隐藏AI视频分组标题 */}
            {parent !== 'AI视频' && <h4>{parent}</h4>}
            {Array.isArray(allFeatures[parent]) && allFeatures[parent].map((child) => (
              // 临时隐藏指定页面按钮（如需恢复显示"消除笔"、"图文成片"、"多图成片"，请删除下方 includes 判断）
              [
                '消除笔',   // inpaint
                '图文成片', // imgtextvideo
                '多图成片'  // mulimgvideo
              ].includes(child) 
                ? null // === 恢复显示方法：删除本 includes 判断及其内容 ===
                : (
                  <label key={child} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={isCommon(child)}
                      onChange={() => onFeatureChange(child)}
                    />
                    <span className="toggle-track"></span>
                    <span>{child}</span>
                  </label>
                )
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default EditFeaturesModal; 