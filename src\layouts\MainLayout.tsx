import React from 'react';
import { useLocation } from 'react-router-dom';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const location = useLocation();
  
  // 检查当前路径是否为管理后台路径
  const isAdminPath = location.pathname.startsWith('/admin');
  
  // 如果是管理后台路径，只渲染子组件，不渲染导航栏和侧边栏
  if (isAdminPath) {
    return <>{children}</>;
  }
  
  // 否则渲染完整的前台布局，但这里我们只返回子组件
  // App.tsx 中已经有了导航栏和侧边栏的渲染逻辑
  return <>{children}</>;
};

export default MainLayout; 