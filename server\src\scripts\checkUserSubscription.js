/**
 * 检查用户订阅信息诊断脚本
 * 使用方法: node checkUserSubscription.js <userId>
 */

const mongoose = require('mongoose');
const Subscription = require('../modules/admin/subscribe/subscription.model');
const Plan = require('../modules/admin/subscribe/plan.model');
const User = require('../modules/auth/user.model');

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/aibikini';

async function checkUserSubscription(userId) {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('数据库连接成功');

    // 检查用户是否存在
    const user = await User.findById(userId);
    if (!user) {
      console.log('❌ 用户不存在:', userId);
      return;
    }
    console.log('✅ 用户存在:', {
      _id: user._id,
      username: user.username,
      name: user.name
    });

    // 查找所有订阅
    const allSubscriptions = await Subscription.find({ user: userId }).sort({ createdAt: -1 });
    console.log('\n📋 用户所有订阅记录:');
    if (allSubscriptions.length === 0) {
      console.log('❌ 用户没有任何订阅记录');
    } else {
      allSubscriptions.forEach((sub, index) => {
        console.log(`${index + 1}. 订阅ID: ${sub._id}`);
        console.log(`   计划: ${sub.plan}`);
        console.log(`   状态: ${sub.status}`);
        console.log(`   开始时间: ${sub.startDate}`);
        console.log(`   结束时间: ${sub.endDate}`);
        console.log(`   是否过期: ${sub.endDate < new Date() ? '是' : '否'}`);
        console.log(`   价格: ${sub.price}`);
        console.log(`   自动续费: ${sub.autoRenew}`);
        console.log('---');
      });
    }

    // 查找活跃订阅
    const activeSubscription = await Subscription.findOne({
      user: userId,
      status: 'active',
      endDate: { $gt: new Date() }
    });
    console.log('\n🟢 活跃订阅:');
    if (activeSubscription) {
      console.log('✅ 找到活跃订阅:', {
        plan: activeSubscription.plan,
        status: activeSubscription.status,
        endDate: activeSubscription.endDate
      });
    } else {
      console.log('❌ 没有活跃订阅');
    }

    // 查找最新订阅
    const latestSubscription = await Subscription.findOne({
      user: userId
    }).sort({ createdAt: -1 });
    console.log('\n📅 最新订阅:');
    if (latestSubscription) {
      console.log('✅ 找到最新订阅:', {
        plan: latestSubscription.plan,
        status: latestSubscription.status,
        endDate: latestSubscription.endDate,
        createdAt: latestSubscription.createdAt
      });
    } else {
      console.log('❌ 没有订阅记录');
    }

    // 检查订阅计划是否存在
    if (latestSubscription) {
      const plan = await Plan.findOne({ code: latestSubscription.plan });
      console.log('\n📝 订阅计划信息:');
      if (plan) {
        console.log('✅ 计划存在:', {
          code: plan.code,
          name: plan.name,
          description: plan.description
        });
      } else {
        console.log('❌ 计划不存在:', latestSubscription.plan);
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n数据库连接已关闭');
  }
}

// 获取命令行参数
const userId = process.argv[2];
if (!userId) {
  console.log('使用方法: node checkUserSubscription.js <userId>');
  console.log('例如: node checkUserSubscription.js 507f1f77bcf86cd799439011');
  process.exit(1);
}

checkUserSubscription(userId); 