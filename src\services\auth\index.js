/**
 * 认证服务
 * 处理用户认证、权限管理相关的业务逻辑
 */

import { login, logout, register, getCurrentUser, refreshToken } from '../../api/auth';
import { handleError } from '../error';

/**
 * 用户登录服务
 * @param {Object} credentials - 登录凭证
 * @returns {Promise<Object>} 登录结果
 */
export const loginService = async (credentials) => {
  try {
    return await login(credentials);
  } catch (error) {
    return handleError(error, '登录');
  }
};

/**
 * 用户注册服务
 * @param {Object} userData - 用户注册数据
 * @returns {Promise<Object>} 注册结果
 */
export const registerService = async (userData) => {
  try {
    return await register(userData);
  } catch (error) {
    return handleError(error, '注册');
  }
};

/**
 * 用户退出登录服务
 * @returns {Promise<Object>} 退出结果
 */
export const logoutService = async () => {
  try {
    return await logout();
  } catch (error) {
    return handleError(error, '退出登录');
  }
};

/**
 * 获取当前用户信息服务
 * @returns {Promise<Object>} 当前用户信息
 */
export const getCurrentUserService = async () => {
  try {
    return await getCurrentUser();
  } catch (error) {
    return handleError(error, '获取用户信息');
  }
};

/**
 * 刷新token服务
 * @returns {Promise<Object>} 刷新结果
 */
export const refreshTokenService = async () => {
  try {
    return await refreshToken();
  } catch (error) {
    return handleError(error, '刷新令牌');
  }
};

export default {
  loginService,
  registerService,
  logoutService,
  getCurrentUserService,
  refreshTokenService
}; 