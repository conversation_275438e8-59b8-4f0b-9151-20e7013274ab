const crypto = require('crypto');

class CaptchaService {
  static captchaStore = new Map(); // 存储验证码

  // 生成验证码
  static generateCaptcha() {
    // 生成6位随机数字
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // 获取新的验证码
  static async getCaptcha(req, res) {
    try {
      const captcha = this.generateCaptcha();
      const sessionId = crypto.randomBytes(16).toString('hex');
      
      // 存储验证码，5分钟后过期
      this.captchaStore.set(sessionId, {
        code: captcha,
        timestamp: Date.now(),
        attempts: 0
      });

      // 定时清除过期的验证码
      setTimeout(() => {
        this.captchaStore.delete(sessionId);
      }, 5 * 60 * 1000);

      return {
        sessionId,
        captcha
      };
    } catch (error) {
      throw new Error('生成验证码失败');
    }
  }

  // 验证验证码
  static async verifyCaptcha(sessionId, userInput) {
    const captchaData = this.captchaStore.get(sessionId);
    
    if (!captchaData) {
      throw new Error('验证码已过期或不存在');
    }

    // 检查尝试次数
    if (captchaData.attempts >= 3) {
      this.captchaStore.delete(sessionId);
      throw new Error('验证码尝试次数过多，请重新获取');
    }

    // 检查是否过期（5分钟）
    if (Date.now() - captchaData.timestamp > 5 * 60 * 1000) {
      this.captchaStore.delete(sessionId);
      throw new Error('验证码已过期');
    }

    // 增加尝试次数
    captchaData.attempts += 1;

    // 验证码匹配检查
    if (userInput !== captchaData.code) {
      throw new Error('验证码错误');
    }

    // 验证成功后删除验证码
    this.captchaStore.delete(sessionId);
    return true;
  }
}

module.exports = CaptchaService; 