# 场景参考图片OSS动态加载系统使用说明

## 概述

新的场景参考图片系统采用OSS动态加载方式，支持灵活数量的场景图片，无需硬编码数量限制，前端样式和交互完全不变。系统现在支持缩略图和大图的区分，提高加载性能和用户体验。

## OSS目录结构

### 基础路径
```
https://file.aibikini.cn/config/scenes/
```

### 分类目录结构
```
/config/scenes/
├── beach/          # 海滩场景
│   ├── 01.jpg     # 缩略图（用于卡片显示）
│   ├── 01-a.jpg   # 大图（用于预览和任务生成）
│   ├── 02.jpg     # 缩略图
│   ├── 02-a.jpg   # 大图
│   └── ...
├── pool/           # 泳池场景
│   ├── 01.jpg     # 缩略图
│   ├── 01-a.jpg   # 大图
│   ├── 02.jpg     # 缩略图
│   ├── 02-a.jpg   # 大图
│   └── ...
├── indoor/         # 室内场景
│   ├── 01.jpg     # 缩略图
│   ├── 01-a.jpg   # 大图
│   ├── 02.jpg     # 缩略图
│   ├── 02-a.jpg   # 大图
│   └── ...
├── street/         # 街道场景
│   ├── 01.jpg     # 缩略图
│   ├── 01-a.jpg   # 大图
│   └── ...
├── nature/         # 自然场景
│   ├── 01.jpg     # 缩略图
│   ├── 01-a.jpg   # 大图
│   └── ...
└── other/          # 其他场景
    ├── 01.jpg     # 缩略图
    ├── 01-a.jpg   # 大图
    └── ...
```

## 文件命名规则

### 图片文件命名
- **缩略图格式**: `{序号}.jpg` - 用于卡片显示，尺寸较小，加载速度快
- **大图格式**: `{序号}-a.jpg` - 用于预览和任务生成，尺寸较大，质量更高
- **序号**: 2位数字，不足2位前面补0
- **示例**: 
  - `01.jpg` - 第1个场景的缩略图
  - `01-a.jpg` - 第1个场景的大图
  - `10.jpg` - 第10个场景的缩略图
  - `10-a.jpg` - 第10个场景的大图

### 图片使用场景
| 场景 | 使用的图片 | 说明 |
|------|------------|------|
| 卡片显示 | 缩略图（01.jpg） | 快速加载，节省带宽 |
| 预览功能 | 大图（01-a.jpg） | 高质量显示 |
| 任务生成 | 大图（01-a.jpg） | 确保生成质量 |

### 支持的分类
| 分类 | 目录名 | 中文名称 |
|------|--------|----------|
| 海滩 | `beach` | 海滩 |
| 泳池 | `pool` | 泳池 |
| 自然 | `nature` | 自然 |
| 街道 | `street` | 街道 |
| 室内 | `indoor` | 室内 |
| 其他 | `other` | 其他 |

## 前端展示顺序

前端会按照以下顺序展示场景图片：
1. 海滩场景（beach/01.jpg ~ beach/10.jpg）
2. 泳池场景（pool/01.jpg ~ pool/10.jpg）
3. 自然场景（nature/01.jpg ~ nature/10.jpg）
4. 街道场景（street/01.jpg ~ street/10.jpg）
5. 室内场景（indoor/01.jpg ~ indoor/10.jpg）
6. 其他场景（other/01.jpg ~ other/10.jpg）

## 使用方法

### 1. 添加新场景图片
1. 准备场景图片，按照命名规则命名：
   - 缩略图：`01.jpg`（建议尺寸：200x200px）
   - 大图：`01-a.jpg`（建议尺寸：800x800px或更高）
2. 将图片上传到OSS对应的分类目录中
3. 前端会自动发现并显示新图片

### 2. 删除场景图片
1. 从OSS对应的分类目录中删除图片文件（缩略图和大图都要删除）
2. 前端会自动移除该图片

### 3. 修改场景图片
1. 替换OSS目录中的图片文件（保持文件名不变）
2. 前端会自动显示新图片

### 4. 添加新分类
1. 在OSS上创建新的分类目录
2. 在 `src/config/scenes/sceneCategories.js` 中的 `SCENE_CATEGORIES` 数组添加新分类
3. 重新部署前端应用

## 系统特性

### ✅ 双图支持
- 缩略图用于卡片显示，加载速度快
- 大图用于预览和任务生成，质量高
- 自动根据使用场景选择合适的图片

### ✅ 灵活数量支持
- 每个分类支持1-20个场景图片
- 无需预先定义固定数量
- 根据实际文件数量动态显示

### ✅ 统一命名规范
- 清晰的文件命名规则
- 便于管理和维护
- 支持批量操作

### ✅ 自动发现机制
- 系统自动扫描OSS目录
- 按顺序加载图片
- 遇到不存在的图片自动停止

### ✅ 前端无感知
- 保持原有的UI样式
- 保持原有的交互逻辑
- 用户完全无感知

### ✅ 无需重新部署
- 增减图片无需重新部署
- 修改图片无需重新部署
- 只有添加新分类才需要重新部署

## 注意事项

1. **文件格式**: 图片必须为JPG格式
2. **序号格式**: 序号必须为2位数字，不足2位前面补0（如：01, 10, 99）
3. **文件路径**: 图片文件必须放在正确的分类目录中
4. **最大数量**: 每个分类最多支持20张图片
5. **加载顺序**: 前端会按照分类顺序和图片序号顺序加载
6. **图片配对**: 每个场景必须有对应的缩略图和大图文件
7. **图片质量**: 缩略图建议200x200px，大图建议800x800px或更高

## 迁移指南

### 从单图系统迁移到双图系统
1. 将现有的图片重命名：
   - `01.jpg` → `01-a.jpg`
   - `02.jpg` → `02-a.jpg`
   - 以此类推
2. 为每个场景创建对应的缩略图：
   - 创建 `01.jpg`（缩略图版本）
   - 创建 `02.jpg`（缩略图版本）
   - 以此类推
3. 上传所有文件到OSS对应目录

## 扩展建议

如果未来需要更高级的功能，可以考虑：

1. **API驱动**: 将文件列表通过后端API提供，支持实时更新
2. **管理后台**: 开发Web管理界面，支持在线管理场景图片
3. **CDN支持**: 将文件存储在CDN上，提高访问速度
4. **缓存机制**: 添加缓存层，减少文件检查开销
5. **图片压缩**: 自动生成缩略图，减少手动处理

## 技术支持

如有问题，请检查：
1. 文件命名是否符合规范
2. 缩略图和大图是否配对存在
3. 文件格式是否为JPG
4. 文件是否放在正确的分类目录中
5. 网络连接是否正常 