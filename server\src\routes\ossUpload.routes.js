const express = require('express');
const router = express.Router();
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });
const ossUtils = require('../utils/ossUtils');
const sizeOf = require('image-size');
const imageUtils = require('../utils/imageUtils');

// 单文件上传接口
router.post('/upload', upload.single('file'), async (req, res) => {
  let processedFilePath = null;
  let isTempFile = false;
  
  try {
    //判断文件大小
    if (req.file.size > 10 * 1024 * 1024) {
      return res.status(400).json({ message: '文件大小不能超过10MB' });
    } 
    if (!req.file) {
      return res.status(400).json({ message: '请选择要上传的文件' });
    }

    // 处理图片方向校正
    processedFilePath = req.file.path;
    let orientationInfo = null;
    
    // 检查是否为图片文件
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const fileExt = req.file.originalname.toLowerCase().substring(req.file.originalname.lastIndexOf('.'));
    
    if (imageExtensions.includes(fileExt)) {
      try {
        // 校正图片方向
        const processResult = await imageUtils.processImageWithMetadata(req.file.path);
        processedFilePath = processResult.processedPath;
        isTempFile = processResult.isTempFile;
        orientationInfo = {
          originalOrientation: processResult.orientation,
          wasCorrected: processResult.wasCorrected
        };
      } catch (error) {
        console.warn('图片方向校正失败，使用原图:', error.message);
        // 如果校正失败，继续使用原图
      }
    }

    // 获取校正后的图片尺寸
    const dimensions = sizeOf(processedFilePath);

    console.log('上传文件信息:', {
      originalname: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype,
      dimensions,
      orientationInfo
    });

    const file = req.file;
    const savePath = req.body.savePath?req.body.savePath:'uploads';
    const ossPath = `${savePath}/${Date.now()}-${file.originalname}`; // OSS中的存储路径

    // 上传处理后的文件到OSS
    let result = await ossUtils.uploadFile(processedFilePath, ossPath);
    if(result.includes('http://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/')){
      result = result.replace('http://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/', 'https://file.aibikini.cn/');
    }
    if(result.includes('https://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/')){
      result = result.replace('https://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/', 'https://file.aibikini.cn/');
    }
    // 获取文件信息
    const fileInfo = {
      size: req.file.size, // 文件大小(字节)
      mimetype: req.file.mimetype, // 文件MIME类型
      originalname: req.file.originalname, // 原始文件名  
      ...dimensions,
      url: result,
      orientationInfo: orientationInfo
    };
    
    res.json({
      success: true,
      url: result,
      fileInfo: fileInfo,
      message: '文件上传成功'
    });

  } catch (error) {
    console.error('文件上传失败:', error);
    res.status(500).json({
      success: false,
      message: '文件上传失败'
    });
  } finally {
    // 清理临时文件
    if (isTempFile && processedFilePath && processedFilePath !== req.file.path) {
      imageUtils.cleanupTempFile(processedFilePath);
    }
  }
});

// 多文件上传接口
router.post('/uploads', upload.array('files', 20), async (req, res) => {
  const tempFiles = [];
  
  try {
    //判断文件大小
    if (req.files.some(file => file.size > 10 * 1024 * 1024)) {
      return res.status(400).json({ message: '文件大小不能超过10MB' });
    }
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ message: '请选择要上传的文件' });
    }

    const savePath = req.body.savePath?req.body.savePath:'uploads';
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];

    const uploadPromises = req.files.map(async (file, index) => {
      let processedFilePath = file.path;
      let orientationInfo = null;
      
      // 检查是否为图片文件并处理方向
      const fileExt = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
      if (imageExtensions.includes(fileExt)) {
        try {
          const processResult = await imageUtils.processImageWithMetadata(file.path);
          processedFilePath = processResult.processedPath;
          orientationInfo = {
            originalOrientation: processResult.orientation,
            wasCorrected: processResult.wasCorrected
          };
          
          // 记录临时文件以便后续清理
          if (processResult.isTempFile) {
            tempFiles.push(processedFilePath);
          }
        } catch (error) {
          console.warn(`图片 ${file.originalname} 方向校正失败，使用原图:`, error.message);
        }
      }
      
      const ossPath = `${savePath}/${Date.now()}-${file.originalname}`;
      let url = await ossUtils.uploadFile(processedFilePath, ossPath);
      if(url.includes('http://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/')){
        url = url.replace('http://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/', 'https://file.aibikini.cn/');
      }
      if(url.includes('https://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/')){
        url = url.replace('https://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/', 'https://file.aibikini.cn/');
      }
      
      return {
        url,
        file,
        processedFilePath,
        orientationInfo
      };
    });

    const uploadResults = await Promise.all(uploadPromises);
    const urls = uploadResults.map(result => result.url);
    
    const fileInfos = uploadResults.map((result, index) => {
      const dimensions = sizeOf(result.processedFilePath);
      return {
        size: result.file.size,
        mimetype: result.file.mimetype,  
        ...dimensions,
        url: result.url,
        orientationInfo: result.orientationInfo
      };
    });
    
    res.json({
      success: true,
      urls: urls,
      fileInfos: fileInfos,
      message: '文件上传成功'
    });

  } catch (error) {
    console.error('文件上传失败:', error);
    res.status(500).json({
      success: false,
      message: '文件上传失败'
    });
  } finally {
    // 清理所有临时文件
    tempFiles.forEach(tempFile => {
      imageUtils.cleanupTempFile(tempFile);
    });
  }
});

module.exports = router;

