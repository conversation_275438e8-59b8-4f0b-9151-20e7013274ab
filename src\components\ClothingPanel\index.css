/* 导入统一面板样式 */
@import '../../styles/panels.css';

/* 展开按钮激活状态样式 */
.expand-btn.active {
  background: transparent;
}

.expand-btn.active span {
  border-color: #FF3C6A;
}

.expand-btn.active:hover {
  background: transparent;
}

/* 添加蒙版状态样式 */
.mask-status {
  color: var(--brand-primary);
  display: inline-block;
  margin-left: 10px; /* 添加左边距确保间距适当 */
}

/* 移除不再需要的样式 */
.mask-status.manual-mask,
.mask-status.auto-mask {
  color: var(--brand-primary);
} 