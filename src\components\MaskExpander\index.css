/* 导入统一样式 */
@import '../../styles/theme.css';
@import '../../styles/buttons.css';
@import '../../styles/inputs.css';

.mask-expander-setting {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  height: 88px;
  display: flex;
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  position: relative;
}

.weight-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
}

.weight-label {
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
}

.weight-label span:first-child {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.weight-values {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.weight-values span {
  font-size: var(--font-size-md);
  color: var(--brand-primary) !important;
  font-weight: 500;
}

.colored-value {
  font-size: var(--font-size-md);
  color: var(--brand-primary) !important;
  font-weight: 500;
}

.weight-slider-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 20px 0 0px;
  position: relative;
  padding-right: 66px; /* 为右上角的提示按钮留出空间 */
}

.weight-sliders {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 10px 0;
  gap: 16px;
}

.weight-slider-row {
  display: flex;
  align-items: center;
  gap: 0;
  width: 100%;
}

.weight-item-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  white-space: nowrap;
  width: 60px;
  text-align: right;
  margin-right: 10px;
}

.slider-container {
  flex: 1;
  position: relative;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.slider-track {
  position: relative;
  height: 2px;
  background: var(--border-light);
  border-radius: var(--radius-sm);
  width: 100%;
  margin: 8px 0;
}

.slider-fill {
  position: absolute;
  height: 100%;
  background: var(--brand-gradient);
  border-radius: var(--radius-sm);
  transition: width 0s cubic-bezier(0.4, 0, 0.2, 1);
}

.slider-input {
  position: absolute;
  top: 50%;
  left: 0%;
  width: 100%;
  height: 20px;
  transform: translateY(-50%);
  -webkit-appearance: none;
  background: transparent;
  cursor: pointer;
  margin: 0;
  padding: 0;
}

.slider-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  border: 3px solid var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  z-index: 1;
}

.slider-input::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  border: 3px solid var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: pointer;
  transition: var(--transition-normal);
}

.slider-input::-webkit-slider-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
}

.slider-input::-moz-range-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
} 