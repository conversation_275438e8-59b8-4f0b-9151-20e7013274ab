const { CreditTransaction, CreditBalance } = require('../../admin/credits/credit.model');
const  User = require('../../auth/user.model');
const { createError } = require('../../../utils/error');

/**
 * 获取用户算力余额
 */
exports.getUserCreditBalance = async (req, res, next) => {
  try {
    const userId = req.user._id;
    
    // 查找或创建用户算力余额
    let creditBalance = await CreditBalance.findOne({ user: userId });
    if (!creditBalance) {
      creditBalance = new CreditBalance({
        user: userId,
        balance: 0,
        totalRecharged: 0,
        totalConsumed: 0
      });
      await creditBalance.save();
    }
    
    res.json(creditBalance);
  } catch (error) {
    next(createError(500, '获取算力余额失败', error));
  }
};

/**
 * 获取用户算力交易记录
 */
exports.getUserCreditTransactions = async (req, res, next) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10, type, startDate, endDate } = req.query;
    
    // 构建查询条件
    const query = { user: userId };
        
    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, '用户不存在'));
    }
    // 添加类型过滤
    if (type) {
      query.type = type;
    }
    
    // 添加日期范围过滤
    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.createdAt = query.createdAt || {};
      query.createdAt.$gte = start;
    }
    
    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.createdAt = query.createdAt || {};
      query.createdAt.$lte = end;
    }
    
    // 计算总数
    const total = await CreditTransaction.countDocuments(query);
    
    // 查询交易记录
    const transactions = await CreditTransaction.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(Number(limit));
      
    // 补充用户名
    const username = user.username;
    const transactionsWithUsername = transactions.map(t => {
      const transactionObj = t.toObject();
      transactionObj.username = username;
      return transactionObj;
    });

    res.json({
      total,
      page: Number(page),
      limit: Number(limit),
      data: transactionsWithUsername
    });
  } catch (error) {
    next(createError(500, '获取算力交易记录失败', error));
  }
}; 