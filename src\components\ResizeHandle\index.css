.resize-handle {
  width: 16px;
  margin: 0 -6px;
  cursor: col-resize;
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.resize-handle::after {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 3px;
  height: 100%;
  background: var(--border-light);
  transition: var(--transition-normal);
}

/* 移除拖动提示图标
.resize-handle::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 1px;
  height: 24px;
  background-color: var(--text-secondary);
  opacity: 0.5;
  transition: opacity 0.2s ease;
  z-index: 1;
}

.resize-handle:hover::before {
  opacity: 0;
}
*/

.resize-handle:hover::after {
  background: var(--brand-primary);
  width: 3px;
}

/* 拖动过程中防止文本选择和其他干扰 */
body.resizing {
  user-select: none;
  -webkit-user-select: none;
  cursor: col-resize !important;
  overflow: hidden;
}

/* 移动端隐藏ResizeHandle */
@media (max-width: 768px) {
  .resize-handle {
    display: none !important;
  }
} 