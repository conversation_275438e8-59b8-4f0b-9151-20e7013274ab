/* 重置样式，确保纯文本页面 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
}

.legal-page {
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  padding: 0;
  margin: 0;
}

.legal-container {
  width: 100%;
  height: 100vh;
  padding: 0;
  margin: 0;
}

.legal-container h1 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 10px;
  padding: 0;
  border: none;
}

.legal-meta {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.legal-meta p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* iframe容器样式 */
.legal-content-iframe {
  width: 100%;
  height: 100%;
  border: none;
  overflow: hidden;
  padding: 0 40px;
  box-sizing: border-box;
}

/* iframe样式 */
.legal-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.legal-footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.legal-footer p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .legal-content-iframe {
    padding: 0 20px; /* 移动端减少边距 */
  }
}

@media (max-width: 480px) {
  .legal-content-iframe {
    padding: 0 15px; /* 小屏幕进一步减少边距 */
  }
} 