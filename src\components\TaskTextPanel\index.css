/* 使用与TaskPanel相同的基础样式 */
@import '../TaskPanel/index.css';

/* 文本区域样式 */
.task-text-area {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-xxs);
  position: relative;
  min-height: 128px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 生成中状态样式 */
.task-text-area .processing-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  width: 90%;
  height: 80%;
}

.task-text-area .processing-indicator .spinner {
  width: var(--icon-size-md);
  height: var(--icon-size-md);
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--brand-primary);
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  margin-bottom: var(--spacing-md);
}

.task-text-area .processing-indicator span {
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  font-weight: 500;
  text-align: center;
  line-height: 1.5;
  max-width: 80%;
  background: linear-gradient(45deg, var(--text-secondary) 0%, var(--text-primary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: pulse 2s infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.text-content {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  position: relative;
  min-height: 128px;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.text-content p {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-md);
  line-height: 1.6;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
  padding-right: var(--spacing-md);
}

/* 移除自定义滚动条样式，使用全局统一样式 */

/* 文本操作按钮样式 */
.text-actions {
  position: absolute;
  bottom: var(--spacing-md);
  right: var(--spacing-md);
  opacity: 0;
  transition: opacity 0.2s ease;
  background: var(--bg-primary);
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-sm);
}

.text-content:hover .text-actions {
  opacity: 1;
}

/* 空槽样式 */
.task-text-area .empty-slot {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  width: 90%;
  height: 80%;
  box-shadow: var(--shadow-sm);
}

.task-text-area .empty-slot span {
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  font-weight: 500;
  text-align: center;
  line-height: 1.5;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .text-content {
    min-height: 150px;
  }
  
  .text-content p {
    max-height: 200px;
    font-size: var(--font-size-sm);
  }
  
  .text-actions {
    position: relative;
    bottom: auto;
    right: auto;
    opacity: 1;
    margin-top: var(--spacing-md);
    display: flex;
    justify-content: flex-end;
  }
  
  /* 修复TaskTextPanel中edit-btn的宽度问题 */
  .task-actions .edit-btn {
    flex: none !important;
    margin-right: 0;
    min-width: auto !important;
    width: auto !important;
  }
  
  .task-header {
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    gap: 0 !important;
  }
  .task-info {
    flex: 1 1 auto !important;
    min-width: 0 !important;
    margin-right: 8px !important;
  }
  .task-actions {
    flex: none !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 0 !important;
  }
  .more-actions {
    margin-left: 0 !important;
  }
  .more-btn {
    width: 28px !important;
    height: 28px !important;
    font-size: var(--font-size-xs) !important;
  }
}

@media (max-width: 480px) {
  .text-content {
    min-height: 120px;
    padding: var(--spacing-md);
  }
  
  .text-content p {
    max-height: 150px;
  }
  
  /* 480px以下也确保edit-btn宽度正确 */
  .task-actions .edit-btn {
    flex: none !important;
    margin-right: 0;
    min-width: auto !important;
    width: auto !important;
  }
  
  /* 确保TaskTextPanel中的task-info也保持水平排列 */
  .task-info {
    flex-direction: row !important;
    align-items: center !important;
    gap: var(--spacing-xs) !important;
    flex-wrap: wrap !important;
  }
  
  .task-time {
    font-size: 11px !important;
    white-space: nowrap !important;
  }
  
  .task-id-container {
    font-size: 11px !important;
  }
  
  .task-id-label {
    font-size: 11px !important;
  }
  
  .task-id {
    font-size: 11px !important;
  }
  .more-btn {
    width: 24px !important;
    height: 24px !important;
    font-size: var(--font-size-xs) !important;
  }
}

@media (max-width: 360px) {
  .task-info {
    flex-direction: row !important;
    align-items: center !important;
    gap: 6px !important;
    flex-wrap: wrap !important;
  }
  
  .task-time {
    font-size: 10px !important;
    white-space: nowrap !important;
  }
  
  .task-id-container {
    font-size: 10px !important;
  }
  
  .task-id-label {
    font-size: 10px !important;
  }
  
  .task-id {
    font-size: 10px !important;
  }
  .more-btn {
    width: 22px !important;
    height: 22px !important;
    font-size: var(--font-size-xs) !important;
  }
} 
.task-progress {
  position: relative;
  width: 100%;
  height: 4px;
  background-color: var(--bg-secondary);
  border-radius: 2px;
  overflow: hidden;
  margin: 8px 0;
}

.progress-bar {
  height: 100%;
  transition: width 0.3s ease;
}

/* New styles for progress text and percentage */
.processing-row {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 8px;
}

.processing-desc,
.processing-percent {
  font-size: var(--font-size-sm);
  line-height: 1.6;
  font-weight: 500;
  color: var(--text-secondary);
}

.processing-percent {
  margin-left: 12px;
  font-size: 12px;
} 