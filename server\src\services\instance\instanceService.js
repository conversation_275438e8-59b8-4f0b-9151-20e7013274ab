const { toDate } = require('date-fns-tz');

const ResourceAPI = require('../../config/ResourceAPI');
const Instance = require('../../models/Instance');
const MongoDBCache = require('../../utils/cacheUtils');
const { createError } = require('../../utils/error');
const instanceAxios = require('../../utils/instanceAxios');
const excludeInstanceId = [];
const FlowTask = require('../../models/FlowTask');

// 创建缓存实例
const cache = new MongoDBCache({
  collectionName: 'instance_status_cache',
  defaultTTL: 60 * 1000 // 60秒缓存
});

// 初始化缓存连接
cache.connect().catch(err => {
  console.error('缓存连接初始化失败:', err);
});

class InstanceService {

  constructor() {
    this.CACHE_KEY_PREFIX = 'instance:status:';
    this.IDLE_TIMEOUT = 10; // 空闲10分钟后关机
    this.lastActivityTime = new Map(); // 记录实例最后活动时间
    this.USER_INSTANCE_HISTORY_TTL = 30 * 60 * 1000; // 用户实例历史记录30分钟过期
  }

  /**
   * 记录用户使用实例的历史
   * @param {string} userId 用户ID
   * @param {string} instanceId 实例ID
   */
  async recordUserInstanceHistory(userId, instanceId) {
    try {
      const historyKey = `user_instance_history_${userId}`;
      const timestamp = Date.now();

      // 获取现有历史记录
      let history = await cache.get(historyKey) || [];
      if (!Array.isArray(history)) {
        history = [];
      }

      // 添加新的使用记录
      const newRecord = {
        instanceId,
        timestamp,
        lastUsed: timestamp
      };

      // 检查是否已存在该实例的记录
      const existingIndex = history.findIndex(record => record.instanceId === instanceId);
      if (existingIndex !== -1) {
        // 更新现有记录的时间戳
        history[existingIndex].lastUsed = timestamp;
        history[existingIndex].timestamp = timestamp;
      } else {
        // 添加新记录
        history.push(newRecord);
      }

      // 清理超过30分钟的历史记录
      const cutoffTime = timestamp - this.USER_INSTANCE_HISTORY_TTL;
      history = history.filter(record => record.timestamp >= cutoffTime);

      // 按最后使用时间排序，最新的在前面
      history.sort((a, b) => b.lastUsed - a.lastUsed);

      // 保存到缓存
      await cache.set(historyKey, history, this.USER_INSTANCE_HISTORY_TTL);

      console.log(`记录用户 ${userId} 使用实例 ${instanceId} 的历史`);
    } catch (error) {
      console.error('记录用户实例历史失败:', error);
    }
  }

  /**
   * 获取用户最近使用的实例列表
   * @param {string} userId 用户ID
   * @returns {Promise<Array>} 最近使用的实例列表
   */
  async getUserInstanceHistory(userId) {
    try {
      const historyKey = `user_instance_history_${userId}`;
      const history = await cache.get(historyKey) || [];

      if (!Array.isArray(history)) {
        return [];
      }

      // 清理过期的历史记录
      const cutoffTime = Date.now() - this.USER_INSTANCE_HISTORY_TTL;
      const validHistory = history.filter(record => record.timestamp >= cutoffTime);

      // 如果清理后有变化，更新缓存
      if (validHistory.length !== history.length) {
        await cache.set(historyKey, validHistory, this.USER_INSTANCE_HISTORY_TTL);
      }

      return validHistory;
    } catch (error) {
      console.error('获取用户实例历史失败:', error);
      return [];
    }
  }

  /**
   * 根据用户历史优先选择实例
   * @param {Array} availableInstances 可用实例列表
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 优先选择的实例
   */
  async prioritizeInstanceByUserHistory(availableInstances, userId) {
    try {
      if (!userId || availableInstances.length === 0) {
        return availableInstances[0]; // 没有用户ID或没有可用实例，返回第一个
      }

      // 计算半小时前的时间戳
      const halfHourAgo = new Date(Date.now() - 30 * 60 * 1000);

      // 查询用户最近半小时内正在执行的任务
      const recentTasks = await FlowTask.find({
        deviceToken: userId,
        status: 'processing',
        createdAt: { $gte: halfHourAgo }
      }).sort({ createdAt: -1 }).limit(10);

      if (recentTasks.length === 0) {
        console.log(`用户 ${userId} 最近半小时内没有正在执行的任务`);
        return null;
      }

      // 创建实例ID到实例对象的映射
      const instanceMap = new Map();
      availableInstances.forEach(instance => {
        instanceMap.set(instance.instanceId, instance);
      });

      // 按任务创建时间倒序查找可用的实例
      for (const task of recentTasks) {
        if (task.instanceId) {
          const instance = instanceMap.get(task.instanceId);
          if (instance) {
            console.log(`用户 ${userId} 优先选择最近半小时内正在执行任务的实例: ${task.instanceId} (任务ID: ${task.taskId})`);
            return instance;
          }
        }
      }

      console.log(`用户 ${userId} 最近半小时内的任务实例都不可用`);
      return null;

    } catch (error) {
      console.error('根据用户最近任务选择实例失败:', error);
      return null;
    }
  }

  /**
   * 启动实例
   * @param {string} instanceId 实例ID
   * @returns {Promise<boolean>} 是否成功启动
   */
  async startInstance(instanceId) {
    try {
      await cache.set('open_starting_'+instanceId,'1',-1);
      const response = await instanceAxios.put(
        ResourceAPI.INSTANCE_START.url + instanceId
      );

      if (response.data.code === 0) {
        // 更新缓存状态为启动中
        return true;
      }

      throw new Error(response.data.message || '启动实例失败');
    } catch (error) {
      console.log(error);
      await cache.del('open_starting_'+instanceId);
      throw createError(500, `启动实例失败: ${error.message}`);
    }
  }

  /**
   * 等待实例启动完成
   * @param {string} instanceId 实例ID
   * @param {number} timeout 超时时间(毫秒)
   * @returns {Promise<boolean>} 是否启动成功
   */
  async waitForInstanceReady(instanceId, timeout = 300000) { // 默认5分钟超时
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const status = await this.getInstanceStatus(instanceId);

      if (status === 300) { // 运行中或空 闲
        return true;
      }

      if (status === 500) { // 启动失败
        this.stopInstance(instanceId);
        throw createError(500, '实例启动失败');
      }
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    }

    throw createError(504, '实例启动超时');
  }

  /**
   * 关闭实例
   * @param {string} instanceId 实例ID
   * @returns {Promise<boolean>} 是否成功关机
   */
  async stopInstance(instanceId) {
    try {
      const response = await instanceAxios.put(
        ResourceAPI.INSTANCE_STOP.url + instanceId
      );

      if (response.data.code === 0) {
        // 更新缓存状态为关机中
        await this.checkInstanceCache(instanceId);
        return true;
      }

      throw new Error(response.data.message || '关闭实例失败');
    } catch (error) {
      throw createError(500, `关闭实例失败: ${error.message}`);
    }
  }

  /**
   * 记录实例活动
   * @param {string} instanceId 实例ID
   */
  async recordInstanceActivity(instanceId) {
    this.lastActivityTime.set(instanceId, Date.now());
  }

  // 做判断，存在则删除
  async checkInstanceCache(instanceId) {
    const inUse = await cache.get('instance_in_use_' + instanceId);
    const inOpenStart = await cache.get('open_starting_' + instanceId);
    const newVar = await cache.get('instance_finished_' + instanceId);
    if(inUse){
      await cache.del('instance_in_use_' + instanceId);
    }
    if(inOpenStart){
      await cache.del('open_starting_' + instanceId);
    }
    if(newVar){
      await cache.del('instance_finished_' + instanceId);
    }
  }

  /**
   * 检查并关闭空闲实例
   * @param {string} instanceId 实例ID
   */
  async checkAndStopIdleInstance(instanceId) {
    try {
      // 首先检查实例是否设置为常开模式
      const instance = await Instance.findOne({ appId: instanceId });
      if (instance && instance.isAlwaysOn) {
        console.log(`实例 ${instanceId} 设置为常开模式，跳过自动关机检查`);
        return;
      }

      const newVar = await cache.get('instance_finished_' + instanceId);
      const inUse = await cache.get('instance_in_use_' + instanceId);
      const inOpenStart = await cache.get('open_starting_' + instanceId);
      if(!newVar&&!inOpenStart&&!inUse) {
        console.warn('检查到状态出现问题的机器，自动关机');
        await this.stopInstance(instanceId);
      }

      if (newVar) {
        const timeZone = 'Asia/Shanghai'; // 假设存储的是北京时间
        const utcDate = toDate(newVar, { timeZone });

        const diffInMs = new Date() - utcDate;
        const min = Math.floor(diffInMs / (1000 * 60));
        // 提示注解
        console.log('实例'+instanceId+'已空闲'+min+'分钟');
        // 时间格式化判断是否超过 this.IDLE_TIMEOUT
        if (min >= this.IDLE_TIMEOUT) {
          await this.stopInstance(instanceId);
        }
      }else if(!inUse && !inOpenStart) {
        if(newVar){
          const timeZone = 'Asia/Shanghai'; // 假设存储的是北京时间
          const utcDate = toDate(newVar, { timeZone });

          const diffInMs = new Date() - utcDate;
          const min = Math.floor(diffInMs / (1000 * 60));
          // 提示注解
          console.log('实例'+instanceId+'已空闲'+min+'分钟');
          // 时间格式化判断是否超过 this.IDLE_TIMEOUT
          if (min >= this.IDLE_TIMEOUT) {
            await this.stopInstance(instanceId);
          }
        }
      }
    } catch (error) {
      console.log(error);
      console.error('检查空闲实例失败: 定时任务检查失败');
    }
  }

  /**
   * 获取可用实例
   * @param {string} userId 用户ID（可选）
   * @param {boolean} useRunning 是否使用运行中的实例
   * @param {number} times 重试次数
   * @param {string} preferredInstanceId 优先使用的实例ID（用于用户实例连续性）
   * @returns {Promise<Instance>} 可用的实例
   */
  async getAvailableInstance(userId = null, useRunning = false, times = 0, preferredInstanceId = null) {
    try {
      // 调用云服务API获取实例列表
      const response = await instanceAxios.get(ResourceAPI.INSTANCE_LIST.url, {
        params: {
          page: 1,
          limit: 200,
        }
      });

      if (response.data.code !== 0) {
        throw new Error(response.data.message || '获取实例列表失败');
      }

      let instances = response.data.data.appList;
      if (!instances || instances.length === 0) {
        throw createError(503, '没有可用的实例');
      }

      instances = await Promise.all(
        instances.map(async (instance) => {
          return {
            ...instance,
            instanceId: instance.appId,
            name: instance.customName,
            url: instance.webUIAddress,
            apiKey: instance.apiKey,
            type: instance.type,
            status: instance.status
          };
        })
      );
      const instanceConfig = await Instance.find({ isAlwaysOn: true });
      const runningList = [];
      const openList = [];
      const shutList = [];
      for (const instance of instances) {
        try {
          const matchedInstance = instanceConfig.find(
              instance1 => instance1.instanceId === instance.instanceId
          );

          // 检查状态是否符合要求
          if (instance.status == 800) {
            if (matchedInstance){
              continue
            }
            shutList.push(instance);
            continue;
          }
          if (instance.status==300){
            // true结束了
            const isFinished = !!await cache.get('instance_finished_' + instance.appId);
            // true 使用中
            const isInUse = !!await cache.get('instance_in_use_' + instance.appId);

            if (isFinished || !isInUse) {
              openList.push(instance);
              continue;
            }else{
              runningList.push(instance);
              continue;
            }
          }
        } catch (error) {
          console.error(`检查实例 ${instance.appId} 时发生错误:`, error);
          // 继续检查下一个实例
          continue;
        }
      }

      // 优先检查用户指定的实例（用于实例连续性）
      if (preferredInstanceId) {
        const preferredInstance = instances.find(instance => instance.instanceId === preferredInstanceId);
        if (preferredInstance) {
          // 检查指定实例是否可用
          if (preferredInstance.status === 300) {
            const isFinished = !!await cache.get('instance_finished_' + preferredInstance.appId);
            const isInUse = !!await cache.get('instance_in_use_' + preferredInstance.appId);

            // 如果实例正在使用中（用户当前正在该实例上执行任务），直接返回
            if (isInUse && !isFinished) {
              console.log(`用户 ${userId} 继续在指定实例 ${preferredInstanceId} 中运行（实例连续性）`);
              return preferredInstance;
            }
            // 如果实例空闲，也可以使用
            else if (isFinished || !isInUse) {
              console.log(`用户 ${userId} 使用指定的空闲实例 ${preferredInstanceId}（实例连续性）`);
              return preferredInstance;
            }
          }
          console.log(`用户 ${userId} 指定的实例 ${preferredInstanceId} 不可用，将使用其他实例`);
        } else {
          console.log(`用户 ${userId} 指定的实例 ${preferredInstanceId} 不在可用实例列表中`);
        }
      }

      // 检查半小时内容的任务使用的实例，有限选择之前使用的实例
      const selectedInstance = await this.prioritizeInstanceByUserHistory(instances, userId);
      if(selectedInstance){
        return selectedInstance;
      }

      // 如果runningList大于0，则选择runningList中的实例,不需要启动
      if (openList.length>0 ) {
        const randomIndex = Math.floor(Math.random() * openList.length);
        return openList[randomIndex];
      }

      // 其次选择关机中的实例并启动
      if (shutList.length>0 && !useRunning) {
        const randomIndex = Math.floor(Math.random() * shutList.length);
        const shutdownInstance = shutList[randomIndex];
        // 启动实例
        await this.startInstance(shutdownInstance.instanceId);
        // 等待实例就绪
        await this.waitForInstanceReady(shutdownInstance.instanceId);

        // 重新获取实例信息
        return shutdownInstance;
      }

      // 如果都没有找到就排队在runningList中
      if (runningList.length>0 ) {
        const randomIndex = Math.floor(Math.random() * runningList.length);
        return runningList[randomIndex];
      }

      throw createError(503, '没有可用的实例');
    } catch (error) {
      if (times>3){
        throw createError(503, '没有可用的实例');
      }
      return  await this.getAvailableInstance(userId, true,times++)
    }
  }

  /**
   * 获取实例状态
   * @param {string} instanceId 实例ID
   * @returns {Promise<string>} 实例状态
   */
  async getInstanceStatus(instanceId) {
    try {
      // 缓存未命中，调用API获取状态
      const response = await instanceAxios.get(
        ResourceAPI.INSTANCE_LIST.url,{params:{appId:instanceId}}
      );

      if (response.data.code === 0) {
        // 缓存状态
        return response.data.data.appList[0].status;
      }

      throw new Error(response.data.message || '获取实例状态失败');
    } catch (error) {
      throw createError(500, `获取实例状态失败: ${error.message}`);
    }
  }
  /**
   * 获取所有正在使用中的实例缓存信息
   * @returns {Promise<Array>} 实例使用信息列表
   */
  async getAllInstanceInUseInfo() {
    try {
      // 确保缓存已连接
      if (!cache.isConnected) {
        await cache.connect();
      }

      // 从缓存集合中查找所有以 instance_in_use_ 开头的文档
      const inUseInstances = await cache.collection.find({
        _id: { $regex: '^instance_in_use_' }
      }).toArray();

      return inUseInstances.map(doc => ({
        instanceId: doc._id.replace('instance_in_use_', ''),
        value: doc.value,
        createdAt: doc.createdAt,
        expiresAt: doc.expiresAt
      }));
    } catch (error) {
      throw createError(500, `获取实例使用信息失败: ${error.message}`);
    }
  }

  /**
   * 获取实例空闲时长信息
   * @param {string} instanceId 实例ID
   * @returns {Promise<Object>} 空闲时长信息
   */
  async getInstanceIdleTime(instanceId) {
    try {
      const finishedTime = await cache.get('instance_finished_' + instanceId);
      const inUse = await cache.get('instance_in_use_' + instanceId);
      const inOpenStart = await cache.get('open_starting_' + instanceId);

      // 如果实例正在使用中或正在启动中，返回null
      if (inUse || inOpenStart) {
        return {
          isIdle: false,
          idleMinutes: 0,
          finishedTime: null,
          status: 'in_use'
        };
      }

      // 如果没有完成时间记录，说明实例可能有问题
      if (!finishedTime) {
        return {
          isIdle: false,
          idleMinutes: 0,
          finishedTime: null,
          status: 'unknown'
        };
      }

      // 计算空闲时长
      const timeZone = 'Asia/Shanghai';
      const utcDate = toDate(finishedTime, { timeZone });
      const diffInMs = new Date() - utcDate;
      const idleMinutes = Math.floor(diffInMs / (1000 * 60));

      return {
        isIdle: true,
        idleMinutes: idleMinutes,
        finishedTime: finishedTime,
        status: 'idle',
        shouldStop: idleMinutes >= this.IDLE_TIMEOUT
      };
    } catch (error) {
      console.error(`获取实例 ${instanceId} 空闲时长失败:`, error);
      return {
        isIdle: false,
        idleMinutes: 0,
        finishedTime: null,
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * 获取所有实例的空闲时长信息
   * @returns {Promise<Array>} 所有实例的空闲时长信息
   */
  async getAllInstanceIdleInfo() {
    try {
      // 确保缓存已连接
      if (!cache.isConnected) {
        await cache.connect();
      }

      // 从缓存集合中查找所有以 instance_finished_ 开头的文档
      const finishedInstances = await cache.collection.find({
        _id: { $regex: '^instance_finished_' }
      }).toArray();

      const idleInfoList = [];

      for (const doc of finishedInstances) {
        const instanceId = doc._id.replace('instance_finished_', '');
        const idleInfo = await this.getInstanceIdleTime(instanceId);

        idleInfoList.push({
          instanceId: instanceId,
          ...idleInfo
        });
      }

      return idleInfoList;
    } catch (error) {
      throw createError(500, `获取实例空闲信息失败: ${error.message}`);
    }
  }
}

// 确保在应用退出时关闭缓存连接
process.on('SIGINT', async () => {
  await cache.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await cache.close();
  process.exit(0);
});

// 定期检查空闲实例
setInterval(async () => {
  try {
    console.log('检查实例是否空闲');
    const service = new InstanceService();
    const response = await instanceAxios.get(ResourceAPI.INSTANCE_LIST.url, {
      params: { page: 1, limit: 100,status:300 }
    });

    if (response.data.code === 0) {
      let instances = response.data.data.appList;
      instances = instances
          .filter(async instance => await cache.get('instance_finished_' + instance.appId));

      console.log('监测运行中实例数量：'+ instances.length);
      for (const instance of instances) {
        // 检查实例是否设置为常开模式
        const instanceConfig = await Instance.findOne({ appId: instance.appId , isAlwaysOn: true });
        if (instanceConfig && instanceConfig.isAlwaysOn) {
          console.log(`实例 ${instance.appId} 设置为常开模式，跳过自动关机检查`);
          continue;
        }
        await service.checkAndStopIdleInstance(instance.appId);
      }
    }
  } catch (error) {
    console.error('检查空闲实例时出错:', error);
  }
},  1000*60*1); // 每1分钟检查一次

module.exports = new InstanceService();