const mongoose = require('mongoose');
const Subscription = require('../modules/admin/subscribe/subscription.model');

// 连接数据库
mongoose.connect('mongodb://localhost:27017/aibikini', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function runAllTests() {
  try {
    console.log('🚀 开始全面测试订阅状态自动更新功能...\n');
    
    // 测试1：状态计算功能
    await testStatusCalculation();
    
    // 测试2：自动状态更新功能
    await testAutoStatusUpdate();
    
    // 测试3：批量更新功能
    await testBatchUpdate();
    
    // 测试4：数据库操作测试
    await testDatabaseOperations();
    
    // 测试5：边界条件测试
    await testEdgeCases();
    
    console.log('\n✅ 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// 测试1：状态计算功能
async function testStatusCalculation() {
  console.log('=== 测试1：状态计算功能 ===');
  
  const now = new Date();
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
  const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  
  // 测试用例1：未开始状态
  const notStartedSubscription = {
    startDate: tomorrow,
    endDate: nextWeek,
    status: 'pending'
  };
  const notStartedStatus = Subscription.calculateActualStatus(notStartedSubscription);
  console.log(`未开始订阅: ${notStartedStatus} (期望: not_started) - ${notStartedStatus === 'not_started' ? '✅' : '❌'}`);
  
  // 测试用例2：活跃状态
  const activeSubscription = {
    startDate: yesterday,
    endDate: tomorrow,
    status: 'pending'
  };
  const activeStatus = Subscription.calculateActualStatus(activeSubscription);
  console.log(`活跃订阅: ${activeStatus} (期望: active) - ${activeStatus === 'active' ? '✅' : '❌'}`);
  
  // 测试用例3：已过期状态
  const expiredSubscription = {
    startDate: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
    endDate: yesterday,
    status: 'active'
  };
  const expiredStatus = Subscription.calculateActualStatus(expiredSubscription);
  console.log(`已过期订阅: ${expiredStatus} (期望: expired) - ${expiredStatus === 'expired' ? '✅' : '❌'}`);
  
  // 测试用例4：已取消状态（应该保持不变）
  const canceledSubscription = {
    startDate: yesterday,
    endDate: tomorrow,
    status: 'canceled'
  };
  const canceledStatus = Subscription.calculateActualStatus(canceledSubscription);
  console.log(`已取消订阅: ${canceledStatus} (期望: canceled) - ${canceledStatus === 'canceled' ? '✅' : '❌'}`);
  
  console.log('');
}

// 测试2：自动状态更新功能
async function testAutoStatusUpdate() {
  console.log('=== 测试2：自动状态更新功能 ===');
  
  // 创建测试订阅
  const testSubscription = new Subscription({
    user: new mongoose.Types.ObjectId(),
    plan: 'basic',
    status: 'pending',
    startDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // 昨天开始
    endDate: new Date(Date.now() + 24 * 60 * 60 * 1000),   // 明天结束
    price: 0,
    autoRenew: false
  });
  
  await testSubscription.save();
  console.log(`创建测试订阅: ${testSubscription._id}, 初始状态: ${testSubscription.status}`);
  
  // 测试自动更新
  const updatedStatus = await Subscription.updateSubscriptionStatus(testSubscription._id);
  console.log(`自动更新后状态: ${updatedStatus} (期望: active) - ${updatedStatus === 'active' ? '✅' : '❌'}`);
  
  // 验证数据库中的状态
  const updatedSubscription = await Subscription.findById(testSubscription._id);
  console.log(`数据库中的状态: ${updatedSubscription.status} (期望: active) - ${updatedSubscription.status === 'active' ? '✅' : '❌'}`);
  
  // 清理测试数据
  await Subscription.findByIdAndDelete(testSubscription._id);
  console.log('清理测试数据完成\n');
}

// 测试3：批量更新功能
async function testBatchUpdate() {
  console.log('=== 测试3：批量更新功能 ===');
  
  // 创建多个过期订阅
  const expiredSubscriptions = [];
  for (let i = 0; i < 3; i++) {
    const subscription = new Subscription({
      user: new mongoose.Types.ObjectId(),
      plan: 'basic',
      status: 'active',
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // 昨天过期
      price: 0,
      autoRenew: false
    });
    await subscription.save();
    expiredSubscriptions.push(subscription._id);
  }
  
  console.log(`创建了 ${expiredSubscriptions.length} 个过期订阅`);
  
  // 执行批量更新
  const updatedCount = await Subscription.updateExpiredSubscriptions();
  console.log(`批量更新了 ${updatedCount} 个订阅 (期望: 3) - ${updatedCount === 3 ? '✅' : '❌'}`);
  
  // 验证更新结果
  const updatedSubscriptions = await Subscription.find({ _id: { $in: expiredSubscriptions } });
  const allExpired = updatedSubscriptions.every(sub => sub.status === 'expired');
  console.log(`所有订阅都已更新为过期状态: ${allExpired ? '✅' : '❌'}`);
  
  // 清理测试数据
  await Subscription.deleteMany({ _id: { $in: expiredSubscriptions } });
  console.log('清理测试数据完成\n');
}

// 测试4：数据库操作测试
async function testDatabaseOperations() {
  console.log('=== 测试4：数据库操作测试 ===');
  
  // 测试创建订阅
  const subscription = new Subscription({
    user: new mongoose.Types.ObjectId(),
    plan: 'basic',
    status: 'pending',
    startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    price: 0,
    autoRenew: false
  });
  
  await subscription.save();
  console.log(`✅ 创建订阅成功: ${subscription._id}`);
  
  // 测试更新订阅
  subscription.status = 'active';
  await subscription.save();
  console.log(`✅ 更新订阅成功: ${subscription.status}`);
  
  // 测试查询订阅
  const foundSubscription = await Subscription.findById(subscription._id);
  console.log(`✅ 查询订阅成功: ${foundSubscription ? '找到' : '未找到'}`);
  
  // 测试删除订阅
  await Subscription.findByIdAndDelete(subscription._id);
  console.log(`✅ 删除订阅成功\n`);
}

// 测试5：边界条件测试
async function testEdgeCases() {
  console.log('=== 测试5：边界条件测试 ===');
  
  // 测试边界时间
  const now = new Date();
  const edgeCases = [
    {
      name: '开始时间等于当前时间',
      startDate: now,
      endDate: new Date(now.getTime() + 24 * 60 * 60 * 1000),
      expected: 'active'
    },
    {
      name: '结束时间等于当前时间',
      startDate: new Date(now.getTime() - 24 * 60 * 60 * 1000),
      endDate: now,
      expected: 'expired'
    },
    {
      name: '开始和结束时间都等于当前时间',
      startDate: now,
      endDate: now,
      expected: 'active'
    }
  ];
  
  for (const testCase of edgeCases) {
    const subscription = {
      startDate: testCase.startDate,
      endDate: testCase.endDate,
      status: 'pending'
    };
    
    const actualStatus = Subscription.calculateActualStatus(subscription);
    console.log(`${testCase.name}: ${actualStatus} (期望: ${testCase.expected}) - ${actualStatus === testCase.expected ? '✅' : '❌'}`);
  }
  
  // 测试无效数据
  try {
    const invalidSubscription = {
      startDate: 'invalid-date',
      endDate: 'invalid-date',
      status: 'pending'
    };
    
    const status = Subscription.calculateActualStatus(invalidSubscription);
    console.log(`无效日期处理: ${status} - ${status ? '✅' : '❌'}`);
  } catch (error) {
    console.log(`无效日期处理: 抛出异常 - ✅`);
  }
  
  console.log('');
}

// 运行所有测试
runAllTests(); 