const { CreditTransaction, CreditBalance } = require('./credit.model');
const User = require('../../auth/user.model');
const { createError } = require('../../../utils/error');

/**
 * 获取用户算力余额
 */
exports.getUserCreditBalance = async (req, res, next) => {
  try {
    const userId = req.params.userId;
    
    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, '用户不存在'));
    }
    
    // 查找或创建用户算力余额
    let creditBalance = await CreditBalance.findOne({ user: userId });
    if (!creditBalance) {
      creditBalance = new CreditBalance({
        user: userId,
        balance: 0,
        totalRecharged: 0,
        totalConsumed: 0
      });
      await creditBalance.save();
    }
    
    res.json(creditBalance);
  } catch (error) {
    next(createError(500, '获取用户算力余额失败', error));
  }
};

/**
 * 获取用户算力交易记录
 */
exports.getUserCreditTransactions = async (req, res, next) => {
  try {
    const userId = req.params.userId;
    const { page = 1, limit = 20, type, startDate, endDate } = req.query;
    
    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, '用户不存在'));
    }
    
    // 构建查询条件
    const query = { user: userId };
    
    // 添加类型过滤
    if (type) {
      query.type = type;
    }
    
    // 添加日期范围过滤
    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.createdAt = query.createdAt || {};
      query.createdAt.$gte = start;
    }
    
    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.createdAt = query.createdAt || {};
      query.createdAt.$lte = end;
    }
    
    // 计算总数
    const total = await CreditTransaction.countDocuments(query);
    
    // 查询交易记录
    const transactions = await CreditTransaction.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(Number(limit));
    // 补充用户名

    // 补充用户名
    const username = user.username;
    const transactionsWithUsername = transactions.map(t => {
      const transactionObj = t.toObject();
      transactionObj.username = username;
      return transactionObj;
    });




    res.json({
      total,
      page: Number(page),
      limit: Number(limit),
      data: transactionsWithUsername
    });
  } catch (error) {
    next(createError(500, '获取用户算力交易记录失败', error));
  }
};

/**
 * 管理员给用户充值算力
 */
exports.rechargeUserCredit = async (req, res, next) => {
  try {
    const { userId, amount, description } = req.body;
    const adminId = req.user._id;
    
    if (!userId || !amount || amount <= 0) {
      return next(createError(400, '用户ID和充值金额为必填项，且金额必须大于0'));
    }
    
    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, '用户不存在'));
    }
    
    // 查找或创建用户算力余额
    let creditBalance = await CreditBalance.findOne({ user: userId });
    if (!creditBalance) {
      creditBalance = new CreditBalance({
        user: userId,
        balance: 0,
        totalRecharged: 0,
        totalConsumed: 0
      });
    }
    
    // 更新余额
    const newBalance = creditBalance.balance + amount;
    creditBalance.balance = newBalance;
    creditBalance.totalRecharged += amount;
    creditBalance.lastRechargeDate = new Date();
    creditBalance.updatedAt = new Date();
    
    await creditBalance.save();
    
    // 创建交易记录
    const transaction = new CreditTransaction({
      user: userId,
      amount: amount,
      type: 'recharge',
      balance: newBalance,
      description: description || '管理员充值',
      metadata: {
        adminId,
        expiryDate: null
      },
      createdAt: new Date()
    });
    
    await transaction.save();
    
    res.status(201).json({
      message: '充值成功',
      transaction,
      balance: creditBalance
    });
  } catch (error) {
    next(createError(500, '充值算力失败', error));
  }
};

/**
 * 消费用户算力
 */
exports.consumeUserCredit = async (req, res, next) => {
  try {
    const { userId, amount, description, taskId } = req.body;
    
    if (!userId || !amount || amount <= 0) {
      return next(createError(400, '用户ID和消费金额为必填项，且金额必须大于0'));
    }
    
    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, '用户不存在'));
    }
    
    // 查找用户算力余额
    let creditBalance = await CreditBalance.findOne({ user: userId });
    if (!creditBalance) {
      return next(createError(400, '用户没有算力余额'));
    }
    
    // 检查余额是否足够
    if (creditBalance.balance < amount) {
      return next(createError(400, '用户算力余额不足'));
    }
    
    // 更新余额
    const newBalance = creditBalance.balance - amount;
    creditBalance.balance = newBalance;
    creditBalance.totalConsumed += amount;
    creditBalance.lastConsumeDate = new Date();
    creditBalance.updatedAt = new Date();
    
    await creditBalance.save();
    
    // 创建交易记录
    const transaction = new CreditTransaction({
      user: userId,
      amount: -amount,
      type: 'consume',
      balance: newBalance,
      description: description || '算力消费',
      metadata: {
        taskId
      },
      createdAt: new Date()
    });
    
    await transaction.save();
    
    res.status(201).json({
      message: '消费成功',
      transaction,
      balance: creditBalance
    });
  } catch (error) {
    next(createError(500, '消费算力失败', error));
  }
};

/**
 * 获取算力统计数据
 */
exports.getCreditStats = async (req, res, next) => {
  try {
    // 总用户数
    const totalUsers = await User.countDocuments();
    
    // 有算力的用户数
    const usersWithCredit = await CreditBalance.countDocuments({ balance: { $gt: 0 } });
    
    // 总算力余额
    const totalBalance = await CreditBalance.aggregate([
      { $group: { _id: null, total: { $sum: "$balance" } } }
    ]);
    
    // 总充值算力
    const totalRecharged = await CreditBalance.aggregate([
      { $group: { _id: null, total: { $sum: "$totalRecharged" } } }
    ]);
    
    // 总消费算力
    const totalConsumed = await CreditBalance.aggregate([
      { $group: { _id: null, total: { $sum: "$totalConsumed" } } }
    ]);
    
    // 今日充值算力
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayRecharged = await CreditTransaction.aggregate([
      { 
        $match: { 
          type: 'recharge', 
          createdAt: { $gte: today } 
        } 
      },
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ]);
    
    // 今日消费算力
    const todayConsumed = await CreditTransaction.aggregate([
      { 
        $match: { 
          type: 'consume', 
          createdAt: { $gte: today } 
        } 
      },
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ]);
    
    res.json({
      totalUsers,
      usersWithCredit,
      totalBalance: totalBalance[0]?.total || 0,
      totalRecharged: totalRecharged[0]?.total || 0,
      totalConsumed: totalConsumed[0]?.total || 0,
      todayRecharged: todayRecharged[0]?.total || 0,
      todayConsumed: Math.abs(todayConsumed[0]?.total || 0)
    });
  } catch (error) {
    next(createError(500, '获取算力统计数据失败', error));
  }
};

/**
 * 管理员扣除用户算力
 */
exports.deductUserCredit = async (req, res, next) => {
  try {
    const userId = req.params.userId;
    const { amount, description } = req.body;
    const adminId = req.user._id;
    
    if (!amount || amount <= 0) {
      return next(createError(400, '扣除金额为必填项，且金额必须大于0'));
    }
    
    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, '用户不存在'));
    }
    
    // 查找用户算力余额
    let creditBalance = await CreditBalance.findOne({ user: userId });
    if (!creditBalance) {
      return next(createError(400, '用户没有算力余额'));
    }
    
    // 检查余额是否足够
    if (creditBalance.balance < amount) {
      return next(createError(400, '用户算力余额不足'));
    }
    
    // 更新余额
    const newBalance = creditBalance.balance - amount;
    creditBalance.balance = newBalance;
    creditBalance.updatedAt = new Date();
    
    await creditBalance.save();
    
    // 创建交易记录
    const transaction = new CreditTransaction({
      user: userId,
      amount: -amount,
      type: 'deduct',
      balance: newBalance,
      description: description || '管理员扣除',
      metadata: {
        adminId
      },
      createdAt: new Date()
    });
    
    await transaction.save();
    
    res.status(201).json({
      message: '扣除成功',
      transaction,
      balance: creditBalance
    });
  } catch (error) {
    next(createError(500, '扣除算力失败', error));
  }
};

/**
 * 获取所有用户的算力交易记录
 */
exports.getAllCreditTransactions = async (req, res, next) => {
  try {
    const { page = 1, limit = 20, type, startDate, endDate, username } = req.query;
    
    // 构建查询条件
    const query = {};
    
    // 添加类型过滤
    if (type) {
      query.type = type;
    }
    
    // 添加日期范围过滤
    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.createdAt = query.createdAt || {};
      query.createdAt.$gte = start;
    }
    
    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.createdAt = query.createdAt || {};
      query.createdAt.$lte = end;
    }
    
    // 如果提供了用户名，先查找用户ID
    if (username) {
      const user = await User.findOne({ username });
      if (user) {
        query.user = user._id;
      } else {
        // 如果找不到用户，返回空结果
        return res.json({
          total: 0,
          page: Number(page),
          limit: Number(limit),
          data: []
        });
      }
    }
    
    // 计算总数
    const total = await CreditTransaction.countDocuments(query);
    
    // 查询交易记录
    const transactions = await CreditTransaction.find(query)
      .populate('user', 'username name')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(Number(limit));
    
    // 处理交易记录，添加用户名
    const processedTransactions = transactions.map(transaction => {
      const transactionObj = transaction.toObject();
      if (transactionObj.user && typeof transactionObj.user === 'object') {
        transactionObj.username = transactionObj.user.username;
        transactionObj.user = transactionObj._id;
      }
      return transactionObj;
    });
    
    res.json({
      total,
      page: Number(page),
      limit: Number(limit),
      data: processedTransactions
    });
  } catch (error) {
    next(createError(500, '获取算力交易记录失败', error));
  }
}; 