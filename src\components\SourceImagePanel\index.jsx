import React from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

/**
 * 原始图片面板组件 - 用于展示上传的原始图片和状态
 * 
 * 此组件用于高清放大页面，显示待处理的原始图片
 */
const SourceImagePanel = ({
  panel,
  onExpandClick,
  onDelete,
  onReupload,
  onStatusChange,
  isActive,
  onPanelsChange
}) => {
  // 处理上传结果
  const handleUploadResult = (results) => {
    console.log('处理上传结果:', JSON.stringify(results, null, 2));
    
    try {
      // 如果是标准的success/results格式
      if (results.success && Array.isArray(results.results)) {
        const newPanels = results.results.map((result, index) => {
          console.log(`处理结果项 #${index+1}:`, result);
          
          // 检查是否有relativePath字段
          let processedImageUrl = null;
          if (result.processedFile) {
            if (result.relativePath) {
              processedImageUrl = `http://localhost:3002${result.relativePath}`;
              console.log('使用相对路径构建URL:', processedImageUrl);
            } else {
              // 直接使用上传图片，不获取处理后的图片
              console.log('使用默认路径构建URL:', processedImageUrl);
            }
          }
          
          return {
            id: `source-${index + 1}`,
            title: '原始图片',
            status: result.error ? 'error' : 'completed',
            error: result.error || null,
            serverFileName: result.serverFileName,
            processedFile: processedImageUrl,
            fileInfo: {
              ...(result.fileInfo || {}),
              format: result.fileInfo?.type || 'image/png',
              serverFileName: result.serverFileName
            },
            showOriginal: true
          };
        });
        console.log('更新后的面板数据:', newPanels);
        onPanelsChange(newPanels);
        return;
      }

      // 处理初始的panels类型数据
      if (results.type === 'panels' && Array.isArray(results.panels)) {
        const panels = results.panels.map(panel => ({
          ...panel,
          title: '原始图片'
        }));
        onPanelsChange(panels);
        return;
      }

      throw new Error('无法识别的响应格式');

    } catch (error) {
      console.error('处理上传结果时出错:', error);
      const errorPanel = {
        id: 'source-1',
        title: '原始图片',
        status: 'error',
        error: error.message || '处理失败',
        serverFileName: results?.filename,
        showOriginal: true
      };
      onPanelsChange([errorPanel]);
    }
  };

  const handleExpandClick = (e) => {
    const buttonRect = e.currentTarget.getBoundingClientRect();
    onExpandClick(panel, {
      top: buttonRect.top,
      left: buttonRect.left + buttonRect.width
    });
  };

  const handleDelete = () => {
    if (panel && panel.componentId) {
      onDelete?.(panel.componentId);
      message.success('已删除原始图片面板');
    }
  };

  const handleReupload = () => {
    if (panel && panel.componentId) {
      onReupload?.(panel);
    }
  };

  const getImageInfo = () => {
    if (!panel || !panel.processedFile) return null;

    if (panel.fileInfo) {
      return {
        size: `${(panel.fileInfo.size / (1024 * 1024)).toFixed(2)} MB`,
        resolution: `${panel.fileInfo.width} x ${panel.fileInfo.height} px`,
        format: panel.fileInfo.format
      };
    }

    return {
      size: '--',
      resolution: '--',
      format: '--'
    };
  };

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          <ThumbnailWithPreview
            imageUrl={panel.processedFile || panel.url}
            alt="原始图片上传结果"
            status={panel.status}
            error={panel.error}
            onStatusChange={onStatusChange}
            featureName="原始图片预览"
          />
          <div className="component-text">
            <h3>{panel.title || '原始图片'}</h3>
            <div className="component-content">
              <p>
                {panel.status === 'completed' && '上传完成'}
                {panel.status === 'processing' && '处理中...'}
                {panel.status === 'uploading' && '上传中...'}
                {panel.status === 'error' && panel.error}
              </p>
            </div>
          </div>
        </div>
        <button 
          className={`expand-btn ${isActive ? 'active' : ''}`}
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

SourceImagePanel.propTypes = {
  panel: PropTypes.shape({
    componentId: PropTypes.string.isRequired,
    serverFileName: PropTypes.string,
    title: PropTypes.string.isRequired,
    status: PropTypes.oneOf(['processing', 'completed', 'error', 'uploading']).isRequired,
    error: PropTypes.string,
    processedFile: PropTypes.string,
    url: PropTypes.string,
    originalImage: PropTypes.string,
    type: PropTypes.string,
    source: PropTypes.string,
    fileInfo: PropTypes.shape({
      size: PropTypes.number,
      width: PropTypes.number,
      height: PropTypes.number,
      format: PropTypes.string,
      serverFileName: PropTypes.string,
    }),
    showOriginal: PropTypes.bool,
  }).isRequired,
  onExpandClick: PropTypes.func.isRequired,
  onDelete: PropTypes.func,
  onReupload: PropTypes.func,
  onStatusChange: PropTypes.func,
  isActive: PropTypes.bool,
  onPanelsChange: PropTypes.func.isRequired,
};

export default SourceImagePanel; 