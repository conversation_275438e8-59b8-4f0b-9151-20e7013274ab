/**
 * 快速插入工作流脚本
 * 
 * 使用方法:
 * node server/src/scripts/quickInsertWorkflows.js
 * 
 * 这个脚本会:
 * 1. 自动扫描 apiJson 目录中的所有 JSON 文件
 * 2. 根据文件名生成工作流记录
 * 3. 插入到数据库中
 * 4. 跳过已存在的工作流
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 导入模型
const Workflow = require('../models/Workflow');
const User = require('../modules/auth/user.model');

// 简化的工作流配置
const WORKFLOW_CONFIG = {
  // A系列 - 款式设计
  'A01-trending': { name: '爆款开发', category: '款式设计', priority: 90 },
  'A02-optimize': { name: '款式优化', category: '款式设计', priority: 70 },
  'A02b-optimizetext': { name: '文本优化', category: '款式设计', priority: 1 },
  'A03-inspiration': { name: '灵感探索', category: '款式设计', priority: 45 },
  'A05-drawing': { name: '绘图设计', category: '款式设计', priority: 10 },
  'A06-divergent': { name: '发散创意', category: '款式设计', priority: 8 },
  
  // B系列 - 模特图
  'B01-fashion': { name: '时尚大片', category: '模特图', priority: 75 },
  'B02-tryonauto': { name: '自动换装', category: '模特图', priority: 85 },
  'B02-tryonmanual': { name: '手动换装', category: '模特图', priority: 25 },
  'B02-tryonother': { name: '其他换装', category: '模特图', priority: 1 },
  'B03-changemodel': { name: '换模特', category: '模特图', priority: 40 },
  'B04-recolor': { name: '重新配色', category: '模特图', priority: 60 },
  'B05-fabric': { name: '面料生成', category: '模特图', priority: 35 },
  'B06-background': { name: '背景处理', category: '模特图', priority: 50 },
  'B07-virtual': { name: '虚拟展示', category: '模特图', priority: 15 },
  'B08-detailmigration': { name: '细节迁移', category: '模特图', priority: 6 },
  'B09-handfix': { name: '手部修复', category: '模特图', priority: 4 },
  'B10-changeposture': { name: '姿态调整', category: '模特图', priority: 2 },
  
  // C系列 - 工具
  'C01-extract': { name: '文本提取', category: '工具', priority: 30 },
  'C02-upscale': { name: '图像放大', category: '工具', priority: 55 },
  'C03-mattingbg': { name: '背景抠图', category: '工具', priority: 80 },
  'C03-mattingbgfile': { name: '背景抠图(文件)', category: '工具', priority: 1 },
  'C03-mattingclo': { name: '服装抠图', category: '工具', priority: 1 },
  'C04-extend': { name: '图像扩展', category: '工具', priority: 20 }
};

async function main() {
  try {
    console.log('🚀 开始快速插入工作流...\n');

    // 连接数据库
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aibikini');
    console.log('✅ 数据库连接成功');

    // 获取管理员用户
    const adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      throw new Error('未找到管理员用户');
    }
    console.log(`👤 使用管理员用户: ${adminUser.username}`);

    // 扫描JSON文件
    const jsonDir = path.resolve(__dirname, '../services/comfyClient/apiJson');
    const files = fs.readdirSync(jsonDir);
    const jsonFiles = files.filter(file => file.endsWith('.json'));
    
    console.log(`📁 找到 ${jsonFiles.length} 个JSON文件`);

    let insertCount = 0;
    let skipCount = 0;
    let errorCount = 0;

    // 处理每个文件
    for (const filename of jsonFiles) {
      const workflowId = path.basename(filename, '.json');
      const config = WORKFLOW_CONFIG[workflowId];
      
      if (!config) {
        console.log(`⚠️  跳过未配置的工作流: ${workflowId}`);
        skipCount++;
        continue;
      }

      try {
        // 检查是否已存在
        const existing = await Workflow.findOne({ id: workflowId });
        if (existing) {
          console.log(`⏭️  工作流已存在: ${workflowId}`);
          skipCount++;
          continue;
        }

        // 创建工作流
        const workflow = new Workflow({
          id: workflowId,
          name: config.name,
          displayName: config.name,
          category: config.category,
          description: `${config.name}工作流 - 基于ComfyUI实现`,
          version: '1.0.0',
          enabled: true,
          parameters: new Map(),
          supportedPlatforms: ['comfyui'],
          recommendedPlatform: 'comfyui',
          tags: [config.category.replace('类', ''), 'ComfyUI'],
          priority: config.priority,
          createdBy: adminUser._id,
          usageStats: {
            totalRuns: 0,
            successRuns: 0,
            failedRuns: 0
          }
        });

        await workflow.save();
        console.log(`✅ 插入工作流: ${workflowId} - ${config.name}`);
        insertCount++;

      } catch (error) {
        console.error(`❌ 插入工作流 ${workflowId} 失败:`, error.message);
        errorCount++;
      }
    }

    // 显示结果
    console.log('\n📊 插入结果:');
    console.log(`   新增: ${insertCount} 个`);
    console.log(`   跳过: ${skipCount} 个`);
    console.log(`   失败: ${errorCount} 个`);
    console.log(`   总计: ${jsonFiles.length} 个文件`);

    // 显示分类统计
    const workflows = await Workflow.find({}).select('category');
    const categoryStats = {};
    workflows.forEach(w => {
      categoryStats[w.category] = (categoryStats[w.category] || 0) + 1;
    });

    console.log('\n📈 当前数据库中的工作流分类统计:');
    Object.entries(categoryStats).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} 个`);
    });

    console.log('\n🎉 快速插入完成!');

  } catch (error) {
    console.error('\n💥 执行失败:', error.message);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = main;
