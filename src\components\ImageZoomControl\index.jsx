import React, { useState, useEffect, useRef } from 'react';
import { MdOutlineZoomOutMap } from 'react-icons/md';
import './index.css';

/**
 * 图片缩放控制组件
 * 
 * @param {number} initialScale - 初始缩放比例（相对于图片真实分辨率的百分比）
 * @param {function} onScaleChange - 缩放比例变化时的回调函数
 * @param {function} onReset - 复位按钮点击时的回调函数
 * @param {function} onPreview - 预览按钮点击时的回调函数
 * @param {Object} imagePosition - 图片当前位置，用于判断是否需要复位
 * @param {number} compareWidthRatio - 分割线当前位置
 * @param {number} defaultCompareWidthRatio - 分割线默认位置
 */
const ImageZoomControl = ({ 
  initialScale, 
  onScaleChange,
  onReset,
  onPreview,
  imagePosition = { x: 0, y: 0 },
  compareWidthRatio,
  defaultCompareWidthRatio = 50
}) => {
  // 记录initialScale值，帮助调试
  // console.log('ImageZoomControl received initialScale:', initialScale);
  
  // 使用useRef记录第一次收到的有效初始比例
  const initialValidScaleRef = useRef(null);
  
  // 如果我们收到了有效的初始比例，记录它
  useEffect(() => {
    if (initialScale && initialScale > 0 && initialValidScaleRef.current === null) {
      // console.log('ImageZoomControl - Recording initial valid scale:', initialScale);
      initialValidScaleRef.current = initialScale;
    }
  }, [initialScale]);
  
  // 选择要显示的缩放比例：优先使用传入的initialScale
  const displayScale = initialScale || initialValidScaleRef.current || 100;
  // console.log('ImageZoomControl - Using displayScale:', displayScale);
  
  // 预设的缩放比例（相对于图片真实分辨率）
  // 100%表示图片以真实分辨率1:1显示
  const SCALE_LEVELS = [25, 50, 75, 100, 125, 150, 175, 200];
  
  // 当前缩放比例状态
  const [currentScale, setCurrentScale] = useState(displayScale);
  
  // 计算所有可用的缩放级别（包括初始比例）
  const [availableScales, setAvailableScales] = useState([]);
  
  // 当initialScale变化时更新组件状态
  useEffect(() => {
    // console.log('ImageZoomControl initialScale changed to:', initialScale);
    
    // 确保有有效的缩放比例
    const validScale = initialScale > 0 ? initialScale : 100;
    // console.log('ImageZoomControl - Using validScale:', validScale);
    
    // 将初始比例添加到预设比例中，并排序
    const scales = [...new Set([...SCALE_LEVELS, validScale])].sort((a, b) => a - b);
    console.log('Available scales:', scales);
    setAvailableScales(scales);
    
    // 设置当前缩放比例，确保显示的是最新传入的initialScale
    // 只在initialScale变化时才更新当前缩放值，避免拖动图片时重置
    setCurrentScale(validScale);
  }, [initialScale]); // 移除imagePosition依赖项，避免拖动时重置缩放

  // 获取下一个缩放级别
  const getNextScale = (current, isZoomIn) => {
    const currentIndex = availableScales.indexOf(current);
    if (isZoomIn) {
      return currentIndex < availableScales.length - 1 ? availableScales[currentIndex + 1] : current;
    } else {
      return currentIndex > 0 ? availableScales[currentIndex - 1] : current;
    }
  };

  // 处理缩小
  const handleZoomOut = () => {
    const newScale = getNextScale(currentScale, false);
    console.log('Zoom out to:', newScale);
    setCurrentScale(newScale);
    onScaleChange(newScale);
  };

  // 处理放大
  const handleZoomIn = () => {
    const newScale = getNextScale(currentScale, true);
    console.log('Zoom in to:', newScale);
    setCurrentScale(newScale);
    onScaleChange(newScale);
  };

  // 处理复位
  const handleReset = () => {
    // 确保直接使用传入的initialScale作为复位值
    const resetScale = initialScale;
    console.log('Reset to initialScale:', resetScale);
    
    // 立即更新组件内部状态
    setCurrentScale(resetScale);
    
    // 通知父组件
    onScaleChange(resetScale);
    
    // 调用父组件的复位函数
    onReset?.();
  };

  // 判断是否需要启用复位按钮
  const shouldEnableReset = () => {
    // 检查缩放比例是否需要复位（当前缩放不等于初始缩放）
    const scaleNeedsReset = currentScale !== initialScale;
    // 检查图片位置是否需要复位（偏移不为0）
    const positionNeedsReset = Math.abs(imagePosition.x) > 0.1 || Math.abs(imagePosition.y) > 0.1;
    // 检查分割线位置是否需要复位（不等于默认位置）
    const compareWidthNeedsReset = Math.abs(compareWidthRatio - defaultCompareWidthRatio) > 0.1;
    
    return scaleNeedsReset || positionNeedsReset || compareWidthNeedsReset;
  };

  return (
    <div className="image-zoom-control">
      <div className="zoom-scale">
        <span>{currentScale}%</span>
      </div>
      <div className="zoom-buttons">
        <div className="zoom-buttons-row">
          <button 
            className="zoom-button zoom-out" 
            onClick={handleZoomOut}
            disabled={currentScale === availableScales[0]}
            title="缩小"
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <rect x="7" y="11" width="10" height="2" />
            </svg>
          </button>
          <button 
            className="zoom-button zoom-in" 
            onClick={handleZoomIn}
            disabled={currentScale === availableScales[availableScales.length - 1]}
            title="放大"
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <rect x="7" y="11" width="10" height="2" />
              <rect x="11" y="7" width="2" height="10" />
            </svg>
          </button>
        </div>
        <button 
          className="zoom-button reset" 
          onClick={handleReset}
          disabled={!shouldEnableReset()}
          title="复位"
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M17.65 6.35A7.958 7.958 0 0 0 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8v-3l4 4-4 4v-3c-5.52 0-10-4.48-10-10S6.48 2 12 2c3.31 0 6.24 1.61 8.04 4.09l-2.39 2.39z" />
          </svg>
        </button>
        <button 
          className="zoom-button preview" 
          onClick={onPreview}
          title="预览图片"
        >
          <MdOutlineZoomOutMap />
        </button>
      </div>
    </div>
  );
};

export default ImageZoomControl; 