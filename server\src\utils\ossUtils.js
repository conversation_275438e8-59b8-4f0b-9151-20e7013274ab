const Config = require('config');
const AliyunOSS = require('ali-oss');
const ossConfig = Config.get('ossConfig');
const OSS = require('ali-oss');
const axios = require('axios');
const path = require('path');
const sizeOf = require('image-size');
const probe = require('probe-image-size');
const {removeComfyUIMetadata} = require('./image')
class OSSImageUtils {
    constructor() {
        this.client = new OSS({
            region: ossConfig.region,
            accessKeyId: ossConfig.accessKeyId,
            accessKeySecret: ossConfig.accessKeySecret,
            bucket: ossConfig.bucket,
            // secure: true // 强制使用HTTPS
        });

        console.log("文件存储配置信息")
        console.log({
            region: ossConfig.region,
            accessKeyId: ossConfig.accessKeyId,
            accessKeySecret: ossConfig.accessKeySecret,
            bucket: ossConfig.bucket,
            // secure: true // 强制使用HTTPS
        })
    }

    async streamToBuffer(stream) {
        return new Promise((resolve, reject) => {
            const chunks = [];
            stream.on('data', (chunk) => chunks.push(chunk));
            stream.on('end', () => resolve(Buffer.concat(chunks)));
            stream.on('error', reject);
        });
    }
    async getImageDimensionsFromStream(stream) {
        return new Promise((resolve, reject) => {
            probe(stream)
                .then(dimensions => resolve(dimensions))
                .catch(reject);
        });
    }
    /**
     * 将容器里的图片转存到OSS
     * @param {string} imageUrl 原始图片URL
     * @param {string} [savePath='images/'] 保存路径
     * @param {boolean} [isPublic=true] 是否公开可读
     * @returns {Promise<{url: string, key: string}>} 返回访问URL和OSS key
     */
    async transferImage(imageUrl,comfyClient, savePath = 'images/', isPublic = true) {
        try {
            // 1. 下载原始图片
            const response =await comfyClient.getFile(imageUrl);
            let buffer = Buffer.from(await this.streamToBuffer(response.data)); // 转为 Buffer
            
            // 2. 处理图片，移除ComfyUI元数据
            const filename = path.basename(imageUrl);
            const extname = path.extname(filename).toLowerCase();
            if (extname === '.png') {
                try {
                    buffer = await removeComfyUIMetadata(buffer);
                    console.log(`图片 ${filename} 元数据处理完成`);
                } catch (error) {
                    console.error(`处理图片 ${filename} 时出错:`, error.message);
                    // 如果处理失败，继续使用原始buffer
                }
            }
            
            // 3. 获取图片尺寸信息
            const dimensions = sizeOf(buffer);
            // 2. 获取文件大小,不通过该header
            const fileSize = buffer.length;

            const fileInfo = {
                size: fileSize,
                mimetype: response.headers['content-type'], 
                originalname: path.basename(imageUrl),
                ...dimensions
            };

            // 2. 生成OSS文件名
            const ossExtname = path.extname(imageUrl.split('?')[0]) || '.jpg';
            const ossFilename = `${Date.now()}${Math.floor(Math.random() * 1000)}${ossExtname}`;
            const objectKey = `${savePath}${ossFilename}`;

            // 3. 上传到OSS
            const uploadOptions = {
                headers: {
                    'Content-Type': response.headers['content-type'] || 'image/jpeg',
                    'x-oss-object-acl': isPublic ? 'public-read' : 'private'
                }
            };

            const result = await this.client.put(objectKey, buffer, uploadOptions);

            // 4. 返回访问URL
            let accessUrl = isPublic
                ? result.url
                : this.client.signatureUrl(objectKey, { expires: 3600 * 24 * 365*10 }); // 1年有效期
            if(accessUrl.includes('http://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/')){
                accessUrl = accessUrl.replace('http://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/', 'https://file.aibikini.cn/');
            }
                if(accessUrl.includes('https://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/')){
                accessUrl = accessUrl.replace('https://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/', 'https://file.aibikini.cn/');
            }
            return {
                url: accessUrl,
                key: objectKey,
                ...fileInfo
            };
        } catch (error) {
            console.error('图片转存失败:', error);
            throw new Error(`图片转存失败: ${error.message}`);
        }
    }

    /**
     * 批量转存图片
     * @param {string[]} imageUrls 图片URL数组
     * @param {Object} comfyClient ComfyUI客户端实例
     * @param {string} [savePath='images/'] 保存路径
     * @param {boolean} [isPublic=true] 是否公开可读
     * @returns {Promise<Array<{url: string, key: string, originalUrl: string}>>}
     */
    async batchTransferImages(imageUrls, comfyClient, savePath = 'images/', isPublic = true) {
        const results = [];

        for (const url of imageUrls) {
            try {
                const { url: ossUrl, key } = await this.transferImage(url, comfyClient, savePath, isPublic);
                results.push({
                    originalUrl: url,
                    url: ossUrl,
                    key: key,
                    success: true
                });
            } catch (error) {
                results.push({
                    originalUrl: url,
                    error: error.message,
                    success: false
                });
            }
        }

        return results;
    }

    /**
     * 上传文件到OSS
     * @param {string} filePath 本地文件路径
     * @param {string} ossPath OSS存储路径
     * @returns {Promise<string>} 返回OSS访问URL
     */
    async uploadFile(filePath, ossPath, isPublic = true) {
        try {
            // 文件上传设置可以公开访问
            const uploadOptions = {
                headers: {
                    'x-oss-object-acl': isPublic ? 'public-read' : 'private'
                }
            };
            const result = await this.client.put(ossPath, filePath, uploadOptions);
            return result.url;
        } catch (error) {
            console.error('文件上传失败:', error);
            throw new Error(`文件上传失败: ${error.message}`);
        }
    }
}

// 使用示例
// (async () => {
//     const ossConfig = {
//         region: 'oss-cn-hangzhou',
//         accessKeyId: 'your-access-key-id',
//         accessKeySecret: 'your-access-key-secret',
//         bucket: 'your-bucket-name'
//     };
//
//     const transfer = new OSSImageTransfer(ossConfig);
//
//     try {
//         // 单张图片转存
//         const result = await transfer.transferImage(
//             'https://example.com/sample.jpg',
//             'web-images/'
//         );
//         console.log('转存成功:', result.url);
//
//         // 批量转存
//         const batchResults = await transfer.batchTransferImages([
//             'https://example.com/image1.jpg',
//             'https://example.com/image2.png'
//         ]);
//         console.log('批量转存结果:', batchResults);
//     } catch (error) {
//         console.error('操作失败:', error);
//     }
// })();
module.exports = new OSSImageUtils();


