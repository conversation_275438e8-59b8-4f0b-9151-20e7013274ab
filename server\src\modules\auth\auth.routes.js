const express = require('express');
const MongoDBCache = require('../../utils/cacheUtils');
const router = express.Router();
const jwt = require('jsonwebtoken');
const User = require('./user.model'); // 更新为新的模型路径
const { auth, requireRole } = require('./auth.middleware'); // 更新为新的中间件路径
const { createError } = require('../../utils/error');
const bcrypt = require('bcryptjs');
const AliyunSmsUtil = require('../../utils/smsUtils');
const Config = require('config');
const Subscription = require('../admin/subscribe/subscription.model');
const cache = new MongoDBCache({
  collectionName: 'verification_code'
});
// 用户注册
router.post('/register', async (req, res, next) => {
  try {
    const { username, phone, password,verifyCode } = req.body;
    // 使用mongodb 缓存 验证码
    const cachedCode = await cache.get(phone);
    if (!cachedCode) {
      throw createError(400, '验证码已过期');
    } else if (cachedCode !== verifyCode) {
      throw createError(400, '验证码错误');
    }
    // 验证码删除
    await cache.del(phone);
    
    // 验证密码长度
    if (!password || password.length < 8 || password.length > 32) {
      throw createError(400, '密码长度必须在8-32位之间');
    }
    
    // 检查用户名是否已存在
    const existingUsername = await User.findOne({ username });
    if (existingUsername) {
      throw createError(400, '用户名已被注册');
    }
    
    // 检查手机号是否已存在
    const existingPhone = await User.findOne({ phone });
    if (existingPhone) {
      throw createError(400, '手机号已被注册');
    }

    // 创建用户，使用用户名作为name字段的默认值
    const user = new User({ 
      username, 
      phone, 
      password,
      name: username // 使用用户名作为姓名的默认值
    });
    await user.save();
    // 为新用户创建免费的无限期订阅
    const freeSubscription = new Subscription({
      user: user._id,
      type: 'free',
      name: '免费订阅',
      price: 0,
      startDate: new Date(),
      endDate: new Date(new Date().getTime() + 1000 * 60 * 60 * 24 * 365 * 100),
      status: 'active',
      features: ['基础功能访问']
    });
    await freeSubscription.save();
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: '7d'
    });

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          phone: user.phone,
          name: user.name,
          role: user.role
        },
        token
      }
    });
  } catch (error) {
    next(error);
  }
});
// 发送验证码
router.post('/send-verification-code', async (req, res, next) => {
  try {
    const { phone } = req.body;
    // 检查验证码
    const cachedCode = await cache.get(phone);
    if (cachedCode) {
      throw createError(400, '验证码已发送，请稍后再试');
    } 

    // 生成验证码
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    // 使用mongodb 缓存 验证码
    await cache.set(phone, verificationCode, 1000 * 60 * 5);
    // 发送验证码
    await AliyunSmsUtil.sendSms(phone,
      Config.get('aliyunSms.templateCode'),
      {
        code: verificationCode
      }
    );

    res.json({
      success: true,
      message: '验证码发送成功'
    }); 
  } catch (error) {
    next(error);
  }
});


// 用户登录
router.post('/login', async (req, res, next) => {
  try {
    const { loginId, password } = req.body;
    
    // 使用用户名或手机号查找用户
    const user = await User.findOne({
      $or: [
        { username: loginId },
        { phone: loginId }
      ]
    });

    if (!user || !(await user.comparePassword(password))) {
      return res.status(401).json({
        success: false,
        status: 'fail',
        message: '账号或密码错误'
      });
    }

    if (user.status !== 'active') {
      return res.status(403).json({
        success: false,
        status: 'fail',
        message: '账号已被禁用'
      });
    }

    // 获取真实IP地址
    const ip = req.headers['x-forwarded-for']?.split(',')[0] || req.ip;
    const userAgent = req.headers['user-agent'];

    // 生成新的JWT令牌
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: '7d'
    });

    // 添加新的会话
    await user.addSession(token, {
      userAgent,
      ip
    });

    // 更新登录历史
    const loginHistory = {
      ip,
      timestamp: new Date(),
      userAgent
    };

    // 更新用户登录信息
    await user.updateOne({
      $set: {
        lastLoginAt: loginHistory.timestamp,
        lastLoginIp: ip
      },
      $push: {
        loginHistory: {
          $each: [loginHistory],
          $slice: -10 // 只保留最近10条记录
        }
      }
    });

    // 获取上次登录信息
    const lastLoginInfo = user.loginHistory && user.loginHistory.length > 1 
      ? user.loginHistory[user.loginHistory.length - 2] 
      : null;

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          phone: user.phone,
          name: user.name,
          role: user.role
        },
        token,
        lastLoginInfo: lastLoginInfo ? {
          ip: lastLoginInfo.ip,
          time: lastLoginInfo.timestamp,
          userAgent: lastLoginInfo.userAgent
        } : null
      }
    });
  } catch (error) {
    next(error);
  }
});

// 申请开发者权限 - 已禁用直接申请功能
router.post('/developer/apply', auth, requireRole('admin'), async (req, res, next) => {
  try {
    const { userId, purpose, website } = req.body;
    
    // 管理员为指定用户授予开发者权限
    if (!userId) {
      throw createError(400, '需要提供用户ID');
    }
    
    const targetUser = await User.findById(userId);
    if (!targetUser) {
      throw createError(404, '用户不存在');
    }
    
    if (targetUser.role === 'developer') {
      throw createError(400, '该用户已经是开发者了');
    }

    // 更新用户角色为开发者
    targetUser.role = 'developer';
    targetUser.developer = {
      isVerified: true,
      allowedOrigins: website ? [website] : []
    };

    await targetUser.save();

    res.json({
      success: true,
      message: '已成功为用户授予开发者权限'
    });
  } catch (error) {
    next(error);
  }
});

// 生成开发者API密钥（需要开发者权限）
router.post('/developer/keys', auth, requireRole('developer'), async (req, res, next) => {
  try {
    const keys = await req.user.generateDeveloperKeys();
    
    res.json({
      success: true,
      data: keys
    });
  } catch (error) {
    next(error);
  }
});

// 获取当前用户信息
router.get('/me', auth, async (req, res) => {
  res.json({
    success: true,
    data: {
      user: {
        id: req.user._id,
        name: req.user.name,
        username: req.user.username,
        role: req.user.role,
        phone: req.user.phone,
        developer: req.user.role === 'developer' ? {
          isVerified: req.user.developer.isVerified,
          allowedOrigins: req.user.developer.allowedOrigins
        } : undefined
      }
    }
  });
});

// 更新用户资料
router.put('/user/profile', auth, async (req, res, next) => {
  try {
    const { username } = req.body;
    
    // 验证数据
    if (username && username.trim() === '') {
      throw createError(400, '用户名不能为空');
    }
    
    // 检查用户名是否已被其他用户使用
    if (username) {
      const existingUser = await User.findOne({ 
        username, 
        _id: { $ne: req.user._id } 
      });
      
      if (existingUser) {
        throw createError(400, '该用户名已被使用');
      }
      
      // 更新用户名
      req.user.username = username;
    }
    
    // 保存更改
    await req.user.save();
    
    res.json({
      success: true,
      message: '用户资料更新成功',
      data: {
        user: {
          id: req.user._id,
          username: req.user.username,
          phone: req.user.phone,
          name: req.user.name,
          role: req.user.role
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// 退出登录
router.post('/logout', auth, async (req, res) => {
  try {
    // 从用户的活跃会话中移除当前会话
    await req.user.removeSession(req.token);
    
    res.json({
      success: true,
      message: '已退出登录'
    });
  } catch (error) {
    console.error('退出登录失败:', error);
    res.status(500).json({
      success: false,
      message: '退出登录失败'
    });
  }
});

// 添加临时的管理员创建路由
router.get('/generate-admin', async (req, res) => {
  try {
    // 检查是否已存在管理员账号
    const existingAdmin = await User.findOne({ role: 'admin' });
    if (existingAdmin) {
      return res.json({
        success: false,
        message: '管理员账号已存在'
      });
    }

    // 创建管理员账号
    const admin = new User({
      username: "admin",
      phone: "13800138000",
      password: "admin123", // 密码会通过中间件自动加密
      name: "系统管理员",
      role: "admin",
      status: "active",
      createdAt: new Date(),
      lastLoginAt: new Date()
    });

    await admin.save();

    res.json({
      success: true,
      message: '管理员账号创建成功',
      credentials: {
        username: "admin",
        phone: "13800138000",
        password: "admin123"  // 明文密码仅在开发环境显示
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});
// 手机验证码验证修改密码接口实现
router.post('/user/password', auth, async (req, res, next) => {
  try {
    const { phone, code, newPassword } = req.body;

    // 验证新密码长度
    if (!newPassword || newPassword.length < 8 || newPassword.length > 32) {
      throw createError(400, '新密码长度必须在8-32位之间');
    }

    // 验证手机号是否存在
    const existingUser = await User.findOne({ phone });
    if (!existingUser) {
      throw createError(400, '手机号不存在');
    }

    // 验证验证码
    const smsUtil = AliyunSmsUtil;  
    await smsUtil.verifyCode(phone, code);

    // 更新密码
    existingUser.password = newPassword;
    
    // 清除所有活跃会话
    existingUser.activeSessions = [];
    
    await existingUser.save();

    res.json({  
      success: true,
      message: '密码修改成功，所有设备已退出登录'
    });
  } catch (error) {
    next(error);
  }
});

// reset-password
router.post('/reset-password',async (req, res, next) => {
  try {
    const { phone, verificationCode, newPassword } = req.body;

    // 验证新密码长度 
    if (!newPassword || newPassword.length < 8 || newPassword.length > 32) {
      throw createError(400, '新密码长度必须在8-32位之间');
    }

    // 验证手机号是否存在
    const existingUser = await User.findOne({ phone }); 
    if (!existingUser) {
      throw createError(400, '手机号不存在');
    }

    // 验证验证码
    const cachedCode = await cache.get(phone);
    if (!cachedCode) {
      throw createError(400, '验证码已过期');
    } else if (cachedCode !== verificationCode) {
      throw createError(400, '验证码错误');
    } 

    // 更新密码
    existingUser.password = newPassword;
    // 清除所有活跃会话
    existingUser.activeSessions = [];
    await existingUser.save();

    res.json({
      success: true,
      message: '密码修改成功'
    }); 
  } catch (error) {
    next(error);
  }
});


// 添加验证码相关路由
router.get('/captcha/get', async (req, res) => {
  try {
    const svgCaptcha = require('svg-captcha');
    const captcha = svgCaptcha.create({
      size: 6,           // 验证码长度
      ignoreChars: '0o1il', // 排除容易混淆的字符
      noise: 3,          // 干扰线条数
      color: true,       // 验证码字符将有不同的颜色
      background: '#f5f5f5', // 背景色
      width: 200,        // 宽度
      height: 60         // 高度
    });

    // 将验证码存储在 session 中
    req.session.captcha = captcha.text.toLowerCase();
    
    res.type('svg');
    res.status(200).send(captcha.data);
  } catch (error) {
    console.error('生成验证码失败:', error);
    res.status(500).json({
      success: false,
      message: '生成验证码失败'
    });
  }
});

router.post('/captcha/verify', async (req, res) => {
  try {
    const { code } = req.body;
    
    // 验证验证码 判断手机短信验证开关

      if (!code&&process.env.ENABLE_PHONE_VERIFICATION === 'true') {
      return res.status(400).json({
        success: false,
        message: '请输入验证码'
      });
    }

    // 从 session 中获取验证码
    const savedCode = req.session.captcha;
    
    if (!savedCode) {
      return res.status(400).json({
        success: false,
        message: '验证码已过期'
      });
    }

    // 验证码比对（不区分大小写）
    const isValid = code.toLowerCase() === savedCode;
    
    // 验证完成后，清除 session 中的验证码
    req.session.captcha = null;

    if (!isValid) {
      return res.status(400).json({
        success: false,
        message: '验证码错误'
      });
    }

    res.json({
      success: true,
      message: '验证成功'
    });
  } catch (error) {
    console.error('验证码验证失败:', error);
    res.status(500).json({
      success: false,
      message: '验证码验证失败'
    });
  }
});

// 添加一个测试CSRF的路由
router.post('/test-csrf', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'CSRF验证通过',
    timestamp: new Date().toISOString()
  });
});
// 验证验证码 verify-code
router.post('/verify-code', async (req, res, next) => {
  try {
    const { phone, code } = req.body;
    // 使用mongodb 缓存 验证码
    const cachedCode = await cache.get(phone);
    if (!cachedCode) {
      throw createError(400, '验证码已过期');
    } else if (cachedCode !== code) {
      throw createError(400, '验证码错误');
    } 
    res.json({
      success: true,
      message: '验证码验证成功'
    });
  } catch (error) {
    next(error);
  }
}); 

// 获取用户设备列表
router.get('/admin/user/:userId/devices', auth, requireRole('admin'), async (req, res, next) => {
  try {
    const user = await User.findById(req.params.userId);
    if (!user) {
      throw createError(404, '用户不存在');
    }

    res.json({
      success: true,
      data: {
        devices: user.activeSessions.map(session => ({
          token: session.token,
          deviceInfo: session.deviceInfo,
          lastActive: session.lastActive
        }))
      }
    });
  } catch (error) {
    next(error);
  }
});

// 踢出用户设备
router.post('/admin/user/:userId/kick-device', auth, requireRole('admin'), async (req, res, next) => {
  try {
    const { token } = req.body;
    const user = await User.findById(req.params.userId);
    
    if (!user) {
      throw createError(404, '用户不存在');
    }

    await user.removeSession(token);

    res.json({
      success: true,
      message: '设备已踢出'
    });
  } catch (error) {
    next(error);
  }
});

// 更新用户最大登录设备数
router.put('/admin/user/:userId/max-sessions', auth, requireRole('admin'), async (req, res, next) => {
  try {
    const { maxSessions } = req.body;
    const user = await User.findById(req.params.userId);
    
    if (!user) {
      throw createError(404, '用户不存在');
    }

    // 验证最大设备数
    if (maxSessions < 1 || maxSessions > 5) {
      throw createError(400, '最大登录设备数必须在1-5之间');
    }

    // 如果当前活跃会话数超过新的限制，删除多余的会话
    if (user.activeSessions.length > maxSessions) {
      user.activeSessions.sort((a, b) => a.lastActive - b.lastActive);
      user.activeSessions = user.activeSessions.slice(-maxSessions);
    }

    user.maxSessions = maxSessions;
    await user.save();

    res.json({
      success: true,
      message: '最大登录设备数更新成功'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router; 