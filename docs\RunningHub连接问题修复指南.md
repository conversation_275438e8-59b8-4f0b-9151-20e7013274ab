# RunningHub 连接问题修复指南

## 问题分析

根据错误信息分析，主要问题是：

1. **错误的URL连接**：脚本尝试连接到 `https://www.runninghub.cn` 而不是正确的 `netWssUrl`
2. **平台识别错误**：任务可能被错误识别为ComfyUI任务而不是RunningHub任务
3. **数据传递问题**：`netWssUrl` 可能没有正确传递到Python脚本

## 错误信息解读

```
为任务 01175362530856327 创建ComfyUI连接失败: timed out during opening handshake
无法连接到ComfyUI实例: https://www.runninghub.cn
```

这表明：
- 任务被当作ComfyUI任务处理
- 使用了错误的URL（`https://www.runninghub.cn` 而不是 `netWssUrl`）

## 修复方案

### 1. 确保前端正确发送平台信息

#### **正确的WebSocket订阅消息格式：**
```javascript
const subscribeMessage = {
  type: "subscribe_task",
  task_id: "01175362530856327",
  prompt_id: "1949465676051324929",
  instance_ws_url: "wss://www.runninghub.cn",  // 这个可以是基础URL
  platform: "runninghub",  // 🔑 关键：必须指定平台
  task_data: {
    netWssUrl: "wss://www.runninghub.cn:443/ws/c_instance?c_host=***************&c_port=87&clientId=88f6947dc37e94b20654a5c5e67fce69&workflowId=1948587667540836353&Rh-Comfy-Auth=eyJ1c2VySWQiOiI2NTRmNWY1OTU2YTM1MWE0OWY4ZjUyYjQ5Y2E3NmI4ZiIsInNpZ25FeHBpcmUiOjE3NTQyMjg2MTExNTMsInRzIjoxNzUzNjIzODExMTUzLCJzaWduIjoiZmU3YThkNWIxMGM2NmVmMDNhZjliYjg4NGFkZWQwN2YifQ%3D%3D&target=http://myfj.runninghub.cn",
    taskId: "1949465676051324929",
    clientId: "88f6947dc37e94b20654a5c5e67fce69",
    taskStatus: "RUNNING"
  }
};

ws.send(JSON.stringify(subscribeMessage));
```

### 2. 检查Node.js后端数据传递

#### **确保executeResult包含正确信息：**
```javascript
// 在 UnifiedWorkflowService.js 中
return {
  success: true,
  platform: 'runninghub',
  promptId: runningHubTaskId,
  instanceId: 'runninghub',
  url: 'https://www.runninghub.cn',
  taskId: runningHubTaskId,
  // 🔑 关键：确保包含RunningHub特有信息
  netWssUrl: createResult.netWssUrl,
  clientId: createResult.clientId,
  taskStatus: createResult.taskStatus,
  promptTips: createResult.promptTips,
  promptTipsData: createResult.promptTipsData,
  runningHubService: runningHubService
};
```

#### **在异步处理中正确传递数据：**
```javascript
// 在 comfyUI.routes.js 中
processWorkflowResultAsync(
  executeResult.platform,        // 'runninghub'
  executeResult.promptId,        // RunningHub任务ID
  executeResult.instanceId,      // 'runninghub'
  workflowName,
  taskId,
  executeResult.url,             // 基础URL
  executeResult.client,          // runningHubService实例
  executeResult                  // 🔑 完整的executeResult，包含netWssUrl等
);
```

### 3. Python脚本调试增强

已添加的调试信息将帮助诊断问题：

```python
# 在 subscribe_task 方法中
print(f"订阅任务: task_id={task_id}, platform={platform}, instance_ws_url={instance_ws_url}")
print(f"task_data: {task_data}")

# 在 listen_to_runninghub_instance 方法中
print(f"RunningHub任务 {task_id} 的task_data: {task_data}")
print(f"提取的netWssUrl: {netWssUrl}")
```

### 4. 前端集成修复

#### **在前端WebSocket处理中：**
```javascript
// 确保在订阅RunningHub任务时传递正确信息
function subscribeToRunningHubTask(taskId, executeResult) {
  const subscribeMessage = {
    type: "subscribe_task",
    task_id: taskId,
    prompt_id: executeResult.promptId,
    instance_ws_url: executeResult.url,
    platform: "runninghub",  // 🔑 明确指定平台
    task_data: {
      netWssUrl: executeResult.netWssUrl,
      clientId: executeResult.clientId,
      taskStatus: executeResult.taskStatus,
      promptTips: executeResult.promptTips
    }
  };
  
  console.log('发送RunningHub订阅消息:', subscribeMessage);
  progressWs.send(JSON.stringify(subscribeMessage));
}
```

### 5. 验证步骤

#### **步骤1：检查前端发送的消息**
在浏览器开发者工具中查看WebSocket发送的消息：
```javascript
// 在发送前添加日志
console.log('发送订阅消息:', JSON.stringify(subscribeMessage, null, 2));
```

#### **步骤2：检查Python脚本日志**
查看Python脚本输出，确认：
- 平台识别正确：`platform=runninghub`
- `netWssUrl` 存在且格式正确
- 启动了RunningHub监听器而不是ComfyUI监听器

#### **步骤3：使用测试脚本**
运行测试脚本验证连接：
```bash
python scripts/test_runninghub_connection.py
```

### 6. 常见问题排查

#### **问题1：平台识别错误**
**症状：** 任务被当作ComfyUI处理
**解决：** 确保前端发送 `platform: "runninghub"`

#### **问题2：netWssUrl缺失**
**症状：** 日志显示"缺少netWssUrl"
**解决：** 检查Node.js后端是否正确传递了`netWssUrl`

#### **问题3：URL格式错误**
**症状：** 连接失败，URL不是WebSocket格式
**解决：** 确保`netWssUrl`以`wss://`开头

#### **问题4：数据传递链断裂**
**症状：** `task_data`为空或不完整
**解决：** 检查从Node.js到Python的完整数据传递链

### 7. 调试命令

#### **查看Python脚本日志：**
```bash
# 如果使用systemd
journalctl -u your-python-service -f

# 如果直接运行
python scripts/main.py
```

#### **测试WebSocket连接：**
```bash
# 使用wscat测试
wscat -c "wss://www.runninghub.cn:443/ws/c_instance?..."
```

### 8. 完整的修复检查清单

- [ ] 前端发送正确的`platform: "runninghub"`
- [ ] 前端在`task_data`中包含`netWssUrl`
- [ ] Node.js后端正确返回RunningHub信息
- [ ] Python脚本正确识别平台类型
- [ ] Python脚本能够提取到`netWssUrl`
- [ ] `netWssUrl`格式正确（以`wss://`开头）
- [ ] RunningHub监听器被正确启动
- [ ] WebSocket连接成功建立

### 9. 预期的正确日志输出

```
收到订阅消息: {'type': 'subscribe_task', 'platform': 'runninghub', ...}
订阅任务: task_id=01175362530856327, platform=runninghub, instance_ws_url=wss://www.runninghub.cn
task_data: {'netWssUrl': 'wss://www.runninghub.cn:443/ws/c_instance?...', ...}
根据平台 'runninghub' 启动监听器
启动RunningHub监听器 for task 01175362530856327
RunningHub任务 01175362530856327 的task_data: {'netWssUrl': 'wss://...', ...}
提取的netWssUrl: wss://www.runninghub.cn:443/ws/c_instance?...
为任务 01175362530856327 创建新的RunningHub连接: wss://www.runninghub.cn:443/ws/c_instance?...
开始监听RunningHub实例: wss://www.runninghub.cn:443/ws/c_instance?... (task_id=01175362530856327)
```

通过以上修复步骤，应该能够解决RunningHub连接问题，确保任务正确使用`netWssUrl`进行WebSocket连接。
