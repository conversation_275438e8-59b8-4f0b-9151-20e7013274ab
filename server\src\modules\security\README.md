# CSRF 保护机制

本项目实现了完整的CSRF（跨站请求伪造）保护机制，包括前端和后端的实现。

## 目录结构

随着项目重构，安全相关功能已经按照以下结构组织：

```
项目根目录
├── src/                  # 前端代码
│   └── utils/security/   # 前端安全相关代码
│       └── csrf.js       # 前端CSRF实现
│
└── server/               # 后端代码
    └── src/
        └── modules/
            ├── auth/     # 认证相关代码（已迁移）
            │   ├── auth.middleware.js 
            │   ├── auth.routes.js     
            │   ├── user.model.js      
            │   └── index.js
            │
            ├── captcha/  # 验证码相关代码（已迁移）
            │   ├── captcha.controller.js
            │   ├── captcha.routes.js
            │   ├── captcha.service.js
            │   └── index.js
            │
            └── security/ # 安全相关代码（当前模块）
                ├── csrf.middleware.js # CSRF保护中间件
                ├── index.js          # 模块导出
                └── README.md         # 本文档
```

## 前端实现

前端实现位于 `src/utils/security/csrf.js`，主要功能包括：

1. **生成CSRF令牌**：在用户登录成功后生成一个随机的CSRF令牌，并存储在localStorage中。
2. **刷新CSRF令牌**：定期刷新CSRF令牌，以提高安全性。
3. **清除CSRF令牌**：在用户登出时清除CSRF令牌。
4. **在API请求中添加CSRF令牌**：在所有非GET请求中，自动在请求头中添加CSRF令牌。

## 后端实现

后端实现位于 `server/src/modules/security/csrf.middleware.js`，主要功能包括：

1. **验证CSRF令牌**：验证所有非GET请求中的CSRF令牌。
2. **排除特定路由**：可以配置特定路由不需要CSRF验证，如登录和注册路由。
3. **令牌过期检查**：检查CSRF令牌是否已过期（默认24小时）。

## 使用方法

### 前端

前端已经在 `AuthContext.jsx` 中集成了CSRF令牌的生成和刷新功能：

- 用户登录成功后，会自动生成CSRF令牌。
- 每30分钟会自动刷新CSRF令牌。
- 用户登出时，会自动清除CSRF令牌。

API请求已经在 `api.js` 中集成了CSRF令牌的添加功能：

- 所有非GET请求会自动在请求头中添加 `X-CSRF-Token` 字段。

### 后端

后端已经在 `app.js` 中集成了CSRF验证中间件：

- 所有API路由都会进行CSRF验证，除了登录和注册等特定路由。
- 验证失败会返回403错误。
- 现在从 `modules/security` 导入CSRF中间件，通过 `csrfMiddleware.csrfProtection` 使用。

## 启动服务器

要启动服务器，你需要：

1. 进入server目录：`cd server`
2. 运行开发服务器：`npm run dev`

注意：在项目根目录下运行`npm run dev`会失败，因为dev脚本只在server/package.json中定义。

## 测试

可以使用以下方法测试CSRF保护机制：

1. 登录系统，然后尝试发送一个POST请求到 `/api/auth/test-csrf`，应该能够成功。
2. 清除localStorage中的CSRF令牌，然后再次尝试发送请求，应该会收到403错误。
3. 修改localStorage中的CSRF令牌格式，然后再次尝试发送请求，应该会收到403错误。

## 注意事项

- CSRF保护机制只对已登录用户有效，未登录用户不需要CSRF保护。
- CSRF令牌的有效期为24小时，超过有效期需要重新登录。
- 如果需要添加新的不需要CSRF验证的路由，请在 `app.js` 中的 `csrfProtection` 中间件配置中添加。
- 随着项目重构，CSRF中间件已经从 `middleware/csrf.js` 移动到 `modules/security/csrf.middleware.js`，原文件已删除。
- 类似地，认证相关代码也已从middleware/auth.js和routes/auth.js移动到modules/auth/目录，原文件已删除。
- 使用新的导入语法：`const { csrfMiddleware: { csrfProtection } } = require('./modules/security');` 