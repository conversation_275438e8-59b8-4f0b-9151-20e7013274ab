# 平台检测问题调试

## 问题描述

从您的截图可以看到，WebSocket消息中显示：
- `platform: "comfyui"` ❌ 错误
- `instance_id: "runninghub"` ❌ 这个字段不应该是平台名称

但是根据您提供的任务执行接口返回数据，应该是：
- `platform: "runninghub"` ✅ 正确
- `instance_id: ""` ✅ RunningHub任务的instanceId应该是空字符串

## 调试步骤

### 1. 检查任务数据来源

请在浏览器开发者工具的控制台中查看以下调试信息：

#### **平台检测详情：**
```
平台检测详情: {
  taskId: "01175362784990113",
  task.platform: "runninghub",  // 🔑 这个值是什么？
  task.netWssUrl: "wss://www.runninghub.cn:443/ws/c_instance?...",  // 🔑 这个值存在吗？
  task.platform === "runninghub": true,  // 🔑 这个应该是true
  !!task.netWssUrl: true  // 🔑 这个应该是true
}

平台检测结果: {
  taskId: "01175362784990113",
  isRunningHubTask: true,  // 🔑 这个应该是true
  finalPlatform: "runninghub"  // 🔑 这个应该是"runninghub"
}
```

### 2. 检查任务数据结构

请在控制台中执行以下代码来检查任务数据：

```javascript
// 查看当前所有任务
console.log('所有任务:', tasks);

// 查看处理中的任务
const processingTasks = tasks.filter(task => task.status === 'processing');
console.log('处理中的任务:', processingTasks);

// 详细检查第一个处理中的任务
if (processingTasks.length > 0) {
  const task = processingTasks[0];
  console.log('任务详细信息:', {
    taskId: task.taskId,
    platform: task.platform,
    netWssUrl: task.netWssUrl,
    url: task.url,
    instanceId: task.instanceId,
    promptId: task.promptId,
    status: task.status,
    // 显示所有属性
    allProperties: Object.keys(task)
  });
}
```

### 3. 可能的问题原因

#### **原因1：任务数据中platform字段缺失或错误**
```javascript
// 如果任务数据是这样的：
{
  taskId: "01175362784990113",
  platform: undefined,  // ❌ 缺失
  netWssUrl: "wss://...",
  // ...
}

// 或者是这样的：
{
  taskId: "01175362784990113",
  platform: "comfyui",  // ❌ 错误的值
  netWssUrl: "wss://...",
  // ...
}
```

**解决方法：** 检查后端返回的数据是否正确设置了`platform: "runninghub"`

#### **原因2：任务数据中netWssUrl字段缺失**
```javascript
// 如果任务数据是这样的：
{
  taskId: "01175362784990113",
  platform: "runninghub",
  netWssUrl: undefined,  // ❌ 缺失
  // ...
}
```

**解决方法：** 检查后端是否正确传递了`netWssUrl`字段

#### **原因3：数据传递链中的问题**
可能在以下环节出现问题：
1. 后端API返回数据
2. 前端接收数据
3. 状态更新
4. 任务列表渲染

### 4. 验证修复

#### **步骤1：检查后端返回数据**
在网络面板中查看任务执行API的响应：
```json
{
  "success": true,
  "platform": "runninghub",  // ✅ 应该存在且为"runninghub"
  "netWssUrl": "wss://www.runninghub.cn:443/ws/c_instance?...",  // ✅ 应该存在
  "instanceId": "",  // ✅ 应该是空字符串
  // ...
}
```

#### **步骤2：检查前端状态更新**
在任务创建/更新的地方添加日志：
```javascript
// 在setTasks或updateTask的地方
console.log('更新任务状态:', {
  taskId: newTask.taskId,
  platform: newTask.platform,
  netWssUrl: newTask.netWssUrl
});
```

#### **步骤3：验证WebSocket消息**
正确的WebSocket订阅消息应该是：
```json
{
  "type": "subscribe_task",
  "task_id": "01175362784990113",
  "prompt_id": "1949482621214003201",
  "instance_id": "",  // ✅ 空字符串，不是"runninghub"
  "instance_ws_url": "wss://www.runninghub.cn:443/ws/c_instance?...",
  "platform": "runninghub",  // ✅ 应该是"runninghub"
  "task_data": {
    "netWssUrl": "wss://www.runninghub.cn:443/ws/c_instance?...",
    "clientId": "087c9dcd6e753a3faa83119807775ef4",
    "taskStatus": "RUNNING",
    "promptTips": "{\"result\": true, \"outputs_to_execute\": [\"32\", \"36\"]}"
  }
}
```

### 5. 临时测试代码

您可以在控制台中运行以下代码来测试平台检测逻辑：

```javascript
// 测试平台检测函数
function testPlatformDetection(task) {
  console.log('测试任务:', task);
  
  const isRunningHubTask = task.platform === 'runninghub' || task.netWssUrl;
  const platform = isRunningHubTask ? 'runninghub' : 'comfyui';
  
  console.log('检测结果:', {
    'task.platform': task.platform,
    'task.netWssUrl': !!task.netWssUrl,
    'task.platform === "runninghub"': task.platform === 'runninghub',
    isRunningHubTask,
    finalPlatform: platform
  });
  
  return platform;
}

// 测试不同的任务数据
testPlatformDetection({
  taskId: "test1",
  platform: "runninghub",
  netWssUrl: "wss://example.com"
});

testPlatformDetection({
  taskId: "test2",
  platform: undefined,
  netWssUrl: "wss://example.com"
});

testPlatformDetection({
  taskId: "test3",
  platform: "comfyui",
  netWssUrl: undefined
});
```

### 6. 检查清单

请确认以下各项：

- [ ] 后端API返回的数据包含正确的`platform: "runninghub"`
- [ ] 后端API返回的数据包含`netWssUrl`字段
- [ ] 前端正确接收并存储了任务数据
- [ ] 平台检测逻辑正确执行
- [ ] WebSocket消息中的`platform`字段为"runninghub"
- [ ] WebSocket消息中的`instance_id`字段为空字符串（不是"runninghub"）
- [ ] WebSocket消息中的`task_data`包含RunningHub特定信息

### 7. 预期的正确日志输出

```
平台检测详情: {
  taskId: "01175362784990113",
  task.platform: "runninghub",
  task.netWssUrl: "wss://www.runninghub.cn:443/ws/c_instance?...",
  task.platform === "runninghub": true,
  !!task.netWssUrl: true
}

平台检测结果: {
  taskId: "01175362784990113",
  isRunningHubTask: true,
  finalPlatform: "runninghub"
}

订阅runninghub任务: {
  taskId: "01175362784990113",
  platform: "runninghub",
  hasNetWssUrl: true
}

发送订阅消息: {
  type: "subscribe_task",
  task_id: "01175362784990113",
  prompt_id: "1949482621214003201",
  instance_id: "",
  instance_ws_url: "wss://www.runninghub.cn:443/ws/c_instance?...",
  platform: "runninghub",
  task_data: {
    netWssUrl: "wss://www.runninghub.cn:443/ws/c_instance?...",
    clientId: "087c9dcd6e753a3faa83119807775ef4",
    taskStatus: "RUNNING",
    promptTips: "{\"result\": true, \"outputs_to_execute\": [\"32\", \"36\"]}"
  }
}
```

通过这些调试步骤，应该能够找到平台检测错误的根本原因。
