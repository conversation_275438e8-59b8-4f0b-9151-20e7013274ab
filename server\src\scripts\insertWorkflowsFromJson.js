/**
 * 根据ComfyUI JSON工作流文件生成并插入工作流到数据库
 * 
 * 使用方法:
 * node server/src/scripts/insertWorkflowsFromJson.js
 * 
 * 可选参数:
 * --clear: 清空现有工作流数据
 * --dry-run: 仅显示将要插入的数据，不实际插入
 * --user-id: 指定创建者用户ID（默认使用第一个管理员用户）
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 导入模型
const Workflow = require('../models/Workflow');
const User = require('../models/User');

// 工作流分类映射
const CATEGORY_MAPPING = {
  'A': '款式设计',  // A系列：款式设计类
  'B': '模特图',    // B系列：模特图类  
  'C': '工具'       // C系列：工具类
};

// 工作流名称映射（基于现有的工作流名称）
const WORKFLOW_NAME_MAPPING = {
  'A01-trending': '爆款开发',
  'A02-optimize': '款式优化', 
  'A02b-optimizetext': '文本优化',
  'A03-inspiration': '灵感探索',
  'A05-drawing': '绘图设计',
  'A06-divergent': '发散创意',
  'B01-fashion': '时尚大片',
  'B02-tryonauto': '自动换装',
  'B02-tryonmanual': '手动换装',
  'B02-tryonother': '其他换装',
  'B03-changemodel': '换模特',
  'B04-recolor': '重新配色',
  'B05-fabric': '面料生成',
  'B06-background': '背景处理',
  'B07-virtual': '虚拟展示',
  'B08-detailmigration': '细节迁移',
  'B09-handfix': '手部修复',
  'B10-changeposture': '姿态调整',
  'C01-extract': '文本提取',
  'C02-upscale': '图像放大',
  'C03-mattingbg': '背景抠图',
  'C03-mattingbgfile': '背景抠图(文件)',
  'C03-mattingclo': '服装抠图',
  'C04-extend': '图像扩展'
};

// 工作流描述映射
const WORKFLOW_DESCRIPTION_MAPPING = {
  'A01-trending': '基于市场趋势和流行元素，生成具有商业价值的爆款服装设计',
  'A02-optimize': '对现有服装款式进行优化改进，提升设计质量和商业价值',
  'A02b-optimizetext': '优化服装设计相关的文本描述和标签',
  'A03-inspiration': '通过AI分析生成服装设计灵感和创意方向',
  'A05-drawing': '生成服装设计手绘图和技术图纸',
  'A06-divergent': '基于单一设计理念发散生成多种创意变体',
  'B01-fashion': '生成高质量的时尚大片和产品展示图',
  'B02-tryonauto': '自动识别并进行服装试穿效果展示',
  'B02-tryonmanual': '手动指定区域进行精确的服装试穿',
  'B02-tryonother': '其他类型的服装试穿和搭配展示',
  'B03-changemodel': '更换模特同时保持服装效果一致',
  'B04-recolor': '对服装进行重新配色和色彩搭配',
  'B05-fabric': '生成各种面料纹理和材质效果',
  'B06-background': '更换或优化服装展示的背景环境',
  'B07-virtual': '生成虚拟服装展示和3D效果',
  'B08-detailmigration': '将服装细节从一个设计迁移到另一个',
  'B09-handfix': '修复和优化模特手部的显示效果',
  'B10-changeposture': '调整模特姿态和动作',
  'C01-extract': '从图像中提取文本信息和标签',
  'C02-upscale': '提升图像分辨率和质量',
  'C03-mattingbg': '智能抠除图像背景',
  'C03-mattingbgfile': '批量处理图像背景抠除',
  'C03-mattingclo': '精确抠除服装区域',
  'C04-extend': '扩展图像边界和内容'
};

// 工作流标签映射
const WORKFLOW_TAGS_MAPPING = {
  'A01-trending': ['爆款', '趋势', '商业', '设计'],
  'A02-optimize': ['优化', '改进', '质量', '设计'],
  'A02b-optimizetext': ['文本', '优化', '标签', '描述'],
  'A03-inspiration': ['灵感', '创意', '探索', '设计'],
  'A05-drawing': ['绘图', '手绘', '技术图', '设计'],
  'A06-divergent': ['发散', '创意', '变体', '多样性'],
  'B01-fashion': ['时尚', '大片', '展示', '摄影'],
  'B02-tryonauto': ['试穿', '自动', '换装', 'AI'],
  'B02-tryonmanual': ['试穿', '手动', '精确', '换装'],
  'B02-tryonother': ['试穿', '搭配', '展示', '其他'],
  'B03-changemodel': ['换模特', '一致性', '展示'],
  'B04-recolor': ['配色', '色彩', '搭配', '调色'],
  'B05-fabric': ['面料', '纹理', '材质', '生成'],
  'B06-background': ['背景', '环境', '场景', '替换'],
  'B07-virtual': ['虚拟', '3D', '展示', '效果'],
  'B08-detailmigration': ['细节', '迁移', '转移', '融合'],
  'B09-handfix': ['手部', '修复', '优化', '细节'],
  'B10-changeposture': ['姿态', '动作', '调整', '变换'],
  'C01-extract': ['提取', '文本', '识别', 'OCR'],
  'C02-upscale': ['放大', '超分', '质量', '分辨率'],
  'C03-mattingbg': ['抠图', '背景', '分离', '智能'],
  'C03-mattingbgfile': ['抠图', '批量', '文件', '处理'],
  'C03-mattingclo': ['抠图', '服装', '精确', '分离'],
  'C04-extend': ['扩展', '延伸', '补全', '生成']
};

// 优先级映射（基于使用频率和重要性）
const PRIORITY_MAPPING = {
  // 高优先级 - 常用核心功能
  'A01-trending': 90,
  'B02-tryonauto': 85,
  'C03-mattingbg': 80,
  'B01-fashion': 75,
  'A02-optimize': 70,
  
  // 中优先级 - 重要功能
  'B04-recolor': 60,
  'C02-upscale': 55,
  'B06-background': 50,
  'A03-inspiration': 45,
  'B03-changemodel': 40,
  
  // 中低优先级 - 专业功能
  'B05-fabric': 35,
  'C01-extract': 30,
  'B02-tryonmanual': 25,
  'C04-extend': 20,
  'B07-virtual': 15,
  
  // 低优先级 - 特殊功能
  'A05-drawing': 10,
  'A06-divergent': 8,
  'B08-detailmigration': 6,
  'B09-handfix': 4,
  'B10-changeposture': 2,
  'A02b-optimizetext': 1,
  'B02-tryonother': 1,
  'C03-mattingbgfile': 1,
  'C03-mattingclo': 1
};

class WorkflowInserter {
  constructor() {
    this.jsonDir = path.resolve(__dirname, '../services/comfyClient/apiJson');
    this.workflows = [];
    this.dryRun = false;
    this.clearExisting = false;
    this.userId = null;
  }

  // 解析命令行参数
  parseArgs() {
    const args = process.argv.slice(2);
    this.dryRun = args.includes('--dry-run');
    this.clearExisting = args.includes('--clear');
    
    const userIdIndex = args.indexOf('--user-id');
    if (userIdIndex !== -1 && args[userIdIndex + 1]) {
      this.userId = args[userIdIndex + 1];
    }
  }

  // 连接数据库
  async connectDB() {
    try {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aibikini');
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error);
      process.exit(1);
    }
  }

  // 获取创建者用户ID
  async getCreatorUserId() {
    if (this.userId) {
      const user = await User.findById(this.userId);
      if (!user) {
        throw new Error(`用户ID ${this.userId} 不存在`);
      }
      return this.userId;
    }

    // 查找第一个管理员用户
    const adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      throw new Error('未找到管理员用户，请先创建管理员用户或使用 --user-id 参数指定用户ID');
    }
    
    return adminUser._id;
  }

  // 扫描JSON文件
  scanJsonFiles() {
    console.log(`📁 扫描目录: ${this.jsonDir}`);
    
    if (!fs.existsSync(this.jsonDir)) {
      throw new Error(`JSON目录不存在: ${this.jsonDir}`);
    }

    const files = fs.readdirSync(this.jsonDir);
    const jsonFiles = files.filter(file => file.endsWith('.json'));
    
    console.log(`📄 找到 ${jsonFiles.length} 个JSON文件`);
    return jsonFiles;
  }

  // 解析单个JSON文件
  parseJsonFile(filename) {
    const filePath = path.join(this.jsonDir, filename);
    const workflowId = path.basename(filename, '.json');
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const jsonData = JSON.parse(content);
      
      // 获取分类（基于文件名前缀）
      const prefix = workflowId.charAt(0);
      const category = CATEGORY_MAPPING[prefix] || '其他';
      
      // 构建工作流对象
      const workflow = {
        id: workflowId,
        name: WORKFLOW_NAME_MAPPING[workflowId] || workflowId,
        displayName: WORKFLOW_NAME_MAPPING[workflowId] || workflowId,
        category: category,
        description: WORKFLOW_DESCRIPTION_MAPPING[workflowId] || `${workflowId} 工作流`,
        version: '1.0.0',
        enabled: true,
        parameters: new Map(),
        supportedPlatforms: ['comfyui'],
        recommendedPlatform: 'comfyui',
        tags: WORKFLOW_TAGS_MAPPING[workflowId] || [],
        priority: PRIORITY_MAPPING[workflowId] || 0,
        usageStats: {
          totalRuns: 0,
          successRuns: 0,
          failedRuns: 0
        }
      };

      return workflow;
    } catch (error) {
      console.error(`❌ 解析文件 ${filename} 失败:`, error.message);
      return null;
    }
  }

  // 处理所有JSON文件
  processJsonFiles() {
    const jsonFiles = this.scanJsonFiles();
    
    for (const filename of jsonFiles) {
      const workflow = this.parseJsonFile(filename);
      if (workflow) {
        this.workflows.push(workflow);
      }
    }
    
    console.log(`✅ 成功解析 ${this.workflows.length} 个工作流`);
  }

  // 清空现有工作流
  async clearExistingWorkflows() {
    if (!this.clearExisting) return;
    
    console.log('🗑️  清空现有工作流数据...');
    const result = await Workflow.deleteMany({});
    console.log(`✅ 已删除 ${result.deletedCount} 个现有工作流`);
  }

  // 插入工作流到数据库
  async insertWorkflows() {
    const creatorId = await this.getCreatorUserId();
    console.log(`👤 使用创建者ID: ${creatorId}`);
    
    if (this.dryRun) {
      console.log('\n🔍 DRY RUN 模式 - 以下是将要插入的工作流:');
      this.workflows.forEach((workflow, index) => {
        console.log(`\n${index + 1}. ${workflow.id}`);
        console.log(`   名称: ${workflow.name}`);
        console.log(`   分类: ${workflow.category}`);
        console.log(`   描述: ${workflow.description}`);
        console.log(`   标签: ${workflow.tags.join(', ')}`);
        console.log(`   优先级: ${workflow.priority}`);
      });
      return;
    }

    await this.clearExistingWorkflows();
    
    console.log('\n💾 开始插入工作流到数据库...');
    let successCount = 0;
    let errorCount = 0;

    for (const workflowData of this.workflows) {
      try {
        // 检查是否已存在
        const existing = await Workflow.findOne({ id: workflowData.id });
        if (existing) {
          console.log(`⚠️  工作流 ${workflowData.id} 已存在，跳过`);
          continue;
        }

        // 创建新工作流
        const workflow = new Workflow({
          ...workflowData,
          createdBy: creatorId
        });

        await workflow.save();
        console.log(`✅ 插入工作流: ${workflowData.id} - ${workflowData.name}`);
        successCount++;
      } catch (error) {
        console.error(`❌ 插入工作流 ${workflowData.id} 失败:`, error.message);
        errorCount++;
      }
    }

    console.log(`\n📊 插入完成:`);
    console.log(`   成功: ${successCount} 个`);
    console.log(`   失败: ${errorCount} 个`);
    console.log(`   总计: ${this.workflows.length} 个`);
  }

  // 显示统计信息
  displayStats() {
    console.log('\n📈 工作流统计:');
    
    const categoryStats = {};
    this.workflows.forEach(workflow => {
      categoryStats[workflow.category] = (categoryStats[workflow.category] || 0) + 1;
    });

    Object.entries(categoryStats).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} 个`);
    });

    console.log(`\n🏷️  标签统计:`);
    const tagStats = {};
    this.workflows.forEach(workflow => {
      workflow.tags.forEach(tag => {
        tagStats[tag] = (tagStats[tag] || 0) + 1;
      });
    });

    const sortedTags = Object.entries(tagStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);

    sortedTags.forEach(([tag, count]) => {
      console.log(`   ${tag}: ${count} 次`);
    });
  }

  // 主执行方法
  async run() {
    try {
      console.log('🚀 开始工作流插入脚本\n');
      
      this.parseArgs();
      await this.connectDB();
      
      this.processJsonFiles();
      this.displayStats();
      
      await this.insertWorkflows();
      
      console.log('\n🎉 脚本执行完成!');
    } catch (error) {
      console.error('\n💥 脚本执行失败:', error.message);
      process.exit(1);
    } finally {
      await mongoose.disconnect();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const inserter = new WorkflowInserter();
  inserter.run();
}

module.exports = WorkflowInserter;
