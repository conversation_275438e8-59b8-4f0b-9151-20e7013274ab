import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import './index.css';
import '../../../src/styles/buttons.css'; // 导入按钮样式，包括开关组件

// 选项对应的数字值定义
// 1: 服装提取
// 2: 模特提取
// 3: 图片整体提取
// 4: 自定义提取
// 这些数字值会直接传递给后端的comfyUI工作流节点

const ImageExtractionOptions = ({
  onChange,
  defaultOptions = {
    optionValue: 1, // 默认选中服装，对应数字1
    customText: '' // 自定义文本
  }
}) => {
  const [selectedOption, setSelectedOption] = useState(() => {
    // 获取默认选中的选项值
    return defaultOptions.optionValue || 1; // 默认为1
  });
  
  const [customText, setCustomText] = useState(defaultOptions.customText || '');
  const [showCustomInput, setShowCustomInput] = useState(selectedOption === 4);
  
  // 自定义文本输入框相关状态 - 与 TextDescriptionPanel 完全一致
  const [textareaHeight, setTextareaHeight] = useState(60);
  const [isDragging, setIsDragging] = useState(false);
  const isDraggingRef = useRef(false);
  const startYRef = useRef(0);
  const startHeightRef = useRef(0);
  
  // refs
  const textareaRef = useRef(null);
  const resizeHandleRef = useRef(null);
  
  // 检测滚动条并调整拖动按钮位置 - 与 TextDescriptionPanel 完全一致
  const checkScrollbarAndAdjustHandle = useCallback(() => {
    if (textareaRef.current && resizeHandleRef.current) {
      const textarea = textareaRef.current;
      const handle = resizeHandleRef.current;
      
      // 检测是否有垂直滚动条
      const hasScrollbar = textarea.scrollHeight > textarea.clientHeight;
      
      if (hasScrollbar) {
        // 有滚动条时，right为20px
        handle.style.setProperty('right', '10px', 'important');
      } else {
        // 无滚动条时，right为12px
        handle.style.setProperty('right', '4px', 'important');
      }
    }
  }, []);
  
  // 组件初始化时，确保有默认设置
  useEffect(() => {
    // 设置默认选项
    setSelectedOption(defaultOptions.optionValue || 1);
    setCustomText(defaultOptions.customText || '');
    setShowCustomInput(defaultOptions.optionValue === 4);
  }, [defaultOptions]);

  // 监听文本内容和高度变化，检测滚动条 - 与 TextDescriptionPanel 完全一致
  useEffect(() => {
    if (showCustomInput) {
      checkScrollbarAndAdjustHandle();
    }
  }, [customText, textareaHeight, checkScrollbarAndAdjustHandle, showCustomInput]);

  // 使用 ResizeObserver 监听 textarea 尺寸变化 - 与 TextDescriptionPanel 完全一致
  useEffect(() => {
    if (!showCustomInput) return;
    
    const textarea = textareaRef.current;
    if (!textarea) return;

    const resizeObserver = new ResizeObserver(() => {
      checkScrollbarAndAdjustHandle();
    });

    resizeObserver.observe(textarea);

    return () => {
      resizeObserver.disconnect();
    };
  }, [checkScrollbarAndAdjustHandle, showCustomInput]);

  const handleOptionChange = (optionValue) => {
    setSelectedOption(optionValue);
    setShowCustomInput(optionValue === 4);
    
    if (onChange) {
      // 传递选项值和自定义文本
      onChange({ 
        optionValue,
        customText: optionValue === 4 ? customText : ''
      });
    }
  };

  // 处理文本输入变化 - 与 TextDescriptionPanel 完全一致
  const handleCustomTextChange = useCallback((e) => {
    const newText = e.target.value;
    setCustomText(newText);
    
    if (onChange) {
      onChange({ 
        optionValue: selectedOption,
        customText: newText
      });
    }
    
    // 使用 requestAnimationFrame 确保DOM已更新后再检测滚动条
    requestAnimationFrame(() => {
      checkScrollbarAndAdjustHandle();
    });
  }, [onChange, selectedOption, checkScrollbarAndAdjustHandle]);
  
  // 处理拖动事件 - 与 TextDescriptionPanel 完全一致
  const handleMouseMove = useCallback((e) => {
    if (!isDraggingRef.current) return;
    
    // 计算高度变化
    const deltaY = e.clientY - startYRef.current;
    
    // 调整textarea高度
    const newTextareaHeight = Math.max(60, Math.min(300, startHeightRef.current + deltaY));
    
    // 更新高度
    setTextareaHeight(newTextareaHeight);
  }, []);
  
  // 结束拖动事件 - 与 TextDescriptionPanel 完全一致
  const handleMouseUp = useCallback(() => {
    if (!isDraggingRef.current) return;
    
    isDraggingRef.current = false;
    setIsDragging(false); // 更新状态用于UI反馈
    
    // 移除事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    
    // 拖动结束后使用 requestAnimationFrame 检测滚动条
    requestAnimationFrame(() => {
      checkScrollbarAndAdjustHandle();
    });
  }, [handleMouseMove, checkScrollbarAndAdjustHandle]);
  
  // 开始拖动事件 - 与 TextDescriptionPanel 完全一致
  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    
    // 更新ref值
    isDraggingRef.current = true;
    startYRef.current = e.clientY;
    startHeightRef.current = textareaHeight;
    
    // 更新状态用于UI反馈
    setIsDragging(true);
    
    // 添加事件监听器
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [textareaHeight, handleMouseMove, handleMouseUp]);
  
  // 清理函数 - 使用useEffect确保组件卸载时移除事件监听器 - 与 TextDescriptionPanel 完全一致
  useEffect(() => {
    // 只在组件卸载时执行清理
    return () => {
      if (isDraggingRef.current) {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      }
    };
  }, [handleMouseMove, handleMouseUp]);

  return (
    <div className="extraction-panel">
      <div className="extraction-content">
        <div className="extraction-label">
          <span>取词选项</span>
        </div>
        
        <div className="extraction-options-container">
          <div className="extraction-options-row">
            <div className="extraction-option-item">
              <span className="option-label">服装</span>
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={selectedOption === 1}
                  onChange={() => handleOptionChange(1)}
                />
                <span className="toggle-track"></span>
              </label>
            </div>
            
            <div className="extraction-option-item">
              <span className="option-label">模特</span>
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={selectedOption === 2}
                  onChange={() => handleOptionChange(2)}
                />
                <span className="toggle-track"></span>
              </label>
            </div>
            
            <div className="extraction-option-item">
              <span className="option-label">图片整体</span>
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={selectedOption === 3}
                  onChange={() => handleOptionChange(3)}
                />
                <span className="toggle-track"></span>
              </label>
            </div>
            
            <div className="extraction-option-item">
              <span className="option-label">自定义</span>
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={selectedOption === 4}
                  onChange={() => handleOptionChange(4)}
                />
                <span className="toggle-track"></span>
              </label>
            </div>
          </div>
          
          {/* 自定义文本输入框 - 与 TextDescriptionPanel 完全一致 */}
          {showCustomInput && (
            <div className="custom-text-container">
              <div className="component-content">
                <textarea
                  ref={textareaRef}
                  className="custom-text-input"
                  value={customText}
                  onChange={handleCustomTextChange}
                  placeholder="请输入自定义的取词描述..."
                  style={{ height: `${textareaHeight}px` }}
                />
                <div 
                  className="textarea-resize-handle" 
                  onMouseDown={handleMouseDown}
                  title="拖动调整高度"
                  ref={resizeHandleRef}
                >
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

ImageExtractionOptions.propTypes = {
  onChange: PropTypes.func,
  defaultOptions: PropTypes.shape({
    optionValue: PropTypes.number,
    customText: PropTypes.string
  })
};

export default ImageExtractionOptions; 