# AIBIKINI 样式指南

## 目录
1. [样式系统概述](#样式系统概述)
2. [项目结构](#项目结构)
3. [基础样式](#基础样式)
4. [组件样式](#组件样式)
5. [动画系统](#动画系统)
6. [响应式设计](#响应式设计)
7. [使用指南](#使用指南)
8. [开发规范](#开发规范)
9. [最佳实践](#最佳实践)
10. [常见问题](#常见问题)

## 项目结构

```
src/
├── styles/                # 统一样式目录
│   ├── README.md         # 样式指南文档
│   ├── buttons.css       # 按钮样式
│   ├── cards.css         # 卡片样式
│   ├── common.css        # 通用样式
│   ├── inputs.css        # 输入框样式
│   ├── modals.css        # 模态框样式
│   ├── panels.css        # 面板样式
│   ├── preview.css       # 预览样式 (已迁移到ImagePreviewModal组件)
│   ├── scrollbars.css    # 滚动条样式
│   └── tabs.css          # 标签页样式
├── components/           # 组件目录
│   └── [组件名]/
│       ├── index.jsx     # 组件逻辑
│       └── index.css     # 组件特定样式
└── pages/               # 页面目录
    └── [页面名]/
        ├── index.jsx     # 页面逻辑
        └── index.css     # 页面特定样式
```

### 文件命名规范
- 统一样式文件：使用功能描述性名称，如 `buttons.css`
- 组件样式文件：与组件同名的 `index.css`
- 页面样式文件：与页面同名的 `index.css`

## 开发规范

### 样式命名规范

1. BEM命名规范
```css
/* Block */
.card { }

/* Element */
.card__title { }
.card__image { }

/* Modifier */
.card--featured { }
.card--disabled { }
```

2. 组件样式前缀
```css
/* 模态框相关 */
.modal-* { }

/* 按钮相关 */
.btn-* { }

/* 输入框相关 */
.input-* { }
```

3. 状态类命名
```css
.is-active { }
.is-disabled { }
.has-error { }
.is-loading { }
```

### Git提交规范

样式相关的提交信息格式：

```bash
# 新增样式
feat(styles): 添加按钮悬停效果

# 修复样式问题
fix(styles): 修复模态框溢出问题

# 优化样式
perf(styles): 优化卡片动画性能

# 重构样式
refactor(styles): 重构输入框样式结构
```

### 版本控制

样式文件的版本控制原则：

1. 重大更新
   - 修改主题色系
   - 改变布局结构
   - 更新设计规范

2. 普通更新
   - 添加新组件样式
   - 优化现有样式
   - 修复样式问题

3. 补丁更新
   - 修复小型样式bug
   - 调整细节参数
   - 更新文档注释

### 样式评审清单

提交样式更改前的自查项：

1. 代码质量
   - [ ] 遵循命名规范
   - [ ] 样式结构清晰
   - [ ] 代码注释完整
   - [ ] 移除未使用的样式

2. 功能完整性
   - [ ] 支持所有必要的状态
   - [ ] 实现响应式适配
   - [ ] 考虑边界情况
   - [ ] 处理错误状态

3. 性能考虑
   - [ ] 优化选择器
   - [ ] 合理使用动画
   - [ ] 避免样式重复
   - [ ] 控制样式体积

4. 兼容性
   - [ ] 主流浏览器测试
   - [ ] 移动端适配
   - [ ] 降级方案
   - [ ] 特性检测

### 样式编写顺序

推荐的CSS属性书写顺序：

```css
.element {
  /* 定位 */
  position: absolute;
  top: 0;
  right: 0;
  z-index: 100;

  /* 盒模型 */
  display: flex;
  width: 100px;
  height: 100px;
  padding: 10px;
  margin: 10px;
  
  /* 文字排版 */
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
  
  /* 视觉效果 */
  background: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
  opacity: 1;
  
  /* 交互相关 */
  cursor: pointer;
  transition: all 0.3s;
}
```

### 注释规范

1. 文件顶部注释
```css
/**
 * 按钮样式定义
 * @description 包含所有按钮相关的样式定义
 * <AUTHOR>
 * @date 2024-01-20
 */
```

2. 区块注释
```css
/* ---------------- 
   主要按钮样式
   ---------------- */
```

3. 单行注释
```css
/* 禁用状态样式 */
.btn:disabled { }
```

## 样式系统概述

我们的样式系统采用模块化设计，将常用样式抽象为可复用的组件。主要包括：

- 基础样式：通用工具类、滚动条等
- 组件样式：按钮、输入框、模态框等
- 布局样式：卡片、面板等
- 交互样式：动画、过渡效果等

### 设计原则

1. 一致性：保持视觉和交互的一致
2. 可复用：避免重复编写样式
3. 可维护：清晰的命名和结构
4. 响应式：支持多种设备尺寸

### 颜色系统

- 主色：#FF3C6A
- 文本色：
  - 主要文本：#333
  - 次要文本：#666
  - 提示文本：#999
- 边框色：#eee, #e0e0e0
- 背景色：
  - 主背景：white
  - 次背景：#f5f5f5, #f8f9fa
- 状态色：
  - 成功：#52c41a
  - 警告：#faad14
  - 错误：#ff4d4f
  - 信息：#1890ff

### 尺寸规范

- 间距：4px, 8px, 12px, 16px, 20px, 24px
- 字体：
  - 小号：13px
  - 正文：14px
  - 标题：16px, 18px
- 圆角：4px, 6px, 8px, 12px
- 边框：1px solid #eee

## 基础样式

### 通用工具类 (common.css)
```css
/* 布局 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }

/* 间距 */
.gap-8 { gap: 8px; }
.gap-12 { gap: 12px; }
.gap-16 { gap: 16px; }
.mt-16 { margin-top: 16px; }
.p-16 { padding: 16px; }

/* 文本 */
.text-sm { font-size: 13px; }
.text-base { font-size: 14px; }
.text-lg { font-size: 16px; }
.font-medium { font-weight: 500; }

/* 状态类 */
.text-success { color: #52c41a; }
.text-warning { color: #faad14; }
.text-error { color: #ff4d4f; }
.text-info { color: #1890ff; }
```

### 滚动条样式 (scrollbars.css)
```css
/* 导入方式 */
@import '@/styles/scrollbars.css';

/* 自动应用于所有滚动容器 */
/* 统一宽度为6px，圆角设计 */
*::-webkit-scrollbar {
  width: 7px !important;
}

*::-webkit-scrollbar-track {
  background: #f7f7f7 !important;
  border-radius: 8px !important;
}

*::-webkit-scrollbar-thumb {
  background: #dcdcdc !important;
  border-radius: 8px !important;
}
```

## 组件样式

### 按钮 (buttons.css)
```html
<!-- 主要按钮 -->
<button class="generate-btn">生成</button>

<!-- 次要按钮 -->
<button class="action-btn">操作</button>

<!-- 文本按钮 -->
<button class="clear-btn">清空</button>

<!-- 过滤按钮 -->
<button class="filter-option">筛选</button>

<!-- 禁用状态 -->
<button class="generate-btn" disabled>禁用状态</button>
```

### 输入框 (inputs.css)
```html
<!-- 基础输入框 -->
<div class="input-group">
  <label class="input-label">标题</label>
  <input class="input-base" />
</div>

<!-- 文本域 -->
<div class="input-group">
  <label class="input-label">描述</label>
  <textarea class="textarea-base"></textarea>
</div>

<!-- 错误状态 -->
<div class="input-group">
  <label class="input-label">错误输入</label>
  <input class="input-base error" />
  <span class="error-message">错误提示文本</span>
</div>
```

### 模态框 (modals.css)
```html
<div class="base-modal">
  <div class="modal-content">
    <div class="modal-header">标题</div>
    <div class="modal-body">内容</div>
    <div class="modal-footer">
      <button class="clear-btn">取消</button>
      <button class="save-settings-btn">确定</button>
    </div>
  </div>
</div>

<!-- 大尺寸模态框 -->
<div class="base-modal">
  <div class="modal-content modal-large">
    <!-- 内容 -->
  </div>
</div>
```

### 卡片 (cards.css)
```html
<div class="cards-grid">
  <div class="card-item">
    <div class="card-preview">
      <img src="..." alt="预览图" />
    </div>
    <div class="card-caption">
      <div class="card-info">
        <span class="card-name">标题</span>
      </div>
    </div>
  </div>
</div>

<!-- 骨架屏 -->
<div class="card-skeleton">
  <div class="card-skeleton-item">
    <div class="card-skeleton-image"></div>
    <div class="card-skeleton-caption">
      <div class="card-skeleton-text"></div>
    </div>
  </div>
</div>
```

### 面板 (panels.css)
```html
<div class="panel-component">
  <div class="component-header">
    <div class="component-info">
      <div class="component-text">
        <h3>标题</h3>
        <p>描述</p>
      </div>
    </div>
    <button class="expand-btn">
      <span></span>
    </button>
  </div>
</div>
```

### 图片预览 (ImagePreviewModal组件)
```html
<div class="image-preview-modal">
  <div class="preview-content">
    <img src="..." alt="预览图" />
    <button class="preview-close-button">关闭</button>
  </div>
</div>
```

### 开发中提示 (common.css)
```html
<div class="coming-soon">
  <div class="coming-soon-icon">
    <!-- 图标 -->
  </div>
  <h3>功能开发中</h3>
  <p>敬请期待...</p>
</div>
```

## 动画系统

### 过渡动画
```css
/* 淡入 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 滑入 */
@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 缩放 */
@keyframes zoomIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 加载动画 */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
```

### 动画使用
```css
.modal-fade-in {
  animation: fadeIn 0.2s ease;
}

.content-slide-in {
  animation: slideIn 0.3s ease;
}

.image-zoom-in {
  animation: zoomIn 0.3s ease;
}
```

## 响应式设计

### 断点定义
```css
/* 移动端 */
@media (max-width: 480px) {
  /* 样式 */
}

/* 平板 */
@media (max-width: 768px) {
  /* 样式 */
}

/* 小屏幕电脑 */
@media (max-width: 1024px) {
  /* 样式 */
}

/* 大屏幕 */
@media (max-width: 1400px) {
  /* 样式 */
}
```

### 响应式布局示例
```css
/* 卡片网格 */
.cards-grid {
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
}

@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .cards-grid {
    grid-template-columns: 1fr;
  }
}
```

## 使用指南

### 导入样式
```css
/* 在组件样式文件顶部导入需要的样式 */
@import '@/styles/buttons.css';
@import '@/styles/inputs.css';
@import '@/styles/modals.css';
```

### 组合使用
```html
<!-- 组合多个样式类 -->
<div class="flex items-center gap-16">
  <input class="input-base" />
  <button class="action-btn">确定</button>
</div>
```

### 性能优化建议
1. 使用 `will-change` 提示浏览器优化
```css
.card-item {
  will-change: transform;
  transform: translateZ(0);
}
```

2. 避免重排/重绘
```css
/* 推荐 */
.element {
  transform: translate(0, 100px);
}

/* 不推荐 */
.element {
  top: 100px;
}
```

## 开发规范

### 样式命名规范

1. BEM命名规范
```css
/* Block */
.card { }

/* Element */
.card__title { }
.card__image { }

/* Modifier */
.card--featured { }
.card--disabled { }
```

2. 组件样式前缀
```css
/* 模态框相关 */
.modal-* { }

/* 按钮相关 */
.btn-* { }

/* 输入框相关 */
.input-* { }
```

3. 状态类命名
```css
.is-active { }
.is-disabled { }
.has-error { }
.is-loading { }
```

### Git提交规范

样式相关的提交信息格式：

```bash
# 新增样式
feat(styles): 添加按钮悬停效果

# 修复样式问题
fix(styles): 修复模态框溢出问题

# 优化样式
perf(styles): 优化卡片动画性能

# 重构样式
refactor(styles): 重构输入框样式结构
```

### 版本控制

样式文件的版本控制原则：

1. 重大更新
   - 修改主题色系
   - 改变布局结构
   - 更新设计规范

2. 普通更新
   - 添加新组件样式
   - 优化现有样式
   - 修复样式问题

3. 补丁更新
   - 修复小型样式bug
   - 调整细节参数
   - 更新文档注释

### 样式评审清单

提交样式更改前的自查项：

1. 代码质量
   - [ ] 遵循命名规范
   - [ ] 样式结构清晰
   - [ ] 代码注释完整
   - [ ] 移除未使用的样式

2. 功能完整性
   - [ ] 支持所有必要的状态
   - [ ] 实现响应式适配
   - [ ] 考虑边界情况
   - [ ] 处理错误状态

3. 性能考虑
   - [ ] 优化选择器
   - [ ] 合理使用动画
   - [ ] 避免样式重复
   - [ ] 控制样式体积

4. 兼容性
   - [ ] 主流浏览器测试
   - [ ] 移动端适配
   - [ ] 降级方案
   - [ ] 特性检测

### 样式编写顺序

推荐的CSS属性书写顺序：

```css
.element {
  /* 定位 */
  position: absolute;
  top: 0;
  right: 0;
  z-index: 100;

  /* 盒模型 */
  display: flex;
  width: 100px;
  height: 100px;
  padding: 10px;
  margin: 10px;
  
  /* 文字排版 */
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
  
  /* 视觉效果 */
  background: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
  opacity: 1;
  
  /* 交互相关 */
  cursor: pointer;
  transition: all 0.3s;
}
```

### 注释规范

1. 文件顶部注释
```css
/**
 * 按钮样式定义
 * @description 包含所有按钮相关的样式定义
 * <AUTHOR>
 * @date 2024-01-20
 */
```

2. 区块注释
```css
/* ---------------- 
   主要按钮样式
   ---------------- */
```

3. 单行注释
```css
/* 禁用状态样式 */
.btn:disabled { }
```

## 最佳实践

1. 样式复用
   - 优先使用现有的统一样式
   - 避免编写重复的样式定义
   - 需要新样式时先考虑是否可以复用

2. 响应式设计
   - 使用相对单位（rem, em, %）
   - 适配不同屏幕尺寸
   - 测试多种设备下的显示效果

3. 性能优化
   - 合理使用动画效果
   - 避免不必要的嵌套
   - 优化选择器性能
   - 使用 CSS 硬件加速
   - 避免大量使用阴影和模糊效果

4. 维护建议
   - 遵循命名规范
   - 及时更新文档
   - 定期检查和优化
   - 保持代码注释的完整性
   - 遵循模块化组织原则

5. 浏览器兼容
   - 使用 autoprefixer 处理前缀
   - 测试主流浏览器
   - 提供降级方案

## 常见问题

1. 样式冲突
   - 检查样式优先级
   - 使用更具体的选择器
   - 考虑使用 `!important`（谨慎使用）
   - 使用 CSS Modules 或 scoped styles

2. 响应式问题
   - 检查媒体查询断点
   - 测试边界情况
   - 使用浏览器开发工具调试
   - 确保视口设置正确

3. 兼容性问题
   - 查看浏览器支持情况
   - 添加必要的前缀
   - 提供降级方案
   - 使用 @supports 检测特性支持

4. 性能问题
   - 避免过度使用阴影和滤镜
   - 优化动画性能
   - 减少重排操作
   - 合理使用硬件加速

5. 开发调试
   - 使用 Chrome DevTools
   - 检查样式继承关系
   - 分析性能瓶颈
   - 使用 CSS Grid/Flexbox 调试工具 