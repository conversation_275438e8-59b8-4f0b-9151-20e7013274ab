/**
 * 下载辅助工具 - 提供统一的下载体验和消息通知
 */
import { message } from 'antd';
import JSZ<PERSON> from 'jszip';

/**
 * 处理图像批量下载
 * @param {Object} task - 任务对象
 * @param {Function} fetchFunction - 获取图像的函数
 * @param {Function} singleDownloadHandler - 单张图片下载处理函数
 * @returns {Promise<void>}
 */
export const handleBatchDownload = async (task, fetchFunction, singleDownloadHandler) => {
  try {
    if (!task) {
      message.error('无效的任务数据，无法进行批量下载');
      return;
    }
    console.log('task',task.processInfo);
    
    // 直接使用generatedImages属性，不再支持旧的images属性
    const imagesList = task.processInfo.results || [];
    
    // 过滤出已生成的图片
    const validImages = imagesList.filter(img => img && img.url);
    
    if (validImages.length === 0) {
      message.warning('没有可下载的图片');
      return;
    }

    // 如果只有一张图片，直接下载
    if (validImages.length === 1) {
      message.info('下载单张图片中...');
      // 获取正确的文件扩展名
      const imageUrl = validImages[0].url;
      const urlParts = imageUrl.split('.');
      const originalExtension = urlParts.length > 1 ? urlParts[urlParts.length - 1].split('?')[0] : 'jpg';
      
      // 调用单张图片下载函数，传递正确的扩展名
      singleDownloadHandler(validImages[0].url.replace(/^http:/, 'https:'), task.taskId, 0, originalExtension);
      return;
    }

    // 显示准备中消息
    message.info('正在准备下载，请稍候...');
    // 创建一个新的JSZip实例
    const zip = new JSZip();
    
    // 收集所有需要下载的图片URL
    const imageUrls = validImages.map((img, index) => {
      // 从URL中提取原始文件扩展名
      const urlParts = img.url.split('.');
      const originalExtension = urlParts.length > 1 ? urlParts[urlParts.length - 1].split('?')[0] : 'jpg';
      
      return {
        url: img.url.replace(/^http:/, 'https:'),
        fileName: `${task.taskId}_图片${index + 1}.${originalExtension}`
      };
    });
    
    // 下载所有图片并添加到zip
    const fetchPromises = imageUrls.map(async (item) => {
      try {
        const response = await fetchFunction(item.url);
        if (!response.ok) throw new Error(`下载失败: ${response.statusText}`);
        
        const blob = await response.blob();
        
        // 优先从Content-Type响应头获取文件扩展名
        let originalExtension = 'jpg'; // 默认值
        const contentType = response.headers.get('content-type');
        if (contentType) {
          if (contentType.includes('image/png')) {
            originalExtension = 'png';
          } else if (contentType.includes('image/jpeg') || contentType.includes('image/jpg')) {
            originalExtension = 'jpg';
          } else if (contentType.includes('image/webp')) {
            originalExtension = 'webp';
          } else if (contentType.includes('image/gif')) {
            originalExtension = 'gif';
          }
        } else {
          // 如果无法从响应头获取，则从URL中提取
          const urlParts = item.url.split('.');
          originalExtension = urlParts.length > 1 ? urlParts[urlParts.length - 1].split('?')[0] : 'jpg';
        }
        
        // 使用正确的文件扩展名
        const fileName = item.fileName.replace(/\.[^/.]+$/, `.${originalExtension}`);
        zip.file(fileName, blob);
        
        return true;
      } catch (error) {
        console.error(`下载图片 ${item.url} 失败:`, error);
        return false;
      }
    });
    
    // 等待所有图片下载完成
    const results = await Promise.all(fetchPromises);
    const failedCount = results.filter(r => !r).length;
    
    // 如果所有图片都下载失败，显示错误
    if (failedCount === validImages.length) {
      message.error('所有图片下载失败，请稍后重试');
      return;
    }
    
    // 生成zip文件
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    
    // 创建下载链接
    const zipUrl = URL.createObjectURL(zipBlob);
    const link = document.createElement('a');
    link.href = zipUrl;
    link.download = `任务_${task.taskId}_图片集.zip`;
    
    // 创建下载完成事件监听器
    const downloadCompleteHandler = () => {
      message.success('图片打包已完成，正在下载中');
      link.removeEventListener('click', downloadCompleteHandler);
    };
    
    link.addEventListener('click', downloadCompleteHandler);
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    setTimeout(() => {
      URL.revokeObjectURL(zipUrl);
      
      // 显示下载结果信息
      if (failedCount > 0) {
        message.warning(`${failedCount}张图片下载失败，其余图片已打包下载`);
      }
    }, 100);
  } catch (error) {
    console.error('打包下载失败:', error);
    message.error('打包下载失败: ' + (error.message || '请稍后重试'));
  }
};

export default handleBatchDownload; 