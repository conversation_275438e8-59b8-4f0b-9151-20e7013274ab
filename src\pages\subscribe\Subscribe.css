/* 移动端全局样式修复 */
@media (max-width: 1024px) {
  * {
    box-sizing: border-box;
  }
  
  body {
    overflow-x: hidden;
  }
  
  /* 强制修复移动端布局问题 */
  .subscribe-container,
  .subscription-plans,
  .banner-section,
  .promotions-section,
  .plan-comparison {
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }
  
  .plan-card {
    min-width: 200px !important;
    max-width: 100% !important;
    margin-top: 6px;
  }
}

.subscribe-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 40px;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.subscribe-header {
  width: 100%;
  min-width: 0;
  overflow: hidden;
  text-align: center;
  margin-bottom: 60px;
}

.subscribe-header h1 {
  font-size: 2.2rem;
  margin-bottom: 16px;
  background: var(--brand-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subscribe-header p {
  font-size: 1.1rem;
  color: var(--text-secondary);
}

/* 订阅计划信息独立样式 */
.subscribe-plan-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-top: 8px;
  font-size: 0.92rem;
  color: var(--text-secondary);
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
}

.subscribe-plan-info-label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.92em;
}

.subscribe-plan-info-name {
  font-size: 0.92em;
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 2px;
}

.subscribe-plan-info-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
  margin: 0 0px 0 6px;
}
.subscribe-plan-info-dot.active {
  background: var(--success-color);
}
.subscribe-plan-info-dot.expired {
  background: var(--error-color);
}
.subscribe-plan-info-dot.pending {
  background: var(--warning-color);
}
.subscribe-plan-info-dot.canceled,
.subscribe-plan-info-dot.not-started {
  background: var(--text-secondary);
}

.subscribe-plan-info-status,
.subscribe-plan-info-expiry {
  font-size: 0.92em;
  color: var(--text-secondary);
  padding-left: 0px;
  padding-right: 4px;
}

.subscription-plans {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
}

.plan-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 30px 25px 40px;
  box-shadow: var(--shadow-sm);
  position: relative;
  transition: var(--transition-normal);
  display: flex;
  flex-direction: column;
  min-width: 300px;
  border: 1px solid var(--border-lighter);
  width: 100%;
  box-sizing: border-box;
}

.plan-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.plan-card.recommended {
  border: none;
  background: linear-gradient(var(--bg-primary), var(--bg-primary)) padding-box,
              linear-gradient(45deg, #FF6B6B, #FF8E53) border-box;
  border: 2px solid transparent;
}

.recommended-badge {
  position: absolute;
  top: -12px;
  right: 20px;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: white;
  padding: 4px 16px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.2);
}

.limited-badge {
  position: absolute;
  top: 6px;
  right: -10px;
  width: 66px;
  height: 28px;
  line-height: 28px;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: #fff;
  text-align: center;
  font-size: 0.85rem;
  font-weight: 600;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.12);
  z-index: 2;
  letter-spacing: 2px;
  pointer-events: none;
  padding: 0;
}

.plan-card h2,
.plan-description,
.price,
.features li,
.features li.sub-feature,
.features li.sub-title {
  line-height: 1.28 !important;
}

.plan-card h2 {
  font-size: 1.5rem;
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--text-primary);
  font-weight: 600;
  text-align: center;
}

.plan-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 20px;
}

.monthly-price {
  font-size: 2rem;
  font-weight: bold;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8px;
  text-align: center;
  position: relative;
  display: inline-block;
}

.monthly-price-value {
  font-size: 2.4rem;
  font-weight: bold;
  font-family: 'Roboto', Arial, sans-serif;
}

.year-price-value {
  font-size: 1.2em;
  font-weight: bold;
  font-family: 'Roboto', Arial, sans-serif;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.monthly-price-unit {
  font-size: 0.9rem;
  color: var(--text-secondary) !important;
  vertical-align: super;
  margin-left: 2px;
  position: relative;
  top: -0.5em;
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: var(--text-secondary) !important;
}

.price {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-secondary);
  margin-bottom: 8px;
  text-align: center;
}

.price-unit {
  font-size: 1rem;
  color: var(--text-secondary) !important;
  vertical-align: baseline;
  margin-left: 2px;
  position: relative;
  top: 0;
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: var(--text-secondary) !important;
}

.features {
  list-style: none;
  padding: 0;
  margin: 0 0 30px 0;
  flex-grow: 1;
}

.features li {
  padding: 10px 0;
  display: flex;
  align-items: baseline;
  font-size: 0.9rem;
}

.features li:not(.sub-feature) {
  font-weight: 600;
  color: var(--text-primary);
  padding-top: 20px;
}

.features li:not(.sub-feature)::before {
  content: "✓";
  color: #FF6B6B;
  margin-right: 10px;
  font-weight: bold;
}

.features li.sub-feature {
  padding: 4px 0 4px 24px;
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: normal;
}

.features li.sub-feature::before {
  content: "";
  color: #888;
  margin-right: 8px;
}

.features li.sub-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
  margin-top: 10px;
  margin-bottom: 2px;
  padding-left: 0;
  background: none;
  letter-spacing: 0.5px;
}

.subscribe-button {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 8px;
  background: var(--brand-gradient);
  color: var(--text-inverse);
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.2);
}

.subscribe-button:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(255, 107, 107, 0.3);
  background: var(--brand-gradient);
  filter: brightness(1.05);
}

.subscribe-button.special-trial-btn {
  background: transparent;
  color: var(--text-primary);
  border: 1.5px solid transparent;
  background-image: linear-gradient(var(--bg-primary), var(--bg-primary)), var(--brand-gradient);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  box-shadow: none;
  font-weight: 500;
  transition: all 0.3s;
}

.subscribe-button.special-trial-btn:hover {
  color: var(--text-primary);
  border: 1.5px solid transparent;
  background-image: linear-gradient(var(--bg-primary), var(--bg-primary)), var(--brand-gradient);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  filter: brightness(1.05);
}

.faq-section {
  margin-top: 80px;
}

.faq-section h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 1.7rem;
  color: var(--text-primary);
  font-weight: 600;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.faq-item {
  background: var(--bg-primary);
  padding: 24px;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-lighter);
  transition: var(--transition-normal);
}

.faq-item:hover {
  box-shadow: var(--shadow-md);
}

.faq-item h3 {
  color: var(--text-primary);
  margin-bottom: 12px;
  font-size: 1.05rem;
  font-weight: 600;
}

.faq-item p {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 0.95rem;
}

@media (max-width: 1280px) {
  .subscription-plans {
    gap: 20px;
    padding: 0;
  }

  .plan-card {
    padding: 25px 20px;
    min-width: 260px;
  }
}

@media (max-width: 1024px) {
  .subscribe-container {
    padding: 40px 20px;
  }

  .subscription-plans {
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }

  .plan-card {
    padding: 20px 15px;
    min-width: 260px;
  }

  .plan-card h2 {
    font-size: 1.5rem;
  }

  .monthly-price {
    font-size: 1.8rem;
  }

  .price {
    font-size: 0.9rem;
  }

  .features li {
    font-size: 0.9rem;
  }

  .features li.sub-feature {
    font-size: 0.85rem;
  }
}

@media (max-width: 1024px) {
  .subscribe-container {
    padding: 20px 16px;
    max-width: 100%;
    overflow-x: hidden;
  }
  
  .subscribe-header {
    margin-bottom: 40px;
  }
  
  .subscribe-header h1 {
    font-size: 1.8rem;
    margin-bottom: 12px;
  }
  
  .subscribe-header p {
    font-size: 0.95rem;
  }

  .subscribe-plan-info {
    margin-top: 12px;
    font-size: 0.95rem;
    gap: 6px;
  }

  .subscribe-plan-info-dot {
    width: 5px;
    height: 5px;
  }
  
  .subscription-plans {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 25px;
    padding: 0 8px;
    width: 100%;
  }
  
  .plan-card {
    max-width: 100%;
    padding: 20px 16px 30px;
    min-width: 200px;
    width: 100%;
  }

  .plan-card h2 {
    font-size: 1.3rem;
    margin-bottom: 16px;
  }
  
  .plan-description {
    font-size: 0.85rem;
    margin-bottom: 16px;
  }

  .monthly-price {
    font-size: 1.6rem;
    margin-bottom: 6px;
  }
  
  .monthly-price-value {
    font-size: 1.8rem;
  }

  .price {
    font-size: 0.9rem;
    margin-bottom: 6px;
  }
  
  .year-price-value {
    font-size: 1.1em;
  }

  .features li {
    font-size: 0.85rem;
    padding: 8px 0;
  }
  
  .features li:not(.sub-feature) {
    padding-top: 16px;
  }

  .features li.sub-feature {
    font-size: 0.8rem;
    padding: 3px 0 3px 20px;
  }
  
  .features li.sub-title {
    font-size: 0.9rem;
    margin-top: 8px;
    margin-bottom: 2px;
  }
  
  .subscribe-button {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
  
  .recommended-badge {
    font-size: 0.8rem;
    padding: 3px 12px;
    top: -10px;
    right: 16px;
  }
}

@media (max-width: 480px) {
  .faq-section {
    margin: 12px 0;
    padding: 0 4px;
  }
  .faq-item {
    padding: 12px 16px !important;
  }
  .faq-item h3 {
    font-size: 0.9rem;
    margin-bottom: 6px;
  }
  .faq-item p {
    font-size: 0.7rem;
    line-height: 1.4;
  }
  .faq-section h2 {
    font-size: 1.3rem !important;
    margin-bottom: 10px;
  }
  .faq-grid {
    gap: 16px !important;
  }
  .faq-item {
    margin-bottom: 0 !important;
    padding: 8px 10px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .subscribe-container {
    padding: 10px 6px;
  }
  
  .subscribe-header h1 {
    font-size: 1.2rem;
  }
  
  .subscribe-header p {
    font-size: 0.8rem;
  }

  .subscribe-plan-info {
    margin-top: 8px;
    font-size: 0.8rem;
    gap: 4px;
  }

  .subscribe-plan-info-dot {
    width: 4px;
    height: 4px;
  }
  
  .subscription-plans {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
    padding: 0 1px;
  }
  
  .plan-card {
    padding: 10px 6px 16px;
    min-width: 200px;
  }

  .plan-card h2 {
    font-size: 0.9rem;
    margin-bottom: 8px;
  }
  
  .plan-description {
    font-size: 0.65rem;
    margin-bottom: 8px;
  }

  .monthly-price {
    font-size: 1rem;
  }
  
  .monthly-price-value {
    font-size: 1.2rem;
  }

  .price {
    font-size: 0.7rem;
  }
  
  .year-price-value {
    font-size: 0.85em;
  }

  .features li {
    font-size: 0.65rem;
    padding: 3px 0;
  }
  
  .features li:not(.sub-feature) {
    padding-top: 8px;
  }

  .features li.sub-feature {
    font-size: 0.6rem;
    padding: 1px 0 1px 12px;
  }
  
  .features li.sub-title {
    font-size: 0.7rem;
    margin-top: 3px;
  }
  
  .subscribe-button {
    padding: 5px 8px;
    font-size: 0.7rem;
  }
  
  .recommended-badge {
    font-size: 0.6rem;
    padding: 1px 6px;
    top: -5px;
    right: 8px;
  }
  
  /* 横幅和促销区域超小屏幕优化 */
  .banner-section {
    margin: 15px 0;
    padding: 0 1px;
  }
  
  .banner-content {
    padding: 10px 6px;
    gap: 8px;
  }

  .banner-icon {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }

  .banner-info h3 {
    font-size: 0.8rem;
    margin-bottom: 2px;
  }

  .banner-info p {
    font-size: 0.65rem;
  }

  .banner-price {
    font-size: 1rem;
  }

  .banner-button {
    padding: 4px 8px;
    font-size: 0.7rem;
  }
  
  .promotions-section {
    margin: 15px 0;
    padding: 0 1px;
  }

  .promotion-card {
    padding: 10px 6px;
    gap: 8px;
  }

  .promotion-left {
    gap: 5px;
  }
  
  .promo-icon {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }

  .promotion-right {
    gap: 4px;
  }

  .promotion-info h3 {
    font-size: 0.8rem;
    margin-bottom: 2px;
  }
  
  .promotion-info p {
    font-size: 0.65rem;
  }

  .benefit-value {
    font-size: 1rem;
  }
  
  .benefit-label {
    font-size: 0.6rem;
  }

  .promo-button {
    padding: 4px 8px;
    font-size: 0.7rem;
  }
}

.contact-section {
  margin-top: 60px;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.contact-section .contact-support {
  background: transparent;
  box-shadow: none;
  padding: 0;
}

.contact-section .contact-methods {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
}

.contact-section .contact-method {
  display: flex;
  flex-direction: row !important;
  align-items: center !important;
  gap: 16px;
  font-family: var(--font-family) !important;
  font-size: 0.9rem !important;
  line-height: 1.5 !important;
  font-weight: 500 !important;
}

.contact-section .method-label,
.contact-section .wechat-text,
.contact-section .phone-text,
.contact-section .email-text {
  font-family: var(--font-family) !important;
  font-size: 0.9rem !important;
  line-height: 1.4 !important;
  font-weight: 500 !important;
  display: inline-block !important;
  vertical-align: baseline !important;
  margin: 0 !important;
  padding: 0 !important;
}

.contact-section .wechat-box {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 0 !important;
}

.contact-section .phone-box,
.contact-section .email-box {
  display: flex !important;
  align-items: baseline !important;
  gap: 8px !important;
  padding: 0 !important;
}

.contact-section .qr-container img {
  display: inline-block;
  vertical-align: middle;
}

.contact-section .copy-id-btn svg {
  width: 16px;
  height: 16px;
}

.contact-section .qr-container {
  position: relative;
  width: 36px;
  height: 36px;
}

.contact-section .contact-method.qr img {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-md);
  padding: 2px;
}

.contact-section .zoom-btn {
  width: 16px;
  height: 16px;
  right: -2px;
  bottom: -2px;
}

.contact-section .zoom-btn svg {
  width: 8px;
  height: 8px;
}

.contact-section .copy-btn {
  padding: 4px 8px;
  font-size: 0.8rem;
}

/* 响应式布局 */
@media (max-width: 1024px) {
  .contact-section {
    margin-top: 40px;
    padding: 16px;
  }

  .contact-section .contact-methods {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 24px;
    justify-content: center;
  }

  .contact-section .contact-method {
    flex: 0 0 auto;
  }

  .contact-section .phone-box,
  .contact-section .email-box {
    max-width: 200px;
  }

  .contact-section .phone-text,
  .contact-section .email-text {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .contact-section {
    margin-top: 30px;
    padding: 12px;
  }
  
  .contact-section .contact-methods {
    flex-direction: column;
    gap: 12px;
  }

  .contact-section .method-label {
    font-size: 0.8rem;
  }
  
  .contact-section .wechat-text,
  .contact-section .phone-text,
  .contact-section .email-text {
    font-size: 0.8rem;
  }

  .contact-section .phone-box,
  .contact-section .email-box {
    max-width: none;
  }
  
  .contact-section .qr-container {
    width: 32px;
    height: 32px;
  }
  
  .contact-section .contact-method.qr img {
    width: 32px;
    height: 32px;
  }
  
  .contact-section .copy-btn {
    padding: 3px 6px;
    font-size: 0.75rem;
  }
}

/* 横幅样式 */
.banner-section {
  margin: 40px 0;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
}

.banner-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-lighter);
  transition: var(--transition-normal);
  overflow: hidden;
}

.banner-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30px 40px;
  gap: 40px;
}

.banner-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.banner-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-shrink: 0;
}

.energy-pack-banner .banner-icon {
  background: var(--energy-gradient-yellow-light);
  box-shadow: 0 4px 15px rgba(230, 194, 0, 0.3);
}

.banner-info h3 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.banner-icon-text {
  font-size: 1.3rem;
  color: #E6C200;
  display: inline-block;
  transform: translateY(-1px);
}

.banner-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.banner-right {
  display: flex;
  align-items: center;
  gap: 30px;
  flex-shrink: 0;
}

/* 加油包专用样式 */
.banner-pricing {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.banner-price {
  font-size: 2rem;
  font-weight: bold;
  background: var(--energy-gradient-yellow-light);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.banner-ratio {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 加油包算力值显示 */
.banner-computing-power {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

@media (max-width: 1024px) {
  .banner-computing-power {
    font-size: 0.85rem !important;
  }
}

/* 优惠活动样式 */
.promotions-section {
  margin: 40px 0;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
}

.promotions-container {
  display: flex;
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.promotion-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-lighter);
  transition: var(--transition-normal);
  overflow: hidden;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25px 35px;
  gap: 30px;
}

.promotion-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.promotion-left {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.promo-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
  display: inline-block;
  transform: translateY(-1px);
}

.renewal-icon {
  color: #4CAF50;
}

.referral-icon {
  color: #FF6B6B;
}

.promotion-info {
  flex: 1;
}

.promotion-info h3 {
  margin: 0 0 6px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.promotion-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.promotion-right {
  display: flex;
  align-items: center;
  gap: 25px;
  flex-shrink: 0;
}

.promo-benefit {
  text-align: center;
}

.benefit-label {
  display: block;
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.benefit-value {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 2px;
}

.promo-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  background: var(--brand-gradient);
  color: var(--text-inverse);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
  white-space: nowrap;
}

.promo-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(255, 107, 107, 0.4);
  background: var(--brand-gradient);
  filter: brightness(1.05);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .promotions-container {
    flex-direction: column;
    gap: 15px;
    width: 100%;
  }

  .promotion-card {
    padding: 20px 25px;
    gap: 20px;
    width: 100%;
  }

  .promotion-left {
    gap: 12px;
    width: 100%;
  }

  .promo-icon {
    font-size: 1.1rem;
  }

  .promotion-info h3 {
    font-size: 1.2rem;
  }

  .promotion-info p {
    font-size: 0.9rem;
  }

  .promotion-right {
    gap: 4px !important;
  }

  .benefit-value {
    font-size: 1.6rem;
  }

  .benefit-label {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .promotions-section {
    margin: 20px 0;
    padding: 0 2px;
    width: 100%;
  }

  .promotion-card {
    flex-direction: column;
    text-align: center;
    padding: 12px 8px;
    gap: 10px;
    width: 100%;
  }

  .promotion-left {
    flex-direction: column;
    gap: 6px;
    width: 100%;
  }
  
  .promo-icon {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }

  .promotion-right {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .promotion-info h3 {
    font-size: 0.9rem;
    margin-bottom: 3px;
  }
  
  .promotion-info p {
    font-size: 0.7rem;
  }

  .benefit-value {
    font-size: 1.1rem;
  }
  
  .benefit-label {
    font-size: 0.65rem;
  }

  .promo-button {
    padding: 5px 10px;
    font-size: 0.75rem;
  }
}

  /* 横幅按钮样式 */
.banner-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.energy-pack-btn {
  background: var(--energy-gradient-yellow-light);
  color: var(--text-inverse);
  box-shadow: 0 4px 12px rgba(230, 194, 0, 0.3);
}

.energy-pack-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(230, 194, 0, 0.4);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .banner-content {
    padding: 25px 30px;
    gap: 30px;
  }

  .banner-icon {
    width: 50px;
    height: 50px;
    font-size: 1.8rem;
  }

  .banner-info h3 {
    font-size: 1.3rem;
  }

  .banner-price {
    font-size: 1.8rem;
  }
}

@media (max-width: 1024px) {
  .banner-section {
    margin: 25px 0;
    padding: 0 12px;
    width: 100%;
  }

  .banner-content {
    flex-direction: column;
    text-align: center;
    padding: 20px 16px;
    gap: 16px;
    width: 100%;
  }

  .banner-left {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }
  
  .banner-icon {
    width: 50px;
    height: 50px;
    font-size: 1.6rem;
  }
  
  .banner-info h3 {
    font-size: 1.3rem;
    margin-bottom: 6px;
  }
  
  .banner-info p {
    font-size: 0.9rem;
  }

  .banner-right {
    gap: 6px !important;
    width: 100%;
  }
  
  .banner-price {
    font-size: 1.2rem;
  }
  
  .banner-button {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .banner-section {
    margin: 20px 0;
    padding: 0 2px;
    width: 100%;
  }
  
  .banner-content {
    padding: 12px 8px;
    gap: 10px;
    width: 100%;
  }

  .banner-icon {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }

  .banner-info h3 {
    font-size: 0.9rem;
    margin-bottom: 3px;
  }

  .banner-info p {
    font-size: 0.7rem;
  }

  .banner-price {
    font-size: 1.1rem !important;
  }

  .banner-button {
    padding: 5px 10px;
    font-size: 0.75rem;
  }

  .banner-computing-power {
    font-size: 0.65rem !important;
  }
}

.plan-comparison {
  margin: 80px auto;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
}

.plan-comparison h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 2rem;
  color: var(--text-primary);
  font-weight: 600;
}

.comparison-table-wrapper {
  max-width: 100%;
  overflow-x: auto;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-lighter);
}

.comparison-table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  table-layout: auto;
  box-sizing: border-box;
}

.comparison-table th,
.comparison-table td {
  padding: 8px 16px !important;
  font-size: 1rem;
  line-height: 1.5;
  text-align: center;
  border-bottom: 1px solid var(--border-lighter);
}

.comparison-table th {
  background: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1.05rem;
  white-space: nowrap;
  font-family: var(--font-family);
}

.comparison-table th:first-child {
  text-align: left;
  border-top-left-radius: var(--radius-lg);
  width: 22%;
  max-width: 200px;
  min-width: 120px;
  word-break: break-all;
  white-space: normal;
  box-sizing: border-box;
  font-size: 1.05rem;
  font-family: var(--font-family);
  font-weight: 600;
}

.comparison-table th:last-child {
  border-top-right-radius: var(--radius-lg);
  font-size: 1.05rem;
  font-family: var(--font-family);
  font-weight: 600;
}

.comparison-table th.highlight {
  background: var(--brand-gradient);
  color: var(--text-inverse);
  font-size: 1.05rem;
  font-family: var(--font-family);
  font-weight: 600;
}

.comparison-table td:first-child {
  text-align: left;
  font-weight: 500;
  color: var(--text-primary);
  width: 22%;
  max-width: 200px;
  min-width: 120px;
  word-break: break-all;
  white-space: normal;
  box-sizing: border-box;
}

.comparison-table tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-lg);
}

.comparison-table tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-lg);
}

.comparison-table tr.category td {
  background: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
  padding: 10px 20px;
}

.comparison-table tr.category td:first-child {
  font-size: 1rem;
}

.comparison-table td {
  color: var(--text-secondary);
}

.comparison-table td:not(:first-child) {
  font-weight: 500;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 勾选标记样式 */
.comparison-table td:not(:first-child):not(:empty) {
  font-size: 1rem;
}

.comparison-table td:not(:first-child) {
  color: #FF6B6B;
}

.comparison-table tr:hover:not(.category) {
  background: var(--bg-hover);
}

/* 强制缩小套餐对比表格中细分项目的行高和内边距 */
.comparison-table tr td.sub-feature {
  padding-left: 28px !important;
  font-size: 0.97rem;
  line-height: 1.3;
}

@media (max-width: 888px) {
  .plan-comparison {
    margin: 60px auto;
  }

  .plan-comparison h2 {
    font-size: 1.8rem;
    margin-bottom: 30px;
  }

  .comparison-table th,
  .comparison-table td {
    padding: 10px 14px;
  }
}

@media (max-width: 480px) {
  .plan-comparison {
    margin: 30px auto;
    padding: 0 8px;
    width: 100%;
  }

  .plan-comparison h2 {
    font-size: 1.3rem;
    margin-bottom: 20px;
  }

  .comparison-table-wrapper {
    border-radius: var(--radius-md);
    width: 100%;
    overflow-x: auto;
  }
  
  .comparison-table {
    min-width: 600px;
  }
  
  .comparison-table th,
  .comparison-table td {
    padding: 4px 0px !important;
    font-size: 0.85rem;
  }
  
  .comparison-table .sub-feature {
    font-size: 0.7rem !important;
  }
  
  .comparison-table td:not(:first-child),
  .comparison-table .highlight-price {
    font-size: 0.7rem !important;
  }

  .comparison-table {
    border-radius: 8px !important;
  }
  .comparison-table th:first-child {
    border-top-left-radius: 8px !important;
    background: var(--bg-secondary, #232323) !important;
  }
  .comparison-table th:last-child {
    border-top-right-radius: 8px !important;
    background: var(--bg-secondary, #232323) !important;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.contact-section .contact-support:hover,
.contact-section .wechat-box:hover,
.contact-section .phone-box:hover,
.contact-section .email-box:hover {
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
}

.highlight-price {
  color: #FF6B6B;
  font-weight: bold;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.2em;
  vertical-align: bottom;
}

/* 法律条款链接样式 */
.legal-section {
  text-align: center;
  margin: 30px 0;
  padding: 0;
}

.subscribe-legal-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  font-size: 16px;
  color: var(--text-secondary);
  font-weight: 600;
}

.legal-text {
  color: var(--text-secondary);
}

.legal-link {
  color: #FF6B6B;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
}

.legal-link:hover {
  color: #FF8E53;
  text-decoration: underline;
}

.legal-separator {
  color: var(--text-secondary);
}

/* 移动端适配 */
@media (max-width: 1024px) {
  .legal-section {
    margin: 20px 0;
  }
  
  .subscribe-legal-links {
    font-size: 15px;
    gap: 6px;
    font-weight: 600;
  }
}

@media (max-width: 480px) {
  .legal-section {
    margin: 15px 0;
  }
  
  .subscribe-legal-links {
    flex-direction: column;
    gap: 4px;
    font-size: 14px;
  }
}

/* 优化弹窗标题区域样式 */
.contact-modal {
  max-width: 420px !important;
  width: 96vw;
}

.contact-modal .modal-header {
  background: transparent !important;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  padding: 14px 24px 6px 24px;
  min-height: unset;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.contact-modal .modal-header h2 {
  color: #fff;
  font-size: 1.08rem;
  font-weight: bold;
  margin: 0;
  line-height: 1.2;
  display: flex;
  align-items: center;
}

@media (min-width: 600px) and (max-width: 1024px) {
  .subscription-plans {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 480px) {
  .comparison-table th {
    font-size: 0.85rem !important;
    font-family: var(--font-family) !important;
    font-weight: 600 !important;
  }
  
  .comparison-table th:first-child {
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    padding: 8px 12px !important;
    text-align: left !important;
    vertical-align: middle !important;
    font-family: var(--font-family) !important;
  }
  
  .comparison-table th:last-child {
    font-size: 0.85rem !important;
    font-family: var(--font-family) !important;
    font-weight: 600 !important;
  }
  
  .comparison-table th.highlight {
    font-size: 0.85rem !important;
    font-family: var(--font-family) !important;
    font-weight: 600 !important;
  }
  
  .comparison-table tr.category td {
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    padding: 8px 12px !important;
    text-align: left !important;
    vertical-align: middle !important;
  }
}

@media (max-width: 480px) {
  .comparison-table td:first-child {
    padding-left: 8px !important;
  }
  .comparison-table tr td.sub-feature {
    padding-left: 18px !important;
  }
} 

/* 订阅计划卡片中的数字专用样式 - 独立于其他地方 */
.plan-card .highlight-price {
  color: #FF6B6B;
  font-weight: bold;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.2em;
  vertical-align: bottom;
}

/* 移动端订阅计划卡片数字样式 */
@media (max-width: 1024px) {
  .plan-card .highlight-price {
    font-size: 1.15em !important;
  }
}

@media (max-width: 480px) {
  .plan-card .highlight-price {
    font-size: 1.25em !important;
  }
}

@media (max-width: 360px) {
  .plan-card .highlight-price {
    font-size: 1.35em !important;
  }
}

@media (max-width: 1024px) {
  .monthly-price-unit {
    vertical-align: baseline !important;
    top: -4px !important;
  }
}

@media (max-width: 1024px) {
  .banner-content, .promotion-card {
    display: flex !important;
    flex-direction: row !important;
    align-items: stretch !important;
    justify-content: space-between !important;
    text-align: left !important;
    gap: 0 12px !important;
    column-gap: 12px !important;
    padding: 10px 12px !important;
    overflow: hidden !important;
  }
  .banner-left, .promotion-left {
    flex: 1 1 0%;
    min-width: 0 !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 8px !important;
    overflow: hidden;
  }
  .banner-info, .promotion-info {
    flex: 1 1 0%;
    min-width: 0;
    text-align: left !important;
    padding: 0 6px;
    font-size: 0.95em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    height: 100% !important;
  }
  .banner-info h3, .promotion-info h3 {
    margin: 0 !important;
    flex-shrink: 0 !important;
  }
  .banner-info p, .promotion-info p {
    margin: 0 !important;
    margin-top: auto !important;
    flex-shrink: 0 !important;
  }
  .banner-right, .promotion-right {
    flex: 0 0 auto !important;
    max-width: 40vw !important;
    min-width: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
    justify-content: space-between !important;
    gap: 4px !important;
    height: 100% !important;
  }
  .banner-button, .promo-button {
    width: auto !important;
    min-width: 90px !important;
    max-width: 100% !important;
    padding: 4px 10px !important;
    margin-top: 0 !important;
    font-size: 0.8em !important;
    height: 28px !important;
    box-sizing: border-box;
    white-space: nowrap;
    flex-shrink: 0 !important;
  }
}

@media (max-width: 1024px) {
  .promotion-info h3 {
    gap: 4px !important;
  }
  .promo-icon {
    display: inline-flex !important;
    align-items: center !important;
    vertical-align: middle !important;
    position: relative !important;
    top: 0 !important;
    font-size: 1.1rem;
  }
}

@media (max-width: 1024px) {
  .banner-icon-text {
    display: inline-flex !important;
    align-items: center !important;
    vertical-align: middle !important;
    position: relative !important;
    top: 0 !important;
    font-size: 1.1rem !important;
    margin-right: 4px !important;
  }
  .banner-info h3 {
    gap: 4px !important;
  }
}

/* 申请试用链接样式 */
.try-free-link {
  color: #FF6B6B;
  text-decoration: underline;
  transition: all 0.3s ease;
  font-weight: 500;
}

.try-free-link:hover {
  color: #FF8E53;
  text-decoration: underline;
}

/* 功能价格明细区域样式 */
.feature-pricing-section {
  margin: 60px 0 !important;
  padding: 40px 0 !important;
  position: relative !important;
  overflow: visible !important;
  z-index: 1 !important;
}

.feature-pricing-section h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 2rem;
  color: var(--text-primary);
  font-weight: 600;
}

.pricing-description {
  text-align: center;
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.pricing-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
  gap: 30px !important;
  margin-bottom: 40px !important;
  position: relative !important;
  overflow: visible !important;
}

.pricing-category {
  background: var(--bg-primary) !important;
  border-radius: var(--radius-lg) !important;
  padding: 30px !important;
  box-shadow: var(--shadow-sm) !important;
  border: 1px solid var(--border-lighter) !important;
  transition: var(--transition-normal) !important;
  position: relative !important;
  overflow: visible !important;
}

.pricing-category:hover {
  box-shadow: var(--shadow-md);
}

.pricing-category h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
  text-align: center;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--border-lighter);
}

.pricing-items {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
  position: relative !important;
  overflow: visible !important;
}

.pricing-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 12px 0 !important;
  border-bottom: 1px solid var(--border-lighter) !important;
  min-height: 20px !important;
  position: relative !important;
  overflow: visible !important;
}

.pricing-item:last-child {
  border-bottom: none;
}

.feature-name {
  font-size: 0.95rem;
  color: var(--text-primary);
  font-weight: 500;
  flex: 1;
}

.feature-cost {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding-left: 16px;
}

.pricing-note {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

.pricing-note p {
  margin: 8px 0;
}

/* 移动端适配 */
@media (max-width: 1024px) {
  .pricing-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
  
  .pricing-category {
    padding: 25px;
  }
}

@media (max-width: 1024px) {
  .feature-pricing-section {
    margin: 40px 0;
    padding: 30px 0;
  }
  
  .feature-pricing-section h2 {
    font-size: 1.8rem;
  }
  
  .pricing-description {
    font-size: 1rem;
    margin-bottom: 30px;
  }
  
  .pricing-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .pricing-category {
    padding: 20px;
  }
  
  .pricing-category h3 {
    font-size: 1.2rem;
  }
  
  .feature-name {
    font-size: 0.9rem;
  }
  
  .feature-cost {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .feature-pricing-section {
    margin: 30px 0;
    padding: 20px 0;
  }
  
  .feature-pricing-section h2 {
    font-size: 1.6rem;
  }
  
  .pricing-description {
    font-size: 0.95rem;
    margin-bottom: 25px;
  }
  
  .pricing-category {
    padding: 18px;
  }
  
  .pricing-category h3 {
    font-size: 1.1rem;
    margin-bottom: 16px;
  }
  
  .pricing-items {
    gap: 10px;
  }
  
  .pricing-item {
    padding: 10px 0;
  }
  
  .feature-name {
    font-size: 0.85rem;
  }
  
  .feature-cost {
    font-size: 0.9rem;
    padding-left: 12px;
  }
  
  .pricing-note {
    font-size: 0.85rem;
  }
} 

.pricing-title {
  text-align: center;
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 18px;
  letter-spacing: 1px;
}

.pricing-table {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.pricing-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-lighter);
  padding: 12px 0;
  font-size: 1rem;
}

.pricing-row:last-child {
  border-bottom: none;
}

.pricing-feature {
  flex: 1;
  color: var(--text-primary);
  font-weight: 400;
  text-align: left;
  word-break: break-all;
}

.pricing-value {
  min-width: 60px;
  text-align: right;
  font-weight: 400;
  font-size: 1.05rem;
  color: var(--primary-color);
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 1px;
} 

.pricing-subtitle {
  text-align: center;
  color: var(--text-secondary);
  font-size: 1rem;
  margin-bottom: 28px;
  margin-top: -18px;
  letter-spacing: 0.5px;
} 

