import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children, requiredRole }) => {
  const { isAuthenticated, user ,logout } = useAuth();
  
  // 检查用户是否已登录
  if (!isAuthenticated) {
    logout();
  }
  
  // 如果需要特定角色，检查用户是否有该角色
  if (requiredRole && user.role !== requiredRole) {
    return <Navigate to="/unauthorized" replace />;
  } 
  
  return <>{children}</>;
};

export default ProtectedRoute; 