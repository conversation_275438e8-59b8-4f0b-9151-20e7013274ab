@import '../styles/theme.css';
@import '../styles/scrollbars.css';
@import '../styles/common.css';
@import '../styles/buttons.css';
@import '../styles/mobile-modal-fix.css';

/* 基础样式设置，不包含过渡效果 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  max-width: 100%; /* 确保所有元素不会超过其父容器宽度 */
}

/* 添加全局字体设置，确保整站风格一致 */
body, html, #root, .app-container, button, input, textarea, select, .ant-btn, .ant-input, .ant-modal, .ant-message, .ant-dropdown, .ant-select {
  font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif !important;
}

/* 侧边栏元素不受全局max-width限制 */
.sidebar,
.sidebar *,
.sidebar-item,
.sidebar-item *,
.sidebar-bottom-btn,
.sidebar-bottom-btn * {
  max-width: none !important; /* 侧边栏元素不受全局max-width限制 */
}

/* 防止元素溢出导致横向滚动条 */
img, video, object, embed, iframe {
  max-width: 100%;
  height: auto;
}

html,
body {
  background-color: var(--bg-primary);
  overflow-x: hidden; /* 防止水平滚动条 */
  min-height: 100%;
  height: 100%;
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  position: relative; /* 确保定位上下文 */
}

/* 移除重复的滚动条样式，使用全局滚动条样式 */
/* 注意：html和body的滚动条样式已在 scrollbars.css 中通过 * 选择器统一处理，
   这里不再重复定义，避免双滚动条问题 */

/* ---------------- 全局布局 ---------------- */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 确保所有高级容器都不会导致溢出 */
#root {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

/* 修复移动端布局 */
@media (max-width: 888px) {
  .content-wrap {
    flex-direction: column;
    margin-top: 90px;
    width: 100%; /* 移动端全宽 */
    margin-left: 0; /* 移动端不需要左侧margin */
    padding: 0;
    overflow-x: hidden;
    /* 移除max-width: 100vw，避免页面宽度变化导致双滚动条问题 */
  }

  .main-content {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    padding: 15px; /* 减小移动端内边距 */
    box-sizing: border-box; /* 确保内边距计算正确 */
  }
}

/* ---------------- 顶部导航栏 ---------------- */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100; /* 降低z-index，从1000改为100，确保弹窗能显示在导航栏之上 */
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-primary);
  height: 60px;
  padding: 0 20px;
  border-bottom: 1px solid var(--border-color);
}

.navbar-left {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 36px; /* 根据需要调整大小 */
  width: auto;
  vertical-align: middle;
  display: block; /* 确保图片显示 */
  max-width: 100%; /* 限制最大宽度 */
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.logo:hover .logo-image {
  filter: brightness(1.2) saturate(1.1);
}

.logo {
  margin-left: 8px;
  margin-right: 60px;
  cursor: pointer;
  padding: 0.5rem 0;
  transition: var(--transition-normal);
  position: relative;
  overflow: visible; /* 改为visible以支持外发光效果 */
}

.logo-text {
  font-size: 28px;
  font-weight: 580;
  background: var(--brand-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

/* 删除底部横线效果 */
.logo::after {
  display: none;
}

/* 新的发光效果 - 使用主题相关的颜色 */
.logo::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90%;
  height: 70%;
  background: var(--brand-gradient);
  border-radius: 6px;
  transform: translate(-50%, -50%) scale(0.7);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 0;
  filter: blur(8px);
}

/* 流动光影效果 */
.logo::after {
  content: '';
  position: absolute;
  top: 0;
  left: -120%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 140, 66, 0.3),
    rgba(255, 60, 106, 0.3),
    transparent
  );
  border-radius: 4px;
  opacity: 0;
  transition: all 0.5s ease;
  z-index: 2;
  pointer-events: none;
}

.logo:hover::before {
  transform: translate(-50%, -50%) scale(0.95);
  opacity: 0.12;
}

.logo:hover::after {
  left: 120%;
  opacity: 1;
  animation: logoShine 0.8s ease-out;
}

@keyframes logoShine {
  0% {
    left: -120%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 120%;
    opacity: 0;
  }
}

.logo:hover .logo-text {
  transform: scale(1.05);
  filter: brightness(1.2) saturate(1.1);
}

.nav-links {
  display: flex;
  list-style-type: none;
  margin: 0;
  padding: 0;
  white-space: nowrap;
}

.nav-item {
  margin: 0 clamp(8px, 2vw, 25px);
  cursor: pointer;
  position: relative;
  font-size: clamp(12px, 1.5vw, 16px);
  color: var(--text-primary);
}

.nav-item:hover {
  color: var(--brand-primary);
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--brand-primary);
}

.navbar-right {
  display: flex;
  align-items: center;
}

.subscribe-btn {
  background: var(--brand-gradient);
  border: none;
  padding: 6px 12px;
  margin-right: 10px;
  color: var(--text-inverse);
  border-radius: var(--radius-sm);
  cursor: pointer;
  white-space: nowrap;
  font-size: clamp(12px, 1.5vw, 16px);
  transition: var(--transition-normal);
}

.subscribe-btn:hover {
  filter: brightness(1.1);
}

.login-btn {
  background-color: var(--bg-primary);
  border: 1px solid var(--brand-primary);
  color: var(--brand-primary);
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
  font-size: clamp(12px, 1.5vw, 16px);
}

.login-btn:hover {
  background-color: var(--brand-primary-lighter);
}

/* ---------------- 内容区布局 ---------------- */
.content-wrap {
  padding-top: 60px; /* 导航栏高度 */
  display: flex;
  min-height: calc(100vh - 0px);
  position: relative;
  width: calc(100% - 160px); /* 宽度减去左侧margin, 额外减少2px确保不会出现滚动条 */
  box-sizing: border-box;
  overflow-x: hidden; /* 禁止横向滚动 */
  background-color: var(--bg-primary);
  margin-left: 158px; /* 使用margin-left替代padding-left */
  transition: margin-left 0s, width 0s; /* 移除过渡效果 */
}

.content-wrap.sidebar-collapsed {
  margin-left: 58px;
  width: calc(100% - 60px); /* 宽度减去折叠后的左侧margin, 额外减少2px确保不会出现滚动条 */
}

.app {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden; /* 在最外层容器添加禁止横向滚动 */
  box-sizing: border-box; /* 确保盒模型计算正确 */
  margin: 0;
  padding: 0;
}

/* ---------------- 左侧侧边栏 ---------------- */
.sidebar {
  width: 158px;
  min-width: 158px;
  flex-shrink: 0;
  border-right: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
  box-sizing: border-box;
  background-color: var(--bg-primary);
  overflow-y: auto;
  position: fixed;
  top: 60px; /* 导航栏高度 */
  left: 0;
  bottom: 0;
  z-index: 95; /* 降低z-index，从100改为95，确保导航栏显示在侧边栏之上 */
  display: flex;
  flex-direction: column;
  /* 移除宽度过渡效果，避免页面加载时的宽度变化导致双滚动条问题 */
  overflow-x: visible; /* 允许元素在水平方向溢出 */
}

/* 折叠侧边栏样式 */
.sidebar.collapsed {
  width: 58px;
  min-width: 58px;
  padding: 1rem 0.5rem;
  box-sizing: border-box;
}

.sidebar.collapsed .sidebar-item {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  justify-content: center;
  margin: 0.32rem 0; /* 从0.75rem减小到0.4rem，与展开状态保持一致 */
  padding-top: 0.85rem;
  padding-bottom: 0.85rem;
  height: 3rem; /* 设置固定高度与非折叠状态一致 */
  box-sizing: border-box;
  margin-left: 0;
  margin-right: 0;
  display: flex;
  align-items: center;
  white-space: nowrap; /* 防止文字换行 */
  overflow: visible; /* 允许内容溢出 */
  max-width: none; /* 覆盖全局max-width限制 */
  position: relative; /* 为绝对定位提示添加相对定位父元素 */
}

.sidebar.collapsed .sidebar-item span {
  display: none;
}

.sidebar.collapsed .sidebar-bottom-btn {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  justify-content: center;
  padding-top: 0.5rem; /* 从0.65rem减小到0.5rem */
  padding-bottom: 0.5rem; /* 从0.65rem减小到0.5rem */
  height: 2rem; /* 从2.5rem减小到2rem */
  box-sizing: border-box;
  margin-top: auto;
  margin-bottom: 0;
  margin-left: 0;
  margin-right: 0;
  white-space: nowrap; /* 防止文字换行 */
  overflow: visible; /* 允许内容溢出 */
  max-width: none; /* 覆盖全局max-width限制 */
}

.sidebar.collapsed .sidebar-bottom-btn span {
  display: none;
}

.sidebar.collapsed h3 {
  justify-content: center;
  padding-left: 0;
  height: 56px;
  padding-bottom: 1.25rem;
  padding-top: 0.5rem;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 1rem; /* 确保与非折叠状态相同 */
  box-sizing: border-box;
  align-items: center;
  line-height: 1.2;
}

.sidebar.collapsed h3 span {
  display: none;
}

.sidebar.collapsed h3 .sidebar-edit-btn,
.sidebar.collapsed h3 .sidebar-back-btn {
  margin-left: 0;
  right: 0;
  position: static; /* 改为静态定位，更好控制居中 */
  width: auto;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-edit-btn svg,
.sidebar-back-btn svg {
  font-size: 0.9rem;
  transform: translateY(1px);
  transition: none;
  display: block; /* 确保显示方式一致 */
}

.sidebar h3 {
  margin: 0;
  padding-bottom: 1.25rem;
  padding-top: 0.5rem;
  padding-left: 0.5rem;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 1rem;
  font-size: 0.95rem;
  font-weight: 500; /* 确保字体粗细一致 */
  display: flex;
  align-items: center;
  justify-content: space-between; /* 确保内容分布一致 */
  gap: 0.5rem;
  color: var(--text-primary);
  box-sizing: border-box;
  height: 40px;
  line-height: 1.2;
  min-height: 40px; /* 确保最小高度一致 */
  max-height: 40px; /* 确保最大高度一致 */
}

.sidebar h3 span {
  font-size: 0.95rem;
  font-weight: 500;
  line-height: 1.2;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-section {
  margin-top: 0;
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  padding: 0;
  box-sizing: border-box;
}

.sidebar.collapsed .sidebar-section {
  margin-top: 0;
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  padding: 0;
  box-sizing: border-box;
}

.sidebar-section h2 {
  margin-bottom: 1.2rem;
  font-size: 1.25rem;
}

.sidebar-item {
  margin: 0.25rem 0; /* 从0.32rem减小到0.25rem */
  cursor: pointer;
  padding: 0.85rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-primary);
  transition: background-color 0.2s ease;
  border-radius: 4px;
  padding-left: 1.25rem;
  padding-right: 1.5rem;
  margin-left: -1rem;
  margin-right: -1rem;
  font-size: 0.92rem;
  justify-content: flex-start;
  height: 3.28rem; /* 设置固定高度 */
  box-sizing: border-box;
  white-space: nowrap; /* 防止文字换行 */
  overflow: visible; /* 允许内容溢出 */
  max-width: none; /* 覆盖全局max-width限制 */
}

.sidebar-item span {
  white-space: nowrap; /* 防止文字换行 */
  overflow: visible; /* 允许内容溢出 */
  max-width: none; /* 覆盖全局max-width限制 */
}

.sidebar-item svg {
  font-size: 1.15rem;
  color: var(--text-secondary);
  transition: none;
  flex-shrink: 0;
  width: 1.15rem;
  margin-right: 0.25rem;
}

.sidebar-item:hover {
  color: var(--brand-primary);
  background: var(--bg-hover);
}

.sidebar-item:hover svg {
  color: var(--brand-primary);
  transition: none;
}

.sidebar-item.active {
  color: var(--brand-primary);
}

.sidebar-item.active svg {
  color: var(--brand-primary);
}

.sidebar-item svg {
  font-size: 1.15rem;
  color: var(--text-secondary);
  transition: none;
  flex-shrink: 0;
  width: 1.15rem;
  margin-right: 0.25rem;
}

.sidebar-item:hover svg {
  color: var(--brand-primary);
  transition: none;
}

/* 底部按钮样式 */
.sidebar-bottom-btn {
  margin-top: auto;
  margin-bottom: 0rem;
  margin-left: -1rem;
  margin-right: -1rem;
  padding: 0.5rem 0;
  padding-left: 1.25rem;
  padding-right: 1.5rem;
  border: none;
  background: transparent;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.85rem;
  transition: background-color 0s ease;
  border-radius: 4px;
  justify-content: flex-start;
  height: 2rem; /* 从2.5rem减小到2rem */
  box-sizing: border-box;
  white-space: nowrap; /* 防止文字换行 */
  overflow: visible; /* 允许内容溢出 */
  max-width: none; /* 覆盖全局max-width限制 */
}

.sidebar-bottom-btn span {
  white-space: nowrap; /* 防止文字换行 */
  overflow: visible; /* 允许内容溢出 */
  max-width: none; /* 覆盖全局max-width限制 */
}

.sidebar-bottom-btn:hover {
  background: var(--bg-hover);
  color: var(--brand-primary);
}

.sidebar-bottom-btn svg {
  font-size: 1.15rem;
  flex-shrink: 0;
  width: 1.15rem;
  margin-right: 0.25rem;
  color: var(--text-secondary);
  transition: none;
}

.sidebar-bottom-btn:hover svg {
  color: var(--brand-primary);
}

/* ---------------- 主体内容 ---------------- */
.main-content {
  scroll-behavior: unset;
  overflow: hidden;
  flex: 1;
  padding: 20px;
  box-sizing: border-box;
  min-width: 0; /* 确保可以收缩 */
  width: 100%; /* 保持100%宽度 */
  overflow-x: hidden; /* 禁止横向滚动 */
  background-color: var(--bg-primary);
  max-width: 100%; /* 添加最大宽度限制 */
  position: relative; /* 添加相对定位 */
}

/* 修改输入组件布局样式 */
.input-group {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}

.input-group input {
  flex: 1;
  min-width: 0;
  height: 40px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 14px;
}

.generate-btn {
  margin-top: 20px;
  padding: 8px 16px;
  background: var(--brand-gradient);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition-normal);
}

.generate-btn:hover {
  filter: brightness(1.1);
}

/* ---------------- 弹窗 ---------------- */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-mask);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  position: relative;
  background-color: var(--bg-primary);
  width: 100%;
  max-width: 480px;
  border-radius: 8px;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  padding-bottom: 0;
  border-bottom: none;
  background-color: var(--bg-primary);
}

.modal-header h2 {
  margin: 0px;
  padding: 0px 20px 10px;
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
}

[data-theme="dark"] .modal-header h2 {
  color: var(--text-primary);
}

.close-btn {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-hover);
  cursor: pointer;
  padding: 0;
  border-radius: 50%;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.close-btn:hover {
  background: var(--bg-active);
  color: var(--text-primary);
}

.close-btn:hover::before,
.close-btn:hover::after {
  background: var(--text-primary);
}

.feature-list {
  max-height: unset;
  overflow-y: auto;
  margin: 5px 0 0;
  flex: 1;
}

.feature-list h4 {
  font-size: 0.8rem;
  margin-top: 12px;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: normal;
  padding-left: 8px;
  text-align: right;
  padding-right: 8px;
  padding-left: 0;
}

/* 调整分组间距 */
.feature-list > div {
  margin-bottom: 16px;
}

.feature-list > div:last-child {
  margin-bottom: 0;
}

/* 使用通用开关样式 */
/* 开关按钮容器样式 */
.checkbox-label {
  position: relative;
  display: flex;
  align-items: center;
  margin: 8px 0;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 0.85rem;
  color: var(--text-primary);
  transition: all 0.2s ease;
  border-radius: 4px;
  justify-content: space-between;
  flex-direction: row-reverse;
  gap: 12px;
}

.checkbox-label input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
  display: none;
}

.checkbox-label .toggle-track {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 18px;
  background-color: var(--bg-active);
  border-radius: 10px;
  transition: all 0.3s ease;
}

/* 开关按钮的滑块 */
.checkbox-label .toggle-track::before {
  position: absolute;
  content: '';
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: var(--bg-primary);
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

/* 选中状态样式 */
.checkbox-label input[type="checkbox"]:checked + .toggle-track {
  background: var(--brand-gradient);
}

/* 选中状态滑块位置 */
.checkbox-label input[type="checkbox"]:checked + .toggle-track::before {
  transform: translateX(14px);
  background-color: var(--bg-primary);
}

.sidebar-edit-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 2px;
  cursor: pointer;
  color: var(--text-primary);
  margin-left: auto;
  margin-right: 0;
  position: relative;
  right: -0.5rem;
  transition: none;
  height: 24px; /* 确保按钮高度一致 */
  width: 24px; /* 确保按钮宽度一致 */
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.sidebar-edit-btn svg {
  font-size: 0.9rem;
  transform: translateY(1px);
  transition: none;
}

.sidebar-edit-btn:hover {
  color: var(--brand-primary);
}

.sidebar-back-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 2px;
  cursor: pointer;
  color: var(--text-primary);
  margin-left: auto;
  margin-right: 0;
  position: relative;
  right: -0.5rem;
  transition: none;
  height: 24px; /* 确保按钮高度一致 */
  width: 24px; /* 确保按钮宽度一致 */
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.sidebar-back-btn svg {
  font-size: 0.9rem;
  transform: translateY(1px);
  transition: none;
}

.sidebar-back-btn:hover {
  color: var(--brand-primary);
}
/* 用户菜单样式 */
.user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dropdown-container {
  position: relative;
}

/* 添加扩大悬停区域的伪元素 */
.dropdown-container::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: transparent;
  z-index: -1;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 2px;
  width: 200px;
  min-width: 160px;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
  overflow: visible;
  padding-top: 4px;
  display: flex;
  flex-direction: column;
}

/* 添加下拉菜单上方的扩展区域，解决菜单与按钮之间的缝隙问题 */
.user-dropdown::before {
  content: '';
  position: absolute;
  top: -7px; /* 向上延伸7px，覆盖按钮和菜单之间的间隙 */
  left: 0;
  right: 0;
  height: 7px;
  background: transparent;
}

.dropdown-item {
  cursor: pointer;
  transition: var(--transition-normal);
  color: var(--text-primary);
}

.logout-btn {
  background: none;
  border: 1px solid var(--brand-primary);
  color: var(--brand-primary);
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
  font-size: clamp(12px, 1.5vw, 16px);
}

.logout-btn:hover {
  background-color: var(--brand-primary-lighter);
}

/* 移动端用户中心按钮优化 */
@media (max-width: 888px) {
  .logout-btn {
    padding: 6px 11px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .logout-btn {
    padding: 5px 9px;
    font-size: 13px;
  }
}

/* 通知提示样式 */
.notification {
  position: fixed;
  top: 16px;
  right: 16px;
  padding: 12px 24px;
  border-radius: 4px;
  color: var(--text-inverse);
  font-size: 14px;
  z-index: 1000;
  animation: slideInRight 0.3s ease, fadeOut 0.3s ease 2.7s;
}

.notification.success {
  background-color: var(--success-color);
}

.notification.error {
  background-color: var(--error-color);
}

.notification.warning {
  background-color: var(--warning-color);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


/* 首页样式 */
.home-page {
  width: 100%;
  max-width: 100%;  /* 修改为100%以适应窗口宽度 */
  margin: 0;        /* 移除外边距 */
  padding: 0 24px;  /* 修改内边距 */
  box-sizing: border-box;
}

/* 使用技巧区域样式 */
.tips-section {
  margin: 24px auto 32px;
  max-width: 1800px;
  padding: 0 var(--spacing-md);
  position: relative; /* 为翻页按钮提供定位上下文 */
}

.tips-section h2 {
  font-size: clamp(1rem, 1.6vw, 1.2rem);
  margin-bottom: clamp(10px, 1.5vw, 14px);
  color: var(--text-primary);
  font-weight: 600;
  letter-spacing: 0.02em;
}

.tips-container {
  width: 100%;
  overflow-x: hidden; /* 移除横向滚动条 */
  overflow-y: hidden;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  padding: 4px 0;
  margin: 0 -4px;
  transform: translateZ(0);
  will-change: scroll-position;
  backface-visibility: hidden;
  perspective: 1000px;
  /* 移除 position: relative，因为按钮现在相对于 tips-section 定位 */
  position: relative; /* 为翻页按钮提供相对于卡片区域的定位上下文 */
}

.tips-cards {
  display: flex;
  gap: clamp(16px, 1.5vw, 20px);
  width: max-content;
  padding: 4px 0;
  transform: translateZ(0);
  will-change: transform;
  transition: transform 0.3s ease; /* 添加平滑滚动动画 */
}

/* 删除自定义滚动条样式，因为不再需要横向滚动条 */
.tips-container::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}

.tips-container::-webkit-scrollbar-track {
  display: none;
}

.tips-container::-webkit-scrollbar-thumb {
  display: none;
}

.tips-container::-webkit-scrollbar-thumb:hover {
  display: none;
}

/* 暗色主题特殊处理 - 移除滚动条相关样式 */
[data-theme="dark"] .tips-container::-webkit-scrollbar-thumb {
  display: none;
}

[data-theme="dark"] .tips-container::-webkit-scrollbar-thumb:hover {
  display: none;
}

/* 优化 tip-card 悬停效果 */
.tip-card {
  flex: 0 0 calc((100% - 72px) / 4);
  min-width: 160px;
  max-width: 180px;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  transform: translate3d(0, 0, 0);
  will-change: transform, box-shadow;
  transition: transform 0.15s cubic-bezier(0.2, 0, 0.15, 1), box-shadow 0.15s cubic-bezier(0.2, 0, 0.15, 1);
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
  perspective: 1000px;
  cursor: pointer;
}

.tip-card:hover {
  transform: translate3d(0, -5px, 0);
  box-shadow: var(--shadow-md);
}

.tip-image {
  width: 100%;
  height: 48px;
  overflow: hidden;
}

/* PC端专用样式，确保不受移动端影响 */
@media (min-width: 889px) {
  .tip-image {
    height: 40px !important;
  }
  
  .tip-title {
    font-size: var(--font-size-lg) !important;
    margin: 0 0 12px 0 !important;
    padding: 0 !important;
    color: var(--text-primary) !important;
    text-align: left !important;
    line-height: 1.5 !important;
  }
}

.tip-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.tip-content {
  padding: calc(var(--spacing-md) + 8px) var(--spacing-md);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.tip-content h3 {
  font-size: var(--font-size-lg);
  margin: 0 0 12px 0;
  padding: 0;
  color: var(--text-primary);
}

.tip-content p {
  font-size: var(--font-size-sm);
  margin: 0;
  padding: 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 桌面端显示完整副标题，移动端隐藏 */
.tip-content .tip-description-desktop {
  display: block !important;
  font-size: var(--font-size-sm);
  margin: 0;
  padding: 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

.tip-content .tip-description-mobile {
  display: none !important;
  font-size: 12px;
  margin: 0;
  padding: 0;
  color: var(--text-secondary);
  line-height: 1.4;
  text-align: center;
  margin-top: 2px;
}

/* 导航按钮样式 */
.tips-nav-button {
  position: absolute;
  top: calc(50% + 20px); /* 在居中的基础上向下移动20px */
  transform: translateY(-50%);
  height: 60px;
  width: 40px;
  border: 1px solid var(--border-color); /* 添加描边，参考侧边栏样式 */
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: transform 0.05s ease; /* 进一步缩短过渡时间，让效果更迅速 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  user-select: none;
  background: var(--bg-primary); /* 使用纯色背景，移除透明度 */
  border-radius: var(--radius-sm); /* 使用与订阅按钮相同的圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tips-nav-button.prev {
  left: 0; /* 相对于 tips-container 的左边缘 */
}

.tips-nav-button.next {
  right: 0; /* 相对于 tips-container 的右边缘 */
}

.tips-nav-button::before {
  content: '';
  width: 12px;
  height: 12px;
  border-right: 2px solid var(--brand-primary);
  border-bottom: 2px solid var(--brand-primary);
  display: block;
  transition: all 0.05s ease; /* 同步缩短过渡时间 */
}

.tips-nav-button.prev::before {
  transform: rotate(135deg);
}

.tips-nav-button.next::before {
  transform: rotate(-45deg);
}

.tips-nav-button:hover {
  transform: translateY(-50%) scale(1.05);
}

.tips-nav-button:hover::before {
  border-color: var(--brand-primary);
}

.tips-nav-button:disabled {
  opacity: 0;
  cursor: not-allowed;
  transform: translateY(-50%) scale(1);
  pointer-events: none; /* 禁用鼠标事件 */
  transition: opacity 1s ease, transform 0.1s ease; /* 延长消失时间到2秒，防止误触 */
}

.tips-nav-button:disabled:hover {
  background: var(--bg-primary);
  transform: translateY(-50%) scale(1);
  opacity: 0; /* 确保悬浮时也保持隐藏 */
}

/* 暗色主题下的按钮样式 */
[data-theme="dark"] .tips-nav-button {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .tips-nav-button:hover {
  background: var(--bg-primary);
  transform: translateY(-50%) scale(1.05);
}

/* 移动端隐藏翻页按钮 */
@media (max-width: 888px) {
  .tips-nav-button {
    display: none;
  }
  
  .tips-container {
    overflow-x: auto; /* 移动端恢复横向滚动 */
  }
  
  .tips-container::-webkit-scrollbar {
    display: block; /* 移动端显示滚动条 */
    height: var(--scrollbar-width, 7px);
  }
  
  .tips-container::-webkit-scrollbar-track {
    display: block;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
  }
  
  .tips-container::-webkit-scrollbar-thumb {
    display: block;
    background: var(--text-tertiary) !important;
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
  }
  
  .tips-container::-webkit-scrollbar-thumb:hover {
    display: block;
    background: var(--text-secondary) !important;
  }
  
  [data-theme="dark"] .tips-container::-webkit-scrollbar-thumb {
    display: block;
    background: var(--text-tertiary) !important;
  }
  
  [data-theme="dark"] .tips-container::-webkit-scrollbar-thumb:hover {
    display: block;
    background: var(--text-secondary) !important;
  }
}

/* 功能模块介绍样式 */
.features-section {
  margin: var(--spacing-lg) auto;
  max-width: 1800px;
  padding: 0 var(--spacing-md);
  width: 100%;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: clamp(16px, 1.5vw, 20px); /* 从 clamp(20px, 2vw, 24px) 减小到 clamp(16px, 1.5vw, 20px) */
  margin-top: 20px;
  width: 100%;
  margin: 0 auto;
}

/* 优化 feature-card 悬停效果 */
.feature-card {
  background: var(--bg-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  transform: translate3d(0, 0, 0);
  will-change: transform, box-shadow;
  transition: transform 0.15s cubic-bezier(0.2, 0, 0.15, 1), box-shadow 0.15s cubic-bezier(0.2, 0, 0.15, 1);
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
  perspective: 1000px;
}

.feature-card:hover {
  transform: translate3d(0, -5px, 0);
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.feature-card img {
  padding: 0px;
  margin: 0px;
  width: 100%;
  aspect-ratio: 1 / 1;  /* 设置1:1的宽高比 */
  object-fit: cover;
  display: block;  /* 移除图片底部的间隙 */
}

.feature-content {
  padding: 12px;
  flex-grow: 1;  /* 让内容区域填充剩余空间 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.feature-content h3 {
  font-size: var(--font-size-lg);
  margin-bottom: 8px;
  color: var(--text-primary);
}

.feature-content p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 即将上线卡片样式 */
.feature-image {
  position: relative;
  width: 100%;
  aspect-ratio: 1 / 1;
  overflow: hidden;
}

.coming-soon-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.7);  /* 降低图片亮度以突出文字 */
  transition: transform 0.3s ease;
}

.feature-card:hover .coming-soon-img {
  transform: scale(1.05);
}

.coming-soon-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);  /* 添加半透明遮罩 */
}

.coming-soon-content h3 {
  font-size: 1.3rem;  /* 从1.8rem调小到1.5rem */
  color: white;
  margin: 0;
  line-height: 1.2;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 响应式调整 */
@media (max-width: 888px) {
  .coming-soon-content h3 {
    font-size: 1.1rem;  /* 从1.3rem调小到1.1rem */
  }
}

/* 优秀案例展示样式 */
.showcase-section {
  margin: 32px auto;
  max-width: 1800px;
  padding: 0 24px;
  width: 100%;
}

.showcase-waterfall {
  width: 100%;
  margin: 0 auto;
  padding: 0;
  /* 移除 column-count 相关样式，使用 Masonry 库 */
}

.showcase-item {
  width: calc(20% - 12.8px); /* 5列布局 */
  margin-bottom: 16px;
  background: var(--bg-primary);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  opacity: 1;
  visibility: visible;
  position: relative;
  break-inside: avoid; /* 防止卡片被分割 */
}

.showcase-item img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

/* 响应式布局 */
@media (max-width: 1800px) {
  .showcase-item {
    width: calc(25% - 12px); /* 4列 */
  }
}

@media (max-width: 1400px) {
  .showcase-item {
    width: calc(33.333% - 11px); /* 3列 */
  }
}

@media (max-width: 1024px) {
  .showcase-item {
    width: calc(33.333% - 11px); /* 3列 - 移动端至少显示3列 */
  }
}

@media (max-width: 768px) {
  .showcase-item {
    width: calc(33.333% - 11px); /* 3列 - 中等移动端显示3列 */
  }
}

@media (max-width: 480px) {
  .showcase-item {
    width: calc(50% - 12px); /* 2列 - 小屏幕移动端显示2列 */
    margin-bottom: 16px;
  }
}

.showcase-item.hidden {
  display: none;
  opacity: 0;
  visibility: hidden;
  height: 0;
  margin: 0;
  padding: 0;
  border: none;
}

/* 隐藏状态样式 */
.showcase-item.hidden {
  display: none;
  opacity: 0;
  visibility: hidden;
  height: 0;
  margin: 0;
  padding: 0;
  border: none;
}

.showcase-item:hover img {
  transform: scale3d(1.05, 1.05, 1);
}

/* 按钮容器样式修改 */
.showcase-buttons {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  opacity: 0;
  transition: opacity 0.15s cubic-bezier(0.2, 0, 0.15, 1);
  pointer-events: none;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0);
  will-change: opacity, background-color;
  z-index: 2;  /* 添加z-index确保在图片上层 */
}

.showcase-item:hover .showcase-buttons {
  opacity: 1;
  pointer-events: auto;
  background: rgba(0, 0, 0, 0.5);
}

/* 按钮基础样式 */
.showcase-btn {
  background: var(--bg-primary); /* 使用主题背景色 */
  color: var(--text-primary);    /* 使用主题文字色 */
  border: none;
  padding: 6px 12px;
  border-radius: 4px;  /* 减小圆角 */
  cursor: pointer;
  font-size: 13px;
  font-weight: normal;  /* 改为普通字重 */
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 预览按钮样式修改 */
.preview-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: transparent;
  color: white;
  font-size: 16px;   /* 稍微加大字号，从 15px 改为 16px */
  text-shadow: 0 2px 3px rgba(0, 0, 0, 0.4);  /* 加深文字阴影 */
  pointer-events: auto; /* 修改为auto，使其可点击 */
}

/* 做同款按钮样式 */
.showcase-create-btn {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  width: 100%;
  height: 50px;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: 0;
  backdrop-filter: blur(4px);
  border-top: 1px solid var(--border-color) !important;
  pointer-events: auto;
  transform: translate3d(0, 100%, 0);
  transition: transform 0.15s cubic-bezier(0.2, 0, 0.15, 1), background-color 0.15s ease;
  will-change: transform, background-color;
  opacity: 0.95;
}

.showcase-item:hover .showcase-create-btn {
  transform: translate3d(0, 0, 0);
}

/* 移除预览按钮的悬停效果，保留做同款按钮的效果 */
.showcase-create-btn:hover {
  background: var(--bg-hover);  /* 使用主题悬浮背景色 */
}

.showcase-info {
  padding: 16px;
  background: #fff;
}

.showcase-info h3 {
  margin: 0 0 8px 0;
  font-size: clamp(1rem, 1.8vw, 1.2rem);
  color: var(--text-primary);
}

.showcase-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: clamp(0.85rem, 1.2vw, 0.95rem);
  line-height: 1.4;
}

/* 响应式布局调整 */
@media (max-width: 1800px) {
  .showcase-waterfall {
    /* 移除 column-count，由 Masonry 处理 */
  }
}

@media (max-width: 1400px) {
  .showcase-waterfall {
    /* 移除 column-count，由 Masonry 处理 */
  }
}

@media (max-width: 1024px) {
  .showcase-waterfall {
    /* 移除 column-count，由 Masonry 处理 */
  }

  .promotion-content {
    padding: 40px 24px;
  }
}

@media (max-width: 888px) {
  .home-page {
    padding: 0 16px;
  }

  .promotion-banner {
    height: min(200px, 35vh);  /* 调整移动端高度 */
  }

  .promotion-content {
    padding: 20px 16px;  /* 减小内边距 */
  }

  .promotion-content h1 {
    font-size: clamp(1.1rem, 4vw, 1.4rem);  /* 调整移动端字体大小 */
  }

  .tips-section,
  .features-section,
  .showcase-section {
    padding: 0 16px;
  }

  .showcase-waterfall {
    /* 移除 column-count，由 Masonry 处理 */
  }

  .showcase-item {
    margin-bottom: 16px;
  }

  .tips-cards {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
  }

  /* 使用技巧卡片在移动端的尺寸调整 */
  .tip-card {
    min-width: 118px;
    max-width: 118px;
  }

  .tip-image {
    height: 32px;
  }

  .tip-content {
    padding: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 32px;
  }

  /* 移动端专用样式 */
  .tip-title {
    font-size: 14px !important;
    margin: 20px 12px !important;
    text-align: center !important;
    line-height: 1.2 !important;
  }
  
  /* 移动端图标样式 */
  .tip-image svg {
    width: 18px !important;
    height: 18px !important;
  }

  .tip-content p {
    display: none; /* 隐藏副标题 */
  }

  .tips-nav-button {
    width: 32px;
    height: 32px;
  }

  .tips-nav-button::before {
    font-size: 16px;
  }

  .feature-cards {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
  }

  .feature-card {
    min-height: 200px;
  }

  .feature-content {
    padding: 16px;
  }

  .feature-content h3 {
    font-size: var(--font-size-lg);
    margin-bottom: 8px;
  }

  .feature-content p {
    font-size: var(--font-size-md);
    line-height: 1.4;
  }
  
  /* 768px以下优化卡片内容 */
  @media (max-width: 768px) {
    .feature-content {
      padding: 12px;
    }
    
    .feature-content h3 {
      font-size: var(--font-size-md);
      margin-bottom: 6px;
    }
    
    .feature-content p {
      font-size: var(--font-size-sm);
      line-height: 1.3;
    }
  }
  
  /* 480px以下进一步优化 */
  @media (max-width: 480px) {
    .feature-content {
      padding: 10px;
    }
    
    .feature-content h3 {
      font-size: var(--font-size-sm);
      margin-bottom: 4px;
    }
    
    .feature-content p {
      font-size: var(--font-size-xs);
      line-height: 1.2;
    }
  }

  .sidebar-item {
    padding: 12px 16px;
    font-size: var(--font-size-md);
  }

  .sidebar-item:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 16px;
    right: 16px;
    height: 1px;
    background: var(--border-color);
  }

  .sidebar-item svg {
    width: 20px;
    height: 20px;
  }

  .sidebar-section {
    margin-bottom: 24px;
  }
}

@media (max-width: 768px) {
  .feature-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
  }
}

@media (max-width: 480px) {
  .home-page {
    padding: 0 12px;
  }

  .promotion-banner {
    height: min(180px, 30vh);
  }

  .promotion-content {
    padding: 16px 12px;
  }

  .tips-section,
  .features-section,
  .showcase-section {
    padding: 0 12px;
  }

  .tips-cards {
    grid-template-columns: 1fr;
  }
  
  .feature-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .showcase-waterfall {
    /* 移除 column-count，由 Masonry 处理 */
  }

  /* 移除这里的showcase-item样式覆盖，使用上面定义的响应式样式 */
}

/* section标题样式调整 */
.tips-section h2,
.features-section h2,
.showcase-section h2 {
  font-size: clamp(1rem, 1.6vw, 1.2rem);
  margin-bottom: clamp(10px, 1.5vw, 14px);
  color: var(--text-primary);
  font-weight: 600;
  letter-spacing: 0.02em;
}

/* 优化卡片内容的响应式样式 */
.feature-content h3,
.tip-card h3,
.showcase-info h3 {
  font-size: clamp(0.95rem, 1.5vw, 1.1rem);
  margin-bottom: 6px;
}

.feature-content p,
.tip-card p,
.showcase-info p {
  font-size: clamp(0.8rem, 1vw, 0.9rem);
  line-height: 1.4;
}

/* 加载更多按钮容器 */
.load-more-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 40px;
  margin-bottom: 20px;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.load-more-container.hidden {
  opacity: 0;
  visibility: hidden;
  height: 0;
  margin: 0;
}

/* 加载更多按钮 */
.load-more-btn {
  background: var(--brand-gradient);
  color: var(--text-inverse);
  border: none;
  padding: 8px 32px;
  border-radius: 30px;
  font-size: 1rem;
  font-family: var(--font-family);
  font-weight: 520;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 60, 106, 0.2);
  margin: 0 auto;
}

.load-more-btn:hover {
  box-shadow: 0 4px 12px rgba(255, 60, 106, 0.3);
}

/* 加载完成消息 */
.load-more-message {
  text-align: center;
  margin: 40px 0;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-family: var(--font-family);
  padding: 12px;
  background: var(--bg-secondary);
  border-radius: 8px;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 888px) {
  .load-more-container {
    margin-top: 30px;
  }

  .load-more-btn {
    padding: 6px 24px;
    font-size: 0.9rem;
  }

  .load-more-message {
    margin: 24px 0;
    font-size: var(--font-size-xs);
    padding: 10px;
  }
}

/* 响应式调整 */
@media (max-width: 888px) {
  .tips-section h2,
  .features-section h2,
  .showcase-section h2 {
    font-size: clamp(0.95rem, 1.4vw, 1.1rem);
    margin-bottom: 10px;
  }
}

@media (max-width: 480px) {
  .tips-section h2,
  .features-section h2,
  .showcase-section h2 {
    font-size: 0.95rem;
    margin-bottom: 8px;
  }
}

.tip-card.more-tips {
  background: var(--bg-primary);
  transition: all 0.3s ease;
}

.tip-card.more-tips:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.tip-card.more-tips .tip-image {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, rgba(var(--brand-primary-rgb), 0.7), rgba(var(--brand-primary-rgb), 0.85));
  color: var(--color-white);
  position: relative;
  overflow: hidden;
}

.tip-card.more-tips .tip-content h3 {
  color: var(--text-primary);
}

.tip-card.more-tips .tip-image svg {
  width: 48px;
  height: 48px;
  opacity: 0.9;
  transition: transform 0.3s ease;
}

/* 移动端适配样式 */
@media (max-width: 888px) {
  /* 导航栏适配 */
  .navbar {
    padding: 0 12px;
    height: 50px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
  }

  /* 副导航栏显示 */
  .sub-navbar {
    display: block;
    position: fixed;
    top: 50px;
    left: 0;
    right: 0;
    z-index: 99; /* 降低z-index，从999改为99，确保弹窗能显示在副导航栏之上 */
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-primary);
  }

  /* 内容区适配 */
  .content-wrap {
    flex-direction: column;
    margin-top: 90px;
    width: 100%;
    margin-left: 0; /* 移动端不需要左侧margin */
    padding: 0;
    overflow-x: hidden;
    background-color: var(--bg-primary);
  }

  /* 折叠状态下的内容区域在移动端也需要重置margin */
  .content-wrap.sidebar-collapsed {
    margin-left: 0;
    width: 100%;
  }

  /* 侧边栏适配 */
  .sidebar {
    position: static;
    width: 100%;
    min-width: 100%;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    z-index: 95; /* 降低z-index，从100改为95，确保导航栏显示在侧边栏之上 */
  }

  /* 折叠状态在移动端的特殊处理 */
  .sidebar.collapsed {
    width: 100%;
    min-width: 100%;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    z-index: 95; /* 降低z-index，从100改为95，确保导航栏显示在侧边栏之上 */
  }

  .sidebar h3 {
    display: none;
  }

  .sidebar-section {
    display: flex;
    flex-direction: row;
    padding: 0;
    margin: 0;
    white-space: nowrap;
    position: relative;
    z-index: 94; /* 降低z-index，从998改为94，确保导航栏显示在侧边栏区域之上 */
    background-color: var(--bg-primary);
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    height: 42px;
    align-items: center;
  }

  /* 折叠状态下的侧边栏区域在移动端保持水平布局 */
  .sidebar.collapsed .sidebar-section {
    display: flex;
    flex-direction: row;
    padding: 0;
    margin: 0;
    white-space: nowrap;
    position: relative;
    z-index: 94; /* 降低z-index，从998改为94，确保导航栏显示在侧边栏区域之上 */
    background-color: var(--bg-primary);
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    height: 42px;
    align-items: center;
  }

  .sidebar-item {
    padding: 0 12px;
    margin: 0;
    font-size: 13px;
    flex-shrink: 0;
    border-radius: 0;
    background: none;
    position: relative;
    height: 42px;
    line-height: 42px;
    display: flex;
    align-items: center;
    color: var(--text-primary);
  }

  /* 折叠状态下的侧边栏项目在移动端只显示图标 */
  .sidebar.collapsed .sidebar-item {
    padding: 0 12px;
    margin: 0;
    font-size: 13px;
    flex-shrink: 0;
    border-radius: 0;
    background: none;
    position: relative;
    height: 42px;
    line-height: 42px;
    display: flex;
    align-items: center;
    color: var(--text-primary);
    justify-content: center;
    gap: 0;
  }

  /* 折叠状态下在移动端隐藏文字，只显示图标 */
  .sidebar.collapsed .sidebar-item span {
    display: none;
  }

  .sidebar-item:hover {
    color: var(--brand-primary);
    background-color: var(--bg-hover);
  }

  .sidebar-item.active {
    color: var(--brand-primary);
  }

  .sidebar-item svg {
    color: var(--text-secondary);
  }

  .sidebar-item:hover svg,
  .sidebar-item.active svg {
    color: var(--brand-primary);
  }

  /* 小尺寸移动设备图标缩小 */
  .sidebar-item svg {
    font-size: 15px !important;
    width: 15px !important;
    height: 15px !important;
  }

  /* 小尺寸移动设备图标与标题间距缩小 */
  .sidebar-item {
    gap: 2px !important;
  }
}

/* 更小屏幕的适配 */
@media (max-width: 480px) {
  .showcase-waterfall {
    column-count: 1;
  }

  .feature-cards {
    grid-template-columns: repeat(2, 1fr);  /* 即使在更小的屏幕上也保持两列 */
    gap: 8px;  /* 减小间距以适应更小的屏幕 */
  }

  .feature-content {
    padding: 8px;  /* 减小内边距 */
  }

  .feature-content h3 {
    font-size: 0.85rem;  /* 稍微减小字体大小 */
    margin-bottom: 4px;
  }

  .feature-content p {
    font-size: 0.7rem;
  }
}

/* 添加汉堡菜单按钮样式 */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
}

.mobile-menu-btn span {
  display: block;
  width: 20px;
  height: 2px;
  background-color: var(--text-primary);
  margin: 4px 0;
  transition: all 0.3s ease;
}

@media (max-width: 888px) {
  .mobile-menu-btn {
    display: block;
  }

  .nav-links.mobile-active {
    display: flex;
    white-space: nowrap;
    padding: 10px 0;
  }

  .nav-links.mobile-active .nav-item {
    margin: 0 clamp(4px, 1.5vw, 8px);
    font-size: clamp(10px, 1.2vw, 14px);
  }
}

/* ---------------- 移动端副导航栏 ---------------- */
.sub-navbar {
  display: none;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 0;  /* 移除内边距以确保导航项能完全填充宽度 */
}

.sub-nav-links {
  display: flex;
  list-style-type: none;
  margin: 0;
  padding: 0;
  height: 40px;
  width: 100%;  /* 确保占满整个宽度 */
}

.sub-nav-item {
  flex: 1;  /* 让每个导航项平均分配空间 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 15px;  /* 从默认的14px增加到15px */
  padding: 0 8px;  /* 减小内边距以适应更大的文字 */
  white-space: nowrap;  /* 防止文字换行 */
  transition: color 0.2s ease;
  color: var(--text-primary);
}

.sub-nav-item:hover {
  color: var(--brand-primary);
}

.sub-nav-item.active {
  color: var(--brand-primary);
}

.sub-nav-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;  /* 居中定位 */
  transform: translateX(-50%);  /* 水平居中 */
  width: max-content;  /* 宽度适应内容 */
  min-width: 2em;  /* 最小宽度 */
  max-width: 80%;  /* 最大宽度 */
  height: 2px;
  background-color: var(--brand-primary);
}

@media (max-width: 888px) {
  .navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100; /* 降低z-index，从1000改为100，确保弹窗能显示在导航栏之上 */
    height: 50px;  /* 从54px减小到50px */
  }

  .sub-navbar {
    display: block;
    position: fixed;
    top: 50px;  /* 从54px减小到50px */
    left: 0;
    right: 0;
    z-index: 99; /* 降低z-index，从999改为99，确保弹窗能显示在副导航栏之上 */
  }

  .content-wrap {
    margin-top: 75px;  /* 从85px减小到75px */
  }

  .nav-links {
    display: none;
  }

  .mobile-menu-btn {
    display: none;
  }

  .sub-nav-item {
    font-size: 14px;  /* 在移动端稍微调小一点 */
    padding: 0 4px;  /* 移动端减小内边距 */
  }
}

/* ---------------- 移动端提示消息 ---------------- */
.mobile-tip {
  display: none;  /* 默认不显示 */
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: 12px 50px 12px 20px;
  text-align: center;
  font-size: 13px;
  line-height: 1.4;
  backdrop-filter: blur(4px);
  z-index: 97; /* 降低z-index，从999改为97，确保弹窗能显示在移动端提示之上 */
  box-shadow: 0 -1px 6px rgba(0, 0, 0, 0.1);
  align-items: center;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
  min-height: 44px;
  border-top: 1px solid var(--border-color);
}

.mobile-tip .close-tip {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 6px;
  cursor: pointer;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.mobile-tip .close-tip:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.mobile-tip .close-tip::before {
  content: '×';
  line-height: 1;
}

/* 只在移动端显示提示 */
@media (min-width: 889px) {
  .mobile-tip {
    display: none !important;  /* 强制在PC端隐藏 */
  }
}

@media (max-width: 888px) {
  .mobile-tip {
    display: flex;  /* 在移动端显示 */
  }
}

/* 超小屏幕适配 */
@media (max-width: 320px) {
  .mobile-tip {
    padding: 10px 40px 10px 15px;
    font-size: 12px;
    min-height: 40px;
  }

  .mobile-tip .close-tip {
    width: 28px;
    height: 28px;
    font-size: 18px;
  }
}

/* 移动端侧边栏按钮容器 */
.sidebar-mobile-btn {
  display: none;
  padding: 6px 12px;
  margin: 0;
  color: var(--text-secondary);
  cursor: pointer;
  border: none;
  background: var(--bg-primary);
  position: relative;
  z-index: 1001;
}

.sidebar-mobile-btn::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 16px;
  background: var(--border-color);
}

.sidebar-mobile-btn svg {
  font-size: 0.9rem;
  transform: translateY(1px);
}

.sidebar-mobile-btn:hover {
  color: var(--brand-primary);
}

@media (max-width: 888px) {
  .sidebar-mobile-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-primary);
    z-index: 1001;
    margin-left: 5px;
  }

  .sidebar-section {
    display: flex;
    flex-direction: row;
    padding: 8px 0;
    margin: 0;
    white-space: nowrap;
    position: relative;
    z-index: 97; /* 降低z-index，从999改为97，确保弹窗能显示在侧边栏区域之上 */
    background: var(--bg-primary);
  }

  .edit-modal {
    position: fixed;
    left: 42px;
    top: 95px;
    z-index: 1000;
    width: 180px; /* 从158px增加到180px */
    pointer-events: auto;
  }

  .sidebar-mobile-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-primary);
    z-index: 1001;
  }
}

@media (max-width: 888px) {
  .edit-modal {
    position: fixed;
    left: 42px;
    top: 85px;  /* 从95px减小到85px */
    z-index: 96; /* 降低z-index，从1000改为96，确保弹窗能显示在编辑弹窗之上 */
    width: 180px; /* 从158px增加到180px */
  }

  .edit-modal h4 {
    font-size: 0.75rem;
  }

  .checkbox-label {
    font-size: 0.75rem;
    padding: 2px 6px;  /* 从3px减小到2px */
    margin: 4px 0;     /* 添加更小的上下margin */
  }

  .checkbox-label input[type="checkbox"] + span {
    width: 28px;
    height: 16px;
  }

  .checkbox-label input[type="checkbox"] + span::before {
    width: 12px;
    height: 12px;
  }

  .checkbox-label input[type="checkbox"]:checked + span::before {
    transform: translateX(12px);
  }
}

/* 编辑功能弹窗 */
.edit-modal {
  position: fixed;
  left: 162px;
  top: 64px;
  background-color: var(--bg-primary);
  padding: 16px;
  width: 180px; /* 从158px增加到180px */
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  pointer-events: auto;
  border: 1px solid var(--border-color);
  max-height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  animation: slideIn 0.2s ease;
  z-index: 96; /* 降低z-index，从1000改为96，确保弹窗能显示在编辑弹窗之上 */
}

/* 侧边栏折叠状态下弹窗位置 */
.edit-modal.sidebar-collapsed {
  left: 62px; /* 侧边栏折叠时位置靠近侧边栏 */
}

@media (max-width: 888px) {
  .sidebar-mobile-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-primary);
    z-index: 1001;
  }

  .sidebar-section {
    display: flex;
    flex-direction: row;
    padding: 8px 0;
    margin: 0;
    white-space: nowrap;
    position: relative;
    z-index: 999;
    background: var(--bg-primary);
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .edit-modal {
    position: fixed;
    left: 42px;
    top: 95px;
    z-index: 1000;
    width: 180px; /* 从158px增加到180px */
    pointer-events: auto;
  }
}

/* 确保滚动条不会遮挡弹窗 */
.sidebar::-webkit-scrollbar {
  display: none;
}

.sidebar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.coming-soon-content h3 {
  font-size: 1.3rem;
  color: #ffffff !important;
  margin: 0;
  line-height: 1.2;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.feature-card .coming-soon-content h3 {
  color: #ffffff !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 更多功能卡片特殊样式，确保不受Grid布局变化影响 */
.coming-soon-card {
  /* 确保样式一致性 */
  background: var(--bg-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  transform: translate3d(0, 0, 0);
  will-change: transform, box-shadow;
  transition: transform 0.15s cubic-bezier(0.2, 0, 0.15, 1), box-shadow 0.15s cubic-bezier(0.2, 0, 0.15, 1);
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
  perspective: 1000px;
}

.coming-soon-card:hover {
  transform: translate3d(0, -5px, 0);
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.coming-soon-card .feature-image {
  position: relative;
  width: 100%;
  aspect-ratio: 1 / 1;
  overflow: hidden;
}

.coming-soon-card .coming-soon-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.7);
  transition: transform 0.3s ease;
}

.coming-soon-card:hover .coming-soon-img {
  transform: scale(1.05);
}

.coming-soon-card .coming-soon-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
}

.coming-soon-card .coming-soon-content h3 {
  font-size: 1.3rem;
  color: white;
  margin: 0;
  line-height: 1.2;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.coming-soon-card .feature-content {
  padding: 12px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.coming-soon-card .feature-content h3 {
  font-size: var(--font-size-lg);
  margin-bottom: 8px;
  color: var(--text-primary);
}

.coming-soon-card .feature-content p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 响应式调整 */
@media (max-width: 888px) {
  .coming-soon-card .coming-soon-content h3 {
    font-size: 1.1rem;
  }
}

@media (max-width: 888px) {
  .content-wrap {
    margin-top: 75px;
    padding: 0 1rem;
  }
}

@media (max-width: 888px) {
  .content-wrap {
    margin-top: 90px;  /* 修改为90px，确保有足够空间容纳顶部导航栏(50px)和副导航栏(40px) */
  }
  /* ... existing code ... */
}

.tip-content h3 {
  font-size: var(--font-size-lg);
  margin: 0 0 12px 0;
  padding: 0;
  color: var(--text-primary);
}

.tip-content p {
  font-size: var(--font-size-sm);
  margin: 0;
  padding: 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

    .modal-content {
      width: 100%;
      max-width: 100%;
    }

/* 视频案例样式 */
.showcase-item.video-item {
  position: relative;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  z-index: 1; /* 降低z-index，使其低于showcase-buttons的z-index 2 */
  pointer-events: none; /* 禁用点击事件，让事件穿透到下方的showcase-buttons */
}

.video-overlay:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

.play-button {
  color: white;
  font-size: 40px; /* 从40px调整为18px，与预览按钮的字体大小接近 */
  transition: transform 0.2s ease, opacity 0.2s ease;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  pointer-events: none; /* 确保点击穿透到video-overlay */
}

.showcase-item.video-item:hover .play-button {
  opacity: 0; /* 鼠标悬停时隐藏大三角形播放按钮 */
}

/* 视频预览模态框样式 */
.video-preview-content {
  max-width: 85%;
  max-height: 85%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.preview-video {
  max-width: 100%;
  max-height: 80vh;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  background-color: #000;
}

/* 视频案例修改遮罩按钮样式 */
.showcase-item.video-item .showcase-buttons {
  opacity: 0;
  pointer-events: none;
}

.showcase-item.video-item:hover .showcase-buttons {
  opacity: 1;
  pointer-events: auto;
}

/* 视频案例特别处理播放按钮，使其始终可见 */
.showcase-item.video-item .preview-btn {
  pointer-events: auto; /* 确保播放按钮可点击 */
  opacity: 0;  /* 默认隐藏 */
}

.showcase-item.video-item:hover .preview-btn {
  opacity: 1;  /* 悬停时显示 */
}

.showcase-video {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
}

/* 图片预览模态框样式 - 已迁移到独立组件 */
/* 保留功能名称样式，供其他组件使用 */
.preview-feature-name {
  position: absolute;
  bottom: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
  padding: 9px 15px;
  background: var(--bg-mask);
  color: #fff;
  border-radius: var(--radius-sm);
  font-size: 15px;
  font-weight: 500;
  z-index: 10;
  backdrop-filter: blur(4px);
  border: 1px solid var(--border-light);
  white-space: nowrap;
  text-align: center;
  min-width: 110px;
}

/* 暗色主题下的功能名称样式 */
[data-theme="dark"] .preview-feature-name {
  background: var(--bg-mask);
  color: #fff;
  border: 1px solid var(--border-light);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .preview-feature-name {
    font-size: 12px !important;
    padding: 7px 11px;
    min-width: 95px;
  }
  
  /* 中等移动端优秀案例按钮样式优化 */
  .showcase-buttons {
    padding: 16px; /* 适中的内边距 */
  }
  
  .preview-btn {
    font-size: 12px !important; /* 统一使用小字号 */
    padding: 5px 10px; /* 适中的内边距 */
  }
  
  .showcase-create-btn {
    height: 35px; /* 缩小高度 */
    font-size: 12px !important; /* 统一使用小字号 */
  }
  
  .showcase-feature-label {
    height: 28px; /* 缩小高度 */
    font-size: 12px !important; /* 统一使用小字号 */
  }
}

@media (max-width: 480px) {
  .preview-feature-name {
    font-size: 12px;
    padding: 6px 9px;
    min-width: 80px;
  }
}

/* 登录提示样式 */
/* 已移动到 components/RequireLogin/index.css */

.sidebar-collapse-btn svg {
  font-size: 1rem;
}

/* 切换按钮样式 */
.sidebar-collapse-btn {
  position: fixed;
  left: 158px; /* 完全对齐侧边栏宽度 */
  top: 70px; /* 从65px调整到68px，微调位置 */
  transform: translateY(0); /* 移除Y轴居中变换 */
  width: 20px; /* 宽度从24px减小到20px */
  height: 58px; /* 高度从60px减小到55px */
  background-color: var(--bg-primary);
  border-radius: 0 6px 6px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid var(--border-color);
  border-left: none;
  color: var(--text-secondary);
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
  z-index: 98; /* 进一步降低z-index，从999改为98，确保所有弹窗都能显示在它之上 */
  transition: opacity 0.3s ease, visibility 0.3s ease, left 0s; /* 移除left过渡效果 */
  opacity: 0; /* 默认隐藏 */
  visibility: hidden; /* 默认不可见 */
  pointer-events: auto; /* 明确指定可以接收鼠标事件 */
}

/* 当按钮可见时的样式 */
.sidebar-collapse-btn.visible {
  opacity: 1; /* 完全不透明 */
  visibility: visible;
}

/* 当按钮悬停时的样式 */
.sidebar-collapse-btn:hover {
  opacity: 1;
  visibility: visible;
  color: var(--brand-primary);
}

.content-wrap.sidebar-collapsed .sidebar-collapse-btn {
  left: 58px; /* 完全对齐折叠后的侧边栏宽度 */
  top: 70px; /* 保持与展开状态相同的顶部位置 */
  transform: translateY(0); /* 保持相同的变换 */
}

.content-wrap.sidebar-collapsed .sidebar-collapse-btn:hover {
  color: var(--brand-primary);
  transform: translateY(0); /* 保持相同的变换 */
}

.sidebar-collapse-btn svg {
  font-size: 1.1rem; /* 增加图标尺寸 */
}

.sidebar.collapsed .sidebar-section .sidebar-item:first-child {
  margin-top: 0.32rem; /* 与调整后的间距一致 */
}

@media (max-width: 888px) {
  /* 内容区适配 */
  .content-wrap {
    flex-direction: column;
    margin-top: 90px;
    width: 100%; /* 移动端全宽 */
    margin-left: 0; /* 移动端不需要左侧margin */
    padding: 0;
    overflow-x: hidden;
  }

  .main-content {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    padding: 15px; /* 减小移动端内边距 */
  }
}

.contact-modal {
  max-width: 650px;
  width: 90%;
  padding: 30px;
  padding-top: 0px; /* 减小顶部间距 */
}

.contact-modal .modal-header {
  margin-bottom: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  padding-top: 20px !important;
  padding-bottom: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.contact-modal .modal-header h2 {
  margin: 0 !important;
  padding: 0 !important;
}

.contact-modal .medium-close-button {
  position: static !important;
  margin: 0 !important;
  top: auto !important;
  right: auto !important;
}

.contact-modal .modal-body {
  padding: 0;
  margin-top: 15px; /* 减小标题与内容的间距 */
}

.contact-modal .contact-support {
  box-shadow: none;
  background: transparent !important;
  padding: 0;
}

/* 取消模态框中客服组件的悬浮效果 */
.contact-modal .contact-support:hover {
  box-shadow: none;
}

.contact-modal .zoom-btn:hover,
.contact-modal .copy-btn:hover {
  transform: none;
  box-shadow: var(--shadow-sm);
}

.contact-modal .contact-methods {
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
}

.contact-modal .contact-method {
  width: 100%;
  justify-content: flex-start;
}

.contact-modal .email-box {
  flex: 1;
  max-width: 100%;
}

.contact-modal .email-text {
  word-break: break-all;
}

.contact-intro {
  margin-bottom: 24px;
  color: var(--text-secondary);
  font-size: 14px;
  text-align: center;
  line-height: 1.5;
}

.modal-contact-support {
  width: 100%;
}

.contact-modal .contact-method.qr {
  justify-content: flex-start;
  margin-bottom: 8px;
}

.contact-modal .contact-method.qr img {
  width: 100px;
  height: 100px;
  margin-left: 20px;
}

.contact-modal .method-label {
  min-width: 80px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .contact-modal {
    width: 95%;
    padding: 20px;
  }

  .contact-modal .contact-method.qr img {
    width: 80px;
    height: 80px;
  }

  .contact-modal .method-label {
    min-width: 70px;
    font-size: 14px;
  }
}

.dropdown-item:hover {
  background-color: var(--bg-hover);
}

/* 移动端用户下拉菜单样式优化 */
@media (max-width: 888px) {
  .user-dropdown {
    width: 130px !important; /* 适当缩小宽度 */
    min-width: 130px !important;
    padding-top: 3px; /* 适当减少顶部内边距 */
  }
  
  .user-dropdown .dropdown-item {
    padding: 10px 14px !important; /* 适当缩小内边距 */
    font-size: 14px !important; /* 适当缩小字体 */
  }
  
  /* 用户信息区域 */
  .user-dropdown .dropdown-item:first-child {
    padding: 8px 14px !important;
    font-size: 12px !important;
  }
  
  /* 最后一个项目（退出登录） */
  .user-dropdown .dropdown-item:last-child {
    padding: 10px 14px 14px !important;
  }
}

/* 更小屏幕的进一步优化 */
@media (max-width: 480px) {
  .user-dropdown {
    width: 120px !important; /* 适当缩小宽度 */
    min-width: 120px !important;
  }
  
  .user-dropdown .dropdown-item {
    padding: 8px 12px !important; /* 适当缩小内边距 */
    font-size: 13px !important; /* 适当缩小字体 */
  }
  
  /* 用户信息区域 */
  .user-dropdown .dropdown-item:first-child {
    padding: 6px 12px !important;
    font-size: 11px !important;
  }
  
  /* 最后一个项目（退出登录） */
  .user-dropdown .dropdown-item:last-child {
    padding: 8px 12px 12px !important;
  }
}

/* 添加侧边栏底部按钮组样式 */
.sidebar-bottom-group {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 100%;
  border-top: 1px solid var(--border-light);
  padding-top: 5px;
}

/* 修改底部按钮样式，使其紧凑排列 */
.sound-toggle-sidebar {
  margin-bottom: 0.25rem !important;
}

/* 悬浮提示样式 */
.sidebar.collapsed .sidebar-item .tooltip {
  position: fixed; /* 使用fixed定位，完全脱离文档流 */
  left: 57px; /* 保持靠近侧边栏 */
  background-color: var(--bg-primary);
  color: var(--text-primary); /* 默认使用正常文字颜色 */
  padding: 0 16px; /* 从12px增加到16px，适当增加宽度 */
  border-radius: 0 6px 6px 0; /* 左侧直角，右侧圆角，与折叠按钮一致 */
  font-size: 0.85rem;
  white-space: nowrap;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08); /* 减弱阴影效果 */
  border: 1px solid var(--border-color);
  z-index: 9999; /* 提高z-index到更高值，确保显示在所有元素之上，包括折叠按钮(z-index: 1001) */
  opacity: 0;
  visibility: hidden;
  pointer-events: auto; /* 允许接收鼠标事件，防止被穿透 */
  transition: opacity 0.2s ease, visibility 0.2s ease, background-color 0.2s ease, color 0.2s ease;
  height: 3rem; /* 设置高度与侧边栏按钮一致 */
  box-sizing: border-box; /* 确保与侧边栏按钮使用相同的盒模型 */
  display: flex;
  align-items: center; /* 垂直居中文字 */
  cursor: pointer; /* 添加鼠标指针样式 */
}

/* 显示提示的触发器 */
.sidebar.collapsed .sidebar-item:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* 提示悬浮效果 */
.sidebar.collapsed .sidebar-item .tooltip:hover {
  background: linear-gradient(var(--bg-hover), var(--bg-hover)), var(--bg-primary); /* 创建双层背景，保留原背景色并添加悬浮效果层 */
  color: var(--brand-primary); /* 与侧边栏按钮悬浮时使用相同的文字颜色 */
}

/* 修改sidebar-collapse-btn样式，确保不会覆盖tooltip */
.sidebar-collapse-btn {
  position: fixed;
  left: 158px; /* 完全对齐侧边栏宽度 */
  top: 70px; /* 从65px调整到68px，微调位置 */
  transform: translateY(0); /* 移除Y轴居中变换 */
  width: 20px; /* 宽度从24px减小到20px */
  height: 58px; /* 高度从60px减小到55px */
  background-color: var(--bg-primary);
  border-radius: 0 6px 6px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid var(--border-color);
  border-left: none;
  color: var(--text-secondary);
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
  z-index: 999; /* 调低z-index，确保所有弹窗都能显示在它之上 */
  transition: opacity 0.3s ease, visibility 0.3s ease, left 0s; /* 移除left过渡效果 */
  opacity: 0; /* 默认隐藏 */
  visibility: hidden; /* 默认不可见 */
  pointer-events: auto; /* 明确指定可以接收鼠标事件 */
}

/* 当侧边栏折叠时，折叠按钮的位置也需要相应调整 */
.content-wrap.sidebar-collapsed .sidebar-collapse-btn {
  left: 58px; /* 完全对齐折叠后的侧边栏宽度 */
  top: 70px; /* 保持与展开状态相同的顶部位置 */
  transform: translateY(0); /* 保持相同的变换 */
}

/* 侧边栏折叠状态下弹窗位置 */
.content-wrap.sidebar-collapsed .edit-modal {
  left: 62px; /* 侧边栏折叠时位置靠近侧边栏 */
}

/* 管理页面内容样式 */
.admin-content {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100vh;
  overflow: auto;
}

/* 当是管理页面时，调整内容区域的样式 */
.app-container .content-wrap .main-content.admin-content {
  margin-left: 0;
  transition: margin-left 0.3s ease;
}

/* 管理页面样式 */
.app-container.admin-page {
  /* 管理页面的容器样式 */
}

.admin-page .content-wrap {
  margin-left: 0;
  margin-top: 0;
}

.admin-page .main-content {
  margin-left: 0;
  width: 100%;
  padding: 0;
}

.admin-content {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100vh;
  overflow: auto;
}

/* 当是管理页面时，调整内容区域的样式 */
.app-container.admin-page .content-wrap .main-content.admin-content {
  margin-left: 0;
  transition: margin-left 0.3s ease;
}

/* 确保管理页面的布局正确 */
.app-container.admin-page {
  display: block;
}

/* 移动端适配 */
@media (max-width: 888px) {
  .app-container.admin-page .content-wrap {
    margin-top: 0;
  }
}

/* 核心功能卡片样式 */
.feature-content h3,
.feature-card .feature-content h3 {
  font-size: var(--font-size-lg);
  margin: 0 0 6px 0;
  padding: 0;
  color: var(--text-primary);
}

.feature-content p,
.feature-card .feature-content p {
  font-size: var(--font-size-sm);
  margin: 0;
  padding: 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

@media (max-width: 888px) {
  .feature-content h3,
  .feature-card .feature-content h3 {
    font-size: var(--font-size-md);
    margin-bottom: 4px;
  }
  
  .feature-content p,
  .feature-card .feature-content p {
    font-size: var(--font-size-sm);
  }
}

.tip-card.more-tips .coming-soon-content h3 {
  font-size: 1.1rem;
  color: var(--text-inverse) !important;
  margin: 0;
  line-height: 1.2;
  font-weight: 700;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.tip-card.more-tips .coming-soon-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.tip-card.more-tips .coming-soon-content h3 {
  font-size: 1.1rem;
  color: #ffffff !important;
  margin: 0;
  line-height: 1.2;
  font-weight: 700;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 屏幕阅读器友好的隐藏文本 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 首页优秀案例加载状态样式 */
.showcase-loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 60px 16px;
}

.showcase-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.showcase-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--brand-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.showcase-loading-content span {
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  font-weight: 500;
}

/* 功能板块名称样式 */
.showcase-feature-label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  color: var(--text-primary);
  width: 100%;
  height: 40px;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  font-weight: 500;
  border-radius: 0;
  backdrop-filter: blur(4px);
  border-bottom: 1px solid var(--border-color);
  pointer-events: auto;
  transform: translate3d(0, -100%, 0);
  transition: transform 0.15s cubic-bezier(0.2, 0, 0.15, 1);
  will-change: transform;
  opacity: 0.95;
}

.showcase-item:hover .showcase-feature-label {
  transform: translate3d(0, 0, 0);
}

@media (max-width: 480px) {
  .preview-feature-name {
    font-size: 12px !important;
    padding: var(--spacing-xxs) var(--spacing-xs);
  }
  
  /* 移动端优秀案例按钮样式优化 */
  .showcase-buttons {
    padding: 12px; /* 减小内边距 */
  }
  
  .preview-btn {
    font-size: 12px !important; /* 统一使用小字号 */
    padding: 4px 8px; /* 减小内边距 */
  }
  
  .showcase-create-btn {
    height: 30px; /* 进一步缩小高度 */
    font-size: 12px !important; /* 统一使用小字号 */
  }
  
  .showcase-feature-label {
    height: 24px; /* 进一步缩小高度 */
    font-size: 12px !important; /* 统一使用小字号 */
  }
}

@media (max-width: 888px) {
  /* ...原有移动端样式... */
  .sidebar-collapse-btn {
    display: none !important;
  }
}

/* 移动端底部按钮区域 */
.mobile-bottom-buttons {
  display: none;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 20px;
  margin-top: 20px;
  margin-bottom: -30px;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-primary);
}

.mobile-bottom-buttons .theme-toggle-btn,
.mobile-bottom-buttons .sound-toggle-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: 13px;
  min-width: 120px;
  justify-content: center;
  /* 覆盖固定定位 */
  position: static !important;
  bottom: auto !important;
  right: auto !important;
  z-index: auto !important;
  box-shadow: none !important;
  width: auto !important;
  height: auto !important;
}

.mobile-bottom-buttons .theme-toggle-btn:hover,
.mobile-bottom-buttons .sound-toggle-btn:hover {
  background-color: var(--bg-hover);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
  /* 简化鼠标效果 */
  transform: none !important;
}

.mobile-bottom-buttons .theme-toggle-btn:hover svg,
.mobile-bottom-buttons .sound-toggle-btn:hover svg {
  color: var(--brand-primary) !important;
}

.mobile-bottom-buttons .theme-toggle-btn svg,
.mobile-bottom-buttons .sound-toggle-btn svg {
  font-size: 16px;
  color: inherit;
  transition: var(--transition-normal);
}

@media (max-width: 888px) {
  /* 隐藏移动端侧边栏底部的按钮 */
  .sidebar-bottom-group {
    display: none !important;
  }
  
  /* 显示移动端底部按钮 */
  .mobile-bottom-buttons {
    display: flex;
  }
}

@media (max-width: 888px) {
  .tip-content .tip-description-desktop {
    display: none !important;
  }
  .tip-content .tip-description-mobile {
    display: block !important;
    font-size: 12px !important;
    color: var(--text-secondary) !important;
    margin: 2px 0 8px 0 !important;
    padding: 0 !important;
    text-align: center !important;
    line-height: 1.4 !important;
  }
  .tip-content h3.tip-title {
    margin-top: 8px !important;
    margin-bottom: 8px !important;
  }
}

@media (max-width: 888px) {
  .sidebar-item:not(:last-child)::after {
    display: none !important;
  }
}

.sidebar-mobile-toggle-btn {
  display: none;
  width: 38px;
  height: 38px;
  margin: 0 8px 0 auto;
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  border-radius: 50%;
  transition: background 0.2s, color 0.2s;
}

.sidebar-mobile-toggle-btn svg {
  font-size: 15px !important;
  width: 15px !important;
  height: 15px !important;
}
.sidebar-mobile-toggle-btn:hover {
  background: var(--bg-hover);
  color: var(--brand-primary);
}
@media (max-width: 888px) {
  .sidebar-mobile-toggle-btn {
    display: flex;
  }
}



