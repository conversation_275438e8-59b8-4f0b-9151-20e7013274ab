/**
 * 平台配置管理
 * 管理ComfyUI和RunningHub平台的配置和映射关系
 */

const Config = require('config');

/**
 * 获取平台配置
 * @returns {Object} 平台配置对象
 */
function getPlatformConfig() {
  return {
    // 默认平台选择策略
    defaultPlatform: process.env.DEFAULT_WORKFLOW_PLATFORM || 'runninghub',
    
    // 平台优先级配置（按优先级排序）
    platformPriority: ['runninghub', 'comfyui'],
    
    // RunningHub平台配置
    runningHub: {
      enabled: process.env.RUNNINGHUB_ENABLED === 'true',
      apiKey: process.env.RUNNINGHUB_API_KEY,
      baseURL: 'https://www.runninghub.cn',
      timeout: 30000,
      
      // 工作流映射：本地工作流名称 -> RunningHub工作流ID
      workflowMappings: {
        // 款式设计类
        'A01-trending': '1850925505116598274',      // 爆款开发
        'A02-optimize': '1851234567890123456',      // 款式优化
        'A02b-optimizetext': '1851234567890123457', // 文本优化
        'A03-inspiration': '1851234567890123458',   // 灵感探索
        'A05-drawing': '1851234567890123459',       // 生成线稿
        'A06-divergent': '1851234567890123460',     // 爆款延伸
        
        // 模特图类
        'B01-fashion': '1851234567890123461',       // 时尚大片
        'B02-tryonauto': '1851234567890123462',     // 自动换装
        'B02-tryonmanual': '1851234567890123463',   // 手动换装
        'B02-tryonother': '1851234567890123464',    // 其他换装
        'B03-changemodel': '1851234567890123465',   // 换模特
        'B04-recolor': '1851234567890123466',       // 服装复色
        'B05-fabric': '1851234567890123467',        // 换面料
        'B06-background': '1851234567890123468',    // 换背景
        'B07-virtual': '1851234567890123469',       // 虚拟模特
        'B08-detailmigration': '1851234567890123470', // 细节还原
        'B09-handfix': '1851234567890123471',       // 手部修复
        'B10-changeposture': '1851234567890123472', // 换姿势
        
        // 工具类
        'C01-extract': '1851234567890123473',       // 图片取词
        'C02-upscale': '1851234567890123474',       // 高清放大
        'C03-mattingbg': '1851234567890123475',     // 背景抠图
        'C03-mattingbgfile': '1851234567890123476', // 文件背景抠图
        'C03-mattingclo': '1851234567890123477',    // 服装抠图
        'C04-extend': '1851234567890123478',        // 智能扩图
      },
      
      // 参数映射配置
      parameterMappings: {
        // 通用参数映射
        'prompt': 'prompt',
        'negative_prompt': 'negative_prompt',
        'seed': 'seed',
        'steps': 'steps',
        'cfg_scale': 'cfg_scale',
        'width': 'width',
        'height': 'height',
        
        // 特殊参数映射
        'model_pose': 'pose',
        'style_strength': 'strength',
        'image_url': 'input_image'
      }
    },
    
    // ComfyUI平台配置
    comfyui: {
      enabled: process.env.COMFYUI_ENABLED !== 'false', // 默认启用
      baseURL: process.env.COMFYUI_URL,
      apiKey: process.env.COMFYUI_API_KEY,
      instanceId: process.env.COMFYUI_INSTANCE_ID,
      timeout: 300000, // 5分钟超时
    },
    
    // 平台选择规则
    selectionRules: {
      // 基于工作流类型的规则
      workflowTypeRules: {
        // 优先使用RunningHub的工作流类型
        'runninghub_preferred': ['A01-trending', 'B01-fashion', 'C01-extract'],
        
        // 优先使用ComfyUI的工作流类型
        'comfyui_preferred': ['B08-detailmigration', 'B09-handfix'],
        
        // 仅支持ComfyUI的工作流类型
        'comfyui_only': [],
        
        // 仅支持RunningHub的工作流类型
        'runninghub_only': []
      },
      
      // 基于负载的规则
      loadBalancing: {
        enabled: true,
        // 当ComfyUI实例繁忙时，自动切换到RunningHub
        autoSwitchOnBusy: true,
        // 负载阈值
        loadThreshold: 0.8
      },
      
      // 基于用户的规则
      userRules: {
        // VIP用户优先使用RunningHub
        vipPreferRunningHub: true,
        // 免费用户限制
        freeUserLimitations: {
          maxConcurrentTasks: 2,
          preferredPlatform: 'runninghub'
        }
      }
    }
  };
}

/**
 * 获取工作流的RunningHub映射ID
 * @param {string} workflowName - 工作流名称
 * @returns {string|null} RunningHub工作流ID
 */
function getRunningHubWorkflowId(workflowName) {
  const config = getPlatformConfig();
  return config.runningHub.workflowMappings[workflowName] || null;
}

/**
 * 基于使用次数智能选择RunningHub配置
 * @param {string} workflowName - 工作流名称
 * @param {string} userId - 用户ID（可选）
 * @param {boolean} forceSelect - 是否强制选择配置（忽略10次限制）
 * @returns {Promise<Object>} 选择的配置和平台信息
 */
async function selectConfigByUsage(workflowName, userId = null, forceSelect = false) {
  try {
    // 导入RunningHub配置模型
    const RunningHubConfig = require('../models/RunningHubConfig');

    // 获取配置
    let configs;
    if (userId) {
      // 如果有用户ID，优先获取该用户的配置
      configs = await RunningHubConfig.find({
        createdBy: userId,
        enabled: true
      }).sort({ 'usage.totalTasks': 1 }); // 按使用次数升序排列

      // 如果用户没有配置，获取所有启用的配置作为后备
      if (!configs || configs.length === 0) {
        configs = await RunningHubConfig.find({
          enabled: true
        }).sort({ 'usage.totalTasks': 1 });
      }
    } else {
      // 没有用户ID时，获取所有启用的配置
      configs = await RunningHubConfig.find({
        enabled: true
      }).sort({ 'usage.totalTasks': 1 });
    }

    if (!configs || configs.length === 0) {
      return {
        useComfyUI: true,
        reason: '没有可用的RunningHub配置',
        config: null
      };
    }

    // 检查工作流映射
    const availableConfigs = configs.filter(config =>
      config.workflowMappings.has(workflowName)
    );

    if (availableConfigs.length === 0) {
      return {
        useComfyUI: true,
        reason: `没有配置支持工作流 ${workflowName}`,
        config: null
      };
    }

    // 检查是否所有配置的使用次数都超过10次（除非强制选择）
    const allConfigsOverLimit = availableConfigs.every(config =>
      (config.usage.totalTasks || 0) >= 10
    );

    if (allConfigsOverLimit && !forceSelect) {
      return {
        useComfyUI: true,
        reason: '所有RunningHub配置使用次数都超过10次，切换到ComfyUI',
        config: null,
        configsUsage: availableConfigs.map(config => ({
          name: config.name,
          totalTasks: config.usage.totalTasks || 0
        }))
      };
    }

    // 选择使用次数最少的配置
    const selectedConfig = availableConfigs[0]; // 已按使用次数升序排列

    return {
      useComfyUI: false,
      reason: `选择使用次数最少的配置: ${selectedConfig.name} (${selectedConfig.usage.totalTasks || 0}次)`,
      config: selectedConfig,
      workflowId: selectedConfig.workflowMappings.get(workflowName),
      configsUsage: availableConfigs.map(config => ({
        name: config.name,
        totalTasks: config.usage.totalTasks || 0,
        selected: config._id.toString() === selectedConfig._id.toString()
      }))
    };

  } catch (error) {
    console.error('选择RunningHub配置失败:', error);
    return {
      useComfyUI: true,
      reason: `选择配置时出错: ${error.message}`,
      config: null,
      error: error.message
    };
  }
}

/**
 * 检查工作流是否支持指定平台
 * @param {string} workflowName - 工作流名称
 * @param {string} platform - 平台名称 ('comfyui' | 'runninghub')
 * @returns {boolean} 是否支持
 */
function isWorkflowSupportedOnPlatform(workflowName, platform) {
  const config = getPlatformConfig();
  
  if (platform === 'runninghub') {
    return !!config.runningHub.workflowMappings[workflowName] && config.runningHub.enabled;
  } else if (platform === 'comfyui') {
    return config.comfyui.enabled;
  }
  
  return false;
}

/**
 * 获取工作流的推荐平台
 * @param {string} workflowName - 工作流名称
 * @param {Object} context - 上下文信息（ComfyUI实例状态、用户ID等）
 * @returns {Promise<Object>} 推荐的平台信息
 */
async function getRecommendedPlatform(workflowName, context = {}) {
  const config = getPlatformConfig();
  const rules = config.selectionRules;

  // 1. 检查强制规则
  if (rules.workflowTypeRules.runninghub_only.includes(workflowName)) {
    return {
      platform: 'runninghub',
      reason: '工作流仅支持RunningHub平台',
      config: null
    };
  }

  if (rules.workflowTypeRules.comfyui_only.includes(workflowName)) {
    return {
      platform: 'comfyui',
      reason: '工作流仅支持ComfyUI平台',
      config: null
    };
  }

  // 2. 基于RunningHub配置使用次数的智能选择
  if (isWorkflowSupportedOnPlatform(workflowName, 'runninghub')) {
    const configSelection = await selectConfigByUsage(workflowName, context.userId);

    // 如果选择结果建议使用ComfyUI（所有配置都超过10次使用）
    if (configSelection.useComfyUI) {
      // 检查ComfyUI是否可用
      if (context.comfyuiInstancesStatus) {
        const { availableInstances, allInstancesBusy } = context.comfyuiInstancesStatus;

        if (!allInstancesBusy && availableInstances > 0) {
          return {
            platform: 'comfyui',
            reason: configSelection.reason,
            config: null,
            configsUsage: configSelection.configsUsage
          };
        } else {
          // ComfyUI实例全部繁忙，强制使用RunningHub（选择使用次数最少的配置）
          const fallbackSelection = await selectConfigByUsage(workflowName, context.userId, true); // 强制选择
          return {
            platform: 'runninghub',
            reason: 'ComfyUI实例全部繁忙，强制使用RunningHub配置',
            config: fallbackSelection.config,
            workflowId: fallbackSelection.workflowId,
            configsUsage: fallbackSelection.configsUsage
          };
        }
      } else {
        // 无法获取ComfyUI状态，使用ComfyUI
        return {
          platform: 'comfyui',
          reason: configSelection.reason + '（无法获取ComfyUI状态）',
          config: null
        };
      }
    } else {
      // 选择了RunningHub配置
      return {
        platform: 'runninghub',
        reason: configSelection.reason,
        config: configSelection.config,
        workflowId: configSelection.workflowId,
        configsUsage: configSelection.configsUsage
      };
    }
  }

  // 3. 检查ComfyUI实例可用性（如果RunningHub不支持该工作流）
  if (context.comfyuiInstancesStatus) {
    const { availableInstances, totalInstances, allInstancesBusy } = context.comfyuiInstancesStatus;

    if (availableInstances > 0) {
      return {
        platform: 'comfyui',
        reason: `ComfyUI有可用实例 (${availableInstances}/${totalInstances})`,
        config: null
      };
    } else if (allInstancesBusy) {
      return {
        platform: 'comfyui',
        reason: `ComfyUI实例全部繁忙，但工作流不支持RunningHub (${totalInstances}个实例)`,
        config: null
      };
    }
  }

  // 4. 检查工作流类型偏好（作为后备选择）
  if (rules.workflowTypeRules.runninghub_preferred.includes(workflowName)) {
    if (isWorkflowSupportedOnPlatform(workflowName, 'runninghub')) {
      return {
        platform: 'runninghub',
        reason: '工作流偏好RunningHub平台',
        config: null
      };
    }
  }

  if (rules.workflowTypeRules.comfyui_preferred.includes(workflowName)) {
    if (isWorkflowSupportedOnPlatform(workflowName, 'comfyui')) {
      return {
        platform: 'comfyui',
        reason: '工作流偏好ComfyUI平台',
        config: null
      };
    }
  }

  // 5. 按优先级选择
  for (const platform of config.platformPriority) {
    if (isWorkflowSupportedOnPlatform(workflowName, platform)) {
      return {
        platform,
        reason: `按平台优先级选择: ${platform}`,
        config: null
      };
    }
  }

  // 6. 默认平台
  return {
    platform: config.defaultPlatform,
    reason: '使用默认平台',
    config: null
  };
}

/**
 * 转换参数格式
 * @param {Object} params - 原始参数
 * @param {string} fromPlatform - 源平台
 * @param {string} toPlatform - 目标平台
 * @param {string} workflowName - 工作流名称
 * @returns {Object} 转换后的参数
 */
function convertParameters(params, fromPlatform, toPlatform, workflowName) {
  if (fromPlatform === toPlatform) {
    return params;
  }

  // 从ComfyUI格式转换为RunningHub格式
  if (fromPlatform === 'comfyui' && toPlatform === 'runninghub') {
    return convertComfyUIToRunningHub(params, workflowName);
  }

  // 从RunningHub格式转换为ComfyUI格式
  if (fromPlatform === 'runninghub' && toPlatform === 'comfyui') {
    return convertRunningHubToComfyUI(params, workflowName);
  }

  // 默认返回原参数
  return params;
}

/**
 * 将ComfyUI格式参数转换为RunningHub格式
 * @param {Object} params - ComfyUI格式参数
 * @param {string} workflowName - 工作流名称
 * @returns {Object} RunningHub格式参数
 */
function convertComfyUIToRunningHub(params, workflowName) {
  const nodeInfoList = [];

  // 遍历参数，排除系统参数
  Object.keys(params).forEach(key => {
    if (!['token', 'subInfo', 'forcePlatform'].includes(key)) {
      const value = params[key];

      // 如果值是对象，遍历其属性
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        Object.keys(value).forEach(fieldName => {
          nodeInfoList.push({
            nodeId: key,
            fieldName: fieldName,
            fieldValue: value[fieldName]
          });
        });
      } else {
        // 如果值是基本类型，直接作为fieldValue
        nodeInfoList.push({
          nodeId: key,
          fieldName: 'value', // 默认字段名
          fieldValue: value
        });
      }
    }
  });

  return { nodeInfoList };
}

/**
 * 将RunningHub格式参数转换为ComfyUI格式
 * @param {Object} params - RunningHub格式参数
 * @param {string} workflowName - 工作流名称
 * @returns {Object} ComfyUI格式参数
 */
function convertRunningHubToComfyUI(params, workflowName) {
  const converted = {};

  if (params.nodeInfoList && Array.isArray(params.nodeInfoList)) {
    params.nodeInfoList.forEach(nodeInfo => {
      const { nodeId, fieldName, fieldValue } = nodeInfo;

      if (!converted[nodeId]) {
        converted[nodeId] = {};
      }

      converted[nodeId][fieldName] = fieldValue;
    });
  }

  return converted;
}

/**
 * 获取ComfyUI实例状态
 * @returns {Promise<Object>} ComfyUI实例状态信息
 */
async function getComfyUIInstancesStatus() {
  try {
    // 导入所需模块（避免循环依赖）
    const MongoDBCache = require('../utils/cacheUtils');
    const cache = new MongoDBCache({
      collectionName: 'instance_status_cache'
    });

    // 获取所有实例列表
    const instanceAxios = require('../utils/instanceAxios');
    const ResourceAPI = require('../config/ResourceAPI');

    const response = await instanceAxios.get(ResourceAPI.INSTANCE_LIST.url, {
      params: {
        page: 1,
        limit: 200,
      }
    });

    if (response.data.code !== 0) {
      throw new Error('获取实例列表失败');
    }

    const instances = response.data.data.appList || [];
    const runningInstances = instances.filter(instance => instance.status === 300); // 运行中的实例
    const shutdownInstances = instances.filter(instance =>
      instance.status === 800 // 关机状态的实例
    );

    let availableRunningInstances = 0; // 运行中且空闲的实例
    let busyRunningInstances = 0; // 运行中且繁忙的实例

    // 检查每个运行中实例的使用状态
    for (const instance of runningInstances) {
      const isInUse = await cache.get('instance_in_use_' + instance.appId);
      const isFinished = await cache.get('instance_finished_' + instance.appId);

      // 如果实例已完成任务或没有在使用中，则认为是空闲的
      if (isFinished || !isInUse) {
        availableRunningInstances++;
      } else {
        busyRunningInstances++;
      }
    }

    // 总的可用资源 = 运行中空闲实例 + 关机实例（可开机）
    const totalAvailableResources = availableRunningInstances + shutdownInstances.length;
    const totalInstances = instances.length; // 包括所有实例（运行中+关机）
    const totalRunningInstances = runningInstances.length;
    const totalShutdownInstances = shutdownInstances.length;
    const allInstancesBusy = totalRunningInstances > 0 && availableRunningInstances === 0;

    return {
      totalInstances, // 所有实例总数
      totalRunningInstances, // 运行中实例总数
      totalShutdownInstances, // 关机实例总数
      availableRunningInstances, // 运行中且空闲的实例数
      busyRunningInstances, // 运行中且繁忙的实例数
      totalAvailableResources, // 总可用资源（空闲实例+关机实例）
      allInstancesBusy,
      // 为了向后兼容，保留原有字段名
      availableInstances: availableRunningInstances,
      busyInstances: busyRunningInstances,
      instancesStatus: instances.map(instance => ({
        instanceId: instance.appId,
        name: instance.customName,
        status: instance.status,
        statusText: instance.status === 300 ? '运行中' :
                   instance.status === 800 ? '关机' : '未知'
      }))
    };
  } catch (error) {
    console.error('获取ComfyUI实例状态失败:', error);
    return {
      totalInstances: 0,
      availableInstances: 0,
      busyInstances: 0,
      allInstancesBusy: true,
      instancesStatus: [],
      error: error.message
    };
  }
}

/**
 * 获取平台状态
 * @returns {Promise<Object>} 平台状态信息
 */
async function getPlatformStatus() {
  const config = getPlatformConfig();
  const comfyuiStatus = await getComfyUIInstancesStatus();

  return {
    runninghub: {
      enabled: config.runningHub.enabled,
      available: config.runningHub.enabled && !!config.runningHub.apiKey,
      supportedWorkflows: Object.keys(config.runningHub.workflowMappings).length
    },
    comfyui: {
      enabled: config.comfyui.enabled,
      available: config.comfyui.enabled && !!config.comfyui.baseURL,
      supportedWorkflows: 'all', // ComfyUI支持所有工作流
      instancesStatus: comfyuiStatus
    }
  };
}

module.exports = {
  getPlatformConfig,
  getRunningHubWorkflowId,
  isWorkflowSupportedOnPlatform,
  getRecommendedPlatform,
  convertParameters,
  getComfyUIInstancesStatus,
  getPlatformStatus,
  selectConfigByUsage
};
