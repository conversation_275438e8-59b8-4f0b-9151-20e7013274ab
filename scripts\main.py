# 这是一个示例 Python 脚本。
from io import BytesIO

import oss2
from PIL import ImageOps, ExifTags
from PIL import Image
import os
import uuid
from flask import Flask, request, jsonify, send_file
from PIL import PngImagePlugin
import requests
from flask_sock import Sock
import websockets
import asyncio
import json
from collections import defaultdict
import threading
import time

app = Flask(__name__)

# 配置上传和输出目录
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)


def download_image(url, filename):
    """从URL下载图片到本地"""
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()

        filepath = os.path.join(UPLOAD_FOLDER, filename)
        with open(filepath, 'wb') as f:
            f.write(response.content)
        return filepath
    except Exception as e:
        raise Exception(f"下载图片失败: {str(e)}")


oss_config = {
    "region": "https://oss-cn-hangzhou.aliyuncs.com",
    "accessKeyId": "LTAI5t7acYykjUa5rophiDbN",
    "accessKeySecret": "******************************",
    "bucket": "cust-comfyui-upload-use",
    "objectName": "mask"
}


def get_image_orientation(img):
    try:
        if hasattr(img, '_getexif'):
            exif = img._getexif()
            if exif is not None:
                # 查找 Orientation 标签的编号（通常是 274）
                for tag, value in exif.items():
                    if ExifTags.TAGS.get(tag) == 'Orientation':
                        return value
    except Exception as e:
        print(f"Error reading EXIF: {e}")
    return 1  # 默认返回 1（无旋转）


def process_images(original_path, mask_path):
    """处理图片蒙版"""
    try:
        # 处理图片
        with Image.open(original_path) as original_pil:
            # 保存原始元数据

            if get_image_orientation(original_pil) != 1:
                original_pil = ImageOps.exif_transpose(original_pil)  # 自动校正方向

            metadata = PngImagePlugin.PngInfo()
            if hasattr(original_pil, 'text'):
                for k, v in original_pil.text.items():
                    metadata.add_text(k, v)

            original_pil = original_pil.convert('RGBA')

            with Image.open(mask_path) as mask_pil:
                mask_alpha = mask_pil.convert('L')  # 转换为灰度图像获取alpha

                # 将alpha通道应用到原始图像
                original_pil.putalpha(mask_alpha)

            # 将图片保存到内存中
            img_byte_arr = BytesIO()
            original_pil.save(img_byte_arr, format="PNG", compress_level=4, pnginfo=metadata)
            img_byte_arr.seek(0)  # 重置指针位置

            # 上传到OSS
            auth = oss2.Auth(oss_config['accessKeyId'], oss_config['accessKeySecret'])
            bucket = oss2.Bucket(auth, oss_config['region'], oss_config['bucket'])

            file_name = f"{oss_config['objectName']}/{uuid.uuid4()}.png"
            headers = {
                'x-oss-object-acl': 'public-read'
            }
            result = bucket.put_object(
                # 生成文件名
                file_name,
                img_byte_arr,
                headers=headers
            )

            if result.status == 200:
                print(f"图片成功上传到OSS: {oss_config['objectName']}")
                return "https://file.aibikini.cn/" + file_name
            else:
                print(f"OSS上传失败，状态码: {result.status}")
                return ""

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        return ""


from flask_cors import CORS

CORS(app)

@app.route('/api/process_mask', methods=['POST'])
def process_mask2():
    """处理图片蒙版的API端点"""
    try:
        # 获取请求数据
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据为空'}), 400

        original_url = data.get('original_url')
        mask_url = data.get('mask_url')

        if not original_url or not mask_url:
            return jsonify({'error': '缺少必要参数: original_url 和 mask_url'}), 400

        # 生成唯一文件名
        original_filename = f"original_{uuid.uuid4().hex}.png"
        mask_filename = f"mask_{uuid.uuid4().hex}.png"

        # 下载图片
        original_path = download_image(original_url, original_filename)
        mask_path = download_image(mask_url, mask_filename)

        # 处理图片
        output_path = process_images(original_path, mask_path)

        # 清理临时文件
        try:
            os.remove(original_path)
            os.remove(mask_path)
        except:
            pass

        return jsonify({
            'success': True,
            'output_url': output_path,
            'message': '图片处理成功'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/process_mask', methods=['POST'])
def process_mask():
    """处理图片蒙版的API端点"""
    try:
        # 获取请求数据
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据为空'}), 400

        original_url = data.get('original_url')
        mask_url = data.get('mask_url')

        if not original_url or not mask_url:
            return jsonify({'error': '缺少必要参数: original_url 和 mask_url'}), 400

        # 生成唯一文件名
        original_filename = f"original_{uuid.uuid4().hex}.png"
        mask_filename = f"mask_{uuid.uuid4().hex}.png"

        # 下载图片
        original_path = download_image(original_url, original_filename)
        mask_path = download_image(mask_url, mask_filename)

        # 处理图片
        output_path = process_images(original_path, mask_path)

        # 清理临时文件
        try:
            os.remove(original_path)
            os.remove(mask_path)
        except:
            pass

        return jsonify({
            'success': True,
            'output_url': output_path,
            'message': '图片处理成功'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/get_image/<filename>')
def get_image(filename):
    """获取处理后的图片"""
    try:
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        if os.path.exists(file_path):
            return send_file(file_path, mimetype='image/png')
        else:
            return jsonify({'error': '图片不存在'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({'status': 'healthy', 'message': '服务运行正常'})


def print_hi(original, mask):
    try:
        # 处理蒙版
        with Image.open(original) as original_pil:
            # 保存原始元数据
            metadata = PngImagePlugin.PngInfo()
            if hasattr(original_pil, 'text'):
                for k, v in original_pil.text.items():
                    metadata.add_text(k, v)

            original_pil = original_pil.convert('RGBA')

            with Image.open(mask) as mask_pil:
                # 获取alpha通道并反转
                mask_alpha = mask_pil.convert('L')  # 转换为灰度图像获取alpha
                inverted_alpha = ImageOps.invert(mask_alpha)  # 反转alpha值

                # 将反转后的alpha应用到原始图像
                original_pil.putalpha(inverted_alpha)

            original_pil.save("output_direct.png", format="PNG", compress_level=4, pnginfo=metadata)
        return True
    except Exception as e:
        print(f"Error: {e}")  # 打印错误信息便于调试
        return False


sock = Sock(app)

auth_token = "5809bb0cc06f4fd4dbc0a79564e14d5d"


# 优化的全局连接管理器 - 支持多平台
class GlobalConnectionManager:
    def __init__(self):
        # 前端连接管理: { connection_id: { ws: websocket, tasks: set(task_ids), last_activity: timestamp } }
        self.frontend_connections = {}

        # 任务监听管理: { task_id: {
        #   prompt_id: str,
        #   platform: 'comfyui'|'runninghub',
        #   instance_ws_url: str,
        #   comfy_ws: websocket,
        #   runninghub_ws: websocket,
        #   listeners: set(connection_ids),
        #   status: 'active'|'completed'|'failed',
        #   created_at: timestamp,
        #   task_data: dict  # 平台特定数据
        # }}
        self.task_listeners = {}

        # ComfyUI连接池: { instance_ws_url: { ws: websocket, last_used: timestamp, task_count: int } }
        self.comfy_connections = {}

        # RunningHub连接池: { netWssUrl: { ws: websocket, last_used: timestamp, task_count: int } }
        self.runninghub_connections = {}

        # 连接锁
        self.connection_locks = {}
        self.cleanup_thread = None
        self.running = True
        self.connection_timeout = 300  # 5分钟无活动超时

        # 任务状态缓存，避免重复处理已完成的任务
        self.completed_task_cache = {}  # { task_id: { status, timestamp, platform } }
        self.cache_cleanup_interval = 3600  # 1小时清理一次缓存

        # 队列状态缓存，用于检测队列变化
        self.queue_status_cache = {}  # { instance_ws_url: { queue_remaining: int, last_check: timestamp } }

        # 启动清理线程
        self.start_cleanup_thread()

    def start_cleanup_thread(self):
        """启动清理线程，定期清理断开的连接"""

        def cleanup_worker():
            while self.running:
                try:
                    time.sleep(60)  # 每60秒清理一次
                    self.cleanup_dead_connections()
                except Exception as e:
                    print(f"清理线程错误: {e}")

        self.cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self.cleanup_thread.start()

    def cleanup_dead_connections(self):
        """清理断开的连接和超时的连接"""
        try:
            current_time = time.time()

            # 清理前端连接
            dead_connections = []
            for conn_id, conn_info in self.frontend_connections.items():
                try:
                    # 检查连接是否超时
                    if current_time - conn_info.get('last_activity', 0) > self.connection_timeout:
                        print(f"连接 {conn_id} 超时，准备清理")
                        dead_connections.append(conn_id)
                        continue

                    # 尝试发送ping消息检测连接状态
                    conn_info['ws'].send(json.dumps({"type": "ping", "timestamp": current_time}))
                    conn_info['last_activity'] = current_time
                except Exception as e:
                    print(f"连接 {conn_id} 检测失败: {e}")
                    dead_connections.append(conn_id)

            # 移除断开的连接
            for conn_id in dead_connections:
                self.remove_frontend_connection(conn_id)

            # 清理ComfyUI连接
            dead_comfy_connections = []
            for connection_key, conn_info in self.comfy_connections.items():
                try:
                    # 检查连接状态和超时
                    if (self.is_closed(conn_info['ws']) or
                            current_time - conn_info.get('last_used', 0) > self.connection_timeout):
                        dead_comfy_connections.append((connection_key, conn_info.get('task_id')))
                except Exception as e:
                    print(f"ComfyUI连接 {connection_key} 检测失败: {e}")
                    dead_comfy_connections.append((connection_key, conn_info.get('task_id')))

            for connection_key, task_id in dead_comfy_connections:
                instance_url = self.comfy_connections[connection_key].get('instance_url', connection_key)
                self.close_comfy_connection(instance_url, task_id)

            # 清理RunningHub连接
            dead_runninghub_connections = []
            for connection_key, conn_info in self.runninghub_connections.items():
                try:
                    # 检查连接状态和超时
                    if (self.is_closed(conn_info['ws']) or
                            current_time - conn_info.get('last_used', 0) > self.connection_timeout):
                        dead_runninghub_connections.append((connection_key, conn_info.get('task_id')))
                except Exception as e:
                    print(f"RunningHub连接 {connection_key} 检测失败: {e}")
                    dead_runninghub_connections.append((connection_key, conn_info.get('task_id')))

            for connection_key, task_id in dead_runninghub_connections:
                self.close_runninghub_connection(connection_key, task_id)

            # 清理完成的任务监听器
            completed_tasks = []
            for task_id, task_info in self.task_listeners.items():
                if task_info['status'] in ['completed', 'failed']:
                    # 如果任务完成超过5分钟，清理监听器
                    if current_time - task_info.get('created_at', 0) > 300:
                        completed_tasks.append(task_id)

            for task_id in completed_tasks:
                self.close_task_listener(task_id)

            # 清理过期的任务缓存（每小时执行一次）
            if int(current_time) % 3600 < 60:  # 每小时执行一次
                self.cleanup_completed_tasks()
                self.cleanup_queue_status_cache()

        except Exception as e:
            print(f"清理连接时出错: {e}")

    def close_comfy_connection(self, instance_url, task_id=None):
        """关闭ComfyUI连接"""
        # 如果指定了task_id，关闭特定任务的连接
        if task_id:
            connection_key = f"{instance_url}_{task_id}"
            if connection_key in self.comfy_connections:
                try:
                    conn_info = self.comfy_connections[connection_key]
                    if not self.is_closed(conn_info['ws']):
                        asyncio.run(conn_info['ws'].close())
                    del self.comfy_connections[connection_key]
                    print(f"已关闭任务 {task_id} 的ComfyUI连接: {instance_url}")
                except Exception as e:
                    print(f"关闭任务 {task_id} 的ComfyUI连接失败: {e}")
                return

        # 如果没有指定task_id，关闭所有相关连接
        keys_to_remove = []
        for key, conn_info in self.comfy_connections.items():
            if conn_info.get('instance_url') == instance_url:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            try:
                conn_info = self.comfy_connections[key]
                if not self.is_closed(conn_info['ws']):
                    asyncio.run(conn_info['ws'].close())
                del self.comfy_connections[key]
                print(f"已关闭ComfyUI连接: {key}")
            except Exception as e:
                print(f"关闭ComfyUI连接失败: {e}")

    def close_runninghub_connection(self, netWssUrl, task_id=None):
        """关闭RunningHub连接"""
        # 如果指定了task_id，关闭特定任务的连接
        if task_id:
            connection_key = f"{netWssUrl}_{task_id}"
            if connection_key in self.runninghub_connections:
                try:
                    conn_info = self.runninghub_connections[connection_key]
                    if not self.is_closed(conn_info['ws']):
                        asyncio.run(conn_info['ws'].close())
                    del self.runninghub_connections[connection_key]
                    print(f"已关闭任务 {task_id} 的RunningHub连接: {netWssUrl}")
                except Exception as e:
                    print(f"关闭任务 {task_id} 的RunningHub连接失败: {e}")
                return

        # 如果没有指定task_id，关闭所有相关连接
        keys_to_remove = []
        for key, conn_info in self.runninghub_connections.items():
            if conn_info.get('netWssUrl') == netWssUrl:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            try:
                conn_info = self.runninghub_connections[key]
                if not self.is_closed(conn_info['ws']):
                    asyncio.run(conn_info['ws'].close())
                del self.runninghub_connections[key]
                print(f"已关闭RunningHub连接: {key}")
            except Exception as e:
                print(f"关闭RunningHub连接失败: {e}")

    async def create_comfy_connection(self, instance_ws_url, task_id=None):
        """为每个任务创建独立的ComfyUI连接"""
        # 确保URL有正确的协议前缀
        if not instance_ws_url.startswith(('ws://', 'wss://')):
            if instance_ws_url.startswith('http://'):
                instance_ws_url = instance_ws_url.replace('http://', 'ws://')
            elif instance_ws_url.startswith('https://'):
                instance_ws_url = instance_ws_url.replace('https://', 'wss://')
            else:
                instance_ws_url = f"ws://{instance_ws_url}"

        # 确保URL以/ws结尾
        if not instance_ws_url.endswith('/ws'):
            instance_ws_url = f"{instance_ws_url}/ws"

        # 为每个任务创建独立的连接
        connection_key = f"{instance_ws_url}_{task_id}" if task_id else instance_ws_url

        if connection_key in self.comfy_connections:
            conn_info = self.comfy_connections[connection_key]
            if not self.is_closed(conn_info['ws']):
                conn_info['last_used'] = time.time()
                conn_info['task_count'] += 1
                return conn_info['ws']

        # 验证URL格式
        if not self.validate_websocket_url(instance_ws_url, "ComfyUI"):
            return None

        try:
            print(f"正在连接到ComfyUI实例: {instance_ws_url}")
            comfy_ws = await websockets.connect(instance_ws_url)
            self.comfy_connections[connection_key] = {
                'ws': comfy_ws,
                'last_used': time.time(),
                'task_count': 1,
                'task_id': task_id,
                'instance_url': instance_ws_url
            }
            print(f"为任务 {task_id} 创建新的ComfyUI连接: {instance_ws_url}")
            return comfy_ws
        except Exception as e:
            print(f"为任务 {task_id} 创建ComfyUI连接失败: {e}")
            return None

    def validate_websocket_url(self, url, platform="WebSocket"):
        """验证WebSocket URL格式"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)

            # 检查协议
            if parsed.scheme not in ['ws', 'wss']:
                print(f"❌ {platform} URL协议错误: {parsed.scheme} (应该是ws或wss)")
                return False

            # 检查主机名
            if not parsed.hostname:
                print(f"❌ {platform} URL缺少主机名")
                return False

            # 检查端口
            if parsed.port and (parsed.port < 1 or parsed.port > 65535):
                print(f"❌ {platform} URL端口无效: {parsed.port}")
                return False

            print(f"✅ {platform} URL格式验证通过")
            print(f"   协议: {parsed.scheme}")
            print(f"   主机: {parsed.hostname}")
            print(f"   端口: {parsed.port or ('443' if parsed.scheme == 'wss' else '80')}")
            return True

        except Exception as e:
            print(f"❌ {platform} URL验证失败: {e}")
            return False

    async def create_runninghub_connection(self, netWssUrl, task_id=None):
        """为每个任务创建独立的RunningHub连接"""
        # 验证URL格式
        if not self.validate_websocket_url(netWssUrl, "RunningHub"):
            return None

        # 为每个任务创建独立的连接
        connection_key = f"{netWssUrl}_{task_id}" if task_id else netWssUrl

        if connection_key in self.runninghub_connections:
            conn_info = self.runninghub_connections[connection_key]
            if not self.is_closed(conn_info['ws']):
                conn_info['last_used'] = time.time()
                conn_info['task_count'] += 1
                print(f"🔄 复用现有RunningHub连接 (task_id={task_id})")
                return conn_info['ws']

        try:
            print(f"正在连接到RunningHub实例: {netWssUrl[:100]}...")
            runninghub_ws = await websockets.connect(netWssUrl)
            self.runninghub_connections[connection_key] = {
                'ws': runninghub_ws,
                'last_used': time.time(),
                'task_count': 1,
                'task_id': task_id,
                'netWssUrl': netWssUrl
            }
            print(f"为任务 {task_id} 创建新的RunningHub连接: {netWssUrl[:100]}...")
            return runninghub_ws
        except Exception as e:
            print(f"为任务 {task_id} 创建RunningHub连接失败: {e}")
            return None

    def add_frontend_connection(self, connection_id, ws):
        """添加前端连接"""
        self.frontend_connections[connection_id] = {
            'ws': ws,
            'tasks': set(),
            'last_activity': time.time()
        }
        print(f"添加前端连接 (connection_id={connection_id})")

    def remove_frontend_connection(self, connection_id):
        """移除前端连接"""
        if connection_id in self.frontend_connections:
            conn_info = self.frontend_connections[connection_id]

            # 从所有任务监听器中移除
            for task_id in conn_info['tasks']:
                if task_id in self.task_listeners:
                    self.task_listeners[task_id]['listeners'].discard(connection_id)

                    # 如果没有监听器了，关闭ComfyUI连接
                    if not self.task_listeners[task_id]['listeners']:
                        self.close_task_listener(task_id)

            del self.frontend_connections[connection_id]
            print(f"移除前端连接 (connection_id={connection_id})")

    def update_connection_activity(self, connection_id):
        """更新连接活动时间"""
        if connection_id in self.frontend_connections:
            self.frontend_connections[connection_id]['last_activity'] = time.time()

    def subscribe_task(self, connection_id, task_id, prompt_id, instance_ws_url, platform='comfyui', task_data=None):
        """订阅任务进度 - 支持多平台"""
        print(f"订阅任务: task_id={task_id}, platform={platform}, instance_ws_url={instance_ws_url}")
        print(f"task_data: {task_data}")
        # 检查任务是否已经完成或失败（包括缓存检查）
        if self.is_task_cached_completed(task_id):
            print(f"任务 {task_id} 在缓存中已标记为完成，验证实际状态...")
            # 验证任务是否真的完成了
            if self.validate_cached_task_status(task_id, prompt_id, instance_ws_url):
                print(f"任务 {task_id} 确实已完成，发送完成消息")
                # 直接发送完成消息给前端
                completion_message = {
                    "type": "completed",
                    "data": {
                        "prompt_id": prompt_id,
                        "status": "success"
                    },
                    "task_id": task_id
                }
                if connection_id in self.frontend_connections:
                    try:
                        self.frontend_connections[connection_id]['ws'].send(json.dumps(completion_message))
                        print(f"已发送完成消息给连接 {connection_id}")
                    except Exception as e:
                        print(f"发送完成消息失败: {e}")
                return
            else:
                print(f"任务 {task_id} 在缓存中但实际未完成，继续订阅")

        # 检查任务是否已经完成或失败
        if task_id in self.task_listeners:
            task_info = self.task_listeners[task_id]
            if task_info['status'] in ['completed', 'failed', 'closed']:
                print(f"任务 {task_id} 已经完成或失败，跳过订阅")
                # 缓存任务状态
                self.cache_completed_task(task_id, task_info['status'])
                # 直接发送完成消息给前端
                completion_message = {
                    "type": "completed",
                    "data": {
                        "prompt_id": prompt_id,
                        "status": "success"
                    },
                    "task_id": task_id
                }
                if connection_id in self.frontend_connections:
                    try:
                        self.frontend_connections[connection_id]['ws'].send(json.dumps(completion_message))
                        print(f"已发送完成消息给连接 {connection_id}")
                    except Exception as e:
                        print(f"发送完成消息失败: {e}")
                return

        # 更新连接活动时间
        self.update_connection_activity(connection_id)

        # 添加到前端连接的任务列表
        if connection_id in self.frontend_connections:
            self.frontend_connections[connection_id]['tasks'].add(task_id)

        # 创建或更新任务监听器
        if task_id not in self.task_listeners:
            self.task_listeners[task_id] = {
                'prompt_id': prompt_id,
                'platform': platform,
                'instance_ws_url': instance_ws_url,
                'comfy_ws': None,
                'runninghub_ws': None,
                'listeners': set(),
                'status': 'active',
                'created_at': time.time(),
                'task_data': task_data or {}
            }

        self.task_listeners[task_id]['listeners'].add(connection_id)

        # 根据平台启动相应的监听
        print(f"根据平台 '{platform}' 启动监听器")
        if platform == 'comfyui':
            print(f"启动ComfyUI监听器 for task {task_id}")
            if not self.task_listeners[task_id]['comfy_ws']:
                self.start_comfy_listener(task_id)
        elif platform == 'runninghub':
            print(f"启动RunningHub监听器 for task {task_id}")
            if not self.task_listeners[task_id]['runninghub_ws']:
                self.start_runninghub_listener(task_id)
        else:
            print(f"未知平台: {platform}")

        print(f"订阅任务完成 (connection_id={connection_id}, task_id={task_id}, platform={platform})")

    def unsubscribe_task(self, connection_id, task_id):
        """取消订阅任务"""
        # 更新连接活动时间
        self.update_connection_activity(connection_id)

        # 从前端连接的任务列表中移除
        if connection_id in self.frontend_connections:
            self.frontend_connections[connection_id]['tasks'].discard(task_id)

        # 从任务监听器中移除
        if task_id in self.task_listeners:
            self.task_listeners[task_id]['listeners'].discard(connection_id)

            # 如果没有监听器了，关闭ComfyUI连接
            if not self.task_listeners[task_id]['listeners']:
                self.close_task_listener(task_id)

        print(f"取消订阅任务 (connection_id={connection_id}, task_id={task_id})")

    def is_closed(self, websocket):
        if websocket.transport is None or websocket.transport.is_closing():
            return True

        # 检查 socket 是否可读/可写
        sock = websocket.transport.get_extra_info('socket')
        if sock is None:
            return True
        return False

    def start_comfy_listener(self, task_id):
        """启动ComfyUI监听"""
        task_info = self.task_listeners[task_id]

        def start_listener():
            asyncio.run(self.listen_to_comfy_instance(task_id))

        threading.Thread(target=start_listener, daemon=True).start()

    def start_runninghub_listener(self, task_id):
        """启动RunningHub监听"""
        task_info = self.task_listeners[task_id]

        def start_listener():
            asyncio.run(self.listen_to_runninghub_instance(task_id))

        threading.Thread(target=start_listener, daemon=True).start()

    async def listen_to_comfy_instance(self, task_id):
        """监听ComfyUI实例的消息"""
        task_info = self.task_listeners[task_id]
        comfy_ws = None

        try:
            # 再次检查任务状态
            if task_info['status'] in ['completed', 'failed', 'closed']:
                print(f"任务 {task_id} 已经完成或失败，跳过监听")
                return

            # 为每个任务创建独立的连接
            comfy_ws = await self.create_comfy_connection(task_info['instance_ws_url'], task_id)
            if not comfy_ws:
                print(f"无法连接到ComfyUI实例: {task_info['instance_ws_url']}")
                task_info['status'] = 'failed'
                self.send_completion_message(task_id, task_info['prompt_id'])
                return

            task_info['comfy_ws'] = comfy_ws
            print(f"开始监听ComfyUI实例: {task_info['instance_ws_url']} (task_id={task_id})")

            # 设置超时时间（30分钟）
            timeout = time.time() + 1800
            last_message_time = time.time()

            while task_info['status'] == 'active' and not self.is_closed(comfy_ws):
                try:
                    # 检查总超时
                    if time.time() > timeout:
                        print(f"ComfyUI任务 {task_id} 总监听超时 (30分钟)")
                        task_info['status'] = 'failed'
                        self.send_completion_message(task_id, task_info['prompt_id'])
                        break

                    # 检查消息接收超时（5分钟无消息则认为连接有问题）
                    if time.time() - last_message_time > 300:
                        print(f"ComfyUI任务 {task_id} 长时间无消息，检查连接状态")
                        try:
                            # 发送ping测试连接
                            await asyncio.wait_for(comfy_ws.ping(), timeout=10)
                            last_message_time = time.time()
                            print(f"ComfyUI任务 {task_id} 连接正常，继续监听")
                        except Exception as ping_e:
                            print(f"ComfyUI任务 {task_id} 连接已断开: {ping_e}")
                            task_info['status'] = 'failed'
                            self.send_completion_message(task_id, task_info['prompt_id'])
                            break

                    message = await asyncio.wait_for(comfy_ws.recv(), timeout=30)  # 30秒消息接收超时
                    last_message_time = time.time()
                    try:
                        message_data = json.loads(message)
                    except Exception as e:
                        print("报错了，信息是：",e)
                        continue

                    # 忽略监控消息和ping消息
                    if message_data.get("type") in ["crystools.monitor", "ping"]:
                        continue

                    print(f"从ComfyUI收到消息 (task_id={task_id}): {message_data}")

                    # 验证消息是否属于当前任务
                    message_prompt_id = None
                    message_data_obj = message_data.get("data", {})

                    # 从不同位置提取prompt_id
                    if isinstance(message_data_obj, dict):
                        message_prompt_id = message_data_obj.get("prompt_id")
                    elif isinstance(message_data, dict):
                        message_prompt_id = message_data.get("prompt_id")

                    # 如果消息中没有prompt_id，或者prompt_id不匹配，则忽略该消息
                    if message_prompt_id and message_prompt_id != task_info['prompt_id']:
                        print(
                            f"忽略不属于任务 {task_id} 的消息 (消息prompt_id: {message_prompt_id}, 任务prompt_id: {task_info['prompt_id']})")
                        continue

                    # 处理不同类型的消息
                    message_type = message_data.get("type")

                    if message_type == "executing":
                        # 任务开始执行
                        print(f"ComfyUI任务 {task_id} 开始执行")
                        # 添加平台标识
                        message_with_platform = {
                            **message_data,
                            "platform": "comfyui"
                        }
                        self.broadcast_to_task_listeners(task_id, message_with_platform)

                    elif message_type == "progress":
                        # ComfyUI进度更新消息
                        progress_data = message_data.get("data", {})
                        if progress_data:
                            # 发送进度更新消息，添加平台标识
                            progress_message = {
                                "type": "progress",
                                "data": {
                                    "value": progress_data.get("value", 0),
                                    "max": progress_data.get("max", 100),
                                    "prompt_id": task_info['prompt_id'],
                                    "platform": "comfyui",
                                    "node": progress_data.get("node")
                                },
                                "task_id": task_id
                            }
                            self.broadcast_to_task_listeners(task_id, progress_message)
                            print(f"ComfyUI任务 {task_id} 进度更新: {progress_data.get('value', 0)}/{progress_data.get('max', 100)}")

                    elif message_type == "executed":
                        # ComfyUI任务完成
                        print(f"ComfyUI任务 {task_id} 已完成 (executed消息)")
                        task_info['status'] = 'completed'
                        self.send_completion_message(task_id, task_info['prompt_id'])
                        break

                    elif message_type == "execution_success":
                        # ComfyUI执行成功消息（某些版本使用）
                        print(f"ComfyUI任务 {task_id} 执行成功 (execution_success消息)")
                        task_info['status'] = 'completed'
                        self.send_completion_message(task_id, task_info['prompt_id'])
                        break

                    elif message_type == "execution_error":
                        # ComfyUI执行错误消息
                        print(f"ComfyUI任务 {task_id} 执行失败 (execution_error消息)")
                        error_data = message_data.get("data", {})
                        error_message = {
                            "type": "error",
                            "data": {
                                "error": error_data.get("exception_message", "执行失败"),
                                "prompt_id": task_info['prompt_id'],
                                "platform": "comfyui"
                            },
                            "task_id": task_id
                        }
                        self.broadcast_to_task_listeners(task_id, error_message)
                        task_info['status'] = 'failed'
                        self.send_completion_message(task_id, task_info['prompt_id'])
                        break

                    elif message_type == "status":
                        # 状态更新消息
                        self.broadcast_to_task_listeners(task_id, message_data)

                        # 检查队列剩余任务数量变化
                        status_data = message_data.get("data", {})
                        if isinstance(status_data, dict):
                            exec_info = status_data.get("status", {}).get("exec_info", {})
                            queue_remaining = exec_info.get("queue_remaining")

                            if queue_remaining is not None:
                                # 队列剩余任务数量发生变化，检查所有相关任务是否已完成
                                self.check_tasks_completion_on_queue_change(task_info['instance_ws_url'],
                                                                            queue_remaining)

                        # 检查当前任务是否完成
                        status_data = message_data.get("data", {})
                        if (isinstance(status_data, dict) and
                                status_data.get("status", {}).get("exec_info", {}).get("queue_remaining") == 0):
                            print(f"检测到任务 {task_id} 可能已完成")
                            task_info['status'] = 'completed'
                            self.send_completion_message(task_id, task_info['prompt_id'])
                            break

                        # 检查历史记录
                        try:
                            base_url = self.build_http_url_from_ws_url(task_info['instance_ws_url'])
                            success, result = check_comfyui_history(task_info['prompt_id'], base_url)
                            if success:
                                task_info['status'] = 'completed'
                                self.send_completion_message(task_id, task_info['prompt_id'])
                                break
                        except Exception as e:
                            print(f"检查历史记录失败: {e}")

                    else:
                        # 其他消息类型直接转发
                        self.broadcast_to_task_listeners(task_id, message_data)

                except asyncio.TimeoutError:
                    print(f"ComfyUI消息接收超时 (task_id={task_id})")
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print(f"ComfyUI连接已关闭: {task_info['instance_ws_url']} (task_id={task_id})")
                    task_info['status'] = 'failed'
                    self.send_completion_message(task_id, task_info['prompt_id'])
                    break
                except Exception as e:
                    print(f"处理ComfyUI消息时出错 (task_id={task_id}): {e}")
                    task_info['status'] = 'failed'
                    break

        except Exception as e:
            print(f"监听ComfyUI实例失败 (task_id={task_id}): {e}")
            task_info['status'] = 'failed'
            self.send_completion_message(task_id, task_info['prompt_id'])
        finally:
            if comfy_ws:
                try:
                    await comfy_ws.close()
                except Exception:
                    pass

    def broadcast_to_task_listeners(self, task_id, message):
        """向任务的所有监听器广播消息"""
        if task_id not in self.task_listeners:
            return

        task_info = self.task_listeners[task_id]
        dead_connections = []

        # 添加任务ID到消息中
        message_with_task_id = {
            **message,
            'task_id': task_id
        }

        for conn_id in task_info['listeners']:
            if conn_id in self.frontend_connections:
                try:
                    self.frontend_connections[conn_id]['ws'].send(json.dumps(message_with_task_id))
                    # 更新连接活动时间
                    self.frontend_connections[conn_id]['last_activity'] = time.time()
                except Exception as e:
                    print(f"向连接 {conn_id} 发送消息失败: {e}")
                    dead_connections.append(conn_id)
            else:
                dead_connections.append(conn_id)

        # 移除断开的连接
        for conn_id in dead_connections:
            task_info['listeners'].discard(conn_id)
            if conn_id in self.frontend_connections:
                self.frontend_connections[conn_id]['tasks'].discard(task_id)

    def close_task_listener(self, task_id):
        """关闭任务监听器"""
        if task_id in self.task_listeners:
            task_info = self.task_listeners[task_id]
            task_info['status'] = 'closed'

            # 关闭ComfyUI连接
            if task_info['comfy_ws']:
                try:
                    asyncio.run(task_info['comfy_ws'].close())
                except:
                    pass

            # 关闭特定任务的连接
            self.close_comfy_connection(task_info['instance_ws_url'], task_id)

            del self.task_listeners[task_id]
            print(f"关闭任务监听器 (task_id={task_id})")

    def send_completion_message(self, task_id, prompt_id=None):
        """发送完成消息"""
        try:
            # 缓存任务状态
            self.cache_completed_task(task_id, 'completed')

            # 获取prompt_id
            if prompt_id is None and task_id in self.task_listeners:
                prompt_id = self.task_listeners[task_id]['prompt_id']
            elif prompt_id is None:
                # 如果任务不在监听器中，尝试从缓存中获取
                print(f"警告: 任务 {task_id} 不在监听器中，无法获取prompt_id")
                return

            completion_message = {
                "type": "completed",
                "data": {
                    "prompt_id": prompt_id,
                    "status": "success"
                },
                "task_id": task_id
            }
            self.broadcast_to_task_listeners(task_id, completion_message)
            print(f"已发送完成消息给任务 {task_id}")
        except Exception as e:
            print(f"发送完成消息失败: {e}")

    def get_connection_stats(self):
        """获取连接统计信息"""
        return {
            'frontend_connections': len(self.frontend_connections),
            'task_listeners': len(self.task_listeners),
            'comfy_connections': len(self.comfy_connections),
            'active_tasks': len([t for t in self.task_listeners.values() if t['status'] == 'active']),
            'queue_status_cache': len(self.queue_status_cache),
            'completed_task_cache': len(self.completed_task_cache)
        }

    def shutdown(self):
        """关闭所有连接"""
        self.running = False

        # 关闭所有ComfyUI连接
        for connection_key, conn_info in self.comfy_connections.items():
            try:
                asyncio.run(conn_info['ws'].close())
            except:
                pass

        # 关闭所有RunningHub连接
        for connection_key, conn_info in self.runninghub_connections.items():
            try:
                asyncio.run(conn_info['ws'].close())
            except:
                pass

        # 关闭所有前端连接
        for conn_info in self.frontend_connections.values():
            try:
                conn_info['ws'].close()
            except:
                pass

        self.comfy_connections.clear()
        self.runninghub_connections.clear()
        self.frontend_connections.clear()
        self.task_listeners.clear()
        self.queue_status_cache.clear()
        self.completed_task_cache.clear()

    def is_task_completed(self, task_id):
        """检查任务是否已完成"""
        return task_id in self.completed_task_cache

    def cache_completed_task(self, task_id, status='completed'):
        """缓存已完成的任务"""
        self.completed_task_cache[task_id] = {
            'status': status,
            'timestamp': time.time()
        }
        print(f"缓存已完成任务: {task_id}")

    def is_task_cached_completed(self, task_id):
        """检查任务是否在缓存中标记为已完成"""
        if task_id in self.completed_task_cache:
            cache_info = self.completed_task_cache[task_id]
            # 检查缓存是否过期（2小时，减少错误缓存的影响）
            if time.time() - cache_info['timestamp'] < 7200:
                return True
            else:
                # 清理过期缓存
                del self.completed_task_cache[task_id]
        return False

    def validate_cached_task_status(self, task_id, prompt_id, instance_ws_url):
        """验证缓存中的任务状态是否准确"""
        try:
            base_url = self.build_http_url_from_ws_url(instance_ws_url)
            success, result = check_comfyui_history(prompt_id, base_url)
            if not success:
                # 如果任务实际未完成，清除错误的缓存
                if task_id in self.completed_task_cache:
                    del self.completed_task_cache[task_id]
                    print(f"清除错误的缓存: 任务 {task_id} 实际未完成")
                return False
            return True
        except Exception as e:
            print(f"验证缓存任务状态时出错: {e}")
            # 验证失败时，清除缓存以确保安全
            if task_id in self.completed_task_cache:
                del self.completed_task_cache[task_id]
            return False

    def build_http_url_from_ws_url(self, ws_url):
        """从WebSocket URL构建HTTP URL"""
        if ws_url.startswith(('ws://', 'wss://')):
            return ws_url.replace("ws://", "http://").replace("wss://", "https://").replace("/ws", "")
        elif ws_url.startswith(('http://', 'https://')):
            return ws_url.replace("/ws", "")
        else:
            # 如果没有协议前缀，添加http://
            return f"http://{ws_url.replace('/ws', '')}"

    def check_tasks_completion_on_queue_change(self, instance_ws_url, queue_remaining):
        """当队列剩余任务数量发生变化时，检查所有相关任务是否已完成"""
        try:
            current_time = time.time()

            # 检查队列状态是否真正发生变化
            if instance_ws_url in self.queue_status_cache:
                cached_status = self.queue_status_cache[instance_ws_url]
                cached_queue_remaining = cached_status.get('queue_remaining')

                # 如果队列数量没有减少，则不进行检查
                if cached_queue_remaining is not None and queue_remaining >= cached_queue_remaining:
                    print(f"队列数量未减少 (从 {cached_queue_remaining} 到 {queue_remaining})，跳过检查")
                    # 更新缓存
                    self.queue_status_cache[instance_ws_url] = {
                        'queue_remaining': queue_remaining,
                        'last_check': current_time
                    }
                    return

            print(f"队列剩余任务数量变化: {queue_remaining}, 检查实例 {instance_ws_url} 的所有任务")

            # 更新队列状态缓存
            self.queue_status_cache[instance_ws_url] = {
                'queue_remaining': queue_remaining,
                'last_check': current_time
            }

            # 获取该实例的所有活跃任务
            instance_tasks = []
            for task_id, task_info in self.task_listeners.items():
                if (task_info['instance_ws_url'] == instance_ws_url and
                        task_info['status'] == 'active'):
                    instance_tasks.append((task_id, task_info))

            if not instance_tasks:
                print(f"实例 {instance_ws_url} 没有活跃任务")
                return

            print(f"实例 {instance_ws_url} 有 {len(instance_tasks)} 个活跃任务，开始检查完成状态")

            # 检查每个任务的完成状态
            base_url = self.build_http_url_from_ws_url(instance_ws_url)
            print(f"构建的base_url: {base_url}")
            completed_tasks = []

            for task_id, task_info in instance_tasks:
                try:
                    # 检查历史记录
                    success, result = check_comfyui_history(task_info['prompt_id'], base_url)
                    if success:
                        print(f"任务 {task_id} 已完成，准备发送完成通知")
                        completed_tasks.append(task_id)
                        task_info['status'] = 'completed'
                        self.send_completion_message(task_id, task_info['prompt_id'])
                    else:
                        print(f"任务 {task_id} 尚未完成: {result}")
                except Exception as e:
                    print(f"检查任务 {task_id} 完成状态时出错: {e}")

            if completed_tasks:
                print(f"队列变化检测到 {len(completed_tasks)} 个任务已完成: {completed_tasks}")
            else:
                print("队列变化但未检测到已完成的任务")

        except Exception as e:
            print(f"检查任务完成状态时出错: {e}")

    def cleanup_completed_tasks(self, max_age_hours=2):
        """清理超过指定时间的已完成任务"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600

        tasks_to_remove = []
        for task_id in self.completed_task_cache:
            cache_info = self.completed_task_cache[task_id]
            if current_time - cache_info.get('timestamp', 0) > max_age_seconds:
                tasks_to_remove.append(task_id)

        for task_id in tasks_to_remove:
            del self.completed_task_cache[task_id]

        if tasks_to_remove:
            print(f"清理了 {len(tasks_to_remove)} 个过期的已完成任务缓存")

        return len(tasks_to_remove)

    def cleanup_queue_status_cache(self, max_age_hours=2):
        """清理超过指定时间的队列状态缓存"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600

        cache_to_remove = []
        for instance_url in self.queue_status_cache:
            cache_info = self.queue_status_cache[instance_url]
            if current_time - cache_info.get('last_check', 0) > max_age_seconds:
                cache_to_remove.append(instance_url)

        for instance_url in cache_to_remove:
            del self.queue_status_cache[instance_url]

        if cache_to_remove:
            print(f"清理了 {len(cache_to_remove)} 个过期的队列状态缓存")

        return len(cache_to_remove)

    async def listen_to_runninghub_instance(self, task_id):
        """监听RunningHub实例的消息"""
        task_info = self.task_listeners[task_id]
        runninghub_ws = None

        try:
            # 再次检查任务状态
            if task_info['status'] in ['completed', 'failed', 'closed']:
                print(f"RunningHub任务 {task_id} 已经完成或失败，跳过监听")
                return

            # 获取RunningHub连接信息
            task_data = task_info.get('task_data', {})
            netWssUrl = task_data.get('netWssUrl')

            # 调试信息
            print(f"RunningHub任务 {task_id} 的task_data: {task_data}")
            print(f"提取的netWssUrl: {netWssUrl}")

            if not netWssUrl:
                print(f"RunningHub任务 {task_id} 缺少netWssUrl")
                print(f"可用的task_data键: {list(task_data.keys())}")
                task_info['status'] = 'failed'
                self.send_completion_message(task_id, task_info['prompt_id'])
                return

            # 验证netWssUrl格式
            if not netWssUrl.startswith(('ws://', 'wss://')):
                print(f"RunningHub任务 {task_id} 的netWssUrl格式不正确: {netWssUrl}")
                task_info['status'] = 'failed'
                self.send_completion_message(task_id, task_info['prompt_id'])
                return

            # 创建RunningHub连接
            runninghub_ws = await self.create_runninghub_connection(netWssUrl, task_id)
            if not runninghub_ws:
                print(f"无法连接到RunningHub实例: {netWssUrl}")
                task_info['status'] = 'failed'
                self.send_completion_message(task_id, task_info['prompt_id'])
                return

            task_info['runninghub_ws'] = runninghub_ws
            print(f"开始监听RunningHub实例: {netWssUrl} (task_id={task_id})")

            # 设置超时时间（10分钟）
            timeout = time.time() + 600

            while task_info['status'] == 'active' and not self.is_closed(runninghub_ws):
                try:
                    # 检查超时
                    if time.time() > timeout:
                        print(f"RunningHub任务 {task_id} 监听超时")
                        task_info['status'] = 'failed'
                        self.send_completion_message(task_id, task_info['prompt_id'])
                        break

                    message = await asyncio.wait_for(runninghub_ws.recv(), timeout=60)  # 60秒超时
                    try:
                        message_data = json.loads(message)
                    except Exception as e:
                        print(f"RunningHub消息解析失败: {e}")
                        continue

                    print(f"从RunningHub收到消息 (task_id={task_id}): {message_data}")

                    # 处理不同类型的RunningHub消息
                    message_type = message_data.get("type")

                    if message_type == "progress":
                        # RunningHub标准进度消息
                        progress_data = message_data.get("data", {})
                        progress_message = {
                            "type": "progress",
                            "data": {
                                "value": progress_data.get("value", 0),
                                "max": progress_data.get("max", 100),
                                "prompt_id": task_info['prompt_id'],
                                "platform": "runninghub",
                                "node": progress_data.get("node")
                            },
                            "task_id": task_id
                        }
                        self.broadcast_to_task_listeners(task_id, progress_message)
                        print(f"RunningHub任务 {task_id} 进度更新: {progress_data.get('value', 0)}/{progress_data.get('max', 100)}")

                    elif message_type == "progress_state":
                        # RunningHub进度状态消息 - 包含所有节点的进度信息
                        progress_data = message_data.get("data", {})
                        nodes = progress_data.get("nodes", {})

                        if nodes:
                            # 计算总体进度
                            total_value = 0
                            total_max = 0
                            running_nodes = []
                            finished_nodes = []

                            for node_id, node_info in nodes.items():
                                value = node_info.get("value", 0)
                                max_val = node_info.get("max", 1)
                                state = node_info.get("state", "unknown")

                                total_value += value
                                total_max += max_val

                                if state == "running":
                                    running_nodes.append(node_id)
                                elif state == "finished":
                                    finished_nodes.append(node_id)

                            # 发送进度消息
                            progress_message = {
                                "type": "progress",
                                "data": {
                                    "value": total_value,
                                    "max": total_max,
                                    "prompt_id": task_info['prompt_id'],
                                    "platform": "runninghub",
                                    "nodes_info": {
                                        "running": running_nodes,
                                        "finished": finished_nodes,
                                        "total_nodes": len(nodes)
                                    }
                                },
                                "task_id": task_id
                            }
                            self.broadcast_to_task_listeners(task_id, progress_message)

                            # 计算百分比用于日志
                            percentage = (total_value / total_max * 100) if total_max > 0 else 0
                            print(f"RunningHub任务 {task_id} 进度更新: {total_value:.1f}/{total_max} ({percentage:.1f}%) - 运行中节点: {running_nodes}")

                    elif message_type == "task_progress":
                        # RunningHub进度更新（兼容旧格式）
                        progress_data = message_data.get("data", {})
                        progress_message = {
                            "type": "progress",
                            "data": {
                                "value": progress_data.get("progress", 0),
                                "max": 100,
                                "prompt_id": task_info['prompt_id'],
                                "platform": "runninghub"
                            },
                            "task_id": task_id
                        }
                        self.broadcast_to_task_listeners(task_id, progress_message)
                        print(f"RunningHub任务 {task_id} 进度更新: {progress_data.get('progress', 0)}%")

                    elif message_type == "executing":
                        # RunningHub节点执行开始
                        executing_data = message_data.get("data", {})
                        executing_message = {
                            "type": "executing",
                            "data": {
                                "node": executing_data.get("node"),
                                "display_node": executing_data.get("display_node"),
                                "prompt_id": task_info['prompt_id'],
                                "platform": "runninghub"
                            },
                            "task_id": task_id
                        }
                        self.broadcast_to_task_listeners(task_id, executing_message)
                        print(f"RunningHub任务 {task_id} 开始执行节点: {executing_data.get('node')}")

                    elif message_type == "executed":
                        # RunningHub节点执行完成
                        executed_data = message_data.get("data", {})
                        executed_message = {
                            "type": "executed",
                            "data": {
                                "node": executed_data.get("node"),
                                "display_node": executed_data.get("display_node"),
                                "output": executed_data.get("output"),
                                "prompt_id": task_info['prompt_id'],
                                "platform": "runninghub"
                            },
                            "task_id": task_id
                        }
                        self.broadcast_to_task_listeners(task_id, executed_message)
                        print(f"RunningHub任务 {task_id} 完成执行节点: {executed_data.get('node')}")

                    elif message_type == "task_completed":
                        # RunningHub任务完成
                        print(f"RunningHub任务 {task_id} 已完成")
                        task_info['status'] = 'completed'
                        self.send_completion_message(task_id, task_info['prompt_id'])
                        break

                    elif message_type == "execution_start":
                        # RunningHub执行开始
                        start_data = message_data.get("data", {})
                        start_message = {
                            "type": "execution_start",
                            "data": {
                                "prompt_id": task_info['prompt_id'],
                                "platform": "runninghub",
                                "timestamp": start_data.get('timestamp')
                            },
                            "task_id": task_id
                        }
                        self.broadcast_to_task_listeners(task_id, start_message)
                        print(f"RunningHub任务 {task_id} 开始执行 (execution_start)")

                    elif message_type == "execution_cached":
                        # RunningHub执行缓存
                        cached_data = message_data.get("data", {})
                        cached_message = {
                            "type": "execution_cached",
                            "data": {
                                "nodes": cached_data.get("nodes", []),
                                "prompt_id": task_info['prompt_id'],
                                "platform": "runninghub",
                                "timestamp": cached_data.get('timestamp')
                            },
                            "task_id": task_id
                        }
                        self.broadcast_to_task_listeners(task_id, cached_message)
                        print(f"RunningHub任务 {task_id} 使用缓存 (execution_cached)")

                    elif message_type == "status":
                        # RunningHub状态消息
                        status_data = message_data.get("data", {})
                        status_message = {
                            "type": "status",
                            "data": {
                                "status": status_data.get("status"),
                                "prompt_id": task_info['prompt_id'],
                                "platform": "runninghub"
                            },
                            "task_id": task_id
                        }
                        self.broadcast_to_task_listeners(task_id, status_message)

                        # 检查队列状态
                        exec_info = status_data.get("status", {}).get("exec_info", {})
                        queue_remaining = exec_info.get("queue_remaining", 0)
                        if queue_remaining == 0:
                            print(f"RunningHub任务 {task_id} 队列已清空，可能即将完成")

                    elif message_type == "execution_error":
                        # RunningHub执行错误 - 任务失败
                        error_data = message_data.get('data', {})
                        error_message = error_data.get('exception_message', '未知错误')
                        error_type = error_data.get('exception_type', 'Error')
                        node_id = error_data.get('node_id', '未知节点')
                        node_type = error_data.get('node_type', '未知类型')

                        print(f"RunningHub任务 {task_id} 执行错误 (execution_error): {error_type} - {error_message} (节点: {node_id})")
                        task_info['status'] = 'failed'

                        # 发送错误消息给前端
                        error_message_to_frontend = {
                            "type": "error",
                            "data": {
                                "prompt_id": task_info['prompt_id'],
                                "platform": "runninghub",
                                "error_type": error_type,
                                "error_message": error_message,
                                "node_id": node_id,
                                "node_type": node_type,
                                "traceback": error_data.get('traceback', []),
                                "timestamp": error_data.get('timestamp')
                            },
                            "task_id": task_id
                        }
                        self.broadcast_to_task_listeners(task_id, error_message_to_frontend)
                        self.send_completion_message(task_id, task_info['prompt_id'])
                        break

                    elif message_type == "execution_success":
                        # RunningHub执行成功 - 这是RunningHub的标准完成消息
                        print(f"RunningHub任务 {task_id} 执行成功 (execution_success)")
                        task_info['status'] = 'completed'

                        # 发送完成消息，包含执行结果数据
                        completion_data = message_data.get('data', {})
                        completion_message = {
                            "type": "completed",
                            "data": {
                                "prompt_id": task_info['prompt_id'],
                                "platform": "runninghub",
                                "timestamp": completion_data.get('timestamp'),
                                "execution_time": completion_data.get('execution_time')
                            },
                            "task_id": task_id
                        }
                        self.broadcast_to_task_listeners(task_id, completion_message)
                        self.send_completion_message(task_id, task_info['prompt_id'])
                        break

                    elif message_type == "task_failed":
                        # RunningHub任务失败
                        print(f"RunningHub任务 {task_id} 执行失败")
                        task_info['status'] = 'failed'
                        self.send_completion_message(task_id, task_info['prompt_id'])
                        break

                    elif message_type == "status_update":
                        # 状态更新消息
                        status_data = message_data.get("data", {})
                        status = status_data.get("status")

                        if status in ["SUCCESS", "COMPLETED"]:
                            print(f"RunningHub任务 {task_id} 状态更新为完成")
                            task_info['status'] = 'completed'
                            self.send_completion_message(task_id, task_info['prompt_id'])
                            break
                        elif status in ["FAILED", "ERROR"]:
                            print(f"RunningHub任务 {task_id} 状态更新为失败")
                            task_info['status'] = 'failed'
                            self.send_completion_message(task_id, task_info['prompt_id'])
                            break
                        else:
                            # 转发状态更新消息
                            self.broadcast_to_task_listeners(task_id, message_data)

                    else:
                        # 其他消息类型直接转发
                        self.broadcast_to_task_listeners(task_id, message_data)

                except asyncio.TimeoutError:
                    print(f"RunningHub消息接收超时 (task_id={task_id})")
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print(f"RunningHub连接已关闭: {netWssUrl} (task_id={task_id})")
                    task_info['status'] = 'failed'
                    self.send_completion_message(task_id, task_info['prompt_id'])
                    break
                except Exception as e:
                    print(f"处理RunningHub消息时出错 (task_id={task_id}): {e}")
                    task_info['status'] = 'failed'
                    break

        except Exception as e:
            print(f"监听RunningHub实例失败 (task_id={task_id}): {e}")
            task_info['status'] = 'failed'
            self.send_completion_message(task_id, task_info['prompt_id'])
        finally:
            if runninghub_ws:
                try:
                    await runninghub_ws.close()
                except Exception:
                    pass


# 创建全局连接管理器实例
connection_manager = GlobalConnectionManager();

import requests
from urllib.parse import urljoin


def check_comfyui_history(history_id, base_url="http://h39-ibaxoy6ptbgsy1ta1-fwldoo5e-custom.service.onethingrobot.com",
                          timeout=10):
    """
    检查ComfyUI历史记录是否生成成功

    参数:
        history_id: 历史记录ID (如: 3ca77ea6-ea3e-4b4a-ae29-5531a60f9291)
        base_url: ComfyUI服务基础URL
        timeout: 请求超时时间(秒)

    返回:
        tuple: (是否成功, 返回数据/错误信息)
    """
    try:
        # 构建完整URL
        endpoint = f"/history/{history_id}"
        url = urljoin(base_url, endpoint)

        # 发送GET请求
        response = requests.get(
            url,
            timeout=timeout,
            headers={"Accept": "application/json"}
        )

        # 检查HTTP状态码
        if response.status_code == 200:
            data = response.json()

            # 检查返回数据是否包含有效内容
            if data and isinstance(data, dict):
                print(f"历史记录 {history_id} 请求成功")
                return True, data
            else:
                return False, "返回数据格式无效"

        elif response.status_code == 404:
            return False, "历史记录不存在"
        else:
            return False, f"HTTP错误: {response.status_code} - {response.text}"

    except requests.exceptions.Timeout:
        return False, "请求超时"
    except requests.exceptions.RequestException as e:
        return False, f"请求失败: {str(e)}"
    except json.JSONDecodeError:
        return False, "返回数据不是有效JSON"


@app.route('/api/run_workflow', methods=['POST'])
async def run_workflow():
    """启动工作流（需替换为实际调用）"""
    data = request.json
    # 实际项目中这里应调用对应实例的API
    return jsonify({
        "prompt_id": f"generated-id-{hash(str(data))}",
        "instance_ws_url": data["instance_ws_url"]
    })


@app.route('/api/websocket/stats', methods=['GET'])
def get_websocket_stats():
    """获取WebSocket连接统计信息"""
    return jsonify(connection_manager.get_connection_stats())


@app.route('/api/task/status', methods=['GET'])
def get_task_status():
    """获取任务状态信息"""
    try:
        task_id = request.args.get('task_id')
        if task_id:
            # 查询特定任务状态
            if task_id in connection_manager.task_listeners:
                return jsonify({
                    'success': True,
                    'task_id': task_id,
                    'status': connection_manager.task_listeners[task_id]
                })
            elif connection_manager.is_task_cached_completed(task_id):
                return jsonify({
                    'success': True,
                    'task_id': task_id,
                    'status': 'cached_completed',
                    'cache_info': connection_manager.completed_task_cache[task_id]
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f'任务 {task_id} 不存在'
                }), 404
        else:
            # 返回所有任务状态摘要
            active_tasks = len([t for t in connection_manager.task_listeners.values() if t['status'] == 'active'])
            completed_tasks = len(
                [t for t in connection_manager.task_listeners.values() if t['status'] in ['completed', 'failed']])
            cached_tasks = len(connection_manager.completed_task_cache)

            return jsonify({
                'success': True,
                'summary': {
                    'active_tasks': active_tasks,
                    'completed_tasks': completed_tasks,
                    'cached_tasks': cached_tasks,
                    'total_listeners': len(connection_manager.task_listeners)
                },
                'recent_tasks': dict(list(connection_manager.task_listeners.items())[-10:])  # 最近10个任务
            })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@sock.route('/ws/progress')
def progress_ws(ws):
    try:
        # 1. 接收前端订阅请求
        init_data = json.loads(ws.receive())
        print("收到前端订阅:", init_data)

        if init_data.get("type") == "ping":
            return

        # 处理连接信息
        if init_data.get("type") == "connection_info":
            connection_id = init_data.get("connection_id")
            if connection_id:
                connection_manager.add_frontend_connection(connection_id, ws)
                print(f"已注册前端连接 (connection_id={connection_id})")

                # 处理重连时的任务恢复
                subscribed_tasks = init_data.get("subscribed_tasks", [])
                for task_id in subscribed_tasks:
                    # 这里需要从数据库或其他地方获取任务的详细信息
                    # 暂时跳过，等待前端重新订阅
                    pass

                # 保持连接活跃
                try:
                    while True:
                        message = ws.receive()
                        if message is None:
                            break

                        # 处理其他消息类型
                        try:
                            data = json.loads(message)
                            connection_manager.update_connection_activity(connection_id)

                            if data.get("type") == "subscribe_task":
                                print(f"收到订阅消息: {data}")
                                connection_manager.subscribe_task(
                                    connection_id,
                                    data.get("task_id"),
                                    data.get("prompt_id"),
                                    data.get("instance_ws_url"),
                                    data.get("platform", "comfyui"),
                                    data.get("task_data", {})
                                )
                            elif data.get("type") == "unsubscribe_task":
                                connection_manager.unsubscribe_task(
                                    connection_id,
                                    data.get("task_id")
                                )
                            elif data.get("type") == "ping":
                                # 响应ping消息
                                ws.send(json.dumps({"type": "pong", "timestamp": time.time()}))
                        except json.JSONDecodeError:
                            pass

                except Exception as e:
                    print(f"WebSocket心跳异常: {e}")
                finally:
                    connection_manager.remove_frontend_connection(connection_id)
                return

        # 兼容旧版本：直接订阅任务
        prompt_id = init_data.get("prompt_id")
        instance_ws_url = init_data.get('instance_ws_url')
        task_id = init_data.get("task_id")

        if not prompt_id or not instance_ws_url or not task_id:
            ws.close(code=1008, reason="Missing required fields")
            return

        # 生成连接ID
        connection_id = f"legacy_{int(time.time() * 1000)}"
        connection_manager.add_frontend_connection(connection_id, ws)

        # 检查任务是否已经完成
        if connection_manager.is_task_cached_completed(task_id):
            print(f"任务 {task_id} 在缓存中已标记为完成，直接发送完成消息")
            completion_message = {
                "type": "completed",
                "data": {
                    "prompt_id": prompt_id,
                    "status": "success"
                },
                "task_id": task_id
            }
            try:
                ws.send(json.dumps(completion_message))
                print(f"已发送完成消息给连接 {connection_id}")
            except Exception as e:
                print(f"发送完成消息失败: {e}")
            return

        if task_id in connection_manager.task_listeners:
            task_info = connection_manager.task_listeners[task_id]
            if task_info['status'] in ['completed', 'failed', 'closed']:
                print(f"任务 {task_id} 已经完成，直接发送完成消息")
                completion_message = {
                    "type": "completed",
                    "data": {
                        "prompt_id": prompt_id,
                        "status": "success"
                    },
                    "task_id": task_id
                }
                try:
                    ws.send(json.dumps(completion_message))
                    print(f"已发送完成消息给连接 {connection_id}")
                except Exception as e:
                    print(f"发送完成消息失败: {e}")
                return

        # 兼容旧版本，默认使用ComfyUI平台
        platform = init_data.get("platform", "comfyui")
        task_data = init_data.get("task_data", {})
        connection_manager.subscribe_task(connection_id, task_id, prompt_id, instance_ws_url, platform, task_data)

        # 保持连接活跃
        try:
            while True:
                message = ws.receive()
                if message is None:
                    break
                # 更新连接活动时间
                connection_manager.update_connection_activity(connection_id)
        except Exception as e:
            print(f"WebSocket心跳异常: {e}")
        finally:
            connection_manager.remove_frontend_connection(connection_id)

    except Exception as e:
        print(f"WebSocket错误: {e}")


def start_background_listener():
    """后台线程启动监听（实际项目需改为按需启动）"""
    asyncio.new_event_loop().run_forever()


if __name__ == '__main__':
    threading.Thread(target=start_background_listener, daemon=True).start()

    try:
        # 启动Flask应用
        app.run(host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("正在关闭服务...")
        connection_manager.shutdown()
        print("服务已关闭")

# 访问 https://www.jetbrains.com/help/pycharm/ 获取 PyCharm 帮助
