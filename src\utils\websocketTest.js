import { globalWebSocketManager } from './comfyUITaskTracker';
import websocketLifecycleManager from './websocketLifecycleManager';
import websocketUtils from './websocketUtils';

/**
 * WebSocket功能测试脚本
 * 用于验证全局WebSocket管理器的各项功能
 */
export const testWebSocketManager = async () => {
  console.log('=== WebSocket管理器测试开始 ===');

  try {
    // 1. 测试连接状态
    console.log('1. 测试连接状态:');
    const connectionStatus = globalWebSocketManager.getConnectionStatus();
    console.log('连接状态:', connectionStatus);

    // 2. 测试生命周期管理器状态
    console.log('2. 测试生命周期管理器状态:');
    const lifecycleStatus = websocketLifecycleManager.getStatus();
    console.log('生命周期状态:', lifecycleStatus);

    // 3. 测试连接建立
    console.log('3. 测试连接建立:');
    await globalWebSocketManager.connect();
    console.log('连接建立成功');

    // 4. 测试连接状态更新
    console.log('4. 测试连接状态更新:');
    const updatedStatus = globalWebSocketManager.getConnectionStatus();
    console.log('更新后的连接状态:', updatedStatus);

    // 5. 测试任务订阅（模拟）
    console.log('5. 测试任务订阅:');
    const mockTaskId = 'test_task_' + Date.now();
    const mockCallbacks = {
      onProgress: (progress, max) => {
        console.log(`模拟任务进度: ${progress}/${max}`);
      },
      onCompleted: (outputs) => {
        console.log('模拟任务完成:', outputs);
      }
    };

    await globalWebSocketManager.subscribeTask(
      mockTaskId,
      'mock_prompt_id',
      'mock_instance_id',
      'ws://mock-instance.com/ws',
      mockCallbacks
    );

    console.log('任务订阅成功');

    // 6. 测试获取订阅任务列表
    console.log('6. 测试获取订阅任务列表:');
    const subscribedTasks = websocketUtils.getSubscribedTasks();
    console.log('订阅的任务:', subscribedTasks);

    // 7. 测试取消订阅
    console.log('7. 测试取消订阅:');
    globalWebSocketManager.unsubscribeTask(mockTaskId);
    console.log('任务取消订阅成功');

    // 8. 测试工具函数
    console.log('8. 测试工具函数:');
    const utilsStatus = websocketUtils.getConnectionStatus();
    console.log('工具函数状态:', utilsStatus);

    console.log('=== WebSocket管理器测试完成 ===');
    return true;

  } catch (error) {
    console.error('WebSocket测试失败:', error);
    return false;
  }
};

/**
 * 测试连接重连功能
 */
export const testReconnection = async () => {
  console.log('=== 测试连接重连功能 ===');

  try {
    // 模拟连接断开
    console.log('模拟连接断开...');
    if (globalWebSocketManager.ws) {
      globalWebSocketManager.ws.close();
    }

    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试重连
    console.log('测试重连...');
    const success = await websocketUtils.reconnect();
    
    if (success) {
      console.log('重连成功');
    } else {
      console.log('重连失败');
    }

    return success;

  } catch (error) {
    console.error('重连测试失败:', error);
    return false;
  }
};

/**
 * 测试生命周期管理
 */
export const testLifecycleManagement = () => {
  console.log('=== 测试生命周期管理 ===');

  try {
    // 测试生命周期状态
    const status = websocketLifecycleManager.getStatus();
    console.log('生命周期管理器状态:', status);

    // 测试用户退出登录（不实际关闭连接）
    console.log('测试用户退出登录处理...');
    // 注意：这里只是测试函数调用，不会实际关闭连接
    // websocketLifecycleManager.handleUserLogout();

    console.log('生命周期管理测试完成');
    return true;

  } catch (error) {
    console.error('生命周期管理测试失败:', error);
    return false;
  }
};

/**
 * 运行所有测试
 */
export const runAllTests = async () => {
  console.log('开始运行WebSocket功能测试...');

  const results = {
    managerTest: false,
    reconnectionTest: false,
    lifecycleTest: false
  };

  try {
    // 运行管理器测试
    results.managerTest = await testWebSocketManager();

    // 运行重连测试
    results.reconnectionTest = await testReconnection();

    // 运行生命周期测试
    results.lifecycleTest = testLifecycleManagement();

    console.log('=== 测试结果汇总 ===');
    console.log('管理器测试:', results.managerTest ? '通过' : '失败');
    console.log('重连测试:', results.reconnectionTest ? '通过' : '失败');
    console.log('生命周期测试:', results.lifecycleTest ? '通过' : '失败');

    const allPassed = Object.values(results).every(result => result);
    console.log('总体结果:', allPassed ? '全部通过' : '部分失败');

    return results;

  } catch (error) {
    console.error('测试运行失败:', error);
    return results;
  }
};

// 如果在浏览器环境中，可以添加到全局对象
if (typeof window !== 'undefined') {
  window.testWebSocketManager = testWebSocketManager;
  window.testReconnection = testReconnection;
  window.testLifecycleManagement = testLifecycleManagement;
  window.runAllTests = runAllTests;
}

export default {
  testWebSocketManager,
  testReconnection,
  testLifecycleManagement,
  runAllTests
}; 