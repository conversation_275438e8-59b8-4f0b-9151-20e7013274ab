import request from './request';

// 基础 API 配置
const BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:3002/api'  // 开发环境
  : 'https://api.aibikini.com/api';  // 生产环境

/**
 * 用户登录
 * @param {string} loginId - 用户名或手机号
 * @param {string} password - 密码
 * @returns {Promise<Object>} 登录结果，包含用户信息和token
 */
export const login = async (loginId, password) => {
  try {
    const response = await request.post('/auth/login', {
      loginId,
      password
    });
    
    if (response.token) {
      localStorage.setItem('token', response.token);
      localStorage.setItem('user', JSON.stringify(response.user));
    }
    
    return response;
  } catch (error) {
    // 改进错误处理，确保密码错误被正确标识
    if (error.status === 401 || 
        (error.message && (
          error.message.includes('密码') || 
          error.message.includes('认证') || 
          error.message.includes('auth') ||
          error.message.includes('credentials')
        ))
    ) {
      throw new Error('密码错误，请重新输入');
    }
    throw new Error(error.message || '登录失败，请检查账号和密码');
  }
};

/**
 * 用户注册
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} 注册结果
 */
export const register = async (userData) => {
  try {
    const response = await request.post('/auth/register', userData);
    return response;
  } catch (error) {
    throw new Error(error.message || '注册失败，请稍后重试');
  }
};

/**
 * 退出登录
 */
export const logout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
};

/**
 * 更新用户资料
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateUserProfile = async (userData) => {
  try {
    const response = await request.put('/auth/user/profile', userData);
    
    // 更新本地存储的用户信息
    if (response.success) {
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      const updatedUser = { ...currentUser, ...userData };
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
    
    return response;
  } catch (error) {
    throw new Error(error.message || '更新资料失败，请稍后重试');
  }
};

// 导出所有认证相关API
export default {
  login,
  register,
  logout,
  updateUserProfile
}; 