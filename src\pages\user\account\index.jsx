import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Table, Tag, Statistic, Row, Col, Select, Space, Button, message } from 'antd';
import { ReloadOutlined, WalletOutlined, CrownOutlined } from '@ant-design/icons';
import { MdOutlineAccountBalanceWallet } from 'react-icons/md';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import DateRangePicker from '../../../components/DateRangePicker';
import ContactSupport from '../../../components/ContactSupport';
import api from '../../../api';
import { getUserSubscription } from '../../../api/credits';
import RequireLogin from '../../../components/RequireLogin';
import './account.css';

// 设置 dayjs 默认语言为中文
dayjs.locale('zh-cn');
const { Option } = Select;

// 订阅计划名称映射（作为后备方案）
const getPlanDisplayName = (planCode, planName) => {
  // 优先使用后端返回的计划名称
  if (planName) {
    return planName;
  }
  
  // 后备方案：使用硬编码映射
  const planNameMap = {
    'free': '免费版',
    'design': '设计版',
    'model': '模特版',
    'full': '完整版',
    'enterprise': '企业版',
    'basic': '轻量版',
    'premium': '标准版'
  };
  return planNameMap[planCode] || '免费版';
};

const AccountPage = ({ isLoggedIn }) => {
  const [loading, setLoading] = useState(false);
  const [creditBalance, setCreditBalance] = useState(null);
  const [userSubscription, setUserSubscription] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [showContactModal, setShowContactModal] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 15,
    total: 0
  });
  const [filters, setFilters] = useState({
    dateRange: null,
    type: null
  });
  // 拖动弹窗相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });
  const modalRef = useRef(null);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 获取用户算力余额
  const fetchCreditBalance = async () => {
    try {
      const response = await api.get('/user/credits');
      setCreditBalance(response);
    } catch (error) {
      message.error('获取算力余额失败');
      console.error(error);
    }
  };

  // 获取用户订阅信息
  const fetchUserSubscription = async () => {
    try {
      const response = await getUserSubscription();
      console.log('获取到的用户订阅信息:', response);
      setUserSubscription(response);
    } catch (error) {
      console.error('获取用户订阅信息失败:', error);
      // 不显示错误消息，因为免费用户可能没有订阅
    }
  };

  // 获取交易记录
  const fetchTransactions = async (page = 1, pageSize = 15) => {
    setLoading(true);
    try {
      let queryParams = `page=${page}&limit=${pageSize}`;
      
      if (filters.type) {
        queryParams += `&type=${filters.type}`;
      }
      
      if (filters.dateRange && filters.dateRange.length === 2) {
        queryParams += `&startDate=${filters.dateRange[0].format('YYYY-MM-DD')}`;
        queryParams += `&endDate=${filters.dateRange[1].format('YYYY-MM-DD')}`;
      }
      
      const response = await api.get(`/user/credits/transactions?${queryParams}`);
      
      setTransactions(response.data || []);
      setPagination({
        current: response.page || 1,
        pageSize: response.limit || 10,
        total: response.total || 0
      });
    } catch (error) {
      message.error('获取交易记录失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (isLoggedIn) {
      fetchCreditBalance();
      fetchUserSubscription();
      fetchTransactions();
    }
  }, [isLoggedIn]);

  // 处理表格变化
  const handleTableChange = (pagination) => {
    fetchTransactions(pagination.current, pagination.pageSize);
  };

  // 处理筛选变化
  const handleFilterChange = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchTransactions(1, pagination.pageSize);
  };

  // 鼠标按下，开始拖动
  const handleModalMouseDown = (e) => {
    if (e.target.closest('.modal-header') && !e.target.closest('button')) {
      setIsDragging(true);
      const rect = modalRef.current.getBoundingClientRect();
      setDragOffset({ x: e.clientX - rect.left, y: e.clientY - rect.top });
      document.body.classList.add('no-select');
      modalRef.current.classList.add('dragging');
      e.preventDefault();
    }
  };

  // 鼠标移动，更新弹窗位置
  const handleMouseMove = useCallback((e) => {
    if (isDragging) {
      const newX = e.clientX - dragOffset.x;
      const newY = e.clientY - dragOffset.y;
      // 可加边界限制
      setModalPosition({ x: newX, y: newY });
    }
  }, [isDragging, dragOffset]);

  // 鼠标松开，结束拖动
  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      document.body.classList.remove('no-select');
      if (modalRef.current) modalRef.current.classList.remove('dragging');
    }
  }, [isDragging]);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 弹窗关闭时重置位置
  useEffect(() => {
    if (!showContactModal) {
      setModalPosition({ x: 0, y: 0 });
    }
  }, [showContactModal]);

  // 交易记录表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const typeMap = {
          recharge: { text: '充值', color: 'success' },
          consume: { text: '消费', color: 'processing' },
          refund: { text: '退款', color: 'warning' },
          bonus: { text: '奖励', color: 'purple' },
          deduct: { text: '扣除', color: 'error' },
          expire: { text: '过期', color: 'default' }
        };
        const { text, color } = typeMap[type] || { text: type, color: 'default' };
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '任务ID',
      dataIndex: ['metadata', 'taskId'],
      key: 'taskId',
      render: (taskId) => taskId || '-'
    },
    {
      title: '算力值 C',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => (
        <span style={{ color: 'var(--text-primary)' }}>
          {amount >= 0 ? '+' : ''}{amount}
        </span>
      )
    },
    {
      title: '余额 C',
      dataIndex: 'balance',
      key: 'balance'
    },
    {
      title: '说明',
      dataIndex: 'description',
      key: 'description'
    }
  ];

  // 渲染筛选区域
  const renderFilterArea = () => {
    if (isMobile) {
      return (
        <div className="mobile-filter-container">
          <div className="mobile-filter-row">
            <DateRangePicker
              value={filters.dateRange}
              onChange={(dates) => {
                setFilters(prev => ({ ...prev, dateRange: dates }));
              }}
            />
          </div>
          <div className="mobile-filter-row mobile-filter-inline">
            <Select
              allowClear
              placeholder="交易类型"
              value={filters.type}
              onChange={(value) => {
                setFilters(prev => ({ ...prev, type: value }));
              }}
              className="account-type-select mobile-type-select"
              dropdownClassName="account-type-dropdown"
            >
              <Option value="recharge">充值</Option>
              <Option value="consume">消费</Option>
              <Option value="refund">退款</Option>
              <Option value="bonus">奖励</Option>
              <Option value="deduct">扣除</Option>
              <Option value="expire">过期</Option>
            </Select>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleFilterChange}
              className="account-refresh-btn mobile-filter-btn"
            >
              筛选
            </Button>
          </div>
        </div>
      );
    }

    return (
      <Space>
        <DateRangePicker
          value={filters.dateRange}
          onChange={(dates) => {
            setFilters(prev => ({ ...prev, dateRange: dates }));
          }}
        />
        <Select
          allowClear
          placeholder="交易类型"
          style={{ width: 120 }}
          value={filters.type}
          onChange={(value) => {
            setFilters(prev => ({ ...prev, type: value }));
          }}
          className="account-type-select"
          dropdownClassName="account-type-dropdown"
        >
          <Option value="recharge">充值</Option>
          <Option value="consume">消费</Option>
          <Option value="refund">退款</Option>
          <Option value="bonus">奖励</Option>
          <Option value="deduct">扣除</Option>
          <Option value="expire">过期</Option>
        </Select>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={handleFilterChange}
          className="account-refresh-btn"
        >
          筛选
        </Button>
      </Space>
    );
  };

  return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="算力明细功能">
      <div className="account-page">
        <div className="account-container">
          {/* 算力统计卡片 */}
          <Card className="stats-card">
            <Row gutter={16}>
              <Col span={6}>
                <div className="balance-statistic-container">
                  <div className="balance-value-row">
                    <Statistic
                      title="当前算力余额"
                      value={creditBalance?.balance || 0}
                      suffix={
                        <span>
                          C
                          <Button
                            type="primary"
                            size="small"
                            onClick={() => setShowContactModal(true)}
                            className="recharge-btn"
                          >
                            充值
                          </Button>
                        </span>
                      }
                    />
                  </div>
                </div>
              </Col>
              <Col span={6}>
                <Statistic
                  title="累计充值"
                  value={creditBalance?.totalRecharged || 0}
                  valueStyle={{ color: 'var(--success-color)' }}
                  suffix=" C"
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="累计消费"
                  value={creditBalance?.totalConsumed || 0}
                  valueStyle={{ color: 'var(--brand-primary)' }}
                  suffix=" C"
                />
              </Col>
              <Col span={6}>
                <div className="subscription-statistic-container">
                  <Statistic
                    title="订阅计划"
                    value={getPlanDisplayName(userSubscription?.plan, userSubscription?.planName)}
                    prefix={<CrownOutlined className="subscription-crown-icon" />}
                    suffix={
                      <div className="subscription-expiry">
                        {userSubscription?.endDate && (
                          <div className="subscription-expiry-row">
                            {userSubscription.status === 'active' && (
                              <span className="subscription-status-row"><span className="subscription-status-dot active"></span><span className="subscription-status-text">活跃</span></span>
                            )}
                            {userSubscription.status === 'expired' && (
                              <span className="subscription-status-row"><span className="subscription-status-dot expired"></span><span className="subscription-status-text">已过期</span></span>
                            )}
                            {userSubscription.status === 'pending' && (
                              <span className="subscription-status-row"><span className="subscription-status-dot pending"></span><span className="subscription-status-text">待激活</span></span>
                            )}
                            {userSubscription.status === 'canceled' && (
                              <span className="subscription-status-row"><span className="subscription-status-dot canceled"></span><span className="subscription-status-text">已取消</span></span>
                            )}
                            {userSubscription.status === 'not_started' && (
                              <span className="subscription-status-row"><span className="subscription-status-dot not-started"></span><span className="subscription-status-text">未开始</span></span>
                            )}
                            <span className="subscription-expiry-date">到期: {dayjs(userSubscription.endDate).format('YYYY-MM-DD')}</span>
                          </div>
                        )}
                      </div>
                    }
                    valueStyle={{ 
                      color: 'var(--warning-color)'
                    }}
                  />
                </div>
              </Col>
            </Row>
          </Card>

          {/* 交易记录表格 */}
          <Card
            className="transactions-card"
            title="算力交易记录"
            extra={renderFilterArea()}
          >
            <Table
              columns={columns}
              dataSource={transactions}
              rowKey="_id"
              pagination={{
                ...pagination,
                className: 'account-pagination',
                showSizeChanger: true,
                pageSizeOptions: ['10', '15', '20', '50', '100'],
                showTotal: (total, range) => `第${range[0]}-${range[1]}条，共${total}条`,
                showQuickJumper: true,
                locale: {
                  items_per_page: '条/页',
                  jump_to: '跳至',
                  jump_to_confirm: '确定',
                  page: '页',
                },
                // 自定义每页条数下拉渲染为中文
                renderItem: (option) => `${option.value}条/页`,
              }}
              onChange={handleTableChange}
              loading={loading}
              className="account-transactions-table"
              components={{
                table: (props) => <table {...props} className="account-transactions-table-inner" />,
                header: {
                  wrapper: (props) => <thead {...props} className="account-transactions-table-header" />,
                  row: (props) => <tr {...props} className="account-transactions-table-header-row" />,
                  cell: (props) => <th {...props} className="account-transactions-table-header-cell" />
                },
                body: {
                  wrapper: (props) => <tbody {...props} className="account-transactions-table-body" />,
                  row: (props) => <tr {...props} className="account-transactions-table-body-row" />,
                  cell: (props) => <td {...props} className="account-transactions-table-body-cell" />
                }
              }}
            />
          </Card>
        </div>
        
        {/* 客服弹窗 */}
        {showContactModal && (
          <div className="modal-overlay" onClick={() => setShowContactModal(false)}>
            <div
              ref={modalRef}
              className="modal-content contact-modal"
              style={
                (modalPosition.x !== 0 || modalPosition.y !== 0)
                  ? {
                      left: modalPosition.x,
                      top: modalPosition.y,
                      position: 'fixed',
                      cursor: isDragging ? 'grabbing' : 'default',
                      zIndex: 1001
                    }
                  : {
                      cursor: isDragging ? 'grabbing' : 'default',
                      zIndex: 1001
                    }
              }
              onMouseDown={handleModalMouseDown}
              onClick={e => e.stopPropagation()}
            >
              <div className="modal-header" style={{ cursor: 'grab' }}>
                <h2>联系客服充值</h2>
                <button className="medium-close-button" onClick={() => setShowContactModal(false)}>×</button>
              </div>
              <div className="modal-body">
                <ContactSupport />
              </div>
            </div>
          </div>
        )}
      </div>
    </RequireLogin>
  );
};

export default AccountPage; 