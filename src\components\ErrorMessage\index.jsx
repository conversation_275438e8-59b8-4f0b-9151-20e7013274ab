import React from 'react';
import './styles.css';

/**
 * 错误消息组件
 * @param {Object} props - 组件属性
 * @param {string} props.message - 错误消息
 * @param {string} props.type - 错误类型 (network, validation, server, auth, unknown, locked)
 * @param {string} props.className - 额外的CSS类名
 * @param {Function} props.onRetry - 重试回调函数
 * @param {boolean} props.showIcon - 是否显示图标
 * @param {number} props.remainingAttempts - 剩余尝试次数
 * @param {number} props.lockoutTime - 锁定时间（分钟）
 * @param {boolean} props.simple - 是否使用简单样式（红色小字）
 * @returns {JSX.Element} 错误消息组件
 */
const ErrorMessage = ({ 
  message, 
  type = 'unknown', 
  className = '', 
  onRetry,
  showIcon = true,
  remainingAttempts,
  lockoutTime,
  simple = false
}) => {
  // 根据错误类型获取图标和建议
  const getErrorDetails = () => {
    switch (type) {
      case 'network':
        return {
          icon: '🌐',
          suggestion: '请检查您的网络连接或稍后再试'
        };
      case 'validation':
        return {
          icon: '⚠️',
          suggestion: '请检查输入内容是否符合要求'
        };
      case 'server':
        return {
          icon: '🖥️',
          suggestion: '服务器暂时不可用，请稍后再试'
        };
      case 'auth':
        return {
          icon: '🔒',
          suggestion: remainingAttempts ? `还剩${remainingAttempts}次尝试机会` : '请检查您的账号和密码'
        };
      case 'timeout':
        return {
          icon: '⏱️',
          suggestion: '请求超时，请检查网络连接或稍后再试'
        };
      case 'locked':
        return {
          icon: '🔐',
          suggestion: `账户已被临时锁定，请${lockoutTime}分钟后再试`
        };
      default:
        return {
          icon: '❓',
          suggestion: '发生未知错误，请稍后再试'
        };
    }
  };

  const { icon, suggestion } = getErrorDetails();

  // 如果使用简单样式，只显示红色小字错误信息
  if (simple) {
    return (
      <div className={`simple-error-message ${className}`}>
        {message}
        {remainingAttempts && <span className="remaining-attempts">（还剩{remainingAttempts}次尝试机会）</span>}
        {lockoutTime && <span className="lockout-time">（请{lockoutTime}分钟后再试）</span>}
      </div>
    );
  }

  // 否则使用原来的样式
  return (
    <div className={`error-message ${type} ${className}`}>
      {showIcon && <span className="error-icon">{icon}</span>}
      <div className="error-content">
        <p className="error-text">{message}</p>
        <p className="error-suggestion">{suggestion}</p>
        {onRetry && (
          <button className="error-retry" onClick={onRetry}>
            重试
          </button>
        )}
      </div>
    </div>
  );
};

export default ErrorMessage; 