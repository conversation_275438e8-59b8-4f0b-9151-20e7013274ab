/* 订阅弹窗样式 */
.subscribe-modal {
  width: 400px;
  max-width: 90vw;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.subscribe-modal .modal-header {
  border-bottom: none;
  padding: 12px;
}

.subscribe-modal .modal-body {
  padding: 24px;
}

.subscribe-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.subscribe-option-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-normal);
  background: var(--bg-secondary);
  width: 100%;
}

.subscribe-option-btn span {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.subscribe-option-btn small {
  font-size: 14px;
  color: var(--text-secondary);
}

.subscribe-option-btn.trial {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.subscribe-option-btn.trial:hover {
  background: var(--bg-active);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* 暗色主题下的样式 */
[data-theme="dark"] .subscribe-option-btn.trial {
  background: var(--bg-secondary);
  border-color: var(--border-light);
}

[data-theme="dark"] .subscribe-option-btn.trial:hover {
  background: var(--bg-active);
}

/* premium按钮样式 */
.subscribe-option-btn.premium {
  background: var(--brand-gradient);
  color: var(--text-inverse);
}

.subscribe-option-btn.premium span {
  color: var(--text-inverse);
}

.subscribe-option-btn.premium small {
  color: rgba(255, 255, 255, 0.8);
}

/* 暗色主题下的样式 */
[data-theme="dark"] .subscribe-option-btn.premium {
  background: var(--brand-gradient);
}

[data-theme="dark"] .subscribe-option-btn.premium span {
  color: var(--text-inverse);
  text-shadow: none;
}

[data-theme="dark"] .subscribe-option-btn.premium small {
  color: rgba(51, 51, 51, 0.9);
}

.subscribe-option-btn.premium:hover {
  transform: translateY(-2px);
  filter: brightness(1.1);
  box-shadow: var(--shadow-md);
}

/* 订阅弹窗移动端适配 */
@media (max-width: 480px) {
  .subscribe-modal {
    width: 90%;
    margin: 0 auto;
  }

  .subscribe-option-btn {
    padding: 20px 16px;
  }

  .subscribe-option-btn span {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .subscribe-option-btn small {
    font-size: 13px;
  }

  .subscribe-options {
    gap: 12px;
    padding: 0 12px;
  }

  .subscribe-modal .modal-body {
    padding: 16px 0 20px;
  }
} 