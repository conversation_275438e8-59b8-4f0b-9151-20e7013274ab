import React from 'react';
import PropTypes from 'prop-types';
import './index.css';

const TipsPanel = ({
  tipContent,
}) => {
  return (
    <div className="tips-setting">
      <div className="tips-content">
        <div className="tips-label">
          <span>提示</span>
        </div>
        <div className="tips-area">
          <div className="tips-text">
            {React.isValidElement(tipContent) ? (
              tipContent
            ) : (
              tipContent
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

TipsPanel.propTypes = {
  tipContent: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.element,
    PropTypes.arrayOf(PropTypes.element)
  ]).isRequired,
};

export default TipsPanel; 