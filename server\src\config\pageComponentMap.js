/**
 * 页面-组件映射常量
 * 定义每个页面类型所需的组件
 * 
 * 此配置文件是集中式定义，用于替代原先在多个文件中的重复定义
 * 所有对此映射表的修改只需在此文件中进行即可
 */

const PAGE_COMPONENT_MAP = {
  'background': ['uploadBox', 'foregroundPanel', 'scenePanel', 'randomSeedSelector', 'quantityPanel'],
  'fabric': ['uploadBox', 'clothingPanel', 'uploadBox_Model', 'MaskDescriptionPanel', 'fabricPanel', 'randomSeedSelector', 'quantityPanel'],
  'fashion': ['uploadBox', 'clothingPanel', 'textDescriptionPanel', 'weightPanel','randomSeedSelector', 'imageSizeSelector', 'quantityPanel'],
  'recolor': ['uploadBox', 'clothingPanel', 'MaskDescriptionPanel', 'colorPanel', 'colorAdjustPanel', 'typeSelector', 'quantityPanel'],
  'try-on': ['uploadBox', 'clothingPanel', 'uploadBox_Model', 'modelMaskPanel', 'MaskDescriptionPanel', 'randomSeedSelector', 'quantityPanel'],
  'virtual': ['textDescriptionPanel', 'randomSeedSelector'],
  'inspiration': ['clothingAttributesPanel', 'randomSeedSelector', 'imageSizeSelector', 'quantityPanel'],
  'optimize': ['uploadBox_Model', 'modelMaskPanel', 'textDescriptionPanel', 'randomSeedSelector', 'quantityPanel'],
  'trending': ['uploadBox', 'patternPanel', 'uploadBox_Model', 'printingPanel', 'textDescriptionPanel', 'modelNobodyPanel', 'weightPanel', 'randomSeedSelector', 'quantityPanel'],
  'extend': ['uploadBox', 'sourceImagePanel', 'extendSize','randomSeedSelector', 'quantityPanel'],
  'extract': ['uploadBox', 'sourceImagePanel', 'imageExtractionOptions'],
  'matting': ['uploadBox', 'MaskDescriptionPanel', 'sourceImagePanel'],
  'upscale': ['uploadBox', 'sourceImagePanel', 'magnificationSize']
};

module.exports = PAGE_COMPONENT_MAP; 