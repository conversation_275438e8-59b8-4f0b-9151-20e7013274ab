.announcement-carousel {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
}

.announcement-card {
  margin: 0 16px;
  border-radius: 8px;
  overflow: hidden;
}

.announcement-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #1890ff;
}

.announcement-content {
  margin-bottom: 16px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.announcement-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.announcement-image {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}

@media (max-width: 768px) {
  .announcement-carousel {
    padding: 8px;
  }
  
  .announcement-card {
    margin: 0 8px;
  }
  
  .announcement-image {
    width: 100%;
    height: auto;
  }
} 