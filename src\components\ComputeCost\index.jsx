import React from 'react';
import { getBillingCost } from '../../config/billing';

/**
 * 算力值显示组件
 * @param {Object} props
 * @param {string} props.featureName - 功能名称
 * @param {string} props.quantity - 数量
 * @param {string} props.className - 自定义类名
 * @param {string} props.subType - 子类型
 */
const ComputeCost = ({ featureName, quantity = 1, className = '', subType }) => {
  const cost = getBillingCost(featureName, subType) * quantity;
  if (!featureName || cost <= 0) return null;
  return (
    <span className={`compute-cost ${className}`}>
      {cost}
    </span>
  );
};

export default ComputeCost; 