declare module 'masonry-layout' {
  interface MasonryOptions {
    itemSelector: string;
    columnWidth?: string | number;
    percentPosition?: boolean;
    gutter?: number;
    horizontalOrder?: boolean;
    fitWidth?: boolean;
    transitionDuration?: string;
    initLayout?: boolean;
  }

  class Masonry {
    constructor(element: Element | string, options?: MasonryOptions);
    layout(): void;
    reloadItems(): void;
    destroy(): void;
  }

  export = Masonry;
} 