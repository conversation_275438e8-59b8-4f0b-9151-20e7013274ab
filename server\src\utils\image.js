const fs = require('fs');
const path = require('path');
const { PNG } = require('pngjs'); // Import PNG from pngjs

/**
 * Removes specific text metadata (like ComfyUI workflow/prompt) from a PNG image.
 * This function reads the PNG, filters its text chunks, and then writes a new PNG.
 * @param {string|Buffer} input - The path to the input PNG file or Buffer.
 * @param {string|Buffer} output - The path where the cleaned PNG file will be saved or Buffer.
 * @returns {Promise<boolean|Buffer>} - True if successful (file mode), Buffer if successful (buffer mode), false otherwise.
 */
async function removeComfyUIMetadata(input, output) {
    // 检查输入类型
    if (Buffer.isBuffer(input)) {
        // Buffer模式
        return removeComfyUIMetadataFromBuffer(input);
    } else {
        // 文件路径模式
        return removeComfyUIMetadataFromFile(input, output);
    }
}

/**
 * Removes ComfyUI metadata from PNG Buffer
 * @param {Buffer} inputBuffer - The input PNG buffer
 * @returns {Promise<Buffer>} - Returns cleaned PNG buffer
 */
async function removeComfyUIMetadataFromBuffer(inputBuffer) {
    const os = require('os');
    const path = require('path');
    
    // 创建临时文件路径
    const tempDir = os.tmpdir();
    const tempInputPath = path.join(tempDir, `temp_input_${Date.now()}.png`);
    const tempOutputPath = path.join(tempDir, `temp_output_${Date.now()}.png`);
    
    try {
        // 将Buffer写入临时输入文件
        fs.writeFileSync(tempInputPath, inputBuffer);
        
        // 调用文件处理方法
        const success = await removeComfyUIMetadataFromFile(tempInputPath, tempOutputPath);
        
        if (success) {
            // 读取处理后的文件并返回Buffer
            const cleanedBuffer = fs.readFileSync(tempOutputPath);
            console.log(`Buffer元数据移除完成，处理后的Buffer大小: ${cleanedBuffer.length} bytes`);
            return cleanedBuffer;
        } else {
            console.log('文件处理失败，返回原始Buffer');
            return inputBuffer;
        }
    } catch (error) {
        console.error(`Buffer处理错误: ${error.message}`);
        return inputBuffer;
    } finally {
        // 清理临时文件
        try {
            if (fs.existsSync(tempInputPath)) {
                fs.unlinkSync(tempInputPath);
            }
            if (fs.existsSync(tempOutputPath)) {
                fs.unlinkSync(tempOutputPath);
            }
        } catch (cleanupError) {
            console.error('清理临时文件失败:', cleanupError.message);
        }
    }
}

/**
 * Removes ComfyUI metadata from PNG file
 * @param {string} inputPath - The path to the input PNG file
 * @param {string} outputPath - The path where the cleaned PNG file will be saved
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
async function removeComfyUIMetadataFromFile(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
        fs.createReadStream(inputPath)
            .pipe(new PNG({
                filterType: 4, // Important for better compression/quality
                // Note: pngjs automatically parses tEXt and zTXt chunks into img.text
            }))
            .on('parsed', function() {
                // 'this' refers to the PNG object
                const originalText = this.text || {};
                const newText = {};
                let metadataRemoved = false;

                // Keys that ComfyUI typically uses for workflow/prompt
                const comfyuiKeys = ['parameters', 'workflow', 'ComfyUI Workflow', 'comf', 'prompt'];

                for (const key in originalText) {
                    if (originalText.hasOwnProperty(key)) {
                        if (comfyuiKeys.includes(key)) {
                            console.log(`Removing ComfyUI metadata key: ${key}`);
                            metadataRemoved = true;
                        } else {
                            // Keep other metadata
                            newText[key] = originalText[key];
                        }
                    }
                }

                // Assign the filtered text object back
                // This is crucial: by modifying this.text, pngjs will write only these chunks.
                this.text = newText;

                // Write the modified PNG to the output file
                this.pack().pipe(fs.createWriteStream(outputPath))
                    .on('finish', () => {
                        console.log(`Successfully re-written image to ${outputPath}. Metadata removal status: ${metadataRemoved ? 'SUCCESS' : 'NO_COMFY_METADATA_FOUND'}`);
                        resolve(true);
                    })
                    .on('error', (err) => {
                        console.error(`Error writing PNG: ${err.message}`);
                        reject(false);
                    });
            })
            .on('error', (err) => {
                console.error(`Error parsing PNG: ${err.message}`);
                reject(false);
            });
    });
}

/**
 * Verifies if a PNG image contains potential ComfyUI workflow information.
 * @param {string} imagePath - The path to the image file.
 * @returns {Promise<boolean>} - True if metadata is likely present, false otherwise.
 */
async function verifyComfyUIMetadata(imagePath) {
    return new Promise((resolve, reject) => {
        fs.createReadStream(imagePath)
            .pipe(new PNG({
                filterType: 4 // Only for parsing, outputting is handled by pack()
            }))
            .on('parsed', function() {
                const textInfo = this.text || {};
                let hasComfyData = false;
                const comfyDataFound = {};

                const comfyuiKeys = ['parameters', 'workflow', 'ComfyUI Workflow', 'comf', 'prompt'];

                console.log("--- Current PNG Text Metadata ---");
                if (Object.keys(textInfo).length === 0) {
                    console.log("No text metadata found.");
                } else {
                    for (const key in textInfo) {
                        if (textInfo.hasOwnProperty(key)) {
                            const value = textInfo[key];
                            console.log(`  Key: "${key}"`);
                            console.log(`    Value (first 100 chars): ${typeof value === 'string' ? value.substring(0, 100) + (value.length > 100 ? '...' : '') : value}`);

                            if (comfyuiKeys.includes(key)) {
                                comfyDataFound[key] = value;
                                hasComfyData = true;
                            }
                            // Special check for 'prompt' if it contains JSON-like structure
                            if (key === 'prompt' && typeof value === 'string' && (value.includes('{ "nodes":') || value.includes('{ "prompt":'))) {
                                comfyDataFound[key] = value;
                                hasComfyData = true;
                            }
                        }
                    }
                }
                console.log("---------------------------------");

                if (hasComfyData) {
                    console.log(`图片包含ComfyUI工作流信息:`);
                    for (const key in comfyDataFound) {
                        const value = comfyDataFound[key];
                        console.log(`  - ${key} 数据: ${typeof value === 'string' ? value.substring(0, 100) + '...' : value}`);
                    }
                    resolve(true);
                } else {
                    console.log("未检测到ComfyUI工作流信息。");
                    resolve(false);
                }
            })
            .on('error', (err) => {
                console.error(`验证错误: ${err.message}`);
                reject(false);
            });
    });
}

async function main() {
    let inputPng = "p4.png"; // Make sure this is a PNG file from ComfyUI
    let outputPng = "cleaned_output_no_comfy.png";
    console.log(process.argv);
    if (process.argv.length > 2) {
        inputPng = process.argv[2];
        if (process.argv.length > 3) {
            outputPng = process.argv[3];
        }
    }

    if (!fs.existsSync(inputPng)) {
        console.error(`Error: Input file '${inputPng}' not found.`);
        process.exit(1);
    }

    // Ensure the input file is a PNG, as ComfyUI metadata is for PNGs
    if (path.extname(inputPng).toLowerCase() !== '.png') {
        console.error(`Error: Input file '${inputPng}' is not a PNG. ComfyUI metadata is typically in PNGs.`);
        console.error("Please provide a ComfyUI-generated PNG file.");
        process.exit(1);
    }

    console.log(`Processing file: ${inputPng}`);

    console.log("\n[Verifying Original Image]");
    let hasMetadata = await verifyComfyUIMetadata(inputPng);

    if (!hasMetadata) {
        console.log("Warning: Input image did not detect specific ComfyUI workflow information. Proceeding anyway.");
        // In Node.js, we don't have interactive prompt easily like Python's input().
        // For CLI, you might consider a library like 'readline' if truly needed.
        // For now, we'll just proceed without prompting.
    }

    console.log("\n[Processing]");
    const success = await removeComfyUIMetadata(inputPng, outputPng);

    if (success) {
        console.log("\n[Verifying Result]");
        await verifyComfyUIMetadata(outputPng);
        console.log(`\nProcessing complete! Cleaned image saved to: ${outputPng}`);
        console.log("Loading this image in ComfyUI should ideally not show workflow information.");
    } else {
        console.log("\nProcessing failed or could not fully remove ComfyUI metadata.");
    }
}

// 导出函数供其他模块使用
module.exports = {
    removeComfyUIMetadata,
    removeComfyUIMetadataFromBuffer,
    removeComfyUIMetadataFromFile,
    verifyComfyUIMetadata
};

// 如果直接运行此文件，则执行main函数
if (require.main === module) {
    main();
}

