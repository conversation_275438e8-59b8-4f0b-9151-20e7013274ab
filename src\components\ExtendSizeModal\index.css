/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/tabs.css';
@import '../../styles/buttons.css';
@import '../../styles/close-buttons.css';

/* 扩图比例与尺寸弹窗包装器样式 */
.extend-size-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100dvh;
  z-index: 99999; /* 大幅提高z-index，确保在所有内容之上，包括移动端导航栏 */
  pointer-events: auto; /* 改为auto，使其可以接收点击事件，实现点击外部关闭功能 */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 扩图比例与尺寸弹窗样式 */
.extend-size-modal {
  position: fixed;
  width: 780px; /* 修改：将宽度从480px增加到780px，为右侧预览区域腾出空间 */
  max-width: 90vw;
  max-height: none; /* 修改：移除最大高度限制 */
  background: var(--bg-primary);
  pointer-events: auto; /* 保持弹窗本身可点击 */
  box-shadow: var(--shadow-md);
  border-radius: 12px;
  user-select: none; /* 防止拖动时选中文本 */
  z-index: 100000; /* 确保弹窗内容在包装器之上 */
  /* 位置由传入的style决定，不在这里设置默认位置 */
  display: flex; /* 添加：使用flex布局，便于分割左右两个区域 */
  flex-direction: column; /* 添加：垂直方向的flex布局 */
}

/* 拖动状态下的样式 */
.extend-size-modal.dragging {
  user-select: none;
  pointer-events: auto;
}

/* 修复弹窗拖动时手型光标 */
.extend-size-modal.dragging .modal-header {
  cursor: grabbing !important;
}

/* 标题区域可拖动的提示 */
.extend-size-modal .modal-header:hover {
  background: rgba(0, 0, 0, 0.02);
}

[data-theme="dark"] .extend-size-modal .modal-header:hover {
  background: rgba(255, 255, 255, 0.02);
}

/* 拖动时禁止全局文本选择 */
body.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 添加：弹窗内容容器，用于横向分割左右两个区域 */
.extend-size-modal-content {
  display: flex;
  flex-direction: row;
  flex: 1;
  height: auto; /* 修改：从固定高度改为自适应高度 */
  overflow: visible; /* 修改：确保内容可见 */
  align-items: flex-start;
}

/* 添加：左侧设置区域样式 */
.extend-size-left {
  width: 480px;
  border-right: 1px solid var(--border-light); /* 添加竖向分隔线 */
  overflow: visible; /* 修改：从auto改为visible，移除滚动条 */
}

/* 添加：右侧预览区域样式 */
.extend-size-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  overflow: hidden;
}

/* 添加：画布预览样式 */
.canvas-preview {
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  display: flex; /* 添加：使用flex布局居中显示内容 */
  justify-content: center; /* 添加：水平居中 */
  align-items: center; /* 添加：垂直居中 */
  transform: translateZ(0); /* 强制GPU加速 */
  will-change: contents; /* 性能优化，提示浏览器内容会变化 */
}

/* 居中指示线基础样式 */
.center-guide {
  position: absolute;
  background-color: var(--brand-primary);
  pointer-events: none;
  z-index: 10;
}

/* 垂直居中指示线 */
.center-guide-vertical {
  width: 1px;
  height: 100%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

/* 水平居中指示线 */
.center-guide-horizontal {
  width: 100%;
  height: 1px;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

/* 添加：画布中的图片容器样式 */
.canvas-image-container {
  position: absolute;
  cursor: grab; /* 指示可拖动，使用更现代的grab光标 */
  user-select: none; /* 防止选中内容 */
  transition: transform 0.1s ease; /* 平滑移动效果 */
  will-change: transform; /* 性能优化 */
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1); /* 添加微妙的轮廓 */
  border-radius: 2px; /* 轻微的圆角 */
  backface-visibility: hidden; /* 防止闪烁 */
  perspective: 1000; /* 创建3D渲染上下文，提高GPU加速效果 */
  transform: translateZ(0); /* 强制GPU加速 */
}

/* 添加：拖拽时的样式 */
.canvas-image-container.dragging {
  cursor: grabbing; /* 拖拽中的光标样式 */
  transition: none; /* 拖拽时移除过渡效果 */
  z-index: 2; /* 提高层级 */
  box-shadow: 0 0 0 2px var(--brand-primary); /* 拖拽时的高亮边框 */
}

/* 添加：画布中图片容器悬停时的样式 */
.canvas-image-container:hover {
  box-shadow: 0 0 0 2px var(--brand-primary-light); /* 使用品牌主题变量配色替换固定蓝色 */
}

/* 添加：画布中的图片样式 */
.canvas-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  pointer-events: none; /* 防止图片自身接收指针事件 */
  user-select: none; /* 防止选中 */
  -webkit-user-drag: none; /* 防止拖动 */
  backface-visibility: hidden; /* 防止闪烁 */
  transform: translateZ(0); /* 强制GPU加速 */
}

/* 添加：画布提示文字样式 */
.canvas-hint {
  display: block;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--brand-primary);
  text-align: center;
  font-weight: 500;
}

/* 添加：画布背景样式，使用黑白格子背景 */
.canvas-preview-bg {
  background-image: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                    linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  background-color: var(--bg-primary);
  width: 100%;
  height: 100%;
}

/* 添加：画布尺寸信息样式 */
.canvas-dimensions {
  margin-top: var(--spacing-md);
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 暗色主题下添加白色边框 */
[data-theme="dark"] .extend-size-modal {
  background: var(--bg-primary);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
}

/* 标签页样式覆盖，与ColorPickerModal保持一致 */
.extend-size-modal .modal-tabs {
  width: auto !important;
  margin-left: 20px !important;
  flex-shrink: 0;
}

.extend-size-modal .tab-group {
  width: auto !important;
  margin-left: 20px !important;
  flex-shrink: 0;
}

.extend-size-modal .tab-btn {
  flex-shrink: 0;
  min-width: auto;
}

/* 修改modal-body样式，与ColorPickerModal保持一致 */
.extend-size-modal .modal-body {
  padding-top: var(--spacing-md);
  margin-top: 0;
}

/* 模态框内容样式调整 */
.extend-size-content {
  padding: 0 var(--spacing-sx);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* 标签页内容样式 */
.extend-size-modal .tab-content {
  padding: 0 var(--spacing-sx);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xxs);
  min-height: 150px;
}

.extend-size-modal .modal-footer {
  position: relative;
  height: 60px;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-md);
  border-top: 1px solid var(--border-lighter);
  width: 100%; /* 添加：确保footer宽度为100% */
}

/* 标签页内容样式 */
.scale-tab, .custom-tab {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xxs); /* 减小标签页内各元素的间距 */
}

/* 尺寸信息容器 */
.size-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding: 0; /* 移除内边距，让子元素控制 */
  position: relative; /* 为等号定位添加相对定位 */
}

.original-size, .upscaled-size {
  display: flex;
  flex-direction: column;
  gap: 2px; /* 减小内部间距 */
  padding: 6px 8px 6px 16px; /* 减小上下内边距 */
}

/* 尺寸相等时显示的等号 */
.size-equality-sign {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.size-equality-sign span {
  font-size: 22px;
  color: var(--brand-primary); /* 使用网站品牌主色 */
}

/* 警告提示的样式 */
.size-limit-notice.warning {
  color: var(--brand-primary);
  font-weight: 500;
}

/* 尺寸小于原图时的警告样式 */
.size-limit-notice.size-limit-exceeded {
  color: var(--brand-primary);
  font-weight: 500;
}

.size-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.size-value {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
}

/* 扩图比例按钮组 */
.scale-buttons {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  margin: 0 0 12px 16px; /* 减少底部间距，因为现在有多行按钮 */
  width: calc(100% - 32px); /* 考虑左右边距 */
}

.scale-btn {
  flex: 1;
  padding: 10px 0;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-normal);
  text-align: center;
}

.scale-btn:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.scale-btn.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 禁用状态的按钮样式 - 保持与启用状态相同 */
.scale-btn:disabled {
  opacity: 1;
  cursor: not-allowed;
  border-color: var(--border-light);
  background: var(--bg-primary);
  color: var(--text-secondary);
}

.scale-btn:disabled:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.scale-btn:disabled.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 恢复禁用输入框的样式 */
.size-input:disabled {
  opacity: 1;
  cursor: not-allowed;
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-light);
}

.size-input:disabled:hover {
  border-color: var(--border-hover);
}

/* 滑块禁用样式 */
.slider-input:disabled {
  opacity: 1;
  cursor: not-allowed;
}

.slider-input:disabled::-webkit-slider-thumb {
  background: var(--bg-primary);
  border-color: var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: not-allowed;
  transform: scale(1);
}

.slider-input:disabled::-moz-range-thumb {
  background: var(--bg-primary);
  border-color: var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: not-allowed;
  transform: scale(1);
}

/* 滑块样式 */
.scale-slider {
  position: relative;
  margin: var(--spacing-md) 0 0 16px; /* 添加左边距与原图尺寸对齐 */
  padding: 0;
  width: calc(100% - 32px); /* 考虑左右边距 */
}

.slider-track {
  position: relative;
  height: 2px;
  background: var(--border-light);
  border-radius: var(--radius-sm);
  margin: 16px 0;
  width: 100%;
}

/* 移除禁用样式差异 */
.slider-input:disabled ~ .slider-track {
  background: var(--border-light);
  opacity: 1;
}

.slider-input:disabled ~ .slider-track .slider-fill {
  background: var(--brand-gradient);
  opacity: 1;
}

.slider-fill {
  position: absolute;
  height: 100%;
  background: var(--brand-gradient);
  border-radius: var(--radius-sm);
  transition: width 0s cubic-bezier(0.4, 0, 0.2, 1);
}

.slider-input {
  position: absolute;
  top: 50%;
  left: 0%;
  width: 100%;
  height: 20px;
  transform: translateY(-50%);
  -webkit-appearance: none;
  background: transparent;
  cursor: pointer;
  margin: 0;
  padding: 0;
}

.slider-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  border: 3px solid var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  z-index: 1;
}

.slider-input::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  border: 3px solid var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: pointer;
  transition: var(--transition-normal);
}

.slider-input::-webkit-slider-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
}

.slider-input::-moz-range-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
}

.scale-value {
  position: absolute;
  right: 0;
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
  display: inline-block;
}

.custom-scale-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  margin-left: 0px; /* 添加左边距与其他内容对齐 */
  width: calc(100%); /* 考虑左右边距 */
}

.custom-scale-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-left: 0; /* 移除原有的左边距 */
}

.custom-scale-header .scale-value {
  position: static;
  font-size: var(--font-size-md);
  font-weight: 500;
  margin-right: var(--spacing-sm);
}

/* 未上传图片时的警告样式 */
.no-image-warning {
  color: var(--text-secondary);
  background: var(--bg-warning-light);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  text-align: center;
  margin: var(--spacing-md) 0;
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.size-input:disabled {
  background: var(--bg-lighter);
  color: var(--text-disabled);
  cursor: not-allowed;
}

/* 修改未选中标签页的样式 */


/* 调整注意说明文字样式 */
.size-limit-notice {
  font-size: var(--font-size-sm);
  color: #888888; /* 明亮主题下的深灰色，与未激活标签一致 */
  margin-top: 28px; /* 进一步增加与上方输入框的间距 */
  margin-left: 16px; /* 默认左对齐 */
  text-align: left;
  width: calc(100% - 32px); /* 考虑左右边距 */
}

/* 暗黑主题下的样式 */
:root[data-theme='dark'] .size-limit-notice {
  color: #aaaaaa; /* 暗黑主题下的浅灰色，与未激活标签一致 */
}

/* 自定义尺寸输入相关样式 */
.custom-size-inputs {
  margin-top: -4px; /* 添加负上边距进一步缩小空隙 */
  margin-bottom: 8px; /* 增加下边距 */
  padding: 0; /* 移除所有内边距 */
  width: 100%;
}

/* 尺寸输入容器样式 */
.size-input-container {
  display: flex;
  align-items: center;
  margin: 4px 0 4px 16px; /* 左边距与原图尺寸文本对齐 */
  width: calc(100% - 32px); /* 考虑左右边距 */
  justify-content: flex-start;
  gap: var(--spacing-md); /* 增加输入框之间的间距 */
}

/* 尺寸输入组样式 */
.size-input-group {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
  min-width: 120px; /* 增加最小宽度 */
}

/* 尺寸标签样式 */
.size-input-group .size-label {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin-right: 4px;
  white-space: nowrap;
  flex-shrink: 0;
  width: 24px;
  text-align: left;
  position: static;
  transform: none;
}

/* 尺寸单位样式 */
.size-unit {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin-left: 8px;
  white-space: nowrap;
  flex-shrink: 0;
  text-transform: lowercase; /* 确保px单位为小写 */
  position: static;
  transform: none;
}

.size-input {
  width: 75px; /* 增加宽度 */
  flex: 0 0 auto;
  height: 32px; /* 增加高度匹配字号 */
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  padding: 0 4px;
  font-size: var(--font-size-md); /* 从sm改为md，与原图尺寸文本一致 */
  color: var(--text-primary);
  background: var(--bg-primary);
  text-align: center;
  min-width: 65px; /* 增加最小宽度 */
  /* 移除number类型输入框的上下箭头 */
  -webkit-appearance: none;
  -moz-appearance: textfield;
  appearance: textfield;
  /* 过渡效果 */
  transition: var(--transition-normal);
}

.size-input::-webkit-outer-spin-button,
.size-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.size-input:hover {
  border-color: var(--brand-primary);
}

.size-input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(255, 60, 106, 0.1);
}

[data-theme="dark"] .size-input:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

/* 修改长宽比锁定图标样式 - 去除按钮样式，只显示图标 */
.aspect-ratio-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  width: 28px; /* 增加宽度 */
  height: 28px; /* 增加高度 */
  flex-shrink: 0;
  border-radius: 0;
}

.aspect-ratio-icon svg {
  width: 16px;
  height: 16px;
}

/* 长宽比锁定/解锁按钮样式 */
.aspect-ratio-btn {
  width: 28px;
  height: 28px;
  min-width: 28px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  background: var(--bg-primary);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  padding: 0;
  flex-shrink: 0;
  margin: 0 8px;
}

.aspect-ratio-btn:hover {
  color: var(--brand-primary);
  border-color: var(--brand-primary);
}

.aspect-ratio-btn svg {
  width: 16px;
  height: 16px;
}

.aspect-ratio-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 扩图标签页的说明文字左对齐 */
.scale-tab .size-limit-notice {
  margin-top: var(--spacing-md)+30px;
  margin-left: 16px; /* 与输入区域左对齐 */
  text-align: left;
  width: calc(100% - 32px); /* 考虑左右边距 */
}

/* 平台要求尺寸标签页样式 */
.platform-tab {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.platform-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.platform-section h4 {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin: 0 0 4px 16px;
  font-weight: 500;
}

.platform-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 6px;
  margin: 0 16px;
}

.platform-text {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  text-align: center;
  align-items: center;
}

.platform-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 6px 8px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  height: 52px;
}

.platform-btn:hover {
  border-color: var(--brand-primary);
}

.platform-btn.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
}

.platform-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 平台图标基础样式 */
.platform-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
  flex-shrink: 0;
}

.platform-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 平台名称和尺寸的样式 */
.platform-name {
  font-size: 13px;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 4px;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 平台尺寸 */
.platform-size {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 添加：边缘距离显示样式 */
.edge-distances-display {
  display: flex;
  flex-direction: column;
  gap: 4px; /* 缩小行间距 */
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-size: 12px; /* 恢复原来的字体大小 */
}

.distances-row {
  display: flex;
  justify-content: center; /* 中心对齐 */
  width: 100%;
}

.distance-item {
  display: flex;
  align-items: center;
  margin: 0 12px; /* 增加左右间距 */
}

.distance-label {
  font-weight: 500;
  margin-right: 4px;
  color: var(--text-secondary);
  font-size: 12px; /* 恢复原来的字体大小 */
}

/* 为边缘距离中的size-value覆盖默认字体大小 */
.edge-distances-display .size-value {
  font-size: 12px; /* 使用与标签相同的字体大小 */
  font-weight: 500;
}

/* 移除不需要的样式 */
.distance-value {
  display: none;
}

/* 移除实际尺寸边距值样式，因为不再需要 */
.real-distance-value {
  display: none;
}

/* 添加负值边距的样式 */
.edge-distances-display .negative-value {
  color: var(--brand-primary);
  font-weight: 500;
}

/* 添加移动端适配样式 */
@media (max-width: 768px) {
  .extend-size-modal {
    width: 95%;
    max-width: none;
    flex-direction: column;
  }

  .extend-size-modal-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .extend-size-left {
    max-height: 36vh;
    overflow-y: auto;
  }
  .extend-size-preview {
    flex: 1 1 0;
    min-height: 180px;
    overflow-y: auto;
  }

  .extend-size-modal .platform-btn {
    height: 40px !important;
    padding: 4px 6px !important;
  }
  .extend-size-modal .platform-icon {
    width: 16px !important;
    height: 16px !important;
    margin-right: 4px !important;
  }
  .extend-size-modal .platform-name {
    font-size: 12px !important;
    margin-bottom: 2px !important;
  }
  .extend-size-modal .platform-size {
    font-size: 11px !important;
  }
  .extend-size-modal .size-value,
  .extend-size-modal .size-label {
    font-size: 13px !important;
  }
  .extend-size-modal .edge-distances-display {
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: center;
    gap: 8px 16px;
  }
  .extend-size-modal .distances-row {
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: center;
    width: auto !important;
    margin: 0 !important;
  }
  .extend-size-modal .distance-item {
    margin: 0 8px !important;
    min-width: 60px;
  }
}

@media (max-width: 480px) {
  .extend-size-modal {
    width: 98%;
  }
}

@media (max-width: 360px) {
  .extend-size-modal {
    width: 100%;
    border-radius: 0;
  }
}

/* 真实移动设备适配 */
@media (hover: none) and (pointer: coarse) {
  .extend-size-wrapper {
    padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  }

  .extend-size-modal {
    max-height: calc(100dvh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  }

  /* 移动端标签页样式优化，与ColorPickerModal保持一致 */
  .extend-size-modal .modal-tabs {
    width: auto !important;
    margin-left: 20px !important;
    flex-shrink: 0;
  }

  .extend-size-modal .tab-group {
    width: auto !important;
    margin-left: 20px !important;
    flex-shrink: 0;
  }

  .extend-size-modal .tab-btn {
    flex-shrink: 0;
    min-width: auto;
  }
}

/* 横屏模式适配 */
@media (hover: none) and (pointer: coarse) and (orientation: landscape) {
  .extend-size-modal {
    height: calc(100dvh - 20px);
  }
}

/* --- 标签页对齐修正 --- */





/* 兼容移动端的底部线宽度调整，保留原有媒体查询 */

@media (max-width: 768px) {
  .extend-size-modal-content {
    padding: 2px !important;
  }
  .extend-size-left {
    padding: 2px !important;
  }
  .extend-size-preview {
    padding: 2px !important;
  }
  .extend-size-modal .modal-body {
    padding: 2px !important;
  }
  .extend-size-modal .tab-content,
  .extend-size-modal .custom-tab,
  .extend-size-modal .scale-tab,
  .extend-size-modal .platform-tab {
    font-size: 13px !important;
    gap: 8px !important;
  }
  .extend-size-modal input,
  .extend-size-modal button:not(.medium-close-button):not(.tab-btn),
  .extend-size-modal select {
    font-size: 13px !important;
    padding: 4px 8px !important;
  }
}

.extend-size-modal .modal-footer button {
  min-width: 104px;
  width: auto;
  max-width: 100%;
}

@media (max-width: 768px) {
  .extend-size-modal .canvas-dimensions {
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;
    text-align: center;
  }
}
