# 任务工厂与模板使用指南

本指南详细介绍如何使用任务工厂和模板系统，为各功能页面创建标准化的任务数据结构。

## 目录

1. [核心概念介绍](#1-核心概念介绍)
2. [新格式任务数据结构](#2-新格式任务数据结构)
3. [页面改造指南](#3-页面改造指南)
4. [适配工具函数](#4-适配工具函数)
5. [调试与问题排查](#5-调试与问题排查)

## 1. 核心概念介绍

### 1.1 任务工厂模式

任务工厂模式解决了之前任务创建过程中存在的几个问题：
- 各页面创建任务的代码重复
- 不同页面的任务数据结构不统一
- 缺乏源图片信息的标准处理方式

### 1.2 关键模块

- **任务模板** (`baseTaskTemplate`): 定义任务的标准结构
- **任务工厂函数** (`createTaskData`): 创建标准格式的任务数据
- **任务服务** (`createPageTask`): 封装创建任务的完整流程
- **适配工具** (`taskAdapters`): 帮助处理数据格式转换和验证

## 2. 新格式任务数据结构

### 2.1 基本结构

```javascript
{
  id: 'TASK_ID',                // 任务ID
  userId: 'user123',            // 用户ID
  status: 'processing',         // 任务状态
  taskType: 'mattingbg',             // 任务类型 (原为ocbg)
  pageType: 'matting',          // 页面类型
  imageType: 'source',          // 图片类型
  serverFileName: '1234567890-abcdef.jpg',  // 服务器文件名
  settings: {
    // 源图片信息，统一格式
    source: {
      id: 'source-123',
      originalFile: 'photo.jpg',
      serverFileName: '1234567890-abcdef.jpg',
      url: 'blob:...',
      fileInfo: { width: 800, height: 600, size: 150000, type: 'image/jpeg' }
    },
    // 所有页面共用的设置
    common: {
      imageQuantity: 1
    },
    // 页面特定设置
    pageSpecific: {
      // 抠图页面特有设置
      generation: {
        count: 1,
        prompt: '将图片进行背景去除处理'
      }
    }
  }
}
```

### 2.2 与旧格式的兼容

新格式保留了对旧格式的向后兼容性：
- 旧格式中的 `sourcePanels` 仍然支持，但建议使用新的 `source` 字段
- 服务端会自动将旧格式升级到新格式
- 提供了转换工具函数帮助处理旧数据

## 3. 页面改造指南

### 3.1 改造步骤

1. **引入任务服务和工厂函数**

```javascript
import { createPageTask } from '../../services/task';
import { taskAdapters } from '../../utils/taskAdapters';
```

2. **使用工厂函数创建任务**

```javascript
const handleGenerate = async () => {
  // 获取必要的数据
  const sourcePanels = [...]; // 当前页面的源图片面板
  
  // 准备本地任务显示（如有需要）
  const localTaskId = generateId(ID_TYPES.TASK);
  
  // 定义页面特定设置
  const pageSpecificData = {
    generation: {
      count: 1,
      prompt: '页面特定的处理提示'
    }
  };
  
  // 调用创建任务服务
  const result = await createPageTask(
    'pageTypeName',       // 页面类型，如 'matting', 'background' 等
    'workflowType',       // 任务类型，如 'ocbg', 'clothing' 等
    {
      id: localTaskId,
      userId: currentUserId,
      imageQuantity: 1,
      panels: sourcePanels, // 传递面板信息，工厂函数会提取source
      status: 'processing'
    },
    pageSpecificData      // 页面特定设置
  );
  
  // 处理返回结果
  console.log('API返回结果:', result);
  
  // 更新UI状态...
};
```

### 3.2 参考抠图页面的实现

抠图页面已完成改造，可以作为参考模板：
- 位置：`src/pages/tools/matting/index.jsx`
- 关键函数：`handleGenerate()`

### 3.3 常见页面改造要点

不同页面类型的改造要点：

| 页面类型 | 任务类型参考值 | 页面特定设置字段 | 注意事项 |
|---------|-------------|----------------|---------|
| matting | 'mattingbg', 'clothing' | generation | 需处理多个面板 |
| background | 'background' | background, generation | 需处理前景和背景 |
| try-on | 'tryon' | model, clothes | 需处理模特和服装 |

## 4. 适配工具函数

### 4.1 可用的工具函数

任务适配器模块 (`src/utils/taskAdapters.js`) 提供了以下工具函数：

```javascript
// 将旧格式任务转换为新格式
const newTask = taskAdapters.convertTaskToNewFormat(oldTask);

// 验证任务格式
const { isValid, errors } = taskAdapters.validateTaskFormat(task);

// 从任务中提取源图片信息
const source = taskAdapters.extractSourceFromTask(task);

// 获取任务源图片URL
const imageUrl = taskAdapters.getTaskSourceImageUrl(task);
```

### 4.2 使用示例

```javascript
// 示例：在任务列表中显示任务源图片
const TaskItem = ({ task }) => {
  // 获取源图片URL
  const imageUrl = taskAdapters.getTaskSourceImageUrl(task);
  
  return (
    <div className="task-item">
      <img src={imageUrl} alt="源图片" />
      <div>{task.id}</div>
    </div>
  );
};

// 示例：验证并修复任务数据
const processTask = (task) => {
  const { isValid, errors } = taskAdapters.validateTaskFormat(task);
  
  if (!isValid) {
    console.warn('任务格式无效:', errors);
    // 转换为新格式
    return taskAdapters.convertTaskToNewFormat(task);
  }
  
  return task;
};
```

## 5. 调试与问题排查

### 5.1 常见问题

1. **图片无法显示**
   - 检查 `serverFileName` 是否正确设置
   - 验证 `source` 对象中的URL是否有效

2. **任务创建失败**
   - 检查是否提供了必要参数
   - 服务端日志会显示详细错误信息

3. **数据格式不一致**
   - 使用 `validateTaskFormat` 检查任务结构
   - 使用 `convertTaskToNewFormat` 转换为新格式

### 5.2 调试提示

开发环境下可以添加以下调试代码：

```javascript
// 详细记录调用参数
console.log('调用createPageTask:', {
  pageType: 'matting',
  taskType: workflowType,
  baseData: {
    id: localTaskId,
    panels: activePanels
  },
  pageSpecificData
});

// 验证任务格式
const checkResult = taskAdapters.validateTaskFormat(result);
console.log('任务格式验证:', checkResult);
```

### 5.3 开发环境特殊处理

在开发环境中：
- 用户ID统一使用 `'developer'`
- 服务器URL使用 `http://localhost:3002`
- 可以使用调试路由查看最近的任务

```
GET /api/tasks/debug/recent
```

---

## 结语

通过遵循本指南，你可以轻松地为各功能页面集成任务工厂系统，确保应用中的任务数据结构统一、可维护。如有任何问题，请参考示例代码或联系开发团队。 