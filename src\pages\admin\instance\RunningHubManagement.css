.running-hub-management {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.running-hub-management .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.running-hub-management .ant-tabs-content-holder {
  padding: 24px 0;
}

/* 统计卡片样式 */
.running-hub-management .ant-statistic {
  text-align: center;
}

.running-hub-management .ant-statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.running-hub-management .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 表格样式 */
.running-hub-management .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.running-hub-management .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 状态标签样式 */
.running-hub-management .status-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.running-hub-management .status-tag .anticon {
  font-size: 12px;
}

/* 操作按钮样式 */
.running-hub-management .action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.running-hub-management .action-buttons .ant-btn {
  border-radius: 4px;
}

/* 模态框样式 */
.running-hub-management .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.running-hub-management .ant-modal-title {
  font-size: 16px;
  font-weight: 600;
}

.running-hub-management .ant-modal-body {
  padding: 24px;
}

/* 表单样式 */
.running-hub-management .ant-form-item-label > label {
  font-weight: 500;
}

.running-hub-management .ant-input,
.running-hub-management .ant-select-selector {
  border-radius: 4px;
}

.running-hub-management .ant-input:focus,
.running-hub-management .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 抽屉样式 */
.running-hub-management .ant-drawer-header {
  border-bottom: 1px solid #f0f0f0;
}

.running-hub-management .ant-drawer-title {
  font-size: 16px;
  font-weight: 600;
}

.running-hub-management .ant-drawer-body {
  padding: 24px;
}

/* 描述列表样式 */
.running-hub-management .ant-descriptions-item-label {
  font-weight: 500;
  color: #666;
}

.running-hub-management .ant-descriptions-item-content {
  color: #333;
}

/* 进度条样式 */
.running-hub-management .ant-progress-text {
  font-size: 12px;
}

/* 空状态样式 */
.running-hub-management .ant-empty {
  padding: 40px 0;
}

.running-hub-management .ant-empty-description {
  color: #999;
}

/* 警告信息样式 */
.running-hub-management .ant-alert {
  border-radius: 6px;
}

.running-hub-management .ant-alert-message {
  font-weight: 500;
}

/* 标签页样式 */
.running-hub-management .ant-tabs-tab {
  font-weight: 500;
}

.running-hub-management .ant-tabs-tab-active {
  color: #1890ff;
}

.running-hub-management .ant-tabs-ink-bar {
  background: #1890ff;
}

/* 卡片网格样式 */
.running-hub-management .stats-grid {
  margin-bottom: 24px;
}

.running-hub-management .stats-grid .ant-col {
  margin-bottom: 16px;
}

/* 工具栏样式 */
.running-hub-management .toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.running-hub-management .toolbar-left {
  display: flex;
  gap: 8px;
}

.running-hub-management .toolbar-right {
  display: flex;
  gap: 8px;
}

/* 搜索框样式 */
.running-hub-management .search-form {
  background: #fff;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.running-hub-management .search-form .ant-form-item {
  margin-bottom: 0;
}

/* 详情面板样式 */
.running-hub-management .detail-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.running-hub-management .detail-panel-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
}

.running-hub-management .detail-panel-body {
  padding: 24px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .running-hub-management {
    padding: 16px;
  }
  
  .running-hub-management .ant-col {
    margin-bottom: 16px;
  }
  
  .running-hub-management .toolbar {
    flex-direction: column;
    gap: 16px;
  }
  
  .running-hub-management .toolbar-left,
  .running-hub-management .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .running-hub-management .action-buttons {
    justify-content: center;
  }
}

/* 加载状态样式 */
.running-hub-management .loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.running-hub-management .ant-spin-container {
  position: relative;
}

/* 成功状态样式 */
.running-hub-management .success-text {
  color: #52c41a;
}

/* 错误状态样式 */
.running-hub-management .error-text {
  color: #ff4d4f;
}

/* 警告状态样式 */
.running-hub-management .warning-text {
  color: #faad14;
}

/* 信息状态样式 */
.running-hub-management .info-text {
  color: #1890ff;
}

/* 代码块样式 */
.running-hub-management .code-block {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
}

/* 高亮文本样式 */
.running-hub-management .highlight-text {
  background: #fff3cd;
  padding: 2px 4px;
  border-radius: 2px;
  font-weight: 500;
}

/* 分割线样式 */
.running-hub-management .ant-divider {
  margin: 24px 0;
}

/* 头像样式 */
.running-hub-management .ant-avatar {
  background: #1890ff;
}

/* 徽章样式 */
.running-hub-management .ant-badge {
  font-size: 12px;
}

/* 工具提示样式 */
.running-hub-management .ant-tooltip-inner {
  font-size: 12px;
}

/* 开关样式 */
.running-hub-management .ant-switch {
  background: #00000040;
}

.running-hub-management .ant-switch-checked {
  background: #1890ff;
}

/* 选择器样式 */
.running-hub-management .ant-select-dropdown {
  border-radius: 6px;
}

.running-hub-management .ant-select-item-option-selected {
  background: #e6f7ff;
}

/* 分页样式 */
.running-hub-management .ant-pagination {
  text-align: right;
  margin-top: 16px;
}

.running-hub-management .ant-pagination-item-active {
  border-color: #1890ff;
  background: #1890ff;
}

.running-hub-management .ant-pagination-item-active a {
  color: #fff;
}
