import React from 'react';
import ReactDOM from 'react-dom';
import { MdClose, MdContentCopy } from 'react-icons/md';
import { message } from 'antd';
import './index.css';
import '../../styles/close-buttons.css';

const TextPopup = ({
  title,
  position,
  onClose,
  positivePrompt,
  negativePrompt,
  modelTags,
  isVisible
}) => {
  if (!isVisible) return null;

  const isInspirationPopup = title === '灵感探索方向';
  // 简化判断，统一使用一个变量处理所有描述词类型的弹窗
  const isDescriptionPopup = title === '描述词';
  
  // 只有在灵感探索页面才显示标签
  const shouldShowTags = isInspirationPopup && modelTags && modelTags.length > 0;
  
  // 判断是否使用简化样式（描述词样式）- 使用简化逻辑
  // 所有灵感探索页面和描述词页面都使用简化样式，不再区分正面负面提示词
  const useSimplifiedStyle = isDescriptionPopup || isInspirationPopup;

  return ReactDOM.createPortal(
    <div 
      className={`text-popup ${isVisible ? 'show' : ''} ${shouldShowTags ? 'with-tags' : ''}`}
      style={{
        left: position?.left || 0,
        top: position?.top || 0
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="text-popup-header">
        <span className="text-popup-title">
          {isInspirationPopup ? '灵感探索方向' : '描述词'}
        </span>
        <button 
          className="small-close-button"
          onClick={(e) => {
            e.stopPropagation();
            onClose?.();
          }}
        >
          <MdClose />
        </button>
      </div>
      <div className="text-popup-content">
        {shouldShowTags ? (
          <>
            <div className="content-section">
              <h3 className="section-title">标签</h3>
              <div className="popup-tags">
                {modelTags.map((tag, index) => (
                  <span key={index} className="popup-tag">{tag}</span>
                ))}
              </div>
            </div>
            <div className="content-section">
              <h3 className="section-title">描述词</h3>
              <div className="prompt-section">
                <p>{positivePrompt || '无'}</p>
              </div>
            </div>
          </>
        ) : useSimplifiedStyle ? (
          // 使用简化样式 - 只显示一个文本框，不区分正负面
          <>
            <div className="prompt-section">
              <p>{positivePrompt || '无描述内容'}</p>
            </div>
          </>
        ) : (
          <>
            <div className="prompt-section">
              <strong>正面提示词：</strong>
              <p>{positivePrompt || '无'}</p>
            </div>
            <div className="prompt-section">
              <strong>负面提示词：</strong>
              <p>{negativePrompt || '无'}</p>
            </div>
          </>
        )}
      </div>
      <div className={`text-popup-footer ${useSimplifiedStyle ? 'right-aligned' : ''}`}>
        {useSimplifiedStyle ? (
          // 使用简化样式 - 只显示一个复制按钮，右对齐
          <button 
            className="copy-text-btn"
            onClick={(e) => {
              e.stopPropagation();
              navigator.clipboard.writeText(positivePrompt || '无');
              message.success('已复制描述词');
            }}
          >
            <MdContentCopy />
            复制描述词
          </button>
        ) : (
          // 其他类型弹窗的底部 - 显示两个复制按钮
          <>
            <button 
              className="copy-text-btn"
              onClick={(e) => {
                e.stopPropagation();
                navigator.clipboard.writeText(positivePrompt || '无');
                message.success('已复制正面词');
              }}
            >
              <MdContentCopy />
              复制正面词
            </button>
            <button 
              className="copy-text-btn"
              onClick={(e) => {
                e.stopPropagation();
                navigator.clipboard.writeText(negativePrompt || '无');
                message.success('已复制负面词');
              }}
            >
              <MdContentCopy />
              复制负面词
            </button>
          </>
        )}
      </div>
    </div>,
    document.body
  );
};

export default TextPopup;