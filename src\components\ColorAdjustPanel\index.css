/* 导入统一面板样式 */
@import '../../styles/panels.css';
@import '../../styles/buttons.css';

.color-adjust-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: var(--spacing-xxs);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  min-height: 320px; /* 设置最小高度以适应内容 */
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.panel-header h3 {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

/* 添加头部内容布局 */
.header-content {
  display: flex;
  align-items: center;
  gap: 26px;
}

/* 提交提示样式 */
.submit-tip {
  font-size: var(--font-size-sm);
  color: var(--brand-primary);
  font-weight: 500;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 添加按钮容器样式 */
.panel-buttons {
  display: flex;
  gap: 8px; /* 按钮之间的间距 */
}

.reset-button {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition-normal);
}

.reset-button:hover {
  color: var(--brand-primary);
}

.reset-button svg {
  width: 18px;
  height: 18px;
}

/* 保存按钮样式 */
.color-adjust-save-button {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition-normal);
  border-radius: 0;
  box-shadow: none;
  padding: 0;
}

.color-adjust-save-button:hover {
  color: var(--brand-primary);
  background: transparent;
  filter: none;
}

.color-adjust-save-button svg {
  width: 18px;
  height: 18px;
}

.adjusters-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.color-adjuster {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.adjuster-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.adjuster-label span {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
}

.adjuster-value-input {
  position: relative;
  display: flex;
  align-items: center;
}

.adjuster-value-input input {
  width: 60px;
  height: 30px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: 0 8px;
  font-size: var(--font-size-sm);
  text-align: left;
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  /* 隐藏原生的上下调节按钮 */
  -moz-appearance: textfield;
}

/* 隐藏Webkit浏览器中的上下调节按钮 */
.adjuster-value-input input::-webkit-outer-spin-button,
.adjuster-value-input input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.adjuster-value-input input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-lighter);
}

.adjuster-slider {
  position: relative;
  padding: 8px 0;
}

/* 修改类名以避免冲突 */
.color-adjust-slider-track {
  position: relative;
  width: 100%;
  height: 12px; /* 增加轨道高度从8px到12px */
  background: var(--bg-secondary);
  border-radius: 6px; /* 增加轨道圆角，与高度成比例 */
  overflow: visible;
  z-index: 0; /* 确保轨道在底层 */
}

/* 新增：中心标记 */
.color-adjust-slider-zero-mark {
  position: absolute;
  top: -3px; /* 调整顶部位置，适应更高的轨道 */
  left: 50%;
  width: 2px;
  height: 18px; /* 增加标记高度 */
  background-color: rgba(0, 0, 0, 0);
  transform: translateX(-50%);
  pointer-events: none; /* 确保此元素不会捕获鼠标事件 */
  z-index: 0;
}

.color-adjust-slider-fill {
  position: absolute;
  height: 100%;
  width: 100%; /* 填满整个轨道区域 */
  left: 0;
  top: 0;
  border-radius: 5px; /* 调整填充圆角，稍小于轨道圆角 */
  transition: all 0.1s ease;
  pointer-events: none; /* 确保填充不会干扰滑块按钮的点击 */
  z-index: 1; /* 在轨道之上，但在滑块之下 */
}

.color-adjust-slider-input {
  position: absolute;
  top: 50%;
  left: 0%;
  width: 100%;
  height: 20px;
  transform: translateY(-50%);
  -webkit-appearance: none;
  background: transparent;
  cursor: pointer;
  margin: 0;
  padding: 0;
  z-index: 2; /* 确保滑块输入在最上层 */
}

.color-adjust-slider-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;  /* 减小宽度 */
  height: 22px;  /* 增加高度 */
  border-radius: 8px;  /* 保持固定像素值，创建竖向胶囊形状 */
  background: white; /* 默认使用白色背景 */
  border: 2px solid var(--text-secondary); /* 保持边框厚度 */
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  z-index: 3; /* 增加z-index确保按钮在最顶层 */
  pointer-events: auto; /* 确保按钮可以接收鼠标事件 */
}

.color-adjust-slider-input::-moz-range-thumb {
  width: 12px;  /* 减小宽度 */
  height: 22px;  /* 增加高度 */
  border-radius: 8px;  /* 保持固定像素值，创建竖向胶囊形状 */
  background: white; /* 默认使用白色背景 */
  border: 2px solid var(--text-secondary); /* 保持边框厚度 */
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  z-index: 3; /* 增加z-index确保按钮在最顶层 */
  pointer-events: auto; /* 确保按钮可以接收鼠标事件 */
}

/* 暗黑主题下的按钮样式 */
[data-theme="dark"] .color-adjust-slider-input::-webkit-slider-thumb {
  background: var(--bg-primary); /* 暗黑主题下中间是黑色 */
  border-color: var(--text-secondary); /* 使用主题变量中的灰色 */
}

[data-theme="dark"] .color-adjust-slider-input::-moz-range-thumb {
  background: var(--bg-primary); /* 暗黑主题下中间是黑色 */
  border-color: var(--text-secondary); /* 使用主题变量中的灰色 */
}

/* 明亮主题下的按钮样式 */
[data-theme="light"] .color-adjust-slider-input::-webkit-slider-thumb {
  background: white; /* 明亮主题下中间是白色 */
  border-color: var(--text-tertiary, #D0D0D0); /* 使用更浅的灰色 */
}

[data-theme="light"] .color-adjust-slider-input::-moz-range-thumb {
  background: white; /* 明亮主题下中间是白色 */
  border-color: var(--text-tertiary, #D0D0D0); /* 使用更浅的灰色 */
}

.color-adjust-slider-input::-webkit-slider-thumb:hover {
  transform: scale(1.1);  /* 缩小放大效果，避免胶囊形状过大 */
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
}

.color-adjust-slider-input::-moz-range-thumb:hover {
  transform: scale(1.1);  /* 缩小放大效果 */
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
}

/* 色调滑块样式由组件动态生成 */
.color-adjuster[data-property="hue"] .color-adjust-slider-fill {
  /* 动态生成的渐变，不需要静态样式 */
}

/* 饱和度滑块样式由组件动态生成 */
.color-adjuster[data-property="saturation"] .color-adjust-slider-fill {
  /* 动态生成的渐变，不需要静态样式 */
}

/* 亮度滑块样式由组件动态生成 */
.color-adjuster[data-property="brightness"] .color-adjust-slider-fill {
  /* 动态生成的渐变，不需要静态样式 */
}

/* 修改对比度滑块 */
.color-adjuster[data-property="contrast"] .color-adjust-slider-fill {
  background: linear-gradient(to right, 
    rgb(128, 128, 128) 50%, 
    rgb(255, 255, 255) 100%
  );
  background-position: 0 0, 0 100%;
  background-size: 100% 50%;
  background-image: 
    linear-gradient(to right, rgb(128, 128, 128) 0%, rgb(255, 255, 255) 100%),
    linear-gradient(to right, rgb(128, 128, 128) 0%, rgb(0, 0, 0) 100%);
  background-repeat: no-repeat;
}

/* 数值调节按钮位置样式 */
.adjuster-value-input .number-controls {
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
} 