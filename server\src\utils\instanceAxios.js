const axios = require('axios');
const Config = require('config');

// 创建axios实例
const instanceAxios = axios.create({
  baseURL: Config.get('comfyUi').offUrl,
  timeout: 5000, // 默认5秒超时
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
instanceAxios.interceptors.request.use(
  config => {
    // 添加认证token
    config.headers['Authorization'] = `Bearer ${Config.get('comfyUi').apiKey}`;
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instanceAxios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    return Promise.reject(error);
  }
);

module.exports = instanceAxios;
