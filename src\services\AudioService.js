// 音频服务 - 处理全局提示音播放
class AudioService {
  constructor() {
    this.audioPath = '/sounds/task-complete.mp3';
    this.errorAudioPath = '/sounds/task-error.wav';
    this.isMuted = localStorage.getItem('soundsMuted') === 'true';
    this.initAudio();
  }
  
  // 初始化音频对象
  initAudio() {
    // 销毁旧的实例（如果存在）
    if (this.taskCompleteSound) {
      this.taskCompleteSound.src = '';
      this.taskCompleteSound.load();
      this.taskCompleteSound = null;
    }
    
    if (this.taskErrorSound) {
      this.taskErrorSound.src = '';
      this.taskErrorSound.load();
      this.taskErrorSound = null;
    }
    
    // 创建新的音频实例
    this.taskCompleteSound = new Audio();
    this.taskErrorSound = new Audio();
    
    // 防止浏览器缓存，添加时间戳参数
    const timestamp = new Date().getTime();
    this.taskCompleteSound.src = `${this.audioPath}?t=${timestamp}`;
    this.taskErrorSound.src = `${this.errorAudioPath}?t=${timestamp}`;
    
    // 添加错误处理
    this.taskCompleteSound.onerror = (error) => {
      console.warn('成功提示音加载错误:', error);
      // 尝试使用不同的方式加载
      setTimeout(() => this.reloadAudio('complete'), 500);
    };
    
    this.taskErrorSound.onerror = (error) => {
      console.warn('错误提示音加载错误:', error);
      // 尝试使用不同的方式加载
      setTimeout(() => this.reloadAudio('error'), 500);
    };
    
    // 预加载音频
    this.taskCompleteSound.load();
    this.taskErrorSound.load();
  }
  
  // 重新加载音频（处理错误情况）
  reloadAudio(type = 'complete') {
    try {
      const timestamp = new Date().getTime();
      const audio = new Audio();
      const audioPath = type === 'error' ? this.errorAudioPath : this.audioPath;
      
      // 使用完全不同的加载方式，强制跳过缓存
      audio.src = `${audioPath}?nocache=${timestamp}`;
      audio.load();
      
      // 成功加载后替换原有实例
      audio.oncanplaythrough = () => {
        if (type === 'error') {
          this.taskErrorSound = audio;
        } else {
        this.taskCompleteSound = audio;
        }
        console.log(`${type === 'error' ? '错误' : '成功'}提示音已重新加载成功`);
      };
    } catch (error) {
      console.error(`重新加载${type === 'error' ? '错误' : '成功'}提示音失败:`, error);
    }
  }
  
  // 播放任务完成提示音
  playTaskComplete() {
    if (this.isMuted) return;
    
    try {
      // 确保从头开始播放
      this.taskCompleteSound.currentTime = 0;
      
      // 尝试播放
      const playPromise = this.taskCompleteSound.play();
      
      // 处理播放Promise
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.warn('播放成功提示音失败:', error);
          
          // 如果是由于音频对象问题引起的错误，尝试重新初始化
          if (error.name === 'NotSupportedError' || 
              error.message?.includes('Failed to load') ||
              error.message?.includes('Range')) {
            console.log('检测到成功提示音问题，尝试重新初始化...');
            this.reloadAudio('complete');
            
            // 1秒后重试播放
            setTimeout(() => {
              try {
                this.taskCompleteSound.play().catch(e => 
                  console.warn('重试播放成功提示音仍然失败:', e)
                );
              } catch (e) {
                console.error('重试播放成功提示音出错:', e);
              }
            }, 1000);
          }
        });
      }
    } catch (error) {
      console.error('播放成功提示音过程中发生错误:', error);
    }
  }
  
  // 播放任务失败提示音
  playTaskError() {
    if (this.isMuted) return;
    
    try {
      // 确保从头开始播放
      this.taskErrorSound.currentTime = 0;
      
      // 尝试播放
      const playPromise = this.taskErrorSound.play();
      
      // 处理播放Promise
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.warn('播放错误提示音失败:', error);
          
          // 如果是由于音频对象问题引起的错误，尝试重新初始化
          if (error.name === 'NotSupportedError' || 
              error.message?.includes('Failed to load') ||
              error.message?.includes('Range')) {
            console.log('检测到错误提示音问题，尝试重新初始化...');
            this.reloadAudio('error');
            
            // 1秒后重试播放
            setTimeout(() => {
              try {
                this.taskErrorSound.play().catch(e => 
                  console.warn('重试播放错误提示音仍然失败:', e)
                );
              } catch (e) {
                console.error('重试播放错误提示音出错:', e);
              }
            }, 1000);
          }
        });
      }
    } catch (error) {
      console.error('播放错误提示音过程中发生错误:', error);
    }
  }
  
  // 切换静音状态
  toggleMute() {
    this.isMuted = !this.isMuted;
    localStorage.setItem('soundsMuted', this.isMuted.toString());
    return this.isMuted;
  }
  
  // 获取当前静音状态
  isSoundMuted() {
    return this.isMuted;
  }
}

// 创建单例
const audioService = new AudioService();
export default audioService; 