import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  message,
  Card,
  Space,
  Row,
  Col,
  Tag,
  Statistic,
  Empty,
  Switch,
  Tabs,
  Avatar,
  Alert,
  Divider,
  Dropdown
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  DeleteOutlined,
  EditOutlined,
  ApiOutlined,
  UploadOutlined,
  DownloadOutlined,
  MoreOutlined
} from '@ant-design/icons';
import moment from 'moment';
import api from '../../../api';
import './RunningHubManagement.css';

const { TabPane } = Tabs;
const { TextArea } = Input;

const RunningHubManagement = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  
  // 概览数据
  const [stats, setStats] = useState({
    totalTasks: 0,
    runningTasks: 0,
    successTasks: 0,
    failedTasks: 0,
    totalConfigs: 0
  });

  // 配置管理
  const [configs, setConfigs] = useState([]);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [configForm] = Form.useForm();

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 搜索状态
  const [searchText, setSearchText] = useState('');

  // 强制关闭Modal的方法
  const forceCloseModal = () => {
    console.log('强制关闭Modal');
    setConfigModalVisible(false);
    setEditingConfig(null);
    configForm.resetFields();
  };

  // 移除了任务管理和账户信息相关的状态

  // 工作流数据（用于配置映射）
  const [availableWorkflows, setAvailableWorkflows] = useState([]);



  useEffect(() => {
    loadData();
  }, []);

  // 监听Modal状态变化，确保遮罩层正确处理
  useEffect(() => {
    if (!configModalVisible) {
      // 确保Modal关闭时清理所有相关状态
      const timer = setTimeout(() => {
        document.body.style.overflow = 'auto';
        // 移除可能残留的遮罩层
        const masks = document.querySelectorAll('.ant-modal-mask');
        masks.forEach(mask => {
          if (mask.parentNode) {
            mask.parentNode.removeChild(mask);
          }
        });
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [configModalVisible]);

  // 添加键盘事件监听器
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && configModalVisible) {
        console.log('ESC键关闭Modal');
        forceCloseModal();
      }
    };

    if (configModalVisible) {
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [configModalVisible]);

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      // 先加载配置和工作流数据
      const [configsResult] = await Promise.all([
        loadConfigs(1, 10), // 默认加载第一页，每页10条
        loadWorkflows()
      ]);
      // 然后基于已加载的数据计算统计信息
      await loadStats(configsResult.data);

      // 设置分页信息
      if (configsResult.pagination) {
        setPagination({
          current: configsResult.pagination.current || 1,
          pageSize: configsResult.pagination.pageSize || 10,
          total: configsResult.pagination.total || 0
        });
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载工作流列表
  const loadWorkflows = async () => {
    try {
      const response = await api.get('/runninghub/admin/workflows');
      if (response && response.success) {
        setAvailableWorkflows(response.data || []);
      }
    } catch (error) {
      console.error('加载工作流列表失败:', error);
    }
  };



  // 加载配置列表
  const loadConfigs = async (page = 1, pageSize = 10, search = '') => {
    try {
      const params = {
        page,
        limit: pageSize
      };

      if (search) {
        params.search = search;
      }

      const response = await api.get('/runninghub/admin/configs', { params });
      if (response && response.success) {
        const configsData = response.data || [];
        const pagination = response.pagination || {};

        // 调试信息：检查API密钥长度
        configsData.forEach((config, index) => {
          console.log(`配置${index + 1} (${config.name}) API密钥长度:`, config.apiKey ? config.apiKey.length : 0);
          if (config.apiKey && config.apiKey.length !== 32) {
            console.warn(`配置 ${config.name} 的API密钥长度不正确: ${config.apiKey.length}位`);
          } else if (config.apiKey && config.apiKey.length === 32) {
            console.log(`✅ 配置 ${config.name} 的API密钥长度正确: 32位`);
          }
        });

        setConfigs(configsData);

        // 返回配置数据和分页信息，供后续统计使用
        return { data: configsData, pagination };
      } else {
        message.error('加载配置失败');
        return { data: [], pagination: {} };
      }
    } catch (error) {
      console.error('加载配置失败:', error);
      message.error('加载配置失败');
      return { data: [], pagination: {} };
    }
  };



  // 加载统计数据
  const loadStats = async (configsData = null) => {
    try {
      // 使用传入的配置数据或当前state中的配置数据
      const currentConfigs = configsData || configs;
      const totalConfigs = currentConfigs.length;

      setStats({
        totalTasks: 0,
        runningTasks: 0,
        successTasks: 0,
        failedTasks: 0,
        totalConfigs
      });
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  };

  // 保存配置
  const saveConfig = async (values) => {
    try {
      setLoading(true);

      if (editingConfig) {
        // 更新配置
        const response = await api.put(`/runninghub/admin/configs/${editingConfig._id}`, values);
        if (response && response.success) {
          message.success('配置更新成功');
          forceCloseModal();
          const configsResult = await loadConfigs(pagination.current, pagination.pageSize, searchText);
          loadStats(configsResult.data);
        } else {
          message.error(response?.message || '配置更新失败');
        }
      } else {
        // 创建配置
        const response = await api.post('/runninghub/admin/configs', values);
        if (response && response.success) {
          message.success('配置创建成功');
          forceCloseModal();
          const configsResult = await loadConfigs(pagination.current, pagination.pageSize, searchText);
          loadStats(configsResult.data);
        } else {
          message.error(response?.message || '配置创建失败');
        }
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除配置
  const deleteConfig = async (configId) => {
    try {
      setLoading(true);
      const response = await api.delete(`/runninghub/admin/configs/${configId}`);
      if (response && response.success) {
        message.success('配置删除成功');
        const configsResult = await loadConfigs(pagination.current, pagination.pageSize, searchText);
        loadStats(configsResult.data);
      } else {
        message.error(response?.message || '配置删除失败');
      }
    } catch (error) {
      console.error('删除配置失败:', error);
      message.error('删除配置失败');
    } finally {
      setLoading(false);
    }
  };



  // 导出工作流映射
  const exportWorkflowMappings = (config) => {
    const mappings = config.workflowMappings || {};
    const exportData = {
      configName: config.name,
      exportTime: new Date().toISOString(),
      workflowMappings: mappings
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `runninghub-mappings-${config.name}-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
    message.success('工作流映射已导出');
  };

  // 导入工作流映射
  const importWorkflowMappings = (file, config) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const importData = JSON.parse(e.target.result);
        if (importData.workflowMappings) {
          // 更新配置的工作流映射
          const updateData = {
            workflowMappings: {
              ...config.workflowMappings,
              ...importData.workflowMappings
            }
          };

          // 调用更新API
          const response = await api.put(`/runninghub/admin/configs/${config._id}`, updateData);
          if (response && response.success) {
            message.success('工作流映射已导入');
            const configsResult = await loadConfigs(pagination.current, pagination.pageSize, searchText);
            loadStats(configsResult.data);
          } else {
            message.error('导入失败: ' + (response?.message || '未知错误'));
          }
        } else {
          message.error('导入文件格式不正确');
        }
      } catch (error) {
        message.error('导入文件解析失败');
      }
    };
    reader.readAsText(file);
    return false; // 阻止默认上传行为
  };

  // 处理配置可见性变更
  const handleVisibilityChange = async (configId, isPublic) => {
    try {
      const response = await api.put(`/runninghub/admin/configs/${configId}/visibility`, {
        isPublic
      });

      if (response && response.success) {
        message.success(`配置已设为${isPublic ? '公共' : '私有'}`);
        const configsResult = await loadConfigs(pagination.current, pagination.pageSize, searchText);
        loadStats(configsResult.data);
      } else {
        message.error(response?.message || '设置可见性失败');
      }
    } catch (error) {
      console.error('设置配置可见性失败:', error);
      message.error('设置可见性失败');
    }
  };











  // 配置表格列
  const configColumns = [
    {
      title: '配置名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      render: (text, record) => (
        <Space>
          <Avatar icon={<ApiOutlined />} size="small" />
          <span>{text}</span>
          {record.isDefault && <Tag color="blue">默认</Tag>}
          {record.isPublic && <Tag color="green">公共</Tag>}
        </Space>
      )
    },
    {
      title: 'API密钥',
      dataIndex: 'apiKey',
      key: 'apiKey',
      width: 220,
      ellipsis: true,
      render: (text) => (
        <span>{text ? `${text.substring(0, 8)}...` : '-'}</span>
      )
    },
    {
      title: '工作流映射',
      key: 'workflowMappings',
      width: 120,
      render: (_, record) => {
        const mappings = record.workflowMappings || {};
        const count = Object.keys(mappings).length;
        return (
          <span>
            {count > 0 ? `${count} 个映射` : '未配置'}
          </span>
        );
      }
    },
    {
      title: '可见性',
      key: 'visibility',
      width: 100,
      render: (_, record) => (
        <Switch
          checked={record.isPublic}
          checkedChildren="公共"
          unCheckedChildren="私有"
          onChange={(checked) => handleVisibilityChange(record._id, checked)}
        />
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text) => text || '-'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 260,
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      width: 280,
      render: (_, record) => {
        const menuItems = [
          {
            key: 'export',
            icon: <DownloadOutlined />,
            label: '导出映射',
            onClick: () => exportWorkflowMappings(record)
          },
          {
            key: 'import',
            icon: <UploadOutlined />,
            label: '导入映射',
            onClick: () => {
              const input = document.createElement('input');
              input.type = 'file';
              input.accept = '.json';
              input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                  importWorkflowMappings(file, record);
                }
              };
              input.click();
            }
          },
          {
            type: 'divider'
          },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: '删除',
            danger: true,
            onClick: () => {
              Modal.confirm({
                title: '确定要删除这个配置吗？',
                content: '删除后无法恢复',
                okText: '确定',
                cancelText: '取消',
                maskClosable: true,
                keyboard: true,
                centered: true,
                onOk: () => deleteConfig(record._id),
                onCancel: () => {
                  // 确保取消时清理状态
                  console.log('删除操作已取消');
                }
              });
            }
          }
        ];

        return (
          <Space size="small">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingConfig(record);

                // 调试信息：检查从列表获取的API密钥
                console.log('编辑配置:', record.name);
                console.log('API密钥长度:', record.apiKey ? record.apiKey.length : 0);
                console.log('API密钥内容:', record.apiKey);

                // 安全地处理workflowMappings数据
                let workflowMappings = {};
                if (record.workflowMappings) {
                  if (record.workflowMappings instanceof Map) {
                    // 如果是Map对象，转换为普通对象
                    workflowMappings = Object.fromEntries(record.workflowMappings);
                  } else if (typeof record.workflowMappings === 'object') {
                    // 如果已经是普通对象，直接使用
                    workflowMappings = record.workflowMappings;
                  }
                }

                const formData = {
                  ...record,
                  workflowMappings
                };

                console.log('设置表单数据:', formData);
                console.log('表单中API密钥长度:', formData.apiKey ? formData.apiKey.length : 0);

                configForm.setFieldsValue(formData);
                setConfigModalVisible(true);
              }}
            >
              编辑
            </Button>
            <Dropdown
              menu={{ items: menuItems }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button type="link" size="small" icon={<MoreOutlined />} />
            </Dropdown>
          </Space>
        );
      }
    }
  ];

  return (
    <div className="running-hub-management">
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="概览" key="overview">
            <Row gutter={[16, 16]}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总配置数"
                    value={stats.totalConfigs}
                    prefix={<SettingOutlined />}
                  />
                </Card>
              </Col>

            </Row>

            <Divider />

            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col span={24}>
                <Card title="工作流映射状态" size="small">
                  {Array.isArray(availableWorkflows) && availableWorkflows.length > 0 ? (
                    <Row gutter={[8, 8]}>
                      {availableWorkflows.map(workflow => {
                        const hasMapping = Array.isArray(configs) && configs.some(config =>
                          config.workflowMappings && config.workflowMappings[workflow.id]
                        );
                        return (
                          <Col key={workflow.id}>
                            <Tag
                              color={hasMapping ? 'success' : 'default'}
                              icon={hasMapping ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
                            >
                              {workflow.name}
                            </Tag>
                          </Col>
                        );
                      })}
                    </Row>
                  ) : (
                    <div style={{ textAlign: 'center', color: '#999' }}>
                      暂无工作流数据
                    </div>
                  )}
                </Card>
              </Col>
            </Row>

            <Alert
              message="RunningHub平台管理"
              description="这里可以管理RunningHub平台的API配置和工作流映射等功能。RunningHub是基于云端的AI工作流执行平台，支持即时启动，无需手动开机。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          </TabPane>

          <TabPane tab="配置管理" key="configs">
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Space>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setEditingConfig(null);
                      configForm.resetFields();
                      setConfigModalVisible(true);
                    }}
                  >
                    添加配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => loadData()}
                    loading={loading}
                  >
                    刷新
                  </Button>
                </Space>

                <Input.Search
                  placeholder="搜索配置名称或描述"
                  allowClear
                  style={{ width: 300 }}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  onSearch={async (value) => {
                    console.log('搜索:', value);
                    setLoading(true);
                    try {
                      const configsResult = await loadConfigs(1, pagination.pageSize, value);
                      loadStats(configsResult.data);
                      setPagination({
                        current: 1,
                        pageSize: pagination.pageSize,
                        total: configsResult.pagination.total
                      });
                    } catch (error) {
                      console.error('搜索失败:', error);
                      message.error('搜索失败');
                    } finally {
                      setLoading(false);
                    }
                  }}
                />
              </div>
            </div>

            <Table
              columns={configColumns}
              dataSource={configs}
              rowKey="_id"
              loading={loading}
              scroll={{ x: 1000 }}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                onChange: async (page, pageSize) => {
                  console.log('分页变化:', page, pageSize);
                  setLoading(true);
                  try {
                    const configsResult = await loadConfigs(page, pageSize, searchText);
                    loadStats(configsResult.data);
                    setPagination({
                      current: page,
                      pageSize: pageSize,
                      total: configsResult.pagination.total
                    });
                  } catch (error) {
                    console.error('分页加载失败:', error);
                    message.error('加载数据失败');
                  } finally {
                    setLoading(false);
                  }
                },
                onShowSizeChange: async (current, size) => {
                  console.log('页面大小变化:', current, size);
                  setLoading(true);
                  try {
                    const configsResult = await loadConfigs(1, size, searchText);
                    loadStats(configsResult.data);
                    setPagination({
                      current: 1,
                      pageSize: size,
                      total: configsResult.pagination.total
                    });
                  } catch (error) {
                    console.error('分页大小变化加载失败:', error);
                    message.error('加载数据失败');
                  } finally {
                    setLoading(false);
                  }
                }
              }}
            />
          </TabPane>





          <TabPane tab="工作流映射" key="workflows">
            <Card title="工作流映射管理">
              <Alert
                message="工作流映射配置"
                description="在这里可以查看和管理系统工作流与RunningHub平台工作流的映射关系。每个系统工作流都可以配置对应的RunningHub工作流ID。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />

              {availableWorkflows.length > 0 ? (
                <div>
                  <div style={{ marginBottom: 16 }}>
                    <Space>
                      <span>工作流总数: <strong>{Array.isArray(availableWorkflows) ? availableWorkflows.length : 0}</strong></span>
                      <span>已映射: <strong>
                        {Array.isArray(availableWorkflows) ? availableWorkflows.filter(workflow =>
                          Array.isArray(configs) && configs.some(config =>
                            config.workflowMappings && config.workflowMappings[workflow.id]
                          )
                        ).length : 0}
                      </strong></span>
                      <span>未映射: <strong>
                        {Array.isArray(availableWorkflows) ? availableWorkflows.filter(workflow =>
                          !Array.isArray(configs) || !configs.some(config =>
                            config.workflowMappings && config.workflowMappings[workflow.id]
                          )
                        ).length : 0}
                      </strong></span>
                    </Space>
                  </div>

                  <Table
                    dataSource={availableWorkflows}
                    rowKey="id"
                    pagination={false}
                    scroll={{ y: 400 }}
                    columns={[
                      {
                        title: '工作流信息',
                        key: 'info',
                        render: (_, workflow) => (
                          <div>
                            <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                              {workflow.name}
                            </div>
                            <div style={{ color: '#666', fontSize: '12px' }}>
                              ID: {workflow.id}
                            </div>
                            <div style={{ color: '#999', fontSize: '12px', marginTop: '4px' }}>
                              {workflow.description}
                            </div>
                          </div>
                        )
                      },
                      {
                        title: '分类',
                        dataIndex: 'category',
                        key: 'category',
                        width: 120,
                        render: (category) => (
                          <Tag color={
                            category === '款式设计' ? 'blue' :
                            category === '模特图' ? 'green' : 'orange'
                          }>
                            {category}
                          </Tag>
                        )
                      },
                      {
                        title: '映射状态',
                        key: 'mappingStatus',
                        width: 150,
                        render: (_, workflow) => {
                          const mappedConfigs = Array.isArray(configs) ? configs.filter(config =>
                            config.workflowMappings && config.workflowMappings[workflow.id]
                          ) : [];

                          if (mappedConfigs.length === 0) {
                            return <Tag color="default">未映射</Tag>;
                          }

                          return (
                            <div>
                              <Tag color="success">已映射</Tag>
                              <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                                {mappedConfigs.length} 个配置
                              </div>
                            </div>
                          );
                        }
                      },
                      {
                        title: 'RunningHub工作流ID',
                        key: 'runningHubIds',
                        render: (_, workflow) => {
                          const mappedConfigs = Array.isArray(configs) ? configs.filter(config =>
                            config.workflowMappings && config.workflowMappings[workflow.id]
                          ) : [];

                          if (mappedConfigs.length === 0) {
                            return <span style={{ color: '#ccc' }}>-</span>;
                          }

                          return (
                            <div>
                              {mappedConfigs.map(config => (
                                <div key={config._id} style={{ marginBottom: '4px' }}>
                                  <Tag size="small">{config.name}</Tag>
                                  <span style={{ fontSize: '12px', fontFamily: 'monospace' }}>
                                    {config.workflowMappings[workflow.id]}
                                  </span>
                                </div>
                              ))}
                            </div>
                          );
                        }
                      }
                    ]}
                  />
                </div>
              ) : (
                <Empty
                  description="暂无工作流数据"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </Card>
          </TabPane>




        </Tabs>
      </Card>

      {/* 配置编辑模态框 */}
      <Modal
        title={editingConfig ? '编辑配置' : '添加配置'}
        open={configModalVisible}
        onCancel={() => {
          console.log('Modal取消操作');
          forceCloseModal();
        }}
        onOk={() => configForm.submit()}
        width={600}
        destroyOnClose={true}
        maskClosable={true}
        keyboard={true}
        centered={true}
      >
        <Form
          form={configForm}
          layout="vertical"
          onFinish={saveConfig}
        >
          <Form.Item
            name="name"
            label="配置名称"
            rules={[{ required: true, message: '请输入配置名称' }]}
          >
            <Input placeholder="请输入配置名称" />
          </Form.Item>

          <Form.Item
            name="apiKey"
            label="API密钥"
            rules={[
              { required: true, message: '请输入API密钥' },
              { len: 32, message: 'API密钥必须是32位字符' }
            ]}
          >
            <Input placeholder="请输入32位API密钥" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={3} placeholder="请输入配置描述" />
          </Form.Item>

          <Divider>工作流映射配置</Divider>

          {Array.isArray(availableWorkflows) && availableWorkflows.length > 0 ? (
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {availableWorkflows.reduce((rows, workflow, index) => {
                if (index % 2 === 0) {
                  rows.push([workflow]);
                } else {
                  rows[rows.length - 1].push(workflow);
                }
                return rows;
              }, []).map((rowWorkflows, rowIndex) => (
                <Row gutter={16} key={rowIndex} style={{ marginBottom: 16 }}>
                  {rowWorkflows.map((workflow) => (
                    <Col span={12} key={workflow.id}>
                      <Form.Item
                        name={['workflowMappings', workflow.id]}
                        label={
                          <div>
                            <div style={{ fontWeight: 'bold' }}>
                              {workflow.name} ({workflow.id})
                            </div>
                            <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                              {workflow.category} - {workflow.description}
                            </div>
                          </div>
                        }
                      >
                        <Input
                          placeholder="请输入RunningHub工作流ID"
                          addonBefore={
                            <Tag color={workflow.category === '款式设计' ? 'blue' :
                                      workflow.category === '模特图' ? 'green' : 'orange'}>
                              {workflow.category}
                            </Tag>
                          }
                        />
                      </Form.Item>
                    </Col>
                  ))}
                  {rowWorkflows.length === 1 && <Col span={12} />}
                </Row>
              ))}
            </div>
          ) : (
            <Alert
              message="暂无可用工作流"
              description="请确保系统中已配置工作流列表"
              type="warning"
              showIcon
            />
          )}

          <Form.Item
            name="isPublic"
            label="公共配置"
            valuePropName="checked"
            tooltip="公共配置对所有用户可见，私有配置仅对创建者可见"
          >
            <Switch checkedChildren="公共" unCheckedChildren="私有" />
          </Form.Item>

          <Form.Item
            name="isDefault"
            label="设为默认配置"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="enabled"
            label="启用配置"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>






    </div>
  );
};

export default RunningHubManagement;
