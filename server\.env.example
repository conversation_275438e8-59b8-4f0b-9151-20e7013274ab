# 数据库配置
MONGODB_URI=mongodb://localhost:27017/aibikini
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d

# 服务器配置
PORT=3002
NODE_ENV=development

# ComfyUI配置
COMFYUI_ENABLED=true
COMFYUI_URL=http://localhost:8188
COMFYUI_API_KEY=your-comfyui-api-key
COMFYUI_INSTANCE_ID=default

# RunningHub配置
RUNNINGHUB_ENABLED=true
RUNNINGHUB_API_KEY=your-32-character-runninghub-api-key

# 平台选择配置
DEFAULT_WORKFLOW_PLATFORM=comfyui

# 文件存储配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# 支付配置
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# 缓存配置
CACHE_TTL=3600

# 安全配置
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
