/* Auth Components Shared Styles */

/* Form Groups */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 14px;
  transition: border-color 0.2s ease;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  caret-color: var(--brand-primary);
}

.form-group input::placeholder {
  color: var(--text-tertiary);
  font-size: 13px;
  transition: none;
  animation: none;
}

/* 自动填充样式 */
.form-group input:-webkit-autofill,
.form-group input:-webkit-autofill:hover,
.form-group input:-webkit-autofill:focus,
.form-group input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px var(--bg-primary) inset !important;
  -webkit-text-fill-color: var(--text-primary) !important;
  transition: background-color 5000s ease-in-out 0s;
  font-size: 14px !important;
  font-family: inherit !important;
}

[data-theme="dark"] .form-group input:-webkit-autofill,
[data-theme="dark"] .form-group input:-webkit-autofill:hover,
[data-theme="dark"] .form-group input:-webkit-autofill:focus,
[data-theme="dark"] .form-group input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px var(--bg-primary) inset !important;
  -webkit-text-fill-color: var(--text-primary) !important;
}

/* Special styles for phone input */
.form-group input[name="phone"] {
  transition: none !important;
  animation: none !important;
  transform: none !important;
  background-color: var(--bg-primary) !important;
  border-radius: var(--radius-sm) !important;
  font-family: inherit !important;
  font-size: 14px !important;
}

.form-group input[name="phone"]:hover,
.form-group input[name="phone"]:focus {
  background-color: var(--bg-primary) !important;
  border-radius: var(--radius-sm) !important;
}

[data-theme="dark"] .form-group input[name="phone"] {
  background-color: var(--bg-primary) !important;
  border-radius: var(--radius-sm) !important;
}

.form-group input[name="phone"]::placeholder {
  font-size: 13px;
  transition: none !important;
  animation: none !important;
  opacity: 1;
  transform: none !important;
  position: static !important;
  left: 0 !important;
  right: 0 !important;
  text-indent: 0 !important;
  letter-spacing: normal !important;
  word-spacing: normal !important;
  animation-duration: 0s !important;
  animation-delay: 0s !important;
  animation-iteration-count: 1 !important;
  animation-name: none !important;
  animation-direction: normal !important;
  animation-fill-mode: none !important;
  animation-play-state: running !important;
  animation-timing-function: ease !important;
}

/* Input states */
.form-group input:hover {
  border-color: var(--brand-primary);
  background-color: var(--bg-primary);
}

.form-group input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 3px var(--brand-primary-lighter);
  background-color: var(--bg-primary);
}

.form-group label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  margin-left: 4px;
}

/* Error styles */
.error-message {
  color: var(--error);
  font-size: 14px;
  margin-top: 4px;
}

/* 密码输入框样式 */
.password-input {
  position: relative;
  width: 100%;
}

.password-input input {
  padding-right: 40px;
  font-family: inherit;
  font-size: 14px;
}

.toggle-password {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.toggle-password:hover {
  opacity: 1;
}

/* Submit button */
.submit-btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: var(--radius-sm);
  background: var(--brand-gradient);
  color: var(--text-inverse);
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s;
  margin-top: 12px;
  margin-bottom: 0;
}

.submit-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s;
}

.submit-btn:hover::after {
  left: 100%;
}

.submit-btn span {
  position: relative;
  z-index: 1;
}

/* Disabled state for submit button */
.submit-btn:disabled {
  background: var(--bg-disabled);
  cursor: not-allowed;
}

.submit-btn:disabled span {
  opacity: 0.7;
}

/* Modal shared styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-md);
  width: 100%;
  max-width: 420px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

[data-theme="dark"] .modal-header h2 {
  color: var(--text-primary);
}

.close-btn {
  width: 30px;
  height: 30px;
  border: none;
  background: transparent;
  position: relative;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: var(--bg-secondary);
}

.close-btn:hover::before,
.close-btn:hover::after {
  background-color: var(--brand-primary);
}

.modal-body {
  padding: 24px;
  padding-bottom: 20px;
} 