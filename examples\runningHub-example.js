/**
 * RunningHub平台使用示例
 * 展示如何使用RunningHub服务创建和管理任务
 */

import runningHubService from '../src/services/runningHub/index.js';
import taskManager from '../src/services/runningHub/taskManager.js';
import { runningHubConfig } from '../src/services/runningHub/config.js';

// 示例配置
const EXAMPLE_CONFIG = {
  apiKey: 'your-32-character-api-key-here', // 替换为您的API密钥
  workflowId: '1850925505116598274', // 替换为您的工作流ID
  description: '示例配置 - 文本生图工作流'
};

/**
 * 示例1: 配置管理
 */
async function exampleConfigManagement() {
  console.log('=== 配置管理示例 ===');
  
  // 添加配置
  runningHubConfig.addConfig('example-config', EXAMPLE_CONFIG, true);
  console.log('✓ 配置添加成功');
  
  // 获取配置
  const config = runningHubConfig.getConfig('example-config');
  console.log('✓ 获取配置:', config.name);
  
  // 获取所有配置
  const allConfigs = runningHubConfig.getAllConfigs();
  console.log('✓ 总配置数量:', allConfigs.length);
  
  // 设置默认配置
  runningHubConfig.setDefaultConfig('example-config');
  console.log('✓ 设置默认配置成功');
}

/**
 * 示例2: 创建简易任务
 */
async function exampleSimpleTask() {
  console.log('\n=== 简易任务示例 ===');
  
  try {
    // 设置API密钥和工作流ID
    runningHubService.setApiKey(EXAMPLE_CONFIG.apiKey);
    runningHubService.setWorkflowId(EXAMPLE_CONFIG.workflowId);
    
    // 创建简易任务
    const result = await runningHubService.createSimpleTask({
      addMetadata: true
    });
    
    if (result.success) {
      console.log('✓ 简易任务创建成功');
      console.log('  任务ID:', result.taskId);
      console.log('  任务状态:', result.taskStatus);
      return result.taskId;
    } else {
      console.error('✗ 简易任务创建失败:', result.error);
    }
  } catch (error) {
    console.error('✗ 简易任务创建异常:', error.message);
  }
}

/**
 * 示例3: 创建高级任务
 */
async function exampleAdvancedTask() {
  console.log('\n=== 高级任务示例 ===');
  
  try {
    // 定义节点信息列表
    const nodeInfoList = [
      {
        nodeId: '6',
        fieldName: 'text',
        fieldValue: 'a beautiful landscape with mountains and lakes, digital art'
      },
      {
        nodeId: '3',
        fieldName: 'seed',
        fieldValue: Math.floor(Math.random() * 1000000).toString()
      }
    ];
    
    // 创建高级任务
    const result = await runningHubService.createAdvancedTask({
      nodeInfoList,
      addMetadata: true
    });
    
    if (result.success) {
      console.log('✓ 高级任务创建成功');
      console.log('  任务ID:', result.taskId);
      console.log('  任务状态:', result.taskStatus);
      return result.taskId;
    } else {
      console.error('✗ 高级任务创建失败:', result.error);
    }
  } catch (error) {
    console.error('✗ 高级任务创建异常:', error.message);
  }
}

/**
 * 示例4: 任务状态监控
 */
async function exampleTaskMonitoring(taskId) {
  console.log('\n=== 任务监控示例 ===');
  
  if (!taskId) {
    console.log('没有任务ID，跳过监控示例');
    return;
  }
  
  try {
    let attempts = 0;
    const maxAttempts = 10;
    
    while (attempts < maxAttempts) {
      // 查询任务状态
      const statusResult = await runningHubService.getTaskStatus(taskId);
      
      if (statusResult.success) {
        console.log(`✓ 任务状态查询 (${attempts + 1}/${maxAttempts}):`, statusResult.status);
        
        // 检查任务是否完成
        if (statusResult.status === 'SUCCESS') {
          console.log('✓ 任务执行成功，获取结果...');
          
          // 获取任务结果
          const resultsResult = await runningHubService.getTaskResults(taskId);
          if (resultsResult.success) {
            console.log('✓ 任务结果获取成功:');
            resultsResult.results.forEach((result, index) => {
              console.log(`  结果 ${index + 1}:`, result.fileUrl);
            });
          }
          break;
        } else if (statusResult.status === 'FAILED') {
          console.error('✗ 任务执行失败');
          break;
        }
        
        // 等待3秒后继续查询
        await new Promise(resolve => setTimeout(resolve, 3000));
        attempts++;
      } else {
        console.error('✗ 任务状态查询失败:', statusResult.error);
        break;
      }
    }
    
    if (attempts >= maxAttempts) {
      console.log('⚠ 达到最大查询次数，停止监控');
    }
  } catch (error) {
    console.error('✗ 任务监控异常:', error.message);
  }
}

/**
 * 示例5: 使用任务管理器
 */
async function exampleTaskManager() {
  console.log('\n=== 任务管理器示例 ===');
  
  try {
    // 创建并监控任务
    const result = await taskManager.createAndMonitorTask(
      {
        type: 'simple',
        params: {
          apiKey: EXAMPLE_CONFIG.apiKey,
          workflowId: EXAMPLE_CONFIG.workflowId,
          addMetadata: true
        }
      },
      // 进度回调
      (progress) => {
        console.log('📊 任务进度更新:', {
          taskId: progress.taskId,
          status: progress.status,
          oldStatus: progress.oldStatus
        });
      },
      // 完成回调
      (completion) => {
        console.log('✅ 任务完成:', {
          taskId: completion.taskId,
          resultCount: completion.results.length
        });
      },
      // 错误回调
      (error) => {
        console.error('❌ 任务失败:', {
          taskId: error.taskId,
          error: error.error
        });
      }
    );
    
    if (result.success) {
      console.log('✓ 任务管理器创建任务成功:', result.taskId);
    } else {
      console.error('✗ 任务管理器创建任务失败:', result.error);
    }
  } catch (error) {
    console.error('✗ 任务管理器异常:', error.message);
  }
}

/**
 * 示例6: 批量任务处理
 */
async function exampleBatchTasks() {
  console.log('\n=== 批量任务示例 ===');
  
  try {
    const taskList = [
      {
        type: 'simple',
        params: {
          apiKey: EXAMPLE_CONFIG.apiKey,
          workflowId: EXAMPLE_CONFIG.workflowId,
          addMetadata: false
        }
      },
      {
        type: 'advanced',
        params: {
          apiKey: EXAMPLE_CONFIG.apiKey,
          workflowId: EXAMPLE_CONFIG.workflowId,
          nodeInfoList: [
            {
              nodeId: '6',
              fieldName: 'text',
              fieldValue: 'a futuristic city at night, cyberpunk style'
            }
          ]
        }
      }
    ];
    
    const result = await runningHubService.createBatchTasks(taskList);
    
    if (result.success) {
      console.log('✓ 批量任务创建成功');
      console.log('  总任务数:', result.totalTasks);
      console.log('  成功数:', result.successCount);
      console.log('  失败数:', result.failureCount);
      
      result.results.forEach((taskResult, index) => {
        if (taskResult.success) {
          console.log(`  任务 ${index + 1}: ✓ ${taskResult.taskId}`);
        } else {
          console.log(`  任务 ${index + 1}: ✗ ${taskResult.error}`);
        }
      });
    } else {
      console.error('✗ 批量任务创建失败:', result.error);
    }
  } catch (error) {
    console.error('✗ 批量任务异常:', error.message);
  }
}

/**
 * 示例7: 账户信息查询
 */
async function exampleAccountInfo() {
  console.log('\n=== 账户信息示例 ===');
  
  try {
    const result = await runningHubService.getAccountInfo(EXAMPLE_CONFIG.apiKey);
    
    if (result.success) {
      console.log('✓ 账户信息获取成功:', result.data);
    } else {
      console.error('✗ 账户信息获取失败:', result.error);
    }
  } catch (error) {
    console.error('✗ 账户信息查询异常:', error.message);
  }
}

/**
 * 主函数 - 运行所有示例
 */
async function runExamples() {
  console.log('🚀 RunningHub平台使用示例开始\n');
  
  // 检查配置
  if (EXAMPLE_CONFIG.apiKey === 'your-32-character-api-key-here') {
    console.error('❌ 请先在示例文件中设置正确的API密钥和工作流ID');
    return;
  }
  
  try {
    // 运行各个示例
    await exampleConfigManagement();
    await exampleAccountInfo();
    
    const simpleTaskId = await exampleSimpleTask();
    const advancedTaskId = await exampleAdvancedTask();
    
    // 监控第一个创建的任务
    if (simpleTaskId) {
      await exampleTaskMonitoring(simpleTaskId);
    }
    
    await exampleTaskManager();
    await exampleBatchTasks();
    
  } catch (error) {
    console.error('❌ 示例运行异常:', error.message);
  }
  
  console.log('\n🎉 RunningHub平台使用示例结束');
}

// 如果直接运行此文件，则执行示例
if (import.meta.url === `file://${process.argv[1]}`) {
  runExamples();
}

export {
  exampleConfigManagement,
  exampleSimpleTask,
  exampleAdvancedTask,
  exampleTaskMonitoring,
  exampleTaskManager,
  exampleBatchTasks,
  exampleAccountInfo,
  runExamples
};
