const workflow = {
  prompt: {
    "1": {
      "inputs": {
        "ckpt_name": "dreamshaperXL_v21TurboDPMSDE.safetensors",
        "vae_name": "sdxl_vae.safetensors",
        "clip_skip": -2,
        "lora_name": "None",
        "lora_model_strength": 1,
        "lora_clip_strength": 1,
        "positive": [
          "32",
          0
        ],
        "negative": "deformed, bad anatomy, disfigured, poorly drawn face, mutated, extra limb, ugly, poorly drawn hands, missing limb, floating limbs, disconnected limbs, disconnected head, malformed hands, long neck, mutated hands and fingers, bad hands, missing fingers, cropped, worst quality, low quality, mutation, poorly drawn, huge calf, bad hands, fused hand, missing hand, disappearing arms, disappearing thigh, disappearing calf, disappearing legs, missing fingers, fused fingers, abnormal eye proportion, Abnormal hands, abnormal legs, abnormal feet, abnormal fingers, drawing, painting, crayon, sketch, graphite, impressionist, noisy, blurry, soft, deformed, ugly, anime, cartoon, graphic, text, painting, crayon, graphite, abstract, glitch",
        "token_normalization": "none",
        "weight_interpretation": "comfy",
        "empty_latent_width": [
          "13",
          3
        ],
        "empty_latent_height": [
          "13",
          4
        ],
        "batch_size": 2
      },
      "class_type": "Efficient Loader",
      "_meta": {
        "title": "图片数量 batch_size"
      }
    },
    "5": {
      "inputs": {
        "weight": 0.8300000000000002,
        "weight_type": "style and composition",
        "combine_embeds": "concat",
        "start_at": 0,
        "end_at": 1,
        "embeds_scaling": "V only",
        "model": [
          "6",
          0
        ],
        "ipadapter": [
          "6",
          1
        ],
        "image": [
          "28",
          0
        ]
      },
      "class_type": "IPAdapterAdvanced",
      "_meta": {
        "title": "印花强度weight"
      }
    },
    "6": {
      "inputs": {
        "preset": "PLUS (high strength)",
        "model": [
          "1",
          0
        ]
      },
      "class_type": "IPAdapterUnifiedLoader",
      "_meta": {
        "title": "IPAdapter Unified Loader"
      }
    },
    "13": {
      "inputs": {
        "aspect_ratio": "original",
        "proportional_width": 1,
        "proportional_height": 1,
        "fit": "crop",
        "method": "lanczos",
        "round_to_multiple": "8",
        "scale_to_side": "longest",
        "scale_to_length": 1536,
        "background_color": "#000000",
        "image": [
          "45",
          0
        ]
      },
      "class_type": "LayerUtility: ImageScaleByAspectRatio V2",
      "_meta": {
        "title": "LayerUtility: ImageScaleByAspectRatio V2"
      }
    },
    "15": {
      "inputs": {
        "strength": 0.20000000000000004,
        "start_percent": 0,
        "end_percent": 0.9000000000000002,
        "positive": [
          "1",
          1
        ],
        "negative": [
          "1",
          2
        ],
        "control_net": [
          "18",
          0
        ],
        "image": [
          "17",
          0
        ],
        "vae": [
          "1",
          4
        ]
      },
      "class_type": "ControlNetApplyAdvanced",
      "_meta": {
        "title": "版型强度strength"
      }
    },
    "17": {
      "inputs": {
        "preprocessor": "CannyEdgePreprocessor",
        "resolution": 1024,
        "image": [
          "13",
          0
        ]
      },
      "class_type": "AIO_Preprocessor",
      "_meta": {
        "title": "AIO Aux Preprocessor"
      }
    },
    "18": {
      "inputs": {
        "control_net_name": "sdxl/diffusers_xl_canny_full.safetensors"
      },
      "class_type": "ControlNetLoader",
      "_meta": {
        "title": "加载ControlNet模型"
      }
    },
    "27": {
      "inputs": {
        "aspect_ratio": "original",
        "proportional_width": 1,
        "proportional_height": 1,
        "fit": "crop",
        "method": "lanczos",
        "round_to_multiple": "8",
        "scale_to_side": "longest",
        "scale_to_length": 1536,
        "background_color": "#000000",
        "image": [
          "44",
          0
        ]
      },
      "class_type": "LayerUtility: ImageScaleByAspectRatio V2",
      "_meta": {
        "title": "LayerUtility: ImageScaleByAspectRatio V2"
      }
    },
    "28": {
      "inputs": {
        "fill_background": true,
        "background_color": "#FFFFFF",
        "RGBA_image": [
          "40",
          0
        ],
        "mask": [
          "40",
          1
        ]
      },
      "class_type": "LayerUtility: ImageRemoveAlpha",
      "_meta": {
        "title": "LayerUtility: ImageRemoveAlpha"
      }
    },
    "32": {
      "inputs": {
        "from_translate": "auto",
        "to_translate": "english",
        "add_proxies": false,
        "proxies": "",
        "auth_data": "",
        "service": "GoogleTranslator",
        "text": "",
        "Show proxy": "proxy_hide",
        "Show authorization": "authorization_hide"
      },
      "class_type": "DeepTranslatorTextNode",
      "_meta": {
        "title": "描述词填写"
      }
    },
    "34": {
      "inputs": {
        "seed": 1111981634424631,
        "steps": 15,
        "cfg": 3,
        "sampler_name": "euler_ancestral",
        "scheduler": "karras",
        "denoise": 1,
        "model": [
          "5",
          0
        ],
        "positive": [
          "15",
          0
        ],
        "negative": [
          "15",
          1
        ],
        "latent_image": [
          "1",
          3
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "随机种子"
      }
    },
    "35": {
      "inputs": {
        "samples": [
          "34",
          0
        ],
        "vae": [
          "1",
          4
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE解码"
      }
    },
    "36": {
      "inputs": {
        "filename_prefix": "Trending",
        "images": [
          "35",
          0
        ]
      },
      "class_type": "SaveImage",
      "_meta": {
        "title": "保存图像"
      }
    },
    "40": {
      "inputs": {
        "threshold": 0.3,
        "detail_method": "VITMatte",
        "detail_erode": 6,
        "detail_dilate": 6,
        "black_point": 0.15,
        "white_point": 0.99,
        "process_detail": true,
        "prompt": "bra, panties, swimsuit",
        "device": "cuda",
        "max_megapixels": 2,
        "image": [
          "27",
          0
        ],
        "sam_models": [
          "41",
          0
        ]
      },
      "class_type": "LayerMask: SegmentAnythingUltra V3",
      "_meta": {
        "title": "LayerMask: SegmentAnythingUltra V3(Advance)"
      }
    },
    "41": {
      "inputs": {
        "sam_model": "sam_vit_h (2.56GB)",
        "grounding_dino_model": "GroundingDINO_SwinB (938MB)"
      },
      "class_type": "LayerMask: LoadSegmentAnythingModels",
      "_meta": {
        "title": "LayerMask: Load SegmentAnything Models(Advance)"
      }
    },
    "44": {
      "inputs": {
        "url": "https://"
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "面料印花图参考图上传"
      }
    },
    "45": {
      "inputs": {
        "url": "https://"
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "版型参考图上传"
      }
    }
  }
};

module.exports = workflow; 