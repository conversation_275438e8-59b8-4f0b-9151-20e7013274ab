.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 1000;
}

.image-info-modal {
  position: fixed;
  width: 280px;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  z-index: 1001;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  transform: translateX(16px); /* 向右偏移16px */
}

/* 拖动时手型切换 */
.image-info-modal.dragging {
  cursor: grabbing !important;
}

[data-theme="dark"] .image-info-modal {
  background: var(--bg-primary);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
}

/* 图片信息区域 */
.image-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.image-info h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
}

[data-theme="dark"] .image-info h3 {
  color: var(--text-primary);
}

.info-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px;
  font-size: 13px;
}

.info-grid span:nth-child(odd) {
  color: var(--text-secondary);
}

[data-theme="dark"] .info-grid span:nth-child(odd) {
  color: var(--text-tertiary);
}

.info-grid span:nth-child(even) {
  color: var(--text-primary);
  text-align: right; /* 内容靠右对齐 */
}

[data-theme="dark"] .info-grid span:nth-child(even) {
  color: var(--text-primary);
}

/* 操作按钮区域 */
.operations {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.operations button {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

[data-theme="dark"] .operations button {
  background: var(--bg-secondary);
  border-color: var(--border-light);
  color: var(--text-primary);
}

.operations button:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}

[data-theme="dark"] .operations button:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

.operations button.delete {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

[data-theme="dark"] .operations button.delete {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.operations .delete-btn {
  color: var(--brand-primary);
  border-color: var(--brand-primary-light);
}

[data-theme="dark"] .operations .delete-btn {
  color: var(--brand-primary);
  border-color: var(--brand-primary-light);
}

.operations .delete-btn:hover {
  background-color: var(--brand-primary-lighter);
  border-color: var(--brand-primary);
}

/* 关闭按钮样式已迁移到统一的close-buttons.css */ 

/* 移动端样式 - 参照 UploadGuideModal */
@media (max-width: 768px) {
  .image-info-modal {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    width: 90% !important;
    max-width: 320px !important;
    height: auto !important;
    max-height: 80vh !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    border-radius: 12px !important;
    box-shadow: var(--shadow-lg) !important;
    z-index: 1001 !important;
  }
  
  .image-info-modal.dragging {
    transform: translate(-50%, -50%) !important;
  }
}

@media (max-width: 480px) {
  .image-info-modal {
    width: 95% !important;
    max-width: 300px !important;
    padding: 12px !important;
  }
  
  .image-info h3 {
    font-size: 14px !important;
  }
  
  .info-grid {
    font-size: 12px !important;
    gap: 6px !important;
  }
  
  .operations button {
    font-size: 13px !important;
    padding: 6px !important;
  }
}

@media (max-width: 360px) {
  .image-info-modal {
    width: 98% !important;
    max-width: 280px !important;
    padding: 10px !important;
  }
  
  .image-info h3 {
    font-size: 13px !important;
  }
  
  .info-grid {
    font-size: 11px !important;
    gap: 4px !important;
  }
  
  .operations button {
    font-size: 12px !important;
    padding: 5px !important;
  }
} 