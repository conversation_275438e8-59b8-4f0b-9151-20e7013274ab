/**
 * ID类型枚举及其对应的数字编号
 * 用于统一管理不同类型的ID前缀
 */
const ID_TYPES = {
  TASK: { code: '01', name: 'task' },       // 任务ID
  ORDER: { code: '02', name: 'order' },     // 订单ID
  USER: { code: '03', name: 'user' },       // 用户ID
  UPLOAD: { code: '04', name: 'upload' },   // 上传文件ID
  IMAGE: { code: '05', name: 'image' },     // 图片ID
  // COMPONENT类型已弃用，使用时间戳格式 `${Date.now()}_${Math.floor(Math.random() * 1000)}`
};

/**
 * 生成指定类型的ID
 * 格式：类型编号(2位) + 时间戳(10位) + 随机数(5位)
 * 总长度：17位数字
 * @param {Object|string} type - ID类型对象或类型名称
 * @returns {string} 生成的ID
 */
const generateId = (type) => {
  // 处理字符串类型输入
  let typeObj = type;
  if (typeof type === 'string') {
    const upperType = type.toUpperCase();
    typeObj = ID_TYPES[upperType] || { code: '00' };
  }

  // 获取类型编号，默认使用 '00'
  const typeCode = typeObj?.code || '00';

  // 获取秒级时间戳（而非毫秒级）
  const timestamp = Math.floor(Date.now() / 1000);

  // 生成5位随机数
  const random = Math.floor(Math.random() * 100000).toString().padStart(5, '0');

  // 组合ID：类型编号(2位) + 时间戳(10位) + 随机数(5位)
  return `${typeCode}${timestamp}${random}`;
};

/**
 * 从ID中提取信息
 * @param {string} id - 要解析的ID
 * @returns {Object} 包含类型和时间戳的对象
 */
const parseId = (id) => {
  if (!id || id.length !== 17) return null;

  const typeCode = id.substring(0, 2);
  const timestamp = id.substring(2, 12);
  const random = id.substring(12);

  // 查找类型名称
  const type = Object.values(ID_TYPES).find(t => t.code === typeCode)?.name || 'unknown';

  return {
    type,
    typeCode,
    timestamp: Number(timestamp),
    random,
    createdAt: new Date(Number(timestamp) * 1000) // 秒转毫秒
  };
};

/**
 * 验证ID是否合法
 * @param {string} id - 要验证的ID
 * @returns {boolean} 是否是合法的ID
 */
const isValidId = (id) => {
  if (!id || typeof id !== 'string') return false;
  
  // 检查长度是否为17位
  if (id.length !== 17) return false;
  
  // 检查是否都是数字
  if (!/^\d+$/.test(id)) return false;
  
  const typeCode = id.substring(0, 2);
  const timestamp = Number(id.substring(2, 12));
  
  // 验证类型编号
  if (!Object.values(ID_TYPES).some(t => t.code === typeCode)) return false;
  
  // 验证时间戳是否合理（不早于2024年且不晚于当前时间）
  const minTimestamp = Math.floor(new Date('2024-01-01').getTime() / 1000);
  if (timestamp < minTimestamp || timestamp > Math.floor(Date.now() / 1000)) return false;
  
  return true;
};

module.exports = {
  ID_TYPES,
  generateId,
  parseId,
  isValidId
}; 