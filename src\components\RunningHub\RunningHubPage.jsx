/**
 * RunningHub主页面组件
 * 整合RunningHub平台的所有功能
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Paper,
  Alert,
  Breadcrumbs,
  Link,
  Chip,
  Card,
  CardContent,
  Grid,
  Divider
} from '@mui/material';
import {
  Settings as SettingsIcon,
  PlayArrow as PlayArrowIcon,
  Monitor as MonitorIcon,
  Info as InfoIcon,
  CloudQueue as CloudQueueIcon
} from '@mui/icons-material';
import ConfigManager from './ConfigManager';
import TaskCreator from './TaskCreator';
import TaskMonitor from './TaskMonitor';
import { runningHubConfig } from '../../services/runningHub/config';
import taskManager from '../../services/runningHub/taskManager.js';

const RunningHubPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [configCount, setConfigCount] = useState(0);
  const [activeTaskCount, setActiveTaskCount] = useState(0);
  const [stats, setStats] = useState({
    totalConfigs: 0,
    activeTasks: 0,
    completedTasks: 0,
    failedTasks: 0
  });

  // 定期更新统计信息
  useEffect(() => {
    const updateStats = () => {
      const configs = runningHubConfig.getAllConfigs();
      const activeTasks = taskManager.getActiveTasks();
      
      setConfigCount(configs.length);
      setActiveTaskCount(activeTasks.length);
      
      setStats({
        totalConfigs: configs.length,
        activeTasks: activeTasks.filter(t => t.status === 'RUNNING' || t.status === 'QUEUED').length,
        completedTasks: activeTasks.filter(t => t.status === 'SUCCESS').length,
        failedTasks: activeTasks.filter(t => t.status === 'FAILED').length
      });
    };

    // 初始更新
    updateStats();

    // 定期更新
    const interval = setInterval(updateStats, 5000);
    return () => clearInterval(interval);
  }, []);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleTaskCreated = (taskInfo) => {
    console.log('新任务创建:', taskInfo);
    // 可以在这里添加任务创建后的处理逻辑
    // 例如自动切换到监控页面
    setActiveTab(2);
  };

  const renderOverview = () => (
    <Box>
      <Typography variant="h5" gutterBottom>
        RunningHub平台概览
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          RunningHub是一个基于云端的AI工作流平台，支持ComfyUI工作流的API化调用。
          与传统的实例管理方式不同，RunningHub采用应用调用方式，无需手动开机，更加便捷高效。
        </Typography>
      </Alert>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <SettingsIcon color="primary" />
                <Box>
                  <Typography variant="h4">{stats.totalConfigs}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    配置数量
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <PlayArrowIcon color="success" />
                <Box>
                  <Typography variant="h4">{stats.activeTasks}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    运行中任务
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <MonitorIcon color="info" />
                <Box>
                  <Typography variant="h4">{stats.completedTasks}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    已完成任务
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <InfoIcon color="error" />
                <Box>
                  <Typography variant="h4">{stats.failedTasks}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    失败任务
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Divider sx={{ my: 3 }} />

      <Typography variant="h6" gutterBottom>
        平台特点
      </Typography>
      
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                <CloudQueueIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                云端执行
              </Typography>
              <Typography variant="body2" color="textSecondary">
                无需本地部署，所有工作流在云端执行，节省本地资源
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                <PlayArrowIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                即时启动
              </Typography>
              <Typography variant="body2" color="textSecondary">
                应用调用方式，无需等待实例开机，任务即时执行
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                灵活配置
              </Typography>
              <Typography variant="body2" color="textSecondary">
                支持多种任务类型，可自定义工作流参数
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                <MonitorIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                实时监控
              </Typography>
              <Typography variant="body2" color="textSecondary">
                实时监控任务状态，自动获取执行结果
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {configCount === 0 && (
        <Alert severity="warning" sx={{ mt: 3 }}>
          您还没有配置RunningHub API，请先在"配置管理"页面添加配置。
        </Alert>
      )}
    </Box>
  );

  const tabLabels = [
    { label: '概览', icon: <InfoIcon /> },
    { label: '配置管理', icon: <SettingsIcon /> },
    { label: '创建任务', icon: <PlayArrowIcon /> },
    { label: '任务监控', icon: <MonitorIcon /> }
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* 面包屑导航 */}
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link underline="hover" color="inherit" href="/">
          首页
        </Link>
        <Typography color="text.primary">RunningHub平台</Typography>
      </Breadcrumbs>

      {/* 页面标题 */}
      <Box display="flex" alignItems="center" gap={2} mb={3}>
        <CloudQueueIcon color="primary" sx={{ fontSize: 32 }} />
        <Box>
          <Typography variant="h4" component="h1">
            RunningHub平台
          </Typography>
          <Typography variant="subtitle1" color="textSecondary">
            云端AI工作流执行平台
          </Typography>
        </Box>
        <Box ml="auto" display="flex" gap={1}>
          <Chip
            label={`${configCount} 个配置`}
            color="primary"
            variant="outlined"
            size="small"
          />
          <Chip
            label={`${activeTaskCount} 个活跃任务`}
            color="success"
            variant="outlined"
            size="small"
          />
        </Box>
      </Box>

      {/* 主要内容 */}
      <Paper sx={{ width: '100%' }}>
        {/* 标签页 */}
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          {tabLabels.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>

        {/* 标签页内容 */}
        <Box sx={{ p: 3 }}>
          {activeTab === 0 && renderOverview()}
          {activeTab === 1 && <ConfigManager />}
          {activeTab === 2 && <TaskCreator onTaskCreated={handleTaskCreated} />}
          {activeTab === 3 && <TaskMonitor />}
        </Box>
      </Paper>
    </Container>
  );
};

export default RunningHubPage;
