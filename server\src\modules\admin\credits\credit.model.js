const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * 算力交易记录模式
 */
const CreditTransactionSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  type: {
    type: String,
    enum: ['recharge', 'consume', 'refund', 'bonus', 'expire','deduct'],
    required: true
  },
  balance: {
    type: Number,
    required: true
  },
  description: {
    type: String
  },
  metadata: {
    orderId: String,
    taskId: String,
    adminId: Schema.Types.ObjectId,
    subscriptionId: Schema.Types.ObjectId,
    expiryDate: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * 用户算力余额模式
 */
const CreditBalanceSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  balance: {
    type: Number,
    default: 0
  },
  totalRecharged: {
    type: Number,
    default: 0
  },
  totalConsumed: {
    type: Number,
    default: 0
  },
  lastRechargeDate: {
    type: Date
  },
  lastConsumeDate: {
    type: Date
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// 创建模型
const CreditTransaction = mongoose.model('CreditTransaction', CreditTransactionSchema);
const CreditBalance = mongoose.model('CreditBalance', CreditBalanceSchema);

module.exports = {
  CreditTransaction,
  CreditBalance
}; 