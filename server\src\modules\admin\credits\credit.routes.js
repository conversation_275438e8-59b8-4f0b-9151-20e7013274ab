const express = require('express');
const router = express.Router();
const creditController = require('./credit.controller');
const { auth, requireRole } = require('../../../middleware/auth.middleware');

// 所有路由都需要管理员权限
router.use(auth, requireRole('admin'));

// 获取用户算力余额
router.get('/user/:userId/credits', creditController.getUserCreditBalance);

// 获取用户算力交易记录
router.get('/user/:userId/credits/transactions', creditController.getUserCreditTransactions);

// 管理员给用户充值算力
router.post('/user/:userId/credits/recharge', creditController.rechargeUserCredit);

// 管理员扣除用户算力
router.post('/user/:userId/credits/deduct', creditController.deductUserCredit);

// 消费用户算力 (系统内部调用)
router.post('/user/:userId/credits/consume', creditController.consumeUserCredit);

// 获取所有用户的算力交易记录
router.get('/credits/transactions', creditController.getAllCreditTransactions);

// 获取算力统计数据
router.get('/credits/stats', creditController.getCreditStats);

module.exports = router; 