/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/scrollbars.css';
@import '../../styles/close-buttons.css';

/* 蒙版绘制弹窗样式 */
.mask-draw-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  height: 100dvh !important;
  z-index: 99999 !important; /* 大幅提高z-index，确保在所有内容之上，包括移动端导航栏 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.mask-draw-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  height: 100dvh;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 99999; /* 大幅提高z-index，确保在所有内容之上，包括移动端导航栏 */
}

.mask-draw-content {
  position: relative !important;
  width: 90% !important;
  max-width: 1200px !important;
  height: calc(90vh - 80px) !important;
  min-height: 600px !important;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
  border: 1px solid var(--border-light);
  z-index: 100000; /* 确保弹窗内容在包装器之上 */
}

/* 主要内容区域 - 三栏布局 */
.mask-draw-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧工具栏 */
.mask-tools-sidebar {
  width: 160px;
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
}

.tools-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
}

/* 工具分割线 */
.tools-divider {
  height: 1px;
  background-color: var(--border-light);
  margin: 16px auto;
  width: 80%;
}

/* 中间绘制区 */
.mask-canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
  height: 100%;
}

.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  min-width: 0;
  transform: translateZ(0);
  will-change: contents;
  padding: 0;
  user-select: none;
  cursor: none !important; /* 强制隐藏默认鼠标 */
  /* 移动端触摸优化 */
  touch-action: none; /* 禁用默认触摸行为 */
  -webkit-touch-callout: none; /* 禁用iOS长按菜单 */
  -webkit-user-select: none; /* 禁用iOS文本选择 */
  -webkit-tap-highlight-color: transparent; /* 禁用触摸高亮 */
}

.canvas-container.dragging {
  cursor: grabbing !important; /* 强制显示抓手光标 */
}

/* 空格键按下状态 - 高优先级 */
.canvas-container.space-down,
.canvas-container.space-down canvas {
  cursor: grab !important; /* 空格键按下时显示抓手光标 */
}

.canvas-container canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: #000;
  transform-origin: center center;
  will-change: transform;
  border-radius: 0;
  cursor: none !important; /* 强制隐藏canvas元素上的光标 */
  /* 移动端触摸优化 */
  touch-action: none; /* 禁用默认触摸行为 */
  -webkit-touch-callout: none; /* 禁用iOS长按菜单 */
  -webkit-user-select: none; /* 禁用iOS文本选择 */
  -webkit-tap-highlight-color: transparent; /* 禁用触摸高亮 */
}

/* 右侧操作栏 */
.mask-actions-sidebar {
  width: 100px;
  border-left: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  position: relative;
}

.actions-content {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* PC端布局样式 */
.desktop-actions-layout {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

/* 移动端布局 - 默认隐藏在PC端 */
.mobile-actions-layout {
  display: none;
}

/* PC端缩放控件样式 */
.desktop-actions-layout .image-zoom-control {
  margin-top: 55px;
  padding: 12px 0;
  border-top: 1px solid var(--border-light);
  border-bottom: 1px solid var(--border-light);
  width: 100%; /* 确保适应容器宽度 */
}

/* 通用缩放控件样式（保持向下兼容） */
.mask-actions-sidebar .image-zoom-control {
  margin-top: 55px;
  padding: 12px 0;
  border-top: 1px solid var(--border-light);
  border-bottom: 1px solid var(--border-light);
  width: 100%; /* 确保适应容器宽度 */
}

.drawing-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

/* 操作栏按钮样式，添加特定的类名避免影响其他组件 */
.actions-content .tool-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background-color: var(--bg-primary);
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--text-primary);
}

.actions-content .tool-btn:hover {
  background-color: var(--bg-hover);
  transform: translateY(-2px);
}

.actions-content .tool-btn.undo, 
.actions-content .tool-btn.redo {
  color: var(--text-secondary);
}

.actions-content .tool-btn.clear {
  color: var(--error-color);
}

.actions-content .tool-btn.save {
  color: var(--brand-primary);
}

/* 确认按钮定位 */
.mask-confirm-btn {
  position: absolute;
  bottom: 24px;
  width: 76px;
  height: 38px;
  padding: 8px 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

/* 动画定义 */
@keyframes modalSlideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  /* 移动端弹窗定位优化 - 参考UploadGuideModal */
  .mask-draw-content {
    width: 95%;
    height: calc(100vh - 40px);
    height: calc(100dvh - 40px);
    min-height: 400px;
  }
  
  .mask-draw-modal {
    align-items: center;
    justify-content: center;
  }
  
  .mask-draw-main {
    flex-direction: column;
  }
  
  .mask-tools-sidebar,
  .mask-actions-sidebar {
    width: 100%;
    border: none;
    max-height: 200px;
  }
  
  .mask-tools-sidebar {
    border-bottom: 1px solid var(--border-light);
  }
  
  /* 移动端工具区域左右布局优化 */
  .mask-tools-sidebar .tools-content {
    display: flex !important;
    flex-direction: row !important;
    gap: 0 !important;
    padding: 12px !important;
  }
  
  /* 左侧工具区域 */
  .mask-tools-sidebar .tools-content > div:first-child {
    flex: 1 !important;
    width: 50% !important;
    padding-right: 10px !important;
  }
  
  /* 分割线 */
  .mask-tools-sidebar .tools-divider {
    width: 1px !important;
    height: auto !important;
    margin: 0 10px !important;
    background-color: var(--border-light) !important;
  }
  
  /* 右侧历史工具区域 */
  .mask-tools-sidebar .tools-content > div:last-child {
    flex: 1 !important;
    width: 50% !important;
    padding-left: 10px !important;
  }
  
  .mask-actions-sidebar {
    width: 100%;
    border-top: 1px solid var(--border-light);
    border-left: none;
    max-height: none;
    position: static;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding-bottom: env(safe-area-inset-bottom, 8px);
  }
  
  .actions-content {
    flex-direction: column;
    padding: 8px var(--spacing-sm) ;
    gap: 0;
  }
  
  /* PC端布局 - 默认隐藏在移动端 */
  .desktop-actions-layout {
    display: none !important;
  }
  
  /* 移动端布局 - 显示 */
  .mobile-actions-layout {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
    width: 100% !important;
  }
  
  /* 移动端第一行 - 缩放控件和提示按钮 */
  .mobile-actions-row-1 {
    display: flex !important;
    flex-direction: row !important;
    align-items: stretch !important;
    gap: 6px !important;
    width: 100% !important;
    justify-content: space-between !important;
  }
  
  /* 移动端第一行中的缩放控件样式调整 - 水平布局 */
  .mobile-actions-row-1 .image-zoom-control {
    flex: 8 !important;
    width: auto !important;
    margin-top: 0 !important;
    padding: 0 !important;
    border: none !important;
    flex-direction: row !important;
    align-items: stretch !important;
    gap: 6px !important;
    min-width: 0 !important;
    justify-content: flex-start !important;
    height: 32px !important;
  }
  
  /* 移动端第一行中的百分比显示框 */
  .mobile-actions-row-1 .zoom-scale {
    min-width: 55px !important;
    flex: 1 !important;
    max-width: 70px !important;
    height: 32px !important;
    font-size: 11px !important;
    margin-bottom: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
  
  /* 移动端第一行中的按钮容器 */
  .mobile-actions-row-1 .zoom-buttons {
    flex-direction: row !important;
    width: auto !important;
    gap: 6px !important;
    align-items: stretch !important;
    flex: 1 !important;
    justify-content: flex-start !important;
  }
  
  /* 移动端第一行中的按钮行 */
  .mobile-actions-row-1 .zoom-buttons-row {
    width: auto !important;
    gap: 6px !important;
    flex: 1 !important;
    justify-content: flex-start !important;
  }
  
  /* 移动端第一行中的所有按钮 */
  .mobile-actions-row-1 .zoom-button {
    min-width: 32px !important;
    height: 32px !important;
    margin-top: 0 !important;
    flex: 1 !important;
    max-width: 120px !important;
    width: auto !important;
  }
  
  /* 移动端缩小和放大按钮特别设置 */
  .mobile-actions-row-1 .zoom-button.zoom-out,
  .mobile-actions-row-1 .zoom-button.zoom-in {
    flex: 1.5 !important;
    max-width: 150px !important;
  }
  
  .mobile-actions-row-1 .zoom-button svg {
    width: 14px !important;
    height: 14px !important;
  }
  
  /* 移动端第二行 - 预览和保存按钮 */
  .mobile-actions-row-2 {
    display: flex !important;
    flex-direction: row !important;
    gap: 8px !important;
    width: 100% !important;
  }
  
  /* 移动端按钮样式调整 */
  .mobile-help-btn {
    margin-top: 0 !important;
    flex: 0 0 auto !important;
    min-width: 60px !important;
    max-width: 80px !important;
    height: 32px !important;
    padding: 0 8px !important;
    font-size: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
  
  .mobile-preview-btn,
  .mobile-save-btn {
    flex: 1 !important;
    height: 38px !important;
    font-size: 14px !important;
    margin: 0 !important;
    position: static !important;
    width: auto !important;
    left: auto !important;
    transform: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
  
  /* 移动端DrawTools组件优化 */
  .mask-tools-sidebar .draw-tools {
    padding: 4px !important;
    gap: 12px !important;
  }
  
  .mask-tools-sidebar .draw-tools .tools-group {
    gap: 8px !important;
  }
  
  .mask-tools-sidebar .draw-tools .tool-buttons {
    gap: 8px !important;
    flex-direction: row !important;
  }
  
  /* 涂抹和擦除按钮水平排列 */
  .mask-tools-sidebar .draw-tools .tool-buttons .tool-btn {
    flex: 1 !important;
    width: 50% !important;
    height: 32px !important;
    padding: 0 8px !important;
    font-size: 12px !important;
  }
  
  .mask-tools-sidebar .draw-tools .tool-name {
    font-size: 11px !important;
  }
  
  .mask-tools-sidebar .draw-tools .brush-size-control {
    gap: 12px !important;
  }
  
  .mask-tools-sidebar .draw-tools .brush-size-display {
    padding: 0 2px !important;
  }
  
  .mask-tools-sidebar .draw-tools .brush-size-value,
  .mask-tools-sidebar .draw-tools .brush-size-label {
    font-size: 11px !important;
  }
  
  /* 移动端HistoryTools组件优化 */
  .mask-tools-sidebar .history-tools {
    padding: 4px !important;
    gap: 12px !important;
  }
  
  .mask-tools-sidebar .history-tools .tools-group {
    gap: 8px !important;
  }
  
  .mask-tools-sidebar .history-tools .tool-buttons {
    gap: 6px !important;
    flex-direction: row !important;
    align-items: stretch !important;
  }
  
  /* 清空按钮占较小比例 */
  .mask-tools-sidebar .history-tools .clear-btn {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    height: 32px !important;
    padding: 0 8px !important;
    font-size: 11px !important;
    flex: 0 0 25% !important;
    width: auto !important;
    min-width: 50px !important;
  }
  
  /* 撤销和恢复按钮占更大比例 */
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn:not(.clear-btn) {
    flex: 1 !important;
    width: auto !important;
    height: 32px !important;
    padding: 0 8px !important;
    font-size: 12px !important;
    display: inline-flex !important;
  }
  
  /* 撤销和恢复按钮容器 - 占剩余75%空间 */
  .mask-tools-sidebar .history-tools .tool-buttons .undo-redo-container {
    display: flex !important;
    flex-direction: row !important;
    gap: 6px !important;
    flex: 1 !important;
  }
  
  .mask-tools-sidebar .history-tools .tool-name {
    font-size: 11px !important;
  }
  
  /* 移动端原有按钮样式，只应用于desktop布局中的按钮 */
  .desktop-actions-layout .preview-mask-btn,
  .desktop-actions-layout .mask-confirm-btn {
    position: static;
    width: auto;
    min-width: 0;
    margin: 8px 0 0 0;
    left: auto;
    transform: none;
    display: block;
    height: 38px;
    font-size: 15px;
  }
  
  /* 移动端布局中的按钮样式在上面的mobile-actions-layout中已定义 */
  .mask-canvas-area {
    padding: var(--spacing-xs) !important;
  }

  /* 历史工具按钮区域分两行布局 */
  .mask-tools-sidebar .history-tools .tool-buttons {
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
    align-items: flex-start !important;
  }
  /* 第一行：清空按钮单独一行，宽度适中，靠左 */
  .mask-tools-sidebar .history-tools .clear-btn {
    width: 80px !important;
    min-width: 60px !important;
    max-width: 120px !important;
    height: 32px !important;
    font-size: 12px !important;
    margin: 0 !important;
    align-self: flex-start !important;
    padding: 0 12px !important;
  }
  /* 第二行：撤销和恢复按钮水平排列 */
  .mask-tools-sidebar .history-tools .undo-redo-container {
    display: flex !important;
    flex-direction: row !important;
    gap: 8px !important;
    width: 100% !important;
    justify-content: flex-start !important;
  }
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn:not(.clear-btn) {
    flex: 1 1 0 !important;
    width: 50% !important;
    min-width: 48px !important;
    height: 32px !important;
    font-size: 12px !important;
    padding: 0 8px !important;
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
}

@media (max-width: 768px) {
  .mask-tools-sidebar .history-tools .tool-buttons {
    display: grid !important;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 6px !important;
    row-gap: 18px !important;
    align-items: stretch !important;
  }
  /* 清空按钮放到第一行第一列 */
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn.clear-btn {
    grid-column: 1 / 2 !important;
    grid-row: 1 / 2 !important;
    width: 100% !important;
    min-width: 60px !important;
    max-width: 100% !important;
    height: 32px !important;
    font-size: 12px !important;
    margin: 0 !important;
    align-self: stretch !important;
    padding: 0 12px !important;
  }
  /* 恢复按钮放到第二行第一列 */
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn:nth-child(2) {
    grid-column: 1 / 2 !important;
    grid-row: 2 / 3 !important;
    width: 100% !important;
    min-width: 0 !important;
    height: 32px !important;
    font-size: 12px !important;
    padding: 0 8px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
  /* 撤销按钮放到第二行第二列 */
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn:nth-child(1) {
    grid-column: 2 / 3 !important;
    grid-row: 2 / 3 !important;
    width: 100% !important;
    min-width: 0 !important;
    height: 32px !important;
    font-size: 12px !important;
    padding: 0 8px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
}

@media (max-width: 480px) {
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn.clear-btn {
    font-size: 11px !important;
    height: 28px !important;
    padding: 0 8px !important;
  }
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn:not(.clear-btn) {
    height: 28px !important;
    font-size: 11px !important;
    padding: 0 6px !important;
  }
}

@media (max-width: 360px) {
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn.clear-btn {
    font-size: 10px !important;
    height: 24px !important;
    padding: 0 4px !important;
  }
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn:not(.clear-btn) {
    height: 24px !important;
    font-size: 10px !important;
    padding: 0 4px !important;
  }
}

@media (max-width: 480px) {
  /* 小屏幕移动端弹窗进一步优化 */
  .mask-draw-content {
    width: 98% !important;
    height: 90vh !important;
    min-height: 450px !important;
    /* 确保在小屏幕上有足够的顶部间距 */
    max-height: calc(100vh - 40px) !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }
  
  /* 小屏幕工具区域进一步优化 */
  .mask-tools-sidebar .tools-content {
    padding: 8px !important;
  }
  
  .mask-tools-sidebar .tools-content > div:first-child {
    padding-right: 6px !important;
  }
  
  .mask-tools-sidebar .tools-content > div:last-child {
    padding-left: 6px !important;
  }
  
  .mask-tools-sidebar .tools-divider {
    margin: 0 6px !important;
  }
  
  /* 小屏幕涂抹和擦除按钮优化 */
  .mask-tools-sidebar .draw-tools .tool-buttons .tool-btn {
    height: 28px !important;
    padding: 0 6px !important;
    font-size: 11px !important;
  }
  
  /* 小屏幕历史工具按钮优化 */
  .mask-tools-sidebar .history-tools .clear-btn {
    height: 28px !important;
    padding: 0 6px !important;
    font-size: 10px !important;
    min-width: 45px !important;
  }
  
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn:not(.clear-btn) {
    height: 28px !important;
    padding: 0 6px !important;
    font-size: 11px !important;
  }
  
  .mask-tools-sidebar .draw-tools .tool-name,
  .mask-tools-sidebar .history-tools .tool-name {
    font-size: 10px !important;
  }
  
  .mask-tools-sidebar .draw-tools .brush-size-value,
  .mask-tools-sidebar .draw-tools .brush-size-label {
    font-size: 10px !important;
  }
  
  /* 小屏幕移动端按钮布局调整 */
  .mobile-help-btn {
    min-width: 50px !important;
    max-width: 70px !important;
    height: 28px !important;
    font-size: 11px !important;
    padding: 0 6px !important;
  }
  
  .mobile-preview-btn,
  .mobile-save-btn {
    height: 32px !important;
    font-size: 12px !important;
  }
  
  .mobile-actions-row-1 .image-zoom-control {
    padding: 0 !important;
    gap: 4px !important;
    height: 28px !important;
  }
  
  .mobile-actions-row-1 .zoom-scale {
    min-width: 40px !important;
    height: 28px !important;
    font-size: 10px !important;
  }
  
  .mobile-actions-row-1 .zoom-button {
    min-width: 28px !important;
    height: 28px !important;
    max-width: 50px !important;
  }
  
  .mobile-actions-row-1 .zoom-buttons {
    gap: 4px !important;
  }
  
  .mobile-actions-row-1 .zoom-buttons-row {
    gap: 4px !important;
  }
  
  .mobile-actions-row-1 .zoom-button svg {
    width: 12px !important;
    height: 12px !important;
  }
}

@media (max-width: 360px) {
  /* 超小屏幕移动端弹窗最大化利用空间 */
  .mask-draw-content {
    width: 100% !important;
    height: 95vh !important;
    min-height: 400px !important;
    max-height: calc(100vh - 20px) !important;
    border-radius: var(--radius-md) !important; /* 稍微减小圆角 */
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }
  
  /* 超小屏幕工具区域极致优化 */
  .mask-tools-sidebar .tools-content {
    padding: 6px !important;
  }
  
  .mask-tools-sidebar .tools-content > div:first-child {
    padding-right: 4px !important;
  }
  
  .mask-tools-sidebar .tools-content > div:last-child {
    padding-left: 4px !important;
  }
  
  .mask-tools-sidebar .tools-divider {
    margin: 0 4px !important;
  }
  
  /* 超小屏幕涂抹和擦除按钮优化 */
  .mask-tools-sidebar .draw-tools .tool-buttons .tool-btn {
    height: 24px !important;
    padding: 0 4px !important;
    font-size: 10px !important;
  }
  
  /* 超小屏幕历史工具按钮优化 */
  .mask-tools-sidebar .history-tools .clear-btn {
    height: 24px !important;
    padding: 0 4px !important;
    font-size: 9px !important;
    min-width: 40px !important;
  }
  
  .mask-tools-sidebar .history-tools .tool-buttons .tool-btn:not(.clear-btn) {
    height: 24px !important;
    padding: 0 4px !important;
    font-size: 10px !important;
  }
  
  .mask-tools-sidebar .draw-tools .tool-name,
  .mask-tools-sidebar .history-tools .tool-name {
    font-size: 9px !important;
  }
  
  .mask-tools-sidebar .draw-tools .brush-size-value,
  .mask-tools-sidebar .draw-tools .brush-size-label {
    font-size: 9px !important;
  }
  
  /* 超小屏幕移动端按钮布局调整 */
  .mobile-help-btn {
    min-width: 45px !important;
    max-width: 60px !important;
    height: 24px !important;
    font-size: 10px !important;
    padding: 0 4px !important;
  }
  
  .mobile-preview-btn,
  .mobile-save-btn {
    height: 28px !important;
    font-size: 11px !important;
  }
  
  .mobile-actions-row-1,
  .mobile-actions-row-2 {
    gap: 6px !important;
  }
  
  .mobile-actions-row-1 .image-zoom-control {
    padding: 0 !important;
    gap: 3px !important;
    height: 24px !important;
  }
  
  .mobile-actions-row-1 .zoom-scale {
    min-width: 35px !important;
    height: 24px !important;
    font-size: 9px !important;
  }
  
  .mobile-actions-row-1 .zoom-button {
    min-width: 24px !important;
    height: 24px !important;
    max-width: 40px !important;
  }
  
  .mobile-actions-row-1 .zoom-buttons {
    gap: 3px !important;
  }
  
  .mobile-actions-row-1 .zoom-buttons-row {
    gap: 3px !important;
  }
  
  .mobile-actions-row-1 .zoom-button svg {
    width: 10px !important;
    height: 10px !important;
  }
}

/* 真实移动设备适配 */
@media (hover: none) and (pointer: coarse) {
  .mask-draw-modal {
    padding: env(safe-area-inset-top, 0px) env(safe-area-inset-right, 0px) env(safe-area-inset-bottom, 0px) env(safe-area-inset-left, 0px);
  }

  .mask-draw-content {
    max-height: calc(100dvh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px) - 20px) !important;
  }
  
  /* 移动端绘制性能优化 */
  .canvas-container {
    /* 启用硬件加速 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    /* 优化触摸响应 */
    touch-action: none;
    /* 禁用滚动和缩放 */
    -webkit-overflow-scrolling: touch;
  }
  
  .canvas-container canvas {
    /* 启用硬件加速 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    /* 优化渲染性能 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
  
  /* 移动端笔刷指示器优化 */
  .brush-cursor {
    /* 启用硬件加速 */
    transform: translate(-50%, -50%) translateZ(0);
    -webkit-transform: translate(-50%, -50%) translateZ(0);
    /* 优化移动端显示 */
    will-change: left, top, width, height;
  }
}

/* 横屏模式适配 */
@media (hover: none) and (pointer: coarse) and (orientation: landscape) {
  .mask-draw-content {
    height: calc(100dvh - 20px) !important;
    max-height: calc(100dvh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px) - 20px) !important;
  }
}

/* 加载指示器 */
.loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
}

.loading-indicator p {
  margin-top: 16px;
  color: white;
  font-size: 14px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 内部确认关闭弹窗样式 - 完全复刻Ant Design确认弹窗样式 */
.mask-draw-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  height: 100dvh;
  background-color: rgba(0, 0, 0, 0.45);
  z-index: 1000000; /* 确保在蒙版绘制弹窗之上 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.mask-draw-confirm-modal {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 416px;
  max-width: calc(100vw - 32px);
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.mask-draw-confirm-header {
  padding: 16px 24px 0;
}

.mask-draw-confirm-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
}

.mask-draw-confirm-body {
  padding: 16px 24px;
}

.mask-draw-confirm-body p {
  margin: 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.5715;
  font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
}

.mask-draw-confirm-footer {
  padding: 0 24px 16px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.mask-draw-confirm-cancel {
  padding: 4px 15px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
}

.mask-draw-confirm-cancel:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.mask-draw-confirm-ok {
  padding: 4px 15px;
  border: 1px solid #ff4d4f;
  border-radius: 6px;
  background: #ff4d4f;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
}

.mask-draw-confirm-ok:hover {
  background: #ff7875;
  border-color: #ff7875;
}

/* 暗色主题适配 */
[data-theme="dark"] .mask-draw-confirm-modal {
  background: #1f1f1f;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .mask-draw-confirm-header h3 {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme="dark"] .mask-draw-confirm-body p {
  color: rgba(255, 255, 255, 0.65);
}

[data-theme="dark"] .mask-draw-confirm-cancel {
  background: #1f1f1f;
  border-color: #434343;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme="dark"] .mask-draw-confirm-cancel:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

[data-theme="dark"] .mask-draw-confirm-ok {
  background: #ff4d4f;
  border-color: #ff4d4f;
  color: #fff;
}

[data-theme="dark"] .mask-draw-confirm-ok:hover {
  background: #ff7875;
  border-color: #ff7875;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 帮助提示按钮 */
.help-tip-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 4px 8px;
  height: 34px;
  font-size: 12px;
  border: 1px solid var(--border-light, #e8e8e8);
  border-radius: 6px;
  background: var(--bg-primary, #f5f5f5);
  color: var(--text-secondary, #666);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 12px;
}

.help-tip-button:hover {
  background: var(--bg-hover, #eee);
  color: var(--text-primary, #333);
  border-color: var(--border-color, #d0d0d0);
}

.help-tip-button svg {
  width: 16px;
  height: 16px;
}

/* 笔刷指示器样式 */
.brush-cursor {
  pointer-events: none;
  position: absolute;
  z-index: 9999;
  transform: translate(-50%, -50%);
  will-change: left, top, width, height, transform;
}

/* 笔刷指示器内部元素 - 用于旋转效果 */
.brush-cursor::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  border: 1.5px dashed rgba(255, 60, 106, 0.5);
  outline: 1px dashed #ffffff;
  outline-offset: -3px;
  animation: dash-rotate 10s linear infinite;
}

/* 虚线旋转动画 */
@keyframes dash-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 检查蒙版按钮样式 */
.preview-mask-btn {
  position: absolute;
  bottom: 76px;
  width: 76px;
  left: 50%;
  transform: translateX(-50%);
  height: 32px;
  padding: 8px 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-mask-btn:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 增大前五个按钮的字号和图标 */
.mobile-actions-row-1 .zoom-scale {
  font-size: 12px !important;
}
.mobile-actions-row-1 .zoom-button {
  font-size: 12px !important;
}
.mobile-actions-row-1 .zoom-button svg {
  width: 16px !important;
  height: 16px !important;
}
.mobile-actions-row-1 .zoom-button.reset,
.mobile-actions-row-1 .zoom-button.preview {
  font-size: 11px !important;
}
.mobile-actions-row-1 .zoom-button.reset svg,
.mobile-actions-row-1 .zoom-button.preview svg {
  width: 13px !important;
  height: 13px !important;
}

/* 多边形选区状态提示 */
.polygon-status-tip {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  z-index: 1000;
  pointer-events: none;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  animation: fadeInDown 0.3s ease-out;
}

.polygon-status-tip .status-message {
  text-align: center;
  line-height: 1.4;
}

.polygon-status-tip .shortcuts {
  margin-top: 6px;
  font-size: 12px;
  opacity: 0.8;
  color: #ccc;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .polygon-status-tip {
    top: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 12px;
    padding: 10px 16px;
  }

  .polygon-status-tip .shortcuts {
    font-size: 11px;
  }
}