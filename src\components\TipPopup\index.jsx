import React, { useEffect, useState, useCallback, useRef } from 'react';
import ReactDOM from 'react-dom';
import { MdClose, MdMenuBook } from 'react-icons/md';
import './index.css';
import '../../styles/close-buttons.css';
import { MASK_DRAW_TIPS, IMAGE_DETAILS_TIPS } from '../../config/guides/operation-tips';

/**
 * 提示弹窗组件
 * 用于显示简单的提示信息
 * 
 * @param {Object} props
 * @param {string} props.type - 弹窗类型，如'mask-draw'、'image-details'等
 * @param {Object} props.position - 弹窗位置，包含left和top属性
 * @param {Function} props.onClose - 关闭弹窗的回调
 * @param {string} props.content - 弹窗内容（可选，如果提供则优先使用）
 * @param {boolean} props.isVisible - 是否显示弹窗
 */
const TipPopup = ({
  type = '',
  position,
  onClose,
  content,
  isVisible
}) => {
  // 拖动相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [currentPosition, setCurrentPosition] = useState({ left: 0, top: 0 });
  const popupRef = useRef(null);

  // 根据类型获取提示内容
  const getTipContent = () => {
    if (content) return content; // 如果提供了content，优先使用
    
    switch (type) {
      case 'mask-draw':
        return MASK_DRAW_TIPS.BASIC;
      case 'image-details':
        return IMAGE_DETAILS_TIPS.ZOOM;
      default:
        return '暂无提示内容';
    }
  };

  // 处理链接按钮点击
  const handleLinkClick = () => {
    window.open('https://www.yuque.com/artahasy/aibikini/lgby708hipbuhokw?singleDoc#', '_blank');
  };

  // 初始化位置 - 在组件挂载和位置变化时立即设置
  useEffect(() => {
    if (position) {
      setCurrentPosition({
        left: position.left || 0,
        top: position.top || 0,
        transform: position.transform || undefined
      });
    }
  }, [position]);

  // 当弹窗显示状态改变时重置拖动状态
  useEffect(() => {
    if (!isVisible) {
      setIsDragging(false);
      setDragOffset({ x: 0, y: 0 });
    }
  }, [isVisible]);

  // 拖动处理函数
  const handleMouseMove = useCallback((e) => {
    if (isDragging) {
      const newX = e.clientX - dragOffset.x;
      const newY = e.clientY - dragOffset.y;
      
      // 限制在视窗范围内
      const maxX = window.innerWidth - 240; // 弹窗宽度
      const maxY = window.innerHeight - 200; // 弹窗大概高度
      
      setCurrentPosition({
        left: Math.max(0, Math.min(newX, maxX)),
        top: Math.max(0, Math.min(newY, maxY))
      });
    }
  }, [isDragging, dragOffset]);

  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      document.body.classList.remove('no-select');
    }
  }, [isDragging]);

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 处理鼠标按下事件
  const handleMouseDown = (e) => {
    // 只在弹窗标题区域允许拖动，不包括关闭按钮
    if (e.target.closest('.tip-popup-header') && !e.target.closest('.tiny-close-button')) {
      setIsDragging(true);
      
      const rect = popupRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
      
      // 防止文本选择
      document.body.classList.add('no-select');
      e.preventDefault();
    }
  };
  
  // 添加点击外部关闭的事件处理
  useEffect(() => {
    if (!isVisible) return;
    
    const handleOutsideClick = (e) => {
      // 如果点击的不是弹窗内部元素，则关闭弹窗
      if (e.target.closest('.tip-popup') === null) {
        onClose?.();
      }
    };
    
    // 添加事件监听
    document.addEventListener('mousedown', handleOutsideClick);
    
    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [isVisible, onClose]);

  if (!isVisible) return null;
  
  // 获取当前应显示的提示内容
  const tipContent = getTipContent();

  return ReactDOM.createPortal(
    <div 
      ref={popupRef}
      className={`tip-popup ${isVisible ? 'show' : ''} ${isDragging ? 'dragging' : ''}`}
      style={{
        ...(currentPosition.left !== 0 || currentPosition.top !== 0 ? {
          left: currentPosition.left,
          top: currentPosition.top,
          ...(currentPosition.transform && { transform: currentPosition.transform })
        } : {}),
        cursor: isDragging ? 'grabbing' : 'default'
      }}
      onMouseDown={handleMouseDown}
    >
      {/* 标题区域 - 可拖动 */}
      <div className="tip-popup-header" style={{ cursor: 'grab' }}>
        <span className="tip-popup-title">使用提示</span>
        <button 
          className="tiny-close-button"
          onClick={(e) => {
            e.stopPropagation();
            onClose?.();
          }}
          aria-label="关闭提示"
        >
          <MdClose />
        </button>
      </div>
      
      {/* 内容区域 */}
      <div className="tip-popup-content">
        {tipContent.includes('\n') ? (
          <ul>
            {tipContent.split('\n').map((item, index, array) => {
              const isLastItem = index === array.length - 1;
              const isTextDescriptionType = type === 'text-description';
              const isLastItemWithLink = isLastItem && isTextDescriptionType && item.includes('建议查看详细教程进行学习');
              
              if (isLastItemWithLink) {
                // 移除开头的"• "并添加链接按钮
                const cleanItem = item.replace(/^[•]\s*/, '');
                return (
                  <li key={index} className="tip-item-with-link">
                    <span>{cleanItem}</span>
                    <button 
                      className="tip-link-btn"
                      onClick={handleLinkClick}
                      title="查看详细教程"
                    >
                      <MdMenuBook />
                    </button>
                  </li>
                );
              } else {
                return (
                  <li key={index}>{item.replace(/^[•]\s*/, '')}</li>
                );
              }
            })}
          </ul>
        ) : (
          <p>{tipContent}</p>
        )}
      </div>
    </div>,
    document.body
  );
};

export default TipPopup;
