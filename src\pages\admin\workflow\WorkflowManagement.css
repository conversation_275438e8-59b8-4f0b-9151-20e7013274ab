.workflow-management {
  padding: 24px;
}

.workflow-management .ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.workflow-management .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.workflow-management .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.workflow-management .ant-tag {
  margin: 2px;
}

.workflow-management .ant-btn-link {
  padding: 0;
  height: auto;
}

.workflow-management .ant-space {
  flex-wrap: wrap;
}

.workflow-management .ant-modal {
  top: 20px;
}

.workflow-management .ant-form-item {
  margin-bottom: 16px;
}

.workflow-management .ant-alert {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workflow-management {
    padding: 16px;
  }
  
  .workflow-management .ant-table {
    font-size: 12px;
  }
  
  .workflow-management .ant-modal {
    margin: 0;
    max-width: 100vw;
  }
}

/* 状态标签样式 */
.workflow-management .status-tag {
  font-weight: 500;
}

/* 操作按钮样式 */
.workflow-management .action-buttons .ant-btn {
  margin-right: 8px;
}

.workflow-management .action-buttons .ant-btn:last-child {
  margin-right: 0;
}

/* 工作流信息样式 */
.workflow-management .workflow-info {
  line-height: 1.4;
}

.workflow-management .workflow-info .workflow-name {
  font-weight: bold;
  font-size: 14px;
  color: #262626;
}

.workflow-management .workflow-info .workflow-meta {
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

.workflow-management .workflow-info .workflow-description {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.3;
}

/* 使用统计样式 */
.workflow-management .usage-stats {
  font-size: 12px;
  line-height: 1.4;
}

.workflow-management .usage-stats > div {
  margin-bottom: 2px;
}

.workflow-management .usage-stats > div:last-child {
  margin-bottom: 0;
}

/* 平台标签样式 */
.workflow-management .platform-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.workflow-management .platform-tags .ant-tag {
  margin: 0;
  font-size: 11px;
  padding: 2px 6px;
  line-height: 1.2;
}

/* 表格自定义样式 */
.workflow-management .ant-table-cell {
  vertical-align: top;
}

.workflow-management .ant-table-row {
  transition: background-color 0.2s ease;
}

/* 模态框表单样式 */
.workflow-management .ant-modal-body .ant-form {
  margin-top: 16px;
}

.workflow-management .ant-form-item-label > label {
  font-weight: 500;
}

/* 加载状态样式 */
.workflow-management .ant-spin-container {
  min-height: 200px;
}

/* 空状态样式 */
.workflow-management .ant-empty {
  margin: 40px 0;
}

/* 工具栏样式 */
.workflow-management .toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 8px;
}

.workflow-management .toolbar .ant-space {
  flex-wrap: wrap;
}

/* 分页样式 */
.workflow-management .ant-pagination {
  margin-top: 16px;
  text-align: right;
}

@media (max-width: 576px) {
  .workflow-management .ant-pagination {
    text-align: center;
  }
}
