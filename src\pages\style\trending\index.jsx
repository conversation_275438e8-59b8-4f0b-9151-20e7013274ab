import React, { useRef, useEffect, useState, useCallback, Suspense, useMemo } from 'react';
import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask,checkUserBalance } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import { executeFlow } from '../../../api/flow';
import './index.css';
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDescription } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { Modal, Button, Spin, message, Tabs, Switch, Select, Radio, Collapse, Drawer, Input } from 'antd';
import 'antd/dist/reset.css';
import { filterShowcaseByTag } from '../../../config/showcase/showcase';
import { UPLOAD_CONFIG } from '../../../config/uploads/upload';
import { getModelImagePath } from '../../../data/models';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import { uploadImage } from '../../../api/upload';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import MaskDrawModal from '../../../components/MaskDrawModal';
import UploadBox from '../../../components/UploadBox';
import PatternPanel from '../../../components/PatternPanel';
import PrintingPanel from '../../../components/PrintingPanel';
import QuantityPanel from '../../../components/QuantityPanel';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import GenerationArea from '../../../components/GenerationArea';
import RequireLogin from '../../../components/RequireLogin';
import { getCurrentUserId } from '../../../api';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import { 
  getTasks, 
  getTaskById, 
  deleteTask, 
  createTask,
  filterTasksByUser
} from '../../../api/task';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import JSZip from 'jszip';
import RandomSeedSelector from '../../../components/RandomSeedSelector';
import TipsPanel from '../../../components/TipsPanel';
import WeightPanel from '../../../components/WeightPanel';
import ModelNobodyPanel from '../../../components/ModelNobodyPanel';
import ModelSelectModal from '../../../components/ModelSelectModal';
import TextDescriptionPanel from '../../../components/TextDescriptionPanel';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import { getTaskComponent } from '../../../utils/taskAdapters';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import { useTaskContext } from '../../../contexts/TaskContext';

const MemoizedImageDetailsModal = React.memo(ImageDetailsModal);

const TRENDING_TIP = "请上传版型参考图片和面料印花参考图片，系统将根据您的上传生成爆款设计。";

// 添加默认空白图片路径
const defaultEmptyImagePath = 'https://file.aibikini.cn/config/workflow/trending-01.png'; // 默认空白图片路径

const TrendingPage = ({ isLoggedIn, userId }) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);
  
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const generationAreaRef = useRef(null);
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [patternPanels, setPatternPanels] = useState([]);
  const [printingPanels, setPrintingPanels] = useState([]);
  const [currentReuploadPrintingPanelId, setCurrentReuploadPrintingPanelId] = useState(null);
  const [extraPrintingUploadGuide, setExtraPrintingUploadGuide] = useState(false);
  const [showPrintingUploadGuide, setShowPrintingUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [imageQuantity, setImageQuantity] = useState(4);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [showAdvancedText, setShowAdvancedText] = useState(false);

  const [advancedPopupPosition, setAdvancedPopupPosition] = useState({ top: 0, left: 0 });
  
  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });
  
  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);
  
  // 添加随机种子相关状态
  const [useRandomSeed, setUseRandomSeed] = useState(true);
  const [seed, setSeed] = useState(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)); 
  
  // 添加加载状态
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);
  
  // 添加强度相关状态
  const [weights, setWeights] = useState({ item1: 0.5, item2: 0.5 });
  // 添加权重禁用状态
  const [weightDisabled, setWeightDisabled] = useState({ item1: false, item2: false });
  
  // 添加模特相关状态
  const [selectedModel, setSelectedModel] = useState(null);
  const [showModelSelect, setShowModelSelect] = useState(false);
  const [customModelSettings, setCustomModelSettings] = useState({});
  const [useModel, setUseModel] = useState(true);
  
  // 添加一个ref来存储当前的AbortController
  const taskAbortController = useRef(null);
  
  // 添加缺失的状态变量
  const [imageDetailsTask, setImageDetailsTask] = useState(null);
  
  // 添加textDescription状态
  const [textDescription, setTextDescription] = useState('');
  
  // 添加preloadImage函数
  const preloadImage = (url) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = url;
      console.log('预加载图片:', url);
    });
  };
  
  
  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);

  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 处理版型参考图片上传
  const handleFileUpload = (file) => {
    console.log('版型参考图片上传:', file);
    // 创建本地预览URL，不立即上传到服务器
    const imageUrl = URL.createObjectURL(file);
    
    // 返回一个成功的Promise，模拟上传成功
    return Promise.resolve({
      success: true,
      results: [{
        url: imageUrl,
        localUrl: imageUrl,
        originalFileName: file.name,
        // 保存原始文件对象供后续上传使用
        file: file
      }]
    })
    .then(result => {
      console.log('本地预览创建成功：', result);
      return result;
    })
    .catch(error => {
      console.error('预览失败：', error);
      message.error('预览失败: ' + error.message);
      throw error;
    });
  };

  // 处理版型上传结果
  const handleUploadResult = (results) => {
    setHasUnsavedChanges(true);
    console.log('版型上传结果处理:', results);
    
    if (results.type === 'panels') {
      const panelsWithType = results.panels.map(panel => ({
        ...panel,
        componentId: generateId(ID_TYPES.COMPONENT),
        type: 'pattern',
        source: 'upload', // 添加source属性，标记为用户上传
        // 保留file对象，以便稍后上传到服务器
        file: panel.file
      }));
      setPatternPanels(prevPanels => [...prevPanels, ...panelsWithType]);
    } else if (results.type === 'update') {
      // 处理单个面板更新的情况
      console.log(`更新面板 ${results.panelId} 的状态:`, results.data);
      
      
      setPatternPanels(prevPanels => 
        prevPanels.map(panel => {
          if (panel.componentId === results.panelId) {
            // 获取服务器文件名 - 直接从服务器响应中获取最可靠
            const serverFileName = results.data?.serverFileName;
            
            if (!serverFileName) {
              console.warn('警告：未从服务器响应中获取到文件名');
            }
            
            // 返回更新后的面板数据 - 使用简化的结构
            return {
              ...panel,
              status: 'completed',
              serverFileName: serverFileName,  // 主要字段
              patternServerFileName: serverFileName,  // 兼容字段
              // 简化的fileInfo结构
              fileInfo: {
                ...(panel.fileInfo || {}),
                ...(results.data?.fileInfo || {}),
                serverFileName: serverFileName
              },
              // 简化的processInfo结构
              processInfo: {
                ...(panel.processInfo || {}),
                serverFileName: serverFileName,
                uploadResult: results.data?.processInfo?.uploadResult
              },
              // 保留file对象
              file: panel.file
            };
          }
          
          // 不是要更新的面板，保持不变
          return panel;
        })
      );
    } else if (results.type === 'error') {
      console.error('上传错误:', results.error);
      message.error('上传失败: ' + results.error);
      // 移除处理中的面板
      setPatternPanels(prevPanels => 
        prevPanels.filter(panel => panel.status !== 'processing'));
    }
  };

  // 处理面料上传结果（第二个上传区域）
  const handlePrintingUploadResult2 = (results) => {
    console.log('处理印花图上传结果:', JSON.stringify(results, null, 2));
    
    try {
      if (results.type === 'panels') {
        if (currentReuploadPrintingPanelId) {
          // 如果是重新上传，替换原有面板
          setPrintingPanels(prevPanels => 
            prevPanels.map(panel => 
              panel.componentId === currentReuploadPrintingPanelId 
                ? { 
                    ...results.panels[0], 
                    componentId: currentReuploadPrintingPanelId, 
                    type: 'printing',
                    source: 'upload', // 添加source属性，标记为用户上传
                    file: results.panels[0].file // 保留file对象
                  }
                : panel
            )
          );
          // 重置当前重新上传的面板ID
          setCurrentReuploadPrintingPanelId(null);
        } else {
          // 如果是新上传，添加新面板
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            componentId: generateId(ID_TYPES.COMPONENT),
            type: 'printing',
            source: 'upload', // 添加source属性，标记为用户上传
            file: panel.file // 保留file对象
          }));
          setPrintingPanels(prevPanels => [...prevPanels, ...panelsWithType]);
        }
      } else if (results.type === 'update') {
        console.log('收到面板更新:', results.data);
        
        // 获取面板ID
        const panelId = results.panelId;
        
 
        
        setPrintingPanels(prevPanels => 
          prevPanels.map(panel => {
            if (panel.componentId === panelId) {
              // 获取服务器文件名 - 直接从服务器响应中获取最可靠
              const serverFileName = results.data?.serverFileName;
              
              if (!serverFileName) {
                console.warn('警告：印花图片未从服务器响应中获取到文件名');
              }
              
              // 返回更新后的面板数据 - 使用简化的结构
              return {
                ...panel,
                status: 'completed',
                serverFileName: serverFileName,  // 主要字段
                printingServerFileName: serverFileName,  // 兼容字段
                // 简化的fileInfo结构
                fileInfo: {
                  ...(panel.fileInfo || {}),
                  ...(results.data?.fileInfo || {}),
                  serverFileName: serverFileName
                },
                // 简化的processInfo结构
                processInfo: {
                  ...(panel.processInfo || {}),
                  serverFileName: serverFileName,
                  uploadResult: results.data?.processInfo?.uploadResult
                },
                // 保留file对象
                file: panel.file
              };
            }
            return panel;
          })
        );
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        // 移除处理中的面板
        setPrintingPanels(prevPanels => 
          prevPanels.filter(panel => panel.status !== 'processing'));
        // 重置当前重新上传的面板ID
        setCurrentReuploadPrintingPanelId(null);
      }
    } catch (error) {
      console.error('处理印花图上传结果时出错:', error);
      message.error('处理印花图上传结果时出错: ' + (error.message || '未知错误'));
    }
  };

  // 处理面料原图上传
  const handlePrintingFileUpload = (file) => {
    console.log('面料印花图片上传:', file);
    // 创建本地预览URL，不立即上传到服务器
    const imageUrl = URL.createObjectURL(file);
    
    // 返回一个成功的Promise，模拟上传成功
    return Promise.resolve({
      success: true,
      results: [{
        url: imageUrl,
        localUrl: imageUrl,
        originalFileName: file.name,
        // 保存原始文件对象供后续上传使用
        file: file
      }]
    })
    .then(result => {
      console.log('面料印花本地预览创建成功：', result);
      return result;
    })
    .catch(error => {
      console.error('面料印花预览失败：', error);
      message.error('预览失败: ' + error.message);
      throw error;
    });
  };

  // 处理删除印花图面板
  const handleDeletePrintingPanel = (panelId) => {
    setPrintingPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
  };

  // 处理重新上传印花图片
  const handleReuploadPrinting = (panel) => {
    if (panel && panel.componentId) {
      // 先删除当前面板
      handleDeletePrintingPanel(panel.componentId);
      // 然后打开面料印花上传弹窗
      setShowPrintingUploadGuide(true);
      setOperationsPanel(null);
    }
  };

  // 处理印花图片状态变更
  const handlePrintingStatusChange = (panelId, newStatus) => {
    setPrintingPanels(prevPanels => 
      prevPanels.map(panel => 
        panel.componentId === panelId 
          ? { ...panel, status: newStatus }
          : panel
      )
    );
  };

  // 处理印花图片上传结果
  const handlePrintingUploadResult = (results) => {
    setHasUnsavedChanges(true);
    console.log('处理印花图上传结果:', JSON.stringify(results, null, 2));
    
    // 直接调用现有的处理函数，保持一致性
    handlePrintingUploadResult2(results);
  };

  // 处理删除版型面板
  const handleDeletePatternPanel = (panelId) => {
    setPatternPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
  };

  // 处理重新上传版型参考图片
  const handleReuploadPattern = (panel) => {
    if (panel && panel.componentId) {
      // 先删除当前面板
      handleDeletePatternPanel(panel.componentId);
      // 然后打开版型参考上传弹窗
      setShowUploadGuide(true);
      setOperationsPanel(null);
    }
  };

  // 处理开始生成按钮点击
  const handleGenerate = async () => {
    // 声明taskData变量在try块外部
    let taskData;
        setSeed(useRandomSeed? (Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)) : seed);

    try {      
      // 验证必填项
      if (patternPanels.length === 0 && printingPanels.length === 0) {
        message.error('请上传至少一张参考图片');
        return;
      }
      
      // 移除对textDescription的必填验证
      
      // 如果当前存在进行中的任务，则中止
      setIsGenerating(true);

      if (taskAbortController.current) {
        taskAbortController.current.abort();
      }

      const balance = await checkUserBalance('爆款开发', 'trending', imageQuantity);
      if(balance.code !== 200){
        message.error(balance.message);
        setIsGenerating(false);
        return;
      }
      // 创建新的AbortController
      taskAbortController.current = new AbortController();
      
      setIsGenerating(true);
      setActiveTab('result');  // 切换到结果标签页
      
      // -------------------- 开始处理版型图片上传 --------------------
      // 检查版型面板中是否需要上传图片到服务器
      let patternServerFileName = null;
      let patternServerUrl = null;
      if (patternPanels.length > 0) {
        const patternPanel = patternPanels[0];
        
        // 检查是否存在file对象和上传来源标记
        if ((patternPanel.file && patternPanel.source === 'upload') ||
            (patternPanel.source === 'upload' && !patternPanel.file)) {
          console.log('版型图片需要上传到服务器:', patternPanel.file.name);
          message.loading('正在上传版型图片到服务器...', 0);
          
          try {
            let fileToUpload = patternPanel.file;
            
            // 如果没有file对象但有URL，需要从URL获取文件
            if (!fileToUpload && patternPanel.url) {
              try {
                const response = await fetch(patternPanel.url);
                const blob = await response.blob();
                fileToUpload = new File([blob], patternPanel.serverFileName || 'pattern.jpg', {
                  type: blob.type || 'image/jpeg'
                });
              } catch (error) {
                console.error('从URL获取版型文件失败:', error);
                message.error('版型图片处理失败，请重试');
                setIsGenerating(false);
                return;
              }
            }
            
            // 上传版型图片到服务器
            const uploadResult = await uploadFiles([fileToUpload],"trending");
            if (uploadResult) {
              const resultData = uploadResult.fileInfos[0];
              patternServerFileName =patternPanel.file.serverFileName;
              patternServerUrl = resultData.url;
              // 获取当前用户ID
              // console.log('版型图片上传成功，服务器文件名:', patternServerFileName);
              // console.log('版型图片服务器URL:', patternServerUrl);
              
              // 更新面板中的服务器文件名和URL
              setPatternPanels(prevPanels => 
                prevPanels.map(panel => 
                  panel.componentId === patternPanel.componentId 
                    ? {
                        ...panel,
                        serverFileName: patternServerFileName,
                        url: patternServerUrl,
                        source: 'history' // 更新为历史记录来源
                      }
                    : panel
                )
              );
              
              message.success('版型图片上传成功');
            } else {
              console.error('版型图片上传失败:', uploadResult);
              message.error('版型图片上传失败');
              setIsGenerating(false);
              return;
            }
          } catch (error) {
            console.error('上传版型图片时出错:', error);
            message.error('版型图片上传失败: ' + (error.message || '未知错误'));
            setIsGenerating(false);
            return;
          } finally {
            message.destroy(); // 关闭所有消息
          }
        } else {
          // 已经上传过的图片，直接获取服务器文件名
          patternServerFileName = patternPanel.serverFileName || 
                                 patternPanel.processInfo?.serverFileName || 
                                 patternPanel.processInfo?.uploadResult?.results?.[0]?.serverFileName;
          patternServerUrl = patternPanel.url;
          
          // console.log("已有版型图片服务器文件名:", patternServerFileName);
        }
      }
      
      // -------------------- 开始处理印花图片上传 --------------------
      // 检查印花面板中是否需要上传图片到服务器
      let printingServerFileName = null;
      let printingServerUrl = null;
      
      if (printingPanels.length > 0) {
        const printingPanel = printingPanels[0];
        
        // 检查是否存在file对象和上传来源标记
        if ((printingPanel.file && printingPanel.source === 'upload') ||
            (printingPanel.source === 'upload' && !printingPanel.file)) {
          console.log('印花图片需要上传到服务器:', printingPanel.file.name);
          message.loading('正在上传印花图片到服务器...', 0);
          
          try {
            let fileToUpload = printingPanel.file;
            
            // 如果没有file对象但有URL，需要从URL获取文件
            if (!fileToUpload && printingPanel.url) {
              try {
                const response = await fetch(printingPanel.url);
                const blob = await response.blob();
                fileToUpload = new File([blob], printingPanel.serverFileName || 'printing.jpg', {
                  type: blob.type || 'image/jpeg'
                });
              } catch (error) {
                console.error('从URL获取印花文件失败:', error);
                message.error('印花图片处理失败，请重试');
                setIsGenerating(false);
                return;
              }
            }
            
            // 上传印花图片到服务器
            const uploadResult = await uploadFiles([fileToUpload],"trending");
            
            if (uploadResult) {
              const resultData = uploadResult.fileInfos[0];
              printingServerFileName = printingPanel.serverFileName;
              printingServerUrl = resultData.url;
              // console.log('印花图片上传成功，服务器文件名:', printingServerFileName);
              // console.log('印花图片服务器URL:', printingServerUrl);
              
              // 更新面板中的服务器文件名和URL
              setPrintingPanels(prevPanels => 
                prevPanels.map(panel => 
                  panel.componentId === printingPanel.componentId 
                    ? {
                        ...panel,
                        serverFileName: printingServerFileName,
                        url: printingServerUrl,
                        source: 'history' // 更新为历史记录来源
                      }
                    : panel
                )
              );
              
              message.success('印花图片上传成功');
            } else {
              console.error('印花图片上传失败:', uploadResult);
              message.error('印花图片上传失败');
              setIsGenerating(false);
              return;
            }
          } catch (error) {
            console.error('上传印花图片时出错:', error);
            message.error('印花图片上传失败: ' + (error.message || '未知错误'));
            setIsGenerating(false);
            return;
          } finally {
            message.destroy(); // 关闭所有消息
          }
        } else {
          // 已经上传过的图片，直接获取服务器文件名
          printingServerFileName = printingPanel.serverFileName || 
                                printingPanel.processInfo?.serverFileName || 
                                printingPanel.processInfo?.uploadResult?.results?.[0]?.serverFileName;
          printingServerUrl = printingPanel.url
          // console.log("已有印花图片服务器文件名:", printingServerFileName);
        }
      }
      
      // 检查面板中的serverFileName字段
      // 详细记录版型面板信息，帮助调试
      console.log('版型面板详细信息:', patternPanels.map(panel => ({
        componentId: panel.componentId,
        serverFileName: panel.serverFileName,
        patternServerFileName: panel.patternServerFileName,
        source: panel.source,
        processInfo: {
          serverFileName: panel.processInfo?.serverFileName,
          serverFileFromResults: panel.processInfo?.uploadResult?.results?.[0]?.serverFileName,
          fullUploadResult: panel.processInfo?.uploadResult
        }
      })));
      
      // 详细记录印花面板信息，帮助调试
      console.log('印花面板详细信息:', printingPanels.map(panel => ({
        componentId: panel.componentId,
        serverFileName: panel.serverFileName,
        printingServerFileName: panel.printingServerFileName,
        source: panel.source,
        processInfo: {
          serverFileName: panel.processInfo?.serverFileName,
          serverFileFromResults: panel.processInfo?.uploadResult?.results?.[0]?.serverFileName,
          fullUploadResult: panel.processInfo?.uploadResult
        }
      })));
      
      // 如果标准路径没有找到，记录错误
      if (patternPanels.length > 0 && !patternServerFileName) {
        console.error('版型图片缺少标准路径的服务器文件名:', patternPanels[0]);
      }
      
      if (printingPanels.length > 0 && !printingServerFileName) {
        console.error('印花图片缺少标准路径的服务器文件名:', printingPanels[0]);
      }
      
      console.log('获取到的服务器文件名:', {
        patternServerFileName,
        printingServerFileName
      });
        
      // 确定主图文件名 - 优先使用版型图片
      const primaryImageFileName = patternServerFileName || printingServerFileName;
      
      
      console.log('使用主图文件名:', primaryImageFileName);
      
      // 设置生成中状态
      setIsGenerating(true);
      
      // 准备生成任务所需数据
      taskData = {
        taskId: generateId(ID_TYPES.TASK),
        userId: userId || getCurrentUserId() || 'developer',
        createdAt: new Date(),
        status: 'processing',
        imageCount: imageQuantity,
        taskType: 'trending',
        pageType: 'trending',
        // 添加必要的文件引用字段
        serverFileName: primaryImageFileName,
        primaryImageFileName: primaryImageFileName,
        
        // 添加默认空白图片路径
        defaultEmptyImagePath: defaultEmptyImagePath,
        // 标记哪些组件使用了默认空白图片
        hasDefaultPatternImage: patternPanels.length === 0,
        hasDefaultPrintingImage: printingPanels.length === 0,
        seed: seed,
        useRandomSeed: useRandomSeed,
        weights: weights,
        textDescription: textDescription,
        // 添加components结构，使用数组形式
        components: [
          // 版型面板组件
          {
            componentType: 'patternReferencePanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: patternPanels.length > 0 ? (patternPanels[0].name || '版型参考图片') : '版型参考图片',
            url: patternPanels.length > 0 ? (patternServerUrl || patternPanels[0].url) : defaultEmptyImagePath,
            type: 'pattern',
            status: 'completed',
            originalImage: patternPanels.length > 0 ? (patternPanels[0].url || patternPanels[0].preview) : defaultEmptyImagePath,
            fileInfo: patternPanels.length > 0 ? patternPanels[0].fileInfo || {} : {},
            extraData: patternPanels.length > 0 && patternPanels[0].extraData ? patternPanels[0].extraData : undefined,
            serverFileName: patternServerFileName || 'defaultEmpty.png', // 使用默认文件名
            source: patternPanels.length > 0 ? 'history' : 'default', // 标记来源
            isDefaultEmpty: patternPanels.length === 0 // 标记是否使用默认空白图片
          },
          
          // 印花面板组件
          {
            componentType: 'printingReferencePanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: printingPanels.length > 0 ? (printingPanels[0].name || '面料印花参考图片') : '面料印花参考图片',
            url: printingPanels.length > 0 ? (printingServerUrl || printingPanels[0].url) : defaultEmptyImagePath,
            type: 'printing',
            status: 'completed',
            originalImage: printingPanels.length > 0 ? (printingPanels[0].url || printingPanels[0].preview) : defaultEmptyImagePath,
            fileInfo: printingPanels.length > 0 ? printingPanels[0].fileInfo || {} : {},
            extraData: printingPanels.length > 0 && printingPanels[0].extraData ? printingPanels[0].extraData : undefined,
            serverFileName: printingServerFileName || 'defaultEmpty.png', // 使用默认文件名
            source: printingPanels.length > 0 ? 'history' : 'default', // 标记来源
            isDefaultEmpty: printingPanels.length === 0 // 标记是否使用默认空白图片
          },
          
          // 提示词面板
          ...(textDescription ? [{
            componentType: 'textDescriptionPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '文字描述',
            status: 'completed',
            prompt: textDescription
          }] : []),
          
          // 强度设置组件
          {
            componentType: 'weightPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '强度设置',
            status: 'completed',
            patternWeight: weights.item1,  // 不再除以10，保持与回填时相同的数值范围
            printingWeight: weights.item2,  // 不再除以10，保持与回填时相同的数值范围
            patternDisabled: weightDisabled.item1,
            printingDisabled: weightDisabled.item2
          },
          
          // 随机种子选择器
          {
            componentType: 'randomSeedSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '随机种子',
            status: 'completed',
            useRandom: false,
            value:seed // 确保传递数字类型
          },
          
          // 源图片面板组件
          {
            componentType: 'sourceImagePanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '源图片',
            status: 'completed',
            url: patternPanels.length > 0 ? (patternServerUrl || patternPanels[0].url) : 
                 (printingPanels.length > 0 ? (printingServerUrl || printingPanels[0].url) : null),
            // 明确使用服务器处理后的文件名
            serverFileName: patternServerFileName,
            // 确保fileInfo中也使用正确的服务器文件名
            fileInfo: patternPanels.length > 0 ? {
              ...patternPanels[0].fileInfo || {},
              serverFileName: patternServerFileName,
              // 确保在fileInfo中包含正确的serverFileName
              processInfo: {
                ...patternPanels[0].processInfo || {},
                serverFileName: patternServerFileName,
                uploadResult: patternPanels[0].processInfo?.uploadResult || {
                  results: [{
                    serverFileName: patternServerFileName
                  }]
                }
              }
            } : (printingPanels.length > 0 ? {
              ...printingPanels[0].fileInfo || {},
              serverFileName: printingServerFileName,
              // 确保在fileInfo中包含正确的serverFileName
              processInfo: {
                ...printingPanels[0].processInfo || {},
                serverFileName: printingServerFileName,
                uploadResult: printingPanels[0].processInfo?.uploadResult || {
                  results: [{
                    serverFileName: printingServerFileName
                  }]
                }
              }
            } : {})
          }
        ],
        
        // 使用generatedImages结构初始化图片数组
        generatedImages: Array(imageQuantity).fill(null).map((_, index) => ({
          imageIndex: index,
          status: 'processing'
        })),
        processInfo:{
          results:[]
        }
      };

      console.log('准备发送的任务数据:', {
        taskId: taskData.taskId,
        components: Object.keys(taskData.components),
        serverFileName: taskData.serverFileName,
        primaryImageFileName: taskData.primaryImageFileName
      });

      // 先添加到本地状态，使UI立即响应
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      await createFlowTask(taskData);
      const resultData = await executeFlow(WORKFLOW_NAME.TRENDING,{
        "1":{
           "batch_size": imageQuantity
        },
        "34":{
          "seed": seed
        },
        "15":{
          "strength":weights.item1
        },  
        "5":{
          "weight":weights.item2
        },
        "32":{
          "text":textDescription
        },
        "45": {
          "url":patternServerUrl?patternServerUrl:"https://file.aibikini.cn/config/workflow/trending-01.png"
          },
        "44": {
          "url":printingServerUrl?printingServerUrl:"https://file.aibikini.cn/config/workflow/trending-01.png"        },
        "subInfo":{
          "type": "trending",
          "title":"爆款开发",
          "count":imageQuantity
        }
      },taskData.taskId);
      setIsGenerating(false);
      setHasUnsavedChanges(false);
    if( generationAreaRef.current){
        taskData.promptId = resultData.promptId;
        taskData.instanceId = resultData.instanceId;
        taskData.url = resultData.url;
                taskData.promptId = resultData.promptId;
        taskData.instanceId = resultData.instanceId;
        taskData.url = resultData.url;
        taskData.newTask = true;
        taskData.netWssUrl=resultData.netWssUrl;
        taskData.clientId=resultData.clientId;
        taskData.netWssUrl=resultData.netWssUrl;
        taskData.clientId=resultData.clientId;
        generationAreaRef.current.setGenerationTasks(taskData);
    }
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error('创建任务失败: ' + error.message);
      taskData.status = 'failed';
      setIsGenerating(false);
      setHasUnsavedChanges(false);
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      
      // 调用updateTask以触发失败提示音
      updateTask(taskData);
    }
  };

  // 处理单张图片下载
  const handleDownloadImage = async (imageUrl, taskId, index) => {
    try {
      message.info('准备下载...');
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      const response = await fetch(httpsUrl);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `${taskId}_${parseInt(index) + 1}.jpg`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      message.error('下载失败');
      console.error('下载出错:', error);
    }
  };

  // 处理批量下载
  const handleBatchDownload = async (task) => {
    // 调用通用下载辅助函数
    await downloadHelper(
      task, 
      fetch, // 传递fetch函数
      handleDownloadImage // 传递单张图片下载函数
    );
  };

  // 处理编辑任务
  const handleEditTask = (task) => {
    try {
      console.log('编辑任务:', task);
      
      // 只处理数组结构，不再兼容对象结构
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);
      
      // 获取各组件 - 只使用新的组件名称
      const patternComponent = components.find(c => c.componentType === 'patternReferencePanel');
      const printingComponent = components.find(c => c.componentType === 'printingReferencePanel');
      const textDescriptionComponent = components.find(c => c.componentType === 'textDescriptionPanel');
      const modelComponent = components.find(c => c.componentType === 'modelPanel');
      // 只查找weightPanel组件
      const weightComponent = components.find(c => c.componentType === 'weightPanel');
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      const quantityComponent = components.find(c => c.componentType === 'quantityPanel');
      
      console.log('获取到的编辑组件:', {
        patternComponent,
        printingComponent,
        textDescriptionComponent,
        modelComponent,
        weightComponent,
        seedComponent,
        quantityComponent
      });
      
      // 回填数据到实际状态
      // 版型面板回填
      if (patternComponent) {
        // 获取服务器文件名（仅使用标准路径）
        const patternServerFileName = patternComponent.serverFileName || 
                               patternComponent.processInfo?.serverFileName ||
                               patternComponent.processInfo?.uploadResult?.results?.[0]?.serverFileName;
        
        console.log('版型回填：使用服务器文件名:', patternServerFileName);
        
        const patternPanel = {
          componentId: generateId(ID_TYPES.COMPONENT),
          preview: patternComponent.url || patternComponent.originalImage,
          url: patternComponent.url || patternComponent.originalImage,
          name: patternComponent.name || '版型参考图片',
          title: patternComponent.name || '版型参考图片',
          status: 'completed',
          type: 'pattern',
          // 添加服务器文件名相关字段
          serverFileName: patternServerFileName,
          patternServerFileName: patternServerFileName,
          ...(patternComponent.extraData && { extraData: patternComponent.extraData }),
          // 添加完整的processInfo结构
          processInfo: {
            serverFileName: patternServerFileName,
            uploadResult: {
              results: [{
                serverFileName: patternServerFileName
              }]
            }
          },
          fileInfo: patternComponent.fileInfo || {}
        };
        
        setPatternPanels([patternPanel]);
        console.log('回填版型数据:', patternPanel);
      } else {
        setPatternPanels([]);
        console.warn('未找到版型组件数据');
      }
      
      // 印花面板回填
      if (printingComponent) {
        // 获取服务器文件名（仅使用标准路径）
        const printingServerFileName = printingComponent.serverFileName || 
                                printingComponent.processInfo?.serverFileName ||
                                printingComponent.processInfo?.uploadResult?.results?.[0]?.serverFileName;
        
        console.log('印花回填：使用服务器文件名:', printingServerFileName);
        
        const printingPanel = {
          componentId: generateId(ID_TYPES.COMPONENT),
          preview: printingComponent.url || printingComponent.originalImage,
          url: printingComponent.url || printingComponent.originalImage,
          name: printingComponent.name || '面料印花',
          title: printingComponent.name || '面料印花',
          status: 'completed',
          type: 'printing',
          // 添加服务器文件名相关字段
          serverFileName: printingServerFileName,
          printingServerFileName: printingServerFileName,
          ...(printingComponent.extraData && { extraData: printingComponent.extraData }),
          // 添加完整的processInfo结构
          processInfo: {
            serverFileName: printingServerFileName,
            uploadResult: {
              results: [{
                serverFileName: printingServerFileName
              }]
            }
          },
          fileInfo: printingComponent.fileInfo || {}
        };
        
        setPrintingPanels([printingPanel]);
        console.log('回填印花数据:', printingPanel);
      } else {
        setPrintingPanels([]);
        console.warn('未找到印花组件数据');
      }
      
      // 模特数据回填
      if (modelComponent) {
        if (modelComponent.type === 'nobody') {
          setUseModel(false);
          setSelectedModel(null);
          console.log('回填无模特设置');
        } else {
          setUseModel(true);
          setSelectedModel({
            componentId: modelComponent.componentId,
            name: modelComponent.name,
            type: modelComponent.type || 'preset',
            preview: modelComponent.preview,
            ...(modelComponent.type === 'custom' && { 
              prompt: modelComponent.prompt,
              negativePrompt: modelComponent.negativePrompt
            })
          });
          
          // 如果是自定义模特，回填自定义设置
          if (modelComponent.type === 'custom') {
            setCustomModelSettings({
              prompt: modelComponent.prompt || '',
              negativePrompt: modelComponent.negativePrompt || ''
            });
          }
          console.log('回填模特数据:', modelComponent);
        }
      } else {
        setUseModel(false);
        setSelectedModel(null);
        console.warn('未找到模特组件数据');
      }
      
      // 文字描述回填
      if (textDescriptionComponent && textDescriptionComponent.prompt) {
        // 直接设置React状态，不再通过DOM操作
        setTextDescription(textDescriptionComponent.prompt || '');
        console.log('回填文字描述:', textDescriptionComponent);
      } else {
        // 清空描述词
        setTextDescription('');
        console.warn('未找到文字描述组件数据');
      }
      
      // 强度回填
      if (weightComponent) {
        // 获取patternWeight和printingWeight属性
        let patternWeight = weightComponent.patternWeight;
        let printingWeight = weightComponent.printingWeight;
        
        // 设置默认值，确保有值
        patternWeight = patternWeight !== undefined ? patternWeight : 0.5;
        printingWeight = printingWeight !== undefined ? printingWeight : 0.5;
        
        // 直接使用权重值，不再进行历史数据兼容性处理
        setWeights({
          item1: patternWeight,
          item2: printingWeight
        });
        console.log('回填强度设置:', { item1: patternWeight, item2: printingWeight });
      } else {
        setWeights({ item1: 0.5, item2: 0.5 });
        console.warn('未找到强度组件数据');
      }
      
      // 种子回填
      if (seedComponent) {
        setUseRandomSeed(seedComponent.useRandom);
        if (!seedComponent.useRandom && seedComponent.value !== undefined && seedComponent.value !== -1) {
          // 修改为设置数字类型而不是字符串
          setSeed(Number(seedComponent.value));
        }
        console.log('回填种子设置:', seedComponent);
      } else if (task.seed !== undefined) {
        // 回退到任务级seed
        setUseRandomSeed(false);
        // 修改为设置数字类型
        setSeed(Number(task.seed));
        console.log('使用任务级种子:', task.seed);
      } else {
        setUseRandomSeed(true);
        setSeed(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));
        console.warn('未找到种子组件数据');
      }
      
      // 数量回填
      if (quantityComponent) {
        const quantity = quantityComponent.quantity || quantityComponent.value;
        if (quantity) {
          setImageQuantity(quantity);
          console.log('回填数量设置:', quantity);
        }
      } else if (task.imageCount) {
        setImageQuantity(task.imageCount);
        console.log('使用任务级数量:', task.imageCount);
      }
      
      // 滚动到控制面板顶部
      if (controlPanelRef.current) {
        controlPanelRef.current.scrollTop = 0;
      }
      
      message.success('成功加载任务设置');
    } catch (error) {
      console.error('处理编辑任务时出错:', error);
      message.error('无法加载任务编辑器');
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleViewDetails = (image, task) => {
    try {
      console.log('查看图片详情:', image);
      console.log('相关任务数据:', task);
      
      // 只处理数组结构，不再兼容对象结构
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);
      
      // 获取各组件 - 只使用新的组件类型名称
      const patternComponent = components.find(c => c.componentType === 'patternReferencePanel');
      const printingComponent = components.find(c => c.componentType === 'printingReferencePanel');
      const textDescriptionComponent = components.find(c => c.componentType === 'textDescriptionPanel');
      const modelComponent = components.find(c => c.componentType === 'modelPanel');
      // 只查找weightPanel组件，不再兼容weightSettings
      const weightComponent = components.find(c => c.componentType === 'weightPanel');
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      
      console.log('获取到的详情组件:', {
        patternComponent,
        printingComponent,
        textDescriptionComponent,
        modelComponent,
        weightComponent,
        seedComponent
      });
      
      // 准备规范化的组件数组
      const adaptedComponents = [
        // 版型组件
        patternComponent ? {
          ...patternComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'patternReferencePanel',
          originalImage: patternComponent.originalImage || patternComponent.url,
          url: patternComponent.url || patternComponent.originalImage
        } : {
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'patternReferencePanel',
          name: '版型参考图片',
          url: ''
        },
        
        // 印花组件
        printingComponent ? {
          ...printingComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'printingReferencePanel',
          originalImage: printingComponent.originalImage || printingComponent.url,
          url: printingComponent.url || printingComponent.originalImage
        } : {
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'printingReferencePanel',
          name: '面料印花参考图片',
          url: ''
        },
        
        // 模特组件
        modelComponent ? {
          ...modelComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'modelPanel'
        } : {
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'modelPanel',
          useModel: false
        },
        
        // 提示词组件
        textDescriptionComponent ? {
          ...textDescriptionComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'textDescriptionPanel',
          name: '文字描述',
          status: 'completed',
          prompt: textDescriptionComponent.prompt || ''
        } : {
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'textDescriptionPanel',
          name: '文字描述', 
          status: 'completed',
          prompt: ''
        },
        
        // 随机种子组件
        seedComponent ? {
          ...seedComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'randomSeedSelector'
        } : {
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'randomSeedSelector',
          useRandom: false,
          value: task.seed !== undefined ? task.seed : (image.seed || -1)
        },
        
        // 强度设置组件
        weightComponent ? {
          ...weightComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'weightPanel',
          patternWeight: weightComponent.patternWeight !== undefined ? weightComponent.patternWeight : 0.5,
          printingWeight: weightComponent.printingWeight !== undefined ? weightComponent.printingWeight : 0.5
        } : {
          componentId: generateId(ID_TYPES.COMPONENT),
          componentType: 'weightPanel',
          patternWeight: 0.5,
          printingWeight: 0.5
        }
      ];
      
      console.log('准备的规范组件数组:', adaptedComponents);
      
      // 设置任务信息
      return {
        ...task,
        components: adaptedComponents
      }
      
      // 打开弹窗
    } catch (error) {
      console.error('处理查看图片详情时出错:', error);
      message.error('无法加载图片详情');
    }
  };

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);
    
    // 重置状态，无需延迟
    setSelectedImage(null);
    // 重置任务信息，避免保留旧任务导致重新打开时出错
    setImageDetailsTask(null);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);
    

    
    // 重置文本弹窗状态
    setShowAdvancedText(false);
  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');
      
      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;
    
    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;
    
    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;
    
    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }
    
    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };

  // 处理缩放变化
  const handleScaleChange = (newScale) => {
    setImageScale(newScale);
  };

  // 添加复位处理函数
  const handleReset = () => {
    setImageScale(initialScale);
    setImagePosition({ x: 0, y: 0 });
    lastPosition.current = { x: 0, y: 0 };
  };

  const handlePatternStatusChange = (panelId, newStatus) => {
    setPatternPanels(prevPanels => 
      prevPanels.map(panel => 
        panel.componentId === panelId 
          ? { ...panel, status: newStatus }
          : panel
      )
    );
  };

  // 处理图片上传
  const handleUpload = async (file, fileList) => {
    console.log('开始上传图片:', file.name);
    try {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      formData.append('workflow', 'upload');  // 上传类型
      formData.append('pageType', 'trending'); // 页面类型
      formData.append('imageType', 'clothing'); // 图片类型
      
      // 构建API URL
      const apiUrl = `${process.env.REACT_APP_BACKEND_URL}/process`;
      // console.log(`请求URL: ${apiUrl}`);
      
      // 发送上传请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData,
        headers: {
          // 添加Authorization头
          ...(localStorage.getItem('token') ? { 'Authorization': `Bearer ${localStorage.getItem('token')}` } : {})
        }
      });
      
      // 处理响应
      const data = await response.json();
      // console.log('上传响应状态:', response.status);
      
      if (response.ok && data && data.success) {
        console.log('上传成功，服务器返回:', data);
        // 解析返回的结果
        return data;
      } else {
        console.error('上传失败，服务器返回:', data);
        throw new Error(data.message || '图片上传失败');
      }
    } catch (error) {
      console.error('上传图片过程中出错:', error);
      throw error;
    }
  };

  // 检查任务状态
  const checkTaskStatus = async (taskId, abortSignal) => {
    try {
      // 检查是否需要中止操作
      if (abortSignal?.aborted) {
        return;
      }
      
      // 获取当前用户ID
      const userId = getCurrentUserId() || 'developer';
      
      // 使用taskService获取任务详情
      const task = await getTaskById(taskId, userId);
      
      // 再次检查是否需要中止操作
      if (abortSignal?.aborted) {
        return;
      }
      
      if (task) {
        if (task.status === 'completed') {
          // 任务完成，获取生成的图片
          setGeneratedImages(task.results || []);
          setIsGenerating(false);
          message.success('图片生成成功');
        } else if (task.status === 'failed') {
          // 任务失败
          setIsGenerating(false);
          message.error('生成失败: ' + (task.errorMessage || '未知错误'));
        } else {
          // 任务仍在进行中，继续轮询
          // 保存timeout ID以便可以清除
          const timeoutId = setTimeout(() => {
            if (!abortSignal?.aborted) {
              checkTaskStatus(taskId, abortSignal);
            }
          }, 3000);
          
          // 将timeout ID存储在abortSignal的引用中
          if (abortSignal) {
            abortSignal.timeoutId = timeoutId;
          }
        }
      } else {
        setIsGenerating(false);
        message.error('获取任务状态失败: 任务不存在');
      }
    } catch (error) {
      // 忽略中止错误
      if (error.name === 'AbortError') {
        console.log('任务状态检查已中止');
        return;
      }
      
      console.error('获取任务状态失败:', error);
      
      // 确保组件未卸载后再更新状态
      if (!abortSignal?.aborted) {
        setIsGenerating(false);
        message.error('获取任务状态失败: ' + (error.message || '未知错误'));
      }
    }
  };

  const [uploadedPatternId, setUploadedPatternId] = useState(null);
  const [uploadedPatternUrl, setUploadedPatternUrl] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState(null);
  const [generatedImages, setGeneratedImages] = useState([]);

  // 处理服装复色图片上传
  const handlePatternFileUpload = (file) => {
    console.log('版型参考图片上传:', file);
  };

  // 处理绘制蒙版
  const handleDrawMask = (panel) => {
    console.log('绘制蒙版:', panel);
    // 这里可以打开蒙版绘制弹窗组件
    // setShowMaskDrawModal(true);
    // setCurrentPanel(panel);
  };

  // 处理面板展开点击
  const handleExpandClick = (panel, position) => {
    console.log('面板展开:', panel.componentId, position);
    // 显示操作面板
    setOperationsPanel({
      panel,
      position
    });
  };

  // 处理面板改变
  const handlePanelChange = (panelId, data) => {
    console.log('面板改变:', panelId, data);
    // 这里可以实现面板数据变更的逻辑
  };

  // 处理面料图片上传结果
  const handlePatternUploadResult = (results) => {
    console.log('处理面料上传结果:', JSON.stringify(results, null, 2));
    
    // 直接复用服装上传结果处理函数
    handleUploadResult(results);
  };

  // 初始化和清理AbortController的useEffect
  useEffect(() => {
    // 创建一个新的AbortController
    taskAbortController.current = new AbortController();
    
    return () => {
      // 组件卸载时中止所有请求
      if (taskAbortController.current) {
        taskAbortController.current.abort();
        
        // 清除任何相关的timeout
        if (taskAbortController.current.signal.timeoutId) {
          clearTimeout(taskAbortController.current.signal.timeoutId);
        }
      }
    };
  }, []);

  // 处理模特选择
  const handleModelSelect = (model) => {
    setSelectedModel(model);
  };

  // 处理是否使用模特的变化
  const handleUseModelChange = (value) => {
    setUseModel(value);
  };

  // 图片详情显示相关函数
  const setImageDetails = (details) => {
    setSelectedImage(details);
  };

  const setShowImageDetail = (visible) => {
    setShowImageDetails(visible);
  };

  // 处理删除任务
  const handleDeleteTask = (taskId, skipConfirm = false) => {
    // 定义删除任务的函数
    const deleteTaskFunction = async () => {
      try {
        // 获取当前用户ID，如果未登录则使用开发者ID
        const userId = getCurrentUserId() || 'developer'; 
        
        // 显示操作中的消息
        message.loading({ content: '正在删除...', key: `delete-${taskId}` });
        
        // 先从本地状态中移除，提供即时反馈
        setGenerationTasks(prev => prev.filter(task => task.taskId !== taskId));
        
        // 调用API进行删除
        const success = await deleteTask(taskId, userId);
        
        if (success) {
          message.success({ content: '记录已删除', key: `delete-${taskId}` });
        } else {
          // 如果API返回失败但UI已更新，只在控制台记录警告
          console.warn('API删除任务可能失败，但前端已更新状态');
        }
      } catch (error) {
        console.error('删除任务失败:', error);
        message.error({ 
          content: '删除任务失败: ' + error.message,
          key: `delete-${taskId}`
        });
        
        // 即使API调用失败，仍从本地状态中移除
        setGenerationTasks(prev => prev.filter(task => task.taskId !== taskId));
      }
    };
    
    // 如果跳过确认，直接删除
    if (skipConfirm) {
      deleteTaskFunction();
      return;
    }
    
    // 否则显示确认对话框
    showDeleteConfirmModal({
      title: '确认删除',
      content: '确定要删除这条生成记录吗？删除后将无法恢复。',
      okText: '确认删除',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      animation: false,
      transitionName: '',
      maskTransitionName: '',
      onOk: deleteTaskFunction
    });
  };

  // 添加处理函数
  const handleTextDescriptionChange = (value) => {
    setTextDescription(value);
    setHasUnsavedChanges(true);
    console.log('文本描述已更新:', value);
  };

  // 在上传图片成功后更新权重和禁用状态
  useEffect(() => {
    // 根据是否有版型参考图设置权重和禁用状态
    if (patternPanels.length === 0) {
      setWeights(prev => ({ ...prev, item1: 0 }));
      setWeightDisabled(prev => ({ ...prev, item1: true }));
    } else {
      setWeights(prev => ({ ...prev, item1: 0.5 }));
      setWeightDisabled(prev => ({ ...prev, item1: false }));
    }
    
    // 根据是否有面料印花图设置权重和禁用状态
    if (printingPanels.length === 0) {
      setWeights(prev => ({ ...prev, item2: 0 }));
      setWeightDisabled(prev => ({ ...prev, item2: true }));
    } else {
      setWeights(prev => ({ ...prev, item2: 0.5 }));
      setWeightDisabled(prev => ({ ...prev, item2: false }));
    }
  }, [patternPanels.length, printingPanels.length]);

  const { updateTask } = useTaskContext();

  return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="爆款开发功能">
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <div className="trending-page">
        <div className="trending-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isGenerating}
            featureName="trending"
            quantity={imageQuantity}
          >
                    {/* 版型上传区域或版型面板 - 始终位于最上方 */}
                    {patternPanels.length === 0 ? (
                      <UploadBox
                        id="trending-pattern-upload-box"
                        onUpload={handleFileUpload}
                        onShowGuide={() => setShowUploadGuide(true)}
                        onUploadResult={handleUploadResult}
                        panels={patternPanels}
                        className="mt-2"
                        showSupportTag={false}
                        pageType="trending"
                        uploadType="pattern"
                      />
                    ) : (
                      // 展示服装面板
                      patternPanels.map((panel) => (
                        <PatternPanel
                          key={panel.componentId}
                          panel={{
                            ...panel,
                            title: panel.title || panel.name || '版型参考图片' // 确保有title属性
                          }}
                          onDelete={() => handleDeletePatternPanel(panel.componentId)}
                          onReupload={() => handleReuploadPattern(panel)}
                          onStatusChange={(newStatus) => handlePatternStatusChange(panel.componentId, newStatus)}
                          onExpandClick={handleExpandClick}
                          onPanelsChange={(data) => handlePanelChange(panel.componentId, data)}
                          status={panel.status}
                          onUploadResult={handleUploadResult}
                          pageType="trending"
                        />
                      ))
                    )}

                    {/* 面料印花上传区域或面料印花面板 */}
                    {printingPanels.length === 0 ? (
                      <UploadBox
                        id="trending-printing-upload-box"
                        onUpload={handlePrintingFileUpload}
                        onShowGuide={() => setShowPrintingUploadGuide(true)}
                        onUploadResult={handlePrintingUploadResult}
                        panels={printingPanels}
                        className="mt-2"
                        showSupportTag={false}
                        pageType="trending"
                        uploadType="printing"
                      />
                    ) : (
                      // 展示印花面板
                      printingPanels.map((panel) => (
                        <PrintingPanel
                          key={panel.componentId}
                          panel={{
                            ...panel,
                            title: panel.title || panel.name || '印花参考图片'
                          }}
                          onDelete={() => handleDeletePrintingPanel(panel.componentId)}
                          onReupload={() => handleReuploadPrinting(panel)}
                          onStatusChange={(newStatus) => handlePrintingStatusChange(panel.componentId, newStatus)}
                          onExpandClick={handleExpandClick}
                          onPanelsChange={(data) => handlePanelChange(panel.componentId, data)}
                          status={panel.status}
                          onUploadResult={handlePrintingUploadResult}
                          pageType="trending"
                        />
                      ))
                    )}

                    {/* 提示面板 - 当没有同时上传两种图片时显示 */}
                    {(patternPanels.length === 0 || printingPanels.length === 0) && (
                      <TipsPanel 
                        tipContent={
                          <div>
                            <p style={{margin: 0, lineHeight: 1.7}}>可以同时上传两种参考图片。也可以只上传其中一种，另一种在下方进行<span style={{ color: 'var(--brand-primary)' }}>文字描述</span>代替参考图片。</p>
                          </div>
                        }
                      />
                    )}

                    {/* 文字描述组件 */}
                    <TextDescriptionPanel
                      description={textDescription}
                      onChange={handleTextDescriptionChange}
                      placeholder="请输入服装描述，将用于生成更符合要求的爆款服装..."
                    />

                    {/* 无人模特组件 - 暂时隐藏 */}
                    {false && (
                      <ModelNobodyPanel
                        selectedModel={selectedModel}
                        onExpandClick={() => setShowModelSelect(true)}
                        useModel={useModel}
                        onUseModelChange={handleUseModelChange}
                        pageType="trending"
                      />
                    )}

                    {/* 强度调节组件 */}
                    <WeightPanel
                      weights={weights}
                      onChange={setWeights}
                      pageType="trending"
                      item1Label="版型"
                      item2Label="面料印花"
                      disabled1={weightDisabled.item1}
                      disabled2={weightDisabled.item2}
                    />

                    {/* 随机种子选择器 */}
                    <RandomSeedSelector
                      onRandomChange={setUseRandomSeed}
                      onSeedChange={setSeed}
                      defaultRandom={useRandomSeed}
                      defaultSeed={Number(seed)} // 确保是数字类型
                      // 编辑模式下传递历史种子
                      isEdit={selectedImage !== null}
                      editSeed={selectedImage?.seed || null}
                    />

                    {/* 数量面板 */}
                    <QuantityPanel 
                      imageQuantity={imageQuantity} 
                      onChange={setImageQuantity} 
                      min={1} 
                      max={4}
                      pageType="trending" 
                    />
          </ControlPanel>

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />

          <GenerationArea
          ref={generationAreaRef}
          setIsProcessing={setIsGenerating}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            tasks={Array.isArray(generationTasks) ? generationTasks : []}
            onEditTask={handleEditTask}
            onDownloadImage={handleDownloadImage}
            onViewDetails={handleViewDetails}
            onBatchDownload={handleBatchDownload}
            onDeleteTask={handleDeleteTask}
            pageType="trending"
          />
        </div>

        {/* 服装上传指南弹窗 */}
        {showUploadGuide && (
          <UploadGuideModal
            type="pattern"
            pageType="trending"
            onClose={() => setShowUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到上传结果:', result);
              handleUploadResult(result);
              
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowUploadGuide(false);
              }
            }}
          />
        )}

        {/* 面料印花上传指南弹窗 */}
        {extraPrintingUploadGuide && (
          <UploadGuideModal
            onClose={() => setExtraPrintingUploadGuide(false)}
            onUpload={handlePrintingUploadResult2}
            type="printing"
            pageType="trending"
            uploadType="printing"
          />
        )}

        {/* 面料印花上传指南弹窗 */}
        {showPrintingUploadGuide && (
          <UploadGuideModal
            onClose={() => setShowPrintingUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到风格图上传结果:', result);
              handlePrintingUploadResult(result);
              
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowPrintingUploadGuide(false);
              }
            }}
            type="printing"
            pageType="trending"
          />
        )}

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={(componentId) => {
              // 根据面板类型调用对应的删除函数
              if (operationsPanel.panel.type === 'printing') {
                handleDeletePrintingPanel(componentId);
              } else {
                handleDeletePatternPanel(componentId);
              }
            }}
            onReupload={(panel) => {
              // 根据面板类型调用对应的重新上传函数
              if (panel.type === 'printing') {
                handleReuploadPrinting(panel);
              } else {
                handleReuploadPattern(panel);
              }
            }}
            pageType="trending"
          />
        )}

        {/* 添加 ImageDetailsModal 组件 - 使用懒加载 */}
        {showImageDetails && selectedImage ? (
          <MemoizedImageDetailsModal
            selectedImage={selectedImage}
            onClose={handleCloseImageDetails}
            generationTasks={generationTasks}
            onEditTask={handleEditTask}
            pageType="trending"
          />
        ) : null}



        {/* 显示上传的版型图片和生成按钮 */}
        {uploadedPatternUrl && !isGenerating && (
          <div className="action-buttons">
            <Button 
              type="primary" 
              onClick={handleGenerate}
              disabled={isGenerating}
            >
              开始生成
            </Button>
          </div>
        )}
        
        {/* 显示生成中的加载状态 */}
        {isGenerating && (
          <div className="generating-status">
            <Spin size="large" />
            <p>正在生成中，请稍候...</p>
          </div>
        )}
        
        {/* 显示生成的图片 */}
        {generatedImages.length > 0 && (
          <div className="generated-images">
            <h3>生成结果</h3>
            <div className="image-grid">
              {generatedImages.map((image, index) => (
                <div key={index} className="image-item">
                  <img src={image.url} alt={`生成图片 ${index + 1}`} />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 添加模特选择弹窗 */}
        {showModelSelect && (
          <ModelSelectModal
            onClose={() => setShowModelSelect(false)}
            onSelect={handleModelSelect}
            selectedModelId={selectedModel?.componentId}
            savedSettings={customModelSettings}
            onSettingsChange={setCustomModelSettings}
          />
        )}
      </div>
    </RequireLogin>
  );
};

export default TrendingPage; 