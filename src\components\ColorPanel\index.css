/* 导入统一面板样式 */
@import '../../styles/panels.css';

/* 颜色预览区域样式 */
.color-preview-container {
  width: 88px;
  height: 88px;
  border-radius: 0;
  overflow: hidden;
  flex-shrink: 0;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-light);
}

.color-preview-box {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 保留旧类名以兼容性 */
.selected-color-preview {
  width: 88px;
  height: 88px;
  border-radius: 0;
  overflow: hidden;
  flex-shrink: 0;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-light);
}

.selected-color-thumbnail {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 颜色图标容器样式 */
.color-icon-container {
  width: 88px;
  height: 88px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border-right: 1px solid var(--border-light);
}

/* 用户提供的自定义图标样式 */
.color-icon {
  width: 88px; /* 适当调整大小 */
  height: 88px;
  object-fit: contain; /* 保持图标比例 */
}

/* React Icons图标样式 */
.color-picker-icon {
  color: var(--brand-primary);
} 