# 蒙版绘制功能标准化实现指南

## 概述

本文档详细说明了蒙版绘制功能的标准实现方式，基于 MaskDrawModal 组件。这个实现提供了完整的蒙版绘制、预览和保存功能，包括画笔工具、缩放控制和历史记录等特性。

## 核心功能

1. 蒙版绘制
2. 实时预览
3. 缩放和平移控制
4. 历史记录(撤销/重做)
5. 画笔大小调节
6. 黑白蒙版生成

## 标准数据结构

### 1. 组件属性
```javascript
MaskDrawModal.propTypes = {
  isOpen: PropTypes.bool,              // 控制弹窗显示
  panel: PropTypes.object,             // 当前图片面板数据
  onClose: PropTypes.func,             // 关闭回调
  onSaveMask: PropTypes.func,          // 保存蒙版回调
  pageType: PropTypes.string,          // 页面类型标识
  savePath: PropTypes.string.isRequired // 蒙版保存路径
};
```

### 2. 内部状态
```javascript
const [brushSize, setBrushSize] = useState(36);        // 画笔大小
const [activeTool, setActiveTool] = useState('brush'); // 当前工具
const [history, setHistory] = useState([]);            // 历史记录
const [historyIndex, setHistoryIndex] = useState(-1);  // 历史索引
const [scale, setScale] = useState(100);               // 缩放比例
const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 }); // 图片位置
```

## 关键实现

### 1. 蒙版绘制逻辑

```javascript
const renderMaskPreview = useCallback(() => {
  if (!canvasRef.current || !bufferCanvasRef.current) return;
  
  const canvas = canvasRef.current;
  const ctx = canvas.getContext('2d');
  const bufferCanvas = bufferCanvasRef.current;
  
  // 获取绘制区域
  const drawArea = actualDrawAreaRef.current;
  if (!drawArea) return;
  
  // 检查当前历史状态是否有效
  if (historyIndex >= 0 && historyIndex < history.length) {
    // 清除当前画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 获取缓冲画布的像素数据
    const bufferCtx = bufferCanvas.getContext('2d');
    
    // 绘制黑白蒙版
    const imageData = bufferCtx.getImageData(0, 0, bufferCanvas.width, bufferCanvas.height);
    const data = imageData.data;
    
    // 创建黑白蒙版图像数据
    const maskImageData = new ImageData(bufferCanvas.width, bufferCanvas.height);
    const maskData = maskImageData.data;
    
    // 将有笔迹的区域转为白色，其余区域为黑色
    for (let i = 0; i < data.length; i += 4) {
      if (data[i] > 0 || data[i+1] > 0 || data[i+2] > 0 || data[i+3] > 0) {
        // 有颜色的区域设为白色
        maskData[i] = 255;     // 红
        maskData[i+1] = 255;   // 绿
        maskData[i+2] = 255;   // 蓝
        maskData[i+3] = 255;   // 不透明
      } else {
        // 无颜色的区域设为黑色（完全不透明）
        maskData[i] = 0;       // 红
        maskData[i+1] = 0;     // 绿
        maskData[i+2] = 0;     // 蓝
        maskData[i+3] = 255;   // 不透明
      }
    }
    
    // 保存上下文状态
    ctx.save();
    
    // 设置裁剪区域，确保只显示图片区域内的蒙版
    ctx.beginPath();
    ctx.rect(drawArea.x, drawArea.y, drawArea.width, drawArea.height);
    ctx.clip();
    
    // 将蒙版数据绘制到画布上
    ctx.putImageData(maskImageData, 0, 0);
    
    // 恢复上下文状态
    ctx.restore();
  }
}, [history, historyIndex]);
```

### 2. 保存蒙版逻辑

```javascript
const handleSave = async () => {
  try {
    // 获取原始图片尺寸
    const originalSize = originalImageSizeRef.current;
    if (!originalSize) {
      console.error('未找到原始图片尺寸信息');
      return;
    }
    
    // 获取绘制区域
    const drawArea = actualDrawAreaRef.current;
    if (!drawArea) {
      console.error('未找到绘制区域信息');
      return;
    }
    
    // 创建临时canvas用于生成最终蒙版
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = originalSize.width;
    tempCanvas.height = originalSize.height;
    
    // 创建处理用的canvas
    const processCanvas = document.createElement('canvas');
    processCanvas.width = bufferCanvasRef.current.width;
    processCanvas.height = bufferCanvasRef.current.height;
    
    const tempCtx = tempCanvas.getContext('2d');
    const processCtx = processCanvas.getContext('2d');
    
    // 获取缓冲画布的像素数据
    const bufferCtx = bufferCanvasRef.current.getContext('2d');
    const bufferData = bufferCtx.getImageData(0, 0, bufferCanvasRef.current.width, bufferCanvasRef.current.height);
    const bufferPixels = bufferData.data;
    
    // 创建处理用的图像数据
    const processData = processCtx.createImageData(processCanvas.width, processCanvas.height);
    const processPixels = processData.data;
    
    // 将有笔迹的区域转为白色，其余区域为黑色
    for (let i = 0; i < bufferPixels.length; i += 4) {
      if (bufferPixels[i] > 0 || bufferPixels[i+1] > 0 || bufferPixels[i+2] > 0 || bufferPixels[i+3] > 0) {
        // 有颜色的区域设为白色
        processPixels[i] = 255;     // 红
        processPixels[i+1] = 255;   // 绿
        processPixels[i+2] = 255;   // 蓝
        processPixels[i+3] = 255;   // 不透明
      } else {
        // 无颜色的区域设为黑色（重要：alpha值为255，表示不透明的黑色）
        processPixels[i] = 0;       // 红
        processPixels[i+1] = 0;     // 绿
        processPixels[i+2] = 0;     // 蓝
        processPixels[i+3] = 255;   // 不透明黑色（而非透明）
      }
    }
    
    // 填充整个临时画布为黑色背景
    tempCtx.fillStyle = 'black';
    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
    
    // 将处理后的像素数据绘制到中间canvas上
    processCtx.putImageData(processData, 0, 0);
    
    // 从中间canvas的绘制区域裁剪并缩放到最终与原图匹配的尺寸
    tempCtx.drawImage(
      processCanvas,
      drawArea.x, drawArea.y, drawArea.width, drawArea.height, // 源矩形 - 只获取图片区域
      0, 0, originalSize.width, originalSize.height            // 目标矩形 - 缩放到原始图片尺寸
    );
    
    // 生成PNG格式的蒙版图像数据URL
    const maskDataUrl = tempCanvas.toDataURL('image/png');
    
    // 调用保存回调，传递蒙版数据和动态路径
    onSaveMask(maskDataUrl, panel.componentId, savePath);
    onClose();
  } catch (error) {
    console.error('生成蒙版时出错:', error);
    onClose();
  }
};
```

## 使用方法

### 1. 基本用法
```javascript
import MaskDrawModal from '../components/MaskDrawModal';

// 在组件中使用
const MyComponent = () => {
  const handleSaveMask = (maskDataUrl, componentId, savePath) => {
    // 处理保存蒙版的逻辑
  };
  
  return (
    <MaskDrawModal
      isOpen={showMaskDraw}
      panel={currentPanel}
      onClose={() => setShowMaskDraw(false)}
      onSaveMask={handleSaveMask}
      pageType="matting"
      savePath="/path/to/save"
    />
  );
};
```

### 2. 注意事项

1. **必须提供savePath**：
   - savePath属性是必需的，用于指定蒙版保存的位置
   - 如果未提供，将无法保存蒙版

2. **画布尺寸**：
   - 画布尺寸会根据原始图片尺寸自动调整
   - 保存时会自动缩放到原始图片尺寸

3. **性能优化**：
   - 使用useCallback和useRef优化性能
   - 避免不必要的重渲染

4. **错误处理**：
   - 添加适当的错误处理和日志记录
   - 确保即使出错也能正常关闭弹窗

## 总结

这个标准化实现提供了完整的蒙版绘制功能，包括：
- 完整的绘制工具
- 实时预览功能
- 标准的黑白蒙版生成
- 完善的错误处理
- 性能优化措施

其他页面在实现类似功能时，应参考这个标准实现，确保功能的一致性和可维护性。 