import React, { useState } from 'react';
import { Layout, Menu, theme, <PERSON><PERSON><PERSON>rumb, Button, Avatar, Dropdown, Space, Tooltip } from 'antd';
import './AdminPage.css';
import { 
  UserOutlined, 
  DashboardOutlined, 
  SettingOutlined, 
  CreditCardOutlined,
  LogoutOutlined,
  BellOutlined,
  QuestionCircleOutlined,
  DownOutlined,
  HomeOutlined,
  CloudServerOutlined,
  ApiOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import UserManagement from '../../pages/admin/user/UserManagement';
import SubscriptionManagement from '../../pages/admin/subscription/SubscriptionManagement';
import { useAuth } from '../../contexts/AuthContext';
import UserCreditManagement from '../../pages/admin/user/UserCreditManagement';
import InstanceManagement from '../../pages/admin/instance/InstanceManagement';
import RunningHubManagement from '../../pages/admin/instance/RunningHubManagement';
import WorkflowManagement from '../../pages/admin/workflow/WorkflowManagement';
import AnnouncementManagement from '../../pages/admin/announcement/AnnouncementManagement';
const { Header, Content, Footer, Sider } = Layout;
const { SubMenu } = Menu;

const AdminPage = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const auth = useAuth();
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  // 获取当前路径的最后一部分作为当前页面
  const currentPage = location.pathname.split('/').pop() || 'dashboard';

  // 根据路径渲染对应的组件
  const renderContent = () => {
    switch (currentPage) {
      case 'users':
        return <UserManagement />;
      case 'announcement':
        return <AnnouncementManagement />;
      case 'subscription':
        return <SubscriptionManagement />;
      case 'credits':
        return <UserCreditManagement />;
      case 'instances':
        return <InstanceManagement />;
      case 'runninghub':
        return <RunningHubManagement />;
      case 'workflows':
        return <WorkflowManagement />;
      case 'dashboard':
      default:
        return <div>仪表盘内容</div>;
    }
  };

  // 处理菜单点击
  const handleMenuClick = (e) => {
    navigate(`/admin/${e.key}`);
  };

  // 处理退出登录
  const handleLogout = () => {
    auth.logout();
    navigate('/');
  };

  // 返回首页
  const handleBackToHome = () => {
    navigate('/');
  };

  // 用户下拉菜单项
  const userMenuItems = [
    {
      key: 'profile',
      label: '个人资料',
      icon: <UserOutlined />,
      onClick: () => navigate('/admin/profile')
    },
    {
      key: 'settings',
      label: '账户设置',
      icon: <SettingOutlined />,
      onClick: () => navigate('/admin/settings')
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: handleLogout
    }
  ];

  return (
    <Layout className="admin-layout">
      <Sider collapsible collapsed={collapsed} onCollapse={(value) => setCollapsed(value)}>
        <div className="admin-logo">
          {!collapsed ? 'AI 时尚管理' : 'AI'}
        </div>
        <Menu 
          theme="dark" 
          defaultSelectedKeys={[currentPage]} 
          mode="inline"
          onClick={handleMenuClick}
        >
          <Menu.Item key="dashboard" icon={<DashboardOutlined />}>
            仪表盘
          </Menu.Item>
          <Menu.Item key="announcement" icon={<UserOutlined />}>  
            公告管理
          </Menu.Item>
          <Menu.Item key="users" icon={<UserOutlined />}>
            用户管理
          </Menu.Item>
          <Menu.Item key="subscription" icon={<CreditCardOutlined />}>
            订阅管理
          </Menu.Item>
          <Menu.Item key="credits" icon={<CreditCardOutlined />}>
            算力管理
          </Menu.Item>
          <Menu.Item key="instances" icon={<CloudServerOutlined />}>
            实例管理
          </Menu.Item>
          <Menu.Item key="runninghub" icon={<ApiOutlined />}>
            RunningHub管理
          </Menu.Item>
          <Menu.Item key="workflows" icon={<ApartmentOutlined />}>
            工作流管理
          </Menu.Item>
          <SubMenu key="settings" icon={<SettingOutlined />} title="系统设置">
            <Menu.Item key="general">基本设置</Menu.Item>
            <Menu.Item key="security">安全设置</Menu.Item>
          </SubMenu>
        </Menu>
      </Sider>
      <Layout className="site-layout">
        <Header className="admin-header" style={{ background: colorBgContainer }}>
          <div className="header-left">
            <h2>AI 时尚设计平台管理后台</h2>
          </div>
          <div className="header-right">
            <Tooltip title="返回首页">
              <Button 
                type="primary" 
                icon={<HomeOutlined />} 
                onClick={handleBackToHome}
                className="back-home-btn"
              >
                返回首页
              </Button>
            </Tooltip>
            <Button type="text" icon={<BellOutlined />} />
            <Button type="text" icon={<QuestionCircleOutlined />} />
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Space className="user-dropdown">
                <Avatar icon={<UserOutlined />} />
                <span>{auth.user?.name || '管理员'}</span>
                <DownOutlined />
              </Space>
            </Dropdown>
            <Button 
              type="primary" 
              danger 
              icon={<LogoutOutlined />}  
              onClick={handleLogout}
              className="logout-btn"
            >
              退出登录
            </Button>
          </div>
        </Header>
        <Content className="content-wrapper">
          <Breadcrumb style={{ margin: '16px 0' }}>
            <Breadcrumb.Item>管理后台</Breadcrumb.Item>
            <Breadcrumb.Item>
              {currentPage === 'dashboard' && '仪表盘'}
              {currentPage === 'users' && '用户管理'}
              {currentPage === 'subscription' && '订阅管理'}
              {currentPage === 'instances' && '实例管理'}
              {currentPage === 'general' && '基本设置'}
              {currentPage === 'security' && '安全设置'}
            </Breadcrumb.Item>
          </Breadcrumb>
          <div className="content-container" style={{ background: colorBgContainer }}>
            {renderContent()}
          </div>
        </Content>
        <Footer className="admin-footer">AI 时尚设计平台 ©2023</Footer>
      </Layout>
    </Layout>
  );
};

export default AdminPage; 