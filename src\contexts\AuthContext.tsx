import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import logger from '../utils/logger';
import api from '../api';
import { refreshCSRFToken, clearCSRFToken } from '../api/security/csrf';
import websocketUtils from '../utils/websocketUtils';

// 修改 API 基础路径
const API_BASE_URL = process.env.REACT_APP_BACKEND_URL;  // 添加 /api 前缀

// 定义登录安全配置
const LOGIN_SECURITY = {
  MAX_ATTEMPTS: 5,           // 最大尝试次数
  LOCKOUT_DURATION: 15 * 60 * 1000, // 锁定时长(15分钟，单位毫秒)
  STORAGE_KEY: 'login_attempts',
  LOGIN_HISTORY_KEY: 'login_history' // 登录历史记录的存储键
};

// 用户接口定义
export interface User {
  id: string;
  username: string;
  phone?: string;
  avatar?: string;
  role?: string;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: unknown; // 允许其他属性
}

// 登录历史接口定义
export interface LoginHistory {
  lastLoginTime: string;
  lastLoginIp: string;
  currentLoginTime: string;
  currentLoginIp: string;
}

// 登录尝试接口定义
export interface LoginAttempt {
  count: number;
  lastAttempt: number;
  lockUntil: number | null;
}

// 异常登录检查结果接口
export interface AbnormalLoginCheck {
  isAbnormal: boolean;
  message: string;
  lastLoginIp?: string;
  lastLoginTime?: string;
  currentIp?: string;
}

// 登录结果接口
export interface LoginResult {
  success: boolean;
  error?: string;
  message?: string;
  isLocked?: boolean;
  remainingMinutes?: number;
  abnormalLogin?: boolean;
  abnormalMessage?: string;
  lastLoginInfo?: {
    ip: string;
    time: string;
    userAgent: string;
  };
  user?: User;
}

// 注册参数接口
export interface RegisterParams {
  username: string;
  phone: string;
  password: string;
}

// 注册结果接口
export interface RegisterResult {
  success: boolean;
  error?: string;
}

// 验证码相关接口
export interface VerificationResult {
  success: boolean;
  error?: string;
  data?: unknown;
}

// 错误类型接口
export interface ErrorType {
  message?: string;
  error?: unknown;
}

// 密码修改结果接口
export interface ResetPasswordResult {
  success: boolean;
  error?: string;
  message?: string;
}

// AuthContext接口定义
export interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: ErrorType | null;
  setUser: (user: User | null) => void;
  login: (loginId: string, password: string) => Promise<LoginResult>;
  register: (userData: RegisterParams) => Promise<RegisterResult>;
  logout: () => void;
  getCurrentUser: () => Promise<void>;
  sendVerificationCode: (phone: string) => Promise<VerificationResult>;
  verifyCode: (phone: string, code: string, type?: string) => Promise<VerificationResult>;
  resetPassword: (phone: string, code: string, newPassword: string) => Promise<ResetPasswordResult>;
  getRemainingLockoutTime: (loginId: string) => number;
  clearLoginAttempts: (loginId: string) => void;
  getLoginHistory: (loginId: string) => LoginHistory | null;
  checkAbnormalLogin: (loginId: string, ipAddress: string) => AbnormalLoginCheck;
  checkAuth: () => Promise<boolean>; // 从可选变为必需
}

// 默认AuthContext
const defaultAuthContext: AuthContextType = {
  user: null,
  loading: true,
  error: null,
  setUser: () => {},
  login: async () => ({ success: false }),
  register: async () => ({ success: false }),
  logout: () => {},
  getCurrentUser: async () => {},
  sendVerificationCode: async () => ({ success: false }),
  verifyCode: async () => ({ success: false }),
  resetPassword: async () => ({ success: false }),
  getRemainingLockoutTime: () => 0,
  clearLoginAttempts: () => {},
  getLoginHistory: () => null,
  checkAbnormalLogin: () => ({ isAbnormal: false, message: '' }),
  checkAuth: async () => false,
};

// 创建认证上下文
export const AuthContext = createContext<AuthContextType>(defaultAuthContext);

// AuthProvider Props接口
interface AuthProviderProps {
  children: ReactNode;
}

// 认证提供者组件
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<ErrorType | null>(null);

  // 初始化用户状态
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        await getCurrentUser();
      } catch (err) {
        setError({ message: "获取用户信息失败", error: err });
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // 添加定期刷新CSRF令牌的机制
  useEffect(() => {
    // 只有在用户已登录时才刷新令牌
    if (!user) return;

    // 30分钟刷新一次令牌
    const refreshInterval = 30 * 60 * 1000; // 30分钟

    const intervalId = setInterval(() => {
      refreshCSRFToken();
      logger.debug('CSRF令牌已刷新');
    }, refreshInterval);

    // 清理函数
    return () => {
      clearInterval(intervalId);
    };
  }, [user]);

  // 获取登录尝试信息
  const getLoginAttempts = (loginId: string): LoginAttempt | null => {
    try {
      const attemptsData = localStorage.getItem(LOGIN_SECURITY.STORAGE_KEY);
      if (!attemptsData) return null;

      const attempts = JSON.parse(attemptsData);
      return loginId ? attempts[loginId] : attempts;
    } catch (error: any) {
      logger.error('获取登录尝试信息失败', error);
      return null;
    }
  };

  // 更新登录尝试信息
  const updateLoginAttempts = (loginId: string, isSuccess: boolean): void => {
    try {
      const now = Date.now();
      const attemptsData = localStorage.getItem(LOGIN_SECURITY.STORAGE_KEY);
      let attempts: Record<string, LoginAttempt> = attemptsData ? JSON.parse(attemptsData) : {};

      // 如果是新用户或成功登录，重置或初始化记录
      if (isSuccess || !attempts[loginId]) {
        attempts[loginId] = {
          count: 0,
          lastAttempt: now,
          lockUntil: null
        };
      } else {
        // 失败登录，增加计数
        const userAttempts = attempts[loginId];
        userAttempts.count += 1;
        userAttempts.lastAttempt = now;

        // 如果达到最大尝试次数，设置锁定时间
        if (userAttempts.count >= LOGIN_SECURITY.MAX_ATTEMPTS) {
          userAttempts.lockUntil = now + LOGIN_SECURITY.LOCKOUT_DURATION;
          logger.warn('账户被锁定', { loginId, lockUntil: new Date(userAttempts.lockUntil) });
        }

        attempts[loginId] = userAttempts;
      }

      localStorage.setItem(LOGIN_SECURITY.STORAGE_KEY, JSON.stringify(attempts));
    } catch (error: any) {
      logger.error('更新登录尝试信息失败', error);
    }
  };

  // 检查账户是否被锁定
  const isAccountLocked = (loginId: string): boolean => {
    try {
      const attempts = getLoginAttempts(loginId);
      if (!attempts) return false;

      const now = Date.now();
      if (attempts.lockUntil && attempts.lockUntil > now) {
        return true;
      }

      // 如果锁定时间已过，清除锁定状态
      if (attempts.lockUntil) {
        updateLoginAttempts(loginId, true);
      }

      return false;
    } catch (error: any) {
      logger.error('检查账户锁定状态失败', error);
      return false;
    }
  };

  // 获取剩余锁定时间（分钟）
  const getRemainingLockoutTime = (loginId: string): number => {
    try {
      const attempts = getLoginAttempts(loginId);
      if (!attempts || !attempts.lockUntil) return 0;

      const now = Date.now();
      const remainingMs = Math.max(0, attempts.lockUntil - now);
      return Math.ceil(remainingMs / (60 * 1000)); // 转换为分钟并向上取整
    } catch (error: any) {
      logger.error('获取剩余锁定时间失败', error);
      return 0;
    }
  };

  // 清除登录尝试记录
  const clearLoginAttempts = (loginId: string): void => {
    try {
      if (!loginId) return;

      const attemptsData = localStorage.getItem(LOGIN_SECURITY.STORAGE_KEY);
      if (!attemptsData) return;

      const attempts = JSON.parse(attemptsData);
      if (attempts[loginId]) {
        delete attempts[loginId];
        localStorage.setItem(LOGIN_SECURITY.STORAGE_KEY, JSON.stringify(attempts));
      }
    } catch (error: any) {
      logger.error('清除登录尝试记录失败', error);
    }
  };

  // 添加验证函数
  const validateUsername = (username: string): string | null => {
    if (!username) {
      return '请输入用户名';
    }
    // 检查长度
    if (username.length < 4 || username.length > 12) {
      return '用户名长度必须在4-12位之间';
    }
    // 检查是否为纯数字
    if (/^\d+$/.test(username)) {
      return '用户名不能为纯数字';
    }
    // 检查字符类型
    if (!/^[\u4e00-\u9fa5a-zA-Z0-9]*$/.test(username)) {
      return '用户名只能包含中文、英文、数字';
    }
    return null;
  };

  const validatePhone = (phone: string): string | null => {
    const regex = /^1[3-9]\d{9}$/;
    if (!phone) {
      return '请输入手机号';
    }
    if (!regex.test(phone)) {
      return '请输入有效的手机号';
    }
    return null;
  };

  const validateLoginId = (loginId: string): string | null => {
    if (!loginId) {
      return '请输入用户名或手机号';
    }
    // 检查是否为手机号或用户名格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    const usernameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9]{2,12}$/;
    if (!phoneRegex.test(loginId) && !usernameRegex.test(loginId)) {
      return '请输入有效的用户名或手机号';
    }
    return null;
  };

  // 获取用户的登录历史记录
  const getLoginHistory = (loginId: string): LoginHistory | null => {
    try {
      const historyData = localStorage.getItem(LOGIN_SECURITY.LOGIN_HISTORY_KEY);
      if (historyData) {
        const history = JSON.parse(historyData);
        return history[loginId] || null;
      }
      return null;
    } catch (error: any) {
      logger.error('获取登录历史记录失败', error);
      return null;
    }
  };

  // 保存登录历史记录
  const saveLoginHistory = (loginId: string, ipAddress: string): LoginHistory | null => {
    try {
      const now = new Date().toISOString();
      const historyData = localStorage.getItem(LOGIN_SECURITY.LOGIN_HISTORY_KEY);
      let history: Record<string, LoginHistory> = {};

      if (historyData) {
        history = JSON.parse(historyData);
      }

      // 保存当前登录信息
      history[loginId] = {
        lastLoginTime: now,
        lastLoginIp: ipAddress,
        currentLoginTime: now,
        currentLoginIp: ipAddress
      };

      localStorage.setItem(LOGIN_SECURITY.LOGIN_HISTORY_KEY, JSON.stringify(history));
      return history[loginId];
    } catch (error: any) {
      logger.error('保存登录历史记录失败', error);
      return null;
    }
  };

  // 更新登录历史记录
  const updateLoginHistory = (loginId: string, ipAddress: string): LoginHistory | null => {
    try {
      const historyData = localStorage.getItem(LOGIN_SECURITY.LOGIN_HISTORY_KEY);
      let history: Record<string, LoginHistory> = {};

      if (historyData) {
        history = JSON.parse(historyData);
      }

      const now = new Date().toISOString();

      // 如果有历史记录，则更新
      if (history[loginId]) {
        history[loginId] = {
          lastLoginTime: history[loginId].currentLoginTime,
          lastLoginIp: history[loginId].currentLoginIp,
          currentLoginTime: now,
          currentLoginIp: ipAddress
        };
      } else {
        // 如果没有历史记录，则创建新记录
        history[loginId] = {
          lastLoginTime: now,
          lastLoginIp: ipAddress,
          currentLoginTime: now,
          currentLoginIp: ipAddress
        };
      }

      localStorage.setItem(LOGIN_SECURITY.LOGIN_HISTORY_KEY, JSON.stringify(history));
      return history[loginId];
    } catch (error: any) {
      logger.error('更新登录历史记录失败', error);
      return null;
    }
  };

  // 检查是否为异常登录
  const checkAbnormalLogin = (loginId: string, ipAddress: string): AbnormalLoginCheck => {
    try {
      const loginHistory = getLoginHistory(loginId);

      // 如果没有登录历史，则不是异常登录
      if (!loginHistory) {
        return {
          isAbnormal: false,
          message: '首次登录'
        };
      }

      // 检查IP地址是否变化
      if (loginHistory.lastLoginIp && loginHistory.lastLoginIp !== ipAddress) {
        return {
          isAbnormal: true,
          message: `检测到异常登录！上次登录IP: ${loginHistory.lastLoginIp}，当前IP: ${ipAddress}`,
          lastLoginIp: loginHistory.lastLoginIp,
          lastLoginTime: loginHistory.lastLoginTime,
          currentIp: ipAddress
        };
      }

      return {
        isAbnormal: false,
        message: '正常登录'
      };
    } catch (error: any) {
      logger.error('检查异常登录失败', error);
      return {
        isAbnormal: false,
        message: '无法检查登录状态'
      };
    }
  };

  // 获取当前IP地址
  const getCurrentIpAddress = async (): Promise<string> => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error: any) {
      logger.error('获取IP地址失败', error);
      return '未知IP';
    }
  };

  // 修改注册函数
  const register = async (userData: RegisterParams): Promise<RegisterResult> => {
    setLoading(true);
    try {
      // 验证用户名和手机号
      const usernameError = validateUsername(userData.username);
      if (usernameError) {
        return { success: false, error: usernameError };
      }

      const phoneError = validatePhone(userData.phone);
      if (phoneError) {
        return { success: false, error: phoneError };
      }

      // 使用增强的API函数发送注册请求
      const response = await api.post('/auth/register', userData, '用户注册');

      // 处理响应
      if (response && response.data && response.data.token) {
        const { token, user } = response.data;

        localStorage.setItem('token', token);
        setUser(user);

        return { success: true };
      } else {
        return {
          success: false,
          error: response?.message || '注册失败，请稍后重试'
        };
      }
    } catch (error: unknown) {
      const err = error as Error;
      logger.error('注册失败', error);
      return {
        success: false,
        error: err.message || '注册失败，请稍后重试',
      };
    } finally {
      setLoading(false);
    }
  };

  // 修改登录函数
  const login = async (loginId: string, password: string): Promise<LoginResult> => {
    setLoading(true);
    try {
      logger.debug('登录函数开始执行');
      logger.debug('登录参数', { loginId, passwordLength: password?.length });

      if (!loginId || !password) {
        logger.warn('登录参数不完整');
        return {
          success: false,
          message: '请输入用户名和密码'
        };
      }

      // 验证登录ID
      const loginIdError = validateLoginId(loginId);
      if (loginIdError) {
        logger.debug('登录ID验证失败:', loginIdError);
        return {
          success: false,
          message: loginIdError
        };
      }

      // 验证密码不为空
      if (!password.trim()) {
        logger.debug('密码验证失败：密码为空');
        return {
          success: false,
          error: '请输入密码'
        };
      }

      // 检查账户是否被锁定
      if (isAccountLocked(loginId)) {
        const remainingMinutes = getRemainingLockoutTime(loginId);
        logger.warn('账户已被锁定', { loginId, remainingMinutes });
        return {
          success: false,
          message: `账户已被临时锁定，请${remainingMinutes}分钟后再试`,
          isLocked: true,
          remainingMinutes
        };
      }

      logger.debug('开始发送请求..');
      // 使用增强的API函数发送登录请求
      const response = await api.post('/auth/login', {
        loginId,
        password,
      }, '登录');

      logger.debug('收到服务器响应');
      logger.debug('响应数据:', response);

      // 确保响应数据的格式正确
      if (response && response.success) {
        // 正确处理嵌套的数据结构
        const { token, user, lastLoginInfo } = response.data || {};
        logger.debug('登录成功');
        logger.debug('Token存在:', !!token);
        logger.debug('用户信息存在:', !!user);
        logger.debug('响应数据结构:', JSON.stringify(response));

        if (!token || !user) {
          logger.error('响应缺少token或user信息');
          // 记录失败尝试
          updateLoginAttempts(loginId, false);
          return {
            success: false,
            error: '服务器响应缺少必要信息'
          };
        }

        // 保存token
        localStorage.setItem('token', token);

        // 保存用户信息
        localStorage.setItem('user', JSON.stringify(user));

        // 更新状态
        setUser(user);

        // 设置token过期时间（7天后过期）
        const expireDate = new Date();
        expireDate.setDate(expireDate.getDate() + 7);
        localStorage.setItem('tokenExpires', expireDate.toISOString());

        logger.info('登录成功', { username: loginId });

        // 清除失败尝试记录
        updateLoginAttempts(loginId, true);

        // 生成新的CSRF令牌
        refreshCSRFToken();

        // 如果有上次登录信息，检查是否为异常登录
        if (lastLoginInfo) {
          return {
            success: true,
            abnormalLogin: true,
            abnormalMessage: `上次登录IP: ${lastLoginInfo.ip}，时间: ${new Date(lastLoginInfo.time).toLocaleString()}，设备: ${lastLoginInfo.userAgent}`,
            lastLoginInfo,
            user
          };
        }

        return {
          success: true,
          user
        };
      }

      // 处理失败情况
      logger.debug('登录失败，服务器消息:', response?.message);

      // 记录失败尝试
      updateLoginAttempts(loginId, false);

      return {
        success: false,
        message: response?.message || '登录失败，请检查账号和密码'
      };
    } catch (error: any) {
      logger.error('登录过程发生错误', error);
      return {
        success: false,
        error: error.message || '登录失败，请稍后重试'
      };
    } finally {
      setLoading(false);
    }
  };

  // 修改注销函数，清除CSRF令牌并关闭WebSocket连接
  const logout = (): void => {
    // 关闭WebSocket连接
    websocketUtils.handleUserLogout();
    
    // 清除本地存储
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('tokenExpires');
    
    // 清除CSRF令牌
    clearCSRFToken();
    
    // 重定向到首页
    window.location.href = '/';
  };

  // 获取当前用户信息
  const getCurrentUser = async (): Promise<void> => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setLoading(false);
        return;
      }

      // 检查token是否过期
      const tokenExpires = localStorage.getItem('tokenExpires');
      if (tokenExpires) {
        const expireDate = new Date(tokenExpires);
        const now = new Date();
        if (now > expireDate) {
          // token已过期，清除并返回
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          localStorage.removeItem('tokenExpires');
          setUser(null);
          setLoading(false);
          return;
        }
      }

      // 如果是开发者用户，直接从localStorage获取
      if (token.startsWith('dev_token_')) {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          try {
            const user = JSON.parse(userStr);
            setUser(user);
          } catch (err) {
            logger.error('解析本地用户数据失败', err);
          }
          setLoading(false);
          return;
        }
      }

      // 从本地存储先恢复用户状态
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const localUser = JSON.parse(userStr);
          setUser(localUser);
        } catch (err) {
          logger.error('解析本地用户数据失败', err);
        }
      }

      // 正常用户，使用增强的API函数获取用户信息
      try {
        const response = await api.get('/auth/me', {}, '获取用户信息');

        // 如果API请求成功，更新用户信息
        if (response && response.success) {
          // 正确处理嵌套的数据结构
          const userData = response.data?.user;

          if (userData) {
            // 保存用户信息 - 确保只保存user对象
            localStorage.setItem('user', JSON.stringify(userData));
            setUser(userData);
            logger.debug('从服务器获取用户信息成功，已更新', userData);
          } else {
            logger.warn('服务器返回的用户数据格式不正确', response);
          }
        } else {
          logger.warn('服务器响应不成功或格式不正确', response);
        }
      } catch (err) {
        logger.error('获取用户信息失败，但继续使用本地缓存', err);
        // 对于API错误，我们不清除本地用户数据
        // 因为可能只是暂时的网络问题，保留本地数据可以继续使用
      }

      // 设置或更新token过期时间（7天后过期）
      if (!tokenExpires) {
        const expireDate = new Date();
        expireDate.setDate(expireDate.getDate() + 7);
        localStorage.setItem('tokenExpires', expireDate.toISOString());
      }

      setLoading(false);
    } catch (error) {
      logger.error('用户认证初始化失败', error);
      // 只有在严重错误时才清除本地数据
      setUser(null);
      setLoading(false);
    }
  };

  const sendVerificationCode = async (phone: string): Promise<VerificationResult> => {
    try {
      // 使用增强的API函数发送验证码
      await api.post('/auth/send-verification-code', { phone }, '发送验证码');
      return { success: true };
    } catch (error: unknown) {
      const err = error as Error;
      return {
        success: false,
        error: err.message || '发送验证码失败',
      };
    }
  };

  // 重置密码函数
  const resetPassword = async (phone: string, verificationCode: string, newPassword: string): Promise<ResetPasswordResult> => {
    try {
      // 使用增强的API函数修改密码
      const response = await api.post('/auth/reset-password', {
        phone,
        verificationCode,
        newPassword
      }, '修改密码');

      // 修改密码成功后刷新CSRF令牌
      refreshCSRFToken();

      return {
        success: true,
        message: response.message || '密码修改成功'
      };
    } catch (error: unknown) {
      const err = error as Error;
      logger.error('修改密码失败', error);
      return {
        success: false,
        error: err.message || '修改密码失败',
        message: err.message || '修改密码失败，请稍后重试'
      };
    }
  };

  const verifyCode = async (phone: string, code: string, type?: string): Promise<VerificationResult> => {
    try {
      // 使用增强的API函数验证验证码
      const response = await api.post('/auth/verify-code', {
        phone,
        code,
        type
      }, '验证验证码');
      return { success: true, data: response };
    } catch (error: unknown) {
      const err = error as Error;
      return {
        success: false,
        error: err.message || '验证码验证失败',
      };
    }
  };

  // 检查用户认证状态
  const checkAuth = async (): Promise<boolean> => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        logger.debug('检查认证状态 无token');
        setUser(null);
        return false;
      }

      // 使用增强的API函数检查认证状态
      const response = await api.get('/auth/me', {}, '检查认证状态');

      // 正确处理嵌套的数据结构
      if (response && response.success && response.data?.user) {
        const userData = response.data.user;
        setUser(userData);
        // 更新本地存储
        localStorage.setItem('user', JSON.stringify(userData));
        setLoading(false);

        logger.debug('检查认证状态', { isAuthenticated: true, userData });
        return true;
      } else {
        logger.warn('认证检查返回无效数据', response);
        setUser(null);
        return false;
      }
    } catch (error) {
      logger.error('检查认证状态失败', error);
      setUser(null);
      return false;
    }
  };

  const value = {
    user,
    loading,
    error,
    setUser,
    login,
    register,
    logout,
    getCurrentUser,
    sendVerificationCode,
    verifyCode,
    resetPassword,
    getRemainingLockoutTime,
    clearLoginAttempts,
    getLoginHistory,
    checkAbnormalLogin,
    checkAuth, // 添加 checkAuth 方法到 context value
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// 自定义Hook，用于在组件中使用认证上下文
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
