/**
 * 格式化时间戳为可读字符串
 * @param {string|number|Date} timestamp 时间戳或日期对象
 * @returns {string} 格式化后的日期字符串，格式为 YYYY-MM-DD HH:MM
 */
export const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-';
  
  const date = new Date(timestamp);
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return '-';
  
  // 格式化年月日
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  // 格式化时分
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  // 返回格式化后的字符串
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

/**
 * 格式化文件大小为可读字符串
 * @param {number} bytes 文件大小（字节）
 * @returns {string} 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  if (!bytes || isNaN(bytes)) return '-';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
};

/**
 * 格式化数字为千分位分隔的字符串
 * @param {number} num 数字
 * @returns {string} 格式化后的数字字符串
 */
export const formatNumber = (num) => {
  if (num === undefined || num === null) return '-';
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}; 