@REM @echo off

@REM taskkill /F /IM node.exe >nul 2>&1
@REM timeout /t 2 /nobreak >nul

@REM rd /s /q node_modules\.cache >nul 2>&1
@REM del /f /q .env.local >nul 2>&1

@REM set BROWSER=none
@REM set PORT=3001
@REM set WDS_SOCKET_PORT=3001
@REM set FAST_REFRESH=true
@REM set CHOKIDAR_USEPOLLING=true
@REM set WATCHPACK_POLLING=true
@REM set REACT_APP_DISABLE_LIVE_RELOAD=false
@REM set GENERATE_SOURCEMAP=false
@REM set DANGEROUSLY_DISABLE_HOST_CHECK=true

@REM cd server
@REM start cmd /c "set PORT=3002 && npm start"
@REM cd ..
@REM timeout /t 5 /nobreak >nul

@REM start cmd /c "npm start"
@REM timeout /t 3 /nobreak >nul

@REM start http://localhost:3001 