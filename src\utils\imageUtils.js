// 添加图片信息缓存
const imageInfoCache = new Map();

/**
 * 通过URL获取图片的实际文件信息（最准确的方式）
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<Object>} 包含图片实际大小、尺寸和格式信息的对象
 */
export const getActualImageInfo = async (imageUrl) => {
  if (!imageUrl) {
    console.warn('获取图片信息失败: URL为空');
    return null;
  }

  // 检查缓存中是否已有此图片信息
  const cacheKey = imageUrl.split('?')[0]; // 忽略查询参数
  if (imageInfoCache.has(cacheKey)) {
    console.log('从缓存获取图片信息:', cacheKey);
    return imageInfoCache.get(cacheKey);
  }

  console.log('正在获取图片真实信息:', imageUrl);

  try {
    // 方法：通过GET请求下载完整文件获取最准确的文件信息
    const httpsUrl = imageUrl.replace(/^http:/, 'https:');
    const response = await fetch(httpsUrl, { 
      method: 'GET',
      cache: 'no-store'
    });
    
    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }
    
    // 获取文件的blob形式 - 这是最准确的文件大小
    const blob = await response.blob();
    const fileSize = blob.size;  // 真实文件大小
    const fileType = blob.type;  // 真实文件类型
    
    console.log('获取到准确的文件信息:', { fileSize, fileType });

    // 通过Image对象获取图片的真实尺寸
    return new Promise((resolve) => {
      const img = new Image();
      
      img.onload = () => {
        // console.log('图片加载成功，尺寸:', img.naturalWidth, 'x', img.naturalHeight);
        
        // 构建完整的图片信息对象
        const imageInfo = {
          size: fileSize,              // 真实文件大小（字节）
          width: img.naturalWidth,     // 真实图片宽度（像素）
          height: img.naturalHeight,   // 真实图片高度（像素）
          format: fileType || 'image/jpeg'  // 真实文件类型
        };
        
        console.log('获取到完整的图片信息:', imageInfo);
        
        // 释放blob URL
        URL.revokeObjectURL(img.src);
        
        // 存入缓存
        imageInfoCache.set(cacheKey, imageInfo);
        
        // 返回完整信息
        resolve(imageInfo);
      };
      
      img.onerror = (error) => {
        console.error('图片加载失败，错误详情:', error);
        console.error('失败的图片URL:', imageUrl);
        
        // 释放blob URL
        URL.revokeObjectURL(img.src);
        
        // 即使图片解析失败，也返回已获取的文件信息
        const partialInfo = {
          size: fileSize,
          format: fileType || 'image/jpeg'
          // 没有宽高信息
        };
        
        console.warn('返回部分信息（无尺寸）:', partialInfo);
        
        // 缓存部分信息
        imageInfoCache.set(cacheKey, partialInfo);
        
        resolve(partialInfo);
      };
      
      // 使用blob创建临时URL来加载图片，避免跨域问题
      const blobUrl = URL.createObjectURL(blob);
      console.log('使用blob URL加载图片获取尺寸:', blobUrl);
      img.src = blobUrl;
    });
    
  } catch (error) {
    console.error('获取图片信息完全失败:', error);
    return null;
  }
};