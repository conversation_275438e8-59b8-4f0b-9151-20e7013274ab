const crypto = require('crypto');

/**
 * 将token格式化为32位MD5格式
 * @param {string} token - 原始token
 * @returns {string} - 32位MD5格式的token
 */
function formatTokenToMD5(token) {
  if (!token) {
    return null;
  }
  
  try {
    // 使用MD5算法对token进行哈希
    const md5Hash = crypto.createHash('md5').update(token).digest('hex');
    return md5Hash;
  } catch (error) {
    console.error('Token MD5格式化失败:', error);
    return token; // 如果格式化失败，返回原token
  }
}

/**
 * 批量格式化token数组
 * @param {Array<string>} tokens - token数组
 * @returns {Array<string>} - 格式化后的token数组
 */
function formatTokensToMD5(tokens) {
  if (!Array.isArray(tokens)) {
    return [];
  }
  
  return tokens.map(token => formatTokenToMD5(token)).filter(Boolean);
}

/**
 * 验证token是否为有效的MD5格式
 * @param {string} token - 要验证的token
 * @returns {boolean} - 是否为有效的MD5格式
 */
function isValidMD5Token(token) {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  // MD5格式：32位十六进制字符
  const md5Regex = /^[a-fA-F0-9]{32}$/;
  return md5Regex.test(token);
}

/**
 * 生成随机MD5格式的token
 * @returns {string} - 32位MD5格式的随机token
 */
function generateRandomMD5Token() {
  const randomString = crypto.randomBytes(16).toString('hex');
  return formatTokenToMD5(randomString);
}

module.exports = {
  formatTokenToMD5,
  formatTokensToMD5,
  isValidMD5Token,
  generateRandomMD5Token
}; 