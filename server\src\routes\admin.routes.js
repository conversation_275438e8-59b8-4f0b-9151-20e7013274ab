const express = require('express');
const router = express.Router();
const User = require('../modules/auth/user.model');
const { isAuthenticated, isAdmin } = require('../middleware/auth.middleware');
const bcrypt = require('bcryptjs');

// 中间件：检查是否为管理员
router.use(isAuthenticated, isAdmin);

// 获取所有用户
router.get('/users', async (req, res) => {
  try {
    const users = await User.find({}, '-password');
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: '获取用户列表失败', error: error.message });
  }
});

// 获取单个用户
router.get('/users/:id', async (req, res) => {
  try {
    const user = await User.findById(req.params.id, '-password');
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    res.json(user);
  } catch (error) {
    res.status(500).json({ message: '获取用户信息失败', error: error.message });
  }
});

// 创建用户
router.post('/users', async (req, res) => {
  try {
    const { username, email, password, role } = req.body;
    
    // 检查用户名或邮箱是否已存在
    const existingUser = await User.findOne({ $or: [{ username }, { email }] });
    if (existingUser) {
      return res.status(400).json({ message: '用户名或邮箱已存在' });
    }
    
    // 创建新用户
    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = new User({
      username,
      email,
      password: hashedPassword,
      role: role || 'user',
    });
    
    await newUser.save();
    res.status(201).json({ message: '用户创建成功', user: { ...newUser.toObject(), password: undefined } });
  } catch (error) {
    res.status(500).json({ message: '创建用户失败', error: error.message });
  }
});

// 更新用户
router.put('/users/:id', async (req, res) => {
  try {
    const { username, email, role, password } = req.body;
    
    // 检查用户是否存在
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    // 检查用户名或邮箱是否已被其他用户使用
    if (username && username !== user.username) {
      const existingUsername = await User.findOne({ username });
      if (existingUsername) {
        return res.status(400).json({ message: '用户名已存在' });
      }
    }
    
    if (email && email !== user.email) {
      const existingEmail = await User.findOne({ email });
      if (existingEmail) {
        return res.status(400).json({ message: '邮箱已存在' });
      }
    }
    
    // 更新用户信息
    const updateData = { username, email, role };
    
    // 如果提供了新密码，则更新密码
    if (password) {
      updateData.password = await bcrypt.hash(password, 10);
    }
    
    const updatedUser = await User.findByIdAndUpdate(
      req.params.id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-password');
    
    res.json({ message: '用户更新成功', user: updatedUser });
  } catch (error) {
    res.status(500).json({ message: '更新用户失败', error: error.message });
  }
});

// 删除用户
router.delete('/users/:id', async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    // 防止删除自己
    if (user._id.toString() === req.user._id.toString()) {
      return res.status(400).json({ message: '不能删除当前登录的用户' });
    }
    
    await User.findByIdAndDelete(req.params.id);
    res.json({ message: '用户删除成功' });
  } catch (error) {
    res.status(500).json({ message: '删除用户失败', error: error.message });
  }
});

module.exports = router; 