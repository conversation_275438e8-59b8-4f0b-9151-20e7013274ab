/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/tabs.css';
@import '../../styles/scrollbars.css';
@import '../../styles/pagination.css';

/* 生成区域通用样式 - 被所有使用此组件的页面共享 */
.generation-area {
  flex: 1;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding: 0;
  min-width: 888px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 20px 0 0;
  margin-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  flex-shrink: 0;
}

.result-header h2 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0;
  font-weight: 500;
}

/* 头部操作按钮容器 */
.result-header .header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
  margin-right: -18px;
}

/* 清除无效记录按钮样式 */
.result-header .edit-btn {
  height: 28px;
  padding: 0 12px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
}

.result-header .edit-btn:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 筛选按钮样式 */
.result-header .filter-btn.ant-btn {
  height: 28px;
  padding: 0 12px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
  box-shadow: none;
}

.result-header .filter-btn.ant-btn:hover,
.result-header .filter-btn.ant-btn:focus {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
  background: var(--bg-primary);
  box-shadow: none;
}

/* 更多操作按钮样式 */
.result-header .more-actions {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: -10px;
}

.result-header .more-btn {
  width: 28px;
  height: 28px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
  font-size: var(--font-size-xs);
}

.result-header .more-btn:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.result-header .more-btn span {
  display: block;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: var(--text-secondary);
  position: relative;
}

.result-header .more-btn span::before,
.result-header .more-btn span::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: var(--text-secondary);
  transition: var(--transition-normal);
}

.result-header .more-btn:hover span,
.result-header .more-btn:hover span::before,
.result-header .more-btn:hover span::after {
  background: var(--brand-primary);
}

.result-header .more-btn span::before {
  left: -5px;
}

.result-header .more-btn span::after {
  right: -5px;
}

.result-header .dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: 4px;
  min-width: 120px;
  z-index: 1000;
}

.result-header .dropdown-item {
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  text-align: left;
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.result-header .dropdown-item:hover {
  background: var(--bg-hover);
}

.result-header .dropdown-item.delete {
  color: var(--error-color);
}

.result-header .dropdown-item.delete:hover {
  background: var(--error-bg);
  color: var(--error-color);
}

.result-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
  padding: 20px;
}

.result-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  font-size: var(--font-size-md);
  padding: 20px;
}

.demo-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  max-width: 100%;
  width: 100%;
}

.demo-gif-container {
  margin-bottom: 24px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: transparent;
  padding: 0;
}

.demo-gif {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-md);
  display: block;
}

.demo-text {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.demo-title {
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0;
}

.demo-description {
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  margin: 0;
  line-height: 1.5;
}

.demo-tip {
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  margin: 0;
  font-style: italic;
}

.task-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0;
}

.pagination-wrapper {
  flex-shrink: 0;
  padding: 0;
  border-top: 1px solid var(--border-color);
  background: var(--bg-primary);
}

.showcase-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background: var(--bg-primary);
}

.result-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 6px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  cursor: pointer;
  transition: var(--transition-normal);
}

.action-btn:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.empty-result {
  text-align: center;
  color: var(--text-tertiary);
  font-size: var(--font-size-md);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .generation-area {
    margin-left: 0 !important;
    margin-top: 12px;
    min-height: 400px;
    height: auto !important;
    flex: none;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  .result-content {
    padding: 16px;
    min-height: 300px;
  }
  
  .result-header {
    padding: 20px 0 0;  /* 保持与PC端一致的padding */
    margin-bottom: 8px;
  }
  
  .result-header h2 {
    font-size: var(--font-size-md);
  }
  
  /* 移动端隐藏搜索框、日期筛选器、筛选按钮和清除无效记录按钮 */
  .header-actions .search-container,
  .header-actions .account-unique-range-picker,
  .header-actions .filter-btn,
  .header-actions .clear-invalid-btn {
    display: none !important;
  }
  
  /* 移动端确保更多按钮保持正确尺寸和位置 */
  .header-actions .more-actions {
    margin-left: 0 !important;
  }
  
  .header-actions .more-btn {
    width: 28px !important;
    height: 28px !important;
  }
  
  .demo-content {
    max-width: 100%;
    padding: 0 8px;
  }
  
  .demo-gif-container {
    margin-bottom: 16px;
    padding: 0;
  }
  
  .demo-title {
    font-size: var(--font-size-md);
  }
  
  .demo-description {
    font-size: var(--font-size-sm);
  }
  
  .demo-tip {
    font-size: var(--font-size-xs);
  }
}

/* 480px以下进一步优化 */
@media (max-width: 480px) {
  .generation-area {
    margin-left: 0 !important;
    margin-top: 8px;
    min-height: 350px;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  .result-content {
    padding: 12px;
    min-height: 250px;
  }
  
  .result-header {
    padding: 20px 0 0;  /* 保持与PC端一致的padding */
    margin-bottom: 6px;
  }
  
  .result-header h2 {
    font-size: var(--font-size-sm);
  }
  
  .header-actions {
    gap: 8px;
  }
  
  /* 确保在480px以下也隐藏搜索框、日期筛选器、筛选按钮和清除无效记录按钮 */
  .header-actions .search-container,
  .header-actions .account-unique-range-picker,
  .header-actions .filter-btn,
  .header-actions .clear-invalid-btn {
    display: none !important;
  }
  
  /* 480px以下确保更多按钮保持正确尺寸和位置 */
  .header-actions .more-actions {
    margin-left: 0 !important;
  }
  
  .header-actions .more-btn {
    width: 24px !important;
    height: 24px !important;
  }
  
  .result-header .edit-btn,
  .result-header .filter-btn.ant-btn {
    height: 24px;
    padding: 0 8px;
    font-size: var(--font-size-xs);
  }
  
  .result-header .more-btn {
    width: 24px;
    height: 24px;
  }
}

/* 360px以下极致优化 */
@media (max-width: 360px) {
  .generation-area {
    margin-left: 0 !important;
    margin-top: 6px;
    min-height: 300px;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  .result-content {
    padding: 8px;
    min-height: 200px;
  }
  
  .result-header {
    padding: 20px 0 0;  /* 保持与PC端一致的padding */
    margin-bottom: 4px;
  }
  
  .result-header h2 {
    font-size: var(--font-size-xs);
  }
  
  .header-actions {
    gap: 6px;
  }
  
  /* 确保在360px以下也隐藏搜索框、日期筛选器、筛选按钮和清除无效记录按钮 */
  .header-actions .search-container,
  .header-actions .account-unique-range-picker,
  .header-actions .filter-btn,
  .header-actions .clear-invalid-btn {
    display: none !important;
  }
  
  /* 360px以下确保更多按钮保持正确尺寸和位置 */
  .header-actions .more-actions {
    margin-left: 0 !important;
  }
  
  .header-actions .more-btn {
    width: 22px !important;
    height: 22px !important;
  }
  
  .result-header .edit-btn,
  .result-header .filter-btn.ant-btn {
    height: 22px;
    padding: 0 6px;
    font-size: var(--font-size-xs);
  }
  
  .result-header .more-btn {
    width: 22px;
    height: 22px;
  }
}

/* 确保没有其他滚动条样式覆盖 */

/* 搜索框容器样式 */
.search-container {
  /* 移除额外的margin-right，使用header-actions的gap统一间距 */
}

/* 搜索输入框样式 */
.task-search-input {
  width: 186px !important;
  height: 28px !important;
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-sm) !important;
  color: var(--text-primary) !important;
  font-size: var(--font-size-xs) !important;
  transition: var(--transition-normal) !important;
  box-shadow: none !important;
  padding-left: 8px !important;
}

.task-search-input:hover {
  border-color: var(--brand-primary) !important;
}

.task-search-input.ant-input-focused,
.task-search-input.ant-input-affix-wrapper-focused {
  border-color: var(--brand-primary) !important;
  box-shadow: none !important;
}

/* 搜索图标样式 */
.task-search-input .search-icon {
  color: var(--text-secondary) !important;
  font-size: var(--font-size-sm) !important;
}

.task-search-input:hover .search-icon {
  color: var(--brand-primary) !important;
}

/* 清除按钮样式 */
.task-search-input .ant-input-clear-icon {
  color: var(--text-secondary) !important;
  font-size: var(--font-size-sm) !important;
}

.task-search-input:hover .ant-input-clear-icon {
  color: var(--brand-primary) !important;
}

/* 输入框占位符样式 */
.task-search-input.ant-input::placeholder,
.task-search-input .ant-input::placeholder {
  color: var(--text-secondary) !important;
}

/* 搜索输入框样式 */
.task-search-input .ant-input-prefix {
  margin-right: 8px !important;
}

/* GenerationArea 中的日期选择器样式 - 使用高优先级选择器 */
html body .generation-area .header-actions .account-unique-range-picker.ant-picker.ant-picker-range {
  height: 28px !important;
  width: auto !important;
  min-width: 220px !important;
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-sm) !important;
  color: var(--text-primary) !important;
  font-size: var(--font-size-xs) !important;
  transition: var(--transition-normal) !important;
  box-shadow: none !important;
}

/* 日期选择器活动指示条 - 使用品牌色，采用高优先级选择器 */
html body .generation-area .header-actions .account-unique-range-picker.ant-picker.ant-picker-range .ant-picker-active-bar {
  background: var(--brand-primary) !important;
  background-color: var(--brand-primary) !important;
  height: 2px !important;
  bottom: 0 !important;
  transition: var(--transition-normal) !important;
}

/* 确保在不同状态下都使用品牌色 */
html body .generation-area .header-actions .account-unique-range-picker.ant-picker-focused .ant-picker-active-bar,
html body .generation-area .header-actions .account-unique-range-picker.ant-picker-focused:hover .ant-picker-active-bar {
  background: var(--brand-primary) !important;
  background-color: var(--brand-primary) !important;
  height: 2px !important;
  bottom: 0 !important;
}

html body .generation-area .header-actions .account-unique-range-picker:hover {
  border-color: var(--brand-primary) !important;
}

html body .generation-area .header-actions .account-unique-range-picker.ant-picker-focused {
  border-color: var(--brand-primary) !important;
  box-shadow: none !important;
}

/* 确保日期选择器在header-actions中的样式 */
.generation-area .header-actions .account-unique-range-picker .ant-picker-input > input {
  color: var(--text-primary) !important;
  font-size: var(--font-size-xs) !important;
}

.generation-area .header-actions .account-unique-range-picker .ant-picker-input {
  padding: 0 4px !important;
}

.generation-area .header-actions .account-unique-range-picker .ant-picker-input > input::placeholder {
  color: var(--text-secondary) !important;
}

.generation-area .header-actions .account-unique-range-picker .ant-picker-separator {
  color: var(--text-secondary) !important;
  margin: 0 2px !important;
}

.generation-area .header-actions .account-unique-range-picker .ant-picker-suffix {
  color: var(--text-secondary) !important;
}

.generation-area .header-actions .account-unique-range-picker .ant-picker-clear {
  right: 6px !important;
}

.generation-area .header-actions .account-unique-range-picker:hover .ant-picker-suffix {
  color: var(--brand-primary) !important;
} 