const express = require('express');
const router = express.Router();
const announcementController = require('./announcement.controller');
const { isAuthenticated, isAdmin, auth } = require('../../../middleware/auth.middleware');

// 管理员路由 - 需要管理员权限
router.use('/admin/announcements', isAuthenticated, auth, isAdmin);
router.get('/admin/announcements', announcementController.getAnnouncements);
router.post('/admin/announcements', announcementController.createAnnouncement);
router.put('/admin/announcements/:id', announcementController.updateAnnouncement);
router.delete('/admin/announcements/:id', announcementController.deleteAnnouncement);

// 用户路由 - 获取当前有效公告
router.get('/announcements/active', announcementController.getActiveAnnouncements);

module.exports = router; 