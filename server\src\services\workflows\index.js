/**
 * 工作流索引文件
 * 导出所有可用的工作流配置
 */

const mattingbg = require('./mattingbg');  // 原ocbg.js已重命名为mattingbg.js
const mattingclo = require('./mattingclo');  // 原cutclothe.js已重命名为mattingclo.js
const tryonauto = require('./tryonauto');
const tryonmanual = require('./tryonmanual');
const mattingbgfile = require('./mattingbgfile');  // 从rmbgfile.js重命名为mattingbgfile.js
// const mattingclofile = require('./mattingclofile');  // 移除mattingclofile引用
const trending = require('./trending');
const divergent = require('./divergent');
const optimize = require('./optimize');
const inspiration = require('./inspiration');
const fashion = require('./fashion');
const recolor = require('./recolor');
const fabric = require('./fabric');
const background = require('./background');
const virtual = require('./virtual');
const extract = require('./extract');
const upscale = require('./upscale');
const extend = require('./extend');
const changemodel = require('./changemodel');

// 添加一个简单的upload工作流，用于不需要抠图直接上传的图片上传
const upload = {
  prompt: {
    // 这是一个空的工作流，不执行任何处理
    // 仅作为标识符使用，实际处理在app.js中完成
  }
};

// 工作流映射表
const workflows = {
  // 自动抠图（移除背景）
  mattingbg,  // 从ocbg重命名为mattingbg
  
  // 批量自动抠图（支持多张图片）
  mattingbgfile,  // 从rmbgfile重命名为mattingbgfile
  
  // 简单上传（无处理）
  upload,

  // 抠衣服工作流
  mattingclo,  // 从cutclothe重命名为mattingclo
  
  // 批量抠衣服工作流
  // mattingclofile,  // 移除mattingclofile导出
  
  // 自动模特换装工作流
  tryonauto,
  
  // 手动模特换装工作流
  tryonmanual,
  
  // 爆款开发工作流
  trending,
  
  // 爆款延伸工作流
  divergent,
  
  // 款式优化工作流
  optimize,
  
  // 灵感探索工作流
  inspiration,
  
  // 时尚设计工作流
  fashion,
  
  // 服装复色工作流
  recolor,
  
  // 换面料工作流
  fabric,
  
  // 换背景工作流
  background,
  
  // 虚拟模特工作流
  virtual,
  
  // 图片取词工作流
  extract,
  
  // 高清放大工作流
  upscale,
  
  // 智能扩图工作流
  extend,

  // 换模特工作流
  changemodel,
  
  // 可以继续添加更多工作流...
};

/**
 * 获取工作流配置
 * @param {string} type 工作流类型
 * @param {string} pageType 页面类型
 * @param {string} imageType 图片类型
 * @returns {Object} 工作流配置对象
 */
function getWorkflow(type, pageType, imageType) {
  // 获取基础工作流
  const baseWorkflow = workflows[type];
  
  if (!baseWorkflow) {
    throw new Error(`未找到工作流类型: ${type}`);
  }
  
  // 创建工作流的深拷贝，避免修改原始对象
  const workflowConfig = JSON.parse(JSON.stringify(baseWorkflow));
  
  // 根据页面类型和图片类型进行工作流定制
  // 目前直接返回拷贝的工作流，未来可以在这里添加更多定制逻辑
  return workflowConfig;
}

// 导出工作流配置和选择器函数
module.exports = workflows;
module.exports.getWorkflow = getWorkflow;

// 导出工作流类型枚举
module.exports.WorkflowType = {
  MATTING: 'mattingbg',    // 从OCBG: 'ocbg'重命名为MATTING: 'mattingbg'
  MATTINGFILE: 'mattingbgfile',  // 从RMBGFILE: 'rmbgfile'重命名为MATTINGFILE: 'mattingbgfile'
  UPLOAD: 'upload',
  MATTINGCLO: 'mattingclo',  // 从CUTCLOTHE: 'cutclothe'重命名为MATTINGCLO: 'mattingclo'
  // MATTINGCLOFILE: 'mattingclofile',  // 移除MATTINGCLOFILE枚举
  TRYONAUTO: 'tryonauto',
  TRYONMANUAL: 'tryonmanual',
  TRENDING: 'trending',
  DIVERGENT: 'divergent',
  OPTIMIZE: 'optimize',
  INSPIRATION: 'inspiration',
  FASHION: 'fashion',
  RECOLOR: 'recolor',
  FABRIC: 'fabric',
  BACKGROUND: 'background',
  VIRTUAL: 'virtual',
  EXTRACT: 'extract',
  UPSCALE: 'upscale',
  EXTEND: 'extend',
  CHANGEMODEL: 'changemodel'
}; 