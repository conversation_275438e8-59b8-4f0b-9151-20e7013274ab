# 工作流管理系统说明

## 概述

本系统提供了完整的工作流管理功能，包括工作流的创建、编辑、删除和配置映射管理。管理员可以通过Web界面管理所有工作流，这些工作流会自动出现在RunningHub配置管理的映射选项中。

## 功能特性

### 1. 工作流CRUD操作
- ✅ 创建新工作流
- ✅ 编辑现有工作流
- ✅ 删除工作流
- ✅ 批量状态更新

### 2. 工作流属性管理
- ✅ 基本信息（ID、名称、描述、版本）
- ✅ 分类管理（款式设计、模特图、工具、其他）
- ✅ 平台支持配置
- ✅ 优先级和标签管理
- ✅ 启用/禁用状态

### 3. 使用统计
- ✅ 运行次数统计
- ✅ 成功率计算
- ✅ 最后使用时间记录

### 4. 集成配置映射
- ✅ 自动出现在RunningHub配置映射中
- ✅ 支持动态工作流列表
- ✅ 分类标签显示

## 系统架构

### 数据模型
```javascript
{
  id: String,              // 工作流唯一标识
  name: String,            // 工作流名称
  displayName: String,     // 显示名称
  category: String,        // 分类
  description: String,     // 描述
  version: String,         // 版本
  enabled: Boolean,        // 是否启用
  supportedPlatforms: [],  // 支持的平台
  recommendedPlatform: String, // 推荐平台
  tags: [],               // 标签
  priority: Number,       // 优先级
  usageStats: {           // 使用统计
    totalRuns: Number,
    successRuns: Number,
    failedRuns: Number,
    lastUsed: Date
  }
}
```

### API接口
- `GET /api/workflows` - 获取工作流列表
- `POST /api/workflows` - 创建工作流
- `PUT /api/workflows/:id` - 更新工作流
- `DELETE /api/workflows/:id` - 删除工作流
- `GET /api/workflows/meta/categories` - 获取分类列表
- `PUT /api/workflows/batch/status` - 批量更新状态

## 使用指南

### 1. 初始化数据

首次使用需要初始化默认工作流数据：

```bash
# 进入服务器目录
cd server

# 运行初始化脚本
node scripts/initWorkflows.js

# 如果需要清空现有数据重新初始化
node scripts/initWorkflows.js --clear
```

### 2. 访问管理界面

1. 启动服务器：`npm start`
2. 登录管理后台（需要管理员权限）
3. 进入"RunningHub管理"页面
4. 点击"工作流管理"标签页

### 3. 工作流管理操作

#### 3.1 添加工作流
1. 点击"添加工作流"按钮
2. 填写工作流信息：
   - **工作流ID**: 唯一标识，如 `A01-trending`
   - **工作流名称**: 显示名称，如 `爆款开发`
   - **分类**: 选择合适的分类
   - **描述**: 详细描述工作流功能
   - **支持平台**: 选择支持的执行平台
   - **推荐平台**: 设置推荐使用的平台
   - **优先级**: 数字越大优先级越高
   - **标签**: 添加相关标签便于分类

#### 3.2 编辑工作流
1. 在工作流列表中点击"编辑"按钮
2. 修改相关信息（工作流ID不可修改）
3. 保存更改

#### 3.3 删除工作流
1. 在工作流列表中点击"删除"按钮
2. 确认删除操作

#### 3.4 批量操作
1. 选择多个工作流
2. 使用批量操作按钮进行状态更新

### 4. 配置映射使用

添加的工作流会自动出现在RunningHub配置管理中：

1. 进入"配置管理"标签页
2. 创建或编辑RunningHub配置
3. 在"工作流映射配置"部分可以看到所有启用的工作流
4. 为每个工作流配置对应的RunningHub工作流ID

## 工作流分类说明

### 款式设计类
专注于服装设计和款式开发的工作流：
- 爆款开发、款式优化、灵感探索等

### 模特图类
处理模特图片和展示效果的工作流：
- 时尚大片、换装、换模特、背景处理等

### 工具类
提供辅助功能的工作流：
- 图片处理、文本提取、质量优化等

### 其他类
不属于上述分类的特殊工作流

## 最佳实践

### 1. 命名规范
- **工作流ID**: 使用字母数字和横线，如 `A01-trending`
- **工作流名称**: 简洁明了，如 `爆款开发`
- **描述**: 详细说明功能和用途

### 2. 分类管理
- 按功能类型进行分类
- 保持分类的一致性
- 定期整理和优化分类

### 3. 平台配置
- 根据工作流特性选择支持的平台
- 设置合理的推荐平台
- 考虑用户类型和性能需求

### 4. 优先级设置
- 常用工作流设置较高优先级
- 新功能可以设置中等优先级
- 实验性功能设置较低优先级

## 数据迁移

### 从配置文件迁移
如果之前使用配置文件管理工作流，可以通过以下步骤迁移：

1. 备份现有配置文件
2. 运行初始化脚本创建数据库记录
3. 手动添加配置文件中的自定义工作流
4. 验证迁移结果

### 数据备份
定期备份工作流数据：

```bash
# 导出工作流数据
mongoexport --db aibikini --collection workflows --out workflows_backup.json

# 导入工作流数据
mongoimport --db aibikini --collection workflows --file workflows_backup.json
```

## 故障排除

### 1. 工作流不显示
- 检查工作流是否启用
- 确认数据库连接正常
- 查看服务器日志

### 2. 映射配置不生效
- 确认工作流ID正确
- 检查工作流是否启用
- 验证配置保存成功

### 3. 创建工作流失败
- 检查工作流ID是否唯一
- 确认必填字段已填写
- 查看错误提示信息

## 开发扩展

### 添加新字段
1. 更新数据模型 `server/src/models/Workflow.js`
2. 修改API接口处理逻辑
3. 更新前端表单和显示组件
4. 运行数据库迁移脚本

### 自定义分类
1. 修改模型中的分类枚举
2. 更新前端分类选项
3. 调整分类颜色和图标

### 集成新平台
1. 在支持平台列表中添加新平台
2. 更新平台选择逻辑
3. 实现新平台的执行接口

## 监控和维护

### 1. 使用统计监控
- 定期查看工作流使用情况
- 分析成功率和性能数据
- 优化低效工作流

### 2. 数据清理
- 定期清理无用的工作流
- 归档历史版本
- 优化数据库性能

### 3. 版本管理
- 为重要更新创建新版本
- 保持版本号的一致性
- 记录版本变更日志

## 安全考虑

### 1. 权限控制
- 只有管理员可以管理工作流
- 记录操作日志
- 定期审核权限

### 2. 数据验证
- 验证输入数据格式
- 防止SQL注入和XSS攻击
- 限制文件上传大小

### 3. 备份策略
- 定期自动备份
- 多地备份存储
- 测试恢复流程

通过这套完整的工作流管理系统，您可以灵活地管理所有工作流定义，并与RunningHub配置系统无缝集成，实现真正的动态工作流配置管理。
