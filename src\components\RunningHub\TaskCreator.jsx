/**
 * RunningHub任务创建组件
 * 用于创建和管理RunningHub任务
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  CircularProgress,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayArrowIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { runningHubConfig } from '../../services/runningHub/config';
import runningHubAPI from '../../api/runningHub';
import taskManager from '../../services/runningHub/taskManager.js';

const TaskCreator = ({ onTaskCreated }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [configs, setConfigs] = useState([]);
  const [selectedConfig, setSelectedConfig] = useState('');
  const [taskType, setTaskType] = useState('simple');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // 简易任务表单
  const [simpleForm, setSimpleForm] = useState({
    addMetadata: false
  });

  // 高级任务表单
  const [advancedForm, setAdvancedForm] = useState({
    nodeInfoList: [],
    addMetadata: false
  });

  // AI应用任务表单
  const [appForm, setAppForm] = useState({
    appId: '',
    inputData: {}
  });

  // 加载配置列表
  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = () => {
    const allConfigs = runningHubConfig.getAllConfigs();
    const defaultConfigName = runningHubConfig.getDefaultConfigName();
    setConfigs(allConfigs);
    if (defaultConfigName && !selectedConfig) {
      setSelectedConfig(defaultConfigName);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    const types = ['simple', 'advanced', 'app'];
    setTaskType(types[newValue]);
  };

  const handleAddNodeInfo = () => {
    setAdvancedForm(prev => ({
      ...prev,
      nodeInfoList: [
        ...prev.nodeInfoList,
        { nodeId: '', fieldName: '', fieldValue: '' }
      ]
    }));
  };

  const handleRemoveNodeInfo = (index) => {
    setAdvancedForm(prev => ({
      ...prev,
      nodeInfoList: prev.nodeInfoList.filter((_, i) => i !== index)
    }));
  };

  const handleNodeInfoChange = (index, field, value) => {
    setAdvancedForm(prev => ({
      ...prev,
      nodeInfoList: prev.nodeInfoList.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const handleCreateTask = async () => {
    try {
      if (!selectedConfig) {
        showSnackbar('请选择配置', 'error');
        return;
      }

      const config = runningHubConfig.getConfig(selectedConfig);
      if (!config) {
        showSnackbar('配置不存在', 'error');
        return;
      }

      setLoading(true);

      let taskOptions;
      switch (taskType) {
        case 'simple':
          taskOptions = {
            type: 'simple',
            params: {
              apiKey: config.apiKey,
              workflowId: config.workflowId,
              addMetadata: simpleForm.addMetadata
            }
          };
          break;

        case 'advanced':
          if (advancedForm.nodeInfoList.length === 0) {
            showSnackbar('请添加至少一个节点信息', 'error');
            setLoading(false);
            return;
          }
          
          // 验证节点信息
          for (const node of advancedForm.nodeInfoList) {
            if (!node.nodeId || !node.fieldName || node.fieldValue === '') {
              showSnackbar('请填写完整的节点信息', 'error');
              setLoading(false);
              return;
            }
          }

          taskOptions = {
            type: 'advanced',
            params: {
              apiKey: config.apiKey,
              workflowId: config.workflowId,
              nodeInfoList: advancedForm.nodeInfoList,
              addMetadata: advancedForm.addMetadata
            }
          };
          break;

        case 'app':
          if (!appForm.appId) {
            showSnackbar('请输入应用ID', 'error');
            setLoading(false);
            return;
          }

          taskOptions = {
            type: 'app',
            params: {
              apiKey: config.apiKey,
              appId: appForm.appId,
              inputData: appForm.inputData
            }
          };
          break;

        default:
          throw new Error('不支持的任务类型');
      }

      // 创建并监控任务
      const result = await taskManager.createAndMonitorTask(
        taskOptions,
        (progress) => {
          console.log('任务进度:', progress);
          // 可以在这里更新UI显示进度
        },
        (completion) => {
          console.log('任务完成:', completion);
          showSnackbar('任务执行成功', 'success');
          if (onTaskCreated) {
            onTaskCreated(completion);
          }
        },
        (error) => {
          console.error('任务失败:', error);
          showSnackbar(`任务执行失败: ${error.error?.message || '未知错误'}`, 'error');
        }
      );

      if (result.success) {
        showSnackbar('任务创建成功，正在执行中...', 'success');
      } else {
        showSnackbar(`任务创建失败: ${result.error}`, 'error');
      }

    } catch (error) {
      console.error('创建任务失败:', error);
      showSnackbar(`创建任务失败: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const renderSimpleTaskForm = () => (
    <Box display="flex" flexDirection="column" gap={2}>
      <Alert severity="info">
        简易任务将使用工作流的默认参数直接执行，相当于在RunningHub平台上点击"运行"按钮。
      </Alert>
      
      <FormControl>
        <InputLabel>添加元数据</InputLabel>
        <Select
          value={simpleForm.addMetadata}
          onChange={(e) => setSimpleForm(prev => ({ ...prev, addMetadata: e.target.value }))}
        >
          <MenuItem value={false}>否</MenuItem>
          <MenuItem value={true}>是</MenuItem>
        </Select>
      </FormControl>
    </Box>
  );

  const renderAdvancedTaskForm = () => (
    <Box display="flex" flexDirection="column" gap={2}>
      <Alert severity="info">
        高级任务允许您自定义工作流节点的参数值，实现更精细的控制。
      </Alert>

      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h6">节点信息列表</Typography>
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={handleAddNodeInfo}
        >
          添加节点
        </Button>
      </Box>

      {advancedForm.nodeInfoList.map((node, index) => (
        <Accordion key={index}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>
              节点 {index + 1} {node.nodeId && `(ID: ${node.nodeId})`}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box display="flex" flexDirection="column" gap={2}>
              <TextField
                label="节点ID"
                value={node.nodeId}
                onChange={(e) => handleNodeInfoChange(index, 'nodeId', e.target.value)}
                fullWidth
                required
              />
              <TextField
                label="字段名称"
                value={node.fieldName}
                onChange={(e) => handleNodeInfoChange(index, 'fieldName', e.target.value)}
                fullWidth
                required
              />
              <TextField
                label="字段值"
                value={node.fieldValue}
                onChange={(e) => handleNodeInfoChange(index, 'fieldValue', e.target.value)}
                fullWidth
                required
                multiline
                rows={2}
              />
              <Box display="flex" justifyContent="flex-end">
                <IconButton
                  color="error"
                  onClick={() => handleRemoveNodeInfo(index)}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            </Box>
          </AccordionDetails>
        </Accordion>
      ))}

      <FormControl>
        <InputLabel>添加元数据</InputLabel>
        <Select
          value={advancedForm.addMetadata}
          onChange={(e) => setAdvancedForm(prev => ({ ...prev, addMetadata: e.target.value }))}
        >
          <MenuItem value={false}>否</MenuItem>
          <MenuItem value={true}>是</MenuItem>
        </Select>
      </FormControl>
    </Box>
  );

  const renderAppTaskForm = () => (
    <Box display="flex" flexDirection="column" gap={2}>
      <Alert severity="info">
        AI应用任务用于调用RunningHub平台上的AI应用。
      </Alert>

      <TextField
        label="应用ID"
        value={appForm.appId}
        onChange={(e) => setAppForm(prev => ({ ...prev, appId: e.target.value }))}
        fullWidth
        required
      />

      <TextField
        label="输入数据 (JSON格式)"
        value={JSON.stringify(appForm.inputData, null, 2)}
        onChange={(e) => {
          try {
            const data = JSON.parse(e.target.value);
            setAppForm(prev => ({ ...prev, inputData: data }));
          } catch (error) {
            // 忽略JSON解析错误，让用户继续编辑
          }
        }}
        fullWidth
        multiline
        rows={4}
        placeholder='{"key": "value"}'
      />
    </Box>
  );

  return (
    <Box>
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            创建RunningHub任务
          </Typography>

          {/* 配置选择 */}
          <Box mb={3}>
            <FormControl fullWidth>
              <InputLabel>选择配置</InputLabel>
              <Select
                value={selectedConfig}
                onChange={(e) => setSelectedConfig(e.target.value)}
                required
              >
                {configs.map((config) => (
                  <MenuItem key={config.name} value={config.name}>
                    <Box display="flex" alignItems="center" gap={1}>
                      {config.name}
                      {runningHubConfig.getDefaultConfigName() === config.name && (
                        <Chip label="默认" size="small" color="primary" />
                      )}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* 任务类型选择 */}
          <Box mb={3}>
            <Tabs value={activeTab} onChange={handleTabChange}>
              <Tab label="简易任务" />
              <Tab label="高级任务" />
              <Tab label="AI应用任务" />
            </Tabs>
          </Box>

          {/* 任务表单 */}
          <Box mb={3}>
            {activeTab === 0 && renderSimpleTaskForm()}
            {activeTab === 1 && renderAdvancedTaskForm()}
            {activeTab === 2 && renderAppTaskForm()}
          </Box>

          {/* 操作按钮 */}
          <Box display="flex" justifyContent="flex-end" gap={2}>
            <Button
              variant="contained"
              startIcon={loading ? <CircularProgress size={20} /> : <PlayArrowIcon />}
              onClick={handleCreateTask}
              disabled={loading || !selectedConfig}
            >
              {loading ? '创建中...' : '创建任务'}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TaskCreator;
