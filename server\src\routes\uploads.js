const express = require('express');
const router = express.Router();
const Upload = require('../models/Upload');
const { auth, selectAuth } = require('../modules/auth');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// 配置multer存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 获取用户ID，如果没有则使用'developer'
    const userId = req.user?._id?.toString() || req.body.userId || 'developer';
    
    // 从请求对象获取保存路径，优先使用保存的路径
    let savePath = req.savedPath || req.body.path || req.query.savePath;
    
    console.log('Multer配置 - 请求体:', req.body);
    console.log('Multer配置 - 请求查询:', req.query);
    console.log('Multer配置 - 保存路径参数:', savePath);
    console.log('Multer配置 - 预保存路径:', req.savedPath);
    
    // 如果没有提供path参数，则使用默认路径
    if (!savePath) {
      console.log('Multer配置 - 未指定保存路径，使用默认路径');
      savePath = `server/storage/${userId}/model/try-on/mask`;
    } else {
      console.log('Multer配置 - 使用指定的保存路径:', savePath);
    }
    
    // 确保路径包含用户ID
    if (!savePath.includes(userId)) {
      const storageIndex = savePath.indexOf('storage');
      if (storageIndex !== -1) {
        const pathPrefix = savePath.substring(0, storageIndex + 'storage'.length);
        const pathSuffix = savePath.substring(storageIndex + 'storage'.length);
        savePath = `${pathPrefix}/${userId}${pathSuffix}`;
      }
    }
    
    // 确保目录存在
    const fullPath = path.join(__dirname, '../../..', savePath);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }
    
    cb(null, fullPath);
  },
  filename: function (req, file, cb) {
    // 使用原始文件名
    cb(null, file.originalname);
  }
});

const upload = multer({ storage: storage });

// 获取用户的最近20条上传记录
router.get('/', selectAuth, async (req, res) => {
  try {
    const userId = req.user._id;
    const uploads = await Upload.find({ userId })
      .sort({ createdAt: -1 })
      .limit(20);
    
    res.json({
      success: true,
      data: uploads
    });
  } catch (error) {
    console.error('获取上传记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取上传记录失败'
    });
  }
});

// 添加新的上传记录，并删除超过20条的旧记录
router.post('/', selectAuth, async (req, res) => {
  try {
    const userId = req.user._id;
    const uploadData = req.body;
    
    // 验证必填字段
    if (!uploadData.filename || !uploadData.originalname) {
      return res.status(400).json({
        success: false,
        message: '缺少必要的文件信息'
      });
    }
    
    // 创建上传记录，标记为临时状态
    const upload = new Upload({
      userId,
      filename: uploadData.filename,
      originalname: uploadData.originalname,
      mimetype: uploadData.mimetype || 'image/jpeg',
      size: uploadData.size || 0,
      status: 'temporary' // 明确设置为临时状态
    });
    
    await upload.save();

    // 获取该用户的所有上传记录，按时间倒序排列
    const uploads = await Upload.find({ userId })
      .sort({ createdAt: -1 });

    // 如果记录超过20条，删除多余的记录及其对应的文件
    if (uploads.length > 20) {
      const uploadsToDelete = uploads.slice(20);
      for (const upload of uploadsToDelete) {
        const filePath = path.join(__dirname, `../../storage/${userId}/uploads`, upload.filename);
        try {
          fs.unlinkSync(filePath);
        } catch (err) {
          console.error('删除文件失败:', err);
        }
        await upload.remove();
      }
    }

    res.json({
      success: true,
      data: upload
    });
  } catch (error) {
    console.error('保存上传记录失败:', error);
    res.status(500).json({
      success: false,
      message: '保存上传记录失败'
    });
  }
});

// 删除上传记录
router.delete('/:id', auth, async (req, res) => {
  try {
    const userId = req.user._id;
    const upload = await Upload.findOne({
      _id: req.params.id,
      userId
    });

    if (!upload) {
      return res.status(404).json({
        success: false,
        message: '上传记录不存在'
      });
    }

    // 删除文件
    const filePath = path.join(__dirname, `../../storage/${userId}/uploads`, upload.filename);
    try {
      fs.unlinkSync(filePath);
    } catch (err) {
      console.error('删除文件失败:', err);
    }

    // 删除记录
    await upload.remove();

    res.json({
      success: true,
      message: '上传记录已删除'
    });
  } catch (error) {
    console.error('删除上传记录失败:', error);
    res.status(500).json({
      success: false,
      message: '删除上传记录失败'
    });
  }
});

// 上传蒙版API
router.post('/upload-mask', selectAuth, (req, res) => {
  try {
    console.log('开始处理蒙版上传请求');
    
    // 获取用户ID
    const userId = req.user?._id?.toString() || 'developer';
    console.log(`蒙版上传用户ID: ${userId}`);
    
    // 创建自定义的multer上传中间件
    const multerUpload = multer().single('file');
    
    // 先解析文件上传
    multerUpload(req, res, function(err) {
      if (err) {
        console.error('蒙版上传错误:', err);
        return res.status(500).json({
          success: false,
          message: `蒙版上传失败: ${err.message}`
        });
      }
      
      // 检查是否有文件
      if (!req.file) {
        console.error('蒙版上传失败: 未接收到文件');
        return res.status(400).json({
          success: false,
          message: '未接收到蒙版文件'
        });
      }
      
      // 获取页面类型 - 从FormData字段中获取
      const pageType = req.body.pageType || 'try-on';
      console.log(`蒙版上传页面类型: ${pageType}`);
      
      // 获取客户端指定的保存路径 - 从FormData字段中获取
      const clientPath = req.body.path;
      console.log(`客户端指定的保存路径: ${clientPath}`);
      
      // 根据路径和页面类型确定保存路径
      let maskDir;
      if (clientPath) {
        // 如果客户端提供了路径，优先使用该路径
        maskDir = path.join(__dirname, `../../../${clientPath}`);
        console.log(`使用客户端指定的保存路径: ${maskDir}`);
      } else if (pageType === 'optimize') {
        maskDir = path.join(__dirname, `../../storage/${userId}/style/optimize/mask`);
        console.log(`款式优化蒙版保存目录: ${maskDir}`);
      } else {
        // 默认为模特换装页面
        maskDir = path.join(__dirname, `../../storage/${userId}/model/try-on/mask`);
        console.log(`模特换装蒙版保存目录: ${maskDir}`);
      }
      
      // 确保目录存在
      if (!fs.existsSync(maskDir)) {
        fs.mkdirSync(maskDir, { recursive: true });
        console.log(`已创建蒙版目录: ${maskDir}`);
      }
      
      // 保存文件到最终目标位置
      const finalFilename = req.file.originalname;
      const finalPath = path.join(maskDir, finalFilename);
      
      // 将缓冲区写入文件
      fs.writeFile(finalPath, req.file.buffer, (err) => {
        if (err) {
          console.error('保存蒙版文件失败:', err);
          return res.status(500).json({
            success: false,
            message: `保存蒙版文件失败: ${err.message}`
          });
        }
        
        console.log('蒙版上传成功:', {
          originalname: finalFilename,
          path: finalPath,
          size: req.file.size,
          destination: maskDir
        });
        
        // 返回成功响应
        res.json({
          success: true,
          data: {
            filename: finalFilename,
            path: finalPath,
            size: req.file.size,
            destination: maskDir
          },
          message: '蒙版上传成功'
        });
      });
    });
  } catch (error) {
    console.error('蒙版上传处理异常:', error);
    res.status(500).json({
      success: false,
      message: '蒙版上传处理异常: ' + error.message
    });
  }
});

module.exports = router; 