# 基于使用次数的配置智能分配说明

## 功能概述

根据用户需求，实现了基于RunningHub配置使用次数的智能分配机制：
1. **平均分配负载**：根据配置的使用次数，优先选择使用次数最少的配置
2. **自动切换机制**：当所有配置使用次数都超过10次时，自动切换到ComfyUI平台
3. **数据库驱动**：配置信息从数据库实时获取，确保数据准确性

## 核心实现

### 1. **智能配置选择函数**

#### `selectConfigByUsage(workflowName, userId, forceSelect)`

```javascript
async function selectConfigByUsage(workflowName, userId = null, forceSelect = false) {
  // 1. 获取所有启用的配置（按使用次数升序排列）
  const configs = await RunningHubConfig.find({ 
    enabled: true 
  }).sort({ 'usage.totalTasks': 1 });
  
  // 2. 筛选支持该工作流的配置
  const availableConfigs = configs.filter(config => 
    config.workflowMappings.has(workflowName)
  );
  
  // 3. 检查是否所有配置都超过10次使用
  const allConfigsOverLimit = availableConfigs.every(config => 
    (config.usage.totalTasks || 0) >= 10
  );
  
  // 4. 决策逻辑
  if (allConfigsOverLimit && !forceSelect) {
    return { useComfyUI: true, reason: '所有配置都超过10次使用' };
  }
  
  // 5. 选择使用次数最少的配置
  const selectedConfig = availableConfigs[0];
  return {
    useComfyUI: false,
    config: selectedConfig,
    workflowId: selectedConfig.workflowMappings.get(workflowName)
  };
}
```

### 2. **增强的平台推荐函数**

#### `getRecommendedPlatform(workflowName, context)`

现在返回详细的推荐信息对象：

```javascript
{
  platform: 'runninghub',           // 推荐的平台
  reason: '选择使用次数最少的配置',    // 选择原因
  config: configObject,             // 选择的配置对象
  workflowId: '1850925505116598274', // 工作流ID
  configsUsage: [                   // 所有配置的使用统计
    {
      name: '配置A',
      totalTasks: 5,
      selected: true
    },
    {
      name: '配置B', 
      totalTasks: 8,
      selected: false
    }
  ]
}
```

### 3. **智能决策流程**

```mermaid
flowchart TD
    A[开始工作流执行] --> B[检查强制规则]
    B --> C{仅支持特定平台?}
    C -->|是| D[返回指定平台]
    C -->|否| E[获取RunningHub配置]
    E --> F[按使用次数排序]
    F --> G{所有配置>10次?}
    G -->|是| H{ComfyUI可用?}
    H -->|是| I[使用ComfyUI]
    H -->|否| J[强制使用RunningHub]
    G -->|否| K[选择使用次数最少的配置]
    K --> L[使用RunningHub]
```

## 数据库集成

### 1. **配置使用统计模型**

```javascript
// RunningHubConfig Schema
usage: {
  totalTasks: {        // 总任务数
    type: Number,
    default: 0
  },
  successTasks: {      // 成功任务数
    type: Number,
    default: 0
  },
  failedTasks: {       // 失败任务数
    type: Number,
    default: 0
  },
  lastUsed: Date       // 最后使用时间
}
```

### 2. **自动统计更新**

```javascript
// 任务成功时
await config.updateUsage(true);

// 任务失败时  
await config.updateUsage(false);
```

### 3. **查询优化**

```javascript
// 按使用次数升序排列，确保选择最少使用的配置
const configs = await RunningHubConfig.find({ 
  enabled: true 
}).sort({ 'usage.totalTasks': 1 });
```

## 执行流程优化

### 1. **上下文传递**

```javascript
// 在selectPlatform中添加用户ID
context.userId = userId;

// 获取ComfyUI实例状态
context.comfyuiInstancesStatus = await getComfyUIInstancesStatus();

// 调用智能推荐
const recommendation = await getRecommendedPlatform(workflowName, context);
```

### 2. **配置信息传递**

```javascript
// 保存选择的配置到上下文
if (recommendation.platform === 'runninghub' && recommendation.config) {
  context.selectedConfig = recommendation.config;
  context.selectedWorkflowId = recommendation.workflowId;
}
```

### 3. **执行时使用选择的配置**

```javascript
async executeOnRunningHub(workflowName, params, userId, taskId, callback, context = {}) {
  // 优先使用智能选择的配置
  if (context.selectedConfig && context.selectedWorkflowId) {
    config = context.selectedConfig;
    runningHubWorkflowId = context.selectedWorkflowId;
    console.log(`使用智能选择的配置: ${config.name} (使用次数: ${config.usage.totalTasks || 0})`);
  } else {
    // 后备方案：使用默认配置
    config = await RunningHubConfig.getDefaultConfig(userId);
  }
}
```

## 任务记录增强

### 1. **详细的平台信息记录**

```javascript
const platformInfo = {
  selectedAt: new Date(),
  reason: recommendation.reason,
  comfyuiInstancesStatus: context.comfyuiInstancesStatus
};

// 记录RunningHub配置信息
if (recommendation.config) {
  platformInfo.runningHubConfig = {
    configId: recommendation.config._id,
    configName: recommendation.config.name,
    workflowId: recommendation.workflowId,
    usageCount: recommendation.config.usage.totalTasks || 0
  };
}

// 记录配置使用统计
if (recommendation.configsUsage) {
  platformInfo.configsUsage = recommendation.configsUsage;
}
```

### 2. **使用统计自动更新**

```javascript
// 任务创建成功后立即更新使用统计
await config.updateUsage(true);

// 任务失败时也要更新统计
catch (error) {
  if (config) {
    await config.updateUsage(false);
  }
}
```

## API响应增强

### 1. **平台推荐API**

`POST /api/comfyui/platform-recommendation`

#### 响应示例：
```json
{
  "success": true,
  "data": {
    "workflowName": "A01-trending",
    "recommendedPlatform": "runninghub",
    "reason": "选择使用次数最少的配置: 配置A (5次)",
    "comfyuiInstancesStatus": {
      "totalInstances": 3,
      "availableInstances": 2,
      "busyInstances": 1,
      "allInstancesBusy": false
    },
    "configsUsage": [
      {
        "name": "配置A",
        "totalTasks": 5,
        "selected": true
      },
      {
        "name": "配置B",
        "totalTasks": 8,
        "selected": false
      }
    ],
    "selectedConfig": {
      "id": "507f1f77bcf86cd799439011",
      "name": "配置A",
      "usageCount": 5
    }
  }
}
```

### 2. **当所有配置超过10次时的响应**

```json
{
  "success": true,
  "data": {
    "workflowName": "A01-trending",
    "recommendedPlatform": "comfyui",
    "reason": "所有RunningHub配置使用次数都超过10次，切换到ComfyUI",
    "configsUsage": [
      {
        "name": "配置A",
        "totalTasks": 12
      },
      {
        "name": "配置B", 
        "totalTasks": 15
      }
    ],
    "selectedConfig": null
  }
}
```

## 负载均衡效果

### 1. **配置使用分布**

通过智能选择，实现配置使用次数的平均分配：

```
配置A: 5次 ← 优先选择
配置B: 8次
配置C: 12次 (超过10次，触发ComfyUI切换)
```

### 2. **自动切换机制**

```
所有配置使用次数 > 10次 → 自动使用ComfyUI
ComfyUI实例全部繁忙 → 强制使用RunningHub（选择使用次数最少的）
```

### 3. **实时监控**

```javascript
console.log(`使用智能选择的配置: ${config.name} (使用次数: ${config.usage.totalTasks || 0})`);
console.log(`工作流 ${workflowName} 推荐使用平台: ${recommendation.platform}, 原因: ${recommendation.reason}`);
```

## 优势总结

### 1. **智能负载均衡**
- 自动选择使用次数最少的配置
- 避免单一配置过度使用
- 实现真正的负载平均分配

### 2. **自适应切换**
- 基于实际使用情况动态调整
- 10次阈值触发平台切换
- 确保服务持续可用

### 3. **数据驱动决策**
- 基于数据库实时数据
- 准确的使用统计
- 透明的决策过程

### 4. **完整的监控体系**
- 详细的选择日志
- 完整的任务记录
- 实时的使用统计

### 5. **容错机制**
- 强制选择模式处理极端情况
- 后备配置确保服务可用
- 错误处理保证系统稳定

通过这次优化，系统实现了真正智能的配置分配机制，能够根据实际使用情况动态调整策略，确保负载均衡和服务质量。
