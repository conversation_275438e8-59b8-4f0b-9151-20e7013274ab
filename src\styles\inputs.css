/**
 * 统一输入框样式定义
 * 
 * 包含以下组件：
 * 1. 提示词文本框 (.prompt-textarea) - 用于多行文本输入
 * 2. 基础输入框 (.input-base) - 单行文本输入的基础样式
 * 3. 文本域 (.textarea-base) - 多行文本输入的基础样式
 * 4. 输入组 (.input-group) - 带标签的输入框组合
 * 
 * 特点：
 * - 统一的边框和圆角样式
 * - 优雅的焦点状态效果
 * - 平滑的状态过渡动画
 * - 支持占位符文本
 * - 支持禁用状态
 * - 支持错误状态
 * 
 * 使用示例：
 * <div class="input-group">
 *   <label class="input-label">标题</label>
 *   <input class="input-base" placeholder="请输入..." />
 * </div>
 * 
 * <div class="input-group">
 *   <label class="input-label">描述</label>
 *   <textarea class="textarea-base" placeholder="请输入详细描述..."></textarea>
 * </div>
 */

@import './theme.css';

/* 提示词文本框样式 - 作为基础样式 */
.prompt-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-md);
  line-height: 1.5;
  resize: vertical;
  min-height: 100px;
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: var(--transition-normal);
}

.prompt-textarea:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

.prompt-textarea::placeholder {
  color: var(--text-tertiary);
}

/* 文本框基础样式 */
.input-base {
  composes: prompt-textarea;
  line-height: normal;
  resize: none;
  min-height: auto;
}

/* 文本域样式 */
.textarea-base {
  composes: prompt-textarea;
}

/* 带标签的输入组 */
.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.input-label {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  font-weight: 500;
}

/* 基础输入框样式 */
.input {
  width: 100%;
  height: 32px;
  padding: 0 var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  transition: var(--transition-normal);
}

.input:hover {
  border-color: var(--brand-primary);
}

.input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

.input::placeholder {
  color: var(--text-tertiary);
}

.input:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
}

/* 文本域样式 */
.textarea {
  width: 100%;
  min-height: 80px;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  line-height: 1.5;
  resize: vertical;
  transition: var(--transition-normal);
}

.textarea:hover {
  border-color: var(--brand-primary);
}

.textarea:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

/* 搜索框样式 */
.search-input {
  position: relative;
  width: 100%;
}

.search-input input {
  width: 100%;
  height: 32px;
  padding-left: 32px;
  padding-right: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  transition: var(--transition-normal);
}

.search-input svg {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: var(--text-tertiary);
  pointer-events: none;
}

/* 数字输入框样式 */
.number-input {
  position: relative;
  width: 100%;
}

.number-input input {
  width: 100%;
  height: 32px;
  padding: 0 32px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  text-align: center;
  transition: var(--transition-normal);
}

.number-input button {
  position: absolute;
  top: 1px;
  width: 32px;
  height: 30px;
  border: none;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
}

.number-input button:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.number-input button.decrease {
  left: 1px;
  border-radius: var(--radius-sm) 0 0 var(--radius-sm);
}

.number-input button.increase {
  right: 1px;
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

/* 选择框样式 */
.select {
  width: 100%;
  height: 32px;
  padding: 0 var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  cursor: pointer;
  transition: var(--transition-normal);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M6 8.825L1.175 4 2.238 2.938 6 6.7l3.763-3.763L10.825 4z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right var(--spacing-sm) center;
  padding-right: 28px;
}

.select:hover {
  border-color: var(--brand-primary);
}

.select:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

/* 复选框样式 */
.checkbox {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkbox-mark {
  width: 16px;
  height: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  margin-right: var(--spacing-xs);
  position: relative;
  transition: var(--transition-normal);
  background: var(--bg-primary);
}

.checkbox input:checked ~ .checkbox-mark {
  background: var(--brand-primary);
  border-color: var(--brand-primary);
}

.checkbox-mark:after {
  content: '';
  position: absolute;
  display: none;
  left: 5px;
  top: 2px;
  width: 3px;
  height: 7px;
  border: solid var(--text-inverse);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox input:checked ~ .checkbox-mark:after {
  display: block;
}

/* 单选框样式 */
.radio {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.radio input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.radio-mark {
  width: 16px;
  height: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-full);
  margin-right: var(--spacing-xs);
  position: relative;
  transition: var(--transition-normal);
  background: var(--bg-primary);
}

.radio input:checked ~ .radio-mark {
  border-color: var(--brand-primary);
}

.radio-mark:after {
  content: '';
  position: absolute;
  display: none;
  left: 3px;
  top: 3px;
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: var(--brand-primary);
}

.radio input:checked ~ .radio-mark:after {
  display: block;
}

/* 成功状态样式 */
.input.success,
.textarea.success,
.search-input input.success,
.number-input input.success,
.select.success {
  border-color: var(--success-color);
}

.input.success:focus,
.textarea.success:focus,
.search-input input.success:focus,
.number-input input.success:focus,
.select.success:focus {
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
} 