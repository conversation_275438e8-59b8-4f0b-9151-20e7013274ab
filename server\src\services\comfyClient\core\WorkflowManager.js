const fs = require('fs');
const path = require('path');

class WorkflowManager {
    constructor() {
        this.workflowsDir = null
        this.jsonDir ='../apiJson'

        // 存放工作流 文件名称:js
        this.workflows = {};
        this.loadWorkflows('../apiJson');
    }
    loadWorkflows() {
        this.workflowsDir = path.resolve(__dirname,this.jsonDir);
        try {
            const files = fs.readdirSync(this.workflowsDir);
            files.forEach(file => {
                if (file.endsWith('.json')) {
                    try {
                        const filePath = path.join(this.workflowsDir, file);
                        const content = fs.readFileSync(filePath, 'utf8');
                        const workflowName = path.basename(file, '.json');

                        this.workflows[workflowName] = JSON.parse(content);
                        console.log(`成功加载工作流: ${workflowName}`);
                    } catch (parseError) {
                        console.error(`加载工作流文件 ${file} 失败:`, parseError.message);
                    }
                }
            });

            if (Object.keys(this.workflows).length === 0) {
                console.warn(`警告: 没有在 ${this.workflowsDir} 中找到任何JSON工作流文件`);
            }
        } catch (readError) {
            throw new Error(`无法读取工作流目录: ${readError.message}`);
        }
    }

    getWorkflow(name) {
        if (!this.workflows[name]) {
            throw new Error(`工作流 ${name} 不存在`);
        }
        return JSON.parse(JSON.stringify(this.workflows[name])); // 返回深拷贝
    }

    listWorkflows() {
        return Object.keys(this.workflows);
    }
}

module.exports = WorkflowManager;