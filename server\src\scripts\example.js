/**
 * 工作流管理脚本使用示例
 * 
 * 这个文件展示了如何使用各种工作流管理脚本
 * 仅作为示例，不要直接运行此文件
 */

const WorkflowInserter = require('./insertWorkflowsFromJson');
const WorkflowUpdater = require('./updateWorkflowsInfo');
const WorkflowValidator = require('./validateWorkflows');

// 示例1: 程序化插入工作流
async function exampleInsertWorkflows() {
  console.log('示例1: 插入工作流');
  
  const inserter = new WorkflowInserter();
  
  // 设置为预览模式
  inserter.dryRun = true;
  
  try {
    await inserter.run();
    console.log('✅ 插入预览完成');
  } catch (error) {
    console.error('❌ 插入失败:', error.message);
  }
}

// 示例2: 程序化更新工作流
async function exampleUpdateWorkflows() {
  console.log('示例2: 更新工作流');
  
  const updater = new WorkflowUpdater();
  
  // 设置为预览模式，只更新名称字段
  updater.dryRun = true;
  updater.targetField = 'name';
  
  try {
    await updater.run();
    console.log('✅ 更新预览完成');
  } catch (error) {
    console.error('❌ 更新失败:', error.message);
  }
}

// 示例3: 程序化验证工作流
async function exampleValidateWorkflows() {
  console.log('示例3: 验证工作流');
  
  const validator = new WorkflowValidator();
  
  try {
    await validator.run();
    console.log('✅ 验证完成');
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
  }
}

// 示例4: 自定义工作流处理
async function exampleCustomProcessing() {
  console.log('示例4: 自定义处理');
  
  const mongoose = require('mongoose');
  const Workflow = require('../models/Workflow');
  
  try {
    // 连接数据库
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aibikini');
    
    // 查找所有高优先级工作流
    const highPriorityWorkflows = await Workflow.find({ 
      priority: { $gte: 70 } 
    }).select('id name priority');
    
    console.log('高优先级工作流:');
    highPriorityWorkflows.forEach(w => {
      console.log(`  ${w.id}: ${w.name} (优先级: ${w.priority})`);
    });
    
    // 统计各分类的工作流数量
    const categoryStats = await Workflow.aggregate([
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    console.log('\n分类统计:');
    categoryStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} 个`);
    });
    
    // 查找最近创建的工作流
    const recentWorkflows = await Workflow.find({})
      .sort({ createdAt: -1 })
      .limit(5)
      .select('id name createdAt');
    
    console.log('\n最近创建的工作流:');
    recentWorkflows.forEach(w => {
      console.log(`  ${w.id}: ${w.name} (${w.createdAt.toLocaleDateString()})`);
    });
    
  } catch (error) {
    console.error('自定义处理失败:', error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// 示例5: 批量操作工作流
async function exampleBatchOperations() {
  console.log('示例5: 批量操作');
  
  const mongoose = require('mongoose');
  const Workflow = require('../models/Workflow');
  
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aibikini');
    
    // 批量更新所有A系列工作流的标签
    const updateResult = await Workflow.updateMany(
      { id: { $regex: '^A' } },
      { $addToSet: { tags: '款式设计' } }
    );
    
    console.log(`更新了 ${updateResult.modifiedCount} 个A系列工作流的标签`);
    
    // 批量设置所有未设置优先级的工作流为默认优先级
    const defaultPriorityResult = await Workflow.updateMany(
      { $or: [{ priority: { $exists: false } }, { priority: 0 }] },
      { $set: { priority: 10 } }
    );
    
    console.log(`设置了 ${defaultPriorityResult.modifiedCount} 个工作流的默认优先级`);
    
    // 查找并显示所有禁用的工作流
    const disabledWorkflows = await Workflow.find({ enabled: false })
      .select('id name enabled');
    
    if (disabledWorkflows.length > 0) {
      console.log('\n禁用的工作流:');
      disabledWorkflows.forEach(w => {
        console.log(`  ${w.id}: ${w.name}`);
      });
    } else {
      console.log('\n✅ 所有工作流都已启用');
    }
    
  } catch (error) {
    console.error('批量操作失败:', error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// 示例6: 工作流数据导出
async function exampleExportWorkflows() {
  console.log('示例6: 导出工作流数据');
  
  const mongoose = require('mongoose');
  const fs = require('fs');
  const path = require('path');
  const Workflow = require('../models/Workflow');
  
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aibikini');
    
    // 导出所有工作流数据
    const workflows = await Workflow.find({}).lean();
    
    // 转换Map类型的字段为普通对象
    const exportData = workflows.map(w => ({
      ...w,
      parameters: w.parameters ? Object.fromEntries(w.parameters) : {}
    }));
    
    // 保存到文件
    const exportPath = path.join(__dirname, 'workflows_export.json');
    fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2));
    
    console.log(`✅ 导出了 ${workflows.length} 个工作流到: ${exportPath}`);
    
    // 生成CSV格式的简化数据
    const csvData = workflows.map(w => ({
      id: w.id,
      name: w.name,
      category: w.category,
      priority: w.priority,
      enabled: w.enabled,
      createdAt: w.createdAt
    }));
    
    const csvPath = path.join(__dirname, 'workflows_export.csv');
    const csvContent = [
      'ID,名称,分类,优先级,启用状态,创建时间',
      ...csvData.map(w => `${w.id},${w.name},${w.category},${w.priority},${w.enabled},${w.createdAt}`)
    ].join('\n');
    
    fs.writeFileSync(csvPath, csvContent);
    console.log(`✅ 导出CSV格式数据到: ${csvPath}`);
    
  } catch (error) {
    console.error('导出失败:', error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// 主函数 - 运行所有示例
async function runExamples() {
  console.log('🚀 工作流管理脚本使用示例\n');
  
  try {
    await exampleInsertWorkflows();
    console.log('\n' + '─'.repeat(50) + '\n');
    
    await exampleUpdateWorkflows();
    console.log('\n' + '─'.repeat(50) + '\n');
    
    await exampleValidateWorkflows();
    console.log('\n' + '─'.repeat(50) + '\n');
    
    await exampleCustomProcessing();
    console.log('\n' + '─'.repeat(50) + '\n');
    
    await exampleBatchOperations();
    console.log('\n' + '─'.repeat(50) + '\n');
    
    await exampleExportWorkflows();
    
    console.log('\n🎉 所有示例执行完成!');
    
  } catch (error) {
    console.error('💥 示例执行失败:', error.message);
  }
}

// 导出示例函数，供其他脚本使用
module.exports = {
  exampleInsertWorkflows,
  exampleUpdateWorkflows,
  exampleValidateWorkflows,
  exampleCustomProcessing,
  exampleBatchOperations,
  exampleExportWorkflows,
  runExamples
};

// 如果直接运行此文件，显示警告
if (require.main === module) {
  console.log('⚠️  这是一个示例文件，不应该直接运行！');
  console.log('请查看代码了解如何使用各种工作流管理脚本。');
  console.log('\n如果要运行示例代码，请取消注释下面这行:');
  console.log('// runExamples();');
  
  // 取消注释下面这行来运行示例
  // runExamples();
}
