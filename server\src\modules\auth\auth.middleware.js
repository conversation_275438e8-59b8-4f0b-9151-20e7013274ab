const jwt = require('jsonwebtoken');
const User = require('./user.model'); // 更新为本模块中的用户模型
const rateLimit = require('express-rate-limit');
const { createError } = require('../../utils/error');

// JWT认证中间件
const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    if (!token) {
      throw createError(401, '未提供认证令牌');
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findOne({ _id: decoded.userId, status: 'active' });

    if (!user) {
      throw createError(401, '用户不存在或已被禁用');
    }

    // 检查会话是否存在
    const session = user.activeSessions.find(s => s.token === token);
    if (!session) {
      // 如果会话不存在，说明用户在其他设备登录，当前会话已失效
      return res.status(401).json({
        success: false,
        status: 'session_expired',
        message: '您的账号已在其他设备登录，请重新登录'
      });
    }

    // 更新会话活动时间
    await user.updateSessionActivity(token);

    req.user = user;
    req.token = token;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      next(createError(401, '无效的认证令牌'));
    } else {
      next(error);
    }
  }
};

// 开发者API认证中间件
const developerAuth = async (req, res, next) => {
  try {
    const apiKey = req.header('X-API-Key');
    const apiSecret = req.header('X-API-Secret');

    if (!apiKey || !apiSecret) {
      throw createError(401, '未提供API密钥');
    }

    const developer = await User.findOne({
      'developer.apiKey': apiKey,
      'developer.apiSecret': apiSecret,
      role: 'developer',
      status: 'active'
    });

    if (!developer) {
      throw createError(401, '无效的API密钥');
    }

    if (!developer.developer.isVerified) {
      throw createError(403, '开发者账号未验证');
    }

    // 检查请求来源
    const origin = req.get('Origin');
    if (origin && developer.developer.allowedOrigins?.length > 0) {
      if (!developer.developer.allowedOrigins.includes(origin)) {
        throw createError(403, '未授权的请求来源');
      }
    }

    req.developer = developer;
    next();
  } catch (error) {
    next(error);
  }
};

// 角色验证中间件
const requireRole = (role) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(createError(401, '需要登录'));
    }
    if (req.user.role !== role && req.user.role !== 'admin') {
      return next(createError(403, '权限不足'));
    }
    next();
  };
};

// API请求频率限制
const createRateLimiter = (windowMs = 60 * 60 * 1000, max = 1000) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: '请求过于频繁，请稍后再试' },
    keyGenerator: (req) => {
      return req.developer ? req.developer.id : req.ip;
    }
  });
};

// 开发环境认证中间件 - 自动通过认证
const devAuth = (req, res, next) => {
  // 模拟开发者用户
  req.user = { 
    _id: 'developer', 
    role: 'developer',
    name: '开发者',
    status: 'active'
  };
  next();
};

// 添加会话清理中间件
const cleanupInactiveSessions = async (req, res, next) => {
  try {
    const inactiveThreshold = 24 * 60 * 60 * 1000; // 24小时
    const now = new Date();

    // 清理所有用户的过期会话
    await User.updateMany(
        {},
        {
          $pull: {
            activeSessions: {
              lastActive: { $lt: new Date(now - inactiveThreshold) }
            }
          }
        }
    );

    next();
  } catch (error) {
    next(error);
  }
};

// 根据环境选择合适的认证中间件
const selectAuth = process.env.NODE_ENV === 'development' ? devAuth : auth;

module.exports = {
  auth,
  developerAuth,
  requireRole,
  createRateLimiter,
  devAuth,
  selectAuth
}; 