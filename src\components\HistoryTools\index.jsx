import React from 'react';
import { MdUndo, MdRedo, MdRemoveCircle } from 'react-icons/md';
import './index.css';

/**
 * 历史操作工具组件 - 包含撤销、恢复和清空功能
 * 
 * @param {Object} props
 * @param {boolean} props.canUndo - 是否可以撤销
 * @param {boolean} props.canRedo - 是否可以恢复
 * @param {Function} props.onUndo - 撤销操作回调函数
 * @param {Function} props.onRedo - 恢复操作回调函数
 * @param {Function} props.onClear - 清空操作回调函数
 */
const HistoryTools = ({ 
  canUndo = false, 
  canRedo = false, 
  onUndo, 
  onRedo, 
  onClear 
}) => {
  return (
    <div className="history-tools">
      <div className="tools-group">
        <div className="tool-buttons">
          <button 
            className="tool-btn history-tool-btn"
            onClick={onUndo}
            disabled={!canUndo}
            title="撤销"
          >
            <MdUndo />
            <span className="tool-name">撤销</span>
          </button>
          <button 
            className="tool-btn history-tool-btn"
            onClick={onRedo}
            disabled={!canRedo}
            title="恢复"
          >
            <MdRedo />
            <span className="tool-name">恢复</span>
          </button>
          <button 
            className="tool-btn history-tool-btn clear-btn"
            onClick={onClear}
            title="清空"
          >
            <MdRemoveCircle size="1.2em" />
            <span className="tool-name">清空</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default HistoryTools; 