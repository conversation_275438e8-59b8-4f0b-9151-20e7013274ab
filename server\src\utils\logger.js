/**
 * 服务器端日志工具模块
 * 
 * 提供统一的日志记录接口，支持不同级别的日志，
 * 并根据环境配置自动调整日志行为
 */

const fs = require('fs');
const path = require('path');

// 获取当前环境
const ENV = process.env.NODE_ENV || 'development';

// 日志级别定义
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4
};

// 根据环境设置默认日志级别
const DEFAULT_LEVEL = ENV === 'production' ? LOG_LEVELS.INFO : LOG_LEVELS.DEBUG;

// 当前日志级别
let currentLevel = DEFAULT_LEVEL;

// 日志文件配置
const LOG_DIR = path.join(process.cwd(), 'logs');
const LOG_FILE = {
  ERROR: path.join(LOG_DIR, 'error.log'),
  COMBINED: path.join(LOG_DIR, 'combined.log')
};

// 确保日志目录存在
try {
  if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
  }
} catch (error) {
  console.error('无法创建日志目录:', error);
}

/**
 * 格式化日志消息
 * @param {string} level 日志级别
 * @param {string} message 日志消息
 * @param {any} data 附加数据
 * @returns {string} 格式化后的日志消息
 */
const formatLogMessage = (level, message, data) => {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [${level}]`;
  
  if (data !== undefined) {
    return `${prefix} ${message} ${JSON.stringify(data, null, 2)}`;
  }
  
  return `${prefix} ${message}`;
};

/**
 * 将日志写入文件
 * @param {string} level 日志级别
 * @param {string} formattedMessage 格式化后的日志消息
 */
const writeToFile = (level, formattedMessage) => {
  try {
    // 所有日志都写入combined.log
    fs.appendFileSync(LOG_FILE.COMBINED, formattedMessage + '\n');
    
    // 错误日志额外写入error.log
    if (level === 'ERROR') {
      fs.appendFileSync(LOG_FILE.ERROR, formattedMessage + '\n');
    }
  } catch (error) {
    console.error('写入日志文件失败:', error);
  }
};

/**
 * 处理日志输出
 * @param {string} level 日志级别
 * @param {string} levelName 日志级别名称
 * @param {string} message 日志消息
 * @param {any} data 附加数据
 */
const handleLog = (level, levelName, message, data) => {
  // 检查日志级别
  if (level < currentLevel) {
    return;
  }

  const formattedMessage = formatLogMessage(levelName, message, data);
  
  // 控制台输出
  switch (levelName) {
    case 'DEBUG':
      console.log(formattedMessage);
      break;
    case 'INFO':
      console.info(formattedMessage);
      break;
    case 'WARN':
      console.warn(formattedMessage);
      break;
    case 'ERROR':
      console.error(formattedMessage);
      break;
  }

  // 生产环境写入文件
  if (ENV === 'production') {
    writeToFile(levelName, formattedMessage);
  }
};

/**
 * 日志工具对象
 */
const logger = {
  /**
   * 设置日志级别
   * @param {string} level 日志级别名称
   */
  setLevel: (level) => {
    if (LOG_LEVELS[level] !== undefined) {
      currentLevel = LOG_LEVELS[level];
    }
  },

  /**
   * 记录调试级别日志
   * @param {string} message 日志消息
   * @param {any} data 附加数据
   */
  debug: (message, data) => {
    handleLog(LOG_LEVELS.DEBUG, 'DEBUG', message, data);
  },

  /**
   * 记录信息级别日志
   * @param {string} message 日志消息
   * @param {any} data 附加数据
   */
  info: (message, data) => {
    handleLog(LOG_LEVELS.INFO, 'INFO', message, data);
  },

  /**
   * 记录警告级别日志
   * @param {string} message 日志消息
   * @param {any} data 附加数据
   */
  warn: (message, data) => {
    handleLog(LOG_LEVELS.WARN, 'WARN', message, data);
  },

  /**
   * 记录错误级别日志
   * @param {string} message 日志消息
   * @param {any} data 附加数据
   */
  error: (message, data) => {
    handleLog(LOG_LEVELS.ERROR, 'ERROR', message, data);
  },

  /**
   * 记录HTTP请求日志
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @param {number} time 处理时间(ms)
   */
  httpRequest: (req, res, time) => {
    const data = {
      method: req.method,
      url: req.originalUrl || req.url,
      status: res.statusCode,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress,
      responseTime: `${time}ms`
    };

    // 根据状态码选择日志级别
    if (res.statusCode >= 500) {
      logger.error(`HTTP ${res.statusCode}`, data);
    } else if (res.statusCode >= 400) {
      logger.warn(`HTTP ${res.statusCode}`, data);
    } else {
      logger.info(`HTTP ${res.statusCode}`, data);
    }
  }
};

module.exports = logger; 