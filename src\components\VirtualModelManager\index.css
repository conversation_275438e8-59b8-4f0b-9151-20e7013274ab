.virtual-model-manager {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  color-scheme: light dark;
}

.virtual-model-manager .model-table-container {
  flex: 1;
  overflow: auto;
  padding: 16px;
}

.virtual-model-manager .model-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  table-layout: fixed;
}

.virtual-model-manager .model-table thead th {
  background-color: var(--bg-secondary);
  padding: 12px 16px;
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-light);
  white-space: nowrap;
}

.virtual-model-manager .model-table tbody td {
  padding: 20px 16px;
  border-bottom: 1px solid var(--border-light) !important;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-height: 160px;
  text-align: center;
  transition: none;
  will-change: auto;
  backface-visibility: hidden;
  transform: translateZ(0);
}

.virtual-model-manager .model-table tbody tr {
  height: 160px;
  transition: background-color 0.15s ease;
}

.virtual-model-manager .model-table tbody tr:last-child td {
  border-bottom: none;
}

.virtual-model-manager .model-table tbody tr:hover {
  background-color: var(--bg-hover);
}

/* 取消特定行的悬浮效果 */
.virtual-model-manager .model-table tbody tr.no-hover-effect:hover {
  background-color: transparent;
}

.virtual-model-manager .thumbnail-col {
  width: 210px;
  text-align: center;
}

.virtual-model-manager .id-col {
  width: 80px;
  text-align: center;
}

.virtual-model-manager .name-col {
  width: 100px;
  text-align: center;
}

.virtual-model-manager .gender-col, 
.virtual-model-manager .age-col, 
.virtual-model-manager .body-type-col, 
.virtual-model-manager .region-col {
  width: 80px;
  text-align: center;
}

.virtual-model-manager .date-col {
  width: 130px;
  text-align: center;
}

.virtual-model-manager .actions-col {
  width: 100px;
  text-align: center;
}

/* 缩略图通用样式 */
.virtual-model-manager .variant-thumbnail-wrapper {
  position: relative;
  margin-bottom: 8px;
}

.virtual-model-manager .variant-thumbnail-overlay {
  position: absolute;
  inset: 0;
  background: var(--bg-mask);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
  cursor: pointer;
  border-radius: var(--radius-sm);
}

.virtual-model-manager .variant-thumbnail-wrapper:hover .variant-thumbnail-overlay {
  opacity: 1;
}

.virtual-model-manager .variant-thumbnail-overlay svg {
  width: var(--font-size-xl);
  height: var(--font-size-xl);
  color: #fff;
}

/* 主模特缩略图 */
.virtual-model-manager .model-thumbnail {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
}

/* 变体图片网格样式 */
.virtual-model-manager .variant-images-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 8px;
  width: 180px;
  height: 120px;
  margin: 0 auto;
}

.virtual-model-manager .variant-thumbnail {
  width: 100%;
  height: 56px;
  object-fit: cover;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
}

.virtual-model-manager .action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
  align-items: center;
}

.virtual-model-manager .action-buttons .edit-button,
.virtual-model-manager .action-buttons .delete-button,
.virtual-model-manager .action-buttons .download-button {
  width: 32px;
  height: 32px;
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--radius-sm);
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: none;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.virtual-model-manager .action-buttons .edit-button:hover,
.virtual-model-manager .action-buttons .delete-button:hover,
.virtual-model-manager .action-buttons .download-button:hover {
  color: var(--brand-primary);
}

.virtual-model-manager .action-buttons .edit-button svg,
.virtual-model-manager .action-buttons .delete-button svg,
.virtual-model-manager .action-buttons .download-button svg {
  width: 15px;
  height: 15px;
  display: block;
  line-height: 1;
}

.virtual-model-manager .no-models-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 24px;
  text-align: center;
}

.virtual-model-manager .no-models-message p {
  margin: 4px 0;
  color: var(--text-secondary);
}

.virtual-model-manager .no-models-message p.tip {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: 8px;
}

.virtual-model-manager .pagination-container {
  margin-top: 16px;
}

/* 预览模态框翻页按钮样式 */
.virtual-model-manager .preview-image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.virtual-model-manager .prev-image-button,
.virtual-model-manager .next-image-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 24px;
  transition: color 0.2s ease;
  z-index: 1;
}

.virtual-model-manager .prev-image-button {
  left: -70px;
}

.virtual-model-manager .next-image-button {
  right: -70px;
}

.virtual-model-manager .prev-image-button:hover,
.virtual-model-manager .next-image-button:hover {
  color: rgba(255, 255, 255, 0.8);
}

.virtual-model-manager .prev-image-button svg,
.virtual-model-manager .next-image-button svg {
  width: 36px;
  height: 36px;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.5));
}

/* 图片索引指示器样式 */
.virtual-model-manager .image-index-indicator {
  position: absolute;
  bottom: -45px;
  left: 50%;
  transform: translateX(-50%);
  background: transparent;
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.7);
}

/* 表头内容排序和筛选样式 */
.virtual-model-manager .th-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.virtual-model-manager .th-content span {
  margin: 3px 6px;
  text-align: center;
}

.virtual-model-manager .sort-button,
.virtual-model-manager .filter-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  transition: var(--transition-normal);
  opacity: 0.9;
  flex-shrink: 0;
}

.virtual-model-manager .sort-button:hover,
.virtual-model-manager .filter-button:hover {
  color: var(--brand-primary);
  opacity: 1;
}

.virtual-model-manager .sort-icon,
.virtual-model-manager .filter-button {
  font-size: 16px;
}

.virtual-model-manager .sort-icon.active,
.virtual-model-manager .filter-button.active {
  color: var(--brand-primary);
  opacity: 1;
}

.virtual-model-manager .sort-icon.default {
  color: var(--text-tertiary);
}

/* 筛选下拉菜单样式 */
.filter-dropdown {
  padding: 4px;
  background: var(--bg-primary);
  border-radius: var(--radius-xs);
  box-shadow: var(--shadow-xs);
  min-width: 90px;
  border: 1px solid var(--border-light);
}

/* 筛选菜单选项 */
.filter-menu-item {
  padding: 6px;
  font-size: calc(var(--font-size-sm) - 1px);
  line-height: 1.2;
  cursor: pointer;
  color: var(--text-primary);
  transition: all 0.1s;
  margin: 1px 0;
}

.filter-menu-item:hover {
  background: var(--bg-hover);
}

.filter-menu-item.active {
  color: var(--brand-primary);
  font-weight: 500;
}

.filter-dropdown .ant-input {
  margin: 2px 0;
  font-size: calc(var(--font-size-sm) - 2px);
  border-radius: var(--radius-xs);
  height: 28px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* 搜索框占位符文字样式 */
.filter-dropdown .ant-input::placeholder {
  font-size: calc(var(--font-size-sm) - 2px);
  color: var(--text-tertiary);
}

/* 自定义输入框样式，取消蓝色边框 */
.filter-dropdown .ant-input:focus,
.filter-dropdown .ant-input-focused {
  border-color: var(--border-medium);
  box-shadow: 0 0 0 1px var(--shadow-color);
}

.filter-dropdown .ant-input:hover {
  border-color: var(--border-medium);
}

/* 自定义清除图标颜色 */
.filter-dropdown .ant-input-clear-icon {
  color: var(--text-tertiary);
}

.filter-dropdown .ant-input-clear-icon:hover {
  color: var(--text-secondary);
}

.filter-actions {
  margin-top: 2px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid var(--border-light);
  padding-top: 2px;
}

/* 自定义清除所有筛选按钮样式 */
.virtual-model-manager .no-models-message .ant-btn {
  border-color: var(--border-light);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  height: 36px !important;
  padding-left: 16px;
  padding-right: 16px;
  margin-bottom: 36px;
}

.virtual-model-manager .no-models-message .ant-btn:hover {
  border-color: var(--border-medium);
  color: var(--text-dark);
  background-color: var(--bg-hover);
}

/* 筛选搜索输入框样式 */
.filter-search-input.ant-input-affix-wrapper {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-light);
}

.filter-search-input.ant-input-affix-wrapper:focus,
.filter-search-input.ant-input-affix-wrapper-focused {
  border-color: var(--border-medium) !important;
  box-shadow: 0 0 0 1px var(--shadow-color) !important;
}

.filter-search-input.ant-input-affix-wrapper:hover {
  border-color: var(--border-medium) !important;
}

.filter-search-input .ant-input {
  background-color: transparent;
}

.filter-search-input .ant-input:focus {
  box-shadow: none;
}

.filter-search-input .ant-input-clear-icon {
  color: var(--text-tertiary);
}

.filter-search-input .ant-input-clear-icon:hover {
  color: var(--text-secondary);
}

.clear-all-filters-button.ant-btn {
  height: 36px !important;
  line-height: 34px !important;
}

/* 修复分割线闪烁的全局样式补丁 */
.virtual-model-manager .model-table *,
.virtual-model-manager .model-table *::before,
.virtual-model-manager .model-table *::after {
  border-color: var(--border-light) !important;
}

/* 修复Ant Design图标在表头中的显示问题 */
.virtual-model-manager .th-content .anticon {
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 确保筛选图标的样式 */
.virtual-model-manager .filter-button .anticon-filter {
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 确保排序图标的样式 */
.virtual-model-manager .sort-button .anticon-sort-ascending,
.virtual-model-manager .sort-button .anticon-sort-descending {
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 统一操作按钮样式，确保三个按钮完全一致 */
.virtual-model-manager .action-buttons > button {
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  border-radius: var(--radius-sm) !important;
  background: none !important;
  color: var(--text-secondary) !important;
  cursor: pointer !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: 1 !important;
  box-shadow: none !important;
  position: relative !important;
  top: 0 !important;
  right: 0 !important;
  opacity: 1 !important;
}

.virtual-model-manager .action-buttons > button:hover {
  color: var(--brand-primary) !important;
  background: none !important;
  opacity: 1 !important;
}

.virtual-model-manager .action-buttons > button svg {
  width: 15px !important;
  height: 15px !important;
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
} 