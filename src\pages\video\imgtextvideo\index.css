.imgtextvideo-page {
  width: 100%;
  height: calc(100vh - 68px);  /* 减去导航栏高度 */
  background: var(--bg-secondary);
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.imgtextvideo-container {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.imgtextvideo-container h1 {
  margin-bottom: 20px;
  color: var(--text-primary);
}

.imgtextvideo-container p {
  color: var(--text-secondary);
  text-align: center;
  max-width: 600px;
}

/* 开发中提示样式 */
.empty-custom {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
  padding: var(--spacing-xl);
}

.empty-custom .coming-soon-icon {
  width: 120px;
  height: 120px;
  margin-bottom: var(--spacing-xl);
  opacity: 0.8;
}

.empty-custom h3 {
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.empty-custom p {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
} 