import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { MdOutlineHelpOutline } from 'react-icons/md';
import './index.css';
import TipPopup from '../TipPopup'; // 导入提示弹窗组件

const WeightPanel = ({
  weights = { item1: 0.5, item2: 0.5 },
  onChange,
  item1Label,
  item2Label,
  pageType, // 新增页面类型参数
  disabled1 = false, // 添加禁用状态
  disabled2 = false,  // 添加禁用状态
  hideItem1 = false  // 新增：是否隐藏第一个滑块
}) => {
  const [localWeights, setLocalWeights] = useState(weights);
  
  // 提示弹窗相关状态
  const [isTipVisible, setIsTipVisible] = useState(false);
  const [tipPosition, setTipPosition] = useState({ left: 0, top: 0 });
  const tipButtonRef = useRef(null);
  
  // 当外部强度变化时，更新本地状态
  useEffect(() => {
    setLocalWeights(weights);
  }, [weights]);

  // 根据页面类型获取标签
  const getLabels = () => {
    // 默认标签
    let labels = {
      item1: item1Label || "因素一",
      item2: item2Label || "因素二"
    };

    // 根据页面类型设置标签
    if (pageType === 'trending') {
      // 爆款开发页面
      labels = {
        item1: "版型",
        item2: "面料印花"
      };
    } else if (pageType === 'fashion') {
      // 时尚大片页面
      labels = {
        item1: "参考图",
        item2: "描述词"
      };
    } else if (item1Label && item2Label) {
      // 如果提供了自定义标签，则使用自定义标签
      labels = {
        item1: item1Label,
        item2: item2Label
      };
    }

    return labels;
  };

  // 格式化数值，保留两位小数
  const formatValue = (value) => {
    return value.toFixed(2);
  };

  // 当第一个滑块变化时
  const handleItem1Change = (e) => {
    if (disabled1) return; // 禁用状态不处理
    
    const item1Value = parseFloat(e.target.value);
    
    const newWeights = {
      ...localWeights,
      item1: item1Value
    };
    
    setLocalWeights(newWeights);
    onChange && onChange(newWeights);
  };

  // 当第二个滑块变化时
  const handleItem2Change = (e) => {
    if (disabled2) return; // 禁用状态不处理
    
    const item2Value = parseFloat(e.target.value);
    
    const newWeights = {
      ...localWeights,
      item2: item2Value
    };
    
    setLocalWeights(newWeights);
    onChange && onChange(newWeights);
  };

  // 均衡按钮点击处理
  const handleBalanceClick = () => {
    const balancedWeights = { item1: 0.5, item2: 0.5 };
    setLocalWeights(balancedWeights);
    onChange && onChange(balancedWeights);
  };

  // 计算填充宽度的百分比
  const calculateFillWidth = (value) => {
    return value * 100;
  };

  // 计算均衡值位置（50%）
  const balancePosition = 50;

  // 判断是否为"面料印花"并处理换行
  const formatLabel = (label) => {
    if (label === "面料印花") {
      return (
        <div className="two-line-label">
          <div>印花</div>
        </div>
      );
    }
    return label;
  };

  // 获取当前页面类型对应的标签
  const labels = getLabels();

  // 处理提示按钮点击
  const handleShowTip = () => {
    if (tipButtonRef.current) {
      const rect = tipButtonRef.current.getBoundingClientRect();
      setTipPosition({
        left: rect.left + rect.width + 28,
        top: rect.top - 8
      });
    }
    setIsTipVisible(true);
  };

  // 处理关闭提示
  const handleCloseTip = () => {
    setIsTipVisible(false);
  };

  return (
    <>
      <div className="weight-setting">
        <div className="weight-content">
          <div className="weight-label">
            <span>强度调节</span>
            <span className="weight-values">
              {!hideItem1 && <span className="colored-value">{formatValue(localWeights.item1)}</span>}
              <span className="colored-value">{formatValue(localWeights.item2)}</span>
            </span>
          </div>
          <div className="weight-slider-container">
            {/* 双滑块界面 */}
            <div className="weight-sliders">
              {/* 第一个强度滑块 */}
              {!hideItem1 && (
                <div className="weight-slider-row">
                  <div className="weight-item-label">{labels.item1}</div>
                  <div className="slider-container">
                    <div className="slider-track">
                      <div 
                        className={`slider-fill ${disabled1 ? 'disabled' : ''}`}
                        style={{ width: `${calculateFillWidth(localWeights.item1)}%` }}
                      ></div>
                      <input 
                        type="range" 
                        min={0}
                        max={1}
                        step={0.01}
                        value={localWeights.item1}
                        onChange={handleItem1Change}
                        className={`slider-input ${disabled1 ? 'disabled' : ''}`}
                        disabled={disabled1}
                      />
                    </div>
                  </div>
                </div>
              )}
              
              {/* 第二个强度滑块 */}
              <div className="weight-slider-row">
                <div className="weight-item-label">{formatLabel(labels.item2)}</div>
                <div className="slider-container">
                  <div className="slider-track">
                    <div 
                      className={`slider-fill ${disabled2 ? 'disabled' : ''}`}
                      style={{ width: `${calculateFillWidth(localWeights.item2)}%` }}
                    ></div>
                    {pageType === 'fashion' && (
                      <div className="recommend-mark">
                        <div className="recommend-label">推荐</div>
                        <div className="recommend-arrow"></div>
                      </div>
                    )}
                    <input 
                      type="range" 
                      min={0}
                      max={1}
                      step={0.01}
                      value={localWeights.item2}
                      onChange={handleItem2Change}
                      className={`slider-input ${disabled2 ? 'disabled' : ''}`}
                      disabled={disabled2}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 提示按钮 */}
        <button 
          className="tip-button-common weight-panel-tip-button"
          onClick={handleShowTip}
          ref={tipButtonRef}
          title="查看使用提示"
        >
          <span className="tip-text">点我</span>
          <MdOutlineHelpOutline />
        </button>
      </div>
      
      {/* 提示弹窗 */}
      <TipPopup 
        type="weight-panel"
        position={tipPosition}
        isVisible={isTipVisible}
        onClose={handleCloseTip}
        content={pageType === 'divergent' ? [
          "• 变化强度越高，生成的延伸款式变化越大",
          "• 建议根据实际需求调整，找到最佳变化程度",
        ].join('\n') : [
          "• 强度数值越高，最终效果受参考图片的影响越大",
          "• 建议根据实际需求调整，找到最佳平衡点",
        ].join('\n')}
      />
    </>
  );
};

WeightPanel.propTypes = {
  weights: PropTypes.shape({
    item1: PropTypes.number,
    item2: PropTypes.number
  }),
  onChange: PropTypes.func,
  item1Label: PropTypes.string,
  item2Label: PropTypes.string,
  pageType: PropTypes.string, // 页面类型属性定义
  disabled1: PropTypes.bool,  // 添加禁用状态属性定义
  disabled2: PropTypes.bool,   // 添加禁用状态属性定义
  hideItem1: PropTypes.bool   // 新增：是否隐藏第一个滑块属性定义
};

export default WeightPanel; 