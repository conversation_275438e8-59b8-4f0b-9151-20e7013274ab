import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getFeaturesByCategory } from '../../config/features';
import './index.css';

const VideoPage = ({ isLoggedIn, userId }) => {
  const navigate = useNavigate();
  const [error, setError] = useState(null);

  const handleNavigate = (path) => {
    try {
      navigate(path);
    } catch (err) {
      setError({
        title: '导航错误',
        message: '无法跳转到指定页面，请稍后重试。'
      });
    }
  };

  if (error) {
    return (
      <div className="error-container">
        <h2>{error.title}</h2>
        <p>{error.message}</p>
        <button onClick={() => setError(null)}>返回</button>
      </div>
    );
  }

  const features = getFeaturesByCategory('AI视频');

  return (
    <div className="model-page">
      <div className="model-header">
        <h1>AI视频</h1>
        <p>利用人工智能技术，一键生成专业品质的泳装产品视频</p>
      </div>
      <div className="page-feature-grid">
        {features.map((feature) => (
          <div 
            key={feature.id} 
            className="page-feature-card"
            onClick={() => {
              const pathMap = {
                '图文成片': '/video/imgtextvideo',
                '多图成片': '/video/mulimgvideo'
              };
              handleNavigate(pathMap[feature.name]);
            }}
          >
            <div className="feature-icon">
              <img src={feature.image} alt={feature.name} />
            </div>
            <h3>{feature.name}</h3>
            <p>{feature.description}</p>
          </div>
        ))}
      </div>
      <div className="feature-tip">
        点击上方卡片或侧边栏按钮，进入对应功能页面
      </div>
    </div>
  );
};

export default VideoPage; 