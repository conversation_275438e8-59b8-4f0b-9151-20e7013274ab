# WebSocket任务订阅调试指南

## 问题描述

当任务执行开始时，前端没有正确发送任务消息给WebSocket，导致无法接收进度更新。

## 调试步骤

### 1. 检查任务数据结构

根据您提供的任务执行接口返回信息：

```json
{
  "success": true,
  "platform": "runninghub",
  "promptId": "1949480441804955649",
  "instanceId": "",  // ⚠️ 注意：RunningHub任务的instanceId为空字符串
  "url": "wss://www.runninghub.cn:443/ws/c_instance?...",
  "taskId": "1949480441804955649",
  "netWssUrl": "wss://www.runninghub.cn:443/ws/c_instance?...",
  "clientId": "087c9dcd6e753a3faa83119807775ef4",
  "taskStatus": "RUNNING",
  "promptTips": "{\"result\": true, \"error\": null, \"outputs_to_execute\": [\"32\", \"36\"], \"node_errors\": {}}"
}
```

### 2. 订阅条件检查

#### **修复前的问题：**
```javascript
// 原有条件要求所有任务都有instanceId
task.url && task.promptId && task.instanceId
```

#### **修复后的条件：**
```javascript
// 根据平台类型使用不同的订阅条件
const shouldSubscribe = !globalWebSocketManager.subscribedTasks.has(task.taskId) &&
  task.status === 'processing' &&
  !completedTasksRef.current.has(task.taskId) &&
  task.promptId && // 所有平台都需要promptId
  (isRunningHubTask ? 
    // RunningHub任务：需要netWssUrl
    task.netWssUrl :
    // ComfyUI任务：需要url和instanceId
    (task.url && task.instanceId)
  );
```

### 3. 平台检测逻辑

```javascript
// 检测任务平台类型
const isRunningHubTask = task.platform === 'runninghub' || task.netWssUrl;
```

**检测优先级：**
1. 明确的`task.platform`字段
2. 存在`task.netWssUrl`字段（RunningHub特征）

### 4. 调试日志输出

添加了详细的调试日志来帮助诊断问题：

```javascript
console.log('检查处理中的任务:', {
  总任务数: tasks.length,
  处理中任务数: processingTasks.length,
  处理中任务: processingTasks.map(t => ({
    taskId: t.taskId,
    platform: t.platform,
    hasNetWssUrl: !!t.netWssUrl,
    hasUrl: !!t.url,
    hasInstanceId: !!t.instanceId,
    promptId: t.promptId
  }))
});

console.log(`任务 ${task.taskId} 订阅检查:`, {
  已订阅: globalWebSocketManager.subscribedTasks.has(task.taskId),
  状态: task.status,
  已完成: completedTasksRef.current.has(task.taskId),
  promptId: !!task.promptId,
  isRunningHubTask,
  netWssUrl: !!task.netWssUrl,
  url: !!task.url,
  instanceId: !!task.instanceId,
  shouldSubscribe
});
```

### 5. 预期的正确日志输出

#### **RunningHub任务：**
```
检查处理中的任务: {
  总任务数: 1,
  处理中任务数: 1,
  处理中任务: [{
    taskId: "1949480441804955649",
    platform: "runninghub",
    hasNetWssUrl: true,
    hasUrl: true,
    hasInstanceId: false,  // ✅ RunningHub任务instanceId为空是正常的
    promptId: "1949480441804955649"
  }]
}

任务 1949480441804955649 订阅检查: {
  已订阅: false,
  状态: "processing",
  已完成: false,
  promptId: true,
  isRunningHubTask: true,
  netWssUrl: true,
  url: true,
  instanceId: false,
  shouldSubscribe: true  // ✅ 应该为true
}

开始监听新任务: {
  taskId: "1949480441804955649",
  platform: "runninghub",
  hasNetWssUrl: true,
  url: "wss://www.runninghub.cn:443/ws/c_instance?...",
  instanceId: ""
}

订阅runninghub任务: {
  taskId: "1949480441804955649",
  platform: "runninghub",
  hasNetWssUrl: true
}

发送订阅消息: {
  type: "subscribe_task",
  task_id: "1949480441804955649",
  prompt_id: "1949480441804955649",
  instance_id: "",
  instance_ws_url: "wss://www.runninghub.cn:443/ws/c_instance?...",
  platform: "runninghub",
  task_data: {
    netWssUrl: "wss://www.runninghub.cn:443/ws/c_instance?...",
    clientId: "087c9dcd6e753a3faa83119807775ef4",
    taskStatus: "RUNNING",
    promptTips: "{\"result\": true, \"outputs_to_execute\": [\"32\", \"36\"]}"
  }
}
```

#### **ComfyUI任务（向后兼容）：**
```
任务 comfyui_task_123 订阅检查: {
  已订阅: false,
  状态: "processing",
  已完成: false,
  promptId: true,
  isRunningHubTask: false,
  netWssUrl: false,
  url: true,
  instanceId: true,  // ✅ ComfyUI任务需要instanceId
  shouldSubscribe: true
}
```

### 6. 常见问题排查

#### **问题1：shouldSubscribe为false**
**可能原因：**
- 任务已经订阅过了
- 任务状态不是'processing'
- 任务已标记为完成
- 缺少必要字段（promptId、netWssUrl等）

**解决方法：**
检查调试日志中的各个条件值

#### **问题2：RunningHub任务不订阅**
**可能原因：**
- `task.netWssUrl`不存在
- `task.platform`不是'runninghub'且没有netWssUrl

**解决方法：**
确保任务数据包含正确的平台标识

#### **问题3：WebSocket连接问题**
**可能原因：**
- WebSocket连接未建立
- 消息发送失败

**解决方法：**
检查WebSocket连接状态和发送日志

### 7. 手动测试步骤

#### **步骤1：检查任务数据**
在浏览器开发者工具中查看任务数据：
```javascript
// 在控制台中执行
console.log('当前任务列表:', tasks);
```

#### **步骤2：检查WebSocket状态**
```javascript
// 检查WebSocket连接状态
console.log('WebSocket状态:', globalWebSocketManager.getConnectionStatus());
```

#### **步骤3：手动触发订阅**
```javascript
// 手动订阅测试
const testTask = {
  taskId: "test_task_123",
  promptId: "test_prompt_456",
  platform: "runninghub",
  netWssUrl: "wss://www.runninghub.cn:443/ws/c_instance?...",
  status: "processing"
};

globalWebSocketManager.subscribeTask(
  testTask.taskId,
  testTask.promptId,
  "",
  testTask.netWssUrl,
  {
    onProgress: (progress, max, platform) => {
      console.log(`测试进度: ${progress}/${max} (${platform})`);
    }
  },
  "runninghub",
  { netWssUrl: testTask.netWssUrl }
);
```

### 8. 修复验证

修复后应该看到：
1. ✅ RunningHub任务能够正确订阅（即使instanceId为空）
2. ✅ 发送正确的订阅消息到WebSocket
3. ✅ 包含平台特定的task_data
4. ✅ 保持ComfyUI任务的向后兼容性

### 9. 后续监控

持续监控以下指标：
- 任务订阅成功率
- WebSocket消息发送成功率
- 进度更新接收率
- 平台检测准确率

通过这些调试步骤，应该能够识别并解决WebSocket任务订阅问题。
