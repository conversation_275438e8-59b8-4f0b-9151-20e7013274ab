const { createError } = require('../../utils/error');
const logger = require('../../utils/logger');

/**
 * CSRF验证中间件
 * 验证非GET请求中的CSRF令牌
 * 
 * ===== 【生产环境部署注意事项】 =====
 * 
 * 警告：当前配置在开发环境中完全禁用了CSRF验证，这在生产环境中是不安全的！
 * 部署到生产环境前，请确保：
 * 
 * 1. 移除或注释掉开发环境中禁用CSRF验证的代码块
 * 2. 确保前端正确生成并传递CSRF令牌（检查src/utils/security/csrf.js）
 * 3. 确保所有需要排除的API路径都在app.js中的csrfProtection配置中列出
 * 4. 考虑使用环境变量来配置CSRF令牌的有效期
 * 
 * 【生产环境中常见问题及解决方案】
 * 
 * - 问题：上传大文件时CSRF验证失败
 *   解决方案：对文件上传API使用特定的排除配置，或实现自定义验证逻辑
 * 
 * - 问题：令牌过期太快导致用户体验不佳
 *   解决方案：调整maxAge值，或实现令牌自动刷新机制
 * 
 * - 问题：多设备登录时令牌冲突
 *   解决方案：将令牌与会话ID绑定，或改用服务器端会话存储
 * 
 * 【安全加固建议】
 * 
 * - 考虑实现双重令牌策略（一个在cookie中，一个在请求头中）
 * - 为不同的用户和API端点使用不同的令牌密钥
 * - 实现令牌轮换机制，定期自动更新令牌
 * - 添加IP地址或用户代理验证，增加额外安全层
 */
const validateCSRF = (req, res, next) => {
  // 在开发环境中完全禁用 CSRF 验证
  if (process.env.NODE_ENV === 'development') {
    console.log('开发环境：已禁用 CSRF 验证');
    return next();
  }

  // 跳过GET、HEAD、OPTIONS请求的验证
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  try {
    // 记录完整请求信息用于调试
    logger.debug(`CSRF验证 - 路径: ${req.path}, 方法: ${req.method}`);
    logger.debug(`CSRF验证 - Headers: ${JSON.stringify(req.headers)}`);
    
    // 从请求头中获取CSRF令牌
    const csrfToken = req.headers['x-csrf-token'];

    // 如果没有提供令牌，返回403错误
    if (!csrfToken) {
      logger.warn(`CSRF验证失败 - 令牌缺失 - 路径: ${req.path}`);
      throw createError(403, 'CSRF令牌缺失');
    }

    // 验证令牌格式
    // 令牌格式应为: randomString_timestamp
    const tokenParts = csrfToken.split('_');
    if (tokenParts.length !== 2) {
      logger.warn(`CSRF验证失败 - 令牌格式无效: ${csrfToken}`);
      throw createError(403, 'CSRF令牌格式无效');
    }

    const timestamp = parseInt(tokenParts[1], 10);
    
    // 验证令牌是否过期（可选，根据需求设置过期时间）
    // 这里设置令牌有效期为24小时
    const tokenAge = Date.now() - timestamp;
    // 在开发环境中延长有效期
    const maxAge = process.env.NODE_ENV === 'development' 
      ? 7 * 24 * 60 * 60 * 1000  // 开发环境设为7天
      : 24 * 60 * 60 * 1000;     // 生产环境24小时
    
    if (isNaN(timestamp) || tokenAge > maxAge) {
      logger.warn(`CSRF验证失败 - 令牌已过期: ${csrfToken}, 年龄: ${tokenAge}ms`);
      throw createError(403, 'CSRF令牌已过期');
    }

    // 令牌验证通过
    logger.debug(`CSRF验证通过 - 令牌: ${csrfToken}`);
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * 创建CSRF验证中间件，可以排除特定路径
 * @param {Array} excludePaths 不需要CSRF验证的路径数组
 * @returns {Function} CSRF验证中间件
 * 
 * 【部署前检查清单】
 * 1. 审查排除路径列表，确保只有必要的路径被排除
 * 2. 对于文件上传等特殊API，考虑使用其他安全措施弥补CSRF保护的缺失
 * 3. 确保所有排除路径都有明确的安全审查和记录
 * 4. 定期检查并更新排除路径列表，移除不再需要的排除项
 */
const csrfProtection = (excludePaths = []) => {
  return (req, res, next) => {
    // 检查请求路径是否在排除列表中
    const isExcluded = excludePaths.some(path => {
      if (typeof path === 'string') {
        return req.path === path;
      } else if (path instanceof RegExp) {
        return path.test(req.path);
      }
      return false;
    });

    // 如果路径在排除列表中，跳过CSRF验证
    if (isExcluded) {
      return next();
    }

    // 否则执行CSRF验证
    validateCSRF(req, res, next);
  };
};

module.exports = {
  validateCSRF,
  csrfProtection
}; 