const workflow = {
  prompt: {
    "3": {
      "inputs": {
        "rem_mode": "RMBG-2.0",
        "image_output": "Hide",
        "save_prefix": "ComfyUI",
        "torchscript_jit": false,
        "add_background": "none",
        "refine_foreground": false,
        "images": [
          "39",
          0
        ]
      },
      "class_type": "easy imageRemBg",
      "_meta": {
        "title": "Image Remove Bg"
      }
    },
    "4": {
      "inputs": {
        "ckpt_name": "dreamshaperXL_v21TurboDPMSDE.safetensors",
        "vae_name": "sdxl_vae.safetensors",
        "clip_skip": -2,
        "lora_name": "None",
        "lora_model_strength": 1,
        "lora_clip_strength": 1,
        "positive": [
          "20",
          0
        ],
        "negative": [
          "19",
          1
        ],
        "token_normalization": "none",
        "weight_interpretation": "comfy",
        "empty_latent_width": 512,
        "empty_latent_height": 512,
        "batch_size": 1,
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "Efficient Loader",
      "_meta": {
        "title": "Efficient Loader"
      }
    },
    "5": {
      "inputs": {
        "scale": 0.9,
        "start_at": 0,
        "end_at": 10000,
        "model": [
          "14",
          0
        ],
        "vae": [
          "4",
          4
        ],
        "image": [
          "8",
          0
        ],
        "mask": [
          "24",
          0
        ],
        "brushnet": [
          "6",
          0
        ],
        "positive": [
          "4",
          1
        ],
        "negative": [
          "4",
          2
        ]
      },
      "class_type": "BrushNet",
      "_meta": {
        "title": "BrushNet"
      }
    },
    "6": {
      "inputs": {
        "brushnet": "segmentation_mask_brushnet_ckpt_sdxl_v1/segmentation_mask_brushnet_ckpt_sdxl_v1.safetensors",
        "dtype": "float16"
      },
      "class_type": "BrushNetLoader",
      "_meta": {
        "title": "BrushNet Loader"
      }
    },
    "7": {
      "inputs": {
        "aspect_ratio": "original",
        "proportional_width": 1,
        "proportional_height": 1,
        "fit": "letterbox",
        "method": "lanczos",
        "round_to_multiple": "8",
        "scale_to_side": "longest",
        "scale_to_length": 1536,
        "background_color": "#000000",
        "image": [
          "3",
          0
        ],
        "mask": [
          "3",
          1
        ]
      },
      "class_type": "LayerUtility: ImageScaleByAspectRatio V2",
      "_meta": {
        "title": "LayerUtility: ImageScaleByAspectRatio V2"
      }
    },
    "8": {
      "inputs": {
        "fill_background": true,
        "background_color": "#FFFFFF",
        "RGBA_image": [
          "7",
          0
        ],
        "mask": [
          "7",
          1
        ]
      },
      "class_type": "LayerUtility: ImageRemoveAlpha",
      "_meta": {
        "title": "LayerUtility: ImageRemoveAlpha"
      }
    },
    "10": {
      "inputs": {
        "seed": 1086681715966814,
        "steps": 15,
        "cfg": 3,
        "sampler_name": "euler_ancestral",
        "scheduler": "karras",
        "denoise": 1,
        "model": [
          "5",
          0
        ],
        "positive": [
          "5",
          1
        ],
        "negative": [
          "5",
          2
        ],
        "latent_image": [
          "36",
          0
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "随机种子"
      }
    },
    "11": {
      "inputs": {
        "samples": [
          "10",
          0
        ],
        "vae": [
          "4",
          4
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE Decode"
      }
    },
    "12": {
      "inputs": {
        "images": [
          "11",
          0
        ]
      },
      "class_type": "PreviewImage",
      "_meta": {
        "title": "Preview Image"
      }
    },
    "13": {
      "inputs": {
        "preset": "PLUS (high strength)",
        "model": [
          "4",
          0
        ]
      },
      "class_type": "IPAdapterUnifiedLoader",
      "_meta": {
        "title": "IPAdapter Unified Loader"
      }
    },
    "14": {
      "inputs": {
        "weight": 0.8,
        "weight_type": "style and composition",
        "combine_embeds": "concat",
        "start_at": 0,
        "end_at": 1,
        "embeds_scaling": "V only",
        "model": [
          "13",
          0
        ],
        "ipadapter": [
          "13",
          1
        ],
        "image": [
          "17",
          0
        ]
      },
      "class_type": "IPAdapterAdvanced",
      "_meta": {
        "title": "IPAdapter Advanced"
      }
    },
    "17": {
      "inputs": {
        "height": [
          "7",
          4
        ],
        "width": [
          "7",
          3
        ],
        "interpolation_mode": "bicubic",
        "image": [
          "38",
          0
        ]
      },
      "class_type": "JWImageResize",
      "_meta": {
        "title": "Image Resize"
      }
    },
    "18": {
      "inputs": {
        "model": "wd-eva02-large-tagger-v3",
        "threshold": 0.35,
        "character_threshold": 0.85,
        "replace_underscore": false,
        "trailing_comma": false,
        "exclude_tags": "",
        "image": [
          "38",
          0
        ]
      },
      "class_type": "WD14Tagger|pysssss",
      "_meta": {
        "title": "WD14 Tagger 🐍"
      }
    },
    "19": {
      "inputs": {
        "styles": "fooocus_styles",
        "select_styles": "Fooocus Negative,artstyle-hyperrealism"
      },
      "class_type": "easy stylesSelector",
      "_meta": {
        "title": "Styles Selector"
      }
    },
    "20": {
      "inputs": {
        "delimiter": ", ",
        "clean_whitespace": "true",
        "text_a": [
          "18",
          0
        ],
        "text_b": [
          "19",
          0
        ],
        "text_c": ""
      },
      "class_type": "Text Concatenate",
      "_meta": {
        "title": "Text Concatenate"
      }
    },
    "23": {
      "inputs": {
        "images": [
          "8",
          0
        ]
      },
      "class_type": "PreviewImage",
      "_meta": {
        "title": "Preview Image"
      }
    },
    "24": {
      "inputs": {
        "mask": [
          "7",
          1
        ]
      },
      "class_type": "InvertMask",
      "_meta": {
        "title": "InvertMask"
      }
    },
    "25": {
      "inputs": {
        "kernel": 1,
        "sigma": 30,
        "inpaint": [
          "11",
          0
        ],
        "original": [
          "7",
          0
        ],
        "mask": [
          "24",
          0
        ]
      },
      "class_type": "BlendInpaint",
      "_meta": {
        "title": "Blend Inpaint"
      }
    },
    "26": {
      "inputs": {
        "images": [
          "25",
          0
        ]
      },
      "class_type": "PreviewImage",
      "_meta": {
        "title": "Preview Image"
      }
    },
    "27": {
      "inputs": {
        "upscale_method": "lanczos",
        "scale_by": 1.5,
        "image": [
          "25",
          0
        ]
      },
      "class_type": "ImageScaleBy",
      "_meta": {
        "title": "Upscale Image By"
      }
    },
    "28": {
      "inputs": {
        "width": [
          "30",
          0
        ],
        "height": [
          "30",
          1
        ],
        "keep_proportions": false,
        "upscale_method": "nearest-exact",
        "crop": "disabled",
        "mask": [
          "31",
          0
        ]
      },
      "class_type": "ResizeMask",
      "_meta": {
        "title": "Resize Mask"
      }
    },
    "29": {
      "inputs": {
        "noise_mask": true,
        "positive": [
          "4",
          1
        ],
        "negative": [
          "4",
          2
        ],
        "vae": [
          "4",
          4
        ],
        "pixels": [
          "27",
          0
        ],
        "mask": [
          "28",
          0
        ]
      },
      "class_type": "InpaintModelConditioning",
      "_meta": {
        "title": "InpaintModelConditioning"
      }
    },
    "30": {
      "inputs": {
        "image": [
          "27",
          0
        ]
      },
      "class_type": "easy imageSize",
      "_meta": {
        "title": "ImageSize"
      }
    },
    "31": {
      "inputs": {
        "invert_mask": false,
        "grow": -2,
        "blur": 2,
        "mask": [
          "24",
          0
        ]
      },
      "class_type": "LayerMask: MaskGrow",
      "_meta": {
        "title": "LayerMask: MaskGrow"
      }
    },
    "32": {
      "inputs": {
        "seed": 1086681715966814,
        "steps": 15,
        "cfg": 3,
        "sampler_name": "euler_ancestral",
        "scheduler": "karras",
        "denoise": 0.4000000000000001,
        "model": [
          "4",
          0
        ],
        "positive": [
          "29",
          0
        ],
        "negative": [
          "29",
          1
        ],
        "latent_image": [
          "29",
          2
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "KSampler"
      }
    },
    "33": {
      "inputs": {
        "samples": [
          "32",
          0
        ],
        "vae": [
          "4",
          4
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE Decode"
      }
    },
    "36": {
      "inputs": {
        "amount": 2,
        "samples": [
          "5",
          3
        ]
      },
      "class_type": "RepeatLatentBatch",
      "_meta": {
        "title": "图片数量"
      }
    },
    "37": {
      "inputs": {
        "filename_prefix": "Background",
        "images": [
          "33",
          0
        ]
      },
      "class_type": "SaveImage",
      "_meta": {
        "title": "Save Image"
      }
    },
    "38": {
      "inputs": {
        "url": "https://",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "背景图片上传"
      }
    },
    "39": {
      "inputs": {
        "url": "https://",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "前景图片上传"
      }
    }
  }
};

module.exports = workflow; 