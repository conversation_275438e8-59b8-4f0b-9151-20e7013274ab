/**
 * 初始化工作流数据脚本
 * 用于向数据库中添加默认的工作流定义
 */

const mongoose = require('mongoose');
const path = require('path');

// 加载环境变量
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

// 导入模型
const Workflow = require('../src/models/Workflow');

// 默认工作流数据
const defaultWorkflows = [
  // 款式设计类
  {
    id: 'A01-trending',
    name: '爆款开发',
    displayName: '爆款开发',
    category: '款式设计',
    description: '基于趋势分析生成爆款设计，结合市场热点和流行元素',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui', 'runninghub'],
    recommendedPlatform: 'runninghub',
    tags: ['趋势', '爆款', '设计'],
    priority: 10
  },
  {
    id: 'A02-optimize',
    name: '款式优化',
    displayName: '款式优化',
    category: '款式设计',
    description: '优化现有款式设计，提升设计质量和市场竞争力',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui', 'runninghub'],
    recommendedPlatform: 'auto',
    tags: ['优化', '改进', '设计'],
    priority: 8
  },
  {
    id: 'A02b-optimizetext',
    name: '文本优化',
    displayName: '文本优化',
    category: '款式设计',
    description: '优化设计文本描述，提升产品描述质量',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui', 'runninghub'],
    recommendedPlatform: 'auto',
    tags: ['文本', '优化', '描述'],
    priority: 6
  },
  {
    id: 'A03-inspiration',
    name: '灵感探索',
    displayName: '灵感探索',
    category: '款式设计',
    description: '探索设计灵感和创意，激发设计师创作思路',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui'],
    recommendedPlatform: 'comfyui',
    tags: ['灵感', '创意', '探索'],
    priority: 7
  },
  {
    id: 'A05-drawing',
    name: '生成线稿',
    displayName: '生成线稿',
    category: '款式设计',
    description: '生成设计线稿图，为设计提供基础框架',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui'],
    recommendedPlatform: 'comfyui',
    tags: ['线稿', '绘图', '基础'],
    priority: 5
  },
  {
    id: 'A06-divergent',
    name: '爆款延伸',
    displayName: '爆款延伸',
    category: '款式设计',
    description: '基于爆款进行延伸设计，创造更多相关产品',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui', 'runninghub'],
    recommendedPlatform: 'runninghub',
    tags: ['延伸', '爆款', '系列'],
    priority: 9
  },

  // 模特图类
  {
    id: 'B01-fashion',
    name: '时尚大片',
    displayName: '时尚大片',
    category: '模特图',
    description: '生成时尚大片效果，展现产品的时尚魅力',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui', 'runninghub'],
    recommendedPlatform: 'runninghub',
    tags: ['时尚', '大片', '摄影'],
    priority: 10
  },
  {
    id: 'B02-tryonauto',
    name: '自动换装',
    displayName: '自动换装',
    category: '模特图',
    description: '自动换装效果，快速展示不同服装搭配',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui', 'runninghub'],
    recommendedPlatform: 'runninghub',
    tags: ['换装', '自动', '搭配'],
    priority: 9
  },
  {
    id: 'B02-tryonmanual',
    name: '手动换装',
    displayName: '手动换装',
    category: '模特图',
    description: '手动控制换装，精确调整服装细节',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui'],
    recommendedPlatform: 'comfyui',
    tags: ['换装', '手动', '精确'],
    priority: 7
  },
  {
    id: 'B03-changemodel',
    name: '换模特',
    displayName: '换模特',
    category: '模特图',
    description: '更换模特，适应不同的展示需求',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui'],
    recommendedPlatform: 'comfyui',
    tags: ['模特', '更换', '展示'],
    priority: 6
  },
  {
    id: 'B04-recolor',
    name: '服装复色',
    displayName: '服装复色',
    category: '模特图',
    description: '服装颜色调整，展示不同色彩方案',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui'],
    recommendedPlatform: 'comfyui',
    tags: ['颜色', '调整', '复色'],
    priority: 5
  },

  // 工具类
  {
    id: 'C01-extract',
    name: '图片取词',
    displayName: '图片取词',
    category: '工具',
    description: '从图片中提取关键词，辅助设计和营销',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui', 'runninghub'],
    recommendedPlatform: 'runninghub',
    tags: ['提取', '关键词', '分析'],
    priority: 8
  },
  {
    id: 'C02-upscale',
    name: '高清放大',
    displayName: '高清放大',
    category: '工具',
    description: '图片高清放大，提升图片质量和分辨率',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui', 'runninghub'],
    recommendedPlatform: 'auto',
    tags: ['放大', '高清', '质量'],
    priority: 7
  },
  {
    id: 'C03-mattingbg',
    name: '背景抠图',
    displayName: '背景抠图',
    category: '工具',
    description: '背景自动抠图，快速分离主体和背景',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui'],
    recommendedPlatform: 'comfyui',
    tags: ['抠图', '背景', '分离'],
    priority: 6
  },
  {
    id: 'C04-extend',
    name: '智能扩图',
    displayName: '智能扩图',
    category: '工具',
    description: '智能图片扩展，增加图片画布大小',
    version: '1.0.0',
    enabled: true,
    supportedPlatforms: ['comfyui'],
    recommendedPlatform: 'comfyui',
    tags: ['扩图', '智能', '画布'],
    priority: 5
  }
];

async function initWorkflows() {
  try {
    // 连接数据库
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aibikini');
    console.log('数据库连接成功');

    // 创建默认管理员用户ID（如果不存在）
    const adminUserId = new mongoose.Types.ObjectId();

    // 清空现有工作流（可选）
    const shouldClearExisting = process.argv.includes('--clear');
    if (shouldClearExisting) {
      await Workflow.deleteMany({});
      console.log('已清空现有工作流数据');
    }

    // 插入默认工作流
    for (const workflowData of defaultWorkflows) {
      const existingWorkflow = await Workflow.findOne({ id: workflowData.id });
      
      if (!existingWorkflow) {
        const workflow = new Workflow({
          ...workflowData,
          createdBy: adminUserId,
          parameters: new Map(),
          usageStats: {
            totalRuns: 0,
            successRuns: 0,
            failedRuns: 0
          }
        });
        
        await workflow.save();
        console.log(`✅ 创建工作流: ${workflowData.name} (${workflowData.id})`);
      } else {
        console.log(`⏭️  工作流已存在: ${workflowData.name} (${workflowData.id})`);
      }
    }

    console.log('\n🎉 工作流初始化完成！');
    console.log(`总共处理 ${defaultWorkflows.length} 个工作流`);
    
    // 显示统计信息
    const stats = await Workflow.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      }
    ]);
    
    console.log('\n📊 工作流分类统计:');
    stats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} 个`);
    });

  } catch (error) {
    console.error('❌ 初始化失败:', error);
  } finally {
    await mongoose.disconnect();
    console.log('数据库连接已关闭');
  }
}

// 运行初始化
if (require.main === module) {
  initWorkflows();
}

module.exports = { initWorkflows, defaultWorkflows };
