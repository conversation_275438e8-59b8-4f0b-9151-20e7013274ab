{"2": {"inputs": {"guidance": 50, "conditioning": ["5", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "4": {"inputs": {"text": "low quality,blurry,", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "5": {"inputs": {"text": "", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "6": {"inputs": {"model": ["69", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "7": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "9": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "11": {"inputs": {"direction": "left-right", "pixels": 0, "method": "auto", "image_1": ["58", 1], "image_2": ["59", 1], "mask_1": ["58", 2], "mask_2": ["59", 2]}, "class_type": "easy makeImageForICLora", "_meta": {"title": "Make Image For ICLora"}}, "12": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "13": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "17": {"inputs": {"samples": ["27", 0], "vae": ["7", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "26": {"inputs": {"noise_mask": true, "positive": ["87", 0], "negative": ["4", 0], "vae": ["7", 0], "pixels": ["11", 0], "mask": ["11", 1]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "27": {"inputs": {"needInput": true, "seed": 542733706889847, "steps": 25, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["6", 0], "positive": ["26", 0], "negative": ["26", 1], "latent_image": ["28", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "随机种子"}}, "28": {"inputs": {"needInput": true, "amount": 2, "samples": ["26", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "图片数量"}}, "58": {"inputs": {"context_expand_pixels": 20, "context_expand_factor": 1.0000000000000002, "fill_mask_holes": false, "blur_mask_pixels": 16, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 512, "force_height": 512, "rescale_factor": 1.0000000000000002, "min_width": 1, "min_height": 1, "max_width": 1024, "max_height": 1024, "padding": 32, "image": ["95", 0], "mask": ["83", 0]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "59": {"inputs": {"context_expand_pixels": 20, "context_expand_factor": 1.0000000000000002, "fill_mask_holes": false, "blur_mask_pixels": 16, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "forced size", "force_width": ["80", 0], "force_height": ["80", 1], "rescale_factor": 1.0000000000000002, "min_width": 512, "min_height": 512, "max_width": 768, "max_height": 768, "padding": 32, "image": ["76", 0], "mask": ["86", 0]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "60": {"inputs": {"x": ["11", 5], "y": 0, "width": ["11", 3], "height": ["11", 4], "image": ["17", 0]}, "class_type": "ETN_CropImage", "_meta": {"title": "Crop Image"}}, "61": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["59", 0], "inpainted_image": ["67", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Stitch node)"}}, "67": {"inputs": {"image": ["60", 0]}, "class_type": "ImpactImageBatchToImageList", "_meta": {"title": "Image Batch to Image List"}}, "68": {"inputs": {"images": ["61", 0]}, "class_type": "ImageListToImageBatch", "_meta": {"title": "Image List to Image Batch"}}, "69": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "75": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "上传衣服图并涂蒙版"}}, "76": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "上传模特图并涂蒙版"}}, "79": {"inputs": {"filename_prefix": "DetailMigration", "images": ["68", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "80": {"inputs": {"image": ["58", 1]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}, "81": {"inputs": {"masks": ["75", 1]}, "class_type": "Mask Fill Holes", "_meta": {"title": "Mask Fill Holes"}}, "82": {"inputs": {"mask": ["81", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "83": {"inputs": {"channel": "red", "image": ["82", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "84": {"inputs": {"masks": ["76", 1]}, "class_type": "Mask Fill Holes", "_meta": {"title": "Mask Fill Holes"}}, "85": {"inputs": {"mask": ["84", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "86": {"inputs": {"channel": "red", "image": ["85", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "87": {"inputs": {"downsampling_factor": 1, "downsampling_function": "area", "mode": "autocrop with mask", "weight": 1, "autocrop_margin": 0.1, "conditioning": ["2", 0], "style_model": ["13", 0], "clip_vision": ["12", 0], "image": ["58", 1], "mask": ["58", 2]}, "class_type": "ReduxAdvanced", "_meta": {"title": "ReduxAdvanced"}}, "95": {"inputs": {"fill_background": true, "background_color": "#FFFFFF", "RGBA_image": ["75", 0], "mask": ["83", 0]}, "class_type": "LayerUtility: ImageRemoveAlpha", "_meta": {"title": "LayerUtility: ImageRemoveAlpha"}}}