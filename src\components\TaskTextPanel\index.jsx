import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd';
import { showDeleteConfirmModal } from '../../utils/modalUtils';
import './index.css';

const isMobile = () => typeof window !== 'undefined' && window.innerWidth <= 768;

const TaskTextPanel = ({
  task,
  onEditTask,
  onDeleteTask,
  pageType,
}) => {

  const [showCopyToast, setShowCopyToast] = useState(false);
  const [isFakeProgress, setIsFakeProgress] = useState(false);
  const [fakeProgress, setFakeProgress] = useState(0);
  const fakeProgressTimer = React.useRef(null);
  const FAKE_PROGRESS_MAX = 0.98; // 98%
  const FAKE_PROGRESS_DURATION = 60000; // 60秒

  const handleCopyId = (e) => {
    e.stopPropagation();
    navigator.clipboard.writeText(task.taskId);
    setShowCopyToast(true);
    
    // 2秒后自动隐藏提示
    setTimeout(() => {
      setShowCopyToast(false);
    }, 2000);
  };

  // 缓入缓出函数
  function easeInOutCubic(x) {
    return x < 0.5
      ? 4 * x * x * x
      : 1 - Math.pow(-2 * x + 2, 3) / 2;
  }

  // 伪进度条启动逻辑
  useEffect(() => {
    if (task.status === 'processing' && (task.progress === undefined || task.progress === 0)) {
      setIsFakeProgress(true);
      setFakeProgress(0);
      const start = Date.now();
      fakeProgressTimer.current = setInterval(() => {
        const elapsed = Date.now() - start;
        let percent = Math.min(elapsed / FAKE_PROGRESS_DURATION, 1);
        let eased = easeInOutCubic(percent);
        let fake = eased * FAKE_PROGRESS_MAX;
        setFakeProgress(fake);
        if (percent >= 1) {
          clearInterval(fakeProgressTimer.current);
        }
      }, 80);
    } else {
      setIsFakeProgress(false);
      setFakeProgress(0);
      if (fakeProgressTimer.current) {
        clearInterval(fakeProgressTimer.current);
      }
    }
    // 清理定时器
    return () => {
      if (fakeProgressTimer.current) {
        clearInterval(fakeProgressTimer.current);
      }
    };
  }, [task.status, task.progress]);

  return (
    <div className={`task-card ${task.isNew ? 'new-task' : ''}`}>
      <div className="task-header">
        <div className="task-info">
          <span className="task-time">
            {task.createdAt
              ? new Date(task.createdAt).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false
                }).replace(/\//g, '-')
              : '--'
            }
          </span>
          <div className="task-id-container">
            <span className="task-id-label">任务ID:</span>
            <span className="task-id">{task.taskId}</span>
            <button 
              className="copy-id-btn"
              onClick={handleCopyId}
              title="复制任务ID"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
              </svg>
            </button>
            {showCopyToast && (
              <div className="copy-toast">已复制到剪贴板</div>
            )}
          </div>
        </div>
        <div className="task-actions">
          {!isMobile() && (
            <button 
              className="edit-btn"
              onClick={() => onEditTask(task)}
              disabled={task.status === 'processing'}
              title={task.status === 'processing' ? '生成中的任务无法编辑' : '重新编辑'}
            >
              重新编辑
            </button>
          )}
          <button 
            className="edit-btn"
            onClick={() => {
              if (task.extractedText) {
                navigator.clipboard.writeText(task.extractedText);
                message.success('文本已复制到剪贴板');
              }
            }}
            disabled={task.status === 'processing'}
            title={task.status === 'processing' ? '生成中的任务无法复制' : '复制文本'}
          >
            复制文本
          </button>
          <div className="more-actions">
            <button 
              className="more-btn"
              onClick={(e) => {
                e.stopPropagation();
                const currentMenu = e.currentTarget.nextElementSibling;
                const allMenus = document.querySelectorAll('.dropdown-menu');
                allMenus.forEach(menu => {
                  if (menu !== currentMenu) {
                    menu.classList.remove('show');
                  }
                });
                currentMenu.classList.toggle('show');
              }}
              disabled={task.status === 'processing'}
              title={task.status === 'processing' ? '生成中的任务暂时无法操作' : '更多操作'}
            >
              <span></span>
            </button>
            <div className="dropdown-menu">
              {isMobile() && (
                <button 
                  className="dropdown-item"
                  onClick={() => onEditTask(task)}
                  disabled={task.status === 'processing'}
                  title={task.status === 'processing' ? '生成中的任务无法编辑' : '重新编辑'}
                >
                  重新编辑
                </button>
              )}
              <button 
                className="dropdown-item delete"
                onClick={(e) => {
                  e.stopPropagation();
                  showDeleteConfirmModal({
                    title: '删除记录',
                    content: '确定要删除此记录吗？删除后将无法恢复。',
                    onOk: () => {
                      onDeleteTask(task.taskId);
                    },
                    okButtonProps: {
                      danger: true
                    }
                  });
                }}
              >
                删除记录
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="task-text-area">
        {task.status === 'failed' ? (
          <div className="empty-message">
            <span>任务生成失败</span>
          </div>
        ) : task.status === 'processing' || !task.extractedText ? (
          <>
            <div className="processing-row">
              <div className="processing-desc">
                {isFakeProgress ? '预处理中...' : '提取中...'}
              </div>
              <span className="processing-percent">
                {Math.round(((isFakeProgress ? fakeProgress : (task.progress===1 ? 0.99 : task.progress || 0)) * 100))}%
              </span>
            </div>
            <div className="task-progress-wrapper" style={{ position: 'relative', width: '100%' }}>
              <div className="task-progress">
                <div 
                  className="progress-bar"
                  style={{ 
                    width: `${((isFakeProgress ? fakeProgress : (task.progress===1 ? 0.99 : task.progress || 0)) * 100)}%`,
                    backgroundColor: '#1890ff'
                  }}
                />
              </div>
            </div>
          </>
        ) : (
          <div className="text-content">
            <p>{task.extractedText}</p>
          </div>
        )}
      </div>
    </div>
  );
};

TaskTextPanel.propTypes = {
  task: PropTypes.shape({
    taskId: PropTypes.string.isRequired,
    createdAt: PropTypes.oneOfType([
      PropTypes.instanceOf(Date),
      PropTypes.string
    ]).isRequired,
    status: PropTypes.oneOf(['processing', 'completed']).isRequired,
    imageCount: PropTypes.number.isRequired,
    settings: PropTypes.shape({
      source: PropTypes.object,
      generation: PropTypes.object
    }),
    components: PropTypes.oneOfType([
      PropTypes.array,
      PropTypes.object
    ]),
    generatedImages: PropTypes.arrayOf(PropTypes.object).isRequired,
    extractedText: PropTypes.string
  }).isRequired,
  onEditTask: PropTypes.func.isRequired,
  onDeleteTask: PropTypes.func.isRequired,
  pageType: PropTypes.string,
};

export default TaskTextPanel; 