import React, { useState, useCallback, useEffect } from 'react';
import ReactDOM from 'react-dom';
import { MdClose } from 'react-icons/md';
import PropTypes from 'prop-types';
import './index.css';

const ImagePreviewModal = ({ 
  visible, 
  imageUrl, 
  onClose, 
  alt = "预览图片",
  showHint = true,
  maxScale = 4,
  minScale = 0.5,
  featureName = null
}) => {
  const [previewScale, setPreviewScale] = useState(1);
  const [previewPosition, setPreviewPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isMobile, setIsMobile] = useState(false);
  
  // 移动端触摸相关状态 - 简化版本，不再需要复杂的状态管理

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                            window.innerWidth <= 768;
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 重置状态
  const resetPreview = useCallback(() => {
    setPreviewScale(1);
    setPreviewPosition({ x: 0, y: 0 });
    setIsDragging(false);
  }, []);

  // 关闭预览
  const handleClose = useCallback(() => {
    resetPreview();
    onClose();
  }, [onClose, resetPreview]);

  // 滚轮缩放
  const handleWheel = useCallback((e) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setPreviewScale(prev => {
      const newScale = prev * delta;
      return Math.max(minScale, Math.min(maxScale, newScale));
    });
  }, [maxScale, minScale]);

  // 鼠标拖拽开始
  const handleMouseDown = useCallback((e) => {
    if (e.button === 0) { // 只响应左键
      setIsDragging(true);
      setDragStart({
        x: e.clientX - previewPosition.x,
        y: e.clientY - previewPosition.y
      });
    }
  }, [previewPosition]);

  // 鼠标拖拽移动
  const handleMouseMove = useCallback((e) => {
    if (isDragging) {
      setPreviewPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  }, [isDragging, dragStart]);

  // 鼠标拖拽结束
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 移动端触摸计算函数 - 已简化，不再需要

  // 移动端触摸开始 - 简化版本，只处理点击关闭
  const handleTouchStart = useCallback((e) => {
    // 移动端禁用复杂的触摸操作，只保留点击关闭功能
    // 防止意外触发拖拽或缩放
  }, []);

  // 移动端触摸移动 - 简化版本
  const handleTouchMove = useCallback((e) => {
    // 移动端禁用触摸移动操作
    e.preventDefault(); // 防止页面滚动
  }, []);

  // 移动端触摸结束 - 简化版本
  const handleTouchEnd = useCallback(() => {
    // 移动端禁用触摸结束处理
  }, []);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!visible) return;
      
      switch (e.key) {
        case 'Escape':
          handleClose();
          break;
        case '=':
        case '+':
          e.preventDefault();
          setPreviewScale(prev => Math.min(maxScale, prev * 1.1));
          break;
        case '-':
          e.preventDefault();
          setPreviewScale(prev => Math.max(minScale, prev * 0.9));
          break;
        case '0':
          e.preventDefault();
          resetPreview();
          break;
        default:
          break;
      }
    };

    if (visible) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden'; // 防止背景滚动
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [visible, handleClose, resetPreview, maxScale, minScale]);

  // 当弹窗显示时重置状态
  useEffect(() => {
    if (visible) {
      resetPreview();
    }
  }, [visible, resetPreview]);

  if (!visible || !imageUrl) {
    return null;
  }

  return ReactDOM.createPortal(
    <div 
      className="image-preview-modal"
      onClick={handleClose}
      onWheel={handleWheel}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <div 
        className="preview-content"
        onClick={e => e.stopPropagation()}
        onMouseDown={handleMouseDown}
        style={{
          cursor: isDragging ? 'grabbing' : 'grab',
          transform: `translate(${previewPosition.x}px, ${previewPosition.y}px) scale(${previewScale})`,
          transition: isDragging ? 'none' : 'transform 0.1s ease-out',
          transformOrigin: 'center center'
        }}
      >
        <button 
          className="preview-close-button"
          onClick={handleClose}
          title={isMobile ? "关闭预览" : "关闭预览 (Esc)"}
        >
          <MdClose />
        </button>
        <div className="preview-image-container">
          <img 
            src={imageUrl} 
            alt={alt}
            style={{ pointerEvents: 'none' }}
          />
          {/* 功能名称或使用提示 */}
          {featureName ? (
            <div className="preview-feature-name">
              {featureName}
            </div>
          ) : (
            showHint && previewScale === 1 && !isMobile && (
              <div className="preview-hint">
                滚轮缩放 • 拖拽移动 • Esc关闭
              </div>
            )
          )}

        </div>
      </div>
    </div>,
    document.body
  );
};

ImagePreviewModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  imageUrl: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
  alt: PropTypes.string,
  showHint: PropTypes.bool,
  maxScale: PropTypes.number,
  minScale: PropTypes.number,
  featureName: PropTypes.oneOfType([PropTypes.string, PropTypes.oneOf([null])])
};

export default ImagePreviewModal; 