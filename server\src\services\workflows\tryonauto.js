const workflow = {
  prompt: {
    "1": {
      "inputs": {
        "width": 1026,
        "height": 1368,
        "interpolation": "lanczos",
        "method": "fill / crop",
        "condition": "always",
        "multiple_of": 0,
        "image": [
          "37",
          0
        ]
      },
      "class_type": "ImageResize+",
      "_meta": {
        "title": "🔧 Image Resize"
      }
    },
    "2": {
      "inputs": {
        "text": "",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        },
        "clip": [
          "9",
          0
        ]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIP Text Encode (Prompt)"
      }
    },
    "3": {
      "inputs": {
        "text": "",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        },
        "clip": [
          "9",
          0
        ]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIP Text Encode (Prompt)"
      }
    },
    "4": {
      "inputs": {
        "guidance": 30,
        "conditioning": [
          "3",
          0
        ]
      },
      "class_type": "FluxGuidance",
      "_meta": {
        "title": "FluxGuidance"
      }
    },
    "5": {
      "inputs": {
        "model": [
          "15",
          0
        ]
      },
      "class_type": "DifferentialDiffusion",
      "_meta": {
        "title": "Differential Diffusion"
      }
    },
    "6": {
      "inputs": {
        "crop": "center",
        "clip_vision": [
          "31",
          0
        ],
        "image": [
          "37",
          0
        ]
      },
      "class_type": "CLIPVisionEncode",
      "_meta": {
        "title": "CLIP Vision Encode"
      }
    },
    "7": {
      "inputs": {
        "strength": 1.0000000000000002,
        "strength_type": "multiply",
        "conditioning": [
          "4",
          0
        ],
        "style_model": [
          "8",
          0
        ],
        "clip_vision_output": [
          "6",
          0
        ]
      },
      "class_type": "StyleModelApply",
      "_meta": {
        "title": "Apply Style Model"
      }
    },
    "8": {
      "inputs": {
        "style_model_name": "flux1-redux-dev.safetensors"
      },
      "class_type": "StyleModelLoader",
      "_meta": {
        "title": "Load Style Model"
      }
    },
    "9": {
      "inputs": {
        "clip_name1": "clip_l.safetensors",
        "clip_name2": "t5xxl_fp16.safetensors",
        "type": "flux",
        "device": "default"
      },
      "class_type": "DualCLIPLoader",
      "_meta": {
        "title": "DualCLIPLoader"
      }
    },
    "10": {
      "inputs": {
        "width": [
          "1",
          1
        ],
        "height": [
          "1",
          2
        ],
        "interpolation": "lanczos",
        "method": "fill / crop",
        "condition": "always",
        "multiple_of": 0,
        "image": [
          "38",
          0
        ]
      },
      "class_type": "ImageResize+",
      "_meta": {
        "title": "🔧 Image Resize"
      }
    },
    "11": {
      "inputs": {
        "direction": "right",
        "match_image_size": true,
        "image1": [
          "1",
          0
        ],
        "image2": [
          "10",
          0
        ]
      },
      "class_type": "ImageConcanate",
      "_meta": {
        "title": "Image Concatenate"
      }
    },
    "12": {
      "inputs": {
        "left": [
          "1",
          1
        ],
        "top": 0,
        "right": 0,
        "bottom": 0,
        "feathering": 0,
        "image": [
          "10",
          0
        ]
      },
      "class_type": "ImagePadForOutpaint",
      "_meta": {
        "title": "Pad Image for Outpainting"
      }
    },
    "13": {
      "inputs": {
        "purge_cache": true,
        "purge_models": true,
        "anything": [
          "1",
          0
        ]
      },
      "class_type": "LayerUtility: PurgeVRAM",
      "_meta": {
        "title": "LayerUtility: Purge VRAM"
      }
    },
    "14": {
      "inputs": {
        "purge_cache": true,
        "purge_models": true,
        "anything": [
          "10",
          0
        ]
      },
      "class_type": "LayerUtility: PurgeVRAM",
      "_meta": {
        "title": "LayerUtility: Purge VRAM"
      }
    },
    "15": {
      "inputs": {
        "unet_name": "flux1-fill-dev.safetensors",
        "weight_dtype": "fp8_e4m3fn"
      },
      "class_type": "UNETLoader",
      "_meta": {
        "title": "Load Diffusion Model"
      }
    },
    "16": {
      "inputs": {
        "vae_name": "ae.sft"
      },
      "class_type": "VAELoader",
      "_meta": {
        "title": "Load VAE"
      }
    },
    "17": {
      "inputs": {
        "left": 0,
        "top": 0,
        "right": [
          "1",
          1
        ],
        "bottom": 0,
        "feathering": 0,
        "image": [
          "1",
          0
        ]
      },
      "class_type": "ImagePadForOutpaint",
      "_meta": {
        "title": "Pad Image for Outpainting"
      }
    },
    "18": {
      "inputs": {
        "samples": [
          "24",
          0
        ],
        "vae": [
          "16",
          0
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE Decode"
      }
    },
    "19": {
      "inputs": {
        "image": [
          "18",
          0
        ]
      },
      "class_type": "ImpactImageBatchToImageList",
      "_meta": {
        "title": "Image Batch to Image List"
      }
    },
    "20": {
      "inputs": {
        "noise_mask": false,
        "positive": [
          "7",
          0
        ],
        "negative": [
          "2",
          0
        ],
        "vae": [
          "16",
          0
        ],
        "pixels": [
          "26",
          1
        ],
        "mask": [
          "26",
          2
        ]
      },
      "class_type": "InpaintModelConditioning",
      "_meta": {
        "title": "InpaintModelConditioning"
      }
    },
    "21": {
      "inputs": {
        "images": [
          "28",
          0
        ]
      },
      "class_type": "ImageListToImageBatch",
      "_meta": {
        "title": "Image List to Image Batch"
      }
    },
    "24": {
      "inputs": {
        "seed": 938646097656208,
        "steps": 25,
        "cfg": 1,
        "sampler_name": "euler",
        "scheduler": "normal",
        "denoise": 1,
        "model": [
          "5",
          0
        ],
        "positive": [
          "20",
          0
        ],
        "negative": [
          "20",
          1
        ],
        "latent_image": [
          "30",
          0
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "随机种子"
      }
    },
    "25": {
      "inputs": {
        "rescale_algorithm": "bicubic",
        "stitch": [
          "26",
          0
        ],
        "inpainted_image": [
          "19",
          0
        ]
      },
      "class_type": "InpaintStitch",
      "_meta": {
        "title": "(OLD 💀, use the new ✂️ Inpaint Stitch node)"
      }
    },
    "26": {
      "inputs": {
        "context_expand_pixels": 10,
        "context_expand_factor": 1.0000000000000002,
        "fill_mask_holes": true,
        "blur_mask_pixels": 0,
        "invert_mask": false,
        "blend_pixels": 16,
        "rescale_algorithm": "bicubic",
        "mode": "ranged size",
        "force_width": 1024,
        "force_height": 1024,
        "rescale_factor": 1.0000000000000002,
        "min_width": 512,
        "min_height": 512,
        "max_width": 1024,
        "max_height": 1024,
        "padding": 32,
        "image": [
          "11",
          0
        ],
        "mask": [
          "29",
          0
        ],
        "optional_context_mask": [
          "34",
          1
        ]
      },
      "class_type": "InpaintCrop",
      "_meta": {
        "title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"
      }
    },
    "27": {
      "inputs": {
        "filename_prefix": "TryonAuto",
        "images": [
          "21",
          0
        ]
      },
      "class_type": "SaveImage",
      "_meta": {
        "title": "Save Image"
      }
    },
    "28": {
      "inputs": {
        "x": [
          "1",
          1
        ],
        "y": 0,
        "width": [
          "1",
          1
        ],
        "height": [
          "1",
          2
        ],
        "image": [
          "25",
          0
        ]
      },
      "class_type": "ETN_CropImage",
      "_meta": {
        "title": "Crop Image"
      }
    },
    "29": {
      "inputs": {
        "expand": 30,
        "tapered_corners": true,
        "mask": [
          "36",
          1
        ]
      },
      "class_type": "GrowMask",
      "_meta": {
        "title": "蒙版扩张"
      }
    },
    "30": {
      "inputs": {
        "amount": 1,
        "samples": [
          "20",
          2
        ]
      },
      "class_type": "RepeatLatentBatch",
      "_meta": {
        "title": "图片数量"
      }
    },
    "31": {
      "inputs": {
        "clip_name": "sigclip_vision_patch14_384.safetensors"
      },
      "class_type": "CLIPVisionLoader",
      "_meta": {
        "title": "Load CLIP Vision"
      }
    },
    "34": {
      "inputs": {
        "threshold": 0.3,
        "detail_method": "VITMatte",
        "detail_erode": 6,
        "detail_dilate": 6,
        "black_point": 0.15,
        "white_point": 0.99,
        "process_detail": true,
        "prompt": "swimsuit",
        "device": "cuda",
        "max_megapixels": 2,
        "image": [
          "17",
          0
        ],
        "sam_models": [
          "35",
          0
        ]
      },
      "class_type": "LayerMask: SegmentAnythingUltra V3",
      "_meta": {
        "title": "款式描述-服装"
      }
    },
    "35": {
      "inputs": {
        "sam_model": "sam_vit_h (2.56GB)",
        "grounding_dino_model": "GroundingDINO_SwinB (938MB)"
      },
      "class_type": "LayerMask: LoadSegmentAnythingModels",
      "_meta": {
        "title": "LayerMask: Load SegmentAnything Models(Advance)"
      }
    },
    "36": {
      "inputs": {
        "threshold": 0.3,
        "detail_method": "VITMatte",
        "detail_erode": 6,
        "detail_dilate": 6,
        "black_point": 0.15,
        "white_point": 0.99,
        "process_detail": true,
        "prompt": "swimsuit",
        "device": "cuda",
        "max_megapixels": 2,
        "image": [
          "12",
          0
        ],
        "sam_models": [
          "35",
          0
        ]
      },
      "class_type": "LayerMask: SegmentAnythingUltra V3",
      "_meta": {
        "title": "款式描述-模特"
      }
    },
    "37": {
      "inputs": {
        "url": "https://",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "服装图片上传"
      }
    },
    "38": {
      "inputs": {
        "url": "https://",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "模特图片上传"
      }
    }
  }
};

module.exports = workflow; 