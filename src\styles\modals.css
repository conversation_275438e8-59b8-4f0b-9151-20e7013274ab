/**
 * 统一模态框样式定义
 * 
 * 包含以下组件：
 * 1. 基础模态框 (.base-modal) - 全屏遮罩层
 * 2. 模态框内容 (.modal-content) - 主体内容容器
 * 3. 模态框头部 (.modal-header) - 标题区域
 * 4. 模态框主体 (.modal-body) - 内容区域
 * 5. 模态框底部 (.modal-footer) - 操作按钮区域
 * 
 * 特点：
 * - 居中显示
 * - 优雅的进入/退出动画
 * - 支持大尺寸模式
 * - 自适应内容高度
 * - 可滚动的内容区域
 * - 固定的页脚区域
 * 
 * 使用示例：
 * <div class="base-modal">
 *   <div class="modal-content">
 *     <button class="large-close-button">
 *       <svg>...</svg>
 *     </button>
 *     <div class="modal-header">
 *       <h3>标题</h3>
 *     </div>
 *     <div class="modal-body">
 *       内容区域
 *     </div>
 *     <div class="modal-footer">
 *       <button>取消</button>
 *       <button>确认</button>
 *     </div>
 *   </div>
 * </div>
 */

@import './theme.css';

/* 基础模态框样式 */
.base-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-mask);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: modalFadeIn 0.2s ease;
  pointer-events: auto;
}

.modal-content {
  pointer-events: auto;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

/* 大尺寸模态框样式 */
.modal-content.modal-large {
  width: calc(100vw - 290px - 40px);
  max-width: 1200px;
  height: calc(100vh - 150px);
  min-height: 600px;
  margin-left: 290px;
}

.modal-header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: var(--spacing-lg) 0 0;
  margin: 0;
  border-bottom: 1px solid var(--border-light);
  position: relative;
  background: var(--bg-primary);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  margin-top: var(--spacing-md);
  padding-bottom: 80px;
}

.modal-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-lighter);
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

/* 特殊模态框底部样式（模特弹窗、场景弹窗、高级自定义弹窗） */
.model-select-modal .modal-footer,
.scene-select-modal .modal-footer,
.advanced-custom-modal .modal-footer {
  box-shadow: none;
  border-top: 1px solid var(--border-color);
}

/* 动画效果 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 888px) {
  .base-modal {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important;
  }
  .modal-content {
    margin: 0 auto !important;
    left: 0 !important;
    right: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    max-width: 95vw !important;
    max-height: 95vh !important;
    position: relative !important;
    transform: none !important;
    box-shadow: var(--shadow-lg) !important;
  }
} 