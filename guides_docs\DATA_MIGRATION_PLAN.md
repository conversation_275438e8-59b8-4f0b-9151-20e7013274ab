# 数据结构迁移计划文档

## 1. 背景

我们对系统中的任务数据结构进行了重新设计和迁移，主要目标是：

- 简化任务数据结构
- 减少数据冗余
- 提高查询效率
- 标准化组件数据格式

## 2. 新数据结构概述

新的任务数据结构主要包含以下关键字段：

```javascript
{
  id: "任务ID",
  userId: "用户ID",
  status: "任务状态",
  taskType: "任务类型",
  pageType: "页面类型",
  
  // 组件结构 - 主要数据存储位置
  components: {
    sourceImage: {
      componentType: "SourceImagePanel",
      id: "组件ID",
      isMainImage: true,
      // 其他组件特有字段...
    },
    // 其他组件...
  },
  
  // 简化的设置
  settings: {
    common: {
      imageQuantity: 1,
      // 其他通用设置...
    },
    pageSpecific: {
      // 页面特定设置...
    }
  },
  
  // 生成的图片列表
  generatedImages: [
    {
      imageIndex: 0,
      status: "completed",
      url: "图片URL",
      // 其他图片信息...
    }
  ]
}
```

## 3. 已移除的旧结构字段

以下字段在新结构中已被移除或替换：

- **顶层字段**：
  - `primaryImageFileName`
  - `mainServerFileName`
  - `images`（替换为 `generatedImages`）

- **settings中的冗余字段**：
  - `primaryImage`/`source`（替换为 `components.sourceImage`）
  - 其他各种旧组件设置（如`modelSettings`, `sceneSettings`等）

## 4. 迁移策略

迁移过程分为三个阶段：

### 4.1 代码更新阶段（已完成）

- 更新`taskFactory.js`，移除对旧结构的支持
- 更新`task.js`中的createTask函数，移除旧结构相关逻辑
- 更新`taskAdapters.js`，简化getTaskComponent函数
- 更新各页面的handleGenerate函数，使用新的数据结构

### 4.2 数据库迁移阶段（就绪）

使用`migrateTasksData.js`脚本对现有数据进行迁移：

```bash
# 在服务器目录执行
node src/scripts/migrateTasksData.js
```

迁移脚本会执行以下操作：

1. 将旧的`images`数组转换为新的`generatedImages`格式
2. 简化`settings`对象，只保留`common`和`pageSpecific`
3. 从旧结构中提取组件数据，创建标准化的`components`对象
4. 移除冗余的顶层字段

### 4.3 清理阶段（计划中）

一旦确认所有功能正常运行，且数据库迁移完成，将：

1. 移除`taskAdapters.js`中剩余的兼容性函数
2. 移除所有组件中对旧结构的检查和引用
3. 更新文档，反映新的数据结构

## 5. API兼容性

为保持向后兼容，API响应可能仍会包含一些旧字段，但前端代码已不再依赖这些字段。

## 6. 性能优化

新的数据结构有以下性能优势：

- 减少了数据冗余，降低存储需求
- 提高了文档读取和解析效率
- 简化了组件数据访问路径，提高了渲染效率

## 7. 下一步计划

- 完成数据库迁移
- 持续监控系统性能
- 更新开发文档，反映新的最佳实践
- 考虑为组件数据添加数据库索引，进一步提升查询效率 