/**
 * 联系客服组件样式
 * 包含:
 * 1. 容器样式
 * 2. 微信二维码部分
 * 3. 邮件客服部分
 * 4. 二维码放大弹窗
 * 5. 响应式布局
 */

.contact-support {
  background: linear-gradient(145deg, var(--bg-primary), var(--bg-tertiary));
  border-radius: var(--radius-lg);
  padding: 24px;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
}

[data-theme="dark"] .contact-support {
  background: transparent;
}

.contact-support:hover {
  box-shadow: var(--shadow-lg);
}

.contact-methods {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32px;
}

/* 联系方式共同样式 */
.contact-method {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

.method-label {
  font-size: 0.95rem;
  color: var(--text-primary);
  font-weight: 600;
  letter-spacing: 0.3px;
  white-space: nowrap;
  min-width: 70px;
}

/* 统一所有内容容器的基础样式 */
.wechat-box,
.phone-box,
.email-box {
  display: flex;
  align-items: center;
  gap: 10px;
  background: transparent;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  white-space: nowrap;
}

/* 统一所有文本样式 */
.wechat-text,
.phone-text,
.email-text {
  font-size: 0.95rem;
  color: var(--text-primary);
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 复制按钮样式 */
.copy-btn {
  background: transparent;
  border: none;
  padding: 2px 10px;
  border-radius: 4px;
  color: var(--text-secondary);
  font-size: 0.85rem;
  font-weight: 400;
  cursor: pointer;
  transition: color 0.2s, background 0.2s;
  box-shadow: none;
}

.copy-btn:hover {
  color: var(--text-primary);
  background: rgba(0,0,0,0.04);
}

[data-theme="dark"] .copy-btn {
  background: transparent;
  color: var(--text-secondary);
}

[data-theme="dark"] .copy-btn:hover {
  color: var(--brand-primary);
  background: rgba(255,255,255,0.04);
}

/* 微信二维码部分 */
.contact-method.qr {
  position: relative;
}

.qr-container {
  position: relative;
  display: flex;
  align-items: center;
}

.contact-method.qr img {
  width: 42px;
  height: 42px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  padding: 3px;
  background: var(--bg-primary);
  transition: transform 0.3s ease;
  cursor: pointer;
}

.zoom-btn {
  position: absolute;
  right: -4px;
  bottom: -4px;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  padding: 0;
}

.zoom-btn svg {
  width: 10px;
  height: 10px;
}

.zoom-btn:hover {
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 邮件客服部分 */
.contact-method.email {
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

.contact-method .email-box {
  /* 移除 margin-left，使用父容器的 gap */
}

[data-theme="dark"] .email-box {
  background: transparent;
  box-shadow: none;
}

/* 电话客服部分 */
.contact-method.phone {
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

.contact-method .phone-box {
  /* 移除 margin-left，使用父容器的 gap */
}

[data-theme="dark"] .phone-box {
  background: transparent;
  box-shadow: none;
}

/* 地址客服部分 */
.contact-method.address {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
}
.address-box {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  background: transparent;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  white-space: normal;
  flex-wrap: nowrap;
}
.address-text {
  font-size: 0.95rem;
  color: var(--text-primary);
  letter-spacing: 0.5px;
  font-weight: 500;
  word-break: break-all;
  flex: 1 1 auto;
  min-width: 0;
  display: block;
}

/* 二维码放大弹窗 */
.qr-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-mask);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

.qr-modal-content {
  position: relative;
  background: var(--bg-primary);
  padding: 20px;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  animation: zoomIn 0.2s ease;
}

[data-theme="dark"] .qr-modal-content {
  background: var(--bg-secondary);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
}

.qr-modal-content img {
  width: 240px;
  height: 240px;
  display: block;
}

.close-modal {
  position: absolute;
  top: -12px;
  right: -12px;
  width: 28px;
  height: 28px;
  border-radius: var(--radius-full);
  border: none;
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

[data-theme="dark"] .close-modal {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  box-shadow: var(--shadow-md);
}

.close-modal:hover {
  color: var(--text-primary);
  transform: scale(1.1);
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .contact-support {
    padding: 20px;
    border-radius: var(--radius-lg);
  }

  .contact-methods {
    flex-direction: column;
    gap: 24px;
  }

  .contact-method {
    width: 100%;
    justify-content: flex-start;
  }

  .phone-box {
    flex: 1;
    max-width: 100%;
  }

  .phone-text {
    word-break: break-all;
  }

  /* 保持水平布局，但移除宽度限制 */
  .wechat-box {
    max-width: none; /* 移除宽度限制 */
    flex-wrap: nowrap; /* 不换行，保持水平布局 */
    gap: 10px; /* 调整间距 */
    align-items: center; /* 垂直居中对齐 */
  }

  .wechat-text {
    font-size: 0.85rem;
    word-break: break-word; /* 允许文本换行 */
    flex-shrink: 1; /* 允许文本收缩 */
    min-width: 0; /* 允许文本收缩到最小 */
  }

  /* 确保二维码容器不被压缩 */
  .qr-container {
    flex-shrink: 0; /* 防止二维码区域被压缩 */
  }
}

@media (max-width: 480px) {
  /* 超小屏幕仍保持水平布局，但进一步优化 */
  .wechat-box {
    max-width: none; /* 移除宽度限制 */
    flex-wrap: nowrap; /* 保持水平布局 */
    gap: 8px; /* 减小间距 */
    align-items: center;
  }
  
  .wechat-text {
    font-size: 0.8rem;
    word-break: break-word;
    flex-shrink: 1;
    min-width: 0;
  }

  /* 确保二维码容器稳定 */
  .qr-container {
    flex-shrink: 0; /* 防止压缩 */
  }

  /* 复制按钮也防止压缩 */
  .copy-id-btn {
    flex-shrink: 0;
  }
}

.wechat-id {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-top: 4px;
}

.wechat-box {
  display: flex;
  align-items: center;
  gap: 10px;
  background: transparent;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  white-space: nowrap;
}

.wechat-text {
  font-size: 0.95rem;
  color: var(--text-primary);
  letter-spacing: 0.5px;
  font-weight: 500;
}

.contact-support,
.contact-support .method-label,
.contact-support .wechat-text,
.contact-support .phone-text,
.contact-support .email-text {
  font-family: var(--font-family);
}

/* 联系客服弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content.contact-modal {
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(80vh - 80px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-content.contact-modal {
    width: 95%;
    padding: 0 20px 20px 20px;
  }
  
  .modal-body {
    padding: 20px;
  }

  .modal-content.contact-modal .modal-header {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }

  .modal-content.contact-modal .contact-method.qr img {
    width: 80px;
    height: 80px;
    margin-left: 20px;
  }

  .modal-content.contact-modal .method-label {
    min-width: 70px;
    font-size: 14px;
  }

  /* 为弹窗中的微信区域保持水平布局 */
  .modal-content.contact-modal .contact-method.qr {
    flex-wrap: nowrap; /* 保持水平布局，不换行 */
    gap: 12px; /* 统一间距 */
    align-items: center; /* 垂直居中 */
  }

  .modal-content.contact-modal .wechat-box {
    max-width: none; /* 移除宽度限制 */
    flex-wrap: nowrap; /* 保持水平布局 */
    gap: 8px;
    align-items: center;
  }

  .modal-content.contact-modal .wechat-text {
    word-break: break-word; /* 允许文本换行 */
    flex-shrink: 1; /* 允许文本收缩 */
    min-width: 0;
  }

  /* 确保弹窗中的二维码和复制按钮不被压缩 */
  .modal-content.contact-modal .qr-container {
    flex-shrink: 0;
  }

  .modal-content.contact-modal .copy-id-btn {
    flex-shrink: 0;
  }
}

/* 联系客服弹窗专属样式 - 从 App.css 移动过来 */
.contact-modal {
  max-width: 650px;
  width: 90%;
  padding: 30px;
  padding-top: 0px; /* 减小顶部间距 */
}

.contact-modal .modal-header {
  margin-bottom: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  padding-top: 20px !important;
  padding-bottom: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.contact-modal .modal-header h2 {
  margin: 0 !important;
  padding: 0 !important;
}

.contact-modal .contact-support {
  box-shadow: none;
  background: transparent !important;
  padding: 0;
}

/* 取消模态框中客服组件的悬浮效果 */
.contact-modal .contact-support:hover {
  box-shadow: none;
}

.contact-modal .zoom-btn:hover,
.contact-modal .copy-btn:hover {
  transform: none;
  box-shadow: var(--shadow-sm);
}

.contact-modal .contact-methods {
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
}

.contact-modal .contact-method {
  width: 100%;
  justify-content: flex-start;
}

.contact-modal .email-box {
  flex: 1;
  max-width: 100%;
}

.contact-modal .email-text {
  word-break: break-all;
}

.contact-intro {
  margin-bottom: 24px;
  color: var(--text-secondary);
  font-size: 14px;
  text-align: center;
  line-height: 1.5;
}

.modal-contact-support {
  width: 100%;
}

.contact-modal .contact-method.qr {
  justify-content: flex-start;
  margin-bottom: 8px;
}

.contact-modal .contact-method.qr img {
  width: 100px;
  height: 100px;
  margin-left: 20px;
}

.contact-modal .method-label {
  min-width: 80px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .contact-modal {
    width: 95%;
    padding: 20px;
  }

  .contact-modal .contact-method.qr img {
    width: 80px;
    height: 80px;
    margin-left: 20px; /* 移除左边距，避免挤压 */
  }

  .contact-modal .method-label {
    min-width: 70px;
    font-size: 14px;
  }

  /* 为弹窗中的微信区域保持水平布局 */
  .contact-modal .contact-method.qr {
    flex-wrap: nowrap; /* 保持水平布局，不换行 */
    gap: 12px; /* 统一间距 */
    align-items: center; /* 垂直居中 */
  }

  .contact-modal .wechat-box {
    max-width: none; /* 移除宽度限制 */
    flex-wrap: nowrap; /* 保持水平布局 */
    gap: 8px;
    align-items: center;
  }

  .contact-modal .wechat-text {
    word-break: break-word; /* 允许文本换行 */
    flex-shrink: 1; /* 允许文本收缩 */
    min-width: 0;
  }

  /* 确保弹窗中的二维码和复制按钮不被压缩 */
  .contact-modal .qr-container {
    flex-shrink: 0;
  }

  .contact-modal .copy-id-btn {
    flex-shrink: 0;
  }
} 

.contact-address-row {
  width: 100%;
  margin-top: 12px;
  display: flex;
  justify-content: center;
} 