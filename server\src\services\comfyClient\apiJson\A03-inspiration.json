{"1": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "UNet加载器"}}, "3": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "4": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "5": {"inputs": {"needInput": true, "seed": 452263050733762, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["1", 0], "positive": ["9", 0], "negative": ["8", 0], "latent_image": ["11", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "随机种子"}}, "6": {"inputs": {"text": ["16", 0], "clip": ["3", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "7": {"inputs": {"text": "", "clip": ["3", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "8": {"inputs": {"conditioning": ["7", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "9": {"inputs": {"guidance": 3.5, "conditioning": ["6", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "11": {"inputs": {"needInput": true, "width": 1024, "height": 1024, "batch_size": 2}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "图片尺寸和数量"}}, "12": {"inputs": {"samples": ["5", 0], "vae": ["4", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "14": {"inputs": {"from_translate": "auto", "to_translate": "english", "add_proxies": false, "needInput": true, "proxies": "", "auth_data": "", "service": "GoogleTranslator", "text": "一套平铺的红色格子比基尼，带着布制的花配饰", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide"}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "灵感探索方向"}}, "15": {"inputs": {"filename_prefix": "Inspiration", "images": ["12", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "16": {"inputs": {"delimiter": ", ", "clean_whitespace": "true", "text_a": ["14", 0], "text_b": ["17", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "17": {"inputs": {"needInput": true, "String": ""}, "class_type": "String", "_meta": {"title": "前段标签合并后输入（暂时不用）"}}}