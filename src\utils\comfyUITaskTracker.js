import { EventEmitter } from 'events';

// 全局WebSocket管理器单例
class GlobalWebSocketManager extends EventEmitter {
  constructor() {
    super();
    this.serverUrl = process.env.REACT_APP_COMFYUI_WS_URL || 'ws://localhost:5000';
    this.ws = null;
    this.isConnecting = false;
    this.connectionPromise = null;
    this.keepAlive = true;
    this.retryAttempts = 0;
    this.maxRetries = 3;
    this.connectionId = null;
    this.subscribedTasks = new Map(); // { taskId: { promptId, instanceId, callbacks } }
    this.pendingSubscriptions = new Set(); // 待处理的订阅请求
    this.isInitialized = false;
    
    // 生成唯一的连接ID
    this.connectionId = `frontend_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async connect() {
    // 如果已经有连接Promise，直接返回
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    // 如果已经连接，直接返回
    if (this.ws?.readyState === WebSocket.OPEN) {
      return Promise.resolve();
    }

    // 如果正在连接中，等待一段时间后重试
    if (this.isConnecting) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      return this.connect();
    }

    this.isConnecting = true;
    console.log('开始建立全局WebSocket连接');

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        const wsUrl = new URL('/ws/progress', this.serverUrl).href;
        console.log('尝试连接到WebSocket URL:', wsUrl);

        this.ws = new WebSocket(wsUrl);

        const timeout = setTimeout(() => {
          if (this.ws.readyState !== WebSocket.OPEN) {
            this.ws.close();
            this.isConnecting = false;
            this.connectionPromise = null;
            reject(new Error('WebSocket连接超时'));
          }
        }, 10000);

        this.ws.onopen = () => {
          clearTimeout(timeout);
          console.log('全局WebSocket连接成功');
          this.isConnecting = false;
          this.retryAttempts = 0;
          this.isInitialized = true;
          this.emit('ws-connected');
          
          // 发送连接信息
          this.sendConnectionInfo();
          
          // 发送所有待处理的订阅请求
          this.pendingSubscriptions.forEach(sub => {
            this.sendSubscriptionMessage(sub);
          });
          this.pendingSubscriptions.clear();
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleProgressUpdate(data);
          } catch (error) {
            console.error('处理WebSocket消息失败:', error);
          }
        };

        this.ws.onerror = (error) => {
          clearTimeout(timeout);
          console.error('WebSocket连接错误:', error);
          this.isConnecting = false;
          this.emit('ws-error', error);
          this.connectionPromise = null;
        };

        this.ws.onclose = (event) => {
          clearTimeout(timeout);
          console.log('WebSocket连接关闭', {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean
          });
          this.isConnecting = false;
          this.connectionPromise = null;
          this.emit('ws-disconnected');
          
          // 如果是正常关闭，不要重连
          if (event.code === 1000 || event.code === 1001) {
            console.log('WebSocket正常关闭，不进行重连');
            this.emit('ws-completed');
            return;
          }
          
          // 重连逻辑
          if (this.keepAlive && this.retryAttempts < this.maxRetries) {
            const delay = Math.min(1000 * Math.pow(2, this.retryAttempts), 10000);
            console.log(`${delay/1000}秒后尝试重连 (第${this.retryAttempts + 1}次)`);
            this.retryAttempts++;
            setTimeout(() => {
              this.connect().catch(() => {
                if (this.retryAttempts >= this.maxRetries) {
                  console.log('达到最大重试次数，停止重连');
                }
              });
            }, delay);
          }
        };
      } catch (error) {
        this.isConnecting = false;
        this.connectionPromise = null;
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  // 发送连接信息
  sendConnectionInfo() {
    if (this.ws?.readyState === WebSocket.OPEN) {
      const connectionInfo = {
        type: 'connection_info',
        connection_id: this.connectionId,
        subscribed_tasks: Array.from(this.subscribedTasks.keys())
      };
      this.ws.send(JSON.stringify(connectionInfo));
    }
  }

  // 发送订阅消息 - 支持多平台
  sendSubscriptionMessage(subscriptionData) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      const message = {
        type: 'subscribe_task',
        task_id: subscriptionData.task_id,
        prompt_id: subscriptionData.prompt_id,
        instance_id: subscriptionData.instance_id,
        instance_ws_url: subscriptionData.instance_ws_url,
        platform: subscriptionData.platform || 'comfyui',
        task_data: subscriptionData.task_data || {}
      };
      console.log('发送订阅消息:', message);
      this.ws.send(JSON.stringify(message));
    }
  }

  // 发送取消订阅消息
  sendUnsubscriptionMessage(taskId) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      const message = {
        type: 'unsubscribe_task',
        task_id: taskId
      };
      this.ws.send(JSON.stringify(message));
    }
  }

  async subscribeTask(taskId, promptId, instanceId, instanceWsUrl, callbacks, platform = 'comfyui', taskData = {}) {
    if (this.subscribedTasks.has(taskId)) {
      console.log(`任务 ${taskId} 已经订阅，跳过`);
      return;
    }

    const subscriptionData = {
      task_id: taskId,
      prompt_id: promptId,
      instance_id: instanceId,
      instance_ws_url: instanceWsUrl,
      platform: platform,
      task_data: taskData
    };

    this.subscribedTasks.set(taskId, { promptId, instanceId, callbacks, platform, taskData });

    console.log(`订阅${platform}任务:`, {
      taskId,
      platform,
      hasNetWssUrl: !!(taskData.netWssUrl)
    });

    try {
      // 等待连接建立
      await this.connect();
      
      if (this.ws?.readyState === WebSocket.OPEN) {
        console.log(`直接发送任务 ${taskId} 的订阅消息`);
        this.sendSubscriptionMessage(subscriptionData);
      } else {
        console.log(`暂存任务 ${taskId} 的订阅请求`);
        this.pendingSubscriptions.add(subscriptionData);
      }
    } catch (error) {
      console.error(`订阅任务 ${taskId} 失败:`, error);
    }
  }

  unsubscribeTask(taskId) {
    if (this.subscribedTasks.has(taskId)) {
      console.log(`取消订阅任务 ${taskId}`);
      this.subscribedTasks.delete(taskId);
      this.sendUnsubscriptionMessage(taskId);
      this.emit('task-unsubscribed', taskId);
    }
  }

  handleProgressUpdate(data) {
    const { type, data: wsData, task_id } = data;

    console.log('处理进度更新:', {
      消息类型: type,
      任务ID: task_id,
      数据: wsData,
      平台: wsData?.platform || 'unknown'
    });

    // 查找匹配的任务
    for (const [taskId, task] of this.subscribedTasks.entries()) {
      // 优先通过task_id匹配，然后通过prompt_id验证
      const isTaskIdMatch = task_id === taskId;
      const isPromptIdMatch = task.promptId === wsData?.prompt_id;
      
      if (isTaskIdMatch || isPromptIdMatch) {
        const { callbacks } = task;
        console.log(`找到匹配的任务 ${taskId}:`, {
          taskId匹配: isTaskIdMatch,
          promptId匹配: isPromptIdMatch,
          taskPromptId: task.promptId,
          messagePromptId: wsData?.prompt_id,
          task: task
        });

        switch (type) {
          case 'executing':
            console.log(`任务 ${taskId} 开始执行`);
            this.emit('task-start', taskId);
            break;

          case 'progress':
            const platform = wsData.platform || task.platform || 'comfyui';
            console.log(`任务 ${taskId} (${platform}) 进度更新:`, {
              当前值: wsData.value,
              最大值: wsData.max,
              平台: platform
            });
            // 调用进度回调，传递进度值和平台信息
            if (callbacks.onProgress) {
              callbacks.onProgress(wsData.value, wsData.max, platform);
            }
            this.emit('task-progress', taskId, wsData.value, wsData.max, platform);
            break;

          case 'completed':
            console.log(`任务 ${taskId} 已完成`);
            if (callbacks.onCompleted) {
              callbacks.onCompleted(wsData);
            }
            this.emit('task-completed', taskId, wsData);
            this.unsubscribeTask(taskId);
            break;

          case 'status':
            console.log(`任务 ${taskId} 状态变更:`, wsData.status);
            this.emit('task-status', taskId, wsData.status);
            break;

          default:
            console.log(`未处理的消息类型: ${type}`, data);
        }
        break;
      }
    }
  }

  // 只在浏览器关闭或退出登录时调用
  disconnect() {
    console.log('关闭全局WebSocket连接');
    this.keepAlive = false;
    this.retryAttempts = 0;
    this.isInitialized = false;
    if (this.ws) {
      try {
        this.ws.close(1000, 'User logout or browser close');
      } catch (e) {
        console.error('关闭WebSocket连接失败:', e);
      }
    }
  }

  // 重置连接状态（如果需要重新使用）
  reset() {
    this.keepAlive = true;
    this.retryAttempts = 0;
    this.subscribedTasks.clear();
    this.pendingSubscriptions.clear();
    return this.connect();
  }

  removeAllListeners() {
    super.removeAllListeners();
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.ws?.readyState === WebSocket.OPEN,
      isConnecting: this.isConnecting,
      isInitialized: this.isInitialized,
      connectionId: this.connectionId,
      subscribedTasksCount: this.subscribedTasks.size
    };
  }
}

// 创建全局单例实例
const globalWebSocketManager = new GlobalWebSocketManager();

// 为了兼容现有代码，保留ComfyUITaskTracker类名
class ComfyUITaskTracker extends EventEmitter {
  constructor() {
    super();
    // 代理到全局管理器
    this.manager = globalWebSocketManager;
    
    // 转发事件
    this.manager.on('ws-connected', () => this.emit('ws-connected'));
    this.manager.on('ws-error', (error) => this.emit('ws-error', error));
    this.manager.on('ws-disconnected', () => this.emit('ws-disconnected'));
    this.manager.on('ws-completed', () => this.emit('ws-completed'));
    this.manager.on('task-start', (taskId) => this.emit('task-start', taskId));
    this.manager.on('task-progress', (taskId, progress, max) => this.emit('task-progress', taskId, progress, max));
    this.manager.on('task-completed', (taskId, data) => this.emit('task-completed', taskId, data));
    this.manager.on('task-status', (taskId, status) => this.emit('task-status', taskId, status));
    this.manager.on('task-unsubscribed', (taskId) => this.emit('task-unsubscribed', taskId));
  }

  async connect() {
    return this.manager.connect();
  }

  async subscribeTask(taskId, promptId, instanceId, instanceWsUrl, callbacks, platform = 'comfyui', taskData = {}) {
    return this.manager.subscribeTask(taskId, promptId, instanceId, instanceWsUrl, callbacks, platform, taskData);
  }

  unsubscribeTask(taskId) {
    return this.manager.unsubscribeTask(taskId);
  }

  disconnect() {
    return this.manager.disconnect();
  }

  reset() {
    return this.manager.reset();
  }

  removeAllListeners() {
    super.removeAllListeners();
    this.manager.removeAllListeners();
  }

  // 获取订阅的任务
  get subscribedTasks() {
    return this.manager.subscribedTasks;
  }

  // 获取连接状态
  getConnectionStatus() {
    return this.manager.getConnectionStatus();
  }
}

// 导出全局管理器实例，供其他地方使用
export { globalWebSocketManager };

export default ComfyUITaskTracker;