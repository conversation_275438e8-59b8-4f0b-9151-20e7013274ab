/**
 * 统一关闭按钮样式定义
 * 
 * 包含四种标准关闭按钮：
 * 1. 大型关闭按钮 (.large-close-button) - 用于中大型弹窗，如上传指导、模特选择、场景选择等
 * 2. 中型关闭按钮 (.medium-close-button) - 用于订阅弹窗和登录注册弹窗
 * 3. 小型关闭按钮 (.small-close-button) - 用于小型弹窗，如图片信息、文本弹窗等
 * 4. 超小号关闭按钮 (.tiny-close-button) - 用于提示组件的弹窗
 */

@import './theme.css';

/* 大型关闭按钮 - 用于中大型弹窗 */
.large-close-button {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  border: none;
  background: var(--bg-hover);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease-out;
  z-index: 1;
  cursor: pointer;
  padding: 0;
}

.large-close-button:hover {
  background: var(--bg-active);
}

.large-close-button svg {
  width: var(--font-size-lg);
  height: var(--font-size-lg);
  color: var(--text-secondary);
}

[data-theme="dark"] .large-close-button {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.65);
}

[data-theme="dark"] .large-close-button:hover {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.85);
}

/* 中型关闭按钮 - 用于订阅弹窗和登录注册弹窗 */
.medium-close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 28px;
  height: 28px;
  border-radius: 50% !important;
  border: none;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
  cursor: pointer;
  padding: 0;
}

.medium-close-button:hover {
  background: var(--bg-hover);
}

.medium-close-button svg {
  width: 16px;
  height: 16px;
  color: var(--text-secondary);
}

.medium-close-button:hover svg {
  color: var(--text-primary);
}

[data-theme="dark"] .medium-close-button {
  background: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .medium-close-button:hover {
  background: rgba(255, 255, 255, 0.12);
  color: rgba(255, 255, 255, 0.8);
}

/* 小型关闭按钮 - 用于小型弹窗 */
.small-close-button {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: var(--radius-full);
  border: none;
  background: var(--bg-tertiary);
  cursor: pointer;
  padding: 0;
  transition: var(--transition-normal);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.small-close-button:hover {
  background-color: var(--bg-hover);
}

.small-close-button svg {
  width: 14px;
  height: 14px;
  color: var(--text-secondary);
}

.small-close-button:hover svg {
  color: var(--text-primary);
}

/* 超小号关闭按钮 - 用于提示组件的弹窗 */
.tiny-close-button {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 18px;
  height: 18px;
  border-radius: var(--radius-full);
  border: none;
  background: var(--bg-tertiary);
  cursor: pointer;
  padding: 0;
  transition: var(--transition-normal);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tiny-close-button:hover {
  background-color: var(--bg-hover);
}

.tiny-close-button svg {
  width: 10px;
  height: 10px;
  color: var(--text-secondary);
}

.tiny-close-button:hover svg {
  color: var(--text-primary);
}

@media (max-width: 768px) {
  .large-close-button {
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
  }
  
  .large-close-button svg {
    width: 18px;
    height: 18px;
  }
  
  .medium-close-button {
    top: 10px;
    right: 10px;
    width: 26px;
    height: 26px;
  }
  
  .medium-close-button svg {
    width: 14px;
    height: 14px;
  }
} 