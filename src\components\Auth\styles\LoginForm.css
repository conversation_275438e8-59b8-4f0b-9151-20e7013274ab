/* Login Form Styles */

.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding-bottom: 20px;
}

/* 简单错误消息样式 */
.login-form .simple-error-message {
  color: var(--error-color);
  font-size: 12px;
  line-height: 1.5;
  margin: 2px 0 0 0;
  display: block;
  text-align: left;
}

.login-form .submit-error {
  margin-top: -10px;
  margin-bottom: 10px;
}

/* 覆盖AuthModals.css中的form-group margin-bottom设置 */
.login-form .form-group {
  margin-bottom: 0 !important;
}

.form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
}

.remember-me input[type="checkbox"] {
  width: 16px;
  height: 16px;
  border: 1px solid var(--border-color);
  border-radius: 2px;
  margin: 0;
  padding: 0;
  appearance: none;
  background-color: var(--bg-primary);
  position: relative;
  cursor: pointer;
}

.remember-me input[type="checkbox"]:checked {
  background-color: var(--brand-primary);
  border-color: var(--brand-primary);
}

.remember-me input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.remember-me input[type="checkbox"]:hover {
  border-color: var(--brand-primary);
}

.forgot-password {
  color: var(--text-secondary);
  text-decoration: none;
  cursor: pointer;
}

.forgot-password:hover {
  color: var(--brand-primary);
}
