/* 绘图工具组件样式 */
@import '../../styles/theme.css';

.draw-tools {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px 8px;
  background: var(--bg-primary);
  border-radius: 0;
  max-width: 100%;
}

.tools-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.group-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-light);
}

.tool-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  justify-content: center;
}

.tool-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  width: auto;
  height: 36px;
  border-radius: var(--radius-sm);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-light);
  cursor: pointer;
  color: var(--text-secondary);
}

/* 重要：按钮动画过渡效果 */
.draw-tool-btn,
.draw-tool-btn:hover,
.draw-tool-btn:active,
.draw-tool-btn.active {
  transition: all 0.2s ease !important;
  position: static !important;
  top: auto !important;
  left: auto !important;
  margin: 0 !important;
  box-shadow: none !important;
  transform: none !important;
}

.tool-btn:hover,
.draw-tool-btn:hover {
  background-color: #eee !important;
  color: #333 !important;
  border-color: var(--border-light) !important;
}

/* 激活状态按钮样式 */
.tool-btn.active {
  background-color: var(--bg-secondary);
  color: var(--brand-primary);
  border-color: var(--border-light);
}

/* 激活状态按钮的悬浮样式 - 确保与激活状态一致 */
.tool-btn.active:hover {
  background-color: var(--bg-secondary) !important;
  color: var(--brand-primary) !important;
  border-color: var(--border-light) !important;
}

.tool-name {
  font-size: var(--font-size-sm);
  transition: none !important;
}

.brush-size-control {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
}

.brush-size-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px;
}

.brush-size-value {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: left;
  padding-right: 8px;
}

.brush-size-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: right;
  padding-left: 8px;
}

.brush-size-slider {
  width: 100%;
  height: 2px;
  background: var(--border-light);
  border-radius: var(--radius-sm);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  position: relative;
}

/* 添加渐变填充效果 */
.brush-size-slider::before {
  content: '';
  position: absolute;
  height: 100%;
  background: var(--brand-gradient);
  border-radius: var(--radius-sm);
  top: 0;
  left: 0;
  width: calc((var(--value) - var(--min)) / (var(--max) - var(--min)) * 100%);
  pointer-events: none;
  z-index: 1;
}

.brush-size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: var(--bg-primary);
  cursor: pointer;
  border: 2px solid var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  transition: var(--transition-normal);
  position: relative;
  z-index: 2;
}

.brush-size-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--bg-primary);
  cursor: pointer;
  border: 2px solid var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  transition: var(--transition-normal);
  position: relative;
  z-index: 2;
}

.brush-size-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
}

.brush-size-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
} 