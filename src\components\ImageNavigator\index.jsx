import React from 'react';
import './index.css';

const ImageNavigator = ({ 
  currentIndex, 
  totalImages, 
  onNavigate,
  className
}) => {
  const handlePrevious = () => {
    if (currentIndex > 0) {
      onNavigate(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < totalImages - 1) {
      onNavigate(currentIndex + 1);
    }
  };

  return (
    <div className={`image-navigator ${className || ''}`}>
      <button
        className="nav-button"
        onClick={handlePrevious}
        disabled={currentIndex === 0}
        title="上一张"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M6 15L12 9L18 15" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      </button>
      <button
        className="nav-button"
        onClick={handleNext}
        disabled={currentIndex === totalImages - 1}
        title="下一张"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M6 9L12 15L18 9" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      </button>
    </div>
  );
};

export default ImageNavigator; 