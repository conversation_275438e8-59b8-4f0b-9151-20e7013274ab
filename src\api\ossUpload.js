
// /api/oss/upload 单文件上传
// /api/oss/uploads 多文件上传
import request from './request';

/**
 * 单文件上传到OSS
 * @param {File} file - 文件对象
 * @param {string} savePath - 保存路径,默认 'images/'
 * @returns {Promise<{url: string, key: string}>} 上传结果
 */
export const uploadFile = async (file, savePath = 'images/') => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('savePath', savePath);

    const { url,fileInfo } = await request.upload(
     '/oss/upload',
        formData
    );

    return {url,fileInfo};

  } catch (error) {
    console.error('文件上传失败:', error);
    throw new Error(`文件上传失败: ${error.message}`);
  }
}

/**
 * 多文件上传到OSS
 * @param {File[]} files - 文件数组
 * @param {string} savePath - 保存路径,默认 'images/'
 * @returns {Promise<Array<{url: string, key: string}>>} 上传结果数组
 */
export const uploadFiles = async (files, savePath = 'images/') => {
  try {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });
    formData.append('savePath', savePath);

    const { urls,fileInfos } = await request.upload(
       '/oss/uploads',
       formData,
    );

    return {urls,fileInfos};

  } catch (error) {
    console.error('文件批量上传失败:', error);
    throw new Error(`文件批量上传失败: ${error.message}`);
  }
}