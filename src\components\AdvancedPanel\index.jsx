import React from 'react';
import PropTypes from 'prop-types';
import './index.css';

const AdvancedPanel = ({
  advancedCustom,
  onExpandClick,
  pageType
}) => {
  const title = pageType === 'optimize' ? '优化要求' : '自定义';
  const description = pageType === 'optimize' 
    ? (advancedCustom ? '已设置优化要求描述' : '进行优化要求设置')
    : (advancedCustom ? '已设置自定义描述和参数' : '进行自定义设置');

  // 根据页面类型和是否已设置内容选择对应的图标
  const iconPath = pageType === 'optimize'
    ? (advancedCustom ? '/images/icons/optimize-active.png' : '/images/icons/optimize.png')
    : (advancedCustom ? '/images/icons/custom-active.png' : '/images/icons/custom.png');

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          <img 
            src={iconPath}
            alt={pageType === 'optimize' ? "优化要求" : "高级自定义"} 
            className="component-icon" 
          />
          <div className="component-text">
            <h3>{title}</h3>
            <div className="component-content">
              <p>{description}</p>
            </div>
          </div>
        </div>
        <button 
          className="expand-btn"
          onClick={onExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

AdvancedPanel.propTypes = {
  advancedCustom: PropTypes.shape({
    type: PropTypes.string,
    description: PropTypes.string,
    negativeDescription: PropTypes.string,
    image: PropTypes.string,
  }),
  onExpandClick: PropTypes.func.isRequired,
  pageType: PropTypes.string,
};

export default AdvancedPanel; 