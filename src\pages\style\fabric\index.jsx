import React, { useRef, useEffect, useState, useCallback, Suspense, useMemo } from 'react';
import './index.css';
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDescription } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { Modal, Button, Spin, message } from 'antd';

import 'antd/dist/reset.css';
import { getCurrentUserId } from '../../../api';
import { executeFlow } from '../../../api/flow';
import { createFlowTask, updateFlowTask, getFlowTasks,getFlowTaskDetail,deleteFlowTask,checkUserBalance } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import {
  getTasks,
  getTaskById,
  deleteTask,
  createTask,
} from '../../../api/task';
import ClothingPanel from '../../../components/ClothingPanel';
import FabricPanel from '../../../components/FabricPanel';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import UploadGuideModal from '../../../components/UploadGuideModal';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadBox from '../../../components/UploadBox';
import UploadBox_Model from '../../../components/UploadBox_Model';
import MaskDescriptionPanel, { tagMapping } from '../../../components/MaskDescriptionPanel';
import QuantityPanel from '../../../components/QuantityPanel';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import GenerationArea from '../../../components/GenerationArea';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import RandomSeedSelector from '../../../components/RandomSeedSelector';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import RequireLogin from '../../../components/RequireLogin';
import TipsPanel from '../../../components/TipsPanel';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import { useTaskContext } from '../../../contexts/TaskContext';

const FABRIC_TIP = '此功能对于纯色面料效果最佳，对于多色面料（如印花、撞色拼接等），尽量提供清晰的大面积的面料图片。';

const FabricPage = ({ isLoggedIn, userId }) => {
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const generationAreaRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [clothingPanels, setClothingPanels] = useState([]);
  const [fabricPanels, setFabricPanels] = useState([]);
  const [currentReuploadFabricPanelId, setCurrentReuploadFabricPanelId] = useState(null);
  const [extraFabricUploadGuide, setExtraFabricUploadGuide] = useState(false);
  const [showFabricUploadGuide, setShowFabricUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [imageQuantity, setImageQuantity] = useState(2);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 修改clothingMaskPanel状态初始化，使用componentId而非id
  const [clothingMaskPanel, setClothingMaskPanel] = useState({
    componentId: generateId(ID_TYPES.COMPONENT),
    selectedTag: '',
    customText: '',
    description: ''
  });

  // 添加拖动状态管理
  const lastPosition = useRef({ x: 0, y: 0 });

  // 添加随机种子相关状态
  const [useRandomSeed, setUseRandomSeed] = useState(true);
  const [seed, setSeed] = useState(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));

  const { updateTask } = useTaskContext();

  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 处理文件上传
  const handleFileUpload = (file) => {
    setShowUploadGuide(false);
    setHasUnsavedChanges(true);
  };

  const handleUploadResult = (results) => {
    console.log('服装上传结果处理:', results);
    setHasUnsavedChanges(true);

    if (results.type === 'panels') {
      const panelsWithType = results.panels.map(panel => ({
        ...panel,
        type: 'clothing',
        source: panel.source || 'upload' // 确保设置source属性
      }));
      setClothingPanels(prevPanels => [...prevPanels, ...panelsWithType]);
    } else if (results.type === 'update') {
      // 处理单个面板更新的情况
      console.log(`更新面板 ${results.panelId} 的状态:`, results.data);

      setClothingPanels(prevPanels =>
        prevPanels.map(panel => {
          if (panel.componentId === results.panelId) {
            // 根据processed数据更新面板
            if (results.data?.processed) {
              const processedData = results.data.processed;

              // 构建处理后图片的URL
              let processedImageUrl = null;
              if (processedData.url) {
                // 如果已经提供了构建好的URL，直接使用
                processedImageUrl = processedData.url;
                console.log('使用提供的URL:', processedImageUrl);
              } else if (processedData.relativePath) {
                processedImageUrl = `http://localhost:3002${processedData.relativePath}`;
                console.log('基于相对路径构建URL:', processedImageUrl);
              }

              console.log('处理后的图片URL:', processedImageUrl);

              // 返回更新后的面板数据
              return {
                ...panel,
                status: 'completed',  // 更新状态为已完成
                originalImage: processedData.originalUrl || panel.previewUrl || panel.url || panel.image, // 保存原始图片URL
                processedFile: processedImageUrl,
                fileInfo: processedData.fileInfo || panel.fileInfo,
                error: null
              };
            }

            // 如果没有processed数据，检查是否有错误
            if (results.error) {
              return {
                ...panel,
                status: 'error',
                error: results.error
              };
            }

            // 如果既没有processed数据也没有错误，保持原样
            return panel;
          }

          // 不是要更新的面板，保持不变
          return panel;
        })
      );
    } else if (results.type === 'error') {
      console.error('上传错误:', results.error);
      message.error('上传失败: ' + results.error);
      // 移除处理中的面板
      setClothingPanels(prevPanels =>
        prevPanels.filter(panel => panel.status !== 'processing'));
    }
  };

  // 处理面料上传结果（第二个上传区域）
  const handleFabricUploadResult2 = (results) => {
    setHasUnsavedChanges(true);
    console.log('处理面料上传结果:', JSON.stringify(results, null, 2));

    try {
      if (results.type === 'panels') {
        if (currentReuploadFabricPanelId) {
          // 如果是重新上传，替换原有面板
          setFabricPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === currentReuploadFabricPanelId
                ? { ...results.panels[0], componentId: currentReuploadFabricPanelId, type: 'fabric' }
                : panel
            )
          );
          // 重置当前重新上传的面板ID
          setCurrentReuploadFabricPanelId(null);
        } else {
          // 如果是新上传，添加新面板
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            type: 'fabric',
            source: panel.source || 'upload' // 确保设置source属性
          }));
          setFabricPanels(prevPanels => [...prevPanels, ...panelsWithType]);
        }
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        // 移除处理中的面板
        setFabricPanels(prevPanels =>
          prevPanels.filter(panel => panel.status !== 'processing'));
        // 重置当前重新上传的面板ID
        setCurrentReuploadFabricPanelId(null);
      }
    } catch (error) {
      console.error('处理面料上传结果时出错:', error);
      message.error('处理面料上传结果时出错: ' + (error.message || '未知错误'));
    }
  };

  // 处理面料原图上传
  const handleFabricFileUpload = (file) => {
    console.log('面料原图上传:', file);
    setHasUnsavedChanges(true);
  };

  // 处理删除面料面板
  const handleDeleteFabricPanel = (panelId) => {
    setFabricPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 处理重新上传面料图片
  const handleReuploadFabric = (panel) => {
    setHasUnsavedChanges(true);
    if (panel && panel.componentId) {
      // 先删除当前面板
      handleDeleteFabricPanel(panel.componentId);
      // 然后打开上传弹窗
      setExtraFabricUploadGuide(true);
      setOperationsPanel(null);
    }
  };

  // 处理面料图片状态变更
  const handleFabricStatusChange = (panelId, newStatus) => {
    setFabricPanels(prev =>
      prev.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
    setHasUnsavedChanges(true);

  };

  const handleDeleteClothingPanel = (panelId) => {
    setClothingPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 重新打开上传弹窗
  const handleReuploadClothing = (panel) => {
    setHasUnsavedChanges(true);
    if (panel && panel.componentId) {
      // 先删除当前面板
      handleDeleteClothingPanel(panel.componentId);
      // 然后打开上传弹窗
      setShowUploadGuide(true);
      setOperationsPanel(null);
    }
  };

  // 处理开始生成按钮点击
  const handleGenerate = async () => {
    setIsGenerating(true);
    setSeed(useRandomSeed? (Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)) : seed);
    // 验证必要的条件 - 模特换装页面需要同时上传服装和模特图片
    if (!clothingPanels || clothingPanels.length === 0) {
      message.error('请先上传服装图片');
      setIsGenerating(false);
      return;
    }

    if (!fabricPanels || fabricPanels.length === 0) {
      message.error('请先上传面料图片');
      setIsGenerating(false);
      return;
    }

    // 验证蒙版描述是否已填写
    if ((!clothingMaskPanel.selectedTag || clothingMaskPanel.selectedTag.trim() === '') &&
        (!clothingMaskPanel.customText || clothingMaskPanel.customText.trim() === '') &&
        (!clothingMaskPanel.description || clothingMaskPanel.description.trim() === '')) {
      message.error('请选择款式标签或填写自定义描述');
      setIsGenerating(false);
      setIsProcessing(false);
      return;
    }
    const balance = await checkUserBalance('换面料', 'fabric', imageQuantity);
    if(balance.code !== 200){
      message.error(balance.message);
      setIsGenerating(false);
      setIsProcessing(false);
      return;
    }

    // 预先定义taskData变量，使其在整个函数作用域内可用
    let taskData = null;

    // 处理可能的自定义上传服装图片
    let clothingToUse = clothingPanels[0];

    // 检查是否有用户上传的服装图片
    console.log('服装图片上传检查:', {
      hasFile: !!clothingPanels[0].file,
      fileIsObject: typeof clothingPanels[0].file === 'object',
      fileType: clothingPanels[0].type,
      source: clothingPanels[0].source,
      fileContent: clothingPanels[0].file
    });
    
    // 从TaskPanel拖拽过来的服装图片也需要上传到服务器
    if ((clothingPanels[0].file && clothingPanels[0].source === 'upload') ||
        (clothingPanels[0].source === 'upload' && !clothingPanels[0].file)) {
      
      // 显示上传中提示
      message.loading('正在上传服装图片...', 0);
      
      try {
        let fileToUpload = clothingPanels[0].file;
        
        // 如果没有file对象但有URL，需要从URL获取文件
        if (!fileToUpload && clothingPanels[0].url) {
          try {
            const response = await fetch(clothingPanels[0].url);
            const blob = await response.blob();
            fileToUpload = new File([blob], clothingPanels[0].serverFileName || 'clothing.jpg', {
              type: blob.type || 'image/jpeg'
            });
          } catch (error) {
            console.error('从URL获取服装文件失败:', error);
            message.error('服装图片处理失败，请重试');
            setIsProcessing(false);
            setIsGenerating(false);
            return;
          }
        }
        
        // 将文件上传到服务器
        const {fileInfos} = await uploadFiles([fileToUpload], 'fabric');
        
        // 创建新的面板，包含服务器URL
        clothingToUse = {
          ...clothingPanels[0],
          serverFileName: clothingPanels[0].serverFileName,
          originalImage: clothingPanels[0].url, // 保持原始本地预览URL
          originalUrl: fileInfos[0].url,
          url: fileInfos[0].url,
          fileInfo: fileInfos[0],
          source: 'history' // 标记已上传到服务器的图片来源为history
        };
        
        message.success('服装图片上传成功');
      } catch (error) {
        console.error('上传服装图片时出错:', error);
        message.error('服装图片上传失败: ' + (error.message || '未知错误'));
        setIsProcessing(false);
        setIsGenerating(false);
        return;
      } finally {
        message.destroy();
      }
    }
    
    // 处理可能的自定义上传面料图片
    let fabricToUse = fabricPanels[0];

    // 检查是否有用户上传的面料图片
    console.log('面料图片上传检查:', {
      hasFile: !!fabricPanels[0].file,
      fileIsObject: typeof fabricPanels[0].file === 'object',
      fileType: fabricPanels[0].type,
      source: fabricPanels[0].source,
      fileContent: fabricPanels[0].file
    });
    
    // 从TaskPanel拖拽过来的面料图片也需要上传到服务器
    if ((fabricPanels[0].file && fabricPanels[0].source === 'upload') ||
        (fabricPanels[0].source === 'upload' && !fabricPanels[0].file)) {
      
      // 显示上传中提示
      message.loading('正在上传面料图片...', 0);
      
      try {
        let fileToUpload = fabricPanels[0].file;
        
        // 如果没有file对象但有URL，需要从URL获取文件
        if (!fileToUpload && fabricPanels[0].url) {
          try {
            const response = await fetch(fabricPanels[0].url);
            const blob = await response.blob();
            fileToUpload = new File([blob], fabricPanels[0].serverFileName || 'fabric.jpg', {
              type: blob.type || 'image/jpeg'
            });
          } catch (error) {
            console.error('从URL获取面料文件失败:', error);
            message.error('面料图片处理失败，请重试');
            setIsProcessing(false);
            setIsGenerating(false);
            return;
          }
        }
        
        // 将文件上传到服务器
        const {fileInfos} = await uploadFiles([fileToUpload], 'fabric');
        
        // 创建新的面板，包含服务器URL
        fabricToUse = {
          ...fabricPanels[0],
          serverFileName: fabricPanels[0].serverFileName,
          originalImage: fabricPanels[0].url, // 保持原始本地预览URL
          originalUrl: fileInfos[0].url,
          fileInfo: fileInfos[0],
          url: fileInfos[0].url,
          source: 'history' // 标记已上传到服务器的图片来源为history
        };
        
        message.success('面料图片上传成功');
      } catch (error) {
        console.error('上传面料图片时出错:', error);
        message.error('面料图片上传失败: ' + (error.message || '未知错误'));
        setIsProcessing(false);
        setIsGenerating(false);
        return;
      } finally {
        message.destroy();
      }
    }

    const currentUserId = userId || getCurrentUserId();

    // 准备任务数据
    taskData = {
      taskId: generateId(ID_TYPES.TASK),
      userId: currentUserId,
      createdAt: new Date(),
      status: 'processing',
      imageCount: imageQuantity,
      taskType: 'fabric-generation',
      pageType: 'fabric', // 添加页面类型标识
      // 使用新的components数组结构
      components: [
        // 服装组件
        {
          componentType: 'clothingPanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          name: clothingToUse.title || '服装',
          status: 'completed',
          serverFileName: clothingToUse.serverFileName,
          originalImage: clothingToUse.originalImage || clothingToUse.url, // 明确设置originalImage
          processedFile: clothingToUse.processedFile,
          fileInfo: {
            ...(clothingToUse.fileInfo || {}),
            serverFileName: clothingToUse.serverFileName // 确保在fileInfo中也设置serverFileName
          },
          url: clothingToUse.url // 服务器URL
        },
        // 面料组件
        {
          componentType: 'fabricPanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          name: fabricToUse.title || '面料',
          status: 'completed',
          serverFileName: fabricToUse.serverFileName,
          originalImage: fabricToUse.originalImage || fabricToUse.url, // 明确设置originalImage
          processedFile: fabricToUse.processedFile,
          fileInfo: {
            ...(fabricToUse.fileInfo || {}),
            serverFileName: fabricToUse.serverFileName // 确保在fileInfo中也设置serverFileName
          },
          url: fabricToUse.url // 服务器URL
        },
        // 蒙版描述组件
        {
          componentType: 'maskDescriptionPanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          name: '款式描述',
          selectedTag: clothingMaskPanel.selectedTag,
          customText: clothingMaskPanel.customText,
          description: clothingMaskPanel.selectedTag ?
            tagMapping[clothingMaskPanel.selectedTag] || clothingMaskPanel.selectedTag :
            clothingMaskPanel.customText
        },
        // 随机种子组件
        {
          componentType: 'randomSeedSelector',
          componentId: generateId(ID_TYPES.COMPONENT),
          name: '随机种子',
          useRandom: false,
          value: seed
        },
        // 图片数量组件
        {
          componentType: 'quantityPanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          name: '生成数量',
          quantity: imageQuantity
        }
      ],
      // 使用新的generatedImages结构
      generatedImages: Array(imageQuantity).fill(null).map((_, index) => ({
        imageIndex: index,
        status: 'processing'
      })),
    };

    try{
      await createFlowTask(taskData);

      const resultData = await executeFlow(WORKFLOW_NAME.FABRIC_GENERATION,{
            '59': {
              'prompt': taskData.components[2].description,
            },
        // 面料
            '61': {
              'url':taskData.components[1].url,
            },
        // 服装
            '62': {
              'url':taskData.components[0].url,
            },'52': {
              'seed':taskData.components[3].value,
            },
            '58':{
              'amount':taskData.components[4].quantity,
            },
            "subInfo":{
              "type": "fabric",
              "title":"换面料",
              "count":imageQuantity
            }
          },taskData.taskId
      );
      setIsProcessing(false);
      setIsGenerating(false);
      setHasUnsavedChanges(false);
      if( generationAreaRef.current){

                taskData.promptId = resultData.promptId;
        taskData.instanceId = resultData.instanceId;
        taskData.url = resultData.url;
        taskData.newTask = true;
        taskData.netWssUrl=resultData.netWssUrl;
        taskData.clientId=resultData.clientId;
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      // 准备页面特定参数
      const pageSpecificParams = {};

      // 添加蒙版描述数据
      if (clothingMaskPanel.description) {
        if (!pageSpecificParams.maskDescriptions) {
          pageSpecificParams.maskDescriptions = {};
        }
        pageSpecificParams.maskDescriptions.clothing = clothingMaskPanel.description;
      }
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error('创建任务失败: ' + error.message);
      setIsGenerating(false);
      setHasUnsavedChanges(false);
      
      // 更新任务状态为失败并触发提示音
      taskData.status = 'failed';
      taskData.errorMessage = error.message;
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      
      // 调用updateTask以触发失败提示音
      updateTask(taskData);
    }

  };

  // 处理单张图片下载
  const handleDownloadImage = async (imageUrl, taskId, index) => {
    try {
      message.info('准备下载...');
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      const response = await fetch(httpsUrl);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `${taskId}_${parseInt(index) + 1}.jpg`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      message.error('下载失败');
      console.error('下载出错:', error);
    }
  };

  // 处理批量下载
  const handleBatchDownload = async (task) => {
    // 调用通用下载辅助函数
    await downloadHelper(
      task,
      fetch, // 传递fetch函数
      handleDownloadImage // 传递单张图片下载函数
    );
  };

  // 修改handleEditTask函数，只处理标准数组结构
  const handleEditTask = (task) => {
    if (!task) return;

    try {
      // 添加完整任务数据的日志
      console.log('编辑任务:', task);
      console.log('任务组件:', task.components);

      // 每次回填前，先完全重置所有状态
      setClothingPanels([]);
      setFabricPanels([]);
      // 重置蒙版描述组件状态
      setClothingMaskPanel({
        componentId: generateId(ID_TYPES.COMPONENT),
        selectedTag: '',
        customText: '',
        description: ''
      });

      // 只处理数组结构，不再兼容对象结构
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);

      // 1. 回填服装面板 - 使用小写组件名
      const clothingComponent = components.find(c => c.componentType === 'clothingPanel');
      if (clothingComponent) {
        console.log('获取到服装组件:', clothingComponent);

        // 处理fileInfo，确保size是数字类型
        let fileInfo = clothingComponent.fileInfo;
        if (fileInfo) {
          // 如果size是字符串类型，尝试转换为数字
          if (typeof fileInfo.size === 'string') {
            // 尝试从字符串中提取数字
            const sizeMatch = fileInfo.size.match(/(\d+(\.\d+)?)/);
            if (sizeMatch) {
              const numericPart = parseFloat(sizeMatch[0]);
              // 根据单位转换为字节数
              if (fileInfo.size.includes('MB')) {
                fileInfo = {...fileInfo, size: Math.round(numericPart * 1024 * 1024)};
              } else if (fileInfo.size.includes('KB')) {
                fileInfo = {...fileInfo, size: Math.round(numericPart * 1024)};
              } else {
                fileInfo = {...fileInfo, size: Math.round(numericPart)};
              }
            } else {
              // 如果无法解析，使用默认值
              fileInfo = {...fileInfo, size: 2300000};
            }
          }
        } else {
          // 使用默认fileInfo
          fileInfo = {
            size: 2300000, // 约2.3MB，转换为字节数
            width: 1024,
            height: 1024,
            format: 'image/jpeg'
          };
        }

        setClothingPanels([{
          // 始终生成新的componentId
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT), // 添加id属性，确保不为undefined
          title: clothingComponent.name || '服装',
          status: 'completed', // 确保状态为completed
          serverFileName: clothingComponent.serverFileName,
          url: clothingComponent.url || clothingComponent.originalImage,
          processedFile: clothingComponent.processedFile,
          fileInfo: {
            ...fileInfo,
            serverFileName: clothingComponent.serverFileName // 确保在fileInfo中也设置serverFileName
          }
        }]);
      } else {
        console.warn('未找到服装组件，请检查任务数据');
      }

      // 2. 回填面料图片 - 使用小写组件名
      const fabricComponent = components.find(c => c.componentType === 'fabricPanel');
      if (fabricComponent) {
        console.log('获取到面料组件:', fabricComponent);

        // 处理fileInfo，确保size是数字类型
        let fileInfo = fabricComponent.fileInfo;
        if (fileInfo) {
          // 如果size是字符串类型，尝试转换为数字
          if (typeof fileInfo.size === 'string') {
            // 尝试从字符串中提取数字
            const sizeMatch = fileInfo.size.match(/(\d+(\.\d+)?)/);
            if (sizeMatch) {
              const numericPart = parseFloat(sizeMatch[0]);
              // 根据单位转换为字节数
              if (fileInfo.size.includes('MB')) {
                fileInfo = {...fileInfo, size: Math.round(numericPart * 1024 * 1024)};
              } else if (fileInfo.size.includes('KB')) {
                fileInfo = {...fileInfo, size: Math.round(numericPart * 1024)};
              } else {
                fileInfo = {...fileInfo, size: Math.round(numericPart)};
              }
            } else {
              // 如果无法解析，使用默认值
              fileInfo = {...fileInfo, size: 1800000};
            }
          }
        } else {
          // 使用默认fileInfo
          fileInfo = {
            size: 1800000, // 约1.8MB，转换为字节数
            width: 1024,
            height: 1024,
            format: 'image/jpeg'
          };
        }

        const fabricPanel = {
          // 始终生成新的componentId
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT), // 添加id属性，确保不为undefined
          type: 'fabric',
          title: fabricComponent.name || '面料',
          status: 'completed', // 确保状态为completed
          serverFileName: fabricComponent.serverFileName,
          url: fabricComponent.url || fabricComponent.originalImage,
          processedFile: fabricComponent.processedFile,
          fileInfo: {
            ...fileInfo,
            serverFileName: fabricComponent.serverFileName // 确保在fileInfo中也设置serverFileName
          }
        };

        setFabricPanels([fabricPanel]);
      } else {
        console.warn('未找到面料组件，请检查任务数据');
      }

      // 3. 回填数量设置 - 使用小写组件名
      const quantityComponent = components.find(c => c.componentType === 'quantityPanel');
      if (quantityComponent && quantityComponent.quantity) {
        console.log('获取到数量组件:', quantityComponent);
        setImageQuantity(quantityComponent.quantity);
      } else if (task.imageCount) {
        // 回退1：检查顶层属性
        console.log('使用顶层imageCount属性:', task.imageCount);
        setImageQuantity(task.imageCount);
      } else {
        // 回退2：使用默认值
        console.warn('未找到数量组件，使用默认值2');
        setImageQuantity(2);
      }

      // 4. 回填种子设置 - 使用小写组件名
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      if (seedComponent) {
        console.log('获取到种子设置:', seedComponent);
        // 检查是否有实际使用的种子值
        if (seedComponent.value >= 0) {
          // 如果存在实际使用的种子值，无论原来是否设置为随机，都回填为固定种子
          setUseRandomSeed(false);
          setSeed(seedComponent.value);
          console.log('回填固定种子值:', seedComponent.value);
        } else {
          // 如果没有固定的种子值，则保持原有设置
          setUseRandomSeed(seedComponent.useRandom);
          if (!seedComponent.useRandom && seedComponent.value !== -1) {
            setSeed(seedComponent.value);
          }
        }
      } else if (task.seed !== undefined) {
        // 回退1：检查顶层属性
        console.log('使用顶层seed属性:', task.seed);
        setUseRandomSeed(false);
        setSeed(task.seed);
      } else {
        // 回退2：使用默认值
        console.warn('未找到种子组件，使用默认随机种子');
        setUseRandomSeed(true);
        setSeed(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));
      }

      // 6. 回填蒙版款式描述 - 使用小写组件名
      const maskDescriptionComponent = components.find(c => c.componentType === 'maskDescriptionPanel');
      if (maskDescriptionComponent) {
        console.log('获取到蒙版描述组件:', maskDescriptionComponent);
        // 创建全新的状态对象，不依赖于之前的状态
        const newMaskPanel = {
          componentId: generateId(ID_TYPES.COMPONENT),
          selectedTag: maskDescriptionComponent.selectedTag || '',
          customText: maskDescriptionComponent.customText || '',
          description: maskDescriptionComponent.description || ''
        };
        setClothingMaskPanel(newMaskPanel);
        console.log('回填蒙版描述:', newMaskPanel);
      } else {
        console.warn('未找到蒙版描述组件，使用默认空值');
        // 确保设置为空值
        setClothingMaskPanel({
          componentId: generateId(ID_TYPES.COMPONENT),
          selectedTag: '',
          customText: '',
          description: ''
        });
      }

      // 切换到结果标签页
      setActiveTab('result');

      // 显示成功消息
      message.success({
        content: '配置已重新导入，可继续进行调整',
        duration: 5
      });
    } catch (error) {
      console.error('处理编辑任务时出错:', error);
      message.error('加载任务设置失败');
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // 修改handleViewDetails函数，只处理标准数组结构
  const handleViewDetails = (image, task) => {
    try {
      console.log('查看图片详情, 任务:', task);
      console.log('当前图片:', image);
      // 只处理数组结构，不再兼容对象结构
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);

      // 使用小写组件名查找各个组件
      const clothingComponent = components.find(c => c.componentType === 'clothingPanel');
      const fabricComponent = components.find(c => c.componentType === 'fabricPanel');
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      // 查找蒙版描述组件
      const maskDescriptionComponent = components.find(c => c.componentType === 'maskDescriptionPanel');

      // 记录找到的组件
      if (clothingComponent) console.log('获取到服装组件:', clothingComponent);
      if (fabricComponent) console.log('获取到面料组件:', fabricComponent);
      if (seedComponent) console.log('获取到种子设置:', seedComponent);
      if (maskDescriptionComponent) console.log('获取到蒙版描述组件:', maskDescriptionComponent);

      // 先预加载图片
      // 预加载原图 (仅使用组件数据)
      const clothingImageUrl = clothingComponent?.originalImage || clothingComponent?.url;
      if (clothingImageUrl) {
        const originalImg = new Image();
        originalImg.src = clothingImageUrl;
        console.log('预加载服装图片:', clothingImageUrl);
      }

      // 预加载面料图
      const fabricImageUrl = fabricComponent?.originalImage || fabricComponent?.url;
      if (fabricImageUrl) {
        const fabricImg = new Image();
        fabricImg.src = fabricImageUrl;
        console.log('预加载面料图片:', fabricImageUrl);
      }

      // 预加载生成图
      if (image.url) {
        const resultImg = new Image();
        resultImg.src = image.url;
        console.log('预加载结果图:', image.url);
      }

      // 为ImageDetailsModal准备合适的组件数据结构 - 使用数组结构
      const adaptedComponents = [
        // 服装组件
        clothingComponent ? {
          ...clothingComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT), // 添加id属性，确保不为undefined
          componentType: 'clothingPanel',
          // 确保originalImage和url属性存在且有效
          originalImage: clothingComponent.originalImage || clothingComponent.url,
          url: clothingComponent.url || clothingComponent.originalImage
        } : null,
        // 面料组件
        fabricComponent ? {
          ...fabricComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT), // 添加id属性，确保不为undefined
          componentType: 'fabricPanel',
          // 确保originalImage和url属性存在且有效
          originalImage: fabricComponent.originalImage || fabricComponent.url,
          url: fabricComponent.url || fabricComponent.originalImage
        } : {
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT), // 添加id属性，确保不为undefined
          componentType: 'fabricPanel',
          originalImage: null,
        },
        // 蒙版描述组件
        maskDescriptionComponent ? {
          ...maskDescriptionComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT), // 添加id属性，确保不为undefined
          componentType: 'maskDescriptionPanel'
        } : {
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT),
          componentType: 'maskDescriptionPanel',
          selectedTag: clothingMaskPanel.selectedTag || '',
          customText: clothingMaskPanel.customText || '',
          description: clothingMaskPanel.selectedTag ?
            tagMapping[clothingMaskPanel.selectedTag] || clothingMaskPanel.selectedTag :
            clothingMaskPanel.customText
        },
        // 随机种子组件
        seedComponent ? {
          ...seedComponent,
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT), // 添加id属性，确保不为undefined
          componentType: 'randomSeedSelector'
        } : null
      ].filter(Boolean); // 移除null值

      // 为ImageDetailsModal准备生成图像数据
      // 这里采用通用格式，保证兼容不同来源的数据
      const adaptedGeneratedImage = {
        url: image.url,
        seed: image.seed || (task.generatedImages?.[0]?.seed) || task.seed,
        // 确保有imageIndex，默认为0
        imageIndex: image.imageIndex !== undefined ? image.imageIndex : 0,
        taskId: task.taskId,
        createdAt: task.createdAt,
        components: adaptedComponents,
        fileInfo: image.fileInfo || {}
      };

      // 设置选中的图片 - 直接使用适配后的生成图像，无需额外的嵌套
      return adaptedGeneratedImage
    } catch (error) {
      console.error('处理图片详情时出错:', error);
      message.error('加载图片详情失败');
    }
  };


  const handleClothingStatusChange = (panelId, newStatus) => {
    setClothingPanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
  };

  // 检查任务状态
  const checkTaskStatus = async (taskId) => {
    try {
      // 获取当前用户ID
      const userId = getCurrentUserId() || 'developer';

      // 使用taskService获取任务详情
      const task = await getTaskById(taskId, userId);

      if (task) {
        if (task.status === 'completed') {
          // 任务完成，获取生成的图片
          setGeneratedImages(task.results || []);
          setIsGenerating(false);
          message.success('图片生成成功');
        } else if (task.status === 'failed') {
          // 任务失败
          setIsGenerating(false);
          message.error('生成失败: ' + (task.errorMessage || '未知错误'));
        } else {
          // 任务仍在进行中，继续轮询
          setTimeout(() => checkTaskStatus(taskId), 3000);
        }
      } else {
        setIsGenerating(false);
        message.error('获取任务状态失败: 任务不存在');
      }
    } catch (error) {
      console.error('获取任务状态失败:', error);
      setIsGenerating(false);
      message.error('获取任务状态失败: ' + (error.message || '未知错误'));
    }
  };

  const [uploadedClothingId, setUploadedClothingId] = useState(null);
  const [uploadedClothingUrl, setUploadedClothingUrl] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState(null);
  const [generatedImages, setGeneratedImages] = useState([]);

  // 处理服装复色图片上传
  const handleClothingFileUpload = (file) => {
    console.log('服装图片上传:', file);
  };

  // 处理绘制蒙版
  const handleDrawMask = (panel) => {
    console.log('绘制蒙版:', panel);
    // 这里可以打开蒙版绘制弹窗组件
    // setShowMaskDrawModal(true);
    // setCurrentPanel(panel);
  };

  // 处理面板展开点击
  const handleExpandClick = (panel, position) => {
    console.log('面板展开:', panel.componentId, position);
    // 显示操作面板
    setOperationsPanel({
      panel: panel,
      position
    });
  };

  // 处理面板改变
  const handlePanelChange = (panelId, data) => {
    console.log('面板改变:', panelId, data);
    // 这里可以实现面板数据变更的逻辑
  };


  // 处理面料图片上传结果
  const handleFabricUploadResult = (results) => {
    console.log('处理面料上传结果:', JSON.stringify(results, null, 2));
    setHasUnsavedChanges(true);

    try {
      if (results.type === 'panels') {
        if (currentReuploadFabricPanelId) {
          // 如果是重新上传，替换原有面板
          setFabricPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === currentReuploadFabricPanelId
                ? { ...results.panels[0], componentId: currentReuploadFabricPanelId, type: 'fabric', source: 'upload' }
                : panel
            )
          );
          // 重置当前重新上传的面板ID
          setCurrentReuploadFabricPanelId(null);
        } else {
          // 如果是新上传，添加新面板
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            type: 'fabric',
            source: panel.source || 'upload' // 确保设置source属性
          }));
          console.log('面料图片本地预览创建成功，将在点击生成时上传到服务器', panelsWithType);
          setFabricPanels(prevPanels => [...prevPanels, ...panelsWithType]);
        }
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        // 移除处理中的面板
        setFabricPanels(prevPanels =>
          prevPanels.filter(panel => panel.status !== 'processing'));
        // 重置当前重新上传的面板ID
        setCurrentReuploadFabricPanelId(null);
      }
    } catch (error) {
      console.error('处理面料上传结果时出错:', error);
      message.error('处理面料上传结果时出错: ' + (error.message || '未知错误'));
    }
  };

  // 处理蒙版描述面板变化
  const handleMaskPanelChange = (panel) => {
    if (panel.name === '款式描述' || panel.componentId === clothingMaskPanel.componentId) {
      setClothingMaskPanel(panel);
    }
    setHasUnsavedChanges(true);
  };

  // 创建缩略图并保存到历史记录
  const createThumbnailAndSaveToHistory = (file, localUrl, serverUrl, fileName, type = 'fabric', pageType = 'fabric') => {
    // 确保服务器URL存在且有效
    if (!serverUrl || typeof serverUrl !== 'string' || serverUrl.trim() === '') {
      console.warn('没有有效的服务器URL，不保存到历史记录');
      return;
    }

    console.log(`准备创建缩略图并保存历史记录: 文件=${fileName}, 类型=${type}, 页面=${pageType}, 服务器URL=${serverUrl}`);

    try {
      // 创建Image对象用于生成缩略图
      const img = new Image();
      img.onload = () => {
        try {
          // 创建canvas并生成缩略图
          const canvas = document.createElement('canvas');

          // 设置缩略图最大尺寸
          const MAX_THUMBNAIL_SIZE = 400;

          // 计算缩略图尺寸比例
          let width = img.width;
          let height = img.height;
          const aspectRatio = width / height;

          if (width > height) {
            // 横向图片
            if (width > MAX_THUMBNAIL_SIZE) {
              width = MAX_THUMBNAIL_SIZE;
              height = width / aspectRatio;
            }
          } else {
            // 纵向图片
            if (height > MAX_THUMBNAIL_SIZE) {
              height = MAX_THUMBNAIL_SIZE;
              width = height * aspectRatio;
            }
          }

          // 设置canvas尺寸
          canvas.width = width;
          canvas.height = height;

          // 绘制缩略图
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);

          // 输出为质量为0.6的JPEG
          const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.6);

          // 构建历史记录对象
          const historyRecord = {
            id: Date.now().toString(),
            url: localUrl,
            thumbnailUrl: thumbnailDataUrl,
            serverUrl: serverUrl,
            fileName: fileName || file.name,
            type: type,
            pageType: pageType,
            fileType: file.type,
            saveTime: Date.now()
          };

          // 调用saveToHistory保存记录
          const success = saveToHistory(historyRecord);

          if (success) {
            console.log(`历史记录保存成功, 文件名: ${fileName}`);
          } else {
            console.warn(`历史记录保存失败, 文件名: ${fileName}`);
          }
        } catch (error) {
          console.error('创建缩略图过程中出错:', error);
        }
      };

      img.onerror = (error) => {
        console.error('加载图片时出错:', error);
      };

      // 使用本地URL加载图片
      img.src = localUrl;
    } catch (error) {
      console.error('处理图片文件时出错:', error);
    }
  };

  // 保存历史记录到localStorage
  const saveToHistory = (record) => {
    // 验证记录合法性 - 使用更严格的URL验证
    if (!record || !record.fileName || !record.serverUrl || typeof record.serverUrl !== 'string' || record.serverUrl.trim() === '') {
      console.warn('记录无效或没有有效的服务器URL，无法保存到历史记录');
      return false;
    }

    // 验证服务器URL是否有效
    try {
      // 尝试解析URL以确保格式正确
      new URL(record.serverUrl);
    } catch (error) {
      console.warn(`服务器URL "${record.serverUrl}" 格式无效，不保存到历史记录`);
      return false;
    }

    try {
      // 使用明确的类型定义，遵循指南标准
      const imageType = record.type || 'default_type';
      const pageType = record.pageType || 'default_page';
      const historyKey = `upload_history_${imageType}_${pageType}`;
      console.log('保存历史记录，使用键值:', historyKey);

      // 获取并解析现有历史记录
      let history = [];
      try {
        const historyJson = localStorage.getItem(historyKey);
        if (historyJson) {
          history = JSON.parse(historyJson);
          if (!Array.isArray(history)) {
            console.warn('历史记录格式无效，重置为空数组');
            history = [];
          }
        }
      } catch (e) {
        console.warn('解析历史记录JSON失败，重置为空数组', e);
        history = [];
      }

      // 通过serverUrl检查重复项
      history = history.filter(item => item.serverUrl !== record.serverUrl);

      // 管理历史记录数量
      const MAX_HISTORY_COUNT = 10;
      if (history.length >= MAX_HISTORY_COUNT) {
        history.pop(); // 移除最旧的记录
      }

      // 删除不可序列化的内容并保存
      const recordToSave = { ...record };
      delete recordToSave.file;  // File对象不能序列化

      // 最后一次确认serverUrl存在
      if (!recordToSave.serverUrl) {
        console.warn('最终记录中缺少serverUrl，不保存到历史记录');
        return false;
      }

      history.unshift(recordToSave);  // 新记录添加到开头
      localStorage.setItem(historyKey, JSON.stringify(history));
      console.log(`成功保存历史记录，当前共有 ${history.length} 条记录`);

      return true;
    } catch (err) {
      console.error('保存到历史记录出错:', err);
      return false;
    }
  };

  return (
    <>
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <RequireLogin isLoggedIn={isLoggedIn} featureName="换面料功能">
        <div className="fabric-page">
          <div className="fabric-container" ref={containerRef}>
            <ControlPanel
              ref={controlPanelRef}
              width={`${controlPanelWidth}%`}
              onGenerate={handleGenerate}
              disabled={isGenerating}
              featureName="fabric"
              quantity={imageQuantity}
            >
              {/* 服装上传区域或服装面板 - 始终位于最上方 */}
              {clothingPanels.length === 0 ? (
                <UploadBox
                  id="fabric-clothing-upload-box"
                  onUpload={handleFileUpload}
                  onShowGuide={() => setShowUploadGuide(true)}
                  onUploadResult={handleUploadResult}
                  panels={clothingPanels}
                  className="mt-2"
                  showSupportTag={false}
                  pageType="fabric"
                  uploadType="clothing"
                />
              ) : (
                // 展示服装面板
                clothingPanels.map((panel) => (
                  <ClothingPanel
                    key={panel.componentId}
                    panel={panel}
                    onDelete={() => handleDeleteClothingPanel(panel.componentId)}
                    onReupload={() => handleReuploadClothing(panel)}
                    onStatusChange={(newStatus) => handleClothingStatusChange(panel.componentId, newStatus)}
                    onExpandClick={(panel, position) => {
                      setOperationsPanel({
                        panel: panel,
                        position
                      });
                    }}
                    onPanelsChange={(data) => handlePanelChange(panel.componentId, data)}
                    status={panel.status}
                    onUploadResult={handleUploadResult}
                    pageType="fabric"
                  />
                ))
              )}

              {/* 服装蒙版描述面板 - 位于服装面板下方 */}
              {clothingPanels.length > 0 && (
                <MaskDescriptionPanel
                  panel={clothingMaskPanel}
                  onPanelsChange={handleMaskPanelChange}
                />
              )}

              {/* 面料上传区域或面料面板 - 始终位于服装下方 */}
              {fabricPanels.length === 0 ? (
                <UploadBox_Model
                  id="fabric-upload-box"
                  onUpload={handleFabricFileUpload}
                  onShowGuide={() => setExtraFabricUploadGuide(true)}
                  onUploadResult={handleFabricUploadResult2}
                  modelPanels={fabricPanels}
                  className="mt-2"
                  showSupportTag={false}
                  pageType="fabric"
                  uploadType="fabric"
                />
              ) : (
                // 展示面料面板
                fabricPanels.map((panel) => (
                  <FabricPanel
                    key={panel.componentId}
                    panel={panel}
                    onDelete={() => handleDeleteFabricPanel(panel.componentId)}
                    onReupload={() => handleReuploadFabric(panel)}
                    onStatusChange={(newStatus) => handleFabricStatusChange(panel.componentId, newStatus)}
                    onExpandClick={handleExpandClick}
                    onPanelsChange={(data) => handlePanelChange(panel.componentId, data)}
                    status={panel.status}
                    onUploadResult={handleFabricUploadResult2}
                    pageType="fabric"
                  />
                ))
              )}

              {/* 提示信息面板 */}
              {(
                <TipsPanel
                  tipContent={FABRIC_TIP}
                />
              )}

              {/* 随机种子选择器 */}
              <RandomSeedSelector
                onRandomChange={setUseRandomSeed}
                onSeedChange={setSeed}
                defaultRandom={useRandomSeed}
                defaultSeed={seed}
                // 编辑模式下传递历史种子
                isEdit={selectedImage !== null}
                editSeed={selectedImage?.seed || null}
              />

              {/* 数量面板 */}
              <QuantityPanel
                imageQuantity={imageQuantity}
                onChange={setImageQuantity}
                min={1}
                max={4}
                pageType="fabric"
              />
            </ControlPanel>

            <ResizeHandle
              ref={handleRef}
              containerRef={containerRef}
              onResize={setControlPanelWidth}
              minWidth={25}
              maxWidth={50}
            />

                    <GenerationArea
              ref={generationAreaRef}    setIsProcessing={setIsGenerating}

              activeTab={activeTab}
              onTabChange={setActiveTab}
              onEditTask={handleEditTask}
              onDownloadImage={handleDownloadImage}
              onViewDetails={handleViewDetails}
              onBatchDownload={handleBatchDownload}
              pageType="fabric"
            />
          </div>

          {/* 服装上传指南弹窗 */}
          {showUploadGuide && (
            <UploadGuideModal
              type="clothing"
              pageType="fabric"
              onClose={() => setShowUploadGuide(false)}
              onUpload={(result) => {
                console.log('收到上传结果:', result);
                handleUploadResult(result);
                setHasUnsavedChanges(true);
                // 根据结果中的shouldClose字段决定是否关闭弹窗
                if (result.shouldClose !== false) {
                  setShowUploadGuide(false);
                }
              }}
            />
          )}

          {/* 面料上传指南弹窗 */}
          {extraFabricUploadGuide && (
            <UploadGuideModal
              onClose={() => setExtraFabricUploadGuide(false)}
              onUpload={(result) => {
                console.log('收到面料上传结果:', result);
                handleFabricUploadResult2(result);

                // 根据结果中的shouldClose字段决定是否关闭弹窗
                if (result.shouldClose !== false) {
                  setExtraFabricUploadGuide(false);
                }
              }}
              type="fabric"
              pageType="fabric"
              uploadType="fabric"
            />
          )}

          {/* 面料上传指南弹窗 */}
          {showFabricUploadGuide && (
            <UploadGuideModal
              onClose={() => setShowFabricUploadGuide(false)}
              onUpload={(result) => {
                console.log('收到面料上传结果:', result);
                handleFabricUploadResult(result);

                // 根据结果中的shouldClose字段决定是否关闭弹窗
                if (result.shouldClose !== false) {
                  setShowFabricUploadGuide(false);
                }
              }}
              type="fabric"
              pageType="fabric"
            />
          )}

          {/* 添加蒙版绘制弹窗 */}

          {/* 添加操作弹窗 */}
          {operationsPanel && (
            <ImageInfoModal
              panel={operationsPanel.panel}
              position={operationsPanel.position}
              onClose={() => setOperationsPanel(null)}
              onDelete={operationsPanel.panel.type === 'fabric' ? handleDeleteFabricPanel : handleDeleteClothingPanel}
              onReupload={operationsPanel.panel.type === 'fabric' ? handleReuploadFabric : handleReuploadClothing}
              pageType="fabric"
            />
          )}



          {/* 显示上传的服装图片和生成按钮 */}
          {uploadedClothingUrl && !isGenerating && (
            <div className="action-buttons">
              <Button
                type="primary"
                onClick={handleGenerate}
                disabled={isGenerating}
              >
                开始生成
              </Button>
            </div>
          )}

          {/* 显示生成中的加载状态 */}
          {isGenerating && (
            <div className="generating-status">
              <Spin size="large" />
              <p>正在生成中，请稍候...</p>
            </div>
          )}

          {/* 显示生成的图片 */}
          {generatedImages.length > 0 && (
            <div className="generated-images">
              <h3>生成结果</h3>
              <div className="image-grid">
                {generatedImages.map((image, index) => (
                  <div key={index} className="image-item">
                    <img src={image.url} alt={`生成图片 ${index + 1}`} />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </RequireLogin>
    </>
  );
};

export default FabricPage;