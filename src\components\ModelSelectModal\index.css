/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/buttons.css';
@import '../../styles/inputs.css';
@import '../../styles/tabs.css';
@import '../../styles/panels.css';
@import '../../styles/cards.css';
@import '../../styles/scrollbars.css';
@import '../../styles/common.css';

.model-select-modal {
  composes: base-modal;
}

.model-select-modal .modal-content {
  pointer-events: auto;
  width: 1200px;
  height: calc(100vh - 150px);
  min-height: 600px;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  margin-left: 290px;
  border: 1px solid var(--border-color);
}

.model-select-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  margin-top: 0;
  padding-bottom: 80px;
  will-change: transform;
  -webkit-overflow-scrolling: touch;
}

/* 更新标签页样式 */
.model-select-modal .modal-header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 20px 20px 0;
  margin: 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  background: var(--bg-primary);
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  padding: 0;
  width: 100%;
}

.model-item {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  cursor: pointer;
  aspect-ratio: 2/3;
  padding: 0;
  width: 100%;
  display: block;
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
}

.model-item:hover {
  border-color: var(--brand-primary);
  box-shadow: var(--shadow-brand);
}

.model-item.selected {
  border-color: var(--brand-primary);
  box-shadow: var(--shadow-brand);
}

.model-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: block;
}

.model-preview img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.model-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-xs);
  background: var(--bg-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0;
  border-top: 1px solid var(--border-lighter);
}

.model-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.model-number {
  font-size: var(--font-size-sm);
  opacity: 0.85;
  color: var(--text-secondary);
  font-weight: 500;
}

.model-name {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.model-info h4 {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin: 0;
  font-weight: 500;
}

.model-info p {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin: 0;
}

.use-model-btn {
  padding: var(--spacing-xxs) var(--spacing-xs);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: var(--transition-normal);
}

.use-model-btn:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.empty-custom {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
  padding: var(--spacing-xl);
}

.empty-custom .coming-soon-icon {
  width: 120px;
  height: 120px;
  margin-bottom: var(--spacing-xl);
  opacity: 0.8;
}

.empty-custom h3 {
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.empty-custom p {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

/* 添加筛选区域样式 */
.model-select-modal .filter-section {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.model-select-modal .filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.model-select-modal .filter-group label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  min-width: 60px;
}

.model-select-modal .filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.model-select-modal .filter-option {
  padding: var(--spacing-xxs) var(--spacing-sm);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-normal);
}

.model-select-modal .filter-option:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.model-select-modal .filter-option.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.preview-button {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-sm);
  border: none;
  background: var(--bg-hover);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  padding: 0;
  flex-shrink: 0;
}

.preview-button:hover {
  background: var(--bg-active);
  color: var(--text-primary);
}

.preview-button svg {
  width: var(--font-size-md);
  height: var(--font-size-md);
}

/* 自定义模特页面样式 */
.custom-model {
  padding: 0 var(--spacing-lg);
}

.custom-model .section-title {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0 0 16px;
  font-weight: 500;
}

[data-theme="dark"] .custom-model .section-title {
  color: var(--text-primary);
}

.custom-model .prompt-input {
  margin-bottom: var(--spacing-lg);
}

.custom-model .prompt-input label {
  display: block;
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin-bottom: 8px;
}

[data-theme="dark"] .custom-model .prompt-input label {
  color: var(--text-secondary);
}

.custom-model .prompt-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  resize: none;
  transition: var(--transition-normal);
}

[data-theme="dark"] .custom-model .prompt-textarea {
  background: var(--bg-secondary);
  border-color: var(--border-light);
  color: var(--text-primary);
}

.custom-model .prompt-textarea:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

[data-theme="dark"] .custom-model .prompt-textarea:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

.custom-model .prompt-textarea::placeholder {
  color: var(--text-tertiary);
}

[data-theme="dark"] .custom-model .prompt-textarea::placeholder {
  color: var(--text-tertiary);
}

/* 使用公共样式类 */
.modal-footer,
.custom-scene-actions {
  composes: button-group from '../../styles/buttons.css';
}

.model-select-modal .panel-component {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  height: 88px;
  display: flex;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 4px;
  transition: all 0.2s ease;
}

.model-select-modal .component-header {
  display: flex;
  align-items: stretch;
  width: 100%;
  background: white;
  border: 1px solid #eee;
  border-radius: 12px;
  padding: 0;
}

.model-select-modal .component-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.model-select-modal .component-text {
  flex: 1;
  min-width: 0;
}

.model-select-modal .component-text h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.model-select-modal .component-content {
  margin-top: 4px;
}

.model-select-modal .component-content p {
  margin: 0;
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 展开按钮样式 */
.model-select-modal .expand-btn {
  width: 48px;
  height: 88px;
  border: none;
  background: none;
  cursor: pointer;
  position: relative;
  padding: 0;
  border-left: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.model-select-modal .expand-btn:hover {
  background-color: #f8f9fa;
}

.model-select-modal .expand-btn span {
  display: block;
  width: 10px;
  height: 10px;
  border-right: 2px solid #666;
  border-bottom: 2px solid #666;
  transform: rotate(-45deg);
  transition: all 0.2s ease;
}

.model-select-modal .expand-btn:hover span {
  border-color: #FF3C6A;
  transform: rotate(-45deg) scale(1.1);
}

/* 选中状态的模特预览样式 */
.model-select-modal .selected-model-preview {
  width: 88px;
  height: 88px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
  background: white;
}

.model-select-modal .selected-model-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top center;
}

/* 完成状态的展开按钮样式 */
.model-select-modal .panel-component:has(.selected-model-preview) .expand-btn {
  background: rgba(255, 60, 106, 0.08);
  border-color: rgba(255, 60, 106, 0.1);
}

.model-select-modal .panel-component:has(.selected-model-preview) .expand-btn span {
  border-color: #FF3C6A;
}

.model-select-modal .panel-component:has(.selected-model-preview) .expand-btn:hover {
  background: rgba(255, 60, 106, 0.12);
}

/* 添加新的状态管理样式 */
.model-select-modal .model-item {
  position: relative;
}

.model-select-modal .model-item.selected::after {
  display: none;
}

.model-select-modal .model-item.selected .model-caption {
  background: white;
}

/* 骨架屏样式 */
.model-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 12px;
  margin-top: 16px;
  padding: 0;
  width: 100%;
}

.model-skeleton-item {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  aspect-ratio: 2/3;
  background: #f5f5f5;
  border: 1px solid #eee;
}

.model-skeleton-image {
  width: 100%;
  height: calc(100% - 40px);
  background: linear-gradient(90deg, #f0f0f0 25%, #f8f8f8 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.model-skeleton-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  padding: 8px;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.model-skeleton-text {
  width: 70%;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #f8f8f8 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.model-skeleton-button {
  width: 24px;
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #f8f8f8 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 图片预览弹窗样式 - 已迁移到独立组件 */
