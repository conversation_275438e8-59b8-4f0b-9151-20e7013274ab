{"19": {"inputs": {"needInput": true, "seed": 179827759379037, "steps": 25, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 0.5000000000000001, "model": ["29", 0], "positive": ["49", 0], "negative": ["21", 0], "latent_image": ["53", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "随机种， denoise调用变化强度"}}, "20": {"inputs": {"text": ["47", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["22", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "21": {"inputs": {"conditioning": ["20", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "22": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "23": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "24": {"inputs": {"pixels": ["25", 0], "vae": ["23", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "25": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 768, "background_color": "#000000", "image": ["50", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "26": {"inputs": {"samples": ["19", 0], "vae": ["23", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "27": {"inputs": {"width": ["28", 0], "height": ["28", 1], "upscale_method": "bicubic", "keep_proportion": "stretch", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 1, "device": "cpu", "image": ["26", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "28": {"inputs": {"image": ["50", 0]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}, "29": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "32": {"inputs": {"caption_type": "Descriptive", "caption_length": "short", "max_new_tokens": 512, "top_p": 0.9, "top_k": 0, "temperature": 0.6, "user_prompt": "", "speak_and_recognation": {"__value__": [false, true]}, "image": ["50", 0], "joycaption_beta1_model": ["33", 0]}, "class_type": "LayerUtility: JoyCaptionBeta1", "_meta": {"title": "LayerUtility: JoyCaption Beta One (Advance)"}}, "33": {"inputs": {"model": "fancyfeast/llama-joycaption-beta-one-hf-llava", "quantization_mode": "nf4", "device": "cuda"}, "class_type": "LayerUtility: LoadJoyCaptionBeta1Model", "_meta": {"title": "LayerUtility: Load JoyCaption Beta One Model (Advance)"}}, "45": {"inputs": {"needInput": true, "from_translate": "auto", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "GoogleTranslator", "text": "", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "自定义提示词"}}, "47": {"inputs": {"select": 1, "sel_mode": false, "input1": ["32", 0], "input2": ["45", 0]}, "class_type": "ImpactSwitch", "_meta": {"title": "选1 AI自动生成，选2自定义输入提示词"}}, "48": {"inputs": {"needInput": true, "text_0": "Photograph of a slender, young Caucasian woman with long brown hair, wearing a black floral-patterned, red-trimmed, halter-neck romper with a keyhole cutout, sitting on a white wooden bench outdoors. Background includes greenery, a tree, and a white table with a floral tablecloth.", "text": ["32", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "49": {"inputs": {"guidance": 3.5, "conditioning": ["20", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "50": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "上传图片"}}, "51": {"inputs": {"filename_prefix": "Divergent", "images": ["27", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存"}}, "53": {"inputs": {"needInput": true, "amount": 4, "samples": ["24", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "图片数量"}}}