.promotion-banner {
  position: relative;
  width: 100%;
  height: min(228px, 24vh);
  border-radius: var(--radius-lg);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  transition: background-image 0.3s ease-in-out;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.promotion-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.28);
  z-index: 1;
}

[data-theme="dark"] .promotion-banner::before {
  background: rgba(255, 255, 255, 0.18);
}

.promotion-banner.no-mask::before {
  display: none;
}

.promotion-content {
  position: relative;
  z-index: 2;
  padding: 0 20px;
  max-width: 800px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.promotion-content h1 {
  font-size: clamp(1.25rem, 2.3vw, 2rem);
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.2;
  text-shadow: 0 2px 8px rgba(60,60,60,0.32) !important;
  color: var(--text-inverse);
}

.promotion-subtitle {
  font-size: clamp(0.875rem, 1.2vw, 1.1rem);
  margin-bottom: 0;
  line-height: 1.8;
  text-shadow: 0 2px 8px rgba(60,60,60,0.32) !important;
  color: var(--text-inverse);
  opacity: 0.9;
  font-weight: 528;
}

.promotion-content .cta-button {
  align-self: center;
  width: auto;
  min-width: 180px;
  max-width: 280px;
}

.cta-button {
  background: var(--text-inverse);
  color: transparent;
  border: none;
  padding: 12px 32px;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 580;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  margin-top: 12px;
  margin-bottom: 0px;
}

.cta-button span {
  background: linear-gradient(45deg, #4158D0, #C850C0, #FFCC70);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dynamic-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #4158D0, #C850C0, #FFCC70);
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
  z-index: 1;
}

/* 登录前样式 */
.promotion-banner.logged-out .dynamic-background {
  background: linear-gradient(45deg, #4158D0, #C850C0, #FFCC70);
}

/* 登录后样式 */
.promotion-banner.logged-in .dynamic-background {
  background: linear-gradient(45deg, #4158D0, #C850C0, #FFCC70);
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 响应式样式 */
@media (max-width: 888px) {
  .promotion-banner {
    height: min(160px, 18vh);
  }

  .promotion-content {
    padding: min(16px, 1.8vw);
  }

  .promotion-content h1 {
    font-size: clamp(0.9rem, 2.5vw, 1.1rem);
  }

  .promotion-subtitle {
    font-size: clamp(0.75rem, 1vw, 0.9rem);
    margin-bottom: 8px;
  }

  .cta-button {
    padding: 8px 20px;
    font-size: 0.85rem;
    min-width: 140px;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .promotion-banner {
    height: min(100px, 12vh);
  }

  .promotion-content {
    padding: min(12px, 1.5vw);
  }

  .promotion-content h1 {
    font-size: clamp(0.8rem, 1vw, 0.9rem);
    margin-bottom: min(8px, 1vw);
  }

  .promotion-subtitle {
    font-size: clamp(0.7rem, 0.9vw, 0.8rem);
    margin-bottom: 6px;
  }

  .cta-button {
    padding: 6px 16px;
    font-size: 0.8rem;
    min-width: 120px;
    max-width: 160px;
  }
}

[data-theme="dark"] .promotion-content h1,
[data-theme="dark"] .promotion-content .promotion-subtitle {
  text-shadow: 0 2px 8px rgba(255,255,255,0.18) !important;
} 