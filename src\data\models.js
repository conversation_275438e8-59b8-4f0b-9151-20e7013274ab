// 预设模特数据
export const PRESET_MODELS = [
  { id: 1, name: "小艾", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 2, name: "美玲", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 3, name: "Ka<PERSON>bal<PERSON>", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 4, name: "<PERSON><PERSON>", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'athletic' },
  { id: 5, name: "梦瑶", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'athletic' },
  { id: 6, name: "<PERSON>", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 7, name: "<PERSON>", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 8, name: "思琪", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 9, name: "欣怡", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'athletic' },
  { id: 10, name: "<PERSON>", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 11, name: "文静", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 12, name: "Emily", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 13, name: "Elizabeth", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 14, name: "晓婷", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'athletic' },
  { id: 15, name: "Sofia", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 16, name: "嘉欣", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 17, name: "Madison", gender: 'female', age: 'adult', region: 'western', bodyType: 'athletic' },
  { id: 18, name: "Scarlett", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 19, name: "Victoria", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 20, name: "雅芝", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 21, name: "Grace", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 22, name: "Chloe", gender: 'female', age: 'adult', region: 'western', bodyType: 'athletic' },
  { id: 23, name: "Camila", gender: 'female', age: 'adult', region: 'latinAmerica', bodyType: 'standard' },
  { id: 24, name: "佳琪", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 25, name: "Riley", gender: 'female', age: 'adult', region: 'western', bodyType: 'athletic' },
  { id: 26, name: "Layla", gender: 'female', age: 'adult', region: 'middleEast', bodyType: 'standard' },
  { id: 27, name: "莉莉", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 28, name: "诗雨", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 29, name: "Zoey", gender: 'female', age: 'adult', region: 'western', bodyType: 'athletic' },
  { id: 30, name: "美琪", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 31, name: "雨欣", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 32, name: "Hannah", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 33, name: "小莉", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 34, name: "Addison", gender: 'female', age: 'adult', region: 'western', bodyType: 'athletic' },
  { id: 35, name: "Eleanor", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 36, name: "佳雯", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 37, name: "Luna", gender: 'female', age: 'adult', region: 'latinAmerica', bodyType: 'standard' },
  { id: 38, name: "思颖", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'athletic' },
  { id: 39, name: "Brooklyn", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 40, name: "雅婷", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 41, name: "Zoe", gender: 'female', age: 'adult', region: 'western', bodyType: 'athletic' },
  { id: 42, name: "晓琳", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 43, name: "Hazel", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 44, name: "婷婷", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 45, name: "Paisley", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' },
  { id: 46, name: "雅琪", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 47, name: "Skylar", gender: 'female', age: 'adult', region: 'western', bodyType: 'athletic' },
  { id: 48, name: "梦洁", gender: 'female', age: 'adult', region: 'eastAsia', bodyType: 'standard' },
  { id: 49, name: "Claire", gender: 'female', age: 'adult', region: 'western', bodyType: 'standard' }
];

// 获取模特图片路径
export const getModelImagePath = (modelId) => `/images/modelscard/model-${modelId}.jpg`;

// 根据筛选条件获取模特列表
export const getFilteredModels = (filters) => {
  return PRESET_MODELS.filter(model => {
    // 如果筛选条件是 'all'，则不进行该条件的筛选
    if (filters.gender !== 'all' && model.gender !== filters.gender) return false;
    if (filters.age !== 'all' && model.age !== filters.age) return false;
    if (filters.region !== 'all' && model.region !== filters.region) return false;
    if (filters.bodyType !== 'all' && model.bodyType !== filters.bodyType) return false;
    return true;
  });
}; 