# 多平台进度更新 Python 脚本适配

## 概述

本文档说明了如何修改 Python 脚本以支持新平台（RunningHub）的进度更新，实现统一的多平台任务监听和进度推送机制。

## 1. 主要改进

### 1.1 连接管理器增强

#### **支持多平台连接池：**
```python
class GlobalConnectionManager:
    def __init__(self):
        # ComfyUI连接池
        self.comfy_connections = {}
        
        # RunningHub连接池 - 新增
        self.runninghub_connections = {}
        
        # 任务监听器增强
        self.task_listeners = {
            # task_id: {
            #   'platform': 'comfyui'|'runninghub',  # 新增平台标识
            #   'comfy_ws': websocket,               # ComfyUI连接
            #   'runninghub_ws': websocket,          # RunningHub连接 - 新增
            #   'task_data': dict                    # 平台特定数据 - 新增
            # }
        }
```

### 1.2 RunningHub连接管理

#### **创建RunningHub连接：**
```python
async def create_runninghub_connection(self, netWssUrl, task_id=None):
    """为每个任务创建独立的RunningHub连接"""
    connection_key = f"{netWssUrl}_{task_id}" if task_id else netWssUrl

    if connection_key in self.runninghub_connections:
        conn_info = self.runninghub_connections[connection_key]
        if not self.is_closed(conn_info['ws']):
            conn_info['last_used'] = time.time()
            return conn_info['ws']

    try:
        runninghub_ws = await websockets.connect(netWssUrl)
        self.runninghub_connections[connection_key] = {
            'ws': runninghub_ws,
            'last_used': time.time(),
            'task_count': 1,
            'task_id': task_id,
            'netWssUrl': netWssUrl
        }
        return runninghub_ws
    except Exception as e:
        print(f"创建RunningHub连接失败: {e}")
        return None
```

#### **关闭RunningHub连接：**
```python
def close_runninghub_connection(self, netWssUrl, task_id=None):
    """关闭RunningHub连接"""
    if task_id:
        connection_key = f"{netWssUrl}_{task_id}"
        if connection_key in self.runninghub_connections:
            conn_info = self.runninghub_connections[connection_key]
            if not self.is_closed(conn_info['ws']):
                asyncio.run(conn_info['ws'].close())
            del self.runninghub_connections[connection_key]
```

## 2. 多平台任务订阅

### 2.1 增强的订阅方法

#### **支持平台参数：**
```python
def subscribe_task(self, connection_id, task_id, prompt_id, instance_ws_url, 
                  platform='comfyui', task_data=None):
    """订阅任务进度 - 支持多平台"""
    
    # 创建任务监听器
    if task_id not in self.task_listeners:
        self.task_listeners[task_id] = {
            'prompt_id': prompt_id,
            'platform': platform,              # 平台标识
            'instance_ws_url': instance_ws_url,
            'comfy_ws': None,
            'runninghub_ws': None,             # RunningHub连接
            'listeners': set(),
            'status': 'active',
            'created_at': time.time(),
            'task_data': task_data or {}       # 平台特定数据
        }

    # 根据平台启动相应的监听
    if platform == 'comfyui':
        if not self.task_listeners[task_id]['comfy_ws']:
            self.start_comfy_listener(task_id)
    elif platform == 'runninghub':
        if not self.task_listeners[task_id]['runninghub_ws']:
            self.start_runninghub_listener(task_id)
```

### 2.2 WebSocket订阅消息格式

#### **新的订阅消息格式：**
```javascript
// 订阅RunningHub任务
{
  "type": "subscribe_task",
  "task_id": "task_123",
  "prompt_id": "1949465676051324929",
  "instance_ws_url": "wss://www.runninghub.cn:443/ws/...",
  "platform": "runninghub",
  "task_data": {
    "netWssUrl": "wss://www.runninghub.cn:443/ws/c_instance?...",
    "clientId": "88f6947dc37e94b20654a5c5e67fce69",
    "taskStatus": "RUNNING"
  }
}

// 订阅ComfyUI任务（兼容旧格式）
{
  "type": "subscribe_task",
  "task_id": "task_456",
  "prompt_id": "comfy_prompt_123",
  "instance_ws_url": "ws://comfyui-instance:8188/ws",
  "platform": "comfyui"  // 可选，默认为comfyui
}
```

## 3. RunningHub监听器

### 3.1 监听器启动

#### **启动RunningHub监听：**
```python
def start_runninghub_listener(self, task_id):
    """启动RunningHub监听"""
    def start_listener():
        asyncio.run(self.listen_to_runninghub_instance(task_id))
    
    threading.Thread(target=start_listener, daemon=True).start()
```

### 3.2 RunningHub消息处理

#### **监听RunningHub实例：**
```python
async def listen_to_runninghub_instance(self, task_id):
    """监听RunningHub实例的消息"""
    task_info = self.task_listeners[task_id]
    
    # 获取连接信息
    task_data = task_info.get('task_data', {})
    netWssUrl = task_data.get('netWssUrl')
    
    # 创建连接
    runninghub_ws = await self.create_runninghub_connection(netWssUrl, task_id)
    task_info['runninghub_ws'] = runninghub_ws
    
    # 监听消息
    while task_info['status'] == 'active':
        message = await runninghub_ws.recv()
        message_data = json.loads(message)
        
        # 处理不同类型的消息
        message_type = message_data.get("type")
        
        if message_type == "task_progress":
            # 进度更新
            progress_data = message_data.get("data", {})
            progress_message = {
                "type": "progress",
                "data": {
                    "value": progress_data.get("progress", 0),
                    "max": 100,
                    "prompt_id": task_info['prompt_id'],
                    "platform": "runninghub"
                },
                "task_id": task_id
            }
            self.broadcast_to_task_listeners(task_id, progress_message)
            
        elif message_type == "task_completed":
            # 任务完成
            task_info['status'] = 'completed'
            self.send_completion_message(task_id, task_info['prompt_id'])
            break
            
        elif message_type == "status_update":
            # 状态更新
            status_data = message_data.get("data", {})
            status = status_data.get("status")
            
            if status in ["SUCCESS", "COMPLETED"]:
                task_info['status'] = 'completed'
                self.send_completion_message(task_id, task_info['prompt_id'])
                break
            elif status in ["FAILED", "ERROR"]:
                task_info['status'] = 'failed'
                self.send_completion_message(task_id, task_info['prompt_id'])
                break
```

## 4. 消息格式标准化

### 4.1 进度消息格式

#### **统一的进度消息：**
```json
{
  "type": "progress",
  "data": {
    "value": 50,
    "max": 100,
    "prompt_id": "task_prompt_id",
    "platform": "runninghub"
  },
  "task_id": "task_123"
}
```

### 4.2 完成消息格式

#### **统一的完成消息：**
```json
{
  "type": "completed",
  "data": {
    "prompt_id": "task_prompt_id",
    "status": "success",
    "platform": "runninghub"
  },
  "task_id": "task_123"
}
```

### 4.3 状态更新消息

#### **平台特定状态消息：**
```json
{
  "type": "status_update",
  "data": {
    "status": "RUNNING",
    "platform": "runninghub",
    "additional_info": {}
  },
  "task_id": "task_123"
}
```

## 5. 连接清理和管理

### 5.1 增强的清理机制

#### **多平台连接清理：**
```python
def cleanup_dead_connections(self):
    """清理断开的连接和超时的连接"""
    current_time = time.time()
    
    # 清理ComfyUI连接
    dead_comfy_connections = []
    for connection_key, conn_info in self.comfy_connections.items():
        if (self.is_closed(conn_info['ws']) or
                current_time - conn_info.get('last_used', 0) > self.connection_timeout):
            dead_comfy_connections.append((connection_key, conn_info.get('task_id')))
    
    # 清理RunningHub连接
    dead_runninghub_connections = []
    for connection_key, conn_info in self.runninghub_connections.items():
        if (self.is_closed(conn_info['ws']) or
                current_time - conn_info.get('last_used', 0) > self.connection_timeout):
            dead_runninghub_connections.append((connection_key, conn_info.get('task_id')))
    
    # 执行清理
    for connection_key, task_id in dead_comfy_connections:
        self.close_comfy_connection(connection_key, task_id)
    
    for connection_key, task_id in dead_runninghub_connections:
        self.close_runninghub_connection(connection_key, task_id)
```

### 5.2 优雅关闭

#### **关闭所有连接：**
```python
def shutdown(self):
    """关闭所有连接"""
    self.running = False
    
    # 关闭所有ComfyUI连接
    for connection_key, conn_info in self.comfy_connections.items():
        try:
            asyncio.run(conn_info['ws'].close())
        except:
            pass
    
    # 关闭所有RunningHub连接
    for connection_key, conn_info in self.runninghub_connections.items():
        try:
            asyncio.run(conn_info['ws'].close())
        except:
            pass
    
    # 清理所有数据
    self.comfy_connections.clear()
    self.runninghub_connections.clear()
    self.frontend_connections.clear()
    self.task_listeners.clear()
```

## 6. 使用示例

### 6.1 前端订阅RunningHub任务

```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:5000/ws/progress');

// 发送连接信息
ws.send(JSON.stringify({
  type: "connection_info",
  connection_id: "frontend_123"
}));

// 订阅RunningHub任务
ws.send(JSON.stringify({
  type: "subscribe_task",
  task_id: "runninghub_task_456",
  prompt_id: "1949465676051324929",
  instance_ws_url: "wss://www.runninghub.cn:443/ws/...",
  platform: "runninghub",
  task_data: {
    netWssUrl: "wss://www.runninghub.cn:443/ws/c_instance?...",
    clientId: "88f6947dc37e94b20654a5c5e67fce69",
    taskStatus: "RUNNING"
  }
}));

// 监听消息
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  
  if (data.type === 'progress') {
    console.log(`任务进度: ${data.data.value}/${data.data.max} (${data.data.platform})`);
  } else if (data.type === 'completed') {
    console.log(`任务完成: ${data.task_id} (${data.data.platform})`);
  }
};
```

### 6.2 兼容性

#### **向后兼容ComfyUI：**
```javascript
// 旧格式仍然支持
ws.send(JSON.stringify({
  type: "subscribe_task",
  task_id: "comfyui_task_789",
  prompt_id: "comfyui_prompt_123",
  instance_ws_url: "ws://comfyui-instance:8188/ws"
  // platform 默认为 "comfyui"
}));
```

## 7. 总结

### 7.1 主要改进

- ✅ **多平台支持**：同时支持ComfyUI和RunningHub平台
- ✅ **统一接口**：提供一致的订阅和消息格式
- ✅ **连接管理**：独立的连接池和清理机制
- ✅ **向后兼容**：保持对现有ComfyUI集成的兼容性

### 7.2 技术特性

- **异步处理**：每个平台独立的异步监听器
- **消息标准化**：统一的进度和状态消息格式
- **错误处理**：健壮的连接管理和错误恢复
- **资源管理**：自动清理断开的连接和超时任务

### 7.3 扩展性

- **平台扩展**：易于添加新的平台支持
- **消息类型**：灵活的消息类型处理机制
- **配置化**：可配置的超时和清理策略

通过这些改进，Python脚本现在能够统一处理多个平台的任务进度更新，为前端提供一致的WebSocket接口体验。🎉
