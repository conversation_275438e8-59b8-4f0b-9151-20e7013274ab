const User = require('../../auth/user.model');
const bcrypt = require('bcryptjs');
const createError = require('http-errors');
const { CreditBalance } = require('../../admin/credits/credit.model');

/**
 * 获取所有用户
 */
exports.getAllUsers = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, search, role, status } = req.query;
    
    // 构建查询条件
    const query = {};
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (role) {
      query.role = role;
    }
    
    if (status) {
      query.status = status;
    }
    
    // 计算总数
    const total = await User.countDocuments(query);
    
    // 查询用户
    const users = await User.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(Number(limit))
      .lean();
    
    // 获取所有用户ID
    const userIds = users.map(user => user._id);
    
    // 查询这些用户的算力余额
    const creditBalances = await CreditBalance.find({ user: { $in: userIds } }).lean();
    console.log('查询到的算力余额:', creditBalances);
    
    // 创建用户ID到算力余额的映射
    const creditBalanceMap = {};
    creditBalances.forEach(balance => {
      creditBalanceMap[balance.user.toString()] = balance;
    });
    
    // 将算力余额添加到用户数据中
    const usersWithCredits = users.map(user => {
      const userId = user._id.toString();
      const creditBalance = creditBalanceMap[userId] || { 
        balance: 0, 
        totalRecharged: 0, 
        totalConsumed: 0 
      };
      
      return {
        ...user,
        creditBalance: {
          balance: creditBalance.balance || 0,
          totalRecharged: creditBalance.totalRecharged || 0,
          totalConsumed: creditBalance.totalConsumed || 0,
          lastRechargeDate: creditBalance.lastRechargeDate || null
        }
      };
    });
    
    res.json({
      total,
      page: Number(page),
      limit: Number(limit),
      data: usersWithCredits
    });
  } catch (error) {
    console.error('获取用户列表错误:', error);
    next(createError(500, '获取用户列表失败', error));
  }
};

/**
 * 获取单个用户
 */
exports.getUser = async (req, res, next) => {
  try {
    const userId = req.params.id;
    
    // 查询用户
    const user = await User.findById(userId).lean();
    if (!user) {
      return next(createError(404, '用户不存在'));
    }
    
    // 查询用户的算力余额
    const creditBalance = await CreditBalance.findOne({ user: userId }).lean();
    
    // 将算力余额添加到用户数据中
    const userWithCredits = {
      ...user,
      creditBalance: creditBalance || { 
        balance: 0, 
        totalRecharged: 0, 
        totalConsumed: 0 
      }
    };
    
    res.json(userWithCredits);
  } catch (error) {
    next(createError(500, '获取用户信息失败', error));
  }
};

/**
 * 创建用户
 */
exports.createUser = async (req, res) => {
  try {
    const { username, email, password, role, phone, name } = req.body;
    
    // 检查用户名或邮箱是否已存在
    const existingUser = await User.findOne({ 
      $or: [
        { username }, 
        { phone: phone || null }
      ] 
    });
    
    if (existingUser) {
      return res.status(400).json({ message: '用户名、或手机号已存在' });
    }
    
    // 创建新用户
    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = new User({
      username,
      email,
      password: hashedPassword,
      role: role || 'user',
      phone,
      name: name || username,
      status: 'active',
      createdAt: new Date()
    });
    
    await newUser.save();
    res.status(201).json({ 
      message: '用户创建成功', 
      user: { ...newUser.toObject(), password: undefined } 
    });
  } catch (error) {
    res.status(500).json({ message: '创建用户失败', error: error.message });
  }
};

/**
 * 更新用户
 */
exports.updateUser = async (req, res) => {
  try {
    const { username, email, role, password, phone, name, status } = req.body;
    
    // 检查用户是否存在
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    // 检查用户名或邮箱是否已被其他用户使用
    if (username && username !== user.username) {
      const existingUsername = await User.findOne({ username });
      if (existingUsername) {
        return res.status(400).json({ message: '用户名已存在' });
      }
    }
    
    if (phone && phone !== user.phone) {
      const existingPhone = await User.findOne({ phone });
      if (existingPhone) {
        return res.status(400).json({ message: '手机号已存在' });
      }
    }
    
    // 更新用户信息
    const updateData = { 
      username, 
      email, 
      role,
      phone,
      name,
      status
    };
    
    // 如果提供了新密码，则更新密码
    if (password) {
      updateData.password = await bcrypt.hash(password, 10);
    }
    
    const updatedUser = await User.findByIdAndUpdate(
      req.params.id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-password');
    
    res.json({ message: '用户更新成功', user: updatedUser });
  } catch (error) {
    res.status(500).json({ message: '更新用户失败', error: error.message });
  }
};

/**
 * 删除用户
 */
exports.deleteUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    // 防止删除自己
    if (user._id.toString() === req.user._id.toString()) {
      return res.status(400).json({ message: '不能删除当前登录的用户' });
    }
    
    await User.findByIdAndDelete(req.params.id);
    res.json({ message: '用户删除成功' });
  } catch (error) {
    res.status(500).json({ message: '删除用户失败', error: error.message });
  }
};

/**
 * 搜索用户
 */
exports.searchUsers = async (req, res, next) => {
  try {
    const { query, phone } = req.query;
    
    // 如果提供了手机号，优先通过手机号查询
    if (phone) {
      const users = await User.find({ phone: phone })
        .select('_id username name email phone')
        .limit(10);
      
      return res.json({ users });
    }
    
    if (!query || query.length < 2) {
      return res.json({ users: [] });
    }
    
    // 创建正则表达式进行模糊搜索
    const regex = new RegExp(query, 'i');
    
    // 查询条件：用户名、姓名、邮箱或手机号匹配
    const users = await User.find({
      $or: [
        { username: regex },
        { name: regex },
        { email: regex },
        { phone: regex }
      ]
    }).select('_id username name email phone').limit(10);
    
    res.json({ users });
  } catch (error) {
    next(createError(500, '搜索用户失败', error));
  }
};

/**
 * 获取用户设备列表
 */
exports.getUserDevices = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      throw createError(404, '用户不存在');
    }

    // 获取设备列表，按最后活动时间排序
    const devices = user.activeSessions
      .sort((a, b) => b.lastActive - a.lastActive)
      .map(session => ({
        token: session.token,
        deviceInfo: session.deviceInfo,
        lastActive: session.lastActive
      }));

    res.json({
      success: true,
      data: {
        devices,
        maxSessions: user.maxSessions
      }
    });
  } catch (error) {
    next(createError(500, '获取设备列表失败', error));
  }
};

/**
 * 踢出用户设备
 */
exports.kickDevice = async (req, res, next) => {
  try {
    const { token } = req.body;
    const user = await User.findById(req.params.id);
    
    if (!user) {
      throw createError(404, '用户不存在');
    }

    // 检查设备是否存在
    const deviceExists = user.activeSessions.some(session => session.token === token);
    if (!deviceExists) {
      throw createError(404, '设备不存在');
    }

    // 移除设备会话
    await user.removeSession(token);

    res.json({
      success: true,
      message: '设备已踢出'
    });
  } catch (error) {
    next(createError(500, '踢出设备失败', error));
  }
};

/**
 * 更新用户最大登录设备数
 */
exports.updateMaxSessions = async (req, res, next) => {
  try {
    const { maxSessions } = req.body;
    const user = await User.findById(req.params.id);
    
    if (!user) {
      throw createError(404, '用户不存在');
    }

    // 验证最大设备数
    if (maxSessions < 1 || maxSessions > 5) {
      throw createError(400, '最大登录设备数必须在1-5之间');
    }

    // 如果当前活跃会话数超过新的限制，删除多余的会话
    if (user.activeSessions.length > maxSessions) {
      // 按最后活动时间排序
      user.activeSessions.sort((a, b) => a.lastActive - b.lastActive);
      // 保留最新的会话
      user.activeSessions = user.activeSessions.slice(-maxSessions);
    }

    user.maxSessions = maxSessions;
    await user.updateOne({maxSessions: maxSessions,activeSessions: user.activeSessions});

    res.json({
      success: true,
      message: '最大登录设备数更新成功',
      data: {
        maxSessions: user.maxSessions,
        activeSessions: user.activeSessions.length
      }
    });
  } catch (error) {
    next(createError(500, '更新最大登录设备数失败', error));
  }
}; 

/**
 * 更新用户备注
 */
exports.updateRemark = async (req, res, next) => {
  try {
    const userId = req.params.id;
    const { remark } = req.body;
    if (typeof remark !== 'string') {
      return res.status(400).json({ message: 'remark字段必须为字符串' });
    }
    const user = await User.findByIdAndUpdate(userId, { remark }, { new: true });
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    res.json({ message: '备注已更新', remark: user.remark });
  } catch (error) {
    console.error('更新用户备注失败:', error);
    next(createError(500, '更新用户备注失败', error));
  }
}; 