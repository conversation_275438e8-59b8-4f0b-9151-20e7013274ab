import { message } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';

import {
  MdClose,
  MdOutlineHelpOutline
} from 'react-icons/md';
import '../../styles/buttons.css';
import '../../styles/close-buttons.css';
import './index.css';

import { MASK_DRAW_TIPS } from '../../config/guides/operation-tips';
import ImagePreviewModal from '../common/ImagePreviewModal';
import DrawTools from '../DrawTools';
import HistoryTools from '../HistoryTools';
import ImageZoomControl from '../ImageZoomControl';
import TipPopup from '../TipPopup';


/**
 * 蒙版绘制弹窗组件
 * 
 * @param {Object} props
 * @param {boolean} props.isOpen - 控制弹窗是否显示
 * @param {Object} props.panel - 当前图片面板数据
 * @param {Function} props.onClose - 关闭弹窗的回调函数
 * @param {Function} props.onSaveMask - 保存蒙版的回调函数，参数为蒙版图像数据
 * @param {string} props.pageType - 页面类型标识，用于日志和调试
 * @param {string} props.savePath - 蒙版保存路径，必须提供，否则无法保存蒙版
 */
const MaskDrawModal = ({ isOpen = false, panel, onClose, onSaveMask, pageType, savePath }) => {
  const canvasRef = useRef(null);
  const canvasContainerRef = useRef(null);
  const bufferCanvasRef = useRef(null); // 添加缓冲canvas引用
  const originalImageSizeRef = useRef(null); // 保存原始图片尺寸
  const actualDrawAreaRef = useRef(null); // 保存实际绘制区域
  const [isDrawing, setIsDrawing] = useState(false);
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;
  console.log('当前屏幕宽度:', window.innerWidth, '是否为移动端:', isMobile);
  const [brushSize, setBrushSize] = useState(isMobile ? 28 : 36);
  const [activeTool, setActiveTool] = useState('brush');
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isLoading, setIsLoading] = useState(true);
  const [scale, setScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const lastPosition = useRef({ x: 0, y: 0 });
  const dragStart = useRef({ x: 0, y: 0 });
  const [hasEdited, setHasEdited] = useState(false);
  const [canvasImage, setCanvasImage] = useState(null);
  const [isSpaceDown, setIsSpaceDown] = useState(false); // 空格键状态
  const previousCursor = useRef(''); // 存储临时切换前的光标样式
  const lastDrawPosition = useRef(null); // 存储上一次绘制的位置
  const [isTipVisible, setIsTipVisible] = useState(false);
  const [tipPosition, setTipPosition] = useState({ left: 0, top: 0 });
  const tipButtonRef = useRef(null);
  const mobileTipButtonRef = useRef(null);

  // 添加鼠标位置状态和笔刷指示器显示状态
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [showBrushCursor, setShowBrushCursor] = useState(true);

  // 存储上一次绘制位置和时间
  const lastPositionRef = useRef({ x: 0, y: 0, time: 0 });

  const [previewMask, setPreviewMask] = useState(false);

  // 多边形选区相关状态
  const [polygonPoints, setPolygonPoints] = useState([]); // 存储多边形的所有点
  const [isPolygonDrawing, setIsPolygonDrawing] = useState(false); // 是否正在绘制多边形
  const [previewPoint, setPreviewPoint] = useState(null); // 鼠标跟随的预览点
  const [polygonComplete, setPolygonComplete] = useState(false); // 多边形是否完成

  // 添加图片预览相关状态
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState('');
  const [previewImageAlt, setPreviewImageAlt] = useState('');

  // 缓存最后渲染的基础图像
  const cachedBaseImage = useRef(null);

  // 存储渲染帧请求ID
  const renderFrameRef = useRef(null);

  // 渲染蒙版预览
  const renderMaskPreview = useCallback(() => {
    if (!canvasRef.current || !bufferCanvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const bufferCanvas = bufferCanvasRef.current;

    // 获取绘制区域
    const drawArea = actualDrawAreaRef.current;
    if (!drawArea) return;

    // 检查当前历史状态是否有效
    if (historyIndex >= 0 && historyIndex < history.length) {
      // 清除当前画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 获取缓冲画布的像素数据
      const bufferCtx = bufferCanvas.getContext('2d');

      // 绘制黑白蒙版
      const imageData = bufferCtx.getImageData(0, 0, bufferCanvas.width, bufferCanvas.height);
      const data = imageData.data;

      // 创建黑白蒙版图像数据
      const maskImageData = new ImageData(bufferCanvas.width, bufferCanvas.height);
      const maskData = maskImageData.data;

      // 先将整个预览区域填充为黑色
      ctx.fillStyle = 'black';
      ctx.fillRect(drawArea.x, drawArea.y, drawArea.width, drawArea.height);

      // 将有笔迹的区域转为白色，其余区域为黑色
      for (let i = 0; i < data.length; i += 4) {
        if (data[i] > 0 || data[i + 1] > 0 || data[i + 2] > 0 || data[i + 3] > 0) {
          // 有颜色的区域设为白色
          maskData[i] = 255;     // 红
          maskData[i + 1] = 255;   // 绿
          maskData[i + 2] = 255;   // 蓝
          maskData[i + 3] = 255;   // 不透明
        } else {
          // 无颜色的区域设为黑色（完全不透明）
          maskData[i] = 0;       // 红
          maskData[i + 1] = 0;     // 绿
          maskData[i + 2] = 0;     // 蓝
          maskData[i + 3] = 255;   // 不透明
        }
      }

      // 保存上下文状态
      ctx.save();

      // 设置裁剪区域，确保只显示图片区域内的蒙版
      ctx.beginPath();
      ctx.rect(drawArea.x, drawArea.y, drawArea.width, drawArea.height);
      ctx.clip();

      // 将蒙版数据绘制到画布上
      ctx.putImageData(maskImageData, 0, 0);

      // 恢复上下文状态
      ctx.restore();
    }
  }, [history, historyIndex]);

  // 将缓冲canvas的内容以半透明方式渲染到主canvas
  const renderBufferToMain = useCallback(() => {
    if (!canvasRef.current || !bufferCanvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const bufferCanvas = bufferCanvasRef.current;

    // 如果处于蒙版预览模式，则渲染黑白蒙版
    if (previewMask) {
      renderMaskPreview();
      return;
    }

    // 清除当前画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 获取当前绘制区域
    const drawArea = actualDrawAreaRef.current;
    if (!drawArea || !canvasImage) return;

    // 计算当前缩放下的图片显示区域
    const currentScale = scale / 100;
    const scaledWidth = drawArea.width * currentScale;
    const scaledHeight = drawArea.height * currentScale;
    const scaledX = drawArea.x * currentScale + imagePosition.x;
    const scaledY = drawArea.y * currentScale + imagePosition.y;

    // 绘制原始图像
    ctx.drawImage(
      canvasImage,
      scaledX,
      scaledY,
      scaledWidth,
      scaledHeight
    );

    // 绘制蒙版（半透明）
    if (bufferCanvas) {
      ctx.save();
      ctx.globalAlpha = 0.5;

      // 设置裁剪区域，只在图片区域内显示蒙版
      ctx.beginPath();
      ctx.rect(scaledX, scaledY, scaledWidth, scaledHeight);
      ctx.clip();

      // 绘制缓冲画布内容，需要根据缩放和位移进行变换
      ctx.setTransform(
        currentScale, 0,
        0, currentScale,
        imagePosition.x,
        imagePosition.y
      );

      ctx.drawImage(bufferCanvas, 0, 0);

      ctx.restore();
    }

    // 绘制多边形选区的可视化
    if (activeTool === 'polygon' && (polygonPoints.length > 0 || previewPoint)) {
      ctx.save();

      // 设置裁剪区域，只在图片区域内显示
      ctx.beginPath();
      ctx.rect(scaledX, scaledY, scaledWidth, scaledHeight);
      ctx.clip();

      // 应用变换
      ctx.setTransform(
        currentScale, 0,
        0, currentScale,
        imagePosition.x,
        imagePosition.y
      );

      // 绘制连接线（先绘制线条，再绘制点，确保点在线条之上）
      if (polygonPoints.length > 1) {
        ctx.strokeStyle = '#ff3c6a';
        ctx.lineWidth = 2 / currentScale;
        ctx.beginPath();
        ctx.moveTo(polygonPoints[0].x, polygonPoints[0].y);

        for (let i = 1; i < polygonPoints.length; i++) {
          ctx.lineTo(polygonPoints[i].x, polygonPoints[i].y);
        }
        ctx.stroke();
      }

      // 绘制已确定的点（在线条之上）
      ctx.fillStyle = '#ff3c6a';
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 1 / currentScale;

      polygonPoints.forEach((point, index) => {
        // 绘制点的外圈（白色边框）
        ctx.beginPath();
        ctx.arc(point.x, point.y, 5 / currentScale, 0, Math.PI * 2);
        ctx.fillStyle = '#ff3c6a';
        ctx.fill();
        ctx.stroke();

        // 绘制点的内圈（更小的红色圆点）
        ctx.beginPath();
        ctx.arc(point.x, point.y, 3 / currentScale, 0, Math.PI * 2);
        ctx.fillStyle = '#ffffff';
        ctx.fill();
      });

      // 绘制预览线（从最后一个点到鼠标位置）
      if (polygonPoints.length > 0 && previewPoint) {
        ctx.strokeStyle = 'rgba(255, 60, 106, 0.5)';
        ctx.setLineDash([5 / currentScale, 5 / currentScale]);
        ctx.beginPath();
        ctx.moveTo(polygonPoints[polygonPoints.length - 1].x, polygonPoints[polygonPoints.length - 1].y);
        ctx.lineTo(previewPoint.x, previewPoint.y);
        ctx.stroke();
        ctx.setLineDash([]);

        // 如果有3个或更多点，绘制到第一个点的预览线
        if (polygonPoints.length >= 3) {
          ctx.strokeStyle = 'rgba(255, 60, 106, 0.3)';
          ctx.setLineDash([3 / currentScale, 3 / currentScale]);
          ctx.beginPath();
          ctx.moveTo(previewPoint.x, previewPoint.y);
          ctx.lineTo(polygonPoints[0].x, polygonPoints[0].y);
          ctx.stroke();
          ctx.setLineDash([]);
        }
      }

      // 高亮第一个点（当有3个或更多点时）
      if (polygonPoints.length >= 3) {
        const firstPoint = polygonPoints[0];

        // 绘制脉动效果的外圈
        const time = Date.now() / 1000;
        const pulseRadius = 8 + Math.sin(time * 4) * 2;

        ctx.strokeStyle = '#ff3c6a';
        ctx.fillStyle = 'rgba(255, 60, 106, 0.2)';
        ctx.lineWidth = 2 / currentScale;
        ctx.beginPath();
        ctx.arc(firstPoint.x, firstPoint.y, pulseRadius / currentScale, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();

        // 绘制内圈（更明显的标识）
        ctx.fillStyle = '#ff3c6a';
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2 / currentScale;
        ctx.beginPath();
        ctx.arc(firstPoint.x, firstPoint.y, 6 / currentScale, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
      }

      ctx.restore();
    }
  }, [scale, imagePosition, previewMask, canvasImage, activeTool, polygonPoints, previewPoint]);

  // 计算初始缩放比例的函数
  const calculateInitialScale = useCallback((img) => {
    // 直接返回100%，因为我们在initializeCanvas中处理适配
    return 100;
  }, []);

  // 获取事件坐标（支持鼠标和触摸事件）
  const getEventCoordinates = useCallback((e) => {
    const rect = canvasRef.current.getBoundingClientRect();
    let clientX, clientY;

    if (e.touches && e.touches.length > 0) {
      // 触摸事件
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      // 鼠标事件
      clientX = e.clientX;
      clientY = e.clientY;
    }

    return {
      x: clientX - rect.left,
      y: clientY - rect.top
    };
  }, []);

  // 画笔工具开始绘制
  const startDrawing = useCallback((e) => {
    if (isSpaceDown || (e.button !== undefined && e.button !== 0)) return;

    // 阻止默认行为，防止移动端滚动
    e.preventDefault();

    setIsDrawing(true);
    setShowBrushCursor(false);

    // 获取事件坐标
    const { x: canvasX, y: canvasY } = getEventCoordinates(e);

    // 获取当前绘制区域
    const drawArea = actualDrawAreaRef.current;
    if (!drawArea) return;

    // 计算当前缩放下的图片显示区域
    const currentScale = scale / 100;
    const scaledX = drawArea.x * currentScale + imagePosition.x;
    const scaledY = drawArea.y * currentScale + imagePosition.y;
    const scaledWidth = drawArea.width * currentScale;
    const scaledHeight = drawArea.height * currentScale;

    // 检查是否在图片区域内
    if (canvasX < scaledX || canvasX > scaledX + scaledWidth ||
      canvasY < scaledY || canvasY > scaledY + scaledHeight) {
      return;
    }

    // 计算在缓冲画布上的绘制坐标
    const drawX = (canvasX - imagePosition.x) / currentScale;
    const drawY = (canvasY - imagePosition.y) / currentScale;

    // 保存起始位置
    lastDrawPosition.current = { x: drawX, y: drawY };
    lastPositionRef.current = { x: drawX, y: drawY, time: Date.now() };

    const bufferCanvas = bufferCanvasRef.current;
    if (!bufferCanvas) return;

    const bufferCtx = bufferCanvas.getContext('2d');

    // 设置绘图模式和样式
    bufferCtx.globalCompositeOperation = activeTool === 'brush' ? 'source-over' : 'destination-out';
    bufferCtx.lineWidth = brushSize;
    bufferCtx.lineCap = 'round';
    bufferCtx.lineJoin = 'round';
    bufferCtx.strokeStyle = activeTool === 'brush' ? 'rgba(255, 60, 106, 0.5)' : '#fff';
    bufferCtx.fillStyle = activeTool === 'brush' ? 'rgba(255, 60, 106, 0.5)' : '#fff';

    // 绘制第一个点
    bufferCtx.beginPath();
    bufferCtx.arc(drawX, drawY, brushSize / 2, 0, Math.PI * 2);
    bufferCtx.fill();

    // 确保在绘制新内容时不会清除之前的蒙版
    renderBufferToMain();
    setHasEdited(true);
  }, [activeTool, brushSize, isSpaceDown, scale, imagePosition, renderBufferToMain, getEventCoordinates]);

  // 多边形选区点击处理
  const handlePolygonClick = useCallback((e) => {
    if (isSpaceDown || (e.button !== undefined && e.button !== 0)) return;

    e.preventDefault();

    // 获取事件坐标
    const { x: canvasX, y: canvasY } = getEventCoordinates(e);

    // 获取当前绘制区域
    const drawArea = actualDrawAreaRef.current;
    if (!drawArea) return;

    // 计算当前缩放下的图片显示区域
    const currentScale = scale / 100;
    const scaledX = drawArea.x * currentScale + imagePosition.x;
    const scaledY = drawArea.y * currentScale + imagePosition.y;
    const scaledWidth = drawArea.width * currentScale;
    const scaledHeight = drawArea.height * currentScale;

    // 检查是否在图片区域内
    if (canvasX < scaledX || canvasX > scaledX + scaledWidth ||
        canvasY < scaledY || canvasY > scaledY + scaledHeight) {
      return;
    }

    // 计算在缓冲画布上的坐标
    const drawX = (canvasX - imagePosition.x) / currentScale;
    const drawY = (canvasY - imagePosition.y) / currentScale;

    const newPoint = { x: drawX, y: drawY };

    // 检查是否点击了第一个点（完成多边形）
    if (polygonPoints.length >= 3) {
      const firstPoint = polygonPoints[0];
      const distance = Math.sqrt(
        Math.pow((drawX - firstPoint.x), 2) + Math.pow((drawY - firstPoint.y), 2)
      );

      // 如果点击距离第一个点很近（考虑缩放），完成多边形
      if (distance < 15 / currentScale) {
        completePolygon();
        return;
      }
    }

    // 检查是否点击了已有的点（避免重复添加）
    for (let i = 0; i < polygonPoints.length; i++) {
      const point = polygonPoints[i];
      const distance = Math.sqrt(
        Math.pow((drawX - point.x), 2) + Math.pow((drawY - point.y), 2)
      );

      // 如果点击距离已有点很近，不添加新点
      if (distance < 10 / currentScale) {
        return;
      }
    }

    // 添加新点
    setPolygonPoints(prev => [...prev, newPoint]);
    setIsPolygonDrawing(true);
    setHasEdited(true);
  }, [isSpaceDown, getEventCoordinates, scale, imagePosition, polygonPoints]);

  // 保存当前画布状态到历史记录
  const saveToHistory = useCallback(() => {
    if (!canvasRef.current || !bufferCanvasRef.current) return;

    // 保存的是缓冲区状态，而不是主画布
    const bufferCanvas = bufferCanvasRef.current;
    const bufferData = bufferCanvas.toDataURL('image/png');

    // 如果当前索引不是最后一个，则移除后面的历史记录
    const newHistory = history.slice(0, historyIndex + 1);

    // 只有当缓冲区有内容时才添加新历史
    const bufferCtx = bufferCanvas.getContext('2d');
    const imageData = bufferCtx.getImageData(0, 0, bufferCanvas.width, bufferCanvas.height);
    const hasContent = Array.from(imageData.data).some(pixel => pixel !== 0);

    if (hasContent || newHistory.length === 0) {
      if (newHistory.length === 0) {
        // 第一次保存，存储原始图像
        const canvas = canvasRef.current;
        newHistory.push(canvas.toDataURL('image/png'));
      }

      // 然后添加缓冲区状态
      newHistory.push(bufferData);

      // 更新历史记录和索引
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);

      // 标记为已编辑
      setHasEdited(true);

      console.log(`保存历史记录: 索引 ${newHistory.length - 1}, 总记录数: ${newHistory.length}`);
    }
  }, [history, historyIndex]);

  // 完成多边形选区
  const completePolygon = useCallback(() => {
    if (polygonPoints.length < 3) return;

    const bufferCanvas = bufferCanvasRef.current;
    if (!bufferCanvas) return;

    const bufferCtx = bufferCanvas.getContext('2d');

    // 设置绘图模式和样式
    bufferCtx.globalCompositeOperation = 'source-over';
    bufferCtx.fillStyle = 'rgba(255, 60, 106, 0.5)';

    // 使用更平滑的多边形绘制
    bufferCtx.beginPath();
    bufferCtx.moveTo(polygonPoints[0].x, polygonPoints[0].y);

    // 绘制平滑的多边形路径
    for (let i = 1; i < polygonPoints.length; i++) {
      const currentPoint = polygonPoints[i];
      const prevPoint = polygonPoints[i - 1];

      // 使用二次贝塞尔曲线来平滑连接（可选）
      // 这里先使用直线连接，保持精确性
      bufferCtx.lineTo(currentPoint.x, currentPoint.y);
    }

    // 闭合路径
    bufferCtx.closePath();

    // 设置抗锯齿
    bufferCtx.imageSmoothingEnabled = true;
    bufferCtx.imageSmoothingQuality = 'high';

    // 填充多边形
    bufferCtx.fill();

    // 清理多边形状态
    setPolygonPoints([]);
    setIsPolygonDrawing(false);
    setPreviewPoint(null);
    setPolygonComplete(false);

    // 渲染到主画布
    renderBufferToMain();

    // 保存到历史记录
    setTimeout(() => {
      saveToHistory();
    }, 0);

    // 显示完成提示
    message.success('多边形选区已完成！');
  }, [polygonPoints, renderBufferToMain, saveToHistory]);

  // 鼠标/触摸事件处理
  const handleMouseDown = useCallback((e) => {
    // 如果空格键按下或者是中键，进行拖动
    if (isSpaceDown || (e.button !== undefined && e.button === 1)) {
      e.preventDefault();
      setIsDragging(true);
      const { x: clientX, y: clientY } = getEventCoordinates(e);
      dragStart.current = {
        x: clientX - imagePosition.x,
        y: clientY - imagePosition.y
      };

      if (canvasRef.current) {
        canvasRef.current.style.cursor = 'grabbing';
      }

      if (canvasContainerRef.current) {
        canvasContainerRef.current.style.cursor = 'grabbing';
      }
    } else if (activeTool === 'polygon') {
      // 多边形选区工具
      handlePolygonClick(e);
    } else {
      // 开始绘制
      startDrawing(e);
    }
  }, [imagePosition, isSpaceDown, activeTool, startDrawing, handlePolygonClick, getEventCoordinates]);

  // 处理鼠标/触摸移动
  const handleMouseMove = useCallback((e) => {
    // 阻止默认行为，防止移动端滚动
    e.preventDefault();

    // 移动端性能优化：使用requestAnimationFrame节流
    const isMobile = 'ontouchstart' in window;
    if (isMobile && !isDragging) {
      // 移动端非拖动状态下，减少事件处理频率
      if (handleMouseMove.throttleTimer) {
        return;
      }
      handleMouseMove.throttleTimer = requestAnimationFrame(() => {
        handleMouseMove.throttleTimer = null;
      });
    }

    // 更新鼠标位置（用于笔刷指示器）
    if (canvasRef.current) {
      const canvas = canvasRef.current;

      // 获取事件坐标
      const { x: canvasX, y: canvasY } = getEventCoordinates(e);

      // 获取实际绘制区域
      const drawArea = actualDrawAreaRef.current;
      if (!drawArea) return;

      // 计算当前缩放下的图片显示区域
      const currentScale = scale / 100;
      const scaledX = drawArea.x * currentScale + imagePosition.x;
      const scaledY = drawArea.y * currentScale + imagePosition.y;
      const scaledWidth = drawArea.width * currentScale;
      const scaledHeight = drawArea.height * currentScale;

      // 更新鼠标位置
      setMousePosition({
        x: canvasX,
        y: canvasY
      });

      // 检查是否在图片区域内
      const isInDrawArea =
        canvasX >= scaledX &&
        canvasX <= scaledX + scaledWidth &&
        canvasY >= scaledY &&
        canvasY <= scaledY + scaledHeight;

      // 多边形选区模式下的预览点更新
      if (activeTool === 'polygon' && isPolygonDrawing && isInDrawArea) {
        const drawX = (canvasX - imagePosition.x) / currentScale;
        const drawY = (canvasY - imagePosition.y) / currentScale;
        setPreviewPoint({ x: drawX, y: drawY });
      }

      // 确保显示笔刷指示器，但仅在图片区域内
      if (!isSpaceDown && isInDrawArea && activeTool !== 'polygon') {
        setShowBrushCursor(true);
      } else {
        setShowBrushCursor(false);
      }
    }

    // 处理拖动
    if (isDragging) {
      const { x: clientX, y: clientY } = getEventCoordinates(e);
      const newX = clientX - dragStart.current.x;
      const newY = clientY - dragStart.current.y;

      requestAnimationFrame(() => {
        setImagePosition({ x: newX, y: newY });
      });
    }
  }, [isDragging, isSpaceDown, scale, imagePosition, activeTool, isPolygonDrawing, getEventCoordinates]);

  // 结束绘制
  const handleEndDrawing = useCallback((e) => {
    if (isDrawing) {
      setIsDrawing(false);
      lastDrawPosition.current = null;

      // 取消所有待处理的渲染请求
      if (renderFrameRef.current) {
        cancelAnimationFrame(renderFrameRef.current);
        renderFrameRef.current = null;
      }

      // 确保进行最后一次渲染
      renderBufferToMain();

      // 保存当前蒙版状态为图像
      if (canvasRef.current && bufferCanvasRef.current) {
        // 保存当前状态到历史记录
        setTimeout(() => {
          saveToHistory();
        }, 0);
      }
    }
  }, [isDrawing, saveToHistory, renderBufferToMain]);

  // 处理鼠标/触摸抬起
  const handleMouseUp = useCallback((e) => {
    // 阻止默认行为
    e.preventDefault();

    if (isDragging) {
      setIsDragging(false);
      lastPosition.current = imagePosition;
      if (canvasRef.current) {
        canvasRef.current.style.cursor = activeTool === 'brush' ? 'crosshair' : 'eraser';
      }
    }

    // 结束绘制
    handleEndDrawing(e);
  }, [isDragging, imagePosition, activeTool, handleEndDrawing]);

  // 处理双击事件（完成多边形选区）
  const handleDoubleClick = useCallback((e) => {
    if (activeTool === 'polygon' && polygonPoints.length >= 3) {
      e.preventDefault();
      completePolygon();
    }
  }, [activeTool, polygonPoints, completePolygon]);

  // 处理鼠标/触摸离开
  const handleMouseLeave = useCallback((e) => {
    if (isDragging) {
      setIsDragging(false);
    }

    // 隐藏笔刷指示器
    setShowBrushCursor(false);

    // 如果正在绘制，结束绘制
    if (isDrawing) {
      handleEndDrawing(e);
    }
  }, [isDragging, isDrawing, handleEndDrawing]);

  // 处理鼠标/触摸进入画布
  const handleCanvasMouseEnter = useCallback((e) => {
    if (!isSpaceDown) {
      // 检查鼠标位置是否在图片区域内
      const canvas = canvasRef.current;
      if (canvas) {
        const { x: canvasX, y: canvasY } = getEventCoordinates(e);

        // 获取图片绘制区域
        const drawArea = actualDrawAreaRef.current;

        if (drawArea) {
          // 计算当前缩放下的图片显示区域
          const currentScale = scale / 100;
          const scaledX = drawArea.x * currentScale + imagePosition.x;
          const scaledY = drawArea.y * currentScale + imagePosition.y;
          const scaledWidth = drawArea.width * currentScale;
          const scaledHeight = drawArea.height * currentScale;

          // 检查是否在图片区域内
          const isInDrawArea =
            canvasX >= scaledX &&
            canvasX <= scaledX + scaledWidth &&
            canvasY >= scaledY &&
            canvasY <= scaledY + scaledHeight;

          // 只在图片区域内显示笔刷指示器
          if (isInDrawArea) {
            setShowBrushCursor(true);
          }
        }
      }
    }
  }, [isSpaceDown, scale, imagePosition, getEventCoordinates]);

  // 处理空格键按下状态变化
  useEffect(() => {
    if (isSpaceDown) {
      setShowBrushCursor(false); // 拖动模式下隐藏笔刷指示器
      if (canvasContainerRef.current) {
        canvasContainerRef.current.style.cursor = 'grab';
      }
    } else {
      if (canvasContainerRef.current) {
        canvasContainerRef.current.style.cursor = 'none'; // 使用自定义笔刷指示器时隐藏默认鼠标
      }
      if (canvasRef.current && document.activeElement === document.body) {
        // 多边形工具不显示笔刷指示器
        if (activeTool !== 'polygon') {
          setShowBrushCursor(true);
        }
      }
    }
  }, [isSpaceDown, activeTool]);

  // 处理工具变化
  useEffect(() => {
    // 当工具变化时，更新鼠标样式
    if (canvasContainerRef.current) {
      const container = canvasContainerRef.current;

      if (isSpaceDown) {
        container.style.cursor = 'grab';
      } else if (activeTool === 'polygon') {
        container.style.cursor = 'crosshair'; // 多边形工具使用十字光标
      } else {
        container.style.cursor = 'none'; // 使用自定义笔刷指示器时隐藏默认鼠标
      }
    }
  }, [activeTool, isSpaceDown]);

  // 初始化画布
  useEffect(() => {
    if (!panel || !canvasRef.current || !canvasContainerRef.current) return;

    // 检查并获取图片URL
    let imageUrl = '';
    if (panel.processedFile) {
      imageUrl = panel.processedFile;
    } else if (panel.url) {
      imageUrl = panel.url;
    } else if (panel.originalImage) {
      imageUrl = panel.originalImage;
    } else {
      console.error('MaskDrawModal - 找不到有效的图片URL');
      return;
    }

    console.log('MaskDrawModal - 图片URL:', imageUrl);
    setIsLoading(true);
    setHistory([]);
    setHistoryIndex(-1);

    // 重置蒙版相关状态
    window.hasClearedMask = false;
    setHasEdited(false);

    // 加载图片
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      // console.log('MaskDrawModal - 图片加载成功:', { width: img.width, height: img.height });

      // 计算初始缩放比例
      const initialScaleValue = calculateInitialScale(img);

      // 保存图片对象以备绘制使用
      setCanvasImage(img);

      // 设置初始缩放比例
      setTimeout(() => {
        console.log('MaskDrawModal - 设置初始缩放比例:', initialScaleValue);
        setScale(initialScaleValue);
        setInitialScale(initialScaleValue);

        // 创建初始历史记录
        initializeCanvas(img, initialScaleValue);



        // 检查并加载已有的蒙版数据，仅当真正存在蒙版数据时
        if (panel && (panel.maskData || panel.maskPath)) {
          console.log('检测到已有蒙版数据，准备加载');
          loadExistingMask();
        } else {
          console.log('未检测到蒙版数据，跳过加载过程');
        }
      }, 0);
    };

    img.onerror = (e) => {
      console.error('MaskDrawModal - 图片加载失败:', e);
      setIsLoading(false);
    };

    img.src = imageUrl;

    // 添加超时检测
    // const timeout = setTimeout(() => {
    //   if (isLoading) {
    //     console.error('MaskDrawModal - 图片加载超时');
    //     setIsLoading(false);
    //   }
    // }, 5000);
    setIsLoading(false);

    // return () => clearTimeout(timeout);
  }, [panel, calculateInitialScale]);

  // 初始化Canvas和历史记录
  const initializeCanvas = useCallback((img, initialScale) => {
    if (!canvasRef.current || !img) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // 保存图片的原始尺寸
    const originalWidth = img.width;
    const originalHeight = img.height;

    // 保存图片的原始尺寸到ref中，以便后续使用
    originalImageSizeRef.current = { width: originalWidth, height: originalHeight };
    console.log('原始图片尺寸:', originalWidth, 'x', originalHeight);

    // 设置Canvas尺寸为容器尺寸
    const container = canvasContainerRef.current;
    canvas.width = container.clientWidth;
    canvas.height = container.clientHeight;

    // 初始化缓冲canvas - 使用与容器相同的尺寸
    if (!bufferCanvasRef.current) {
      bufferCanvasRef.current = document.createElement('canvas');
    }
    const bufferCanvas = bufferCanvasRef.current;
    bufferCanvas.width = canvas.width;
    bufferCanvas.height = canvas.height;
    const bufferCtx = bufferCanvas.getContext('2d');
    bufferCtx.clearRect(0, 0, bufferCanvas.width, bufferCanvas.height);

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 直接计算图片适配容器的尺寸，不使用 initialScale
    const containerWidth = canvas.width;
    const containerHeight = canvas.height;
    const imageRatio = originalWidth / originalHeight;
    const containerRatio = containerWidth / containerHeight;

    let displayWidth, displayHeight;

    if (imageRatio > containerRatio) {
      // 图片更宽，以容器宽度为准
      displayWidth = containerWidth;
      displayHeight = containerWidth / imageRatio;
    } else {
      // 图片更高，以容器高度为准
      displayHeight = containerHeight;
      displayWidth = containerHeight * imageRatio;
    }

    // 计算居中位置
    const drawX = (containerWidth - displayWidth) / 2;
    const drawY = (containerHeight - displayHeight) / 2;

    // 保存实际绘制区域的信息，以便后续裁剪使用
    actualDrawAreaRef.current = {
      x: drawX,
      y: drawY,
      width: displayWidth,
      height: displayHeight
    };
    console.log('实际绘制区域:', drawX, drawY, displayWidth, displayHeight);
    console.log('容器尺寸:', containerWidth, 'x', containerHeight);

    // 绘制图片
    ctx.save();
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(img, drawX, drawY, displayWidth, displayHeight);
    ctx.restore();

    // 保存初始状态到历史记录
    const imageData = canvas.toDataURL('image/png');
    setHistory([imageData]);
    setHistoryIndex(0);

    // 重置编辑状态
    setHasEdited(false);
  }, []);

  // 加载已存在的蒙版数据
  const loadExistingMask = useCallback(() => {
    // 严格检查panel中是否有有效的蒙版数据
    if (!panel || !panel.maskPath || !bufferCanvasRef.current) {
      console.log('未检测到有效的蒙版数据，跳过加载');
      return;
    }
    const maskUrl = panel.maskPath;


    console.log('检测到已有蒙版数据，正在加载...');

    try {
      // 创建临时图像对象加载蒙版数据
      const maskImage = new Image();
      maskImage.crossOrigin = 'anonymous';

      maskImage.onload = () => {
        // 获取绘制区域和缓冲画布
        const drawArea = actualDrawAreaRef.current;
        const bufferCanvas = bufferCanvasRef.current;
        if (!drawArea || !bufferCanvas) return;

        const bufferCtx = bufferCanvas.getContext('2d');

        // 清除缓冲画布
        bufferCtx.clearRect(0, 0, bufferCanvas.width, bufferCanvas.height);

        // 创建临时画布用于处理蒙版
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = bufferCanvas.width;
        tempCanvas.height = bufferCanvas.height;
        const tempCtx = tempCanvas.getContext('2d');

        // 在临时画布上绘制黑色背景
        tempCtx.fillStyle = 'black';
        tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

        // 绘制蒙版到临时画布
        tempCtx.drawImage(
          maskImage,
          0, 0, maskImage.width, maskImage.height,
          drawArea.x, drawArea.y, drawArea.width, drawArea.height
        );

        // 获取蒙版像素数据
        const maskData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
        const pixels = maskData.data;

        // 创建用于缓冲画布的ImageData
        const bufferImageData = bufferCtx.createImageData(bufferCanvas.width, bufferCanvas.height);
        const bufferPixels = bufferImageData.data;

        // 将黑白蒙版转换为缓冲画布格式
        for (let i = 0; i < pixels.length; i += 4) {
          // 白色区域（或接近白色的区域）表示绘制过的区域
          if (pixels[i] > 200 && pixels[i + 1] > 200 && pixels[i + 2] > 200) {
            // 设置为白色（表示绘制区域）
            bufferPixels[i] = 255;    // R
            bufferPixels[i + 1] = 255;  // G
            bufferPixels[i + 2] = 255;  // B
            bufferPixels[i + 3] = 255;  // A
          } else {
            // 保持透明
            bufferPixels[i] = 0;
            bufferPixels[i + 1] = 0;
            bufferPixels[i + 2] = 0;
            bufferPixels[i + 3] = 0;
          }
        }

        // 将处理后的图像数据应用到缓冲画布
        bufferCtx.putImageData(bufferImageData, 0, 0);


        // 重置历史记录并保存初始状态
        let newHistory = [];

        // 第一帧保存画布原图
        if (canvasRef.current && !history.length) {
          // 确保原始图像保存到历史记录第一帧
          const canvas = canvasRef.current;
          const originalImageData = canvas.toDataURL('image/png');
          newHistory.push(originalImageData);
          console.log('保存原始图像到历史记录 (index 0)');
        } else if (history.length > 0) {
          // 已有历史记录，保留第一帧
          newHistory.push(history[0]);
        }

        // 第二帧保存加载的蒙版状态
        const bufferData = bufferCanvas.toDataURL('image/png');
        newHistory.push(bufferData);

        // 更新历史记录
        setHistory(newHistory);
        setHistoryIndex(1); // 设置为蒙版状态
        // 渲染缓冲画布到主画布
        // 加载完成
        doBug(newHistory);

        console.log('初始化历史记录完成: 共', newHistory.length, '帧, 当前索引:', 1);

        // 标记为已编辑状态，以便保存
        setHasEdited(true);

        // 添加多次渲染的尝试，确保蒙版被正确显示
        // setTimeout(() => {
        //   renderBufferToMain(); // 再次渲染，确保状态更新后的显示
        // }, 16);

        // setTimeout(() => {
        //   renderBufferToMain(); // 第三次渲染尝试
        // }, 50);

        console.log('已成功加载蒙版数据');
      };

      maskImage.onerror = (error) => {
        console.error('加载蒙版图像失败:', error);
      };
      maskImage.src = maskUrl;
    } catch (error) {
      console.error('加载已有蒙版数据时出错:', error);
    }
  }, [panel, renderBufferToMain, history, setHasEdited]);

  // 绘制相关函数
  const handleToolChange = (tool) => {
    // 如果从多边形工具切换到其他工具，清理多边形状态
    if (activeTool === 'polygon' && tool !== 'polygon') {
      setPolygonPoints([]);
      setIsPolygonDrawing(false);
      setPreviewPoint(null);
      setPolygonComplete(false);
    }

    setActiveTool(tool);
    // 不设置光标样式，使用自定义笔刷指示器
  };

  const handleBrushSizeChange = (size) => {
    setBrushSize(size);
  };

  // 在两点之间进行插值，创建平滑的线条
  const drawSmoothLine = (ctx, startX, startY, endX, endY, brushSize, isBrush) => {
    // 获取绘制区域
    const drawArea = actualDrawAreaRef.current;
    if (!drawArea) return;

    ctx.save();

    // 设置裁剪区域，仅允许在图片区域内绘制
    ctx.beginPath();
    ctx.rect(drawArea.x, drawArea.y, drawArea.width, drawArea.height);
    ctx.clip();

    // 应用绘制样式
    if (isBrush) {
      // 设置为刷子模式，使用品牌主题色（半透明）
      ctx.globalCompositeOperation = 'source-over';
      ctx.fillStyle = 'rgba(255, 60, 106, 0.5)';
      ctx.strokeStyle = 'rgba(255, 60, 106, 0.5)';
    } else {
      // 设置为橡皮擦模式
      ctx.globalCompositeOperation = 'destination-out';
      ctx.fillStyle = 'rgba(0, 0, 0, 1)';
      ctx.strokeStyle = 'rgba(0, 0, 0, 1)';
    }

    // 设置线条样式
    ctx.lineWidth = brushSize;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // 计算两点之间的距离
    const dx = endX - startX;
    const dy = endY - startY;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // 对短距离直接绘制点
    if (distance < 1) {
      ctx.beginPath();
      ctx.arc(startX, startY, brushSize / 2, 0, Math.PI * 2);
      ctx.fill();
      ctx.restore();
      return;
    }

    // 构建平滑的路径
    ctx.beginPath();

    // 保证起点可见
    ctx.arc(startX, startY, brushSize / 2, 0, Math.PI * 2);
    ctx.fill();

    // 对于长距离，使用线条连接 + 中间插点
    // 控制点数量 - 距离越长，控制点越多
    const segments = Math.ceil(distance / (brushSize * 0.25));
    const segmentLength = distance / segments;

    // 起点
    ctx.beginPath();
    ctx.moveTo(startX, startY);

    // 为长距离使用贝塞尔曲线
    if (distance > brushSize * 2) {
      // 使用二次贝塞尔曲线连接多个点
      for (let i = 1; i <= segments; i++) {
        const ratio = i / segments;
        const x = startX + dx * ratio;
        const y = startY + dy * ratio;
        ctx.lineTo(x, y);
      }
    } else {
      // 中等距离直接使用线条
      ctx.lineTo(endX, endY);
    }

    // 绘制路径
    ctx.stroke();

    // 确保终点可见
    ctx.beginPath();
    ctx.arc(endX, endY, brushSize / 2, 0, Math.PI * 2);
    ctx.fill();

    ctx.restore();
  };

  // 绘制过程
  const handleDraw = useCallback((e) => {
    if (!isDrawing || !canvasRef.current) return;

    // 阻止默认行为，防止移动端滚动
    e.preventDefault();

    // 获取事件坐标
    const { x: canvasX, y: canvasY } = getEventCoordinates(e);

    // 获取当前绘制区域
    const drawArea = actualDrawAreaRef.current;
    if (!drawArea) return;

    // 计算当前缩放下的图片显示区域
    const currentScale = scale / 100;
    const scaledX = drawArea.x * currentScale + imagePosition.x;
    const scaledY = drawArea.y * currentScale + imagePosition.y;
    const scaledWidth = drawArea.width * currentScale;
    const scaledHeight = drawArea.height * currentScale;

    // 检查是否在图片区域内
    if (canvasX < scaledX || canvasX > scaledX + scaledWidth ||
      canvasY < scaledY || canvasY > scaledY + scaledHeight) {
      return;
    }

    // 计算在缓冲画布上的绘制坐标
    const drawX = (canvasX - imagePosition.x) / currentScale;
    const drawY = (canvasY - imagePosition.y) / currentScale;

    // 更新笔刷指示器位置（使用屏幕坐标）
    setMousePosition({
      x: canvasX,
      y: canvasY
    });

    const bufferCanvas = bufferCanvasRef.current;
    if (!bufferCanvas) return;

    const bufferCtx = bufferCanvas.getContext('2d');

    // 计算时间差和距离
    const now = Date.now();
    const lastPos = lastDrawPosition.current || { x: drawX, y: drawY };
    const lastTime = lastPositionRef.current.time || now;
    const dt = now - lastTime;
    const dx = drawX - lastPos.x;
    const dy = drawY - lastPos.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // 移动端性能优化：减少绘制频率
    const isMobile = 'ontouchstart' in window;
    const minDistance = isMobile ? 2 : 1; // 移动端增加最小距离阈值

    // 绘制线条
    if (distance > minDistance) {
      // 保持之前的绘制内容
      bufferCtx.globalCompositeOperation = activeTool === 'brush' ? 'source-over' : 'destination-out';
      bufferCtx.lineWidth = brushSize;
      bufferCtx.lineCap = 'round';
      bufferCtx.lineJoin = 'round';
      bufferCtx.strokeStyle = activeTool === 'brush' ? 'rgba(255, 60, 106, 0.5)' : '#fff';
      bufferCtx.fillStyle = activeTool === 'brush' ? 'rgba(255, 60, 106, 0.5)' : '#fff';

      bufferCtx.beginPath();
      bufferCtx.moveTo(lastPos.x, lastPos.y);
      bufferCtx.lineTo(drawX, drawY);
      bufferCtx.stroke();
    }

    // 更新最后位置和时间
    lastDrawPosition.current = { x: drawX, y: drawY };
    lastPositionRef.current = { x: drawX, y: drawY, time: now };

    // 确保在绘制过程中保持之前的蒙版内容
    renderBufferToMain();
  }, [isDrawing, scale, imagePosition, activeTool, brushSize, renderBufferToMain, getEventCoordinates]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current && canvasContainerRef.current && canvasImage) {
        const container = canvasContainerRef.current;
        const canvas = canvasRef.current;

        // 保存当前画布内容
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = canvas.width;
        tempCanvas.height = canvas.height;
        const tempCtx = tempCanvas.getContext('2d');
        tempCtx.drawImage(canvas, 0, 0);

        // 更新Canvas尺寸
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;

        // 同时更新缓冲canvas尺寸
        if (bufferCanvasRef.current) {
          bufferCanvasRef.current.width = canvas.width;
          bufferCanvasRef.current.height = canvas.height;
        }

        // 重新计算缩放比例
        const initialScaleValue = calculateInitialScale(canvasImage);
        setInitialScale(initialScaleValue);
        setScale(initialScaleValue);

        // 重新绘制内容
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(tempCanvas, 0, 0, canvas.width, canvas.height);
      }
    };

    window.addEventListener('resize', handleResize);

    // 初始化时调整一次
    if (isOpen && canvasContainerRef.current) {
      setTimeout(handleResize, 300);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen, canvasImage, calculateInitialScale]);

  // 撤销操作
  const handleUndo = () => {
    if (historyIndex > 0) {
      loadHistoryState(historyIndex - 1);
    }
  };

  // 重做操作
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      loadHistoryState(historyIndex + 1);
    }
  };

  const doBug = (newHistory, index = 0, img = null) => {
    console.log(newHistory.length);
    if (!canvasRef.current || !bufferCanvasRef.current || index < 0 || index >= newHistory.length) return;

    // 获取画布和缓冲区
    const canvas = canvasRef.current;
    const bufferCanvas = bufferCanvasRef.current;
    // 更新历史索引
    setHistoryIndex(index);

    if (index === 0) {
      // 如果是首帧（原始图像），清空缓冲区
      const bufferCtx = bufferCanvas.getContext('2d');
      bufferCtx.clearRect(0, 0, bufferCanvas.width, bufferCanvas.height);

      // 直接在主画布上绘制原始图像
      const baseImage = new Image();
      baseImage.onload = () => {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(baseImage, 0, 0);
        doBug(newHistory, 1, baseImage)
      };
      baseImage.src = newHistory[0];
    } else {
      // 非首帧，恢复缓冲区状态
      const bufferImage = new Image();
      bufferImage.onload = () => {
        const bufferCtx = bufferCanvas.getContext('2d');
        bufferCtx.clearRect(0, 0, bufferCanvas.width, bufferCanvas.height);
        bufferCtx.drawImage(bufferImage, 0, 0);
        setIsLoading(false);
      };
      bufferImage.src = newHistory[index];
    }

  }

  // 加载指定的历史状态
  const loadHistoryState = (index) => {
    if (!canvasRef.current || !bufferCanvasRef.current || index < 0 || index >= history.length) return;

    // 获取画布和缓冲区
    const canvas = canvasRef.current;
    const bufferCanvas = bufferCanvasRef.current;

    // 更新历史索引
    setHistoryIndex(index);

    if (index === 0) {
      // 如果是首帧（原始图像），清空缓冲区
      const bufferCtx = bufferCanvas.getContext('2d');
      bufferCtx.clearRect(0, 0, bufferCanvas.width, bufferCanvas.height);

      // 直接在主画布上绘制原始图像
      const baseImage = new Image();
      baseImage.onload = () => {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(baseImage, 0, 0);
      };
      baseImage.src = history[0];
    } else {
      // 非首帧，恢复缓冲区状态
      const bufferImage = new Image();
      bufferImage.onload = () => {
        const bufferCtx = bufferCanvas.getContext('2d');
        bufferCtx.clearRect(0, 0, bufferCanvas.width, bufferCanvas.height);
        bufferCtx.drawImage(bufferImage, 0, 0);

        // 然后重新渲染
        renderBufferToMain();
      };
      bufferImage.src = history[index];
    }
  };

  // 清空绘制内容
  const handleClear = () => {
    if (!canvasRef.current || !canvasImage) return;

    console.log('开始清空蒙版内容...');

    // 检查蒙版是否已被删除
    if (!panel.maskData && !hasEdited) {
      console.log('蒙版已被删除，无需清空');
      // 设置标记，表示用户想要删除蒙版
      window.hasClearedMask = true;
      return;
    }

    // 清理多边形选区状态
    if (activeTool === 'polygon') {
      setPolygonPoints([]);
      setIsPolygonDrawing(false);
      setPreviewPoint(null);
      setPolygonComplete(false);
    }

    // 1. 清除缓冲canvas
    if (bufferCanvasRef.current) {
      const bufferCtx = bufferCanvasRef.current.getContext('2d');
      // 确保完全清除缓冲区
      bufferCtx.globalCompositeOperation = 'source-over';
      bufferCtx.clearRect(0, 0, bufferCanvasRef.current.width, bufferCanvasRef.current.height);
    }

    // 2. 如果有主画布，直接清空并重绘原始图像
    if (canvasRef.current && canvasImage) {
      const ctx = canvasRef.current.getContext('2d');
      const canvas = canvasRef.current;

      // 清除画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 获取绘制区域
      const drawArea = actualDrawAreaRef.current;
      if (drawArea) {
        // 重新绘制原始图像
        ctx.drawImage(canvasImage, drawArea.x, drawArea.y, drawArea.width, drawArea.height);
      }
    }

    // 3. 设置标记为已清空
    window.hasClearedMask = true;

    // 4. 更新历史记录
    saveToHistory();
    setHistoryIndex(1);
    setHasEdited(true);

    console.log('蒙版内容已成功清空');
  };

  // 保存蒙版
  const handleSave = () => {
    if (!canvasRef.current || !bufferCanvasRef.current) return;

    try {
      // 获取原始图片和绘制区域的尺寸信息
      const originalSize = originalImageSizeRef.current;
      const drawArea = actualDrawAreaRef.current;

      if (!originalSize || !drawArea) {
        console.error('保存蒙版失败: 缺少图片尺寸信息');
        onClose();
        return;
      }

      console.log('获取到的图片信息:', originalSize, drawArea);

      // 创建一个临时canvas，用于生成与图片尺寸完全一致的蒙版
      const tempCanvas = document.createElement('canvas');
      const bufferCanvas = bufferCanvasRef.current;

      // 检查缓冲画布是否为空（用户是否清空了全部内容）
      const bufferCtx = bufferCanvas.getContext('2d');
      const bufferData = bufferCtx.getImageData(0, 0, bufferCanvas.width, bufferCanvas.height);
      const bufferPixels = bufferData.data;

      // 检查是否所有像素都是透明的（即画布为空）
      let totalPixels = 0;
      let nonEmptyPixels = 0;

      for (let i = 0; i < bufferPixels.length; i += 4) {
        totalPixels++;
        if (bufferPixels[i] > 0 || bufferPixels[i + 1] > 0 || bufferPixels[i + 2] > 0 || bufferPixels[i + 3] > 0) {
          nonEmptyPixels++;
        }
      }

      // 如果有效像素占比低于0.01%，视为空画布
      const isEmpty = nonEmptyPixels / totalPixels < 0.0001;

      console.log(`画布分析：总像素=${totalPixels}, 非空像素=${nonEmptyPixels}, 占比=${(nonEmptyPixels / totalPixels * 100).toFixed(4)}%, 是否为空=${isEmpty}`);

      // 如果有特殊标记表示用户执行了清空操作，或者画布为空，则视为删除蒙版
      // if (isEmpty || window.hasClearedMask === true) {
      //   console.log('检测到空白画布或清空操作，用户想要删除蒙版');

      //   // 清除标记
      //   window.hasClearedMask = false;

      //   // 标记为未编辑状态，防止关闭时再次提示
      //   setHasEdited(false);

      //   // 使用统一标准方案，仅使用componentId
      //   const panelId = panel.componentId;

      //   // 调用回调函数，传递null表示删除蒙版
      //   onSaveMask(null, panelId, null);
      //   onClose();
      //   return;
      // }

      // 设置临时canvas的尺寸为原始图片的尺寸
      tempCanvas.width = originalSize.width;
      tempCanvas.height = originalSize.height;
      const tempCtx = tempCanvas.getContext('2d');

      // 创建一个中间canvas，用于处理缓冲画布内容
      const processCanvas = document.createElement('canvas');
      processCanvas.width = bufferCanvas.width;
      processCanvas.height = bufferCanvas.height;
      const processCtx = processCanvas.getContext('2d');

      // 将缓冲数据转换为黑白蒙版
      const processData = processCtx.createImageData(bufferCanvas.width, bufferCanvas.height);
      const processPixels = processData.data;

      // 将有笔迹的区域转为白色，其余区域为黑色
      for (let i = 0; i < bufferPixels.length; i += 4) {
        if (bufferPixels[i] > 0 || bufferPixels[i + 1] > 0 || bufferPixels[i + 2] > 0 || bufferPixels[i + 3] > 0) {
          // 有颜色的区域设为白色
          processPixels[i] = 255;     // 红
          processPixels[i + 1] = 255;   // 绿
          processPixels[i + 2] = 255;   // 蓝
          processPixels[i + 3] = 255;   // 不透明
        } else {
          // 无颜色的区域设为黑色（重要：alpha值为255，表示不透明的黑色）
          processPixels[i] = 0;       // 红
          processPixels[i + 1] = 0;     // 绿
          processPixels[i + 2] = 0;     // 蓝
          processPixels[i + 3] = 255;   // 不透明黑色（而非透明）
        }
      }

      // 填充整个临时画布为黑色背景
      tempCtx.fillStyle = 'black';
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

      // 将处理后的像素数据绘制到中间canvas上
      processCtx.putImageData(processData, 0, 0);

      // 从中间canvas的绘制区域裁剪并缩放到最终与原图匹配的尺寸
      tempCtx.drawImage(
        processCanvas,
        drawArea.x, drawArea.y, drawArea.width, drawArea.height, // 源矩形 - 只获取图片区域
        0, 0, originalSize.width, originalSize.height            // 目标矩形 - 缩放到原始图片尺寸
      );

      // 生成PNG格式的蒙版图像数据URL
      const maskDataUrl = tempCanvas.toDataURL('image/png');

      console.log('保存单色通道蒙版图像，尺寸:', originalSize.width, 'x', originalSize.height);

      // 标记为未编辑状态，防止关闭时再次提示
      setHasEdited(false);

      // 使用传入的savePath参数，必须显式提供
      const finalPath = savePath;

      console.log(`使用的保存路径: ${finalPath}，面板类型: ${panel.type || '未知'}`);

      // 确保提供了有效的保存路径
      if (!finalPath) {
        console.error('未提供有效的保存路径(savePath)');
        message.error('保存失败：未指定保存路径');
        onClose();
        return;
      }

      // 使用统一标准方案，仅使用componentId
      const panelId = panel.componentId;
      console.log(`保存蒙版使用的ID: ${panelId}`);

      // 调用保存回调，传递蒙版数据和动态路径
      onSaveMask(maskDataUrl, panelId, finalPath);
      onClose();
    } catch (error) {
      console.error('生成蒙版时出错:', error);
      // 确保即使出错也能关闭弹窗
      onClose();
    }
  };

  // 处理缩放变化
  const handleScaleChange = (newScale) => {
    setScale(newScale);
  };

  // 处理复位
  const handleReset = () => {
    setScale(initialScale);
    setImagePosition({ x: 0, y: 0 });
    lastPosition.current = { x: 0, y: 0 };
  };

  // 内部确认弹窗状态
  const [showCloseConfirm, setShowCloseConfirm] = useState(false);

  // 处理关闭请求
  const handleCloseRequest = () => {
    if (hasEdited) {
      // 显示内部确认弹窗
      setShowCloseConfirm(true);
    } else {
      // 直接关闭
      onClose();
    }
  };

  // 处理确认关闭
  const handleConfirmClose = () => {
    setShowCloseConfirm(false);
    onClose();
  };

  // 处理取消关闭
  const handleCancelClose = () => {
    setShowCloseConfirm(false);
  };

  // 计算Canvas样式
  const canvasStyle = {
    cursor: 'none !important',
    display: isLoading ? 'none' : 'block',
    maxWidth: '100%',
    maxHeight: '100%',
    backgroundColor: 'transparent'
  };

  // 渲染时输出初始缩放比例
  console.log('MaskDrawModal rendering with initialScale:', initialScale);

  // 处理键盘按下事件
  const handleKeyDown = useCallback((e) => {
    // ESC键 - 取消多边形选区或关闭弹窗
    if (e.code === 'Escape') {
      e.preventDefault();
      if (activeTool === 'polygon' && isPolygonDrawing) {
        // 取消多边形选区
        setPolygonPoints([]);
        setIsPolygonDrawing(false);
        setPreviewPoint(null);
        setPolygonComplete(false);
      } else {
        handleCloseRequest();
      }
      return;
    }

    // Enter键 - 完成多边形选区
    if (e.code === 'Enter' && activeTool === 'polygon' && polygonPoints.length >= 3) {
      e.preventDefault();
      completePolygon();
      return;
    }

    // Backspace/Delete键 - 删除最后一个多边形点
    if ((e.code === 'Backspace' || e.code === 'Delete') && activeTool === 'polygon' && polygonPoints.length > 0) {
      e.preventDefault();
      setPolygonPoints(prev => prev.slice(0, -1));
      if (polygonPoints.length === 1) {
        setIsPolygonDrawing(false);
      }
      return;
    }

    // 数字键1-3 - 快速切换工具
    if (e.code === 'Digit1') {
      e.preventDefault();
      handleToolChange('brush');
      return;
    }
    if (e.code === 'Digit2') {
      e.preventDefault();
      handleToolChange('eraser');
      return;
    }
    if (e.code === 'Digit3') {
      e.preventDefault();
      handleToolChange('polygon');
      return;
    }

    // Ctrl+Z - 撤销
    if (e.ctrlKey && e.code === 'KeyZ' && !e.shiftKey) {
      e.preventDefault();
      if (historyIndex > 0) {
        loadHistoryState(historyIndex - 1);
      }
      return;
    }

    // Ctrl+Y 或 Ctrl+Shift+Z - 重做
    if ((e.ctrlKey && e.code === 'KeyY') || (e.ctrlKey && e.shiftKey && e.code === 'KeyZ')) {
      e.preventDefault();
      if (historyIndex < history.length - 1) {
        loadHistoryState(historyIndex + 1);
      }
      return;
    }

    // 空格键
    if (e.code === 'Space' && !isSpaceDown) {
      e.preventDefault(); // 防止页面滚动
      setIsSpaceDown(true);
      setShowBrushCursor(false); // 隐藏笔刷指示器

      // 临时切换光标为抓手
      if (canvasContainerRef.current) {
        canvasContainerRef.current.classList.add('space-down');
        canvasContainerRef.current.style.cursor = 'grab';

        // 确保清除canvas元素的none光标，以显示抓手
        if (canvasRef.current) {
          canvasRef.current.style.removeProperty('cursor');
        }
      }

      console.log('空格键按下，切换到拖动模式');
    }
  }, [isSpaceDown, activeTool, isPolygonDrawing, polygonPoints, completePolygon]);

  // 处理键盘松开事件
  const handleKeyUp = useCallback((e) => {
    // 空格键
    if (e.code === 'Space' && isSpaceDown) {
      e.preventDefault();
      setIsSpaceDown(false);

      // 恢复光标和笔刷指示器
      if (canvasContainerRef.current) {
        canvasContainerRef.current.classList.remove('space-down');
        canvasContainerRef.current.style.cursor = 'none';
        setShowBrushCursor(true); // 显示笔刷指示器

        // 重新设置canvas元素的none光标
        if (canvasRef.current) {
          canvasRef.current.style.setProperty('cursor', 'none', 'important');
        }
      }

      console.log('空格键松开，恢复到绘制模式');
    }
  }, [isSpaceDown]);

  // 添加和移除键盘事件监听
  useEffect(() => {
    // 只有在弹窗打开时才添加事件监听
    if (isOpen) {
      window.addEventListener('keydown', handleKeyDown);
      window.addEventListener('keyup', handleKeyUp);

      // 移除事件监听
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
        window.removeEventListener('keyup', handleKeyUp);
      };
    }
  }, [isOpen, handleKeyDown, handleKeyUp]);

  // 处理显示提示
  const handleShowTip = () => {
    console.log('开始处理提示显示...');
    console.log('PC端按钮ref:', tipButtonRef.current);
    console.log('移动端按钮ref:', mobileTipButtonRef.current);

    // 优先使用PC端按钮，如果不存在则使用移动端按钮
    const buttonRef = tipButtonRef.current || mobileTipButtonRef.current;

    if (buttonRef) {
      console.log('找到按钮元素:', buttonRef);
      console.log('按钮样式:', window.getComputedStyle(buttonRef));
      console.log('按钮显示状态:', window.getComputedStyle(buttonRef).display);
      console.log('按钮可见性:', window.getComputedStyle(buttonRef).visibility);

      // 使用 requestAnimationFrame 确保在下一帧渲染时获取位置
      requestAnimationFrame(() => {
        const rect = buttonRef.getBoundingClientRect();
        console.log('按钮位置信息:', rect);

        if (rect.width > 0 && rect.height > 0) {
          console.log('按钮尺寸正常，设置位置:', { left: rect.left, top: rect.top });
          setTipPosition({
            left: rect.left,
            top: rect.top + 42
          });
          setIsTipVisible(true);
        } else {
          console.warn('按钮元素尺寸为0，无法获取正确位置');
          console.log('尝试使用按钮的offsetLeft和offsetTop');

          // 尝试使用offsetLeft和offsetTop作为备选方案
          const offsetLeft = buttonRef.offsetLeft;
          const offsetTop = buttonRef.offsetTop;
          console.log('offset位置:', { left: offsetLeft, top: offsetTop });

          if (offsetLeft > 0 || offsetTop > 0) {
            setTipPosition({
              left: offsetLeft,
              top: offsetTop
            });
            setIsTipVisible(true);
          } else {
            // 使用默认位置作为最后的备选方案
            console.log('使用默认位置');
            setTipPosition({
              left: 100,
              top: 100
            });
            setIsTipVisible(true);
          }
        }
      });
    } else {
      console.error('未找到按钮元素');
    }
  };

  // 处理关闭提示
  const handleCloseTip = () => {
    setIsTipVisible(false);
  };

  useEffect(() => {
    // 添加鼠标/触摸移动事件监听器，确保全局跟踪位置
    const handleGlobalMouseMove = (e) => {
      if (canvasRef.current && isOpen) {
        const rect = canvasRef.current.getBoundingClientRect();

        // 获取事件坐标
        let clientX, clientY;
        if (e.touches && e.touches.length > 0) {
          clientX = e.touches[0].clientX;
          clientY = e.touches[0].clientY;
        } else {
          clientX = e.clientX;
          clientY = e.clientY;
        }

        // 检查是否在画布区域内
        if (
          clientX >= rect.left &&
          clientX <= rect.right &&
          clientY >= rect.top &&
          clientY <= rect.bottom
        ) {
          // 计算画布上的实际位置
          const canvasX = clientX - rect.left;
          const canvasY = clientY - rect.top;

          // 更新鼠标位置
          setMousePosition({
            x: canvasX,
            y: canvasY
          });

          // 获取图片绘制区域
          const drawArea = actualDrawAreaRef.current;

          if (drawArea && !isSpaceDown) {
            // 计算当前缩放下的图片显示区域
            const currentScale = scale / 100;
            const scaledX = drawArea.x * currentScale + imagePosition.x;
            const scaledY = drawArea.y * currentScale + imagePosition.y;
            const scaledWidth = drawArea.width * currentScale;
            const scaledHeight = drawArea.height * currentScale;

            // 检查是否在图片区域内
            const isInDrawArea =
              canvasX >= scaledX &&
              canvasX <= scaledX + scaledWidth &&
              canvasY >= scaledY &&
              canvasY <= scaledY + scaledHeight;

            if (isInDrawArea && !showBrushCursor) {
              setShowBrushCursor(true);
            } else if (!isInDrawArea && showBrushCursor) {
              setShowBrushCursor(false);
            }
          }
        }
      }
    };

    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('touchmove', handleGlobalMouseMove, { passive: false });

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('touchmove', handleGlobalMouseMove);
    };
  }, [isOpen, isSpaceDown, showBrushCursor, scale, imagePosition]);

  // 强制设置canvas光标为none
  useEffect(() => {
    if (canvasRef.current) {
      canvasRef.current.style.setProperty('cursor', 'none', 'important');
    }
  }, [isOpen, activeTool, isDragging]); // 当工具变化或拖动状态变化时重新设置

  // 处理检查蒙版
  const handlePreviewMask = () => {
    setPreviewMask(!previewMask);
  };

  // 处理图片预览
  const handleImagePreview = () => {
    // 获取原始图片URL
    let imageUrl = '';
    if (panel.processedFile) {
      imageUrl = panel.processedFile;
    } else if (panel.url) {
      imageUrl = panel.url;
    } else if (panel.originalImage) {
      imageUrl = panel.originalImage;
    }

    if (imageUrl) {
      setPreviewImageUrl(imageUrl);
      setPreviewImageAlt('蒙版绘制预览图片');
      setPreviewModalVisible(true);
    }
  };

  // 关闭图片预览
  const handleCloseImagePreview = () => {
    setPreviewModalVisible(false);
    setPreviewImageUrl('');
    setPreviewImageAlt('');
  };

  // 监听弹窗打开状态
  useEffect(() => {
    if (isOpen && canvasRef.current) {
      // 初始化清空操作标记
      window.hasClearedMask = false;

      // 弹窗打开时，仅在确实存在蒙版数据时尝试立即渲染
      if (panel && panel.maskData && typeof panel.maskData === 'string' && panel.maskData.length > 0) {
        // 确保在弹窗打开时立即渲染蒙版，并多次尝试以确保渲染成功
        console.log('弹窗打开，尝试立即渲染已有蒙版');

        // 立即渲染一次
        renderBufferToMain();

        // 等待UI更新后渲染多次，确保显示正确
        const renderTimes = [16, 50, 100, 200];
        renderTimes.forEach(time => {
          setTimeout(() => {
            console.log(`延迟 ${time}ms 再次渲染蒙版`);
            renderBufferToMain();
          }, time);
        });
      }
    }
  }, [isOpen, panel, renderBufferToMain]);

  // 修改 getCanvasMousePosition 函数
  const getCanvasMousePosition = useCallback((e) => {
    if (!canvasRef.current) return { x: 0, y: 0 };
    const rect = canvasRef.current.getBoundingClientRect();

    // 直接返回相对于画布的原始坐标
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  }, []);

  // 确保缩放或其他状态变化时更新预览
  useEffect(() => {
    if (isOpen && canvasRef.current) {
      renderBufferToMain();
    }
  }, [isOpen, scale, imagePosition, previewMask]);

  // 多边形选区的动画效果
  useEffect(() => {
    let animationFrame;

    if (activeTool === 'polygon' && polygonPoints.length >= 3 && isPolygonDrawing) {
      const animate = () => {
        renderBufferToMain();
        animationFrame = requestAnimationFrame(animate);
      };
      animationFrame = requestAnimationFrame(animate);
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [activeTool, polygonPoints.length, isPolygonDrawing, renderBufferToMain]);

  // 添加监听历史记录变化的效果，确保蒙版立即显示
  useEffect(() => {
    if (isOpen && canvasRef.current && historyIndex >= 0 && history.length > 0) {
      // 确保在历史记录变化时重新渲染
      renderBufferToMain();
    }
  }, [isOpen, history, historyIndex]);

  // ... 1. 在组件内添加状态和ref
  const [touchMode, setTouchMode] = useState(null); // null | 'panzoom'
  const lastTouchDistance = useRef(0);
  const lastTouchCenter = useRef({ x: 0, y: 0 });
  const lastImagePosition = useRef({ x: 0, y: 0 });
  const lastScale = useRef(100);

  // ... 2. 修改handleMouseDown
  const origHandleMouseDown = handleMouseDown;
  const handleMobileTouchStart = useCallback((e) => {
    if (e.touches && e.touches.length === 2) {
      // 双指开始，进入平移缩放模式
      setTouchMode('panzoom');
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const center = {
        x: (touch1.clientX + touch2.clientX) / 2,
        y: (touch1.clientY + touch2.clientY) / 2,
      };
      const dx = touch1.clientX - touch2.clientX;
      const dy = touch1.clientY - touch2.clientY;
      lastTouchDistance.current = Math.sqrt(dx * dx + dy * dy);
      lastTouchCenter.current = center;
      lastImagePosition.current = { ...imagePosition };
      lastScale.current = scale;
      e.preventDefault();
      return;
    }
    setTouchMode(null);
    origHandleMouseDown(e);
  }, [imagePosition, scale, origHandleMouseDown]);

  // ... 3. 修改handleMouseMove
  const origHandleMouseMove = handleMouseMove;
  const handleMobileTouchMove = useCallback((e) => {
    if (touchMode === 'panzoom' && e.touches && e.touches.length === 2) {
      // 双指平移缩放
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const center = {
        x: (touch1.clientX + touch2.clientX) / 2,
        y: (touch1.clientY + touch2.clientY) / 2,
      };
      const dx = touch1.clientX - touch2.clientX;
      const dy = touch1.clientY - touch2.clientY;
      const distance = Math.sqrt(dx * dx + dy * dy);
      // 平移
      const deltaX = center.x - lastTouchCenter.current.x;
      const deltaY = center.y - lastTouchCenter.current.y;
      setImagePosition({
        x: lastImagePosition.current.x + deltaX,
        y: lastImagePosition.current.y + deltaY,
      });
      // 缩放
      let newScale = lastScale.current * (distance / lastTouchDistance.current);
      newScale = Math.max(10, Math.min(500, newScale));
      setScale(newScale);
      e.preventDefault();
      return;
    }
    // 单指时，调用handleDraw实现持续绘制
    if (e.touches && e.touches.length === 1) {
      handleDraw(e);
      return;
    }
    origHandleMouseMove(e);
  }, [touchMode, origHandleMouseMove, handleDraw]);

  // ... 4. 修改handleMouseUp
  const origHandleMouseUp = handleMouseUp;
  const handleMobileTouchEnd = useCallback((e) => {
    if (touchMode === 'panzoom') {
      setTouchMode(null);
      e.preventDefault();
      return;
    }
    origHandleMouseUp(e);
  }, [touchMode, origHandleMouseUp]);

  // ... 5. 在JSX中canvas-container和canvas的onTouch事件替换为新函数
  // 例如：
  // onTouchStart={isMobile ? handleMobileTouchStart : handleMouseDown}
  // onTouchMove={isMobile ? handleMobileTouchMove : handleMouseMove}
  // onTouchEnd={isMobile ? handleMobileTouchEnd : handleMouseUp}
  // ... 其余代码保持不变 ...

  // ... 在组件内定义移动端提示 ...
  const maskDrawTipText = isMobile
    ? '•  用双指拖动或缩放图片。\n•  蒙版覆盖区域将由 AI 进行重绘。'
    : MASK_DRAW_TIPS.BASIC;

  return (
    <>
      {isOpen && (
        <div className="mask-draw-modal open">
          <div className="mask-draw-overlay" onClick={handleCloseRequest}></div>
          <div className="mask-draw-content">
            {/* 大型关闭按钮 - 放在模态框内容的右上角 */}
            <button
              className="large-close-button"
              onClick={handleCloseRequest}
            >
              <MdClose />
            </button>

            <div className="mask-draw-main">
              {/* 左侧工具栏 */}
              <div className="mask-tools-sidebar">
                <div className="tools-content">
                  <DrawTools
                    activeTool={activeTool}
                    onToolChange={handleToolChange}
                    brushSize={brushSize}
                    onBrushSizeChange={handleBrushSizeChange}
                  />

                  <div className="tools-divider"></div>

                  <HistoryTools
                    canUndo={historyIndex > 0}
                    canRedo={historyIndex < history.length - 1}
                    onUndo={handleUndo}
                    onRedo={handleRedo}
                    onClear={handleClear}
                  />
                </div>
              </div>

              {/* 中间绘制区 */}
              <div className="mask-canvas-area">
                <div
                  className={`canvas-container ${isDragging ? 'dragging' : ''} ${isSpaceDown ? 'space-down' : ''}`}
                  ref={canvasContainerRef}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseLeave}
                  onMouseEnter={handleCanvasMouseEnter}
                  onTouchStart={handleMobileTouchStart}
                  onTouchMove={handleMobileTouchMove}
                  onTouchEnd={handleMobileTouchEnd}
                >
                  {isLoading && (
                    <div className="loading-indicator">
                      <div className="spinner"></div>
                      <p>加载图片中...</p>
                    </div>
                  )}
                  <canvas
                    ref={canvasRef}
                    style={canvasStyle}
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleDraw}
                    onMouseUp={handleEndDrawing}
                    onMouseLeave={handleEndDrawing}
                    onDoubleClick={handleDoubleClick}
                    onTouchStart={handleMobileTouchStart}
                    onTouchMove={handleMobileTouchMove}
                    onTouchEnd={handleMobileTouchEnd}
                  />

                  {/* 笔刷大小指示器 */}
                  {showBrushCursor && (
                    <div
                      className="brush-cursor"
                      style={{
                        position: 'absolute',
                        left: `${mousePosition.x}px`,
                        top: `${mousePosition.y}px`,
                        width: `${brushSize}px`,
                        height: `${brushSize}px`,
                        transform: 'translate(-50%, -50%)',
                        pointerEvents: 'none',
                        cursor: 'none'
                      }}
                    />
                  )}

                  {/* 多边形选区状态提示 */}
                  {activeTool === 'polygon' && (
                    <div className="polygon-status-tip">
                      {!isPolygonDrawing ? (
                        <div className="status-message">
                          <span>点击图片开始绘制多边形选区</span>
                          <div className="shortcuts">
                            <span>快捷键: 1-画笔 2-橡皮 3-选区</span>
                          </div>
                        </div>
                      ) : polygonPoints.length < 3 ? (
                        <div className="status-message">
                          <span>已添加 {polygonPoints.length} 个点，至少需要 3 个点</span>
                          <div className="shortcuts">
                            <span>Backspace-删除点 ESC-取消</span>
                          </div>
                        </div>
                      ) : (
                        <div className="status-message">
                          <span>已添加 {polygonPoints.length} 个点</span>
                          <div className="shortcuts">
                            <span>点击第一个点或按Enter完成 | Backspace-删除点 ESC-取消</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* 右侧操作栏 */}
              <div className="mask-actions-sidebar">
                <div className="actions-content">
                  {/* PC端布局 - 垂直排列 */}
                  <div className="desktop-actions-layout">
                    {/* 添加缩放控制组件 */}
                    <ImageZoomControl
                      initialScale={initialScale}
                      onScaleChange={handleScaleChange}
                      onReset={handleReset}
                      onPreview={handleImagePreview}
                      imagePosition={imagePosition}
                    />

                    {/* 帮助提示按钮 */}
                    <button
                      className="help-tip-button"
                      onClick={handleShowTip}
                      ref={tipButtonRef}
                    >
                      <MdOutlineHelpOutline />
                      <span>提示</span>
                    </button>

                    {/* 检查蒙版按钮 */}
                    <button
                      className="preview-mask-btn"
                      onClick={handlePreviewMask}
                    >
                      {previewMask ? '关闭检查' : '检查蒙版'}
                    </button>

                    {/* 确认按钮 */}
                    <button
                      className="save-settings-btn mask-confirm-btn"
                      onClick={handleSave}
                    >
                      保存蒙版
                    </button>
                  </div>

                  {/* 移动端布局 - 两行排列 */}
                  <div className="mobile-actions-layout">
                    {/* 第一行：百分比视窗、缩小按钮、放大按钮、复位按钮、预览按钮、提示按钮 */}
                    <div className="mobile-actions-row-1">
                      <ImageZoomControl
                        initialScale={initialScale}
                        onScaleChange={handleScaleChange}
                        onReset={handleReset}
                        onPreview={handleImagePreview}
                        imagePosition={imagePosition}
                      />

                      <button
                        className="help-tip-button mobile-help-btn"
                        onClick={handleShowTip}
                        ref={mobileTipButtonRef}
                      >
                        <MdOutlineHelpOutline />
                        <span>提示</span>
                      </button>
                    </div>

                    {/* 第二行：检查蒙版和保存蒙版 */}
                    <div className="mobile-actions-row-2">
                      <button
                        className="preview-mask-btn mobile-preview-btn"
                        onClick={handlePreviewMask}
                      >
                        {previewMask ? '关闭检查' : '检查蒙版'}
                      </button>

                      <button
                        className="save-settings-btn mask-confirm-btn mobile-save-btn"
                        onClick={handleSave}
                      >
                        保存蒙版
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 提示弹窗 */}
      <TipPopup
        type="mask-draw"
        position={tipPosition}
        isVisible={isTipVisible}
        onClose={handleCloseTip}
        content={maskDrawTipText}
      />

      {/* 图片预览模态框 */}
      <ImagePreviewModal
        visible={previewModalVisible}
        imageUrl={previewImageUrl}
        onClose={handleCloseImagePreview}
        alt={previewImageAlt}
        featureName="蒙版绘制"
        showHint={false}
      />

      {/* 内部确认关闭弹窗 */}
      {showCloseConfirm && (
        <div className="mask-draw-confirm-overlay" onClick={handleCancelClose}>
          <div className="mask-draw-confirm-modal" onClick={(e) => e.stopPropagation()}>
            <div className="mask-draw-confirm-header">
              <h3>确认关闭</h3>
            </div>
            <div className="mask-draw-confirm-body">
              <p>您有未保存的蒙版更改，确定要关闭吗？</p>
            </div>
            <div className="mask-draw-confirm-footer">
              <button
                className="mask-draw-confirm-cancel"
                onClick={handleCancelClose}
              >
                取消
              </button>
              <button
                className="mask-draw-confirm-ok"
                onClick={handleConfirmClose}
              >
                确认关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default MaskDrawModal;