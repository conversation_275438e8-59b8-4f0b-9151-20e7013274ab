// 场景分类配置
export const SCENE_CATEGORIES = [
  { key: 'beach', name: '海滩' },
  { key: 'pool', name: '泳池' },
  { key: 'nature', name: '自然' },
  { key: 'street', name: '街道' },
  { key: 'indoor', name: '室内' },
  { key: 'other', name: '其他' }
];

// OSS基础路径
export const SCENE_OSS_BASE = 'https://file.aibikini.cn/config/scenes/';

// 每个分类最大图片数量
export const MAX_IMAGES_PER_CATEGORY = 20;

// 动态获取某分类下所有图片URL
export const fetchSceneImages = async (categoryKey, maxCount = MAX_IMAGES_PER_CATEGORY) => {
  const urls = [];
  for (let i = 1; i <= maxCount; i++) {
    const num = String(i).padStart(2, '0');
    // 检查缩略图是否存在（用于卡片显示）
    const thumbnailUrl = `${SCENE_OSS_BASE}${categoryKey}/${num}.jpg`;
    // 大图URL（用于预览和任务生成）
    const fullImageUrl = `${SCENE_OSS_BASE}${categoryKey}/${num}-a.jpg`;
    
    try {
      const res = await fetch(thumbnailUrl, { method: 'HEAD' });
      if (res.ok) {
        urls.push({
          thumbnail: thumbnailUrl,
          fullImage: fullImageUrl
        });
      } else {
        break; // 遇到第一个不存在的图片就停止
      }
    } catch {
      break;
    }
  }
  return urls;
};

// 统一加载所有分类图片
export const fetchAllSceneImages = async () => {
  const all = [];
  for (const cat of SCENE_CATEGORIES) {
    const images = await fetchSceneImages(cat.key);
    // 按顺序平铺，保留分类信息
    all.push(...images.map((imageUrls, index) => ({
      id: `${cat.key}-${String(index + 1).padStart(2, '0')}`,
      category: cat.key,
      categoryName: cat.name,
      name: `${cat.name}场景`,
      // 缩略图用于卡片显示
      thumbnail: imageUrls.thumbnail + '?v=' + new Date().getTime(),
      // 大图用于预览和任务生成
      image: imageUrls.fullImage + '?v=' + new Date().getTime()
    })));
  }
  return all;
}; 