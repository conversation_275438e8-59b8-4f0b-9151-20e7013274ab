const workflow = {
  prompt: {
    "19": {
      "inputs": {
        "vae_name": "ae.safetensors"
      },
      "class_type": "VAELoader",
      "_meta": {
        "title": "Load VAE"
      }
    },
    "20": {
      "inputs": {
        "width": 1536,
        "height": 1536,
        "batch_size": 1
      },
      "class_type": "EmptySD3LatentImage",
      "_meta": {
        "title": "EmptySD3LatentImage"
      }
    },
    "22": {
      "inputs": {
        "text": [
          "118",
          0
        ],
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        },
        "clip": [
          "81",
          0
        ]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIP Text Encode (Prompt)"
      }
    },
    "26": {
      "inputs": {
        "guidance": 15,
        "conditioning": [
          "31",
          0
        ]
      },
      "class_type": "FluxGuidance",
      "_meta": {
        "title": "FluxGuidance"
      }
    },
    "28": {
      "inputs": {
        "samples": [
          "77",
          0
        ],
        "vae": [
          "19",
          0
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE Decode"
      }
    },
    "31": {
      "inputs": {
        "strength": 0.66,
        "start_percent": 0,
        "end_percent": 0.4,
        "positive": [
          "22",
          0
        ],
        "negative": [
          "32",
          0
        ],
        "control_net": [
          "82",
          0
        ],
        "vae": [
          "19",
          0
        ],
        "image": [
          "123",
          0
        ]
      },
      "class_type": "ControlNetApplySD3",
      "_meta": {
        "title": "Apply Controlnet with VAE"
      }
    },
    "32": {
      "inputs": {
        "text": "",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        },
        "clip": [
          "81",
          0
        ]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIP Text Encode (Prompt)"
      }
    },
    "33": {
      "inputs": {
        "max_shift": 1.15,
        "base_shift": 0.5,
        "width": 1536,
        "height": 1536,
        "model": [
          "80",
          0
        ]
      },
      "class_type": "ModelSamplingFlux",
      "_meta": {
        "title": "ModelSamplingFlux"
      }
    },
    "34": {
      "inputs": {
        "control_net_name": "flux1-dev-controlnet-union-pro2.safetensors"
      },
      "class_type": "ControlNetLoader",
      "_meta": {
        "title": "Load ControlNet Model"
      }
    },
    "77": {
      "inputs": {
        "seed": 707364545331606,
        "steps": 25,
        "cfg": 1,
        "sampler_name": "euler",
        "scheduler": "normal",
        "denoise": 1,
        "model": [
          "33",
          0
        ],
        "positive": [
          "26",
          0
        ],
        "negative": [
          "31",
          1
        ],
        "latent_image": [
          "20",
          0
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "随机种子"
      }
    },
    "80": {
      "inputs": {
        "unet_name": "flux1-dev-fp8.safetensors",
        "weight_dtype": "fp8_e4m3fn"
      },
      "class_type": "UNETLoader",
      "_meta": {
        "title": "Load Diffusion Model"
      }
    },
    "81": {
      "inputs": {
        "clip_name1": "clip_l.safetensors",
        "clip_name2": "t5xxl_fp16.safetensors",
        "type": "flux",
        "device": "default"
      },
      "class_type": "DualCLIPLoader",
      "_meta": {
        "title": "DualCLIPLoader"
      }
    },
    "82": {
      "inputs": {
        "type": "openpose",
        "control_net": [
          "34",
          0
        ]
      },
      "class_type": "SetUnionControlNetType",
      "_meta": {
        "title": "SetUnionControlNetType"
      }
    },
    "101": {
      "inputs": {
        "rows": 2,
        "columns": 3,
        "image": [
          "28",
          0
        ]
      },
      "class_type": "FS: Crop Image Into Even Pieces",
      "_meta": {
        "title": "Crop Image Into Even Pieces (FS)"
      }
    },
    "107": {
      "inputs": {
        "strength": 1,
        "start_percent": 0,
        "end_percent": 1,
        "positive": [
          "111",
          0
        ],
        "negative": [
          "32",
          0
        ],
        "control_net": [
          "110",
          0
        ],
        "image": [
          "116",
          0
        ],
        "vae": [
          "19",
          0
        ]
      },
      "class_type": "ControlNetApplyAdvanced",
      "_meta": {
        "title": "Apply ControlNet"
      }
    },
    "108": {
      "inputs": {
        "text": "high-quality enlarged image，",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        },
        "clip": [
          "81",
          0
        ]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIP Text Encode (Prompt)"
      }
    },
    "110": {
      "inputs": {
        "control_net_name": "Flux/Flux.1-dev-Controlnet-Upscaler.safetensors"
      },
      "class_type": "ControlNetLoader",
      "_meta": {
        "title": "Load ControlNet Model"
      }
    },
    "111": {
      "inputs": {
        "guidance": 4,
        "conditioning": [
          "108",
          0
        ]
      },
      "class_type": "FluxGuidance",
      "_meta": {
        "title": "FluxGuidance"
      }
    },
    "112": {
      "inputs": {
        "seed": 146268946379919,
        "steps": 25,
        "cfg": 1,
        "sampler_name": "euler",
        "scheduler": "simple",
        "denoise": 1,
        "model": [
          "80",
          0
        ],
        "positive": [
          "107",
          0
        ],
        "negative": [
          "107",
          1
        ],
        "latent_image": [
          "113",
          0
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "KSampler"
      }
    },
    "113": {
      "inputs": {
        "width": 1024,
        "height": 1536,
        "batch_size": 1
      },
      "class_type": "EmptySD3LatentImage",
      "_meta": {
        "title": "EmptySD3LatentImage"
      }
    },
    "114": {
      "inputs": {
        "samples": [
          "112",
          0
        ],
        "vae": [
          "19",
          0
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE Decode"
      }
    },
    "116": {
      "inputs": {
        "image": [
          "101",
          0
        ]
      },
      "class_type": "easy imageBatchToImageList",
      "_meta": {
        "title": "Image Batch To Image List"
      }
    },
    "117": {
      "inputs": {
        "images": [
          "114",
          0
        ]
      },
      "class_type": "easy imageListToImageBatch",
      "_meta": {
        "title": "Image List To Image Batch"
      }
    },
    "118": {
      "inputs": {
        "delimiter": ", ",
        "clean_whitespace": "true",
        "text_a": [
          "121",
          0
        ],
        "text_b": [
          "119",
          0
        ]
      },
      "class_type": "Text Concatenate",
      "_meta": {
        "title": "Text Concatenate"
      }
    },
    "119": {
      "inputs": {
        "String": "((masterpiece, photo, best quality)), award winning, 4k,8k,character sheet,in different poses and angles, including front view, side view, and back view,visible face, portrait,turnaround sheet,same cloth",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "String",
      "_meta": {
        "title": "String"
      }
    },
    "121": {
      "inputs": {
        "from_translate": "auto",
        "to_translate": "english",
        "add_proxies": false,
        "proxies": "",
        "auth_data": "",
        "service": "GoogleTranslator",
        "text": "一个非洲黑人女子，穿着红色的时尚连体泳衣，站在沙滩海边，穿着时尚的高跟鞋",
        "Show proxy": "proxy_hide",
        "Show authorization": "authorization_hide",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "DeepTranslatorTextNode",
      "_meta": {
        "title": "描述词输入"
      }
    },
    "122": {
      "inputs": {
        "filename_prefix": "Virtual",
        "images": [
          "117",
          0
        ]
      },
      "class_type": "SaveImage",
      "_meta": {
        "title": "Save Image"
      }
    },
    "123": {
      "inputs": {
        "url": "https://",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "骨骼图图片上传(固定图片)"
      }
    }
  }
};

module.exports = workflow; 