# AIBIKINI 订阅系统数据结构

## 1. 订阅计划模型 (Plan)

### 完整数据结构
```javascript
{
  _id: ObjectId,
  code: {
    type: String,
    required: true,
    unique: true,
    // 示例: 'free', 'design', 'model', 'full', 'enterprise'
  },
  name: {
    type: String,
    required: true,
    // 示例: '免费版', '设计版', '模特版', '完整版', '企业版'
  },
  description: {
    type: String,
    // 示例: '基础功能免费使用，适合个人用户'
  },
  price: {
    monthly: {
      type: Number,
      // 月付价格，单位：元
      // 示例: 0, 99, 199, 299, 999
    },
    yearly: {
      type: Number,
      // 年付价格，单位：元
      // 示例: 0, 999, 1999, 2999, 9999
    },
    discount: {
      type: Number,
      // 年付折扣百分比
      // 示例: 0, 15, 20, 25, 30
    }
  },
  features: {
    design: {
      enabled: {
        type: Boolean,
        default: false
        // 是否启用设计功能模块
      },
      trendDesign: {
        type: Boolean,
        default: false
        // 爆款开发功能
      },
      styleOptimization: {
        type: Boolean,
        default: false
        // 款式优化功能
      },
      inspirationExplore: {
        type: Boolean,
        default: false
        // 灵感探索功能
      }
    },
    model: {
      enabled: {
        type: Boolean,
        default: false
        // 是否启用模特功能模块
      },
      fashionShoot: {
        type: Boolean,
        default: false
        // 时尚大片功能
      },
      modelChange: {
        type: Boolean,
        default: false
        // 换模特功能
      },
      colorChange: {
        type: Boolean,
        default: false
        // 服装复色功能
      },
      backgroundChange: {
        type: Boolean,
        default: false
        // 换背景功能
      },
      virtualModel: {
        type: Boolean,
        default: false
        // 虚拟模特功能
      }
    },
    tools: {
      enabled: {
        type: Boolean,
        default: true
        // 是否启用工具功能模块
      },
      upscale: {
        type: Boolean,
        default: true
        // 高清放大功能
      },
      matting: {
        type: Boolean,
        default: true
        // 自动抠图功能
      },
      extend: {
        type: Boolean,
        default: true
        // 智能扩图功能
      }
    },
    support: {
      level: {
        type: String,
        enum: ['standard', 'premium', 'enterprise'],
        default: 'standard'
        // 支持级别
      },
      responseTime: {
        type: String,
        enum: ['5x8', '7x24'],
        default: '5x8'
        // 响应时间
      }
    }
  },
  usageQuota: {
    totalRequests: {
      type: Number,
      default: -1
      // 总请求次数限制，-1 表示无限制
      // 示例: 10, 100, 1000, -1
    },
    dailyRequests: {
      type: Number,
      default: -1
      // 每日请求次数限制，-1 表示无限制
      // 示例: 2, 10, 50, -1
    }
  },
  isPublic: {
    type: Boolean,
    default: true
    // 是否在公开页面显示
  },
  sortOrder: {
    type: Number,
    default: 0
    // 排序权重，数字越小排序越靠前
  },
  isRecommended: {
    type: Boolean,
    default: false
    // 是否推荐计划
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}
```

### 预设计划数据示例

```javascript
// 免费版
{
  code: 'free',
  name: '免费版',
  description: '基础功能免费使用，适合个人用户',
  price: { monthly: 0, yearly: 0, discount: 0 },
  features: {
    design: { enabled: false },
    model: { enabled: false },
    tools: { enabled: true },
    support: { level: 'standard', responseTime: '5x8' }
  },
  usageQuota: { totalRequests: 10, dailyRequests: 2 },
  isPublic: true,
  sortOrder: 1,
  isRecommended: false
}

// 设计版
{
  code: 'design',
  name: '设计版',
  description: '专注于服装设计功能，适合设计师',
  price: { monthly: 99, yearly: 999, discount: 15 },
  features: {
    design: { 
      enabled: true,
      trendDesign: true,
      styleOptimization: true,
      inspirationExplore: true
    },
    model: { enabled: false },
    tools: { enabled: true },
    support: { level: 'standard', responseTime: '5x8' }
  },
  usageQuota: { totalRequests: 500, dailyRequests: 20 },
  isPublic: true,
  sortOrder: 2,
  isRecommended: true
}

// 完整版
{
  code: 'full',
  name: '完整版',
  description: '包含所有功能，适合专业用户',
  price: { monthly: 299, yearly: 2999, discount: 20 },
  features: {
    design: { enabled: true },
    model: { enabled: true },
    tools: { enabled: true },
    support: { level: 'premium', responseTime: '7x24' }
  },
  usageQuota: { totalRequests: 2000, dailyRequests: 100 },
  isPublic: true,
  sortOrder: 4,
  isRecommended: true
}
```

## 2. 用户订阅模型 (Subscription)

### 完整数据结构
```javascript
{
  _id: ObjectId,
  user: {
    type: ObjectId,
    ref: 'User',
    required: true
    // 关联用户ID
  },
  plan: {
    type: String,
    enum: ['design', 'model', 'full', 'enterprise', 'free'],
    required: true,
    default: 'free'
    // 订阅计划代码
  },
  status: {
    type: String,
    enum: ['active', 'expired', 'canceled', 'pending'],
    default: 'pending'
    // 订阅状态
  },
  startDate: {
    type: Date,
    required: true
    // 订阅开始时间
  },
  endDate: {
    type: Date,
    required: true
    // 订阅结束时间
  },
  autoRenew: {
    type: Boolean,
    default: false
    // 是否自动续费
  },
  price: {
    type: Number,
    required: true
    // 实际支付价格
  },
  paymentMethod: {
    type: String,
    required: false
    // 支付方式
    // 示例: 'alipay', 'wechat', 'credit_card'
  },
  paymentId: {
    type: String
    // 支付平台返回的支付ID
  },
  features: {
    // 与 Plan 模型相同的功能结构
    design: { /* 设计功能配置 */ },
    model: { /* 模特功能配置 */ },
    tools: { /* 工具功能配置 */ },
    support: { /* 支持配置 */ }
  },
  customFeatures: [{
    name: {
      type: String
      // 自定义功能名称
    },
    description: {
      type: String
      // 自定义功能描述
    },
    enabled: {
      type: Boolean
      // 是否启用
    }
  }],
  usageQuota: {
    totalRequests: {
      type: Number,
      default: -1
      // 总请求次数限制
    },
    dailyRequests: {
      type: Number,
      default: -1
      // 每日请求次数限制
    },
    remainingRequests: {
      type: Number
      // 剩余请求次数
    }
  },
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
    // 扩展元数据，可存储任意键值对
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}
```

### 订阅数据示例

```javascript
// 活跃订阅示例
{
  user: ObjectId("507f1f77bcf86cd799439011"),
  plan: 'full',
  status: 'active',
  startDate: new Date('2024-01-01'),
  endDate: new Date('2024-02-01'),
  autoRenew: true,
  price: 299,
  paymentMethod: 'alipay',
  paymentId: 'alipay_20240101123456789',
  features: {
    design: { enabled: true },
    model: { enabled: true },
    tools: { enabled: true },
    support: { level: 'premium', responseTime: '7x24' }
  },
  usageQuota: {
    totalRequests: 2000,
    dailyRequests: 100,
    remainingRequests: 1850
  },
  metadata: {
    'promo_code': 'WELCOME2024',
    'referrer': 'google_ads'
  }
}

// 过期订阅示例
{
  user: ObjectId("507f1f77bcf86cd799439012"),
  plan: 'design',
  status: 'expired',
  startDate: new Date('2023-12-01'),
  endDate: new Date('2024-01-01'),
  autoRenew: false,
  price: 99,
  paymentMethod: 'wechat',
  usageQuota: {
    totalRequests: 500,
    dailyRequests: 20,
    remainingRequests: 0
  }
}
```

## 3. 用户模型扩展 (User)

### 订阅相关字段
```javascript
{
  // ... 基础用户信息
  subscription: {
    type: {
      type: String,
      enum: ['free', 'design', 'model', 'full', 'enterprise'],
      default: 'free'
    },
    endDate: {
      type: Date
    },
    autoRenew: {
      type: Boolean,
      default: false
    },
    paymentMethod: {
      type: String
    }
  },
  credits: {
    balance: {
      type: Number,
      default: 0
      // 算力值余额
    },
    totalEarned: {
      type: Number,
      default: 0
      // 累计获得算力值
    },
    totalSpent: {
      type: Number,
      default: 0
      // 累计消耗算力值
    }
  }
}
```

## 4. 计费配置 (Billing Config)

### 功能计费标准
```javascript
const BILLING_CONFIG = {
  // 款式设计相关功能
  'trending': 30,         // 爆款开发
  'optimize': 30,         // 款式优化
  'inspiration': 30,      // 灵感探索
 
  // 模特图相关功能
  'fashion': 150,          // 时尚大片
  'try-on': 110,           // 模特换装
  'change-model': 150,     // 换模特
  'recolor': 20,           // 服装复色
  'fabric': 35,            // 换面料
  'background': 25,        // 换背景
  'virtual': 100,          // 虚拟模特
  'detail-migration': 80, // 细节还原
  'hand-fix': 80,         // 手部修复
  
  // 快捷工具相关功能
  'extract': 25,           // 图片取词
  'upscale': 10,           // 高清放大
  'matting': {             // 自动抠图支持多标签页定价
    'tab1': 10,            // 去背景
    'tab2': 20             // 抠衣服
  },
  'extend': 60,            // 智能扩图
  'inpaint': 999,          // 消除笔
  
  // 视频工具相关功能
  'imgtextvideo': 300,    // 图文成片
  'mulimgvideo': 400,     // 多图成片
};
```

### 流程图映射
```javascript
const FLOW_MAP = {
  'A01-trending': 'trending',
  'A02-optimize': 'optimize',
  'A03-inspiration': 'inspiration',
  'B01-fashion': 'fashion',
  'B02-tryonauto': 'try-on',
  'B02-tryonmanual': 'try-on',
  'B04-recolor': 'recolor',
  'B05-fabric': 'fabric',
  'B06-background': 'background',
  'B07-virtual': 'virtual',
  'C01-extract': 'extract',
  'C02-upscale': 'upscale',
  'C03-matting': 'matting',
  'C04-inpaint': 'inpaint',
  'D01-imgtextvideo': 'imgtextvideo',
  'D02-mulimgvideo': 'mulimgvideo',
};
```

## 5. 数据库索引

### Plan 模型索引
```javascript
// 代码唯一索引
planSchema.index({ code: 1 }, { unique: true });

// 公开计划查询索引
planSchema.index({ isPublic: 1, sortOrder: 1 });

// 推荐计划查询索引
planSchema.index({ isRecommended: 1, sortOrder: 1 });
```

### Subscription 模型索引
```javascript
// 用户订阅查询索引
subscriptionSchema.index({ user: 1, status: 1 });

// 活跃订阅查询索引
subscriptionSchema.index({ status: 'active', endDate: 1 });

// 过期订阅查询索引
subscriptionSchema.index({ status: 'active', endDate: { $lt: new Date() } });

// 创建时间索引
subscriptionSchema.index({ createdAt: -1 });
```

### User 模型索引
```javascript
// 用户名唯一索引
userSchema.index({ username: 1 }, { unique: true });

// 手机号唯一索引
userSchema.index({ phone: 1 }, { unique: true });

// 订阅类型查询索引
userSchema.index({ 'subscription.type': 1 });

// 状态查询索引
userSchema.index({ status: 1 });
```

## 6. 数据验证规则

### Plan 模型验证
```javascript
// 价格验证
price: {
  monthly: {
    type: Number,
    min: 0,
    validate: {
      validator: function(v) {
        return v >= 0;
      },
      message: '月付价格不能为负数'
    }
  },
  yearly: {
    type: Number,
    min: 0,
    validate: {
      validator: function(v) {
        return v >= 0;
      },
      message: '年付价格不能为负数'
    }
  },
  discount: {
    type: Number,
    min: 0,
    max: 100,
    validate: {
      validator: function(v) {
        return v >= 0 && v <= 100;
      },
      message: '折扣必须在0-100之间'
    }
  }
}
```

### Subscription 模型验证
```javascript
// 日期验证
startDate: {
  type: Date,
  required: true,
  validate: {
    validator: function(v) {
      return v instanceof Date && !isNaN(v);
    },
    message: '开始日期必须是有效日期'
  }
},
endDate: {
  type: Date,
  required: true,
  validate: {
    validator: function(v) {
      return v instanceof Date && !isNaN(v) && v > this.startDate;
    },
    message: '结束日期必须晚于开始日期'
  }
}
```

## 7. 数据关系

### 一对多关系
- **User** → **Subscription**: 一个用户可以有多个订阅记录（历史记录）
- **Plan** → **Subscription**: 一个计划可以有多个订阅实例

### 当前活跃订阅
每个用户同时只能有一个活跃订阅，通过以下查询获取：
```javascript
const activeSubscription = await Subscription.findOne({
  user: userId,
  status: 'active',
  endDate: { $gt: new Date() }
});
```

### 订阅历史
获取用户的所有订阅记录：
```javascript
const subscriptionHistory = await Subscription.find({
  user: userId
}).sort({ createdAt: -1 });
``` 