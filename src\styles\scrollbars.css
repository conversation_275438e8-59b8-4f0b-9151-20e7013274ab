/**
 * 统一滚动条样式定义
 * 
 * 特点：
 * 1. 统一宽度为7px
 * 2. 圆角设计
 * 3. 主题色支持
 * 4. 浅色/深色主题
 * 5. 悬停效果
 * 6. 平滑过渡动画
 * 7. 多浏览器兼容
 * 
 * 使用方法：
 * 1. 直接导入此样式文件即可自动应用到所有滚动条
 * 2. 如需局部覆盖，可以针对特定容器重新定义滚动条样式
 * 
 * 注意事项：
 * 1. 主要支持WebKit内核浏览器（Chrome、Safari、Edge等）
 * 2. 部分样式提供Firefox支持
 * 3. 使用 !important 确保样式优先级
 */

@import './theme.css';

/* WebKit滚动条样式 */
*::-webkit-scrollbar {
  width: var(--scrollbar-width, 7px) !important;
  height: var(--scrollbar-width, 7px) !important; /* 添加水平滚动条支持 */
}

*::-webkit-scrollbar-track {
  background: var(--bg-secondary) !important;
  border-radius: var(--radius-md) !important;
}

*::-webkit-scrollbar-thumb {
  background: var(--text-tertiary) !important;
  border-radius: var(--radius-md) !important;
  transition: var(--transition-normal) !important;
}

*::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary) !important;
}

/* 暗色主题特殊处理 */
[data-theme="dark"] *::-webkit-scrollbar-thumb {
  background: var(--text-tertiary) !important;
}

[data-theme="dark"] *::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary) !important;
}

/* Firefox滚动条样式 */
@-moz-document url-prefix() {
  * {
    scrollbar-width: thin !important;
    scrollbar-color: var(--text-tertiary) var(--bg-secondary) !important;
  }
  
  /* Firefox暗色主题特殊处理 */
  [data-theme="dark"] * {
    scrollbar-color: var(--text-tertiary) var(--bg-secondary) !important;
  }
}

/* 优化触摸设备的滚动体验 */
@media (hover: none) {
  * {
    -webkit-overflow-scrolling: touch !important;
  }
} 