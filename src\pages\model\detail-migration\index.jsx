import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import React, { useRef, useEffect, useState, useCallback, Suspense, memo } from 'react';
import { processMask } from '../../../api/pyApi';
import './index.css';
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDescription } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { Modal, Button, Spin, message, Input, Tabs } from 'antd';
import 'antd/dist/reset.css';
import { filterShowcaseByTag } from '../../../config/showcase/showcase';
import { UPLOAD_CONFIG } from '../../../config/uploads/upload';
import { getModelImagePath } from '../../../data/models';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import MaskDrawModal from '../../../components/MaskDrawModal';
import UploadBox_Model from '../../../components/UploadBox_Model';
import ModelMaskPanel from '../../../components/ModelMaskPanel';
import QuantityPanel from '../../../components/QuantityPanel';
import TipsPanel from '../../../components/TipsPanel';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import GenerationArea from '../../../components/GenerationArea';
import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask,checkUserBalance } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import { executeFlow } from '../../../api/flow';
import { getCurrentUserId } from '../../../api';
import { 
  getTasks, 
  getTaskById, 
  deleteTask, 
  createTask,
  filterTasksByUser
} from '../../../api/task';
import { uploadImage } from '../../../api/upload';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import JSZip from 'jszip';
import RandomSeedSelector from '../../../components/RandomSeedSelector';
import { getFakeTasksForUser } from '../../../api/task';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import RequireLogin from '../../../components/RequireLogin';
import { getTaskComponent } from '../../../utils/taskAdapters';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import ClothingMaskPanel from '../../../components/ClothingMaskPanel';
import { useTaskContext } from '../../../contexts/TaskContext';
const MemoizedImageDetailsModal = React.memo(ImageDetailsModal);

const UPLOAD_BOX_WIDTH = 172;
const UPLOAD_BOX_HEIGHT = 172;
const MODEL_UPLOAD_BOX_WIDTH = 258;
const MODEL_UPLOAD_BOX_HEIGHT = 344;
const DETAIL_MIGRATION_TIP = "请手动绘制蒙版，以指定需要还原的区域，否则无法开始生成。(点击图片右侧按钮)";
const MASK_SAVED_TIP = "请确保蒙版区域覆盖你想还原的区域。";

const DetailMigrationPage = ({ isLoggedIn, userId }) => {
  const containerRef = useRef(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 添加TaskContext的使用
  const { updateTask } = useTaskContext();

  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [processedImages, setProcessedImages] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [clothingPanels, setClothingPanels] = useState([]);
  const [modelPanels, setModelPanels] = useState([]);
  const [currentReuploadClothingPanelId, setCurrentReuploadClothingPanelId] = useState(null);
  const [currentReuploadModelPanelId, setCurrentReuploadModelPanelId] = useState(null);
  const [showModelUploadGuide, setShowModelUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [imageQuantity, setImageQuantity] = useState(2);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);

  const generationAreaRef = useRef(null);
  
  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });
  
  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);
  
  // 添加状态控制提示组件的显示和提示内容
  const [showTips, setShowTips] = useState(false);
  const [tipContent, setTipContent] = useState(DETAIL_MIGRATION_TIP);

  // 添加加载状态
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);

  // 添加随机种子相关状态
  const [useRandomSeed, setUseRandomSeed] = useState(true);  // 是否使用随机种子
  const [seed, setSeed] = useState(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));  // 种子值
  // 处理单张图片下载
  const handleDownloadImage = async (imageUrl, taskId, index) => {
    try {
      message.info('准备下载...');
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      const response = await fetch(httpsUrl);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `${taskId}_${parseInt(index) + 1}.jpg`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      message.error('下载失败');
      console.error('下载出错:', error);
    }
  };
  // 在服装原图上传成功后更新默认尺寸
  useEffect(() => {
    if (modelPanels.length > 0 && modelPanels[0].fileInfo) {
      // 移除与图片尺寸相关的代码
    }
  }, [modelPanels]);
  
  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);

  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 处理服装原图上传结果
  const handleClothingUploadResult = async (results) => {
    try {
      if (results.type === 'panels') {
        let newPanel;

        if (currentReuploadClothingPanelId) {
          const newComponentId = generateId(ID_TYPES.COMPONENT);
          newPanel = {
            ...results.panels[0],
            componentId: newComponentId,
            type: 'clothing',
            source: 'upload',
            file: results.panels[0].file
          };
          setClothingPanels(prevPanels => {
            if (prevPanels.length === 0) {
              return [newPanel];
            }
            return prevPanels.map(panel =>
              panel.componentId === currentReuploadClothingPanelId
                ? newPanel
                : panel
            );
          });
          setCurrentReuploadClothingPanelId(null);
        } else {
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            componentId: generateId(ID_TYPES.COMPONENT),
            type: 'clothing',
            source: 'upload',
            file: panel.file
          }));
          setClothingPanels(prevPanels => [...prevPanels, ...panelsWithType]);
          newPanel = panelsWithType[0];
        }
        // 设置未保存更改状态
        setHasUnsavedChanges(true);
        setShowTips(true);
        setTipContent(DETAIL_MIGRATION_TIP);
        // 不再自动弹出蒙版弹窗，改为点击面板右侧按钮弹出
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        setClothingPanels(prevPanels => 
          prevPanels.filter(panel => panel.status !== 'processing'));
        setCurrentReuploadClothingPanelId(null);
      }
    } catch (error) {
      console.error('处理服装原图上传结果时出错:', error);
      message.error('处理服装原图上传结果时出错: ' + (error.message || '未知错误'));
    }
  };

  // 处理服装原图上传
  const handleClothingFileUpload = (file) => {
    console.log('服装原图上传:', file);
    // 注意：这里不需要立即上传到服务器
    // 文件将在handleClothingUploadResult中处理，并在点击生成按钮时上传
  };

  // 处理模特图片上传
  const handleModelFileUpload = (file) => {
    console.log('模特图片上传:', file);
    // 注意：这里不需要立即上传到服务器
    // 文件将在handleModelUploadResult中处理，并在点击生成按钮时上传
  };

  // 处理删除服装原图面板
  const handleDeleteClothingPanel = (panelId) => {
    setClothingPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 处理重新上传服装原图
  const handleReuploadClothing = (panel) => {
    if (panel && panel.componentId) {
      // 第一步：删除当前面板
      handleDeleteClothingPanel(panel.componentId);
      setOperationsPanel(null);
      setCurrentReuploadClothingPanelId(panel.componentId);
      
      // 第二步：延迟一点点时间后触发上传区域点击（确保UI已更新）
      setTimeout(() => {
        // 如果已经没有面板了，上传框应该会显示出来，直接触发其点击事件
        const uploadBox = document.getElementById('clothing-upload-box');
        if (uploadBox) {
          uploadBox.click();
        }
      }, 50);
    }
  };

  // 处理服装原图状态变更
  const handleClothingStatusChange = (panelId, newStatus) => {
    setClothingPanels(prevPanels =>
      prevPanels.map(panel => 
        panel.componentId === panelId 
          ? { ...panel, status: newStatus }
          : panel
      )
    );
  };

  // 处理模特图片上传结果
  const handleModelUploadResult = async (results) => {
    try {
      if (results.type === 'panels') {
        let newPanel;

        if (currentReuploadModelPanelId) {
          const newComponentId = generateId(ID_TYPES.COMPONENT);
          newPanel = {
            ...results.panels[0],
            componentId: newComponentId,
            type: 'model',
            source: 'upload',
            file: results.panels[0].file
          };
          setModelPanels(prevPanels => {
            if (prevPanels.length === 0) {
              return [newPanel];
            }
            return prevPanels.map(panel =>
              panel.componentId === currentReuploadModelPanelId
                ? newPanel
                : panel
            );
          });
          setCurrentReuploadModelPanelId(null);
        } else {
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            componentId: generateId(ID_TYPES.COMPONENT),
            type: 'model',
            source: 'upload',
            file: panel.file
          }));
          setModelPanels(prevPanels => [...prevPanels, ...panelsWithType]);
          newPanel = panelsWithType[0];
        }
        // 设置未保存更改状态
        setHasUnsavedChanges(true);
        setShowTips(true);
        setTipContent(DETAIL_MIGRATION_TIP);
        // 不再自动弹出蒙版弹窗，改为点击面板右侧按钮弹出
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        setModelPanels(prevPanels => 
          prevPanels.filter(panel => panel.status !== 'processing'));
        setCurrentReuploadModelPanelId(null);
      }
    } catch (error) {
      console.error('处理模特图片上传结果时出错:', error);
      message.error('处理模特图片上传结果时出错: ' + (error.message || '未知错误'));
    }
  };

  // 处理删除模特图片面板
  const handleDeleteModelPanel = (panelId) => {
    setModelPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 处理重新上传模特图片
  const handleReuploadModel = (panel) => {
    if (panel && panel.componentId) {
      // 第一步：删除当前面板
      handleDeleteModelPanel(panel.componentId);
      setOperationsPanel(null);
      setCurrentReuploadModelPanelId(panel.componentId);
      
      // 第二步：延迟一点点时间后触发上传区域点击（确保UI已更新）
      setTimeout(() => {
        // 如果已经没有面板了，上传框应该会显示出来，直接触发其点击事件
        const uploadBox = document.getElementById('model-upload-box');
        if (uploadBox) {
          uploadBox.click();
        }
      }, 50);
    }
  };

  // 处理模特图片状态变更
  const handleModelStatusChange = (panelId, newStatus) => {
    setModelPanels(prevPanels =>
      prevPanels.map(panel => 
        panel.componentId === panelId 
          ? { ...panel, status: newStatus }
          : panel
      )
    );
  };

  // 构建服务器URL的辅助函数
  const buildServerUrl = (resultData, userId) => {
    // 获取当前用户ID
    const currentUserId = userId || getCurrentUserId() || 'developer';

    // 优先使用服务器返回的相对路径
    if (resultData.relativePath) {
      return `${process.env.NODE_ENV === 'development' 
        ? process.env.REACT_APP_BACKEND_URL 
        : ''}${resultData.relativePath}`;
    } else {
      // 使用文件名构建URL
      return `${process.env.NODE_ENV === 'development' 
        ? process.env.REACT_APP_BACKEND_URL 
        : ''}/storage/${currentUserId}/uploads/${resultData.serverFileName}`;
    }
  };
  function drawBrushOnImageByMask(imageUrl, maskUrl, brushColor = [255, 0, 0, 128], outputName = 'brushed.png') {
    if (!imageUrl || !maskUrl) {
      console.error('图像地址不能为空');
      return;
    }

    const img = new Image();
    const mask = new Image();

    img.crossOrigin = 'anonymous';
    mask.crossOrigin = 'anonymous';

    let imgLoaded = false;
    let maskLoaded = false;

    img.onload = () => {
      imgLoaded = true;
      if (maskLoaded) drawBrushEffect();
    };

    mask.onload = () => {
      maskLoaded = true;
      if (imgLoaded) drawBrushEffect();
    };

    img.onerror = (err) => console.error('原图加载失败:', err);
    mask.onerror = (err) => console.error('蒙版图加载失败:', err);

    img.src = imageUrl;
    mask.src = maskUrl;

    function drawBrushEffect() {
      const width = img.width;
      const height = img.height;

      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');

      // 步骤 1：绘制原图
      ctx.drawImage(img, 0, 0);

      // 步骤 2：绘制 mask 到临时 canvas，获取像素数据
      const maskCanvas = document.createElement('canvas');
      maskCanvas.width = width;
      maskCanvas.height = height;
      const maskCtx = maskCanvas.getContext('2d');
      maskCtx.drawImage(mask, 0, 0);
      const maskData = maskCtx.getImageData(0, 0, width, height).data;

      // 步骤 3：叠加笔刷效果
      const brushImageData = ctx.getImageData(0, 0, width, height);
      const pixels = brushImageData.data;

      for (let i = 0; i < pixels.length; i += 4) {
        const r = maskData[i];
        const g = maskData[i + 1];
        const b = maskData[i + 2];
        const gray = (r + g + b) / 3;

        if (gray > 200) { // 认为是白色笔刷区域
          pixels[i] = brushColor[0];     // R
          pixels[i + 1] = brushColor[1]; // G
          pixels[i + 2] = brushColor[2]; // B
          pixels[i + 3] = brushColor[3]; // A (0-255)
        }
      }

      ctx.putImageData(brushImageData, 0, 0);

      // 步骤 4：导出
      canvas.toBlob((blob) => {
        if (!blob) {
          console.error('导出失败');
          return;
        }

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = outputName;
        a.click();
        URL.revokeObjectURL(url);
      }, 'image/png');
    }
  }

  // 检查任务状态
  const checkTaskStatus = async (taskId) => {
    try {
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId() || 'developer';

      // 使用taskService获取任务详情
      const task = await getTaskById(taskId, currentUserId);

      if (task) {
        if (task.status === 'completed') {
          // 任务完成，获取生成的图片
          setGeneratedImages(task.results || []);
          setIsGenerating(false);
          message.success('图片生成成功');
        } else if (task.status === 'failed') {
          // 任务失败
          setIsGenerating(false);
          message.error('生成失败: ' + (task.errorMessage || '未知错误'));
        } else {
          // 任务仍在进行中，继续轮询
          setTimeout(() => checkTaskStatus(taskId), 3000);
        }
      } else {
        setIsGenerating(false);
        message.error('获取任务状态失败: 任务不存在');
      }
    } catch (error) {
      console.error('获取任务状态失败:', error);
      setIsGenerating(false);
      message.error('获取任务状态失败: ' + (error.message || '未知错误'));
    }
  };

  function generateAlphaMaskFromBrush(maskUrl, outputName = 'alpha-mask.png') {
    return new Promise((resolve, reject) => {
      if (!maskUrl) {
        console.error('必须提供蒙版图 URL');
        reject(new Error('必须提供蒙版图 URL'));
        return;
      }

      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        const width = img.width;
        const height = img.height;

        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');

        ctx.drawImage(img, 0, 0);
        const imageData = ctx.getImageData(0, 0, width, height);
        const pixels = imageData.data;

        for (let i = 0; i < pixels.length; i += 4) {
          const r = pixels[i];
          const g = pixels[i + 1];
          const b = pixels[i + 2];
          const gray = (r + g + b) / 3;

          // 设置 RGB 为 255（白色），Alpha 为 灰度值（白笔刷=255，黑背景=0）
          pixels[i] = 255;
          pixels[i + 1] = 255;
          pixels[i + 2] = 255;
          pixels[i + 3] = gray;
        }

        ctx.putImageData(imageData, 0, 0);

        canvas.toBlob((blob) => {
          if (!blob) {
            console.error('导出失败');
            reject(new Error('导出失败'));
            return;
          }

          // 下载功能
          // const url = URL.createObjectURL(blob);
          // const a = document.createElement('a');
          // a.href = url;
          // a.download = outputName;
          // a.click();
          // URL.revokeObjectURL(url);

          // 返回 File 对象（可选，也可以只返回 Blob）
          const file = new File([blob], outputName, { type: 'image/png' });
          resolve(file);
        }, 'image/png');
      };

      img.onerror = (err) => {
        console.error('蒙版加载失败:', err);
        reject(err);
      };

      img.src = maskUrl;
    });
  }

  // 处理开始生成按钮点击
  const handleGenerate = async () => {
    setIsProcessing(true);
    setIsGenerating(true);
    setSeed(useRandomSeed? (Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)) : seed);

    // 声明taskData变量在try块外面，以便catch块可以访问
    let taskData;

    try {
      // 校验服装原图面板
      if (clothingPanels.length === 0) {
        message.error('请先上传服装原图');
        setIsGenerating(false);
        return;
      }
      if (!clothingPanels.every(panel => panel.status === 'completed')) {
        message.error('服装原图处理尚未完成，请稍后再试');
        setIsGenerating(false);
        return;
      }
      if (!clothingPanels[0].hasMask) {
        message.error('请先绘制服装原图蒙版，以指定需要还原的区域');
        setCurrentMaskPanel(clothingPanels[0]);
        setShowMaskDrawModal(true);
        setIsGenerating(false);
        return;
      }
      // 校验模特图片面板
      if (modelPanels.length === 0) {
        message.error('请先上传模特图片');
        setIsGenerating(false);
        return;
      }
      if (!modelPanels.every(panel => panel.status === 'completed')) {
        message.error('模特图片处理尚未完成，请稍后再试');
        setIsGenerating(false);
        return;
      }
      if (!modelPanels[0].hasMask) {
        message.error('请先绘制模特图片蒙版，以指定需要还原的区域');
        setCurrentMaskPanel(modelPanels[0]);
        setShowMaskDrawModal(true);
        setIsGenerating(false);
        return;
      }
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId() || 'developer';
      const balance = await checkUserBalance('细节还原', 'detail-migration', imageQuantity);
      if(balance.code !== 200){
        message.error(balance.message);
        setIsGenerating(false);
        return;
      }
      // 处理可能的自定义上传的服装原图片
      let clothingToUse = { ...clothingPanels[0] };

      // 从TaskPanel拖拽过来的服装原图也需要上传到服务器
      if ((clothingPanels[0].file && clothingPanels[0].source === 'upload') ||
          (clothingPanels[0].source === 'upload' && !clothingPanels[0].file)) {
        // 显示上传中提示
        message.loading('正在上传服装原图...', 0);

        try {
          let fileToUpload = clothingPanels[0].file;
          // 如果没有file对象但有URL，需要从URL获取文件
          if (!fileToUpload && clothingPanels[0].url) {
            try {
              const response = await fetch(clothingPanels[0].url);
              const blob = await response.blob();
              fileToUpload = new File([blob], clothingPanels[0].serverFileName || 'clothing.jpg', {
                type: blob.type || 'image/jpeg'
              });
            } catch (error) {
              console.error('从URL获取服装原图文件失败:', error);
              message.error('服装原图处理失败，请重试');
              setIsProcessing(false);
              setIsGenerating(false);
              return;
            }
          }
          // 上传到服务器
          const { urls, fileInfos } = await uploadFiles([fileToUpload], 'detail-migration');
          // 上传成功后，使用服务器返回的URL更新图片对象
          if (urls) {
            clothingToUse = {
              ...clothingPanels[0],
              url: urls[0],
              serverFileName: fileInfos[0].name,
              originalImage: clothingPanels[0].url,
              originalUrl: urls[0],
              image: urls[0],
              processedFile: urls[0],
              source: 'history',
              file: undefined,
              fileInfo: fileInfos[0]
            };
            message.success('服装原图上传成功');
          } else {
            message.error('服装原图上传失败');
            setIsGenerating(false);
            return;
          }
        } catch (error) {
          console.error('上传服装原图时出错:', error);
          message.error('服装原图上传失败: ' + (error.message || '未知错误'));
          setIsProcessing(false);
          setIsGenerating(false);
          return;
        } finally {
          message.destroy();
        }
      }

      // 处理可能的自定义上传的模特图片
      let modelToUse = { ...modelPanels[0] };

      // 检查是否有需要上传的模特图片（通过file属性和source属性判断）
      if (modelPanels[0].file && modelPanels[0].source === 'upload') {
        // 显示上传中提示
        message.loading('正在上传模特图片...', 0);

        try {
          // 将文件上传到服务器 - 关键步骤：此时才真正上传图片
          const { urls,fileInfos } = await uploadFiles(
            [modelPanels[0].file],
            'detail-migration'
          );

          // 上传成功后，使用服务器返回的URL更新图片对象
          if (urls) {
            modelToUse = {
              ...modelPanels[0],
              url: urls[0],
              serverFileName: fileInfos[0].name,
              originalImage: urls[0],
              image: urls[0], // 同时设置image字段与url保持一致
              processedFile: urls[0], // 添加processedFile字段，与url保持一致
              source: 'history',  // 修改图片来源为历史记录，表示已在服务器上
              fileInfo: fileInfos[0]
            };
            message.success('模特图片上传成功');
          } else {
            message.error('模特图片上传失败');
            setIsProcessing(false);
          setIsGenerating(false);
          return;
          }
        } catch (error) {
          console.error('上传模特图片时出错:', error);
          message.error('模特图片上传失败: ' + (error.message || '未知错误'));
          setIsProcessing(false);
          setIsGenerating(false);
          return;
        } finally {
          // 关闭上传中提示
          message.destroy();
        }
      }

      // 生成唯一任务ID
      const taskId = generateId(ID_TYPES.TASK);

      // 创建任务数据对象，使用处理后的设计图URL
      const taskData = {
        taskId: taskId,
        userId: currentUserId,
        createdAt: new Date(),
        status: 'processing',
        imageCount: imageQuantity, // 使用imageQuantity而不是固定值
        taskType: 'detail-migration',
        pageType: 'detail-migration',
        seed: seed,
        // 使用数组形式的组件结构
        components: [
          {
            componentType: 'clothingMaskPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            isMainImage: true,
            name: clothingToUse.title || '服装原图',
            serverFileName: clothingToUse.serverFileName,
            url: clothingToUse.url, // 使用更新后的URL（可能是服务器URL）
            fileInfo: clothingToUse.fileInfo ? {
              ...clothingToUse.fileInfo,
              // 确保size属性是数字类型
              size: typeof clothingToUse.fileInfo.size === 'string' ?
                parseFloat(clothingToUse.fileInfo.size.replace(/[^\d.]/g, '')) :
                clothingToUse.fileInfo.size,
              // 确保fileInfo中也设置serverFileName
              serverFileName: clothingToUse.serverFileName
            } : { serverFileName: clothingToUse.serverFileName },
            status: 'completed',
            originalImage: clothingToUse.originalImage || clothingToUse.url,
            // 蒙版信息
            hasMask: true,
            maskPath: clothingToUse.maskPath,
            maskFileName: clothingToUse.maskFileName
          },
          {
            componentType: 'modelMaskPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: modelToUse.title || '模特图片',
            serverFileName: modelToUse.serverFileName,
            url: modelToUse.url, // 使用更新后的URL（可能是服务器URL）
            fileInfo: modelToUse.fileInfo ? {
              ...modelToUse.fileInfo,
              // 确保size属性是数字类型
              size: typeof modelToUse.fileInfo.size === 'string' ?
                parseFloat(modelToUse.fileInfo.size.replace(/[^\d.]/g, '')) :
                modelToUse.fileInfo.size,
              // 确保fileInfo中也设置serverFileName
              serverFileName: modelToUse.serverFileName
            } : { serverFileName: modelToUse.serverFileName },
            status: 'completed',
            originalImage: modelToUse.originalImage || modelToUse.url,
            // 蒙版信息
            hasMask: true,
            maskPath: modelToUse.maskPath,
            maskFileName: modelToUse.maskFileName
          },
          {
            componentType: 'quantityPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '生成数量', 
            quantity: imageQuantity
          },
          {
            componentType: 'randomSeedSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '随机种子',
            status: 'completed',
            useRandom: false,
            value: seed
          }
        ],
        // 初始化生成图片数组
        generatedImages: Array(1).fill(null).map((_, index) => ({
          imageIndex: index,
          status: 'processing'
        })),
        processInfo:{
          results:[]
        }
      };
      
      setIsGenerating(true);
      if (generationAreaRef.current && generationAreaRef.current.setGenerationTasks) {
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      }
      // 先添加到本地状态，使UI立即响应
      try {
        // 创建工作流任务
        await createFlowTask(taskData);
        
        // 处理服装原图蒙版
        const clothingImg = await processMask(clothingToUse.url,clothingToUse.maskPath)
        if(clothingImg.output_url==""){
          message.error('蒙版处理失败，检查图片格式信息是否正确');
          setIsGenerating(false);
          return;
        }
        const clothing_output_url = clothingImg.output_url
    
        
        // 处理模特图片蒙版
        const modelImg = await processMask(modelToUse.url,modelToUse.maskPath)
        if(modelImg.output_url==""){
          message.error('蒙版处理失败，检查图片格式信息是否正确');
          setIsGenerating(false);
          return;
        }
        const model_output_url = modelImg.output_url

        // 执行工作流
        const resultData = await executeFlow(WORKFLOW_NAME.DETAIL_MIGRATION,{
          "76": {
              "url":model_output_url
            },
          "75":{
            "url":clothing_output_url
            },
            "27":{
            "seed": seed
            },
            "28":{
              "amount":imageQuantity
            },
            "subInfo":{
            "type": "detail-migration",
            "title":"细节还原",
            "count":imageQuantity
          }
        },taskData.taskId);
        setIsProcessing(false);
        setIsGenerating(false);
        setHasUnsavedChanges(false); 
        // 更新任务状态为成功
      if( generationAreaRef.current){
 
                taskData.promptId = resultData.promptId;
        taskData.instanceId = resultData.instanceId;
        taskData.url = resultData.url;
        taskData.newTask = true;
        taskData.netWssUrl=resultData.netWssUrl;
        taskData.clientId=resultData.clientId;
        generationAreaRef.current.setGenerationTasks(taskData);
    }
    } catch (error) {
        setIsProcessing(false);
        setIsGenerating(false);
      
        // 更新任务状态为失败
        taskData.status = 'failed';
        taskData.errorMessage = error.message;
        if( generationAreaRef.current){
          generationAreaRef.current.setGenerationTasks(taskData);
        }
        updateFlowTask(taskData.taskId,taskData);
        
        // 调用updateTask以触发失败提示音
        updateTask(taskData);
        
        // 更新任务列表
      }
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error('创建任务失败: ' + error.message);
      setIsGenerating(false);

      // 从状态中移除失败的任务，只有当taskData已定义时才执行
      if (taskData) {
        setGenerationTasks(prev => prev.filter(task => task.taskId !== taskData.taskId));
      }
    }
  };

  // 处理重新编辑任务
  const handleEditTask = (task) => {
    if (!task) return;

    try {
      console.log('编辑任务:', task);
      console.log('任务组件:', task.components);

      // 只处理数组结构，不再兼容对象结构
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);

      // 回填服装原图面板 - 包括蒙版状态
      const clothingComponent = components.find(c => c.componentType === 'clothingMaskPanel');
      if (clothingComponent) {
        console.log('获取到服装原图设计组件:', clothingComponent);
        const modelPanel = {
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT), // 确保id与componentId一致
          type: 'clothing',
          title: clothingComponent.title || clothingComponent.name || '服装原图',
          status: 'completed', // 确保状态为completed
          serverFileName: clothingComponent.serverFileName,
          url: clothingComponent.originalImage || clothingComponent.url, // 使用originalImage作为url
          processedFile: clothingComponent.processedFile,
          maskPath: clothingComponent.maskPath,
          fileInfo: clothingComponent.fileInfo ? {
            ...clothingComponent.fileInfo,
            // 确保size属性是数字类型
            size: typeof clothingComponent.fileInfo.size === 'string' ?
              parseFloat(clothingComponent.fileInfo.size.replace(/[^\d.]/g, '')) :
              clothingComponent.fileInfo.size,
            // 确保fileInfo中也设置serverFileName
            serverFileName: clothingComponent.serverFileName
          } : {
            size: 2500000, // 2.5MB对应的字节数
            width: 1024,
            height: 1024,
            format: 'image/jpeg',
            serverFileName: clothingComponent.serverFileName
          }, // 提供默认fileInfo，确保size为数字
          ...clothingComponent,
        };

        // 始终显示提示组件
        setShowTips(true);

        // 统一蒙版状态显示：都显示为需要手动绘制蒙版
        modelPanel.hasMask = false;

        // 如果原任务有蒙版，提示用户需要重新绘制蒙版
        if (clothingComponent.hasMask) {
          // 更新提示内容，告知用户需要重新绘制蒙版
          setTipContent('需重新绘制蒙版，否则无法开始生成。(点击服装原图右侧按钮)');
        } else {
          // 更新提示内容为默认提示
          setTipContent(DETAIL_MIGRATION_TIP);
        }

        setClothingPanels([modelPanel]);
      } else {
        console.warn('未找到模型组件，请检查任务数据');
        message.warning('无法找到服装原图数据');
      }

      // 回填模特图片面板
      const modelComponent = components.find(c => c.componentType === 'modelMaskPanel');
      if (modelComponent) {
        console.log('获取到模特图片组件:', modelComponent);
        const modelPanel = {
          componentId: generateId(ID_TYPES.COMPONENT),
          id: generateId(ID_TYPES.COMPONENT),
          type: 'model',
          title: modelComponent.title || modelComponent.name || '模特图片',
          status: 'completed',
          serverFileName: modelComponent.serverFileName,
          url: modelComponent.originalImage || modelComponent.url,
          processedFile: modelComponent.processedFile,
          maskPath: modelComponent.maskPath,
          fileInfo: modelComponent.fileInfo ? {
            ...modelComponent.fileInfo,
            size: typeof modelComponent.fileInfo.size === 'string' ?
              parseFloat(modelComponent.fileInfo.size.replace(/[^\d.]/g, '')) :
              modelComponent.fileInfo.size,
            serverFileName: modelComponent.serverFileName
          } : {
            size: 2500000,
            width: 1024,
            height: 1024,
            format: 'image/jpeg',
            serverFileName: modelComponent.serverFileName
          },
          ...modelComponent,
        };

        // 统一蒙版状态显示：都显示为需要手动绘制蒙版
        modelPanel.hasMask = false;

        setModelPanels([modelPanel]);
      } else {
        console.warn('未找到模特图片组件，请检查任务数据');
        message.warning('无法找到模特图片数据');
      }

      // 回填数量设置
      const quantityComponent = components.find(c => c.componentType === 'quantityPanel');
      if (quantityComponent?.value) {
        console.log('获取到数量组件:', quantityComponent);
        setImageQuantity(quantityComponent.value);
      } else if (task.imageCount) {
        // 回退：使用任务顶层属性
        console.log('使用任务级imageCount:', task.imageCount);
        setImageQuantity(task.imageCount);
      }

      // 回填种子设置
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      if (seedComponent) {
        console.log('获取到种子组件:', seedComponent);
        // 检查是否有实际使用的种子值
        if (seedComponent.value >= 0 && !seedComponent.useRandom) {
          // 如果存在实际使用的种子值，无论原来是否设置为随机，都回填为固定种子
        setUseRandomSeed(false);
          setSeed(seedComponent.value);
          console.log('回填固定种子值:', seedComponent.value);
        } else {
          // 如果没有固定的种子值，则保持原有设置
          setUseRandomSeed(seedComponent.useRandom);
          if (!seedComponent.useRandom && seedComponent.value !== -1) {
          setSeed(seedComponent.value);
        }
          console.log('回填种子设置:', seedComponent);
        }
      }
      // 回退1：检查任务本身是否有种子值
      else if (task.seed !== undefined && task.seed >= 0) {
        // 如果任务有有效种子值，回填为固定种子
        console.log('使用任务级种子:', task.seed);
        setUseRandomSeed(false);
        setSeed(task.seed);
      } else {
        // 回退2：使用默认随机种子
        console.warn('未找到种子组件，使用默认随机种子');
        setUseRandomSeed(true);
        setSeed(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));
      }
      
      // 切换到结果标签页
      setActiveTab('result');

      // 显示成功消息
      message.success({
        content: '配置已重新导入，可继续进行调整',
        duration: 5
      });

      // 添加种子回填相关日志
      console.log('种子回填信息:', {
        任务种子组件: seedComponent,
        任务级种子值: task.seed,
        当前种子状态: {
          useRandomSeed: useRandomSeed,
          seedValue: seed
        }
      });
    } catch (error) {
      console.error('处理编辑任务时出错:', error);
      message.error('加载任务设置失败');
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleViewDetails = (image, task, index) => {
    try {
      console.log('查看图片详情:', image);
      console.log('任务数据:', task);

      // 只处理数组结构，不再兼容对象结构
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);

      // 先预加载图片
      const preloadImages = () => {
        // 获取服装原图组件
        const clothingComponent = components.find(c => c.componentType === 'clothingMaskPanel');

        // 预加载原图 (仅使用组件数据)
        if (clothingComponent?.originalImage) {
          const originalImg = new Image();
          originalImg.src = clothingComponent.originalImage;
          console.log('预加载服装原图图片:', clothingComponent.originalImage);
        }

        // 预加载生成图
        if (image.url) {
          const resultImg = new Image();
          resultImg.src = image.url;
          console.log('预加载结果图:', image.url);
        }
      };

      // 开始预加载
      // preloadImages();
      
      // 获取所需组件数据
      const clothingComponent = components.find(c => c.componentType === 'clothingMaskPanel');
      const randomSeedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      
      console.log('查看任务详情 - 获取的组件:', {
        clothingComponent,
        randomSeedComponent,
      });

      // ===== 种子值处理逻辑，严格按照规范优先级 =====
      // 1. 优先使用任务级seed
      // 2. 其次使用组件seed值（如果存在且>=0）
      // 3. 最后才使用图片级seed
      const taskSeed = task.seed !== undefined && task.seed >= 0 ? task.seed :
                     (randomSeedComponent?.value !== undefined && randomSeedComponent.value >= 0 ?
                      randomSeedComponent.value : (image.seed || 0));

      // 准备适配后的组件数据 - 使用数组格式
      const adaptedComponents = [
        // 服装原图组件
        clothingComponent ? {
          ...clothingComponent,
          componentType: 'clothingMaskPanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          // 确保originalImage和url属性存在且有效
          originalImage: clothingComponent.originalImage || clothingComponent.url,
          url: clothingComponent.url || clothingComponent.originalImage,
          // 确保serverFileName被正确传递
          serverFileName: clothingComponent.serverFileName
        } : null,
        // 随机种子组件
        {
        componentType: 'randomSeedSelector',
          componentId: generateId(ID_TYPES.COMPONENT),
          useRandom: randomSeedComponent?.useRandom || false,
        value: taskSeed
        }
      ].filter(Boolean); // 过滤掉null值

      // 先设置基础数据，避免阻塞UI
      setSelectedImage({
        ...image,
        taskId: task.taskId,
        createdAt: task.createdAt,
        components: adaptedComponents,
        // 确保图片对象也包含同样的种子值，与任务级保持一致
        seed: taskSeed,
      });


      // 在下一个微任务中设置其他初始状态
      // queueMicrotask(() => {
      //   // 初始化图片位置和缩放状态
      //   setImagePosition({ x: 0, y: 0 });
      //   setIsDragging(false);
      //   lastPosition.current = { x: 0, y: 0 };
      //   setImageScale(100);
      //   setInitialScale(100);
      //
      //   // 初始化预览状态
      //   setPreviewImage(null);
      //   setShowPreviewModal(false);
      // });
      
     return {
      ...task,
        seed: taskSeed,
        // 添加适配后的组件数据供ImageDetailsModal使用
      adaptedComponents
    }
    } catch (error) {
      console.error('处理查看图片详情时出错:', error);
      message.error('查看图片详情失败');
    }
  };

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);
    
    // 重置状态，无需延迟
    setSelectedImage(null);
    // 重置任务信息，避免保留旧任务导致重新打开时出错
    setImageDetailsTask(null);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);
    

  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');
      
      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        // 预览弹窗已移除，无需处理
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;
    
    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;
    
    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;
    
    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }
    
    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };

  // 处理缩放变化
  const handleScaleChange = (newScale) => {
    setImageScale(newScale);
  };

  // 添加复位处理函数
  const handleReset = () => {
    setImageScale(initialScale);
    setImagePosition({ x: 0, y: 0 });
    lastPosition.current = { x: 0, y: 0 };
  };

  const [uploadedClothingId, setUploadedClothingId] = useState(null);
  const [uploadedClothingUrl, setUploadedClothingUrl] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState(null);
  const [generatedImages, setGeneratedImages] = useState([]);
  const [showMaskDrawModal, setShowMaskDrawModal] = useState(false);
  const [currentMaskPanel, setCurrentMaskPanel] = useState(null);

  // 处理打开蒙版绘制弹窗
  const handleDrawMask = (panel) => {
    setCurrentMaskPanel(panel);
    console.log('currentMaskPanel', currentMaskPanel);
    
    setShowMaskDrawModal(true);
  };

  // 处理保存蒙版
  const handleSaveMask = async (maskData, panelId, savePath) => {
    // 如果maskData为null，表示用户想要删除蒙版
    if (maskData === null) {
      console.log(`删除蒙版数据，面板ID: ${panelId}`);
      // 判断panelId属于哪个面板
      if (clothingPanels.some(panel => panel.componentId === panelId)) {
        setClothingPanels(prevPanels =>
          prevPanels.map(panel =>
            panel.componentId === panelId
              ? {
                  ...panel,
                  hasMask: false,
                  maskData: null,
                  maskPath: null,
                  maskFileName: null
                }
              : panel
          )
        );
      } else {
        setModelPanels(prevPanels =>
          prevPanels.map(panel =>
            panel.componentId === panelId
              ? {
                  ...panel,
                  hasMask: false,
                  maskData: null,
                  maskPath: null,
                  maskFileName: null
                }
              : panel
          )
        );
      }
      // 恢复默认提示
      setTipContent(DETAIL_MIGRATION_TIP);
      message.success({
        content: '蒙版已删除',
        duration: 2
      });
      return;
    }
    // 创建文件名 (使用面板ID + 时间戳，确保唯一性)
    const timestamp = Date.now();
    const fileName = `mask_${panelId}_${timestamp}.png`;
    console.log(`生成的蒙版文件名: ${fileName}`);
    // 显示处理中消息
    message.loading({
      content: '正在保存蒙版...',
      key: 'maskSave'
    });
    try {
      const file =await generateAlphaMaskFromBrush(maskData)
      const data = await uploadFiles([file],"detail-migration");
      // 先更新本地状态，确保UI响应良好
      updateLocalMaskState(panelId, maskData, data.fileInfos[0].url , fileName);
      if (data) {
        message.success({
          content: '蒙版保存成功',
          key: 'maskSave',
          duration: 2
        });
        // 更新服装原图面板或模特图片面板的蒙版路径信息，使用服务器返回的路径
        if (clothingPanels.some(panel => panel.componentId === panelId)) {
          setClothingPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === panelId
                ? {
                    ...panel,
                    hasMask: true,
                    maskPath: data.fileInfos[0].url,
                    maskFileName: fileName
                  }
                : panel
            )
          );
        } else {
          setModelPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === panelId
                ? {
                    ...panel,
                    hasMask: true,
                    maskPath: data.fileInfos[0].url,
                    maskFileName: fileName
                  }
                : panel
            )
          );
        }
      } else {
        message.error({
          content: data.message || '服务器拒绝了蒙版保存请求',
          key: 'maskSave',
          duration: 3
        });
        console.error('蒙版上传失败:', data);
      }
    } catch (error) {
      console.error('处理蒙版数据时出错:', error);
      message.error({
        content: '处理蒙版数据时出错',
        key: 'maskSave',
        duration: 3
      });
    }
    // 更新提示内容
    setTipContent(MASK_SAVED_TIP);
  };

  // 辅助函数：更新本地蒙版状态
  const updateLocalMaskState = (panelId, maskData, path, fileName) => {
    if (clothingPanels.some(panel => panel.componentId === panelId)) {
      setClothingPanels(prevPanels =>
        prevPanels.map(panel =>
          panel.componentId === panelId
            ? {
                ...panel,
                hasMask: true,
                maskData,
                maskPath: path,
                maskFileName: fileName
              }
            : panel
        )
      );
    } else {
      setModelPanels(prevPanels =>
        prevPanels.map(panel =>
          panel.componentId === panelId
            ? {
                ...panel,
                hasMask: true,
                maskData,
                maskPath: path,
                maskFileName: fileName
              }
            : panel
        )
      );
    }
  };
  
  // 辅助函数: Base64转Blob
  const base64ToBlob = (base64, mimeType) => {
    const byteCharacters = atob(base64);
    const byteArrays = [];
    
    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512);
      
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      
      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    
    return new Blob(byteArrays, { type: mimeType });
  };

  // 创建缩略图并保存到历史记录
  const createThumbnailAndSaveToHistory = (file, localUrl, serverUrl, fileName, imageType, pageType) => {
    try {
      console.log('创建缩略图并保存到历史记录:', fileName);

      // 创建图片对象用于加载文件
      const img = new Image();
      img.onload = () => {
        try {
          // 创建canvas用于绘制缩略图
          const canvas = document.createElement('canvas');

          // 设置缩略图最大尺寸
          const MAX_THUMBNAIL_SIZE = 400;

          // 计算缩放比例
          let width = img.width;
          let height = img.height;
          const aspectRatio = width / height;

          if (width > height) {
            // 横向图片
            if (width > MAX_THUMBNAIL_SIZE) {
              width = MAX_THUMBNAIL_SIZE;
              height = width / aspectRatio;
            }
          } else {
            // 纵向图片
            if (height > MAX_THUMBNAIL_SIZE) {
              height = MAX_THUMBNAIL_SIZE;
              width = height * aspectRatio;
            }
          }

          // 设置canvas尺寸
          canvas.width = width;
          canvas.height = height;

          // 绘制缩放后的图片
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0, width, height);

          // 输出为低质量的JPEG (质量0.6)
          const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.6);

          // 计算缩略图大小（KB）
          const base64Data = thumbnailDataUrl.split(',')[1];
          const byteSize = atob(base64Data).length;
          const kiloByteSize = byteSize / 1024;

          console.log(`缩略图创建成功，原始大小: ${img.width}x${img.height}, 缩略图大小: ${width}x${height}, 文件大小: ${kiloByteSize.toFixed(2)}KB`);

          // 保存到历史记录
          const success = saveToHistory({
            id: Date.now().toString(),
            url: localUrl,
            thumbnailUrl: thumbnailDataUrl,
            serverUrl: serverUrl,
            fileName: fileName,
            type: imageType,
            pageType: pageType,
            fileType: file.type,
            saveTime: Date.now()
          });

          if (success) {
            console.log(`历史记录保存成功, 文件名: ${fileName}`);
          } else {
            console.warn(`历史记录保存失败, 文件名: ${fileName}`);
          }
        } catch (error) {
          console.error('创建缩略图过程中出错:', error);
        }
      };

      img.onerror = (error) => {
        console.error('加载图片出错:', error);
      };

      // 使用本地URL加载图片
      img.src = localUrl;
    } catch (error) {
      console.error('处理图片文件时出错:', error);
    }
  };

  // 保存到历史记录，返回布尔值而非Promise
  const saveToHistory = (record) => {
    // 使用更明确的类型定义方式，避免不一致
    if (!record || !record.fileName || !record.serverUrl || typeof record.serverUrl !== 'string' || record.serverUrl.trim() === '') {
      console.warn('记录无效或没有服务器URL，无法保存到历史记录');
      return false;
    }
    
    try {
      const MAX_HISTORY_COUNT = 10;
      let history = [];
      // 使用更明确的类型定义方式，避免不一致
      const imageType = record.type || 'clothing';
      const pageType = record.pageType || 'detail-migration';
      const historyKey = `upload_history_${imageType}_${pageType}`;
      console.log('保存历史记录，使用键值:', historyKey);

      try {
        const historyJson = localStorage.getItem(historyKey);
        if (historyJson) {
          history = JSON.parse(historyJson);
          if (!Array.isArray(history)) {
            console.warn('历史记录格式无效，重置为空数组');
            history = [];
          }
        }
      } catch (e) {
        console.warn('解析历史记录JSON失败，重置为空数组', e);
        history = [];
      }

      // 检查是否已存在相同服务器URL的记录
      const existingIndex = history.findIndex(item =>
        item.serverUrl === record.serverUrl &&
        (item.type === record.type || (!item.type && !record.type)) &&
        (item.pageType === record.pageType || (!item.pageType && !record.pageType))
      );

      if (existingIndex !== -1) {
        console.log(`找到已存在的记录 [${existingIndex}]，服务器URL: ${record.serverUrl}，更新而不是添加新记录`);

        // 保留原始ID，更新其他信息
        record.id = history[existingIndex].id;

        // 从历史记录中移除旧记录
        history.splice(existingIndex, 1);
      } else {
        console.log(`未找到相同服务器URL的记录，添加新记录: ${record.serverUrl}`);

        // 如果记录已经达到最大数量，移除最早的记录
        if (history.length >= MAX_HISTORY_COUNT) {
          const removed = history.pop();
          console.log(`历史记录达到最大限制 ${MAX_HISTORY_COUNT}，移除最旧记录: ${removed?.fileName || '未知'}`);
        }
      }

      // 删除文件对象，避免序列化错误
      const recordToSave = { ...record };
      delete recordToSave.file;

      // 将记录添加到数组开头（最新的在前面）
      history.unshift(recordToSave);

      // 保存回localStorage
      localStorage.setItem(historyKey, JSON.stringify(history));
      console.log(`成功保存历史记录，当前共有 ${history.length} 条记录`);

      // 验证保存是否成功
      const verifyRecords = localStorage.getItem(historyKey);
      if (verifyRecords) {
        const parsed = JSON.parse(verifyRecords);
        console.log(`验证保存：记录已成功保存，当前共有${parsed.length}条记录`);
      }

      return true;
    } catch (err) {
      console.error('保存到历史记录出错:', err);
      return false;
    }
  };

  function applyMaskAsAlpha(originalUrl, maskUrl, outputName = 'output_direct.png') {
    if (!originalUrl || !maskUrl) {
      console.error('必须提供原始图像和蒙版图像的 URL');
      return Promise.reject(new Error('缺少必要的图像 URL'));
    }

    return new Promise((resolve, reject) => {
      const originalImg = new Image();
      const maskImg = new Image();

      originalImg.crossOrigin = 'anonymous';
      maskImg.crossOrigin = 'anonymous';

      let originalLoaded = false;
      let maskLoaded = false;

      const processImages = () => {
        if (!originalLoaded || !maskLoaded) return;

        try {
          const canvas = document.createElement('canvas');
          canvas.width = originalImg.width;
          canvas.height = originalImg.height;
          const ctx = canvas.getContext('2d');

          // 绘制原始图像
          ctx.drawImage(originalImg, 0, 0);
          const originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

          // 创建临时画布处理蒙版
          const maskCanvas = document.createElement('canvas');
          maskCanvas.width = maskImg.width;
          maskCanvas.height = maskImg.height;
          const maskCtx = maskCanvas.getContext('2d');

          // 绘制蒙版图像
          maskCtx.drawImage(maskImg, 0, 0);
          const maskImageData = maskCtx.getImageData(0, 0, maskCanvas.width, maskCanvas.height);

          // 应用蒙版的 alpha 通道到原始图像
          const originalPixels = originalImageData.data;
          const maskPixels = maskImageData.data;

          // 确保两个图像尺寸一致，如果不一致则缩放蒙版
          let finalMaskPixels = maskPixels;
          if (maskImg.width !== originalImg.width || maskImg.height !== originalImg.height) {
            // 重新缩放蒙版到原始图像尺寸
            maskCanvas.width = originalImg.width;
            maskCanvas.height = originalImg.height;
            maskCtx.drawImage(maskImg, 0, 0, originalImg.width, originalImg.height);
            finalMaskPixels = maskCtx.getImageData(0, 0, originalImg.width, originalImg.height).data;
          }

          // 将蒙版的 alpha 通道应用到原始图像
          for (let i = 0; i < originalPixels.length; i += 4) {
            // 获取蒙版对应像素的 alpha 值
            const maskAlpha = finalMaskPixels[i + 3];
            // 将蒙版的 alpha 值应用到原始图像
            originalPixels[i + 3] = maskAlpha;
          }

          // 将处理后的图像数据放回画布
          ctx.putImageData(originalImageData, 0, 0);

          // 导出为 PNG 格式
          canvas.toBlob((blob) => {
            if (!blob) {
              reject(new Error('导出图像失败'));
      return;
    }
    
            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = outputName;
            a.click();
            URL.revokeObjectURL(url);

            resolve(true);
          }, 'image/png');

        } catch (error) {
          reject(error);
        }
      };

      originalImg.onload = () => {
        originalLoaded = true;
        processImages();
      };

      maskImg.onload = () => {
        maskLoaded = true;
        processImages();
      };

      originalImg.onerror = (err) => {
        reject(new Error('原始图像加载失败: ' + err));
      };

      maskImg.onerror = (err) => {
        reject(new Error('蒙版图像加载失败: ' + err));
      };

      originalImg.src = originalUrl;
      maskImg.src = maskUrl;
    });
  }

    return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="细节还原功能">
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <div className="detail-migration-page">
        <div className="detail-migration-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isGenerating}
            featureName="detail-migration"
            quantity={imageQuantity}
          >
            {/* 服装原图上传区域 */}
            {clothingPanels.length === 0 ? (
              <UploadBox_Model
                id="clothing-upload-box"
                title="服装原图"
                accept="image/*"
                maxSize={UPLOAD_CONFIG.maxSize}
                onUpload={handleClothingFileUpload}
                onUploadResult={handleClothingUploadResult}
                showInDevelopment={true}
                reuploadFor={currentReuploadClothingPanelId}
                pageType="detail-migration"
                uploadType="clothing"
              />
            ) : (
              clothingPanels.map((panel) => (
                <ClothingMaskPanel
                  key={panel.componentId}
                  panel={panel}
                  onExpandClick={(panel, position) => {
                    const enhancedPanel = {
                      ...panel,
                      isEnhancedModelPanel: true,
                      processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                      fileInfo: panel.fileInfo ? {
                        ...panel.fileInfo,
                        size: typeof panel.fileInfo.size === 'string' ?
                          parseFloat(panel.fileInfo.size.replace(/[^\d.]/g, '')) :
                          panel.fileInfo.size
                      } : {
                        size: 500000,
                        format: 'image/jpeg',
                        type: 'image/jpeg'
                      }
                    };
                    setOperationsPanel({
                      panel: enhancedPanel,
                      position
                    });
                  }}
                  onDelete={handleDeleteClothingPanel}
                  onReupload={handleReuploadClothing}
                  onStatusChange={handleClothingStatusChange}
                  isEnhanced={true}
                  pageType="detail-migration"
                />
              ))
            )}

            {/* 模特图片上传区域 */}
            {modelPanels.length === 0 ? (
              <UploadBox_Model
                id="model-upload-box"
                title="模特图片"
                accept="image/*"
                maxSize={UPLOAD_CONFIG.maxSize}
                onUpload={handleModelFileUpload}
                onUploadResult={handleModelUploadResult}
                showInDevelopment={true}
                reuploadFor={currentReuploadModelPanelId}
                pageType="detail-migration"
                uploadType="model"
              />
            ) : (
              modelPanels.map((panel) => (
                <ModelMaskPanel
                  key={panel.componentId}
                  panel={panel}
                  onExpandClick={(panel, position) => {
                    const enhancedPanel = {
                      ...panel,
                      isEnhancedModelPanel: true,
                      processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                      fileInfo: panel.fileInfo ? {
                        ...panel.fileInfo,
                        size: typeof panel.fileInfo.size === 'string' ?
                          parseFloat(panel.fileInfo.size.replace(/[^\d.]/g, '')) :
                          panel.fileInfo.size
                      } : {
                        size: 500000,
                        format: 'image/jpeg',
                        type: 'image/jpeg'
                      }
                    };
                    setOperationsPanel({
                      panel: enhancedPanel,
                      position
                    });
                  }}
                  onDelete={handleDeleteModelPanel}
                  onReupload={handleReuploadModel}
                  onStatusChange={handleModelStatusChange}
                  isEnhanced={true}
                  pageType="detail-migration"
                />
              ))
            )}
                    
            {/* 提示信息面板 - 只在上传服装原图后显示 */}
            {modelPanels.length > 0 && (
                      <TipsPanel
                        tipContent={tipContent}
                      />
                    )}

                                {/* 随机种子选择器 */}
            <RandomSeedSelector
              onRandomChange={setUseRandomSeed}
              onSeedChange={setSeed}
              defaultRandom={useRandomSeed}
              defaultSeed={seed}
              // 查看详情时显示历史种子但保持可编辑
              isEdit={false}
              editSeed={selectedImage?.seed || null}
            />

                    {/* 数量面板 */}
                    <QuantityPanel
                      imageQuantity={imageQuantity}
                      onChange={setImageQuantity}
                    />
          </ControlPanel>

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />
          <GenerationArea
          ref={generationAreaRef}
            activeTab={activeTab}
            setIsProcessing={setIsGenerating}
            onTabChange={setActiveTab}
            tasks={generationTasks}
            onEditTask={handleEditTask}
            onDownloadImage={handleDownloadImage}
            onViewDetails={handleViewDetails}
            pageType="detail-migration"
          />
        </div>

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={
              operationsPanel.panel.type === 'clothing'
                ? handleDeleteClothingPanel
                : handleDeleteModelPanel
            }
            onReupload={
              operationsPanel.panel.type === 'clothing'
                ? handleReuploadClothing
                : handleReuploadModel
            }
            onDrawMask={handleDrawMask}
            pageType="detail-migration"
          />
        )}

        {/* 添加 ImageDetailsModal 组件 - 使用懒加载 */}
        {showImageDetails && selectedImage ? (
          <MemoizedImageDetailsModal
            selectedImage={selectedImage}
            onClose={handleCloseImageDetails}
            generationTasks={generationTasks}
            onEditTask={handleEditTask}
            pageType="detail-migration"
          />
        ) : null}



        {/* 显示上传的服装原图和生成按钮 */}
        {isGenerating && (
          <div className="generating-status">
            <Spin size="large" />
            <p>正在生成中，请稍候...</p>
          </div>
        )}

        {/* 显示生成的图片 */}
        {generatedImages && generatedImages.length > 0 && (
          <div className="generated-images">
            <h3>生成结果</h3>
            <div className="image-grid">
              {generatedImages.map((image, index) => (
                <div key={index} className="image-item">
                  <img src={image.url} alt={`生成图片 ${index + 1}`} />
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* 蒙版绘制弹窗 */}
        {showMaskDrawModal && currentMaskPanel && (
          <MaskDrawModal 
            isOpen={showMaskDrawModal}
            panel={currentMaskPanel}
            onClose={() => setShowMaskDrawModal(false)}
            onSaveMask={handleSaveMask}
            pageType="detail-migration"
            savePath={`server/storage/${userId || getCurrentUserId() || 'developer'}/style/detail-migration/mask`}
          />
        )}
      </div>
    </RequireLogin>
  );
};

export default DetailMigrationPage; 