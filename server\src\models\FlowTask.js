const mongoose = require('mongoose');

const fileInfoSchema = new mongoose.Schema({
  name: String,
  size: Number,
  type: String,
  serverFileName: String,
  width: Number,
  height: Number,
  url: String,
  mimetype: String,
});

const componentSchema = new mongoose.Schema({
  componentType: String,
  componentId: String,
  serverFileName: String,
  name: String,
  status: String,
  originalUrl: String,
  originalImage: String,
  fileInfo: fileInfoSchema,
  isMainImage: Boolean,
  url: String,
  imageIndex: Number
});

const processInfoSchema = new mongoose.Schema({
  results: []
});

const flowTaskSchema = new mongoose.Schema({
  taskId: String,
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  deviceToken: {
    type: String,
    required: true,
    index: true,
    description: '设备登录token（JWT令牌，每个设备唯一）'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  },
  promptId: String,
  instanceId: String,
  url:String,
  pageType: String,
  textDescription:String,
  components: [],
  processInfo: processInfoSchema,
  extractedText: String,
  imageCount: Number,
  taskType: String,

  // 平台执行相关信息
  platform: {
    type: String,
    enum: ['comfyui', 'runninghub'],
    description: '执行平台'
  },
  platformInfo: {
    type: mongoose.Schema.Types.Mixed,
    description: '平台选择详细信息'
  },
  runningHubConfig: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RunningHubConfig',
    description: 'RunningHub配置ID（如果使用RunningHub平台）'
  },
});

const FlowTask = mongoose.model('FlowTask', flowTaskSchema);

module.exports = FlowTask;
