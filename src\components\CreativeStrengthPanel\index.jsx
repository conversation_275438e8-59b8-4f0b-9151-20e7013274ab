import React from 'react';
import PropTypes from 'prop-types';
import './index.css';

const CreativeStrengthPanel = ({
  creativeStrength = 0.5,
  onChange,
  min = 0,
  max = 1,
  step = 0.01
}) => {
  // 计算填充宽度的百分比
  const calculateFillWidth = () => {
    const range = max - min;
    const position = creativeStrength - min;
    return position * (100 / range);
  };

  // 格式化显示的数值，保留两位小数
  const formatValue = (value) => {
    return value.toFixed(2);
  };

  // 计算推荐值的位置百分比
  const recommendedPosition = 50; // 0.5在0到1范围内是50%的位置
  
  // 推荐值点击处理函数
  const handleRecommendedClick = () => {
    onChange(0.5); // 将创意强度设置为推荐值0.5
  };

  return (
    <div className="creative-setting">
      <div className="creative-content">
        <div className="creative-label">
          <span>创意强度</span>
          <span className="creative-value">{formatValue(creativeStrength)}</span>
        </div>
        <div className="creative-slider">
          <div className="slider-track">
            <div 
              className="slider-fill" 
              style={{ width: `${calculateFillWidth()}%` }}
            ></div>
            <div 
              className="recommended-mark" 
              style={{ left: `${recommendedPosition}%` }}
              onClick={handleRecommendedClick}
            >
              <div className="mark-arrow"></div>
              <div className="mark-label">推荐值</div>
            </div>
            <input 
              type="range" 
              min={min} 
              max={max}
              step={step}
              value={creativeStrength}
              onChange={(e) => onChange(parseFloat(e.target.value))}
              className="slider-input"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

CreativeStrengthPanel.propTypes = {
  creativeStrength: PropTypes.number,
  onChange: PropTypes.func.isRequired,
  min: PropTypes.number,
  max: PropTypes.number,
  step: PropTypes.number
};

export default CreativeStrengthPanel; 