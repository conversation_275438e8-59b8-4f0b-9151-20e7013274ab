{"10": {"inputs": {"ckpt_name": "dreamshaperXL_v21TurboDPMSDE.safetensors", "vae_name": "sdxl_vae.safetensors", "clip_skip": -2, "lora_name": "None", "lora_model_strength": 0.****************, "lora_clip_strength": 1.0000000000000002, "positive": ["29", 0], "negative": ["31", 1], "token_normalization": "none", "weight_interpretation": "comfy", "empty_latent_width": 512, "empty_latent_height": 512, "batch_size": 1, "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Efficient Loader", "_meta": {"title": "Efficient Loader"}}, "13": {"inputs": {"brushnet": "segmentation_mask_brushnet_ckpt_sdxl_v1/segmentation_mask_brushnet_ckpt_sdxl_v1.safetensors", "dtype": "float16"}, "class_type": "BrushNetLoader", "_meta": {"title": "BrushNet Loader"}}, "14": {"inputs": {"scale": 0.9, "start_at": 0, "end_at": 10000, "model": ["10", 0], "vae": ["10", 4], "image": ["60", 0], "mask": ["26", 0], "brushnet": ["13", 0], "positive": ["37", 0], "negative": ["37", 1]}, "class_type": "BrushNet", "_meta": {"title": "BrushNet"}}, "21": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "crop", "method": "lanc<PERSON>s", "round_to_multiple": "64", "scale_to_side": "longest", "scale_to_length": 3072, "background_color": "#000000", "image": ["45", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "26": {"inputs": {"mask": ["33", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "29": {"inputs": {"delimiter": ", ", "clean_whitespace": "true", "text_a": ["30", 0], "text_b": ["31", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "30": {"inputs": {"from_translate": "auto", "to_translate": "english", "needInput": true, "add_proxies": false, "proxies": "", "auth_data": "", "service": "GoogleTranslator", "text": "1个欧洲白人女孩，背景是海滩", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "输入提示词"}}, "31": {"inputs": {"styles": "fooocus_styles", "select_styles": "Fooocus Negative,artstyle-hyperrealism"}, "class_type": "easy stylesSelector", "_meta": {"title": "Styles Selector"}}, "33": {"inputs": {"invert_mask": false, "grow": -1, "blur": 0, "mask": ["155", 1]}, "class_type": "LayerMask: Mask<PERSON>row", "_meta": {"title": "LayerMask: Mask<PERSON>row"}}, "37": {"inputs": {"strength": 0.3500000000000001, "start_percent": 0, "end_percent": 0.****************, "positive": ["48", 0], "negative": ["48", 1], "control_net": ["39", 0], "image": ["38", 0], "vae": ["10", 4]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "38": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": 1024, "image": ["60", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "39": {"inputs": {"type": "depth", "control_net": ["53", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "42": {"inputs": {"needInput": true, "left": 72, "top": 144, "right": 72, "bottom": 144, "feathering": 30, "image": ["157", 0]}, "class_type": "ImagePadForOutpaint", "_meta": {"title": "图片扩充"}}, "44": {"inputs": {"rem_mode": "RMBG-2.0", "image_output": "<PERSON>de", "save_prefix": "ComfyUI", "torchscript_jit": false, "add_background": "none", "refine_foreground": false, "images": ["42", 0]}, "class_type": "easy imageRemBg", "_meta": {"title": "Image Remove Bg"}}, "45": {"inputs": {"fill_background": true, "background_color": "#FFFFFF", "RGBA_image": ["42", 0], "mask": ["44", 1]}, "class_type": "LayerUtility: ImageRemoveAlpha", "_meta": {"title": "LayerUtility: ImageRemoveAlpha"}}, "47": {"inputs": {"needInput": true, "amount": 1, "samples": ["14", 3]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "图片数量"}}, "48": {"inputs": {"strength": 0.5000000000000001, "start_percent": 0, "end_percent": 0.7000000000000002, "positive": ["10", 1], "negative": ["10", 2], "control_net": ["52", 0], "image": ["50", 0], "vae": ["10", 4]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "50": {"inputs": {"preprocessor": "CannyEdgePreprocessor", "resolution": 1024, "image": ["155", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "52": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["53", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "53": {"inputs": {"control_net_name": "sdxl/controlnet_union_diffusion_pytorch_model_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "57": {"inputs": {"kernel": 1, "sigma": 30, "inpaint": ["140", 0], "original": ["60", 0], "mask": ["26", 0]}, "class_type": "BlendInpaint", "_meta": {"title": "Blend Inpaint"}}, "60": {"inputs": {"size": 1536, "interpolation_mode": "bicubic", "image": ["21", 0]}, "class_type": "JWImageResizeByLongerSide", "_meta": {"title": "Image Resize by <PERSON><PERSON>"}}, "70": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": 1.5000000000000002, "image": ["57", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "72": {"inputs": {"noise_mask": true, "positive": ["10", 1], "negative": ["10", 2], "vae": ["10", 4], "pixels": ["70", 0], "mask": ["74", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "73": {"inputs": {"image": ["70", 0]}, "class_type": "easy imageSize", "_meta": {"title": "ImageSize"}}, "74": {"inputs": {"width": ["73", 0], "height": ["73", 1], "keep_proportions": false, "upscale_method": "nearest-exact", "crop": "disabled", "mask": ["75", 0]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "75": {"inputs": {"invert_mask": false, "grow": -4, "blur": 2, "mask": ["76", 0]}, "class_type": "LayerMask: Mask<PERSON>row", "_meta": {"title": "LayerMask: Mask<PERSON>row"}}, "76": {"inputs": {"mask": ["155", 1]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "80": {"inputs": {"guide_size": 64, "guide_size_for": true, "max_size": 1024, "seed": 1122004094951429, "steps": 15, "cfg": 2, "sampler_name": "euler_ancestral", "scheduler": "karras", "denoise": 0.****************, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5000000000000001, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.9300000000000002, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7000000000000002, "sam_mask_hint_use_negative": "False", "drop_size": 10, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "tiled_encode": {"__value__": [false, true]}, "tiled_decode": false, "speak_and_recognation": {"__value__": [false, true]}, "image": ["142", 0], "model": ["10", 0], "clip": ["10", 5], "vae": ["10", 4], "positive": ["10", 1], "negative": ["10", 2], "bbox_detector": ["81", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "81": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "112": {"inputs": {"mode": "add", "blur_sigma": 30.000000000000007, "blend_factor": 0.7000000000000001, "target": ["143", 0], "source": ["149", 0], "mask": ["150", 0]}, "class_type": "DetailTransfer", "_meta": {"title": "Detail Transfer"}}, "139": {"inputs": {"seed": 1096560387326888, "needInput": true, "steps": 15, "cfg": 3, "sampler_name": "euler_ancestral", "scheduler": "karras", "denoise": 1, "model": ["14", 0], "positive": ["14", 1], "negative": ["14", 2], "latent_image": ["47", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "种子在这个K采样调用"}}, "140": {"inputs": {"samples": ["139", 0], "vae": ["10", 4]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "141": {"inputs": {"seed": 848703224381507, "steps": 15, "cfg": 3, "sampler_name": "euler_ancestral", "scheduler": "karras", "denoise": 0.3500000000000001, "model": ["10", 0], "positive": ["72", 0], "negative": ["72", 1], "latent_image": ["72", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "142": {"inputs": {"samples": ["141", 0], "vae": ["10", 4]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "143": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": 389810074707477, "steps": 15, "cfg": 2, "sampler_name": "euler_ancestral", "scheduler": "karras", "denoise": 0.****************, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5000000000000001, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.9300000000000002, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7000000000000002, "sam_mask_hint_use_negative": "False", "drop_size": 10, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "tiled_encode": {"__value__": [false, true]}, "tiled_decode": false, "speak_and_recognation": {"__value__": [false, true]}, "image": ["80", 0], "model": ["10", 0], "clip": ["10", 5], "vae": ["10", 4], "positive": ["10", 1], "negative": ["10", 2], "bbox_detector": ["144", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "144": {"inputs": {"model_name": "bbox/hand_yolov8s.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "149": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": 1.5000000000000002, "image": ["60", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "150": {"inputs": {"height": ["73", 1], "width": ["73", 0], "interpolation_mode": "bicubic", "mask": ["155", 1]}, "class_type": "JWMaskResize", "_meta": {"title": "Mask Resize"}}, "153": {"inputs": {"filename_prefix": "ChangeModel", "images": ["158", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "155": {"inputs": {"threshold": 0.3, "detail_method": "VITMatte", "detail_erode": 6, "needInput": true, "detail_dilate": 6, "black_point": 0.15, "white_point": 0.99, "process_detail": true, "prompt": "bra, panties", "device": "cuda", "max_megapixels": 2, "image": ["60", 0], "sam_models": ["156", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V3", "_meta": {"title": "prompt蒙版输入词"}}, "156": {"inputs": {"sam_model": "sam_vit_h (2.56GB)", "grounding_dino_model": "GroundingDINO_SwinB (938MB)"}, "class_type": "LayerMask: LoadSegmentAnythingModels", "_meta": {"title": "LayerMask: Load SegmentAnything Models(Advance)"}}, "157": {"inputs": {"needInput": true, "url": "https://images.pexels.com/photos/32085609/pexels-photo-32085609.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "输入模特图"}}, "158": {"inputs": {"needInput": true, "width": 1340, "height": 1785, "interpolation": "bicubic", "method": "fill / crop", "condition": "always", "multiple_of": 0, "image": ["112", 0]}, "class_type": "ImageResize+", "_meta": {"title": "传入图片计算后最终尺寸"}}}