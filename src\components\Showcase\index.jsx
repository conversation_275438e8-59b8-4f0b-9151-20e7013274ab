import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { SHOWCASE_BASE_URL, FUNCTION_CONFIGS } from '../../config/showcase/showcase';
import './index.css';
import Masonry from 'masonry-layout';
import { MdOutlineZoomOutMap } from 'react-icons/md';

const Showcase = ({ tag, onImagePreview }) => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  // 移除本地状态，使用全局状态

  // 获取功能板块名称的函数
  const getFeatureName = (tag) => {
    const tagToName = {
      // 款式设计
      'trending': '爆款开发',
      'divergent': '爆款延伸',
      'optimize': '款式优化',
      'inspiration': '灵感探索',
      'fabric': '换面料',
      'drawing': '生成线稿',
      // 模特图
      'fashion': '时尚大片',
      'tryon': '模特换装',
      'change-model': '换模特',
      'change-posture': '换姿势',
      'recolor': '服装复色',
      'background': '换背景',
      'virtual': '虚拟模特',
      'detail-migration': '细节还原',
      'hand-fix': '手部修复',
      // AI视频
      'imgtextvideo': '图文成片',
      'mulimgvideo': '多图成片',
      // 快捷工具
      'matting': '自动抠图',
      'extend': '智能扩图',
      'extract': '图片取词',
      'upscale': '高清放大',
      'inpaint': '消除笔'
    };
    return tagToName[tag] || '未知功能';
  };

  // 获取带大类的功能名称的函数
  const getFeatureNameWithCategory = (tag) => {
    const tagToCategory = {
      // 款式设计
      'trending': '款式设计',
      'divergent': '款式设计',
      'optimize': '款式设计',
      'inspiration': '款式设计',
      'fabric': '款式设计',
      'drawing': '款式设计',
      // 模特图
      'fashion': '模特图',
      'tryon': '模特图',
      'change-model': '模特图',
      'change-posture': '模特图',
      'recolor': '模特图',
      'background': '模特图',
      'virtual': '模特图',
      'detail-migration': '模特图',
      'hand-fix': '模特图',
      // AI视频
      'imgtextvideo': 'AI视频',
      'mulimgvideo': 'AI视频',
      // 快捷工具
      'matting': '快捷工具',
      'extend': '快捷工具',
      'extract': '快捷工具',
      'upscale': '快捷工具',
      'inpaint': '快捷工具'
    };
    
    const category = tagToCategory[tag] || '其他';
    const featureName = getFeatureName(tag);
    return `${category} · ${featureName}`;
  };

  useEffect(() => {
    if (tag === 'img-text-video' || tag === 'multi-img-video') {
      setItems([]);
      setLoading(false);
      return;
    }

    if (!tag || tag === 'all') {
      // For now, we don't load anything for the 'all' tag to avoid excessive requests.
      // This can be implemented later if needed.
      setItems([]);
      return;
    }

    const fetchItems = async () => {
      setLoading(true);
      const config = FUNCTION_CONFIGS[tag];
      if (!config) {
        setItems([]);
        setLoading(false);
        return;
      }
      
      const newItems = [];
      const { urlPath, name, isVideo } = config;

      // Sequentially check for images to maintain order and stop at the first gap.
      for (let i = 1; i <= 50; i++) { // Max 50 items per category
        const sequentialNum = i.toString().padStart(3, '0');
        const imageUrl = `${SHOWCASE_BASE_URL}${urlPath}/${sequentialNum}.jpg`;
        const previewImageUrl = `${SHOWCASE_BASE_URL}${urlPath}/${sequentialNum}-a.jpg`;

        try {
          const response = await fetch(imageUrl, { method: 'HEAD' });
          if (response.ok) {
            newItems.push({
              id: `${tag}-${i}`,
              image: imageUrl,
              previewImage: previewImageUrl,
              alt: `${name} 案例 ${i}`,
              tags: [tag],
              title: `${name} 案例 ${i}`,
              isVideo: isVideo,
              // Add videoUrl if it's a video case
            });
          } else {
            // Got a 404 or other error, assume this is the end of the list.
            // 静默处理404错误，不在控制台显示
            break; 
          }
        } catch (error) {
          // Network error, also stop.
          // 静默处理网络错误，不在控制台显示
          break;
        }
      }
      setItems(newItems);
      setLoading(false);
    };

    fetchItems();
  }, [tag]);

  useEffect(() => {
    if (showcaseRef.current && items.length > 0) {
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });
    } else if (masonryRef.current) {
      masonryRef.current.destroy();
      masonryRef.current = null;
    }

    return () => {
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }
    };
  }, [items]);

  // 添加图片预览处理函数
  const handleImagePreview = (item) => {
    if (onImagePreview) {
      onImagePreview(item);
    }
  };

  if (loading) {
    return (
      <div className="showcase-container showcase-loading">
        <div className="showcase-loading-content">
          <div className="showcase-spinner"></div>
          <span>加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="showcase-container">
      <div className="showcase-waterfall" ref={showcaseRef}>
        {items.map(item => (
          <div key={item.id} className="showcase-item">
            <img 
              src={item.image} 
              alt={item.alt} 
              onLoad={() => masonryRef.current?.layout()} 
            />
            <div className="showcase-buttons">
              <button 
                className="showcase-btn preview-btn"
                onClick={() => handleImagePreview(item)}
              >
                <MdOutlineZoomOutMap />
                预览
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* 移除本地ImagePreviewModal，使用全局的 */}
    </div>
  );
};

Showcase.propTypes = {
  tag: PropTypes.string.isRequired,
  onImagePreview: PropTypes.func,
};

export default Showcase; 