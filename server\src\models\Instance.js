const mongoose = require('mongoose');

const instanceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  url: {
    type: String,
    trim: true
  },
  apiKey: {
    type: String,
  },
  instanceId: {
    type: String,
    required: true
  },
  appId: {
    type: String,
    required: true,
    unique: true,
    description: '云服务中的实例ID，用于关联云服务实例'
  },
  type: {
    type: String,
    enum: ['stable', 'beta', 'custom'],
    default: 'stable'
  },
  status: {
    type: String,
    enum: ['running', 'stopped', 'error'],
    default: 'stopped'
  },
  description: {
    type: String,
    trim: true
  },
  // 添加常开字段
  isAlwaysOn: {
    type: Boolean,
    default: false,
    description: '是否设置为常开模式，常开实例不会被自动关机'
  },
  lastChecked: {
    type: Date
  },
  lastError: {
    type: String
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// 添加索引
instanceSchema.index({ name: 1 });
instanceSchema.index({ status: 1 });
instanceSchema.index({ type: 1 });
instanceSchema.index({ isAlwaysOn: 1 }); // 添加常开字段索引
instanceSchema.index({ createdAt: -1 });

const Instance = mongoose.model('Instance', instanceSchema);

module.exports = Instance;