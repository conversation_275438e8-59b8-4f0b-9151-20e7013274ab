import React from 'react';
import PropTypes from 'prop-types';
import './index.css';

/**
 * 扩图比例与尺寸设置组件
 */
const ExtendSize = ({ onExpandClick, savedSettings, pageType }) => {
  // 格式化尺寸信息显示
  const formatSize = () => {
    if (!savedSettings) return '未设置';
    
    // 如果没有原图信息，显示功能说明文字
    if (!savedSettings.hasOriginalImage) return '设置想要扩图的比例或尺寸';
    
    const { scale, width, height, activeRatio, activeTab, activePlatform } = savedSettings;
    
    // 根据不同情况返回不同的文字描述
    if (activeTab === 'custom') {
      return `自定义尺寸 ${width} × ${height}px`;
    } else if (activeTab === 'platform' && activePlatform) {
      // 如果是平台尺寸页面
      switch (activePlatform) {
        case 'default': return `默认尺寸 ${width} × ${height}px`;
        case 'amazon': return `亚马逊 ${width} × ${height}px`;
        case 'walmart': return `沃尔玛 ${width} × ${height}px`;
        case 'etsy': return `Etsy ${width} × ${height}px`;
        case 'mercado': return `美客多 ${width} × ${height}px`;
        case 'ebay': return `eBay ${width} × ${height}px`;
        case 'temu': return `Temu ${width} × ${height}px`;
        case 'shopify-square': return `Shopify方形 ${width} × ${height}px`;
        case 'shopify-landscape': return `Shopify横版 ${width} × ${height}px`;
        case 'shopify-portrait': return `Shopify竖版 ${width} × ${height}px`;
        case 'shein': return `SHEIN ${width} × ${height}px`;
        case 'aliexpress': return `速卖通 ${width} × ${height}px`;
        case 'shopee': return `Shopee ${width} × ${height}px`;
        case 'lazada': return `Lazada ${width} × ${height}px`;
        case 'tiktok': return `Tiktok Shop ${width} × ${height}px`;
        case 'douyin': return `抖音小店 ${width} × ${height}px`;
        case 'taobao': return `淘宝/天猫 ${width} × ${height}px`;
        case 'jd': return `京东 ${width} × ${height}px`;
        case 'wayfair': return `Wayfair ${width} × ${height}px`;
        case 'cdiscount': return `Cdiscount ${width} × ${height}px`;
        case 'jumia': return `Jumia ${width} × ${height}px`;
        case 'rakuten': return `Rakuten ${width} × ${height}px`;
        case 'wish': return `Wish ${width} × ${height}px`;
        default: return `平台尺寸 ${width} × ${height}px`;
      }
    } else if (activeRatio) {
      // 如果选择了宽高比按钮
      return `${activeRatio} 比例 (${width} × ${height}px)`;
    } else if (scale) {
      // 根据倍数按钮显示对应文字
      if (scale === 1.1) return `原比例 1.1倍 (${width} × ${height}px)`;
      if (scale === 1.2) return `原比例 1.2倍 (${width} × ${height}px)`;
      if (scale === 1.5) return `原比例 1.5倍 (${width} × ${height}px)`;
      if (scale === 2) return `原比例 2倍 (${width} × ${height}px)`;
      if (scale === 2.5) return `原比例 2.5倍 (${width} × ${height}px)`;
      
      // 如果是其他倍数，显示通用格式
      return `${scale.toFixed(1)}倍 (${width} × ${height}px)`;
    }
    
    return '未设置具体比例';
  };
  
  // 处理点击事件
  const handleExpandClick = (event) => {
    if (onExpandClick) {
      onExpandClick(event);
    }
  };
  
  // 新增：副标题内容判断
  let subtitleText = formatSize();
  if (pageType === 'change-model' && subtitleText === '未设置') {
    subtitleText = '通过合理的扩图生成完整的模特';
  }

  // 判断是否应该显示激活状态的图标
  const shouldShowActiveIcon = () => {
    if (!savedSettings) return false;
    
    // 检查是否有有效的设置数据
    const hasValidSettings = (
      savedSettings.scale || 
      savedSettings.width || 
      savedSettings.height ||
      savedSettings.activeRatio ||
      savedSettings.activeTab === 'custom' ||
      (savedSettings.activeTab === 'platform' && savedSettings.activePlatform)
    );
    
    // 如果有有效设置且不是"未设置"状态，则显示激活图标
    return hasValidSettings && subtitleText !== '未设置' && subtitleText !== '设置想要扩图的比例或尺寸';
  };

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          {pageType === 'change-model' ? (
            <div className="extend-size-icon-text">
              调整图片
            </div>
          ) : (
            <img 
              src={shouldShowActiveIcon() ? 'https://file.aibikini.cn/config/icons/extend-active.png' : 'https://file.aibikini.cn/config/icons/extend.png'} 
              alt="扩图比例与尺寸" 
              className="component-icon" 
            />
          )}
          <div className="component-text">
            <h3>
              {pageType === 'change-model' ? '扩图比例与尺寸（非必填）' : '扩图比例与尺寸'}
            </h3>
            <div className="component-content">
              <p>{subtitleText}</p>
            </div>
          </div>
        </div>
        <button 
          className="expand-btn"
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

ExtendSize.propTypes = {
  onExpandClick: PropTypes.func.isRequired,
  savedSettings: PropTypes.object,
  pageType: PropTypes.string
};

export default ExtendSize; 