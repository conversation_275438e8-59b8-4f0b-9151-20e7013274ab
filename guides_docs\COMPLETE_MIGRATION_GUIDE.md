# 数据结构完全迁移指南

## 背景

我们的系统正在从旧的`settings`结构迁移到新的`components`结构。之前的迁移工作已部分完成，但一些代码仍然依赖于旧的数据结构。本指南将帮助开发人员完成完全迁移的最后步骤。

## 迁移目标

1. 完全移除对`settings`结构的依赖
2. 确保所有代码都使用新的`components`结构
3. 删除或替换所有使用`getTaskSetting`的代码
4. 确保API请求和响应都使用新的数据结构

## 需要修改的主要文件

基于代码搜索，以下文件需要进行更新：

### 1. 页面组件

以下页面组件含有大量对旧`settings`结构的引用：

- `src/pages/model/virtual/index.jsx`
- `src/pages/model/fabric/index.jsx`
- `src/pages/model/fashion/index.jsx`
- `src/pages/model/recolor/index.jsx`
- `src/pages/model/background/index.jsx`
- `src/pages/model/try-on/index.jsx`
- `src/pages/style/trending/index.jsx`
- `src/pages/style/optimize/index.jsx`
- `src/pages/style/inspiration/index.jsx`
- `src/pages/tools/matting/index.jsx`
- `src/pages/tools/upscale/index.jsx`
- `src/pages/tools/extend/index.jsx`
- `src/pages/tools/extract/index.jsx`

### 2. 工具和辅助类

- `src/utils/taskAdapters.js`
- `src/components/TaskPanel/index.jsx`
- `src/api/task.js`

## 迁移方法

### 1. 替换 getTaskSetting 函数调用

所有使用`getTaskSetting`的代码应替换为`getTaskComponent`：

```javascript
// 旧代码
const size = getTaskSetting(task, 'size');
const width = getTaskSetting(task, 'size.width');

// 新代码
const size = getTaskComponent(task, 'imageSizeSelector');
const width = getTaskComponent(task, 'imageSizeSelector.width');
```

### 2. 替换直接访问 task.settings 的代码

```javascript
// 旧代码
const seedValue = task.settings.seed.value;
const prompt = task.settings.generation.prompt;

// 新代码
const seedValue = getTaskComponent(task, 'randomSeedSelector.value');
const prompt = getTaskComponent(task, 'modelPanel')?.prompt;
```

### 3. 更新组件映射关系

确保了解`settings`和`components`之间的映射关系：

| 旧结构 (settings) | 新结构 (components) |
|------------------|-------------------|
| seed.useRandom   | randomSeedSelector.useRandom |
| seed.value       | randomSeedSelector.value |
| size.useDefault  | imageSizeSelector.useDefault |
| size.width       | imageSizeSelector.width |
| size.height      | imageSizeSelector.height |
| model            | modelPanel |
| scene            | scenePanel |
| clothing         | clothingPanel |
| fabric           | fabricPanel |

### 4. 更新任务创建/编辑逻辑

当创建或编辑任务时，应该使用新的组件结构：

```javascript
// 旧结构
const taskData = {
  id: taskId,
  settings: {
    seed: { useRandom, value: seedValue },
    size: { useDefault, width, height }
  }
};

// 新结构
const taskData = {
  id: taskId,
  components: {
    randomSeedSelector: { 
      componentType: 'randomSeedSelector',
      id: generateId(ID_TYPES.COMPONENT),
      useRandom, 
      value: seedValue 
    },
    imageSizeSelector: { 
      componentType: 'imageSizeSelector',
      id: generateId(ID_TYPES.COMPONENT),
      useDefault, 
      width, 
      height 
    }
  }
};
```

## 测试方法

为确保迁移成功，请按以下步骤测试：

1. 在每个页面中检查任务创建功能
2. 确认新创建的任务只使用`components`结构
3. 测试任务编辑和克隆功能
4. 确认无法再直接访问`task.settings`
5. 检查所有API请求和响应是否遵循新的数据结构

## 常见问题

### Q: 如何处理旧格式任务的兼容？
A: 由于我们已删除所有历史数据，不需要处理兼容问题。

### Q: 修改了getTaskSetting后，旧代码会如何工作？
A: 已更新的`getTaskSetting`函数将显示错误信息，并尝试从`components`中检索数据。在开发阶段，这将帮助我们识别仍需修改的代码。

### Q: 如何处理API请求格式？
A: 所有API请求应使用新的`taskDataForSubmission`对象，确保其只包含新格式的数据。 