# 图片类型和来源字段标准化指南

## 背景

项目中不同页面处理图片上传的方式存在不一致，导致代码维护困难和容易出错。本文档记录了标准化过程和最终确定的方案。

## 初始问题

不同页面处理图片上传的方式不一致：

1. 背景页面使用`type: 'foreground'`表示业务类型，用`type === 'custom'`判断是否需要上传
2. 时尚大片页面使用`type: 'custom'`标记用户上传图片
3. 服装复色页面使用`type: 'custom'`标记用户上传图片
4. 面料替换页面从`type: 'fabric'`调整为`type: 'custom'`或引入`source: 'upload'`

导致的问题：
- 代码重复且不一致
- 维护困难
- 上传逻辑不明确

## 标准化方案

我们确定以下标准来规范图片类型和来源：

### 属性定义

1. **type** - 表示图片的业务用途
   - 可能的值: 'foreground', 'background', 'fabric', 'clothing', 'reference'等
   - 这个值描述了图片在业务中的用途，例如是背景图、前景图、面料图等

2. **source** - 表示图片的来源
   - 可能的值: 'upload', 'preset', 'history', 'generated'等
   - 'upload': 用户新上传的图片
   - 'preset': 系统预设的图片
   - 'history': 历史上传记录中的图片
   - 'generated': AI生成的图片

### 判断逻辑标准化

- 判断是否需要上传图片到服务器: `if (image.file && image.source === 'upload')`
- 上传成功后更新图片来源: `image.source = 'history'`
- 清除不必要的属性: `serverUploaded: true` 改为使用 `source: 'history'`

## 已更新的组件

1. **面料替换页面 (fabric)**
   - `handleGenerate`函数中使用`source === 'upload'`判断上传
   - 上传成功后使用`source: 'history'`标记来源
   - 移除`serverUploaded`属性

2. **时尚大片页面 (fashion)**
   - 将`type: 'custom'`改为使用`source: 'upload'`
   - 上传成功后使用`source: 'history'`标记来源
   - 移除`serverUploaded`属性

3. **服装复色页面 (recolor)**
   - 使用标准化的`source`属性
   - 上传成功后使用`source: 'history'`标记来源
   - 移除`serverUploaded`属性

4. **背景替换页面 (background)**
   - 保留`type`属性表示业务用途
   - 使用`source`属性表示图片来源
   - 移除`serverUploaded`属性

5. **UploadGuideModal组件**
   - 更新上传结果处理，使用`source: 'upload'`

## 文档更新

更新了自定义图片上传工作流指南(`custom_image_upload_workflow.md`)，确保与新标准一致：
- 使用`type`表示业务用途
- 使用`source`表示图片来源
- 使用`source === 'upload'`判断是否需要上传
- 上传成功后使用`source: 'history'`标记

## 使用示例

### 图片选择面板
```jsx
// 创建自定义上传图片
const customImage = {
  id: 'upload-' + Date.now(),
  name: '自定义图片',
  type: 'background',  // 业务用途是背景图片
  source: 'upload',    // 来源是用户上传
  description: '用户上传的自定义图片',
  image: fileUrl,
  file: file  // 保存文件对象，用于后续上传
};
```

### 上传判断
```jsx
// 检查是否有用户上传的图片
if (selectedImage.file && selectedImage.source === 'upload') {
  // 上传图片到服务器...
  
  // 上传成功后更新图片对象
  const updatedImage = {
    ...selectedImage,
    image: serverUrl, 
    originalFile: resultData.originalFile,
    source: 'history'  // 更新为历史来源
  };
}
```

## 注意事项

1. 所有新开发的功能都应遵循此标准
2. 旧代码在维护时应逐步更新为新标准
3. 避免使用`serverUploaded`属性，统一使用`source`属性
4. 历史记录保存时应保存`source`属性 