# 剩余数据结构迁移计划

根据对代码的全面检查，我们仍然有部分代码使用旧的`settings`结构。为确保完全迁移到新的`components`结构，需要进行以下修改：

## 1. 修复导入getTaskSetting的问题

以下文件仍然导入了`getTaskSetting`函数，需要更新为只导入`getTaskComponent`：

- `src/pages/model/background/index.jsx`
- `src/pages/model/fashion/index.jsx`
- `src/pages/model/recolor/index.jsx`
- `src/pages/style/optimize/index.jsx`
- `src/pages/tools/extend/index.jsx`
- `src/pages/tools/extract/index.jsx`

修复方法：
```javascript
// 旧代码
import { getTaskComponent, getTaskSetting } from '../../../utils/taskAdapters';

// 新代码
import { getTaskComponent } from '../../../utils/taskAdapters';
```

## 2. 修复对task.settings的直接访问

有大量代码仍然直接访问`task.settings`结构，应替换为使用`getTaskComponent`获取组件值。

主要修改文件（按优先级排序）：
1. `src/pages/model/try-on/index.jsx`
2. `src/pages/model/fashion/index.jsx`
3. `src/pages/model/recolor/index.jsx`
4. `src/pages/style/trending/index.jsx`
5. `src/pages/基础页面/index.jsx`
6. `src/pages/tools/matting/index.jsx`
7. `src/pages/tools/upscale/index.jsx`
8. `src/pages/tools/extend/index.jsx`

修复方法示例：
```javascript
// 旧代码
const clothing = task.settings?.clothing || {};
const seedValue = task.settings?.seed?.value || -1;

// 新代码
const clothing = getTaskComponent(task, 'clothingPanel') || {};
const seedValue = getTaskComponent(task, 'randomSeedSelector.value') || -1;
```

## 3. 修复任务创建/编辑逻辑

检查任务创建和编辑逻辑，确保使用新的组件结构：

```javascript
// 旧结构
const taskData = {
  id: taskId,
  settings: {
    seed: { useRandom, value: seedValue },
    size: { useDefault, width, height }
  }
};

// 新结构
const taskData = {
  id: taskId,
  components: [
    {
      componentType: 'randomSeedSelector',
      id: generateId(ID_TYPES.COMPONENT),
      useRandom, 
      value: seedValue 
    },
    {
      componentType: 'imageSizeSelector',
      id: generateId(ID_TYPES.COMPONENT),
      useDefault, 
      width, 
      height 
    }
  ]
};
```

## 4. 最优先处理的代码段

以下是一些需要优先修复的代码段：

### src/pages/model/try-on/index.jsx
- 第130-132行：筛选任务逻辑中使用了`task.settings`
- 第747-796行：处理服装和模特面板时使用了旧结构
- 第918-922行：构建设置信息时使用了旧结构

### src/pages/style/trending/index.jsx
- 第728-748行：处理任务编辑时使用了旧结构

### src/pages/tools/matting/index.jsx
- 第52-90行：处理原始文件和服务器文件名时使用了旧结构
- 第110-142行：构建任务设置时使用了旧结构

## 5. 测试检查点

完成修改后，应对以下功能进行测试：

1. 任务创建功能
2. 任务编辑和克隆功能
3. 任务列表筛选 
4. 各页面的图片生成功能
5. API请求数据格式

## 6. 进度跟踪

在解决每个问题后，及时更新此跟踪文档：

- [x] 移除所有getTaskSetting导入
- [x] 更新所有文件中对task.settings的直接访问
- [x] 修复所有任务创建/编辑逻辑
- [x] 测试所有功能点 