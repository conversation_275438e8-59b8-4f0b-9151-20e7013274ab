.weight-setting {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  height: auto;
  min-height: 128px;
  display: flex;
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  position: relative;
}

.weight-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
}

.weight-label {
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
}

.weight-label span:first-child {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.weight-ratio {
  font-size: var(--font-size-md);
  color: var(--brand-primary);
  font-weight: 500;
}

.weight-values {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.weight-values span {
  font-size: var(--font-size-md);
  color: var(--brand-primary) !important;
  font-weight: 500;
}

.colored-value {
  font-size: var(--font-size-md);
  color: var(--brand-primary) !important;
  font-weight: 500;
}

.weight-slider-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 20px 0 0px;
  position: relative;
}

/* 双滑块样式 */
.weight-sliders {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 10px 0;
  gap: 16px;
}

.weight-slider-row {
  display: flex;
  align-items: center;
  gap: 0;
  width: 100%;
}

.weight-item-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  white-space: nowrap;
  width: 60px;
  text-align: right;
  margin-right: 10px;
}

/* 两行标签样式 */
.two-line-label {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.2;
}

.slider-container {
  flex: 1;
  position: relative;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.slider-track {
  position: relative;
  height: 2px;
  background: var(--border-light);
  border-radius: var(--radius-sm);
  width: 100%;
  margin: 8px 0;
}

.slider-fill {
  position: absolute;
  height: 100%;
  background: var(--brand-gradient);
  border-radius: var(--radius-sm);
  transition: width 0s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 均衡值标记样式 */
.balance-mark {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  top: -10px;
  cursor: pointer;
}

.mark-arrow {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid var(--brand-primary);
  margin: 0 auto;
}

.mark-label {
  font-size: 10px;
  color: var(--brand-primary);
  white-space: nowrap;
  text-align: center;
  opacity: 0.9;
  font-weight: 500;
  margin-top: 30px;
  position: static;
  display: block;
  width: 100%;
}

.slider-input {
  position: absolute;
  top: 50%;
  left: 0%;
  width: 100%;
  height: 20px;
  transform: translateY(-50%);
  -webkit-appearance: none;
  background: transparent;
  cursor: pointer;
  margin: 0;
  padding: 0;
}

.slider-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  border: 3px solid var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  z-index: 1;
}

.slider-input::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  border: 3px solid var(--brand-primary);
  box-shadow: 0 2px 6px var(--brand-primary-light);
  cursor: pointer;
  transition: var(--transition-normal);
}

.slider-input::-webkit-slider-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
}

.slider-input::-moz-range-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 2px 10px var(--brand-primary-lighter);
}

.value-label {
  width: 40px;
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
}

/* 重置按钮样式 */
.reset-weights-btn {
  align-self: center;
  background: transparent;
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  padding: 4px 12px;
  margin-top: 4px;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.3s;
}

.reset-weights-btn:hover {
  background: var(--bg-secondary);
  color: var(--brand-primary);
  border-color: var(--brand-primary-light);
}

.balance-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.balance-icon-button {
  width: 22px;
  height: 22px;
  background: transparent;
  border: none;
  color: var(--brand-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  opacity: 0.7;
}

.balance-icon-button:hover {
  opacity: 1;
}

/* 添加禁用状态样式 */
.slider-fill.disabled {
  background-color: #e0e0e0 !important;
  opacity: 0.5;
}

.slider-input.disabled {
  cursor: not-allowed;
}

/* 时尚大片页面单个轨道的样式 */
.fashion-page .weight-setting {
  min-height: 88px;
}

.fashion-page .weight-sliders {
  padding: 0;
}

.fashion-page .weight-slider-row {
  margin: 0;
}

.fashion-page .slider-container {
  margin: 0;
}

.fashion-page .slider-track {
  margin: 0;
}

/* 推荐值标记样式 */
.recommend-mark {
  position: absolute;
  left: calc(75% - 5px);
  transform: translateX(-50%);
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  top: -28px;
}

.recommend-label {
  font-size: 10px;
  color: var(--brand-primary);
  white-space: nowrap;
  text-align: center;
  opacity: 0.9;
  font-weight: 500;
  margin-bottom: 24px;
}

.recommend-arrow {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 5px solid var(--brand-primary);
  margin-top: 4px;
}

/* 删除.weight-panel-tip-button在480px下的所有媒体查询和相关样式，全部交由全局buttons.css管理 */ 