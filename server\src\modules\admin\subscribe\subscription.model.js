const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const Plan = require('./plan.model');

const SubscriptionSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  plan: {
    type: String,
    required: true,
    default: 'free'
  },
  status: {
    type: String,
    enum: ['active', 'expired', 'canceled', 'pending', 'not_started'],
    default: 'pending'
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  autoRenew: {
    type: Boolean,
    default: false
  },
  price: {
    type: Number,
    required: true
  },
  paymentMethod: {
    type: String,
    required: false
  },
  paymentId: {
    type: String
  },
  features: {
    // 设计功能
    design: {
      enabled: { type: Boolean, default: false },
      trending: { type: Boolean, default: false },      // 爆款开发
      optimize: { type: Boolean, default: false },      // 款式优化
      inspiration: { type: Boolean, default: false }    // 灵感探索
    },
    // 模特功能
    model: {
      enabled: { type: Boolean, default: false },
      fashion: { type: Boolean, default: false },       // 时尚大片
      'try-on': { type: Boolean, default: false },      // 模特换装
      'change-model': { type: Boolean, default: false }, // 换模特
      recolor: { type: Boolean, default: false },       // 服装复色
      fabric: { type: Boolean, default: false },        // 换面料
      background: { type: Boolean, default: false },    // 换背景
      virtual: { type: Boolean, default: false },       // 虚拟模特
      'detail-migration': { type: Boolean, default: false }, // 细节还原
      'hand-fix': { type: Boolean, default: false }     // 手部修复
    },
    // 工具功能
    tools: {
      enabled: { type: Boolean, default: true },
      extract: { type: Boolean, default: true },        // 图片取词
      upscale: { type: Boolean, default: true },        // 高清放大
      matting: { type: Boolean, default: true },        // 自动抠图
      extend: { type: Boolean, default: true },         // 智能扩图
      inpaint: { type: Boolean, default: true }         // 消除笔
    },
    // 视频功能
    video: {
      enabled: { type: Boolean, default: false },
      imgtextvideo: { type: Boolean, default: false },  // 图文成片
      mulimgvideo: { type: Boolean, default: false }    // 多图成片
    },
    support: {
      level: { 
        type: String, 
        enum: ['standard', 'premium', 'enterprise'], 
        default: 'standard' 
      },
      responseTime: { 
        type: String, 
        enum: ['5x8', '7x24'], 
        default: '5x8' 
      }
    }
  },
  customFeatures: [{
    name: String,
    description: String,
    enabled: Boolean
  }],
  usageQuota: {
    totalRequests: { type: Number, default: -1 }, // -1 表示无限制
    dailyRequests: { type: Number, default: -1 },
    remainingRequests: { type: Number },
    maxConcurrentTasks: { type: Number }
  },
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// 更新时自动更新 updatedAt 字段
SubscriptionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 根据订阅计划设置功能
SubscriptionSchema.pre('save', async function(next) {
  if (this.isNew || this.isModified('plan')) {
    try {
      // 从数据库获取对应的计划配置
      const planDoc = await Plan.findOne({ code: this.plan });
      
      if (planDoc) {
        // 使用计划中的功能配置
        this.features = planDoc.features;
        
        // 设置使用配额
        this.usageQuota = {
          totalRequests: planDoc.usageQuota.totalRequests,
          dailyRequests: planDoc.usageQuota.dailyRequests,
          remainingRequests: planDoc.usageQuota.totalRequests,
          maxConcurrentTasks: planDoc.usageQuota.maxConcurrentTasks
        };
        
        console.log(`订阅计划 ${this.plan} 的功能配置已从数据库加载`);
      } else {
        // 如果找不到对应的计划，使用默认配置
        console.warn(`未找到计划 ${this.plan} 的配置，使用默认配置`);
        
        switch (this.plan) {
          case 'design':
            this.features.design.enabled = true;
            this.features.design.trending = true;
            this.features.design.optimize = true;
            this.features.design.inspiration = true;
            
            this.features.model.enabled = false;
            this.features.model.fashion = false;
            this.features.model['try-on'] = false;
            this.features.model['change-model'] = false;
            this.features.model.recolor = false;
            this.features.model.fabric = false;
            this.features.model.background = false;
            this.features.model.virtual = false;
            this.features.model['detail-migration'] = false;
            this.features.model['hand-fix'] = false;
            
            this.features.support.level = 'standard';
            this.features.support.responseTime = '5x8';
            break;
            
          case 'model':
            this.features.design.enabled = false;
            this.features.design.trending = false;
            this.features.design.optimize = false;
            this.features.design.inspiration = false;
            
            this.features.model.enabled = true;
            this.features.model.fashion = true;
            this.features.model['try-on'] = true;
            this.features.model['change-model'] = true;
            this.features.model.recolor = true;
            this.features.model.fabric = true;
            this.features.model.background = true;
            this.features.model.virtual = true;
            this.features.model['detail-migration'] = true;
            this.features.model['hand-fix'] = true;
            
            this.features.support.level = 'standard';
            this.features.support.responseTime = '5x8';
            break;
            
          case 'full':
            this.features.design.enabled = true;
            this.features.design.trending = true;
            this.features.design.optimize = true;
            this.features.design.inspiration = true;
            
            this.features.model.enabled = true;
            this.features.model.fashion = true;
            this.features.model['try-on'] = true;
            this.features.model['change-model'] = true;
            this.features.model.recolor = true;
            this.features.model.fabric = true;
            this.features.model.background = true;
            this.features.model.virtual = true;
            this.features.model['detail-migration'] = true;
            this.features.model['hand-fix'] = true;
            
            this.features.support.level = 'premium';
            this.features.support.responseTime = '7x24';
            break;
            
          case 'enterprise':
            // 企业版默认开启所有功能，具体功能由自定义设置
            this.features.design.enabled = true;
            this.features.design.trending = true;
            this.features.design.optimize = true;
            this.features.design.inspiration = true;
            
            this.features.model.enabled = true;
            this.features.model.fashion = true;
            this.features.model['try-on'] = true;
            this.features.model['change-model'] = true;
            this.features.model.recolor = true;
            this.features.model.fabric = true;
            this.features.model.background = true;
            this.features.model.virtual = true;
            this.features.model['detail-migration'] = true;
            this.features.model['hand-fix'] = true;
            
            this.features.support.level = 'enterprise';
            this.features.support.responseTime = '7x24';
            break;
            
          case 'free':
          default:
            // 免费版只开启基础工具
            this.features.design.enabled = false;
            this.features.design.trending = false;
            this.features.design.optimize = false;
            this.features.design.inspiration = false;
            
            this.features.model.enabled = false;
            this.features.model.fashion = false;
            this.features.model['try-on'] = false;
            this.features.model['change-model'] = false;
            this.features.model.recolor = false;
            this.features.model.fabric = false;
            this.features.model.background = false;
            this.features.model.virtual = false;
            this.features.model['detail-migration'] = false;
            this.features.model['hand-fix'] = false;
            
            this.features.support.level = 'standard';
            this.features.support.responseTime = '5x8';
            break;
        }
      }
    } catch (error) {
      console.error('加载计划配置失败:', error);
      // 出错时使用默认配置
    }
  }
  next();
});

// 创建索引
SubscriptionSchema.index({ user: 1 });
SubscriptionSchema.index({ status: 1 });
SubscriptionSchema.index({ endDate: 1 });
SubscriptionSchema.index({ plan: 1 });

// 计算实际状态（基于时间）
SubscriptionSchema.statics.calculateActualStatus = function(subscription) {
  const now = new Date();
  const startDate = new Date(subscription.startDate);
  const endDate = new Date(subscription.endDate);
  
  // 如果状态是 canceled 或 pending，保持原状态（手动设置，不受时间影响）
  if (subscription.status === 'canceled' || subscription.status === 'pending') {
    return subscription.status;
  }
  
  if (now < startDate) {
    return 'not_started';
  } else if (now > endDate) {
    return 'expired';
  } else {
    return 'active';
  }
};

// 自动更新订阅状态
SubscriptionSchema.statics.updateSubscriptionStatus = async function(subscriptionId) {
  const subscription = await this.findById(subscriptionId);
  if (!subscription) {
    return null;
  }
  
  const actualStatus = this.calculateActualStatus(subscription);
  
  // 如果状态需要更新，则更新
  if (subscription.status !== actualStatus) {
    const oldStatus = subscription.status;
    subscription.status = actualStatus;
    
    // 直接更新数据库，避免触发 save 钩子
    await this.findByIdAndUpdate(subscriptionId, { status: actualStatus });
    console.log(`订阅 ${subscriptionId} 状态已从 ${oldStatus} 更新为 ${actualStatus}`);
  }
  
  return actualStatus;
};

// 批量更新过期订阅状态
SubscriptionSchema.statics.updateExpiredSubscriptions = async function() {
  const now = new Date();
  const result = await this.updateMany(
    {
      status: { $in: ['active', 'pending', 'not_started'] },
      endDate: { $lt: now }
    },
    {
      $set: { status: 'expired' }
    }
  );
  
  console.log(`批量更新了 ${result.modifiedCount} 个过期订阅`);
  return result.modifiedCount;
};

const Subscription = mongoose.model('Subscription', SubscriptionSchema);

module.exports = Subscription; 