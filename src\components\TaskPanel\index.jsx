import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import PropTypes from 'prop-types';
import './index.css';
import { getTaskComponent } from '../../utils/taskAdapters';
import { useTaskContext } from '../../contexts/TaskContext';
import { Modal, Button, Spin, message } from 'antd';
import { handleBatchDownload as downloadHelper } from '../../utils/downloadHelper';
import { showDeleteConfirmModal } from '../../utils/modalUtils';
import { MdClose } from 'react-icons/md';
import ImagePreviewModal from '../common/ImagePreviewModal';

const isMobile = () => typeof window !== 'undefined' && window.innerWidth <= 768;

const TaskPanel = ({
  task,
  onEditTask,
  onDownloadImage,
  onViewDetails,
  onBatchDownload,
  onDeleteTask,
  pageType,
  onAddAsExclusiveModel,
  disableBatchDownload
}) => {
  // 保存实际使用的图片数组
  const [taskImagesState, setTaskImagesState] = useState([]);
  // 使用任务上下文
  const { updateTask } = useTaskContext();
  
  // 预览弹窗相关状态
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  
  // 添加状态追踪引用，避免重复更新
  const prevTaskStatusRef = useRef(task.status);
  
  // 获取有效的任务ID，只使用标准的taskId字段
  const getTaskId = () => task.taskId;
  const handleDownloadImage = async (imageUrl, taskId, index, extension) => {
    try {
      message.info('准备下载...');
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      const response = await fetch(httpsUrl);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      
      // 如果提供了扩展名参数，直接使用；否则自动检测
      let originalExtension = extension || 'jpg'; // 默认值
      
      if (!extension) {
        // 优先从Content-Type响应头获取文件扩展名
        const contentType = response.headers.get('content-type');
        if (contentType) {
          if (contentType.includes('image/png')) {
            originalExtension = 'png';
          } else if (contentType.includes('image/jpeg') || contentType.includes('image/jpg')) {
            originalExtension = 'jpg';
          } else if (contentType.includes('image/webp')) {
            originalExtension = 'webp';
          } else if (contentType.includes('image/gif')) {
            originalExtension = 'gif';
          }
        } else {
          // 如果无法从响应头获取，则从URL中提取
          const urlParts = imageUrl.split('.');
          originalExtension = urlParts.length > 1 ? urlParts[urlParts.length - 1].split('?')[0] : 'jpg';
        }
      }
      
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `${taskId}_${parseInt(index) + 1}.${originalExtension}`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      message.error('下载失败');
      console.error('下载出错:', error);
    }
  };

  // 处理批量下载
  const handleBatchDownload = async (task) => {
    // 调用通用下载辅助函数
    await downloadHelper(
      task, 
      fetch, // 传递fetch函数
      handleDownloadImage // 传递单张图片下载函数
    );
  };
  // 获取任务的图片数组，使用新结构 - 精简版
  const getTaskImages = (task) => {

    let finalImages = [];
    // TODO 删除开始
    
    // 数据修复日志
    if (process.env.NODE_ENV === 'development') {
      console.log('=== 任务数据修复日志 ===');
      
      // 检查主要组件 - 改进的逻辑，兼容数组和对象格式
      let mainComponent = null;
      
      // 处理components可能是对象或数组的情况
      if (Array.isArray(task.components)) {
        // 如果是数组，查找标记为isMainImage的组件
        mainComponent = task.components.find(comp => comp && comp.isMainImage);
      } else if (task.components && typeof task.components === 'object') {
        // 如果是对象，先尝试获取mainComponent键
        mainComponent = task.components.mainComponent || 
                       Object.values(task.components).find(comp => comp && comp.isMainImage);
      }
      
      console.log('主组件存在:', !!mainComponent);
      if (mainComponent) {
              // console.log('主组件serverFileName:', mainComponent.serverFileName);
      // 使用标准化的服务器文件名字段
      console.log('主组件类型:', mainComponent.componentType || mainComponent.type);
      }
      
      // 检查sourceImagePanel组件
      const sourceImagePanel = getTaskComponent(task, 'sourceImagePanel');
      console.log('sourceImagePanel存在:', !!sourceImagePanel);
      if (sourceImagePanel) {
        // console.log('sourceImagePanel.serverFileName:', sourceImagePanel.serverFileName);
      }
      
      // 检查所有sourceImagePanel组件
      let sourceImagePanels = [];
      
      if (Array.isArray(task.components)) {
        // 如果组件是数组格式，直接过滤
        sourceImagePanels = task.components.filter(comp => 
          comp && comp.componentType === 'sourceImagePanel'
        );
      } else if (task.components && typeof task.components === 'object') {
        // 如果组件是对象格式，使用原有的逻辑
        const componentKeys = Object.keys(task.components);
        sourceImagePanels = componentKeys
          .filter(key => key.includes('sourceImagePanel') || 
                  (task.components[key].componentType === 'sourceImagePanel'))
          .map(key => task.components[key]);
      }
      
      console.log('找到sourceImagePanel组件数量:', sourceImagePanels.length);
      sourceImagePanels.forEach((panel, index) => {
        console.log(`sourceImagePanel[${index}]:`, {
          componentId: panel.componentId,
          serverFileName: panel.serverFileName
        });
      });
      
      // 检查任务顶层属性
      // console.log('任务serverFileName:', task.serverFileName);
      console.log('任务primaryImageFileName:', task.primaryImageFileName);
      console.log('任务组件总数:', Array.isArray(task.components) ? task.components.length : Object.keys(task.components || {}).length);
      
      // 检查是否存在关键字段
      if (!task.serverFileName) {
        // console.log('警告: 任务缺少serverFileName字段，数据可能不完整');
      }
      
      if (!task.primaryImageFileName) {
        console.log('警告: 任务缺少primaryImageFileName字段，数据可能不完整');
      }
      
      console.log('=== 数据修复日志结束 ===');
    }
    
    // 如果任务正在处理中，则返回一个空占位对象数组，用于显示加载指示器
    if (task.status === 'processing') {
      if (process.env.NODE_ENV === 'development') {
        console.log('[TaskPanel] 任务处理中，返回占位图片对象');
      }
      return { images: [{ placeholder: true }] };
    }
    
    // 只有当任务已完成时才尝试获取图片
    if (task.status !== 'completed') {
      if (process.env.NODE_ENV === 'development') {
        console.log('[TaskPanel] 任务尚未完成，暂不获取图片:', task.status);
      }
      return { images: [] };
    }
    
    // 首先，尝试获取API返回结果中的图片URL
    if (task.processInfo && task.processInfo.results) {
      const resultUrl = Array.isArray(task.processInfo.results) 
        ? task.processInfo.results[0]?.url 
        : task.processInfo.results?.url;
      
      if (resultUrl && typeof resultUrl === 'string') {
        if (process.env.NODE_ENV === 'development') {
          console.log('[TaskPanel] 找到API结果URL:', resultUrl);
        }
        finalImages.push({ url: resultUrl });
      }
    }
    
    // 然后尝试从processInfo中获取URL
    if (task.processInfo && task.processInfo.url) {
      const url = task.processInfo.url;
      if (process.env.NODE_ENV === 'development') {
        console.log('[TaskPanel] 从processInfo中找到URL:', url);
      }
      if (!finalImages.some(img => img.url === url)) {
        finalImages.push({ url });
      }
    }
    
    // 通用处理：当没有找到API返回的图片URL时的日志和处理逻辑
    if (getTaskId() && finalImages.length === 0) {
      if (process.env.NODE_ENV === 'development') {
        // 只在开发环境下输出详细日志
        console.log(`[TaskPanel] ${pageType}任务未在API返回中找到结果URL，将尝试其他方法获取图片`);
      }
      // 降级为普通日志，避免控制台出现警告
      console.log(`[TaskPanel] 任务未找到API结果，将尝试使用其他途径获取图片`);
      // 所有任务类型都应依赖API返回的确切文件名，而不使用任务ID构建回退路径
      // 这样确保所有任务类型都遵循同样的可靠图片获取策略
    }
    
    // 通过getTaskComponent获取生成的图片
    const generatedImagesComponent = getTaskComponent(task, 'generatedImages');
    if (Array.isArray(generatedImagesComponent) && generatedImagesComponent.length > 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[TaskPanel] 通过getTaskComponent获取到生成图片:', generatedImagesComponent.length);
      }
      // 过滤出包含URL的图片对象
      const validImages = generatedImagesComponent.filter(img => img && img.url);
      // 添加到结果中，避免重复
      validImages.forEach(img => {
        if (!finalImages.some(existing => existing.url === img.url)) {
          finalImages.push(img);
        }
      });
    }
    
    // 使用generatedImages顶级属性（标准结构）
    if (Array.isArray(task.generatedImages) && task.generatedImages.length > 0) {
      // 确保每个图片对象都有可访问的URL
      const images = task.generatedImages.map(image => {
        if (image) {
          // 如果图片有path但没有url或url不是完整地址，则构建完整URL
          if (image.path && (!image.url || !image.url.startsWith('http'))) {
            // process.env.REACT_APP_BACKEND_URL 替换/api
            const baseUrl = process.env.REACT_APP_BACKEND_URL.replace('/api', '');
            return {
              ...image,
              url: image.path.startsWith('http') ? image.path : `${baseUrl}${image.path}`
            };
          }
        }
        return image;
      }).filter(img => img && img.url);
      
      // 添加到结果中，避免重复
      images.forEach(img => {
        if (!finalImages.some(existing => existing.url === img.url)) {
          finalImages.push(img);
        }
      });
      
      if (process.env.NODE_ENV === 'development') {
        console.log('[TaskPanel] 最终找到的有效图片:', finalImages.length);
      }
    }
    // TODO 删除结束
    if (task.processInfo && task.processInfo.results) {
      const finalImages = task.processInfo.results.map(result => {
        return {
          url: result.url,
          width: result.width,
          height: result.height,
          fileInfo: result.fileInfo
        };
      });
      return { images: finalImages };
    }
    return { images: finalImages };
  };
  
  // 初始化时处理任务图片
  useEffect(() => {
    const { images } = getTaskImages(task);
    if (process.env.NODE_ENV === 'development') {
      console.log('[TaskPanel] 初始化图片:', images.length);
    }
    // 直接使用原始图片 URL，不再拼接时间戳参数
    setTaskImagesState(images);
  }, [getTaskId(), task.status, pageType]);
  
  // 当任务状态改变时，通知TaskContext
  useEffect(() => {
    console.log('任务状态改变:', task);
    // 只有当状态从非completed变为completed时才调用updateTask
    if (task.status === 'completed' && prevTaskStatusRef.current !== 'completed' && task.taskId) {
      updateTask(task);
    }
    // 更新状态引用
    prevTaskStatusRef.current = task.status;
  }, [task.status, updateTask, task]);
  
  // 处理复制任务ID到剪贴板
  const handleCopyId = (e) => {
    e.stopPropagation();
    
    // 只使用标准taskId字段
    const taskId = getTaskId();
    
    // 如果没有有效的taskId，报错并返回
    if (!taskId) {
      console.error('任务ID格式错误：', task);
      alert('错误：无法复制无效的任务ID');
      return;
    }
    
    try {
      navigator.clipboard.writeText(taskId).then(() => {
        // 使用antd message组件显示成功提示
        message.success('已复制任务ID');
      }).catch(err => {
        console.error('复制任务ID失败:', err);
        // 回退方法：手动创建和操作文本区域
        fallbackCopy(taskId);
      });
    } catch (err) {
      console.error('复制操作异常:', err);
      // 使用回退方法
      fallbackCopy(taskId);
    }
  };
  
  // 回退的复制方法
  const fallbackCopy = (text) => {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      document.execCommand('copy');
      // 使用antd message组件显示成功提示
      message.success('已复制任务ID');
    } catch (err) {
      console.error('回退复制方法失败:', err);
      alert('复制失败，请手动复制: ' + text);
    }
    
    document.body.removeChild(textArea);
  };
  
  // 处理查看详情按钮点击
  const handleViewDetailsClick = (image, index) => {
    // 验证任务对象有有效的标准taskId
    if (!getTaskId()) {
      console.error('任务ID格式错误，无法查看详情:', task);
      alert('错误：任务ID格式不正确，无法查看详情');
      return;
    }
    
    // 使用传递的回调函数，确保传递正确的参数格式
    onViewDetails(image, task, index);
  };
  
  // 处理打开预览弹窗（移动端专用）
  const handleOpenPreview = (image) => {
    if (!image || !image.url) {
      console.error('无效的图片数据，无法打开预览');
      return;
    }
    
    console.log('移动端打开预览弹窗:', image.url);
    setPreviewImage(image.url);
    setShowPreviewModal(true);
  };
  
  // 处理关闭预览弹窗
  const handleClosePreview = () => {
    setShowPreviewModal(false);
    setPreviewImage(null);
  };
  
  // 处理显示图片详情
  const handleShowImageDetails = (image, imageIndex) => {
    // 验证任务对象有有效的标准taskId
    if (!getTaskId()) {
      console.error('任务ID格式错误，无法显示图片详情:', task);
      alert('错误：任务ID格式不正确，无法显示图片详情');
      return;
    }

    // PC端：打开ImageDetailsModal
    console.log(`【任务面板】准备获取任务详情，任务ID: ${getTaskId()}`);
    
    // 标记任务需要刷新数据
    const taskWithRefreshFlag = {
      ...task,
      _needsRefresh: true,  // 设置标记，指示需要从API获取最新数据
      _dataSource: 'taskPanel_requestRefresh'  // 标记数据来源
    };
    // 调用传入的onViewDetails函数，传递正确ID格式的任务数据
    onViewDetails(image, taskWithRefreshFlag, imageIndex);
  };
  
  // 伪进度条相关状态
  const [isFakeProgress, setIsFakeProgress] = useState(false);
  const [fakeProgress, setFakeProgress] = useState(0);
  const fakeProgressTimer = useRef(null);
  const FAKE_PROGRESS_MAX = 0.98; // 98%
  const FAKE_PROGRESS_DURATION = 60000; // 60秒

  // 缓入缓出函数
  function easeInOutCubic(x) {
    return x < 0.5
      ? 4 * x * x * x
      : 1 - Math.pow(-2 * x + 2, 3) / 2;
  }

  // 伪进度条启动逻辑
  useEffect(() => {
    if (task.status === 'processing' && (task.progress === undefined || task.progress === 0)) {
      setIsFakeProgress(true);
      setFakeProgress(0);
      const start = Date.now();
      fakeProgressTimer.current = setInterval(() => {
        const elapsed = Date.now() - start;
        let percent = Math.min(elapsed / FAKE_PROGRESS_DURATION, 1);
        let eased = easeInOutCubic(percent);
        let fake = eased * FAKE_PROGRESS_MAX;
        setFakeProgress(fake);
        if (percent >= 1) {
          clearInterval(fakeProgressTimer.current);
        }
      }, 80);
    } else {
      setIsFakeProgress(false);
      setFakeProgress(0);
      if (fakeProgressTimer.current) {
        clearInterval(fakeProgressTimer.current);
      }
    }
    // 清理定时器
    return () => {
      if (fakeProgressTimer.current) {
        clearInterval(fakeProgressTimer.current);
      }
    };
  }, [task.status, task.progress]);
  
  return (
    <div className={`task-card ${task.isNew ? 'new-task' : ''}`}>
      <div className="task-header">
        <div className="task-info">
          <span className="task-time">
            {typeof task.createdAt === 'string' 
              ? new Date(task.createdAt).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit', 
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false
                }).replace(/\//g, '-')
              : task.createdAt.toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false
                }).replace(/\//g, '-')
            }
          </span>
          <div className="task-id-container">
            <span className="task-id-label">任务ID:</span>
            <span className="task-id">
              {getTaskId() ? 
                getTaskId() : 
                <span className="task-id-error">ID格式错误</span>
              }
            </span>
            <button 
              className="copy-id-btn"
              onClick={handleCopyId}
              title="复制任务ID"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>
        <div className="task-actions">
          {pageType === 'matting' ? (
            <button 
              className="edit-btn"
              onClick={(e) => {
                e.stopPropagation();
                handleBatchDownload(task);
              }}
              disabled={task.status === 'processing'}
              title={task.status === 'processing' ? '生成中的任务无法下载' : '批量下载'}
            >
              批量下载
            </button>
          ) : (
            !isMobile() && (
              <button 
                className="edit-btn"
                onClick={() => onEditTask(task)}
                disabled={task.status === 'processing'}
                title={task.status === 'processing' ? '生成中的任务无法编辑' : '重新编辑'}
              >
                重新编辑
              </button>
            )
          )}
          {pageType === 'virtual' && task.status === 'completed' && (
            <button 
              className="exclusive-model-btn"
              onClick={(e) => {
                e.stopPropagation();
                const completedImages = taskImagesState.filter(img => img && img.url);
                if (completedImages.length > 0) {
                  const randomImage = completedImages[Math.floor(Math.random() * completedImages.length)];
                  onAddAsExclusiveModel(randomImage, task);
                }
              }}
              title="添加为虚拟模特"
            >
              添加为虚拟模特
            </button>
          )}
          <div className="more-actions">
            <button 
              className="more-btn"
              onClick={(e) => {
                e.stopPropagation();
                const currentMenu = e.currentTarget.nextElementSibling;
                const allMenus = document.querySelectorAll('.dropdown-menu');
                allMenus.forEach(menu => {
                  if (menu !== currentMenu) {
                    menu.classList.remove('show');
                  }
                });
                currentMenu.classList.toggle('show');
              }}
              disabled={task.status === 'processing'}
              title={task.status === 'processing' ? '生成中的任务暂时无法操作' : '更多操作'}
            >
              <span></span>
            </button>
            <div className="dropdown-menu">
              {isMobile() && (
                <button 
                  className="dropdown-item"
                  onClick={() => onEditTask(task)}
                  disabled={task.status === 'processing'}
                  title={task.status === 'processing' ? '生成中的任务无法编辑' : '重新编辑'}
                >
                  重新编辑
                </button>
              )}
              {pageType !== 'matting' && !disableBatchDownload && (
                <button 
                  className="dropdown-item"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleBatchDownload(task);
                  }}
                >
                  批量下载
                </button>
              )}
              <button 
                className="dropdown-item delete"
                onClick={(e) => {
                  e.stopPropagation();
                  showDeleteConfirmModal({
                    title: '删除记录',
                    content: '确定要删除此记录吗？删除后将无法恢复。',
                    onOk: () => {
                      onDeleteTask(getTaskId());
                    },
                    okButtonProps: {
                      danger: true
                    }
                  });
                }}
              >
                删除记录
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* 只在任务处理中时，显示极简进度区域 */}
      {task.status === 'processing' ? (
        <div className="task-content-area">
          <div className="processing-row">
            <div className="processing-desc">
              {isFakeProgress ? '预处理中...' : '生成中...'}
            </div>
            <span className="processing-percent">
              {Math.round(((isFakeProgress ? fakeProgress : (task.progress===1 ? 0.99 : task.progress || 0)) * 100))}%
            </span>
          </div>
          <div className="task-progress-wrapper" style={{ position: 'relative', width: '100%' }}>
            <div className="task-progress">
              <div 
                className="progress-bar" 
                style={{ 
                  width: `${((isFakeProgress ? fakeProgress : (task.progress===1 ? 0.99 : task.progress || 0)) * 100)}%`,
                  backgroundColor: '#1890ff'
                }}
              />
              {task.progressDetails && (
                <div className="progress-text">
                  {Math.round((task.progress || 0) * 100)}%
                  ({task.progressDetails.current}/{task.progressDetails.total})
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="task-images">
          {task.status === 'failed' ? (
            <div className="empty-message">
              <span>任务生成失败</span>
            </div>
          ) : taskImagesState.length === 0 && task.status === 'completed' ? (
            <div className="empty-message">
              <span>未获取到图片</span>
            </div>
          ) : (
            taskImagesState.map((image, index) => (
              <div 
                key={index} 
                className="image-slot transparent-bg"
                onTouchStart={isMobile() ? (e) => {
                  // 移动端触摸开始时添加激活状态
                  e.currentTarget.classList.add('touch-active');
                } : undefined}
                onTouchEnd={isMobile() ? (e) => {
                  // 移动端触摸结束后短暂延迟移除激活状态，让用户有时间点击按钮
                  setTimeout(() => {
                    if (e.currentTarget) {
                      e.currentTarget.classList.remove('touch-active');
                    }
                  }, 3000); // 3秒后自动移除激活状态
                } : undefined}
                onClick={isMobile() ? (e) => {
                  // 移动端点击时切换激活状态
                  const wasActive = e.currentTarget.classList.contains('touch-active');
                  
                  // 移除所有其他图片卡片的激活状态
                  document.querySelectorAll('.image-slot.touch-active').forEach(slot => {
                    if (slot !== e.currentTarget) {
                      slot.classList.remove('touch-active');
                    }
                  });
                  
                  if (!wasActive) {
                    e.currentTarget.classList.add('touch-active');
                    // 5秒后自动移除激活状态
                    setTimeout(() => {
                      if (e.currentTarget) {
                        e.currentTarget.classList.remove('touch-active');
                      }
                    }, 5000);
                  }
                } : undefined}
              >
                <>
                  <img 
                    src={image.url} 
                    alt={`生成图片 ${index + 1}`}
                    data-image-index={index}
                    onLoad={() => {
                      // console.log('图片加载成功:', image);
                      // console.log('图片加载成功:', task);
                      // 只在开发环境下输出详细日志
                      if (process.env.NODE_ENV === 'development') {
                        console.log(`[TaskPanel] 图片 #${index} 加载成功: ${image.url}`);
                      }
                    }}
                    onError={(e) => {
                      // 保留错误日志，但可以在生产环境简化
                      if (process.env.NODE_ENV === 'development') {
                        console.error(`[TaskPanel] 图片加载失败: ${image.url}`, e);
                      } else {
                        console.error(`[TaskPanel] 图片加载失败: 索引 #${index}`);
                      }
                      // 显示简单的加载失败提示
                      e.target.style.display = 'none';
                      const errorDiv = document.createElement('div');
                      errorDiv.className = 'load-error-message';
                      errorDiv.textContent = '加载失败';
                      e.target.parentNode.appendChild(errorDiv);
                    }}
                  />
                  <div className="image-actions">
                    <button 
                      className="view-details-btn" 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleShowImageDetails(image, index);
                      }}
                    >
                      查看详情
                    </button>
                    <button 
                      className="drag-upload-btn"
                      draggable={true}
                      onDragStart={(e) => {
                        e.stopPropagation();
                        // 创建拖拽数据，包含图片信息
                        const dragData = {
                          type: 'image-upload',
                          imageUrl: image.url,
                          fileName: `task_${getTaskId()}_${index + 1}.jpg`,
                          taskId: getTaskId(),
                          imageIndex: index
                        };
                        e.dataTransfer.setData('application/json', JSON.stringify(dragData));
                        e.dataTransfer.effectAllowed = 'copy';
                        
                        // 设置拖拽图像（可选）
                        const img = new Image();
                        img.src = image.url;
                        e.dataTransfer.setDragImage(img, 0, 0);
                      }}
                      title="拖拽到上传区域"
                    >
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z" fill="currentColor"/>
                      </svg>
                    </button>
                    <button 
                      className="download-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownloadImage(image.url, getTaskId(), index);
                      }}
                      title="下载图片"
                     >
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill="currentColor"/>
                      </svg>
                    </button>
                    <button 
                      className="preview-btn task-preview-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleOpenPreview(image);
                      }}
                      title="预览图片"
                    >
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" fill="currentColor"/>
                      </svg>
                    </button>
                  </div>
                </>
              </div>
            ))
          )}
        </div>
      )}
      
      {/* 图片预览弹窗 */}
      <ImagePreviewModal
        visible={showPreviewModal}
        imageUrl={previewImage}
        onClose={handleClosePreview}
        alt="预览图片"
        showHint={true}
        maxScale={4}
        minScale={0.5}
      />
    </div>
  );
};

TaskPanel.propTypes = {
  task: PropTypes.object.isRequired,
  onEditTask: PropTypes.func,
  onDownloadImage: PropTypes.func,
  onViewDetails: PropTypes.func,
  onBatchDownload: PropTypes.func,
  onDeleteTask: PropTypes.func,
  pageType: PropTypes.string,
  onAddAsExclusiveModel: PropTypes.func,
  disableBatchDownload: PropTypes.bool,
};

export default TaskPanel; 