/* Password Strength Indicator Styles */

.password-strength {
  margin-top: 12px;
  padding: 12px;
  background: var(--bg-secondary);
  border-radius: 8px;
}

.strength-bar {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
}

.strength-level {
  height: 4px;
  flex: 1;
  border-radius: 2px;
  background-color: var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.strength-level::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

/* 无密码强度 - 不显示 */
.strength-bar.无 .strength-level::after {
  transform: scaleX(0);
}

/* 弱密码样式 - 亮一格红色 */
.strength-bar.弱 .strength-level:nth-child(-n+1)::after {
  background-color: var(--error-color);
  transform: scaleX(1);
}

/* 中等密码样式 - 亮两格橙色 */
.strength-bar.中 .strength-level:nth-child(-n+2)::after {
  background-color: var(--warning-color);
  transform: scaleX(1);
}

/* 较强密码样式 - 亮三格黄色 */
.strength-bar.较强 .strength-level:nth-child(-n+3)::after {
  background-color: var(--warning-light);
  transform: scaleX(1);
}

/* 强密码样式 - 亮四格绿色 */
.strength-bar.强 .strength-level:nth-child(-n+4)::after {
  background-color: var(--success-color);
  transform: scaleX(1);
}

/* 指示点颜色 */
.strength-bar.无 + .strength-text::before {
  background-color: var(--bg-tertiary);
}

.strength-bar.弱 + .strength-text::before {
  background-color: var(--error-color);
}

.strength-bar.中 + .strength-text::before {
  background-color: var(--warning-color);
}

.strength-bar.较强 + .strength-text::before {
  background-color: var(--warning-light);
}

.strength-bar.强 + .strength-text::before {
  background-color: var(--success-color);
}

.strength-text {
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.strength-tips {
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}

/* 未满足的要求 */
.strength-tips .tip:not(.met) {
  color: var(--error-color);
}

.strength-tips .tip:not(.met)::before {
  color: var(--error-color);
}

/* 已满足的要求 */
.strength-tips .tip.met {
  color: var(--success-color);
}

.strength-tips .tip.met::before {
  color: var(--success-color);
} 