import React, { useEffect, useState, useRef, useCallback } from 'react';
import { MdClose } from 'react-icons/md';
import './index.css';
import '../../styles/close-buttons.css';

const ImageInfoModal = ({ panel, position, onClose, onDelete, onReupload, onDrawMask, pageType, activeTab }) => {
  // 添加状态保存实际图片尺寸
  const [actualDimensions, setActualDimensions] = useState(null);
  // 添加加载状态
  const [isLoadingDimensions, setIsLoadingDimensions] = useState(false);
  
  // 拖动相关状态
  const [isModalDragging, setIsModalDragging] = useState(false);
  const dragStartMouse = useRef({ x: 0, y: 0 });
  const dragStartTransform = useRef({ x: 0, y: 0 });
  const [dragTransform, setDragTransform] = useState({ x: 0, y: 0 });
  const modalRef = useRef(null);

  // 拖动事件
  const handleModalMouseDown = (e) => {
    // 允许在弹窗任何区域拖动，但排除按钮区域
    if (e.target.closest('button')) return;
    
    setIsModalDragging(true);
    dragStartMouse.current = { x: e.clientX, y: e.clientY };
    dragStartTransform.current = { ...dragTransform };
    const modal = modalRef.current;
    if (modal) {
      document.body.classList.add('no-select');
      modal.classList.add('dragging');
    }
    e.preventDefault();
  };

  const handleModalMouseMove = useCallback((e) => {
    if (isModalDragging) {
      const deltaX = e.clientX - dragStartMouse.current.x;
      const deltaY = e.clientY - dragStartMouse.current.y;
      const newX = dragStartTransform.current.x + deltaX;
      const newY = dragStartTransform.current.y + deltaY;
      setDragTransform({ x: newX, y: newY });
    }
  }, [isModalDragging]);

  const handleModalMouseUp = useCallback(() => {
    if (isModalDragging) {
      setIsModalDragging(false);
      document.body.classList.remove('no-select');
      const modal = modalRef.current;
      if (modal) {
        modal.classList.remove('dragging');
      }
    }
  }, [isModalDragging]);

  useEffect(() => {
    if (isModalDragging) {
      document.addEventListener('mousemove', handleModalMouseMove);
      document.addEventListener('mouseup', handleModalMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleModalMouseMove);
        document.removeEventListener('mouseup', handleModalMouseUp);
      };
    }
  }, [isModalDragging, handleModalMouseMove, handleModalMouseUp]);
  
  // 调试日志函数，仅在非生产环境显示
  const logDebug = (message, data) => {
    if (process.env.NODE_ENV !== 'production') {
      console.log(message, data);
    }
  };
  
  // 警告日志函数，仅在非生产环境显示
  const warnDebug = (message) => {
    if (process.env.NODE_ENV !== 'production') {
      console.warn(message);
    }
  };
  
  // 组件挂载时加载图片获取实际尺寸
  useEffect(() => {
    // 如果panel存在且有图片路径
    if (panel) {
      const imagePath = panel.processedFile || panel.url || panel.preview || panel.image;
      
      // 创建引用标志，用于控制异步操作是否应该继续
      let isMounted = true;
      
      if (imagePath) {
        setIsLoadingDimensions(true);
        logDebug('正在加载图片获取实际尺寸:', imagePath);
        
        // 创建新Image对象
        const img = new Image();
        
        // 图片加载完成后获取尺寸
        img.onload = () => {
          // 检查组件是否仍然挂载
          if (isMounted) {
            // logDebug(`图片加载成功，实际尺寸: ${img.width} x ${img.height}`);
            setActualDimensions({
              width: img.width,
              height: img.height
            });
            setIsLoadingDimensions(false);
          }
        };
        
        // 图片加载失败处理
        img.onerror = (error) => {
          // 检查组件是否仍然挂载
          if (isMounted) {
            console.error('加载图片获取尺寸失败:', error);
            setIsLoadingDimensions(false);
          }
        };
        
        // 设置图片源以开始加载
        img.src = imagePath;
      }
      
      // 清理函数，在组件卸载时执行
      return () => {
        // 标记组件已卸载
        isMounted = false;
        logDebug('ImageInfoModal组件正在卸载，取消图片加载操作');
      };
    }
  }, [panel]);
  
  // 组件挂载时打印调试信息
  useEffect(() => {
    logDebug('ImageInfoModal已挂载，接收到的panel数据:', {
      panel,
      hasProcessedFile: panel?.processedFile ? '是' : '否',
      hasFileInfo: panel?.fileInfo ? '是' : '否',
      fileInfoType: panel?.fileInfo ? typeof panel.fileInfo : '无'
    });
  }, [panel]);
  
  // 获取图片信息
  const getImageInfo = () => {
    // 调试日志
    logDebug('getImageInfo被调用，panel数据:', {
      panelExists: !!panel,
      processedFile: panel?.processedFile,
      fileInfo: panel?.fileInfo,
      actualDimensions
    });
    
    // 基本检查
    if (!panel) {
      warnDebug('ImageInfoModal: panel为空');
      return null;
    }
    
    // 检查是否存在处理过的文件
    if (!panel.processedFile) {
      // 尝试使用替代字段
      if (panel.url || panel.preview || panel.image) {
        // 记录使用替代字段的情况
        logDebug('使用替代字段代替processedFile显示图片');
      } else {
        return null;
      }
    }

    // 准备返回数据
    let sizeText = '--';
    let resolutionText = isLoadingDimensions ? '加载中...' : '--';
    let formatText = '--';
    
    // 使用服务器返回的文件信息
    if (panel.fileInfo) {
      try {
        // 获取文件大小
        const size = panel.fileInfo.size || 0;
        sizeText = `${(size / (1024 * 1024)).toFixed(2)} MB`;
        
        // 获取格式
        formatText = panel.fileInfo.format || panel.fileInfo.type || 'image/jpeg';
      } catch (error) {
        console.error('处理fileInfo时出错:', error);
      }
    } else {
      // 只在确实应该有fileInfo的情况下显示警告
      if (panel.type !== 'fabric' && !panel.isEnhancedModelPanel) {
        warnDebug('ImageInfoModal: panel.fileInfo为空');
      }
    }
    
    // 优先使用实际加载的图片尺寸
    if (actualDimensions) {
      resolutionText = `${actualDimensions.width} x ${actualDimensions.height} px`;
    } else if (panel.fileInfo && panel.fileInfo.width && panel.fileInfo.height) {
      // 如果没有实际尺寸，使用fileInfo中的值
      resolutionText = `${panel.fileInfo.width} x ${panel.fileInfo.height} px`;
    }
    
    return {
      size: sizeText,
      resolution: resolutionText,
      format: formatText
    };
  };

  const handleReupload = () => {
    onClose();
    onReupload(panel);
  };

  const handleDelete = () => {
    onDelete(panel.componentId);
    onClose();
  };

  const handleDrawMask = () => {
    onClose();
    if (onDrawMask) {
      onDrawMask(panel);
    }
  };

  const imageInfo = getImageInfo();
  
  // 判断面板类型
  const isModelPanel = () => {
    return pageType === 'try-on' || pageType === 'optimize';
  };

  // 获取模态框标题 - 统一为"图片信息"
  const getModalTitle = () => {
    return '图片信息';
  };

  // 判断是否为款式图面板
  const isDesignPanel = panel && panel.type === 'design';
  // 判断是否为模特原图面板
  const isModelImagePanel = panel && panel.type === 'model';

  // 判断是否为服装原图面板
  const isClothingPanel = panel && panel.type === 'clothing';

  // 判断是否为前景图面板
  const isForegroundPanel = panel && (panel.type === 'foreground');
  // 判断页面类型
  const isTryOnPage = pageType === 'try-on';
  const isOptimizePage = pageType === 'optimize';
  const isBackgroundPage = pageType === 'background';
  const isRecolorPage = pageType === 'recolor';
  const isFabricPage = pageType === 'fabric';
  
  
  // 是否显示绘制蒙版按钮 - 根据组件类型和标签页决定
  // 仅在以下情况显示蒙版按钮:
  // 1. detail-migration 页面下，服装和模特面板都显示
  // 2. 第一个标签页（半自动蒙版）的模特原图上
  // 3. 第二个标签页（手动蒙版）的服装原图或模特图片上
  // 4. 款式优化页面的蒙版描述式标签页（mask）上
  const isDetailMigrationPage = pageType === 'detail-migration';
  const showDrawMaskButton = (
    (isDetailMigrationPage && (isClothingPanel || isModelImagePanel)) ||
    (isModelImagePanel && activeTab === 'front') ||
    (isClothingPanel && activeTab === 'other') ||
    (isModelImagePanel && activeTab === 'other') ||
    (isOptimizePage && activeTab === 'mask' && onDrawMask)
  );

  return (
    <>
      <div className="modal-backdrop" onClick={onClose} />
      <div 
        className={`image-info-modal${isModalDragging ? ' dragging' : ''}`}
        ref={modalRef}
        style={{
          top: position.top,
          left: position.left,
          transform: `translateX(16px) translate(${dragTransform.x}px, ${dragTransform.y}px)`,
          cursor: isModalDragging ? 'grabbing' : 'grab',
          // 移动端使用 CSS 媒体查询控制，这里保持桌面端样式
        }}
        onMouseDown={handleModalMouseDown}
      >
        {/* 图片信息区域 */}
        <div className="image-info">
          <h3>图片信息</h3>
          <div className="info-grid">
            <span>文件大小：</span>
            <span>{imageInfo?.size || '--'}</span>
            <span>分辨率：</span>
            <span>{imageInfo?.resolution || (isLoadingDimensions ? '加载中...' : '--')}</span>
            <span>格式：</span>
            <span>{imageInfo?.format || '--'}</span>
          </div>
        </div>

        {/* 操作按钮区域 */}
        <div className="operations">
          {showDrawMaskButton && (
            <button className="operation-btn draw-mask" onClick={handleDrawMask}>
              绘制蒙版
            </button>
          )}
          <button className="operation-btn reupload" onClick={handleReupload}>
            重新上传
          </button>
          <button className="operation-btn delete" onClick={handleDelete}>
            删除
          </button>
        </div>

        <button 
          className="small-close-button"
          onClick={onClose}
          title="关闭"
        >
          <MdClose />
        </button>
      </div>
    </>
  );
};

export default ImageInfoModal; 