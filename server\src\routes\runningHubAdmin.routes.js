/**
 * RunningHub管理路由
 * 提供RunningHub平台的管理功能API
 */

const express = require('express');
const router = express.Router();
const { auth, requireRole } = require('../middleware/auth.middleware');
const { createError } = require('../utils/error');
const RunningHubService = require('../services/runningHub/runningHubService');
const RunningHubConfig = require('../models/RunningHubConfig');
const FlowTask = require('../models/FlowTask');

// 中间件：检查是否为管理员
router.use(auth, requireRole('admin'));

/**
 * 获取配置列表
 * GET /api/runninghub/admin/configs
 */
router.get('/configs', async (req, res) => {
  try {
    const configs = await RunningHubConfig.find({ createdBy: req.user._id })
      .sort({ isDefault: -1, createdAt: -1 });

    res.json({
      success: true,
      data: configs
    });
  } catch (error) {
    console.error('获取RunningHub配置列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取配置列表失败'
    });
  }
});

/**
 * 创建配置
 * POST /api/runninghub/admin/configs
 */
router.post('/configs', async (req, res) => {
  try {
    const { name, apiKey, workflowMappings, description, isDefault } = req.body;

    if (!name || !apiKey) {
      return res.status(400).json({
        success: false,
        message: '配置名称和API密钥是必需的'
      });
    }

    // 如果设置为默认配置，先取消其他默认配置
    if (isDefault) {
      await RunningHubConfig.updateMany(
        { createdBy: req.user._id },
        { isDefault: false }
      );
    }

    const config = new RunningHubConfig({
      name,
      apiKey,
      workflowMappings: new Map(Object.entries(workflowMappings || {})),
      description,
      isDefault: isDefault || false,
      createdBy: req.user._id
    });

    await config.save();

    res.json({
      success: true,
      data: config,
      message: '配置创建成功'
    });
  } catch (error) {
    console.error('创建RunningHub配置失败:', error);
    res.status(500).json({
      success: false,
      message: '创建配置失败'
    });
  }
});

/**
 * 更新配置
 * PUT /api/runninghub/admin/configs/:id
 */
router.put('/configs/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, apiKey, workflowMappings, description, isDefault, enabled } = req.body;

    const config = await RunningHubConfig.findOne({
      _id: id,
      createdBy: req.user._id
    });

    if (!config) {
      return res.status(404).json({
        success: false,
        message: '配置不存在'
      });
    }

    // 如果设置为默认配置，先取消其他默认配置
    if (isDefault && !config.isDefault) {
      await RunningHubConfig.updateMany(
        { createdBy: req.user._id },
        { isDefault: false }
      );
    }

    // 更新配置
    if (name) config.name = name;
    if (apiKey) config.apiKey = apiKey;
    if (workflowMappings) {
      config.workflowMappings = new Map(Object.entries(workflowMappings));
    }
    if (description !== undefined) config.description = description;
    if (isDefault !== undefined) config.isDefault = isDefault;
    if (enabled !== undefined) config.enabled = enabled;

    await config.save();

    res.json({
      success: true,
      data: config,
      message: '配置更新成功'
    });
  } catch (error) {
    console.error('更新RunningHub配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新配置失败'
    });
  }
});

/**
 * 删除配置
 * DELETE /api/runninghub/admin/configs/:id
 */
router.delete('/configs/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const config = await RunningHubConfig.findOne({
      _id: id,
      createdBy: req.user._id
    });

    if (!config) {
      return res.status(404).json({
        success: false,
        message: '配置不存在'
      });
    }

    await RunningHubConfig.findByIdAndDelete(id);

    res.json({
      success: true,
      message: '配置删除成功'
    });
  } catch (error) {
    console.error('删除RunningHub配置失败:', error);
    res.status(500).json({
      success: false,
      message: '删除配置失败'
    });
  }
});

/**
 * 测试配置
 * POST /api/runninghub/admin/configs/:id/test
 */
router.post('/configs/:id/test', async (req, res) => {
  try {
    const { id } = req.params;

    const config = await RunningHubConfig.findOne({
      _id: id,
      createdBy: req.user._id
    });

    if (!config) {
      return res.status(404).json({
        success: false,
        message: '配置不存在'
      });
    }

    // 创建RunningHub服务实例并测试
    const runningHubService = new RunningHubService();
    runningHubService.setApiKey(config.getFullApiKey());

    try {
      // 测试获取账户信息
      const accountResult = await runningHubService.getAccountInfo();

      if (accountResult.success) {
        // 更新配置测试状态和账户信息
        config.testStatus = 'success';
        config.testError = null;
        config.lastTestedAt = new Date();
        await config.updateAccountInfo(accountResult.data);

        res.json({
          success: true,
          data: {
            testStatus: 'success',
            accountInfo: accountResult.data
          },
          message: '配置测试成功'
        });
      } else {
        throw new Error(accountResult.error || '测试失败');
      }
    } catch (testError) {
      // 更新配置测试状态
      config.testStatus = 'failed';
      config.testError = testError.message;
      config.lastTestedAt = new Date();
      await config.save();

      res.status(400).json({
        success: false,
        message: `配置测试失败: ${testError.message}`
      });
    }
  } catch (error) {
    console.error('测试RunningHub配置失败:', error);
    res.status(500).json({
      success: false,
      message: '测试配置失败'
    });
  }
});

/**
 * 获取系统工作流列表
 * GET /api/runninghub/admin/workflows
 */
router.get('/workflows', async (req, res) => {
  try {
    const Workflow = require('../models/Workflow');

    // 从数据库获取启用的工作流
    const workflows = await Workflow.findEnabled();

    // 如果数据库中没有工作流，返回空列表
    const workflowList = workflows.map(workflow => ({
      id: workflow.id,
      name: workflow.name,
      displayName: workflow.displayName,
      description: workflow.description || '',
      category: workflow.category,
      version: workflow.version,
      enabled: workflow.enabled,
      supportedPlatforms: workflow.supportedPlatforms,
      recommendedPlatform: workflow.recommendedPlatform,
      tags: workflow.tags,
      priority: workflow.priority,
      usageStats: workflow.usageStats,
      successRate: workflow.successRate
    }));

    res.json({
      success: true,
      data: workflowList
    });
  } catch (error) {
    console.error('获取工作流列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取工作流列表失败'
    });
  }
});

/**
 * 获取工作流平台信息
 * GET /api/runninghub/admin/workflow-info
 */
router.get('/workflow-info', async (req, res) => {
  try {
    const { getPlatformConfig } = require('../config/platformConfig');
    const config = getPlatformConfig();

    // 获取工作流列表
    const workflowsResponse = await new Promise((resolve) => {
      router.handle({ method: 'GET', url: '/workflows' }, {
        json: (data) => resolve(data)
      }, () => {});
    });

    const workflows = workflowsResponse?.data || [];

    // 获取所有工作流的平台推荐信息
    const workflowInfo = {};
    workflows.forEach(workflow => {
      const { getRecommendedPlatform } = require('../config/platformConfig');
      const recommendedPlatform = getRecommendedPlatform(workflow.id, {});
      const hasRunningHubMapping = !!config.runningHub.workflowMappings[workflow.id];

      workflowInfo[workflow.id] = {
        ...workflow,
        recommendedPlatform,
        hasRunningHubMapping,
        runningHubWorkflowId: config.runningHub.workflowMappings[workflow.id] || null
      };
    });

    res.json({
      success: true,
      data: {
        workflowInfo,
        workflows,
        platformConfig: {
          runningHubEnabled: config.runningHub.enabled,
          comfyuiEnabled: config.comfyui.enabled,
          defaultPlatform: config.defaultPlatform
        }
      }
    });
  } catch (error) {
    console.error('获取工作流平台信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取工作流平台信息失败'
    });
  }
});

/**
 * 获取任务列表
 * GET /api/runninghub/admin/tasks
 */
router.get('/tasks', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, userId, startDate, endDate } = req.query;
    
    // 这里应该从数据库获取任务列表
    // 暂时返回模拟数据
    const tasks = [];
    const total = 0;
    
    res.json({
      success: true,
      data: tasks,
      total,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('获取任务列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '获取任务列表失败'
    });
  }
});

/**
 * 获取任务详情
 * GET /api/runninghub/admin/tasks/:taskId
 */
router.get('/tasks/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    
    // 这里应该从数据库获取任务详情
    // 暂时返回模拟数据
    const task = {
      id: taskId,
      taskId: taskId,
      taskType: 'simple',
      status: 'success',
      workflowId: '1850925505116598274',
      createdAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      duration: 30,
      results: []
    };
    
    res.json({
      success: true,
      data: task
    });
  } catch (error) {
    console.error('获取任务详情失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '获取任务详情失败'
    });
  }
});

/**
 * 删除任务
 * DELETE /api/runninghub/admin/tasks/:taskId
 */
router.delete('/tasks/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    
    // 这里应该从数据库删除任务
    // 暂时返回成功响应
    
    res.json({
      success: true,
      message: '任务删除成功'
    });
  } catch (error) {
    console.error('删除任务失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '删除任务失败'
    });
  }
});

/**
 * 批量删除任务
 * POST /api/runninghub/admin/tasks/batch-delete
 */
router.post('/tasks/batch-delete', async (req, res) => {
  try {
    const { taskIds } = req.body;
    
    if (!Array.isArray(taskIds) || taskIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: '请提供有效的任务ID列表'
      });
    }
    
    // 这里应该批量删除任务
    // 暂时返回成功响应
    
    res.json({
      success: true,
      message: `成功删除 ${taskIds.length} 个任务`,
      deletedCount: taskIds.length
    });
  } catch (error) {
    console.error('批量删除任务失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '批量删除任务失败'
    });
  }
});

/**
 * 获取统计数据
 * GET /api/runninghub/admin/stats
 */
router.get('/stats', async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    
    // 这里应该从数据库计算统计数据
    // 暂时返回模拟数据
    const stats = {
      totalTasks: 0,
      runningTasks: 0,
      successTasks: 0,
      failedTasks: 0,
      totalConfigs: 0,
      totalUsers: 0,
      apiCallsToday: 0,
      apiCallsThisMonth: 0,
      averageExecutionTime: 0,
      successRate: 0
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取统计数据失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '获取统计数据失败'
    });
  }
});

/**
 * 获取用户配置列表
 * GET /api/runninghub/admin/configs
 */
router.get('/configs', async (req, res) => {
  try {
    const { page = 1, limit = 10, userId, status } = req.query;
    
    // 这里应该从数据库获取用户配置列表
    // 暂时返回模拟数据
    const configs = [];
    const total = 0;
    
    res.json({
      success: true,
      data: configs,
      total,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('获取用户配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '获取用户配置失败'
    });
  }
});

/**
 * 测试用户配置
 * POST /api/runninghub/admin/configs/:configId/test
 */
router.post('/configs/:configId/test', async (req, res) => {
  try {
    const { configId } = req.params;
    
    // 这里应该获取配置并测试
    // 暂时返回成功响应
    
    res.json({
      success: true,
      message: '配置测试成功',
      data: {
        configId,
        status: 'valid',
        accountInfo: {
          userId: 'test_user',
          username: 'Test User',
          memberType: 'premium',
          balance: 1000
        }
      }
    });
  } catch (error) {
    console.error('测试配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '测试配置失败'
    });
  }
});

/**
 * 切换用户配置状态
 * PUT /api/runninghub/admin/configs/:configId/toggle
 */
router.put('/configs/:configId/toggle', async (req, res) => {
  try {
    const { configId } = req.params;
    const { enabled } = req.body;
    
    // 这里应该更新配置状态
    // 暂时返回成功响应
    
    res.json({
      success: true,
      message: `配置已${enabled ? '启用' : '禁用'}`,
      data: {
        configId,
        enabled
      }
    });
  } catch (error) {
    console.error('切换配置状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '切换配置状态失败'
    });
  }
});

/**
 * 获取系统配置
 * GET /api/runninghub/admin/system-config
 */
router.get('/system-config', async (req, res) => {
  try {
    // 这里应该从数据库或配置文件获取系统配置
    // 暂时返回模拟数据
    const systemConfig = {
      maxConcurrentTasks: 10,
      defaultTimeout: 300000,
      enableAutoCleanup: true,
      cleanupInterval: 86400000,
      maxTaskHistory: 1000,
      enableLogging: true,
      logLevel: 'info'
    };
    
    res.json({
      success: true,
      data: systemConfig
    });
  } catch (error) {
    console.error('获取系统配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '获取系统配置失败'
    });
  }
});

/**
 * 更新系统配置
 * PUT /api/runninghub/admin/system-config
 */
router.put('/system-config', async (req, res) => {
  try {
    const config = req.body;
    
    // 这里应该验证并保存系统配置
    // 暂时返回成功响应
    
    res.json({
      success: true,
      message: '系统配置更新成功',
      data: config
    });
  } catch (error) {
    console.error('更新系统配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '更新系统配置失败'
    });
  }
});

/**
 * 获取API使用统计
 * GET /api/runninghub/admin/usage-stats
 */
router.get('/usage-stats', async (req, res) => {
  try {
    const { startDate, endDate, groupBy = 'day' } = req.query;
    
    // 这里应该从数据库获取使用统计
    // 暂时返回模拟数据
    const usageStats = {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      averageResponseTime: 0,
      dailyStats: [],
      topUsers: [],
      topWorkflows: []
    };
    
    res.json({
      success: true,
      data: usageStats
    });
  } catch (error) {
    console.error('获取API使用统计失败:', error);
    res.status(500).json({
      success: false,
      error: error.message || '获取API使用统计失败'
    });
  }
});

module.exports = router;
