# 认证组件库

本目录包含与用户认证相关的React组件，包括登录、注册和密码修改等功能。

## 组件

### CaptchaHandler

验证码处理组件，负责发送验证码并展示倒计时。

#### 用法

```jsx
<CaptchaHandler 
  phone={phoneNumber}
  type="register"
  onCaptchaRequest={handleCaptchaRequest}
  countdown={countdown}
  isLoading={loading}
/>
```

#### 属性

| 属性 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| phone | string | 是 | - | 手机号码 |
| type | string | 否 | 'register' | 验证码类型，可选值: 'register', 'login', 'reset' |
| onCaptchaRequest | function | 是 | - | 请求验证码的回调函数，接收手机号作为参数 |
| disabled | boolean | 否 | false | 是否禁用按钮 |
| countdown | number | 否 | 0 | 倒计时秒数，为0时不显示倒计时 |
| isLoading | boolean | 否 | false | 是否显示加载状态 |
| className | string | 否 | '' | 额外的CSS类名 |
| buttonText | string | 否 | '获取验证码' | 按钮文本 |

### LoginForm

登录表单组件，处理用户登录逻辑。

#### 用法

```jsx
<LoginForm 
  onClose={handleClose}
  onLoginSuccess={handleLoginSuccess}
  onSwitchToRegister={handleSwitchToRegister}
/>
```

#### 属性

| 属性 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| onClose | function | 是 | - | 关闭登录表单的回调函数 |
| onLoginSuccess | function | 否 | - | 登录成功的回调函数，接收通知对象作为参数 |
| onSwitchToRegister | function | 否 | - | 切换到注册表单的回调函数 |
| className | string | 否 | '' | 额外的CSS类名 |

### RegisterForm

注册表单组件，处理用户注册逻辑。

#### 用法

```jsx
<RegisterForm 
  onClose={handleClose}
  onRegisterSuccess={handleRegisterSuccess}
  onSwitchToLogin={handleSwitchToLogin}
/>
```

#### 属性

| 属性 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| onClose | function | 是 | - | 关闭注册表单的回调函数 |
| onRegisterSuccess | function | 否 | - | 注册成功或发送验证码成功的回调函数，接收通知对象作为参数 |
| onSwitchToLogin | function | 否 | - | 切换到登录表单的回调函数 |
| className | string | 否 | '' | 额外的CSS类名 |

### PasswordStrength

密码强度显示组件，用于可视化展示密码强度和安全建议。

#### 用法

```jsx
<PasswordStrength strength={passwordStrengthData} />
```

#### 属性

| 属性 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| strength | object | 是 | - | 密码强度数据对象，包含score、level和feedback等属性 |

### ResetPasswordForm

修改密码表单组件，提供用户修改密码的界面和功能。

#### 用法

```jsx
<ResetPasswordForm 
  onClose={handleClose}
  onResetSuccess={handleResetSuccess}
  onSwitchToLogin={handleSwitchToLogin}
/>
```

#### 属性

| 属性 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| onClose | function | 是 | - | 关闭修改密码表单的回调函数 |
| onResetSuccess | function | 否 | - | 修改密码成功或发送验证码成功的回调函数，接收通知对象作为参数 |
| onSwitchToLogin | function | 否 | - | 切换到登录表单的回调函数 |
| className | string | 否 | '' | 额外的CSS类名 |

#### 功能特点

- 手机号验证
- 验证码发送与验证
- 新密码输入及强度检查
- 确认密码匹配验证
- 提交表单进行密码修改
- 错误处理和提示

## LoginForm

`LoginForm` 组件是一个完整的登录表单，提供以下功能：

- 用户名/手机号和密码验证
- 记住登录状态
- 显示密码功能
- 登录尝试失败处理
- 账户锁定状态显示
- 异常登录检测通知

### 使用方法

```jsx
import LoginForm from './components/Auth/LoginForm';

// 在您的组件中
const MyComponent = () => {
  const handleLoginSuccess = (notification) => {
    // 处理登录成功后的通知
    console.log(notification.type, notification.message);
  };

  return (
    <LoginForm 
      onClose={() => setShowLoginModal(false)}
      onForgotPassword={() => setShowResetPasswordModal(true)}
      onLoginSuccess={handleLoginSuccess}
      className="custom-login-form"
    />
  );
};
```

### 属性

| 属性名 | 类型 | 描述 |
| ------ | ---- | ---- |
| onClose | () => void | 关闭登录表单的回调函数 |
| onForgotPassword | () => void | 处理忘记密码点击的回调函数 |
| onLoginSuccess | (notification: { type: string, message: string }) => void | 可选，登录成功时的回调函数 |
| className | string | 可选，自定义CSS类名 |

### 关于类型处理的说明

由于项目是从JavaScript迁移到TypeScript的过程中，我们在LoginForm组件中使用了类型断言来处理`useAuth`钩子的返回值。这是因为原始的JavaScript上下文提供了一组函数，但在TypeScript中没有适当的类型定义。

我们定义了两个接口来解决这个问题：
- `LoginResult`: 定义了login函数的返回值类型
- `Auth`: 定义了我们需要从useAuth钩子中使用的函数

## CaptchaHandler

验证码处理按钮组件，用于发送验证码。

### 使用方法

```jsx
import { CaptchaHandler } from '../components/Auth';
import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

function RegistrationForm() {
  const [phone, setPhone] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const auth = useAuth();
  
  // 处理验证码请求
  const handleCaptchaRequest = async (phoneNumber, type) => {
    if (!phoneNumber) {
      setError('请输入手机号');
      return;
    }
    
    if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
      setError('请输入有效的手机号');
      return;
    }
    
    try {
      setLoading(true);
      const result = await auth.sendVerificationCode(phoneNumber);
      
      if (result.success) {
        setError('');
        startCountdown();
      } else {
        setError(result.message || '发送验证码失败');
      }
    } catch (error) {
      setError(error.message || '发送验证码失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };
  
  // 开始倒计时
  const startCountdown = () => {
    let count = 60;
    setCountdown(count);
    
    const timer = setInterval(() => {
      count -= 1;
      setCountdown(count);
      
      if (count <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  };
  
  return (
    <form>
      <div className="form-group">
        <label>手机号</label>
        <input 
          type="text" 
          value={phone} 
          onChange={(e) => setPhone(e.target.value)} 
          placeholder="请输入手机号"
        />
        
        <CaptchaHandler 
          phone={phone}
          type="register"
          onCaptchaRequest={handleCaptchaRequest}
          countdown={countdown}
          isLoading={loading}
        />
      </div>
      
      {error && <div className="error-message">{error}</div>}
    </form>
  );
}
```

### 属性说明

| 属性名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| phone | string | 是 | - | 手机号码 |
| type | 'register' \| 'reset' | 是 | - | 验证码类型 |
| onCaptchaRequest | function | 是 | - | 点击按钮时触发的回调函数 |
| disabled | boolean | 否 | false | 是否禁用按钮 |
| countdown | number | 否 | 0 | 当前倒计时，由父组件控制 |
| isLoading | boolean | 否 | false | 是否处于加载状态 |
| className | string | 否 | 'captcha-btn' | 自定义类名 |
| buttonText | string | 否 | '获取验证码' | 按钮文本 |

## PasswordStrength

密码强度检查组件，用于显示密码强度和相关反馈。

### 使用方法

参见组件注释和实现代码。

## AuthModals 组件

认证模态窗口组件，包含登录、注册和修改密码三个模态窗口。

### 用法

```tsx
import { AuthModals } from './components/Auth';

function App() {
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showResetPassword, setShowResetPassword] = useState(false);
  
  const handleNotification = (notification) => {
    // 处理通知消息
    console.log(notification);
  };
  
  return (
    <div className="app">
      <button onClick={() => setShowLoginModal(true)}>登录/注册</button>
      
      <AuthModals 
        showLoginModal={showLoginModal}
        showResetPassword={showResetPassword}
        setShowLoginModal={setShowLoginModal}
        setShowResetPassword={setShowResetPassword}
        onNotification={handleNotification}
      />
    </div>
  );
}
```

### 属性

| 属性名 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|------|------|
| showLoginModal | boolean | ✅ | - | 是否显示登录/注册模态窗口 |
| showResetPassword | boolean | ✅ | - | 是否显示修改密码模态窗口 |
| setShowLoginModal | (show: boolean) => void | ✅ | - | 设置登录/注册模态窗口的显示状态 |
| setShowResetPassword | (show: boolean) => void | ✅ | - | 设置修改密码模态窗口的显示状态 |
| onNotification | (notification: { type: string, message: string }) => void | ❌ | - | 通知回调函数，用于处理操作结果的通知 |
| className | string | ❌ | '' | 自定义样式类名 |
| renderIcon | (Icon: React.ComponentType) => React.ReactNode | ❌ | (Icon) => <Icon /> | 自定义图标渲染函数 |

### 功能说明

1. 提供登录/注册和修改密码两个独立的模态窗口
2. 登录/注册模态窗口提供标签页切换功能
3. 支持在忘记密码时切换到修改密码模态窗口
4. 修改密码成功后可以自动切换回登录模态窗口
5. 所有操作结果通过onNotification回调函数通知外部组件 