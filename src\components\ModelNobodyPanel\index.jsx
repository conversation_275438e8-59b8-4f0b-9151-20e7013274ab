import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';
import '../../../src/styles/buttons.css'; // 导入按钮样式，包括开关组件

const ModelNobodyPanel = ({
  selectedModel,
  onExpandClick,
  useModel = true,
  onUseModelChange,
  pageType = 'default',
}) => {
  const [isUseModel, setIsUseModel] = useState(useModel);
  
  // 组件初始化时，确保使用默认设置
  useEffect(() => {
    if (useModel !== isUseModel) {
      setIsUseModel(useModel);
    }
  }, [useModel]);

  const handleModelToggle = () => {
    const newValue = !isUseModel;
    setIsUseModel(newValue);
    if (onUseModelChange) {
      onUseModelChange(newValue);
    }
  };
  
  const getDisplayText = () => {
    if (!isUseModel) {
      return '不使用模特，仅生成服装';
    }
    
    if (!selectedModel) {
      if (pageType === 'virtual') {
        return '设置虚拟模特的自定义要求';
      }
      return '选择合适的模特';
    } else {
      if (pageType === 'virtual') {
        return '已设置自定义要求';
      }
      return `已选择 ${selectedModel.type === 'custom' ? '自定义模特' : `#${selectedModel.componentId} ${selectedModel.name}`}`;
    }
  };

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          {isUseModel ? (
            selectedModel ? (
              <ThumbnailWithPreview
                imageUrl={selectedModel.type === 'custom' ? 
                  '/images/icons/model-custom.png' : 
                  (selectedModel.preview || '/images/icons/model.png')
                }
                alt={`模特 ${selectedModel.name || '自定义模特'}`}
                status="completed"
                featureName="模特预览"
              />
            ) : (
              <img src="/images/icons/model.png" alt="模特" className="component-icon" />
            )
          ) : (
            <img src="/images/icons/model-nobody.png" alt="无模特" className="component-icon" />
          )}
          <div className="component-text">
            <h3>
              {isUseModel ? "模特" : "无模特"}
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={isUseModel}
                  onChange={handleModelToggle}
                />
                <span className="toggle-track"></span>
              </label>
            </h3>
            <div className="component-content">
              <p>{getDisplayText()}</p>
            </div>
          </div>
        </div>
        <div className="panel-actions">
          {isUseModel && (
            <button 
              className="expand-btn"
              onClick={onExpandClick}
              title={pageType === 'virtual' ? "设置自定义要求" : "选择模特"}
            >
              <span></span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

ModelNobodyPanel.propTypes = {
  selectedModel: PropTypes.shape({
    componentId: PropTypes.string,
    name: PropTypes.string,
    type: PropTypes.string,
    source: PropTypes.string,
    preview: PropTypes.string
  }),
  onExpandClick: PropTypes.func.isRequired,
  useModel: PropTypes.bool,
  onUseModelChange: PropTypes.func,
  pageType: PropTypes.string,
};

export default ModelNobodyPanel; 