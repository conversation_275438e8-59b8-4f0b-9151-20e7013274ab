/* 导入统一样式 */
@import '../../styles/theme.css';
@import '../../styles/buttons.css';
@import '../../styles/inputs.css';

/* 复用面板组件的通用样式 */
.panel-component {
  margin-bottom: 10px;
}

/* 蒙版描述面板 */
.mask-description-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  min-height: 88px;
  display: flex;
  margin-bottom: 10px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  position: relative;
}

.mask-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
}

.mask-label {
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
  flex-shrink: 0;
}

.mask-label span {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

.mask-area {
  flex: 1;
  padding: 0 16px;
  padding-right: 66px; /* 为右上角的提示按钮留出空间 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.mask-description-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 12px 0;
}

/* 标签按钮样式 */
.tags-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-option {
  padding: 4px 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-normal);
  outline: none;
}

.tag-option:hover {
  background: var(--bg-hover);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.tag-option.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 输入框样式 */
.mask-description-input {
  width: 100%;
  padding: 6px 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: var(--transition-normal);
}

.mask-description-input:hover {
  border-color: var(--brand-primary);
}

.mask-description-input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

.mask-description-input::placeholder {
  color: var(--text-tertiary);
}

/* 适配暗色主题 */
[data-theme="dark"] .tag-option {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

[data-theme="dark"] .tag-option:hover {
  background: var(--bg-hover);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

[data-theme="dark"] .tag-option.active {
  background: rgba(255, 60, 106, 0.15);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
} 