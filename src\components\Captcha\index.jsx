import React, { useState, useEffect } from 'react';

import './index.css';
import logger from '../../utils/logger';

const Captcha = ({ onVerify }) => {
  const [captchaSvg, setCaptchaSvg] = useState('');
  const [userInput, setUserInput] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);

  const getCaptcha = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await fetch( `${process.env.REACT_APP_BACKEND_URL}/auth/captcha/get`, {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const svgText = await response.text();
      setCaptchaSvg(svgText);
      setLoading(false);
    } catch (error) {
      logger.error('获取验证码失败', error);
      setError('获取验证码失败，请刷新重试');
      setCaptchaSvg('');
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!userInput&&process.env.ENABLE_PHONE_VERIFICATION === 'true') {
      setError('请输入验证码');
      return;
    }

    try {
      setLoading(true);
      setError('');
      const response = await fetch( `${process.env.REACT_APP_BACKEND_URL}/auth/captcha/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          code: userInput
        })
      });

      const data = await response.json();

      if (data.success) {
        onVerify(true);
      } else {
        throw new Error(data.message || '验证失败');
      }
    } catch (error) {
      logger.error('验证失败', error);
      setError('验证失败，请重试');
      getCaptcha();
      setUserInput('');
    } finally {
      setLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (e) => {
    const value = e.target.value.replace(/[^\da-zA-Z]/g, '').toUpperCase();
    if (value.length <= 6) {
      setUserInput(value);
      setError('');
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && userInput.length === 6) {
      handleSubmit();
    }
  };

  useEffect(() => {
    getCaptcha();
    return () => {
      setCaptchaSvg('');
      setUserInput('');
      setError('');
    };
  }, []);

  return (
    <div className="captcha-container">
      <div className="captcha-box">
        <h3 className="captcha-title">请输入图片中的验证码</h3>

        <div className="captcha-display">
          <div className="captcha-svg-container">
            {loading ? (
              <div className="loading">加载中</div>
            ) : error ? (
              <div className="error">{error}</div>
            ) : (
              <div
                className="captcha-svg"
                dangerouslySetInnerHTML={{ __html: captchaSvg }}
              />
            )}
            <button
              className="refresh-button"
              onClick={getCaptcha}
              disabled={loading}
              aria-label="刷新验证码"
              title="刷新验证码"
            />
          </div>
        </div>

        <div className="input-group">
          <div className="input-container">
            <input
              type="text"
              value={userInput}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="请输入验证码"
              maxLength={6}
              disabled={loading}
              autoComplete="off"
            />
          </div>

          <button
            className="verify-button"
            onClick={handleSubmit}
            disabled={!userInput || userInput.length < 6 || loading}
          >
            验证
          </button>

          {error && <div className="error-message">{error}</div>}
        </div>
      </div>
    </div>
  );
};

export default Captcha;