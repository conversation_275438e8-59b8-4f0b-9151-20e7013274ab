// 网站主题配色方案

// 主色 - 渐变色
export const primaryGradient = 'linear-gradient(45deg, #FF8C42, #FF3C6A)';
export const primaryStartColor = '#FF8C42';  // 渐变起始色（橙色）
export const primaryEndColor = '#FF3C6A';    // 渐变结束色（玫红色）

// 主色的半透明版本（用于图标背景等）
export const primaryAlpha = 'rgba(255, 60, 106, 0.1)';

// 交互效果
export const hoverEffect = 'brightness(1.1)';

// 按钮文字颜色
export const buttonTextLight = '#FFFFFF';        // 深色背景上的文字
export const buttonTextDark = '#FF3C6A';      // 浅色背景上的文字
export const buttonTextGray = '#666666';         // 灰色按钮文字

// 文字颜色
export const textPrimary = '#333333';           // 主要文字
export const textSecondary = '#666666';         // 次要文字

// 背景颜色
export const bgWhite = '#FFFFFF';               // 白色背景
export const bgGray = '#F5F5F5';             // 灰色背景
export const bgLight = '#FFF1F3';            // 浅色背景

// 边框颜色
export const borderColor = '#DDDDDD';
export const shadowColor = 'rgba(0, 0, 0, 0.1)';

// 使用示例：
// import { primaryGradient, primaryEndColor, primaryAlpha, buttonTextLight, buttonTextDark } from '../theme/colors';
// 
// 主按钮：
// background: primaryGradient;
// color: buttonTextLight;
//
// 次要按钮：
// background: bgWhite;
// color: buttonTextDark;
// border: 1px solid primaryEndColor; 