const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const { auth } = require('../modules/auth');

const VirtualModel = require('../models/VirtualModel');
const { MAX_MODELS_COUNT } = require('../config/constants');


// 添加一个简单的测试路由，验证路由是否正常工作
router.get('/test', (req, res) => {
  console.log('收到模型路由测试请求');
  res.json({ success: true, message: '模型路由测试成功' });
});

// 添加错误处理测试路由
router.get('/test-error', (req, res) => {
  console.log('收到错误测试请求');
  try {
    // 故意抛出一个错误
    throw new Error('这是一个测试错误');
  } catch (error) {
    console.error('测试错误:', error);
    res.status(500).json({
      success: false,
      message: '测试错误: ' + error.message
    });
  }
});

/**
 * 保存虚拟模特到服务器文件系统
 * POST /api/models/save-virtual-model
 */
router.post('/save-virtual-model', auth, async (req, res) => {
  const userId = req.user._id;
  try {
    console.log('收到保存虚拟模特请求:', req.body);
    
    // 检查用户的虚拟模特数量是否达到上限
    const currentCount = await VirtualModel.countDocuments({ userId });
    if (currentCount >= MAX_MODELS_COUNT) {
      return res.status(400).json({
        success: false,
        message: `虚拟模特数量已达上限(${MAX_MODELS_COUNT}个)`
      });
    }

    const modelData = {
      ...req.body,
      userId,
      generationInfo: {
        taskId: req.body.taskId,
        prompt: req.body.prompt,
        negative_prompt: req.body.negative_prompt,
        seed: req.body.seed,
        images: req.body.isAll ? req.body.images : [{
          url: req.body.imageUrl,
          fileInfo: req.body.fileInfo
        }]
      }
    };

    // 如果是编辑模式，更新现有模特
    if (req.body.id) {
      const existingModel = await VirtualModel.findOne({ 
        id: req.body.id,
        userId 
      });

      if (existingModel) {
        const updatedModel = await VirtualModel.findOneAndUpdate(
          { id: req.body.id, userId },
          modelData,
          { new: true }
        );
        return res.json({
          success: true,
          message: '虚拟模特更新成功',
          data: updatedModel
        });
      }
    }

    // 创建新的虚拟模特
    const virtualModel = new VirtualModel(modelData);
    await virtualModel.save();

    res.json({
      success: true,
      message: '虚拟模特保存成功',
      data: virtualModel
    });

  } catch (error) {
    console.error('保存虚拟模特失败:', error);
    res.status(500).json({
      success: false,
      message: '保存虚拟模特失败',
      error: error.message
    });
  }
});

/**
 * 获取虚拟模特信息
 * GET /api/models/virtual-model/:id
 */
router.get('/virtual-model/:id', async (req, res) => {
  console.log('收到获取虚拟模特信息请求, 模特ID:', req.params.id);
  try {
    const modelId = req.params.id;
    
    if (!modelId) {
      return res.status(400).json({
        success: false,
        message: '模特ID不能为空'
      });
    }
    
    // 获取用户ID，如未登录则使用开发者ID
    const userId = req.query.userId || 'developer';
    
    // 构建模特信息文件路径
    const modelInfoPath = path.join(
      __dirname, 
      '../..', 
      'storage',
      userId,
      'model',
      'virtual',
      'virtualmodelmanager',
      modelId,
      'model_info.json'
    );
    
    console.log('尝试读取模特信息文件:', modelInfoPath);
    
    // 检查文件是否存在
    if (!fs.existsSync(modelInfoPath)) {
      console.log('模特信息文件不存在:', modelInfoPath);
      return res.status(404).json({
        success: false,
        message: '模特信息不存在'
      });
    }
    
    // 读取模特信息文件
    const modelInfoData = fs.readFileSync(modelInfoPath, 'utf8');
    const modelInfo = JSON.parse(modelInfoData);
    
    // 构建图片URL
    const baseUrl = `${req.protocol}://${req.get('host')}/model/virtual/${userId}/model/virtual/virtualmodelmanager/${modelId}`;
    
    // 添加服务器上的图片URL到返回数据中
    if (modelInfo.variantImages && modelInfo.variantImages.length > 0) {
      modelInfo.variantImages = modelInfo.variantImages.map(image => ({
        ...image,
        url: `${baseUrl}/${image.fileName}`
      }));
    }
    
    // 添加原始图片URL
    modelInfo.originalImageUrl = `${baseUrl}/original.jpg`;
    
    res.json({
      success: true,
      model: modelInfo
    });
  } catch (error) {
    console.error('获取虚拟模特信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误: ' + error.message
    });
  }
});

/**
 * 获取用户的虚拟模特列表
 * GET /api/models/virtual-models
 */
router.get('/virtual-models',auth, async (req, res) => {
  try {
    const userId = req.user._id;
    const models = await VirtualModel.find({ userId })
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: models
    });

  } catch (error) {
    console.error('获取虚拟模特列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取虚拟模特列表失败',
      error: error.message
    });
  }
});

/**
 * 删除虚拟模特
 * DELETE /api/models/virtual-model/:id
 */
router.delete('/virtual-model/:id',auth, async (req, res) => {
  try {
    const userId = req.user._id;
    const modelId = req.params.id;

    const result = await VirtualModel.findOneAndDelete({
      id: modelId,
      userId
    });

    if (!result) {
      return res.status(404).json({
        success: false,
        message: '未找到要删除的虚拟模特'
      });
    }

    res.json({
      success: true,
      message: '虚拟模特删除成功'
    });

  } catch (error) {
    console.error('删除虚拟模特失败:', error);
    res.status(500).json({
      success: false,
      message: '删除虚拟模特失败',
      error: error.message
    });
  }
});

// 确保导出router
module.exports = router; 