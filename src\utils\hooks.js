import { useCallback, useRef } from 'react';

/**
 * 自定义防抖Hook
 * @param {Function} fn - 需要防抖的函数
 * @param {number} delay - 防抖延迟时间（毫秒）
 * @param {Array} deps - 依赖数组，用于useCallback
 * @returns {Function} - 防抖后的函数
 * 
 * @example
 * // 基础用法
 * const debouncedFn = useDebounce((value) => {
 *   console.log(value);
 * }, 280);
 * 
 * // 带依赖的用法
 * const debouncedFn = useDebounce((value) => {
 *   console.log(value, someValue);
 * }, 280, [someValue]);
 */
export const useDebounce = (fn, delay = 280, deps = []) => {
  // 使用useRef存储定时器ID，确保在重渲染时保持引用
  const timerRef = useRef(null);

  // 使用useCallback包装防抖函数，确保函数引用稳定
  return useCallback((...args) => {
    // 如果已经存在定时器，则清除它
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // 设置新的定时器
    timerRef.current = setTimeout(() => {
      fn(...args);
      timerRef.current = null;
    }, delay);
  }, deps); // 依赖数组，当依赖项变化时重新创建防抖函数
};

/**
 * 带取消功能的防抖Hook
 * @param {Function} fn - 需要防抖的函数
 * @param {number} delay - 防抖延迟时间（毫秒）
 * @param {Array} deps - 依赖数组，用于useCallback
 * @returns {Object} - 包含防抖函数和取消方法的对象
 * 
 * @example
 * const { debouncedFn, cancel } = useDebounceWithCancel((value) => {
 *   console.log(value);
 * }, 280);
 * 
 * // 取消待执行的防抖函数
 * cancel();
 */
export const useDebounceWithCancel = (fn, delay = 280, deps = []) => {
  const timerRef = useRef(null);

  const debouncedFn = useCallback((...args) => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    timerRef.current = setTimeout(() => {
      fn(...args);
      timerRef.current = null;
    }, delay);
  }, deps);

  const cancel = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  return { debouncedFn, cancel };
};

/**
 * 带立即执行选项的防抖Hook
 * @param {Function} fn - 需要防抖的函数
 * @param {number} delay - 防抖延迟时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @param {Array} deps - 依赖数组，用于useCallback
 * @returns {Function} - 防抖后的函数
 * 
 * @example
 * const debouncedFn = useDebounceImmediate((value) => {
 *   console.log(value);
 * }, 280, true);
 */
export const useDebounceImmediate = (fn, delay = 280, immediate = false, deps = []) => {
  const timerRef = useRef(null);
  const lastCallTimeRef = useRef(0);

  return useCallback((...args) => {
    const now = Date.now();
    const shouldCallImmediate = immediate && now - lastCallTimeRef.current > delay;

    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    if (shouldCallImmediate) {
      fn(...args);
      lastCallTimeRef.current = now;
    } else {
      timerRef.current = setTimeout(() => {
        fn(...args);
        lastCallTimeRef.current = Date.now();
        timerRef.current = null;
      }, delay);
    }
  }, deps);
}; 