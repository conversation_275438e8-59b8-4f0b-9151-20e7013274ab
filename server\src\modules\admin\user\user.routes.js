const express = require('express');
const router = express.Router();
const userController = require('./user.controller');
const { auth, requireRole } = require('../../../middleware/auth.middleware'); // 更新为新的中间件路径

// 中间件：检查是否为管理员
router.use(auth, requireRole('admin'));

// 用户管理路由
router.get('/search', userController.searchUsers);
router.get('/', userController.getAllUsers);
router.get('/:id', userController.getUser);
router.post('/', userController.createUser);
router.put('/:id', userController.updateUser);
router.delete('/:id', userController.deleteUser);

// 设备管理路由
router.get('/:id/devices', userController.getUserDevices);
router.post('/:id/kick-device', userController.kickDevice);
router.put('/:id/max-sessions', userController.updateMaxSessions);

// 新增：更新用户备注
router.put('/:id/remark', userController.updateRemark);

module.exports = router; 