import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

/**
 * 版型面板组件 - 用于展示上传的版型图片和状态
 * 
 * 此组件在trending页面中被使用，显示版型参考图片
 * - trending(爆款开发)：显示"上传完成"状态文本
 */
const PatternPanel = ({
  panel,
  onExpandClick,
  onDelete,
  onReupload,
  onStatusChange,
  isActive,
  onPanelsChange,
  pageType = 'trending' // 默认为爆款开发页面
}) => {
  // 处理上传结果
  const handleUploadResult = (results) => {
    console.log('处理上传结果:', JSON.stringify(results, null, 2));
    
    try {
      // 如果是标准的success/results格式
      if (results.success && Array.isArray(results.results)) {
        const newPanels = results.results.map((result, index) => {
          console.log(`处理结果项 #${index+1}:`, result);
          
          // 检查是否有relativePath字段
          let processedImageUrl = null;
          if (result.processedFile) {
            if (result.relativePath) {
              processedImageUrl = `http://localhost:3002${result.relativePath}`;
              console.log('使用相对路径构建URL:', processedImageUrl);
            } else {
              // 直接使用上传图片，不获取处理后的图片
              console.log('使用默认路径构建URL:', processedImageUrl);
            }
          }
          
          return {
            componentId: result.componentId || `pattern-${Date.now()}-${index}`,
            title: result.title || '版型参考',
            status: result.status || 'completed',
            processedFile: processedImageUrl,
            serverFileName: result.serverFileName,
            type: result.type || 'pattern',
            fileInfo: {
              ...(result.fileInfo || {}),
              serverFileName: result.serverFileName
            }
          };
        });
        
        if (onPanelsChange) {
          console.log('更新面板数据:', newPanels);
          onPanelsChange(newPanels);
        }
      } else if (results.success && results.panel) {
        // 单个面板结果
        const result = results.panel;
        console.log('处理单个面板结果:', result);
        
        // 检查是否有relativePath字段
        let processedImageUrl = null;
        if (result.processedFile) {
          if (result.relativePath) {
            processedImageUrl = `http://localhost:3002${result.relativePath}`;
            console.log('使用相对路径构建URL:', processedImageUrl);
          } else {
            // 直接使用上传图片，不获取处理后的图片
            console.log('使用默认路径构建URL:', processedImageUrl);
          }
        }
        
        const newPanel = {
          componentId: result.componentId || panel.componentId,
          title: result.title || '版型参考',
          status: result.status || 'completed',
          processedFile: processedImageUrl || result.processedFile,
          serverFileName: result.serverFileName || panel.serverFileName,
          type: result.type || 'pattern',
          fileInfo: {
            ...(result.fileInfo || {}),
            serverFileName: result.serverFileName || panel.serverFileName
          }
        };
        
        if (onPanelsChange) {
          console.log('更新单个面板数据:', newPanel);
          onPanelsChange(newPanel);
        }
      }
    } catch (error) {
      console.error('处理上传结果时出错:', error);
      message.error('处理上传结果时出错');
    }
  };

  const handleExpandClick = (e) => {
    const buttonRect = e.currentTarget.getBoundingClientRect();
    onExpandClick?.(panel, {
      top: buttonRect.top,
      left: buttonRect.left + buttonRect.width
    });
  };

  const handleDelete = () => {
    if (panel && panel.componentId) {
      onDelete?.(panel.componentId);
      message.success('已删除版型面板');
    }
  };

  const handleReupload = () => {
    if (panel && panel.componentId) {
      onReupload?.(panel);
    }
  };

  const getImageInfo = () => {
    if (!panel || !panel.processedFile) return null;

    if (panel.fileInfo) {
      return {
        size: `${(panel.fileInfo.size / (1024 * 1024)).toFixed(2)} MB`,
        resolution: `${panel.fileInfo.width} x ${panel.fileInfo.height} px`,
        format: panel.fileInfo.format
      };
    }

    return {
      size: '--',
      resolution: '--',
      format: '--'
    };
  };

  // 根据页面类型获取适当的状态文本
  const getStatusText = () => {
    // 在爆款开发页面显示简单状态
    return '上传完成';
  };

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          <ThumbnailWithPreview
            imageUrl={panel.processedFile || panel.url}
            alt={`${panel.title} 上传结果`}
            status={panel.status}
            error={panel.error}
            onStatusChange={onStatusChange}
            featureName="版型参考预览"
          />
          <div className="component-text">
            <h3>
              版型参考
            </h3>
            <div className="component-content">
              <p>
                {panel.status === 'completed' && (
                  <>
                    {getStatusText()}
                  </>
                )}
                {panel.status === 'processing' && '处理中...'}
                {panel.status === 'error' && panel.error}
              </p>
            </div>
          </div>
        </div>
        <button 
          className={`expand-btn ${isActive ? 'active' : ''}`}
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

PatternPanel.propTypes = {
  panel: PropTypes.shape({
    componentId: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    status: PropTypes.oneOf(['processing', 'completed', 'error']).isRequired,
    error: PropTypes.string,
    processedFile: PropTypes.string,
    url: PropTypes.string,
    serverFileName: PropTypes.string,
    originalImage: PropTypes.string,
    type: PropTypes.string,
    source: PropTypes.string,
    fileInfo: PropTypes.shape({
      size: PropTypes.number,
      width: PropTypes.number,
      height: PropTypes.number,
      format: PropTypes.string,
      serverFileName: PropTypes.string
    }),
    showOriginal: PropTypes.bool,
  }).isRequired,
  onExpandClick: PropTypes.func.isRequired,
  onDelete: PropTypes.func,
  onReupload: PropTypes.func,
  onStatusChange: PropTypes.func,
  isActive: PropTypes.bool,
  onPanelsChange: PropTypes.func.isRequired,
  pageType: PropTypes.string,
};

export default PatternPanel; 