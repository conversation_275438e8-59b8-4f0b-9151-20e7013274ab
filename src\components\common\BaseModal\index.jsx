import React from 'react';
import { MdClose } from 'react-icons/md';
import './index.css';
import '../../../styles/close-buttons.css';

const BaseModal = ({
  title,
  children,
  onClose,
  footer,
  tabs,
  activeTab,
  onTabChange,
  className = '',
  size = 'default',
  dragRef = null,
  dragStyle = {},
  onDragMouseDown = null,
  ...props
}) => {
  const modalContentClass = `modal-content ${size === 'large' ? 'modal-large' : ''}`; 

  return (
    <div 
      className={`base-modal ${className}`}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose?.();
        }
      }}
    >
      <div
        className={modalContentClass}
        ref={dragRef}
        style={dragStyle}
        onMouseDown={onDragMouseDown}
      >
        <button 
          className="large-close-button"
          onClick={onClose}
        >
          <MdClose />
        </button>

        <div className="modal-header">
          {tabs ? (
            <div className="modal-tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.key}
                  className={`tab-button ${activeTab === tab.key ? 'active' : ''}`}
                  onClick={() => onTabChange?.(tab.key)}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          ) : (
            title && <h2>{title}</h2>
          )}
        </div>

        <div className="modal-body">
          {children}
        </div>

        {footer && (
          <div className="modal-footer">
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default BaseModal; 