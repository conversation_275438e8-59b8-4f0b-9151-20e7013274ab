// 场景数据
export const SCENES = [
  { id: 1, name: "阳光沙滩", tags: ['beach', 'summer'] },
  { id: 2, name: "海岸日落", tags: ['beach', 'sunset'] },
  { id: 3, name: "豪华泳池", tags: ['pool', 'luxury'] },
  { id: 4, name: "热带度假", tags: ['resort', 'tropical'] },
  { id: 5, name: "私人海湾", tags: ['beach', 'private'] },
  { id: 6, name: "水上乐园", tags: ['waterpark', 'fun'] },
  { id: 7, name: "沙滩酒吧", tags: ['beach', 'bar'] },
  { id: 8, name: "游艇派对", tags: ['yacht', 'party'] },
  { id: 9, name: "椰林海滩", tags: ['beach', 'palm'] },
  { id: 10, name: "无边泳池", tags: ['pool', 'infinity'] },
  { id: 11, name: "海景露台", tags: ['terrace', 'sea'] },
  { id: 12, name: "沙滩木屋", tags: ['beach', 'cabin'] },
  { id: 13, name: "热带花园", tags: ['garden', 'tropical'] },
  { id: 14, name: "海滨别墅", tags: ['villa', 'beach'] },
  { id: 15, name: "白色沙滩", tags: ['beach', 'white'] },
  { id: 16, name: "蓝湾海滩", tags: ['beach', 'blue'] },
  { id: 17, name: "度假酒店", tags: ['hotel', 'resort'] },
  { id: 18, name: "沙滩俱乐部", tags: ['beach', 'club'] },
  { id: 19, name: "海边咖啡", tags: ['cafe', 'beach'] },
  { id: 20, name: "日光浴场", tags: ['sunbath', 'beach'] }
];

// 获取场景图片路径
export const getSceneImagePath = (sceneId) => {
  return `/images/scene/scene_${sceneId.toString().padStart(3, '0')}.jpg`;
};

// 根据标签筛选场景
export const getFilteredScenes = (tags = []) => {
  if (!tags || tags.length === 0) return SCENES;
  return SCENES.filter(scene => 
    tags.some(tag => scene.tags.includes(tag))
  );
};

// 根据ID获取场景
export const getSceneById = (sceneId) => {
  return SCENES.find(scene => scene.id === sceneId);
};

// 获取所有可用的标签
export const getAllTags = () => {
  const tagSet = new Set();
  SCENES.forEach(scene => {
    scene.tags.forEach(tag => tagSet.add(tag));
  });
  return Array.from(tagSet);
}; 