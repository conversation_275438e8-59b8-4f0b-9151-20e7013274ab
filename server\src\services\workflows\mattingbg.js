const workflow = {
  prompt: {
      "14": {
        "inputs": {
          "image": "image.jpg",
          "upload": "image"
        },
        "class_type": "LoadImage",
        "_meta": {
          "title": "加载图像"
        }
      },
      "15": {
        "inputs": {
          "rem_mode": "RMBG-2.0",
          "image_output": "Save",
          "save_prefix": "MattingBg",
          "torchscript_jit": false,
          "add_background": "none",
          "images": [
            "14",
            0
          ]
        },
        "class_type": "easy imageRemBg",
        "_meta": {
          "title": "图像背景移除"
        }
      }
  }
};

module.exports = workflow; 