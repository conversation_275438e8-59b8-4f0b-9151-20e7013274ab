/**
 * RunningHub管理API
 * 提供RunningHub平台的管理功能API接口
 */

import api from './index';

/**
 * RunningHub管理API类
 */
class RunningHubAdminAPI {
  constructor() {
    this.baseURL = '/api/runninghub';
  }

  /**
   * 获取任务列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 任务列表
   */
  async getTasks(params = {}) {
    try {
      const response = await api.get(`${this.baseURL}/admin/tasks`, { params });
      return {
        success: true,
        data: response.data || [],
        total: response.total || 0
      };
    } catch (error) {
      console.error('获取任务列表失败:', error);
      throw new Error(error.message || '获取任务列表失败');
    }
  }

  /**
   * 获取任务详情
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object>} 任务详情
   */
  async getTaskDetail(taskId) {
    try {
      const response = await api.get(`${this.baseURL}/admin/tasks/${taskId}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('获取任务详情失败:', error);
      throw new Error(error.message || '获取任务详情失败');
    }
  }

  /**
   * 删除任务
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteTask(taskId) {
    try {
      const response = await api.delete(`${this.baseURL}/admin/tasks/${taskId}`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('删除任务失败:', error);
      throw new Error(error.message || '删除任务失败');
    }
  }

  /**
   * 批量删除任务
   * @param {Array} taskIds - 任务ID数组
   * @returns {Promise<Object>} 删除结果
   */
  async batchDeleteTasks(taskIds) {
    try {
      const response = await api.post(`${this.baseURL}/admin/tasks/batch-delete`, {
        taskIds
      });
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('批量删除任务失败:', error);
      throw new Error(error.message || '批量删除任务失败');
    }
  }

  /**
   * 获取统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 统计数据
   */
  async getStats(params = {}) {
    try {
      const response = await api.get(`${this.baseURL}/admin/stats`, { params });
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('获取统计数据失败:', error);
      throw new Error(error.message || '获取统计数据失败');
    }
  }

  /**
   * 获取用户配置列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 配置列表
   */
  async getUserConfigs(params = {}) {
    try {
      const response = await api.get(`${this.baseURL}/admin/configs`, { params });
      return {
        success: true,
        data: response.data || [],
        total: response.total || 0
      };
    } catch (error) {
      console.error('获取用户配置失败:', error);
      throw new Error(error.message || '获取用户配置失败');
    }
  }

  /**
   * 测试用户配置
   * @param {string} configId - 配置ID
   * @returns {Promise<Object>} 测试结果
   */
  async testUserConfig(configId) {
    try {
      const response = await api.post(`${this.baseURL}/admin/configs/${configId}/test`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('测试配置失败:', error);
      throw new Error(error.message || '测试配置失败');
    }
  }

  /**
   * 禁用/启用用户配置
   * @param {string} configId - 配置ID
   * @param {boolean} enabled - 是否启用
   * @returns {Promise<Object>} 操作结果
   */
  async toggleUserConfig(configId, enabled) {
    try {
      const response = await api.put(`${this.baseURL}/admin/configs/${configId}/toggle`, {
        enabled
      });
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('切换配置状态失败:', error);
      throw new Error(error.message || '切换配置状态失败');
    }
  }



  /**
   * 获取系统配置
   * @returns {Promise<Object>} 系统配置
   */
  async getSystemConfig() {
    try {
      const response = await api.get(`${this.baseURL}/admin/system-config`);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('获取系统配置失败:', error);
      throw new Error(error.message || '获取系统配置失败');
    }
  }

  /**
   * 更新系统配置
   * @param {Object} config - 系统配置
   * @returns {Promise<Object>} 更新结果
   */
  async updateSystemConfig(config) {
    try {
      const response = await api.put(`${this.baseURL}/admin/system-config`, config);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('更新系统配置失败:', error);
      throw new Error(error.message || '更新系统配置失败');
    }
  }

  /**
   * 获取API使用统计
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 使用统计
   */
  async getApiUsageStats(params = {}) {
    try {
      const response = await api.get(`${this.baseURL}/admin/usage-stats`, { params });
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('获取API使用统计失败:', error);
      throw new Error(error.message || '获取API使用统计失败');
    }
  }

  /**
   * 导出任务数据
   * @param {Object} params - 导出参数
   * @returns {Promise<Object>} 导出结果
   */
  async exportTasks(params = {}) {
    try {
      const response = await api.post(`${this.baseURL}/admin/export-tasks`, params, {
        responseType: 'blob'
      });
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('导出任务数据失败:', error);
      throw new Error(error.message || '导出任务数据失败');
    }
  }

  /**
   * 清理过期任务
   * @param {Object} params - 清理参数
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupExpiredTasks(params = {}) {
    try {
      const response = await api.post(`${this.baseURL}/admin/cleanup-tasks`, params);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('清理过期任务失败:', error);
      throw new Error(error.message || '清理过期任务失败');
    }
  }

  /**
   * 获取工作流使用统计
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 工作流统计
   */
  async getWorkflowStats(params = {}) {
    try {
      const response = await api.get(`${this.baseURL}/admin/workflow-stats`, { params });
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('获取工作流统计失败:', error);
      throw new Error(error.message || '获取工作流统计失败');
    }
  }

  /**
   * 获取错误日志
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 错误日志
   */
  async getErrorLogs(params = {}) {
    try {
      const response = await api.get(`${this.baseURL}/admin/error-logs`, { params });
      return {
        success: true,
        data: response.data || [],
        total: response.total || 0
      };
    } catch (error) {
      console.error('获取错误日志失败:', error);
      throw new Error(error.message || '获取错误日志失败');
    }
  }

  /**
   * 清理错误日志
   * @param {Object} params - 清理参数
   * @returns {Promise<Object>} 清理结果
   */
  async clearErrorLogs(params = {}) {
    try {
      const response = await api.post(`${this.baseURL}/admin/clear-error-logs`, params);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('清理错误日志失败:', error);
      throw new Error(error.message || '清理错误日志失败');
    }
  }
}

// 创建API实例
const runningHubAdminAPI = new RunningHubAdminAPI();

// 导出API方法
export const {
  getTasks,
  getTaskDetail,
  deleteTask,
  batchDeleteTasks,
  getStats,
  getUserConfigs,
  testUserConfig,
  toggleUserConfig,
  getSystemConfig,
  updateSystemConfig,
  getApiUsageStats,
  exportTasks,
  cleanupExpiredTasks,
  getWorkflowStats,
  getErrorLogs,
  clearErrorLogs
} = runningHubAdminAPI;

export default runningHubAdminAPI;
