import React from 'react';
import { MdClose } from 'react-icons/md';
import { IconType } from 'react-icons';
import './styles.css';

interface SubscribeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubscribeClick: () => void;
  onTrialClick: () => void;
  renderIcon?: (Icon: IconType) => React.ReactNode;
}

const SubscribeModal: React.FC<SubscribeModalProps> = ({
  isOpen,
  onClose,
  onSubscribeClick,
  onTrialClick,
  renderIcon = (Icon: IconType) => {
    const IconComponent = Icon as React.ComponentType<React.SVGProps<SVGSVGElement>>;
    return <IconComponent />;
  }
}) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content subscribe-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <button
            className="medium-close-button"
            onClick={onClose}
          >
            {renderIcon(MdClose)}
          </button>
        </div>
        <div className="modal-body">
          <div className="subscribe-options">
            <button className="subscribe-option-btn trial" onClick={onTrialClick}>
              <span>申请试用</span>
              <small>免费体验完整功能</small>
            </button>
            <button className="subscribe-option-btn premium" onClick={onSubscribeClick}>
              <span>订阅会员</span>
              <small>了解详情 成为会员</small>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscribeModal; 