# 订阅权限检查功能实现说明

## 功能概述

本功能实现了在执行任务前检查用户订阅权限和并发限制，确保用户只能使用其订阅套餐包含的功能，并且不超过并发任务数限制。

## 后端实现

### 1. 修改的文件

#### `server/src/services/credits/creditService.js`
- 增强了 `checkUserBalance` 方法
- 新增订阅权限检查
- 新增并发任务数检查
- 新增 `_extractFeatureFromWorkflow` 方法

#### `server/src/routes/tasks.routes.js`
- 修改了 `/check-balance` 接口
- 返回更详细的状态信息

### 2. 检查逻辑

```javascript
// 检查流程
1. 检查用户是否有有效订阅
2. 检查订阅是否包含该功能权限
3. 检查当前并发任务数是否超限
4. 检查算力余额是否充足
5. 返回详细的状态信息
```

### 3. 返回格式

```javascript
// 成功情况
{
  canExecute: true,
  code: 200,
  message: '可以执行任务',
  type: 'success',
  currentBalance: 1000,
  requiredCost: 150,
  remainingBalance: 850,
  currentRunning: 1,
  maxAllowed: 2,
  subscription: {
    plan: 'full',
    endDate: '2024-02-01T00:00:00.000Z'
  }
}

// 失败情况
{
  canExecute: false,
  code: 403, // 或其他错误码
  message: '错误描述',
  type: 'error_type',
  // 其他相关信息...
}
```

### 4. 错误类型

| 类型 | 描述 | 处理方式 |
|------|------|----------|
| `subscription_required` | 无有效订阅 | 引导用户购买订阅 |
| `feature_not_allowed` | 功能权限不足 | 引导用户升级套餐 |
| `concurrent_limit_exceeded` | 并发数超限 | 提示用户等待或升级 |
| `insufficient_balance` | 算力余额不足 | 引导用户充值 |
| `balance_not_found` | 余额信息异常 | 系统错误提示 |
| `invalid_billing_config` | 计费配置异常 | 系统错误提示 |
| `system_error` | 系统错误 | 通用错误提示 |

## 前端实现

### 1. 新增文件

#### `src/utils/subscriptionChecker.js`
- 提供检查用户权限的工具函数
- 根据不同类型显示相应的提示模态框
- 提供便捷的检查和执行函数

### 2. 主要函数

#### `checkUserCanExecute(featureName, subType)`
```javascript
// 检查用户是否可以执行特定功能
const result = await checkUserCanExecute('B01-fashion', 'tab1');
```

#### `showExecutionResult(result, onUpgrade)`
```javascript
// 显示执行结果和相应提示
const canExecute = showExecutionResult(result, () => {
  // 升级订阅的回调
  window.location.href = '/subscribe';
});
```

#### `checkAndExecute(featureName, subType, executeTask, onUpgrade)`
```javascript
// 一步完成检查和执行
const result = await checkAndExecute(
  'B01-fashion',
  'tab1',
  async () => await executeTask(),
  () => window.location.href = '/subscribe'
);
```

### 3. 使用示例

#### 基本使用
```javascript
import { checkUserCanExecute, showExecutionResult } from './utils/subscriptionChecker';

const handleExecute = async () => {
  const result = await checkUserCanExecute('B01-fashion', 'tab1');
  
  if (showExecutionResult(result, () => {
    window.location.href = '/subscribe';
  })) {
    // 执行任务
    await executeTask();
  }
};
```

#### React Hook 使用
```javascript
import { useSubscriptionCheck } from './utils/subscriptionChecker';

const MyComponent = () => {
  const { result, loading, check } = useSubscriptionCheck('B01-fashion', 'tab1');
  
  useEffect(() => {
    check();
  }, []);
  
  if (loading) return <div>检查中...</div>;
  
  return (
    <button disabled={!result.canExecute} onClick={handleExecute}>
      执行任务
    </button>
  );
};
```

## 配置说明

### 1. 订阅计划配置

在管理后台配置每个订阅计划的 `maxConcurrentTasks` 字段：

```javascript
// 免费版
{
  code: 'free',
  maxConcurrentTasks: 1
}

// 设计版
{
  code: 'design',
  maxConcurrentTasks: 2
}

// 完整版
{
  code: 'full',
  maxConcurrentTasks: 5
}

// 企业版
{
  code: 'enterprise',
  maxConcurrentTasks: 10
}
```

### 2. 功能权限配置

在订阅模型中配置各功能的权限：

```javascript
features: {
  design: {
    enabled: true,
    trendDesign: true,
    styleOptimization: true,
    inspirationExplore: true
  },
  model: {
    enabled: true,
    fashionShoot: true,
    modelChange: true,
    // ...
  },
  tools: {
    enabled: true,
    upscale: true,
    matting: true,
    extend: true
  }
}
```

## 部署注意事项

### 1. 数据库迁移
如果现有数据没有 `maxConcurrentTasks` 字段，需要设置默认值：

```javascript
// 为现有订阅设置默认并发数
await Subscription.updateMany(
  { 'usageQuota.maxConcurrentTasks': { $exists: false } },
  { $set: { 'usageQuota.maxConcurrentTasks': 1 } }
);
```

### 2. 缓存考虑
考虑对用户订阅信息进行缓存，避免频繁查询数据库：

```javascript
// 使用 Redis 缓存用户订阅信息
const cacheKey = `subscription:${userId}`;
let subscription = await redis.get(cacheKey);
if (!subscription) {
  subscription = await Subscription.findOne({...});
  await redis.setex(cacheKey, 300, JSON.stringify(subscription)); // 缓存5分钟
}
```

### 3. 监控告警
建议添加监控，及时发现异常：

```javascript
// 监控并发任务数异常
if (runningCount > maxConcurrent * 2) {
  // 发送告警
  sendAlert(`用户 ${userId} 并发任务数异常: ${runningCount}/${maxConcurrent}`);
}
```

## 测试建议

### 1. 功能测试
- 测试不同订阅计划的权限检查
- 测试并发限制功能
- 测试各种错误情况的提示

### 2. 性能测试
- 测试高并发下的检查性能
- 测试数据库查询优化效果

### 3. 用户体验测试
- 测试提示信息的友好性
- 测试升级流程的顺畅性

## 后续优化

### 1. 功能增强
- 支持更细粒度的权限控制
- 支持动态调整并发限制
- 支持临时权限授予

### 2. 性能优化
- 实现订阅信息缓存
- 优化数据库查询
- 添加批量检查功能

### 3. 用户体验
- 优化提示界面
- 增加引导流程
- 提供更详细的错误信息 