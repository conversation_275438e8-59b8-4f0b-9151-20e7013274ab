# WebSocket 优化文档

## 概述

本次优化实现了全局WebSocket连接管理，确保整个应用只使用一个WebSocket连接，支持多任务并发处理，并优化了连接生命周期管理。

## 主要改进

### 1. 全局WebSocket管理器

- **单例模式**: 整个应用只维护一个WebSocket连接实例
- **多任务支持**: 单个连接支持多个任务并发订阅和消息处理
- **连接复用**: 避免重复建立连接，提高性能和稳定性

### 2. 连接生命周期管理

- **自动重连**: 连接断开时自动重连机制
- **超时清理**: 定期清理无活动的连接
- **优雅关闭**: 只在浏览器关闭或用户退出登录时关闭连接

### 3. 后端优化

- **连接池管理**: 优化ComfyUI连接池，支持连接复用
- **消息路由**: 改进消息路由机制，支持多任务并发
- **状态监控**: 添加连接状态监控和统计功能

## 文件结构

```
src/utils/
├── comfyUITaskTracker.js          # 全局WebSocket管理器
├── websocketLifecycleManager.js   # 生命周期管理器
└── websocketUtils.js              # 工具函数

scripts/
└── main.py                        # 后端WebSocket服务
```

## 核心组件

### 1. GlobalWebSocketManager

全局WebSocket管理器，负责：
- 维护单个WebSocket连接
- 管理任务订阅和取消订阅
- 处理消息路由和事件分发

```javascript
import { globalWebSocketManager } from './utils/comfyUITaskTracker';

// 获取连接状态
const status = globalWebSocketManager.getConnectionStatus();

// 订阅任务
await globalWebSocketManager.subscribeTask(taskId, promptId, instanceId, instanceWsUrl, callbacks);

// 取消订阅
globalWebSocketManager.unsubscribeTask(taskId);
```

### 2. WebSocketLifecycleManager

生命周期管理器，负责：
- 监听浏览器关闭事件
- 处理页面可见性变化
- 管理连接的生命周期

```javascript
import websocketLifecycleManager from './utils/websocketLifecycleManager';

// 用户退出登录时调用
websocketLifecycleManager.handleUserLogout();

// 获取管理器状态
const status = websocketLifecycleManager.getStatus();
```

### 3. WebSocket工具函数

提供便捷的工具函数：

```javascript
import websocketUtils from './utils/websocketUtils';

// 用户退出登录
websocketUtils.handleUserLogout();

// 获取连接状态
const status = websocketUtils.getConnectionStatus();

// 手动重连
await websocketUtils.reconnect();

// 取消所有任务订阅
websocketUtils.unsubscribeAllTasks();
```

## 使用方式

### 前端组件使用

在React组件中，直接使用全局管理器：

```javascript
import { globalWebSocketManager } from '../../utils/comfyUITaskTracker';

// 在组件中订阅任务
useEffect(() => {
  const processingTasks = tasks.filter(task => task.status === 'processing');
  
  processingTasks.forEach(async (task) => {
    if (!globalWebSocketManager.subscribedTasks.has(task.taskId)) {
      await globalWebSocketManager.subscribeTask(
        task.taskId,
        task.promptId,
        task.instanceId,
        task.url,
        {
          onProgress: (progress, max) => {
            // 处理进度更新
          },
          onCompleted: (outputs) => {
            // 处理完成事件
          }
        }
      );
    }
  });
}, [tasks]);
```

### 用户退出登录

在AuthContext中自动处理：

```javascript
const logout = (): void => {
  // 关闭WebSocket连接
  websocketUtils.handleUserLogout();
  
  // 清除本地存储
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  localStorage.removeItem('tokenExpires');
  
  // 清除CSRF令牌
  clearCSRFToken();
  
  // 重定向到首页
  window.location.href = '/';
};
```

## 后端API

### 连接统计

获取WebSocket连接统计信息：

```
GET /api/websocket/stats
```

返回：
```json
{
  "frontend_connections": 5,
  "task_listeners": 10,
  "comfy_connections": 3,
  "active_tasks": 8
}
```

## 配置参数

### 前端配置

```javascript
// 连接超时时间（秒）
connection_timeout: 300

// 最大重试次数
max_retries: 3

// 重连延迟（秒）
retry_delay: 1000
```

### 后端配置

```python
# 连接超时时间（秒）
connection_timeout = 300

# 清理间隔（秒）
cleanup_interval = 60

# 任务完成清理延迟（秒）
task_cleanup_delay = 300
```

## 监控和调试

### 连接状态监控

```javascript
// 获取详细连接状态
const status = globalWebSocketManager.getConnectionStatus();
console.log('连接状态:', status);

// 获取生命周期状态
const lifecycleStatus = websocketLifecycleManager.getStatus();
console.log('生命周期状态:', lifecycleStatus);
```

### 日志输出

系统会输出详细的连接日志：

```
开始建立全局WebSocket连接
全局WebSocket连接成功
订阅任务 (connection_id=xxx, task_id=yyy)
任务 xxx 进度更新: 当前值: 50, 最大值: 100
任务 xxx 已完成
检测到浏览器即将关闭，准备关闭WebSocket连接
```

## 注意事项

1. **连接复用**: 确保不要重复创建WebSocket连接
2. **事件清理**: 组件卸载时不需要手动关闭连接
3. **错误处理**: 系统会自动处理连接错误和重连
4. **性能优化**: 避免频繁的订阅/取消订阅操作

## 兼容性

- 保持与现有代码的完全兼容
- 支持旧版本的WebSocket消息格式
- 自动处理连接升级和降级

## 故障排除

### 常见问题

1. **连接失败**: 检查网络连接和后端服务状态
2. **消息丢失**: 检查任务订阅状态和回调函数
3. **内存泄漏**: 确保正确使用生命周期管理器

### 调试步骤

1. 检查浏览器控制台日志
2. 查看后端连接统计
3. 验证任务订阅状态
4. 检查网络连接状态 