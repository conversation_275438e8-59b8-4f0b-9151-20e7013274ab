import React, { useState, useEffect } from 'react';
import { Carousel, Card, Image } from 'antd';
import api from '../../api';
import './AnnouncementCarousel.css';

const AnnouncementCarousel = () => {
  const [announcements, setAnnouncements] = useState([]);

  useEffect(() => {
    fetchActiveAnnouncements();
  }, []);

  const fetchActiveAnnouncements = async () => {
    try {
      const response = await api.get('/announcements/active');
      setAnnouncements(response.data || []);
    } catch (error) {
      console.error('获取公告失败:', error);
    }
  };

  if (!announcements.length) {
    return null;
  }

  return (
    <div className="announcement-carousel">
      <Carousel autoplay>
        {announcements.map(announcement => (
          <div key={announcement._id}>
            <Card className="announcement-card">
              <h3 className="announcement-title">{announcement.title}</h3>
              <div className="announcement-content">{announcement.content}</div>
              {announcement.images?.length > 0 && (
                <div className="announcement-images">
                  <Image.PreviewGroup>
                    {announcement.images.map((image, index) => (
                      <Image
                        key={index}
                        src={image.url}
                        alt={image.title || `图片${index + 1}`}
                        className="announcement-image"
                      />
                    ))}
                  </Image.PreviewGroup>
                </div>
              )}
            </Card>
          </div>
        ))}
      </Carousel>
    </div>
  );
};

export default AnnouncementCarousel; 