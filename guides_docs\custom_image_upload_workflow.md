# 自定义图片上传功能实现流程指南

本文档介绍了在React应用中实现自定义图片上传功能的标准流程，适用于各种场景，如场景参考图、模特图片、服装图片等的上传。

## 实现概述

实现自定义图片上传功能时，我们采用以下两阶段处理流程：

1. **临时预览阶段** - 用户选择文件后立即创建临时URL用于预览，提高用户体验
2. **服务器上传阶段** - 当用户点击"生成"按钮等确认操作时，才将图片上传到服务器

这种实现方式有以下优势：

- 用户可以立即看到所选图片，无需等待上传完成
- 节省服务器资源，只上传用户确认使用的图片
- 减少不必要的网络请求，提高应用性能

## 详细实现步骤

### 1. 在选择器组件中添加上传功能

首先，在选择器组件中添加上传功能区域。例如，在场景选择器中增加上传区域：

```jsx
// 在SceneGrid组件中添加上传区域
const SceneGrid = memo(({ scenes, selectedSceneId, onSceneSelect, onPreview, onCustomUpload, uploadedScene }) => (
  <div className="scenes-grid">
    {/* 添加自定义上传区域 */}
    {onCustomUpload && (
      <div 
        className={`card-item upload-entry ${uploadedScene ? 'uploaded' : ''}`}
        onClick={() => uploadedScene && onSceneSelect(uploadedScene)}
      >
        <div className="upload-area-content">
          {uploadedScene ? (
            // 显示已上传的图片
            <>
              <div className="card-preview">
                <img 
                  src={uploadedScene.image}
                  alt="已上传图片"
                />
              </div>
              <div className="card-caption">
                <div className="card-info">
                  <span className="card-name">已上传 自定义图片</span>
                </div>
                <button 
                  className="preview-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    onPreview('custom-upload');
                  }}
                  title="放大预览"
                >
                  <MdZoomOutMap />
                </button>
              </div>
            </>
          ) : (
            // 显示上传按钮
            <>
              <input 
                type="file" 
                id="image-upload" 
                className="file-input" 
                accept={UPLOAD_CONFIG.getAcceptTypes()}
                onChange={onCustomUpload}
                style={{ display: 'none' }}
              />
              <label htmlFor="image-upload" className="upload-label">
                <div className="upload-icon">
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11 14V6H13V14H11Z" fill="currentColor"/>
                    <path d="M7 10L12 5L17 10H7Z" fill="currentColor"/>
                    <path d="M6 18V16H18V18H6Z" fill="currentColor"/>
                  </svg>
                </div>
                <div className="upload-text">
                  <span>上传自定义图片</span>
                </div>
              </label>
            </>
          )}
        </div>
      </div>
    )}
    
    {/* 原有的预设选项 */}
    {scenes.map((scene) => (
      // 预设选项的代码...
    ))}
  </div>
));
```

### 2. 添加处理上传的函数

在组件中添加处理上传的函数：

```jsx
// 处理自定义图片上传
const handleCustomImageUpload = (e) => {
  if (e.target.files && e.target.files.length > 0) {
    const file = e.target.files[0];
    
    // 验证文件类型和大小
    if (!UPLOAD_CONFIG.isValidFileType(file.name)) {
      message.error('不支持的文件类型');
      return;
    }
    
    if (!UPLOAD_CONFIG.isValidFileSize(file.size)) {
      message.warning(`文件需${UPLOAD_CONFIG.maxSize}MB以内`);
      return;
    }
    
    // 创建临时文件URL用于预览
    const fileUrl = URL.createObjectURL(file);
    
    // 创建自定义对象
    const customImage = {
      id: 'custom-' + Date.now(),
      name: '自定义图片',
      type: 'background',  // 表示业务用途是背景图片
      source: 'upload',    // 表示来源是用户上传
      description: '用户上传的自定义图片',
      image: fileUrl,
      file: file  // 保存文件对象，用于后续上传
    };
    
    // 设置上传的图片为当前状态
    setUploadedImage(customImage);
    
    // 选择这个自定义图片
    handleImageSelect(customImage);
    
    // 显示上传成功消息
    message.success('图片上传成功');
  }
};
```

### 3. 添加状态恢复功能

为了在用户返回页面时能恢复上传状态，添加以下代码：

```jsx
// 检查savedSettings是否包含自定义上传的图片
useEffect(() => {
  if (savedSettings && 
      (savedSettings.source === 'upload' && savedSettings.image && 
       !DEFAULT_IMAGES.includes(savedSettings.image))) {
    // 如果是已上传的自定义图片，恢复状态
    setUploadedImage({
      id: 'custom-' + Date.now(),
      name: '自定义图片',
      type: savedSettings.type || 'background', // 保留业务类型
      source: 'upload', // 标记来源为用户上传
      description: savedSettings.description || '用户上传的自定义图片',
      image: savedSettings.image,
      file: savedSettings.file
    });
  }
}, [savedSettings]);
```

### 4. 在生成函数中处理上传到服务器

当用户点击"生成"按钮时，检查是否有自定义上传的图片，并上传到服务器：

```jsx
const handleGenerate = async () => {
  // 其他验证代码...
  
  // 处理可能的自定义上传图片
  let imageToUse = selectedImage;
  
  // 检查是否有自定义上传的图片（通过查看是否有原始文件对象）
  if (selectedImage.file && selectedImage.source === 'upload') {
    // 显示上传中提示
    message.loading('正在上传图片...', 0);
    
    try {
      // 将文件上传到服务器
      const uploadResult = await uploadImage(
        selectedImage.file, 
        'upload', 
        pageType, 
        imageType
      );
      
      // 上传成功后，使用服务器返回的URL更新图片对象
      if (uploadResult && uploadResult.success) {
        const resultData = uploadResult.results[0];
        
        // 创建新的图片对象，包含服务器URL
        imageToUse = {
          ...selectedImage,
          image: resultData.url, // 使用服务器返回的URL
          originalFile: resultData.originalFile, // 保存服务器端文件名
          source: 'history' // 标记为已上传到服务器的图片来源
        };
        
        message.success('图片上传成功');
      } else {
        message.error('图片上传失败');
        setIsProcessing(false);
        return;
      }
    } catch (error) {
      console.error('上传图片时出错:', error);
      message.error('图片上传失败: ' + (error.message || '未知错误'));
      setIsProcessing(false);
      return;
    } finally {
      // 关闭上传中提示
      message.destroy();
    }
  }
  
  // 继续生成流程，使用更新后的imageToUse...
};
```

### 5. 添加预览功能

为上传的图片添加预览功能：

```jsx
const handlePreview = useMemo(() => (imageId) => {
  // 处理自定义上传的图片
  if (imageId === 'custom-upload' && uploadedImage) {
    setPreviewImage(uploadedImage.image);
    return;
  }
  
  // 处理预设图片的预览...
}, [uploadedImage]);
```

### 6. 清空功能

添加清空功能，清除上传状态：

```jsx
const handleClear = () => {
  // 清除已上传的图片
  setUploadedImage(null);
  // 清除选中状态
  onSelect?.(null);
  // 清除保存的设置
  onSettingsChange?.(null);
};
```

## CSS样式实现

为上传区域添加合适的样式：

```css
.upload-entry {
  border: 1px dashed var(--border-color) !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  background: var(--bg-primary);
  position: relative;
  aspect-ratio: 2/3;  /* 根据需要调整宽高比 */
}

.upload-entry:hover {
  border-color: var(--brand-primary) !important;
  background: var(--brand-primary-light);
}

/* 上传后的样式 */
.upload-entry.uploaded {
  border: 1px solid var(--border-color) !important;
  background: var(--bg-primary);
}

.upload-entry.uploaded:hover {
  border-color: var(--brand-primary) !important;
  background: var(--bg-primary);
  box-shadow: var(--shadow-brand);
}

/* 其他样式... */
```

## 应用场景示例

此上传流程可用于多种场景，例如：

1. **场景参考图** - 在场景选择器中添加上传功能，允许用户使用自己的场景图片
2. **模特图片** - 在模特选择器中添加上传功能，允许上传自定义模特图片
3. **服装图片** - 在服装选择器中添加上传功能，允许上传自定义服装图片
4. **背景图片** - 在背景选择器中添加上传功能，允许用户使用自己的背景图片
5. **风格参考图** - 在风格选择器中添加上传功能，用于风格迁移或参考

## 最佳实践

1. **统一命名** - 使用统一的命名约定，如`uploadedImage`、`handleCustomImageUpload`等
2. **文件验证** - 在上传前验证文件类型和大小，给出明确错误提示
3. **二阶段上传** - 先创建临时URL用于预览，在确认操作时再上传到服务器
4. **状态保存** - 保存上传状态以便在返回页面时恢复
5. **错误处理** - 添加完善的错误处理，给用户友好的错误提示
6. **加载状态** - 上传期间显示加载状态，提升用户体验
7. **性能优化** - 使用`useMemo`和`memo`提升性能，避免不必要的重新渲染

遵循这些步骤和最佳实践，可以在应用的任何部分实现一致、高效的图片上传功能。 