import React, { useRef, useEffect, useState, useCallback, Suspense, useMemo } from 'react';
import './index.css';
import { MdOutlineZoomOutMap, MdOutlineAutoAwesome, MdClose, MdOutlineDescription } from 'react-icons/md';
import Masonry from 'masonry-layout';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import { Modal, Button, Spin, message, Tooltip } from 'antd';
import 'antd/dist/reset.css';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import UploadGuideModal from '../../../components/UploadGuideModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import GenerationArea from '../../../components/GenerationArea';
import RequireLogin from '../../../components/RequireLogin';
import TipsPanel from '../../../components/TipsPanel';
import VirtualModelManager from '../../../components/VirtualModelManager';
import { getCurrentUserId } from '../../../api';
import ModelRegistrationModal from '../../../components/ModelRegistrationModal';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import RandomSeedSelector from '../../../components/RandomSeedSelector';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import TextDescriptionPanel from '../../../components/TextDescriptionPanel';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask,checkUserBalance, updateTask } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import { executeFlow } from '../../../api/flow';
import { useTaskContext } from '../../../contexts/TaskContext';

const VirtualPage = ({ isLoggedIn, userId }) => {
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [processedImages, setProcessedImages] = useState([]);
  const { updateTask } = useTaskContext();
  const [isProcessing, setIsProcessing] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);
  const [showAdvancedText, setShowAdvancedText] = useState(false);

  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });
  
  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);
  
  // 添加随机种子相关状态
  const [useRandomSeed, setUseRandomSeed] = useState(true);
  const [seed, setSeed] = useState(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));
  
  // 添加提示词相关状态
  const [prompt, setPrompt] = useState('生成高质量的虚拟模特照片');
  
  // 添加生成相关状态
  const [currentTaskId, setCurrentTaskId] = useState(null);
  
  // 添加文本描述状态
  const [textDescription, setTextDescription] = useState('');
  
  // 添加页面刷新/关闭提示状态
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);
  
  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);
  
  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);


  // 处理生成按钮点击
  const handleGenerate = async () => {
    console.log('开始生成...');
        setSeed(useRandomSeed? (Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)) : seed);


    try {
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId() ;
      const balance = await checkUserBalance('虚拟模特', 'virtual', 1);
      if(balance.code !== 200){
        message.error(balance.message);
setIsProcessing(false);
        return;
      }
      // 验证描述词是否已填写
      if (!textDescription.trim()) {
        message.error('请在描述词中填写内容');
        return;
      }

      // 创建一个新的任务ID
      const taskId = generateId(ID_TYPES.TASK);
      
      // 使用固定尺寸值 1024x1536
      const imageSize = {
        width: 1024,
        height: 1536
      };
      
      // 【注意】向comfyUI工作流传递预设图片（模特骨骼图/姿势图）
      // 此预设图片不需要用户上传，前端也不需要显示
      // 每次运行工作流时都将使用这张固定的图片
      const presetImagePath = 'https://file.aibikini.cn/config/workflow/virtual-01.png'; // 预设图片路径
      
      // 创建任务数据对象
      const taskData = {
        taskId,
        userId: currentUserId,
        createdAt: new Date().toISOString(),
        taskType: 'virtual', // 指定任务类型为虚拟模特
        status: 'processing',
        pageType: 'virtual',
        prompt: prompt,
        seed: seed,
        newTask: true,
        // 添加文本描述到任务数据
        textDescription: textDescription,
        // 添加预设图片路径到任务数据，供后端获取并传递给comfyUI工作流
        presetImagePath: presetImagePath,
        // 添加组件配置到任务数据
        components: [
          {
            componentType: 'randomSeedSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '随机种子',
            status: 'completed',
            useRandom: false,
            value: seed
          },
          // 文字描述组件
          {
            componentType: 'textDescriptionPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            name: '文字描述',
            status: 'completed',
            prompt: textDescription
          }
        ],
        // 初始状态下的空图像数组 - 使用generatedImages
        generatedImages: Array(6).fill(null).map((_, index) => ({
          imageIndex: index,
          status: 'processing'
        })),
        // 添加任务类型
        processInfo:{
          results:[]
        }
      };
      
      setIsProcessing(true);
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }

      // 先添加到本地状态，使UI立即响应
      try {
        // 创建工作流任务
        await createFlowTask(taskData);
        // https://file.aibikini.cn/config/workflow/virtual-01.png
        // 执行工作流
        const result = await executeFlow(WORKFLOW_NAME.VIRTUAL_GENERATION,{
          "7": {
            "seed": seed
          },
          "34": {
            "text": textDescription
          },
          "42":{
            "url":presetImagePath
          },
          "subInfo":{
            "type": "virtual",
            "title":"虚拟模特",
            "count":1
          }
        },taskData.taskId);
        setIsProcessing(false);
        setHasUnsavedChanges(false);

        if( generationAreaRef.current){
          taskData.promptId = result.promptId;
          taskData.instanceId = result.instanceId;
          taskData.url = result.url;
                  taskData.promptId = result.promptId;
        taskData.instanceId = result.instanceId;
        taskData.url = result.url;
        taskData.newTask = true;
        taskData.netWssUrl=result.netWssUrl;
        taskData.clientId=result.clientId;
          generationAreaRef.current.setGenerationTasks(taskData);
      }
        // 更新任务列表
      } catch (error) {
        setIsProcessing(false);
        // 更新任务状态为失败
        taskData.status = 'failed';
        taskData.errorMessage = error.message;
        if( generationAreaRef.current){
          generationAreaRef.current.setGenerationTasks(taskData);
        }
        
        // 调用updateTask以触发失败提示音
        updateTask(taskData);
        
        // 更新任务列表
      }      
      // 切换到结果标签页
      setActiveTab('result');
      
    } catch (error) {
      console.error('生成过程中出错:', error);
      message.error('生成失败: ' + (error.message || '未知错误'));
      setIsProcessing(false);
    } 
  };


  // 处理编辑任务
  const handleEditTask = (task) => {
    if (!task) {
      message.warning('无法编辑：未找到任务信息');
      return;
    }

    // 设置任务ID
    setCurrentTaskId(task.taskId);
    
    // 只处理数组结构，不再兼容对象结构
    const components = Array.isArray(task.components) ? task.components : [];
    console.log('处理的组件数据:', components);
    
    const randomSeedSelector = task.components.find(c => c.componentType === 'randomSeedSelector' );
    if (randomSeedSelector) {
      console.log('获取到随机种子组件:', randomSeedSelector);
      setUseRandomSeed(randomSeedSelector.useRandom);
      setSeed(randomSeedSelector.value);
    }
    // 找到textDescriptionPanel组件
    const textDescriptionPanel = task.components.find(c => c.componentType === 'textDescriptionPanel' );
    if (textDescriptionPanel) {
      setTextDescription(textDescriptionPanel.prompt);
    }
    
    
    // 切换到结果标签页
    setActiveTab('result');
    
    // 显示成功消息
    message.success({
      content: '配置已重新导入，可继续进行调整',
      duration: 3
    });
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleViewDetails = (image, task) => {
    console.log('查看详情, 完整任务数据:', task);
    console.log('查看详情, 图片数据:', image);
    
    // 预加载生成图
    if (image.url) {
      const resultImg = new Image();
      resultImg.src = image.url;
      console.log('预加载结果图:', image.url);
    }
    
    // 只处理数组结构，不再兼容对象结构
    const components = Array.isArray(task.components) ? task.components : [];
    console.log('查看详情 - 处理的组件数据:', components);
    
    // 使用components.find获取组件 - 统一小写组件名
    const randomSeedSelector = components.find(c => c.componentType === 'randomSeedSelector');
    const promptPanel = components.find(c => c.componentType === 'promptPanel');
    
    // 记录组件获取结果
    console.log('查看详情, 组件获取结果:', {
      randomSeedSelector,
      promptPanel
    });
    
    // 获取种子值 - 按照标准优先级顺序
    const taskSeed = task.seed !== undefined && task.seed >= 0 ? task.seed : 
                   (randomSeedSelector?.value !== undefined && randomSeedSelector.value >= 0 ? 
                    randomSeedSelector.value : (image.seed || 0));
    
    console.log('查看详情, 使用种子值:', taskSeed);
    
    // 获取任务信息以便查看详情
    if (image && task) {
      const enhancedImage = {
        ...image,
        taskId: task.taskId,
        generatedId: image.generatedId || image.imageIndex,
        // 添加种子值
        seed: image.seed !== undefined ? image.seed : taskSeed,
        // 使用标准的组件列表
        components: [
          {
            componentType: 'promptPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            id: generateId(ID_TYPES.COMPONENT), // 添加id字段，确保与componentId一致
            prompt: promptPanel?.prompt || '生成虚拟模特'
          },
          {
            componentType: 'randomSeedSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            id: generateId(ID_TYPES.COMPONENT), // 添加id字段，确保与componentId一致
            name: '随机种子',
            useRandom: false,
            value: taskSeed
          },
          {
            componentType: 'textDescriptionPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            id: generateId(ID_TYPES.COMPONENT), // 添加id字段，确保与componentId一致
            name: '文字描述',
            status: 'completed',
            prompt: task.textDescription || textDescription || ''
          }
        ]
      };
      
      return {
        ...task,
        // 确保任务对象也包含适配后的组件数据
        ...enhancedImage
      }

    }
  };

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);
    
    // 重置状态，无需延迟
    setSelectedImage(null);
    // 重置任务信息，避免保留旧任务导致重新打开时出错
    setImageDetailsTask(null);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);
    

    
    // 重置文本弹窗状态
    setShowAdvancedText(false);
  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');
      
      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;
    
    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;
    
    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;
    
    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }
    
    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };
  // 处理打开蒙版绘制弹窗 - 保留空函数架构避免报错
  const handleDrawMask = (panel) => {
    // 功能已移除
    console.log('蒙版功能已禁用');
    message.info('虚拟模特功能不支持蒙版绘制');
  };

  // 添加处理添加为虚拟模特的函数和状态
  const [showModelRegistration, setShowModelRegistration] = useState(false);
  const [selectedModelData, setSelectedModelData] = useState(null);
  // 添加一个ref用于保存GenerationArea组件实例
  const generationAreaRef = useRef(null);

  // 处理模特保存成功
  const handleModelSaved = (modelData) => {
    console.log('模特保存成功:', modelData);
    // 可以在这里添加其他处理逻辑
  };

  // 处理添加为虚拟模特
  const handleAddAsExclusiveModel = (image, task) => {
    console.log('添加为虚拟模特:', image, task);
    
    // 检查是否达到虚拟模特数量上限
    if (generationAreaRef.current) {
      const modelManager = generationAreaRef.current.getVirtualModelManager();
      
      if (modelManager && modelManager.isAtMaxLimit()) {
        // 显示数量上限提示
        message.warning(`虚拟模特数量已达上限(${VirtualModelManager.MAX_MODELS_COUNT}个)，请删除不需要的模特后再添加。`);
        return;
      }
    }
    
    // 使用标准数组结构
    const components = task.components || [];
    
    // 准备传递给模特登记弹窗的数据
    const modelData = {
      url: image.url,
      images: task.processInfo.results.map(result => ({
        url: result.url
      })),
      imageUrl: image.url,
      taskId: task.taskId,
      isAll:true,
      imageIndex: image.imageIndex || 0,
      createdAt: task.createdAt,
      prompt: '',
      tags: []
    };
    
    // 设置选中的模特数据
    setSelectedModelData(modelData);
    
    // 显示模特登记弹窗
    setShowModelRegistration(true);
  };

  // 处理编辑虚拟模特
  const handleEditModel = (model) => {
    console.log('编辑虚拟模特:', model);
    
    // 设置选中的模特数据
    setSelectedModelData({
      ...model,
      // 确保必要的字段都有值
      prompt: model.prompt || '',
      tags: model.tags || []
    });
    
    // 显示模特登记弹窗
    setShowModelRegistration(true);
  };

  // 处理文本描述变化
  const handleTextDescriptionChange = (value) => {
    setTextDescription(value);
    setHasUnsavedChanges(true);
  };

  return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="虚拟模特功能">
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <div className="virtual-page">
        <div className="virtual-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isProcessing}
            featureName="virtual"
            quantity={6}
          >
            {/* 文本描述组件 */}
            <TextDescriptionPanel
              description={textDescription}
              onChange={handleTextDescriptionChange}
              placeholder="请输入对虚拟模特的详细文字描述，将用于辅助生成更符合要求的效果...请尽量详细的描述想要的内容"
            />

            {/* 提示组件 */}
            <TipsPanel 
              tipContent={
                <>
                  虚拟模特图片下载后供其他功能使用，数量固定为6张。耗时较长，请耐心等待。<br />
                  如需生成<span style={{ color: 'var(--brand-primary)' }}>高级专属模特</span>请联系客服。
                </>
              }
            />
            
            {/* 随机种子选择器 */}
            <RandomSeedSelector
              onRandomChange={setUseRandomSeed}
              onSeedChange={setSeed}
              defaultRandom={useRandomSeed}
              defaultSeed={seed}
              // 编辑模式下传递历史种子
              isEdit={selectedImage !== null}
              editSeed={selectedImage?.seed || null}
            />
          </ControlPanel>

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />

          <GenerationArea
            activeTab={activeTab}
            onTabChange={setActiveTab}
            tasks={generationTasks}
            onEditTask={handleEditTask}
            setIsProcessing={setIsProcessing}
            onViewDetails={handleViewDetails}
            pageType="virtual"
            onAddAsExclusiveModel={handleAddAsExclusiveModel}
            onEditModel={handleEditModel}
            onModelSaved={handleModelSaved}
            ref={generationAreaRef}
          />
        </div>

        {/* 上传指导弹窗 */}
        {showUploadGuide && (
          <UploadGuideModal
            type="virtual"
            pageType="virtual"
            onClose={() => setShowUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到上传结果:', result);
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowUploadGuide(false);
              }
            }}
          />
        )}

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={() => console.log('删除功能已禁用')}
            onReupload={() => console.log('重新上传功能已禁用')}
            onDrawMask={handleDrawMask}
            pageType="virtual"
          />
        )}

        {/* 添加 ImageDetailsModal 组件 - 使用懒加载 */}
        {showImageDetails && selectedImage ? (
          <ImageDetailsModal
            selectedImage={selectedImage}
            onClose={handleCloseImageDetails}
            generationTasks={generationTasks}
            onEditTask={handleEditTask}
            pageType="virtual"
          />
        ) : null}



        {/* 添加模特登记弹窗 */}
        {showModelRegistration && (
          <ModelRegistrationModal
            isOpen={showModelRegistration}
            onClose={() => {
              setShowModelRegistration(false);
              // 如果GenerationArea组件引用存在，刷新虚拟模特列表
              if (generationAreaRef.current && activeTab === 'models') {
                generationAreaRef.current.refreshModelManager();
              }
            }}
            modelData={selectedModelData}
            onSaved={handleModelSaved}
          />
        )}

        {/* 显示生成中的加载状态 */}
        {isProcessing && (
          <div className="generating-status">
            <Spin size="large" />
            <p>正在生成中，请稍候...</p>
          </div>
        )}
        
        {/* 显示生成的图片 */}
        {processedImages.length > 0 && (
          <div className="generated-images">
            <h3>生成结果</h3>
            <div className="image-grid">
              {processedImages.map((image, index) => (
                <div key={index} className="image-item">
                  <img src={image.url} alt={`生成图片 ${index + 1}`} />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </RequireLogin>
  );
};

export default VirtualPage; 
