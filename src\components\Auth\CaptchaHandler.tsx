import React, { useState, useEffect } from 'react';

interface CaptchaHandlerProps {
  // 手机号
  phone: string;
  // 验证码类型：register或reset
  type: 'register' | 'reset';
  // 是否禁用按钮
  disabled?: boolean;
  // 点击获取验证码的回调函数，必须提供
  onCaptchaRequest: (phone: string, type: 'register' | 'reset') => Promise<void>;
  // 当前倒计时值，由父组件控制
  countdown?: number;
  // 是否处于加载状态
  isLoading?: boolean;
  // 自定义类名
  className?: string;
  // 自定义按钮文本
  buttonText?: string;
}

/**
 * 验证码处理组件
 * 简化版的验证码请求按钮，将复杂的验证码请求逻辑交给父组件处理
 */
const CaptchaHandler: React.FC<CaptchaHandlerProps> = ({
  phone,
  type,
  disabled = false,
  onCaptchaRequest,
  countdown = 0,
  isLoading = false,
  className = 'captcha-btn',
  buttonText = '获取验证码'
}) => {
  // 验证手机号基本格式
  const isPhoneValid = (phoneNumber: string): boolean => {
    return /^1[3-9]\d{9}$/.test(phoneNumber);
  };

  // 验证并请求验证码
  const handleCaptchaClick = async () => {
    // 若按钮已禁用或倒计时中，不处理点击事件
    if (disabled || countdown > 0) {
      return;
    }

    // 简单验证手机号格式
    if (!phone || !isPhoneValid(phone)) {
      return;
    }

    try {
      // 调用父组件提供的回调函数请求验证码
      await onCaptchaRequest(phone, type);
    } catch (error) {
      console.error('Failed to send captcha:', error);
    }
  };

  return (
    <button
      className={className}
      onClick={handleCaptchaClick}
      disabled={disabled || countdown > 0 || isLoading}
      type="button"
    >
      {(countdown > 0 || isLoading) ? `${countdown || 60}s` : buttonText}
    </button>
  );
};

export default CaptchaHandler;
