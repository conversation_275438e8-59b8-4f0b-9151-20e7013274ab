const express = require('express');
const router = express.Router();
const planController = require('./plan.controller');
const { auth, requireRole } = require('../../../middleware/auth.middleware');

// 公共路由 - 获取公开的订阅计划
router.get('/public', planController.getAllPlans);
router.get('/code/:code', planController.getPlanByCode);

// 需要管理员权限的路由
router.get('/', auth, requireRole('admin'), planController.getAllPlans);
router.get('/:id', auth, requireRole('admin'), planController.getPlanById);
router.post('/', auth, requireRole('admin'), planController.createPlan);
router.put('/:id', auth, requireRole('admin'), planController.updatePlan);
router.delete('/:id', auth, requireRole('admin'), planController.deletePlan);

module.exports = router; 