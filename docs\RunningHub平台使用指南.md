# RunningHub平台使用指南

## 概述

RunningHub平台是一个基于云端的AI工作流执行平台，支持ComfyUI工作流的API化调用。与传统的实例管理方式不同，RunningHub采用应用调用方式，无需手动开机，更加便捷高效。

## 主要特点

- **云端执行**: 无需本地部署，所有工作流在云端执行，节省本地资源
- **即时启动**: 应用调用方式，无需等待实例开机，任务即时执行
- **灵活配置**: 支持多种任务类型，可自定义工作流参数
- **实时监控**: 实时监控任务状态，自动获取执行结果

## 快速开始

### 1. 获取API密钥

1. 访问 [RunningHub官网](https://www.runninghub.cn)
2. 注册并登录账户
3. 开通基础会员权益（免费用户暂不能使用API）
4. 在用户头像菜单中进入"API控制台"
5. 复制您的32位API密钥

### 2. 获取工作流ID

1. 在RunningHub平台上打开您要使用的工作流
2. 从地址栏中获取工作流ID
   - 例如：`https://www.runninghub.cn/#/workflow/1850925505116598274`
   - 工作流ID为：`1850925505116598274`
3. 确保该工作流已经手动运行成功过

### 3. 配置管理

在系统的"RunningHub平台"页面中：

1. 点击"配置管理"标签页
2. 点击"添加配置"按钮
3. 填写配置信息：
   - **配置名称**: 为配置起一个便于识别的名称
   - **API密钥**: 输入从RunningHub获取的32位API密钥
   - **工作流ID**: 输入要使用的工作流ID
   - **描述**: 可选，添加配置的描述信息
4. 点击"保存"完成配置

## 任务类型

### 1. 简易任务

简易任务使用工作流的默认参数直接执行，相当于在RunningHub平台上点击"运行"按钮。

**适用场景**:
- 工作流参数已经预设好
- 不需要动态修改参数
- 快速测试工作流

**使用方法**:
1. 选择已配置的API配置
2. 选择"简易任务"标签页
3. 选择是否添加元数据
4. 点击"创建任务"

### 2. 高级任务

高级任务允许您自定义工作流节点的参数值，实现更精细的控制。

**适用场景**:
- 需要动态修改工作流参数
- 批量处理不同参数的任务
- 精细控制工作流执行

**使用方法**:
1. 选择已配置的API配置
2. 选择"高级任务"标签页
3. 添加节点信息：
   - **节点ID**: ComfyUI工作流中的节点ID
   - **字段名称**: 要修改的参数字段名
   - **字段值**: 新的参数值
4. 可添加多个节点信息
5. 选择是否添加元数据
6. 点击"创建任务"

### 3. AI应用任务

AI应用任务用于调用RunningHub平台上的AI应用。

**适用场景**:
- 使用RunningHub的预制AI应用
- 简化的应用调用接口

**使用方法**:
1. 选择已配置的API配置
2. 选择"AI应用任务"标签页
3. 输入应用ID
4. 输入JSON格式的输入数据
5. 点击"创建任务"

## 任务监控

在"任务监控"页面可以：

- **查看任务状态**: 实时显示任务的执行状态
- **监控进度**: 显示任务执行进度
- **查看详情**: 查看任务的详细信息
- **下载结果**: 任务完成后下载生成的文件
- **取消任务**: 取消正在执行或排队中的任务

### 任务状态说明

- **已创建**: 任务已创建，等待处理
- **排队中**: 任务在队列中等待执行
- **运行中**: 任务正在执行中
- **成功**: 任务执行成功
- **失败**: 任务执行失败

## API接口

系统提供了完整的API接口供开发者使用：

### 前端API

```javascript
import runningHubAPI from './api/runningHub';

// 创建简易任务
const result = await runningHubAPI.createSimpleTask({
  apiKey: 'your-api-key',
  workflowId: 'your-workflow-id',
  addMetadata: false
});

// 创建高级任务
const result = await runningHubAPI.createAdvancedTask({
  apiKey: 'your-api-key',
  workflowId: 'your-workflow-id',
  nodeInfoList: [
    {
      nodeId: '6',
      fieldName: 'text',
      fieldValue: 'your prompt'
    }
  ]
});

// 查询任务状态
const status = await runningHubAPI.getTaskStatus(taskId, apiKey);

// 获取任务结果
const results = await runningHubAPI.getTaskResults(taskId, apiKey);
```

### 服务端API

```bash
# 创建简易任务
POST /api/runninghub/tasks/simple
{
  "apiKey": "your-api-key",
  "workflowId": "your-workflow-id",
  "addMetadata": false
}

# 查询任务状态
GET /api/runninghub/tasks/{taskId}/status?apiKey=your-api-key

# 获取任务结果
GET /api/runninghub/tasks/{taskId}/results?apiKey=your-api-key
```

## 服务集成

### 前端服务

- `runningHubService`: 核心服务类，提供API调用功能
- `taskManager`: 任务管理器，处理任务生命周期
- `runningHubConfig`: 配置管理器，管理API密钥和工作流配置

### 后端服务

- `runningHubService`: 服务端API调用服务
- `runningHub.routes`: API路由处理

## 注意事项

1. **API密钥安全**: 请妥善保管您的API密钥，不要在客户端代码中硬编码
2. **工作流验证**: 确保工作流在RunningHub平台上能正常运行
3. **计费方式**: API调用与网页端运行工作流的计费方式一致
4. **并发限制**: 免费用户有并发限制，企业用户可联系商务开通更高并发
5. **超时设置**: 长时间运行的工作流请适当设置超时时间

## 故障排除

### 常见错误

1. **APIKEY_INVALID_NODE_INFO**: 节点信息与工作流不匹配
   - 检查节点ID、字段名称是否正确
   - 确保工作流结构没有变化

2. **APIKEY TASK STATUS ERROR**: 工作流运行过程中出错
   - 在RunningHub网页端查看具体错误信息
   - 检查工作流参数是否正确

3. **API密钥验证失败**: 
   - 确认API密钥格式正确（32位字母数字）
   - 检查账户是否开通基础会员权益

### 获取帮助

- 查看RunningHub官方文档: https://www.runninghub.cn/runninghub-api-doc/
- 联系技术支持
- 查看系统日志获取详细错误信息

## 更新日志

### v1.0.0
- 初始版本发布
- 支持简易任务、高级任务、AI应用任务
- 提供完整的配置管理和任务监控功能
- 集成前端和后端API服务
