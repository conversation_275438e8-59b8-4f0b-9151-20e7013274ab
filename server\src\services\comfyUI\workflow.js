const WebSocket = require('ws');

// 工作流和结果处理功能扩展
class WorkflowExtension {
  constructor(client) {
    this.client = client;
  }

  /**
   * 执行工作流
   * @param {Object} workflow 工作流配置
   * @param {Object} params 参数对象，包含要注入的参数值
   * @returns {Promise<Object>} 执行结果
   */
  async executeWorkflow(workflow, params = {}) {
    try {
      console.log('准备执行工作流:', {
        workflowType: workflow.metadata?.workflowType || '未知',
        paramCount: Object.keys(params).length
      });
      
      // 清理ComfyUI输入目录的代码已被移除，因为ComfyUI不提供标准的清理API
      
      // 转换图片URLs为数组
      if (params.imageUrl && !params.images) {
        params.images = [params.imageUrl];
      }
      
      // 确保工作流配置正确
      if (!workflow?.prompt) {
        throw new Error('无效的工作流配置');
      }

      // 创建工作流配置的深拷贝，避免修改原始对象
      const workflowConfig = JSON.parse(JSON.stringify(workflow));
      
      console.log('工作流执行接收的参数:', JSON.stringify(params, null, 2));
      
      // 处理特殊情况：需要使用输入文件夹的工作流（如rmbgfile）
      if (workflow.metadata?.requiresInputFolder && params.images && Array.isArray(params.images)) {
        console.log('处理需要输入文件夹的工作流，图片数量:', params.images.length);
        
        // 获取ComfyUI的输入文件夹节点ID
        const folderNodeId = workflow.metadata.folderNodeId || "46";
        
        // 确保节点存在
        if (!workflowConfig.prompt[folderNodeId]) {
          throw new Error(`工作流配置错误：未找到文件夹节点(${folderNodeId})`);
        }
        
        // 获取ComfyUI的输入文件夹路径 - 后续需要上传文件到该文件夹
        const inputFolder = workflow.metadata.inputFolder || "/input/";
        console.log(`将使用ComfyUI输入文件夹: ${inputFolder}`);
        
        // 更新文件夹节点的image_load_cap参数，确保能加载所有图片
        if (workflowConfig.prompt[folderNodeId].class_type === 'LoadImagesFromFolderKJ') {
          const imageCount = params.images.length;
          workflowConfig.prompt[folderNodeId].inputs.image_load_cap = Math.max(imageCount, 
            workflowConfig.prompt[folderNodeId].inputs.image_load_cap || 1);
          console.log(`已设置文件夹节点的加载上限为 ${workflowConfig.prompt[folderNodeId].inputs.image_load_cap}，以匹配图片数量 ${imageCount}`);
        } else {
          console.warn(`文件夹节点${folderNodeId}类型 ${workflowConfig.prompt[folderNodeId].class_type} 可能不支持多图片输入`);
        }
        
        // 创建以第一张图片命名的文件夹并移动图片文件
        if (params.images.length > 0) {
          try {
            const fs = require('fs');
            const path = require('path');
            
            // 获取用户ID，默认为'developer'
            const userId = params.userId || 'developer';
            
            // 获取上传目录的路径
            const uploadDir = path.resolve(__dirname, `../../../storage/${userId}/uploads`);
            
            // 获取第一张图片的文件名
            const firstImageFileName = params.images[0];
            
            // 创建新文件夹路径（使用第一张图片的文件名，不包含扩展名）
            const folderName = path.parse(firstImageFileName).name;
            const newFolderPath = path.join(uploadDir, folderName);
            
            console.log(`创建新文件夹: ${newFolderPath}`);
            
            // 确保文件夹存在
            if (!fs.existsSync(newFolderPath)) {
              fs.mkdirSync(newFolderPath, { recursive: true });
              console.log(`成功创建文件夹: ${newFolderPath}`);
            } else {
              console.log(`文件夹已存在: ${newFolderPath}`);
            }
            
            // 移动所有图片到新文件夹
            const movedImages = [];
            for (const imageName of params.images) {
              const srcPath = path.join(uploadDir, imageName);
              const destPath = path.join(newFolderPath, imageName);
              
              if (fs.existsSync(srcPath)) {
                // 复制文件而不是移动，以确保原始工作流仍然可以访问
                fs.copyFileSync(srcPath, destPath);
                console.log(`已复制图片: ${srcPath} -> ${destPath}`);
                movedImages.push({
                  originalPath: srcPath,
                  newPath: destPath,
                  fileName: imageName
                });
              } else {
                console.warn(`图片文件不存在: ${srcPath}`);
              }
            }
            
            // 将新文件夹信息添加到参数中
            params._groupFolderInfo = {
              folderName,
              folderPath: newFolderPath,
              imageCount: movedImages.length,
              images: movedImages
            };
            
            console.log(`已将图片组织到文件夹 ${folderName} 中，总计 ${movedImages.length} 张图片`);
            
            // 将整个文件夹上传到云容器
            try {
              console.log(`准备上传文件夹 ${folderName} 到云容器...`);
              const uploadFolderResult = await this.client.uploadFolder(newFolderPath);
              
              // 记录上传结果
              params._folderUploadInfo = {
                ...uploadFolderResult,
                folderName,
                uploadTime: new Date().toISOString()
              };
              
              console.log(`文件夹 ${folderName} 上传完成，成功上传 ${uploadFolderResult.successCount}/${uploadFolderResult.totalFiles} 个文件`);
            } catch (error) {
              console.error(`上传文件夹 ${folderName} 到云容器失败:`, error);
              // 记录错误但继续执行，不中断工作流
              params._folderUploadInfo = {
                folderName,
                success: false,
                error: error.message,
                uploadTime: new Date().toISOString()
              };
            }
          } catch (error) {
            console.error('创建文件夹或移动图片时出错:', error);
            // 继续执行工作流，不中断处理
          }
        }
        
        // 将inputFolder和图片路径信息添加到参数中，供后续上传文件使用
        params._inputFolderInfo = {
          inputFolder,
          folderNodeId,
          imageCount: params.images.length
        };
        
        console.log('已准备批量处理图片:', params._inputFolderInfo);
      }
      // 处理特殊情况：tryonauto工作流需要分别注入服装图片和模特图片
      else if (params.clothingImagePath && params.modelImagePath && params.nodeMapping) {
        console.log('处理tryonauto工作流，根据指定节点映射注入图片');
        
        const { clothingNode, modelNode } = params.nodeMapping;
        
        // 提取ComfyUI上传的图片名称
        const clothing = params.clothing;
        const model = params.model;
        
        // 检查是否有有效的服装图片和模特图片名称
        if (!clothing || !model) {
          throw new Error('tryonauto工作流缺少有效的服装或模特图片');
        }
        
        // 注入服装图片到节点31
        if (clothingNode && workflowConfig.prompt[clothingNode] && 
            workflowConfig.prompt[clothingNode].class_type === 'LoadImage') {
          workflowConfig.prompt[clothingNode].inputs.image = clothing;
          console.log(`已将服装图片 ${clothing} 注入到节点 ${clothingNode}`);
        } else {
          console.error(`工作流中未找到有效的服装图片节点(${clothingNode})`);
          throw new Error('工作流配置错误：缺少服装图片节点');
        }
        
        // 注入模特图片到节点34
        if (modelNode && workflowConfig.prompt[modelNode] && 
            workflowConfig.prompt[modelNode].class_type === 'LoadImage') {
          workflowConfig.prompt[modelNode].inputs.image = model;
          console.log(`已将模特图片 ${model} 注入到节点 ${modelNode}`);
        } else {
          console.error(`工作流中未找到有效的模特图片节点(${modelNode})`);
          throw new Error('工作流配置错误：缺少模特图片节点');
        }
      } 
      // 处理服装和模型直接传递的情况，包含nodeMapping
      else if (params.clothing && params.model && params.nodeMapping) {
        const { clothingNode, modelNode } = params.nodeMapping;
        
        // 注入服装图片到指定节点
        if (clothingNode && workflowConfig.prompt[clothingNode] && 
            workflowConfig.prompt[clothingNode].class_type === 'LoadImage') {
          workflowConfig.prompt[clothingNode].inputs.image = params.clothing;
          console.log(`已将服装图片 ${params.clothing} 注入到节点 ${clothingNode}`);
        } else {
          console.error(`工作流中未找到有效的服装图片节点(${clothingNode})`);
          throw new Error(`工作流配置错误：缺少服装图片节点(${clothingNode})`);
        }
        
        // 注入模特图片到指定节点
        if (modelNode && workflowConfig.prompt[modelNode] && 
            workflowConfig.prompt[modelNode].class_type === 'LoadImage') {
          workflowConfig.prompt[modelNode].inputs.image = params.model;
          console.log(`已将模特图片 ${params.model} 注入到节点 ${modelNode}`);
        } else {
          console.error(`工作流中未找到有效的模特图片节点(${modelNode})`);
          throw new Error(`工作流配置错误：缺少模特图片节点(${modelNode})`);
        }
      }
      // 处理服装和模型直接传递的情况，使用默认节点
      else if (params.clothing && params.model) {
        console.log('处理tryonauto工作流，使用默认节点31和节点34');
        
        // 找到节点31（服装图片）和节点34（模特图片）
        if (workflowConfig.prompt["31"] && workflowConfig.prompt["31"].class_type === 'LoadImage') {
          workflowConfig.prompt["31"].inputs.image = params.clothing;
          console.log(`已将服装图片 ${params.clothing} 注入到节点 31`);
        } else {
          console.error('工作流中未找到服装图片节点(31)');
          throw new Error('工作流配置错误：缺少服装图片节点');
        }
        
        if (workflowConfig.prompt["34"] && workflowConfig.prompt["34"].class_type === 'LoadImage') {
          workflowConfig.prompt["34"].inputs.image = params.model;
          console.log(`已将模特图片 ${params.model} 注入到节点 34`);
        } else {
          console.error('工作流中未找到模特图片节点(34)');
          throw new Error('工作流配置错误：缺少模特图片节点');
        }
      } else if (params.clothing || params.model) {
        // 只提供了其中一张图片，抛出错误
        if (params.clothing) {
          console.error('缺少模特图片，无法执行tryonauto工作流');
          throw new Error('模特换装工作流需要同时提供服装图片和模特图片');
        } else {
          console.error('缺少服装图片，无法执行tryonauto工作流');
          throw new Error('模特换装工作流需要同时提供服装图片和模特图片');
        }
      }
      // 常规单图片注入逻辑
      else if (params.image) {
        // 查找LoadImage类型的节点并注入图片名称
        let foundImageNode = false;
        for (const [nodeId, node] of Object.entries(workflowConfig.prompt)) {
          if (node.class_type === 'LoadImage' && node.inputs.image) {
            node.inputs.image = params.image;
            console.log(`已将图片 ${params.image} 注入到节点 ${nodeId}`);
            foundImageNode = true;
          }
        }
        
        if (!foundImageNode) {
          console.error('工作流中未找到LoadImage节点');
          throw new Error('工作流配置错误：缺少图片输入节点');
        }
      } 
      // 如果没有明确的图片输入，但工作流需要从输入文件夹读取图片，那么跳过图片注入
      else if (workflow.metadata?.requiresInputFolder) {
        console.log('工作流将从输入文件夹读取图片，跳过图片注入');
      }
      // 没有提供任何图片参数
      else {
        console.error('缺少必要的图片参数');
        throw new Error('缺少必要的图片参数，无法执行工作流');
      }
      
      // 注入其他参数
      if (params.additionalParams) {
        for (const [paramName, paramValue] of Object.entries(params.additionalParams)) {
          for (const [nodeId, node] of Object.entries(workflowConfig.prompt)) {
            if (node.inputs && paramName in node.inputs) {
              node.inputs[paramName] = paramValue;
              console.log(`已将参数 ${paramName}=${paramValue} 注入到节点 ${nodeId}`);
            }
          }
        }
      }

      // 为SaveImage节点添加唯一的文件名前缀，避免文件名冲突
      if (workflow.metadata && workflow.metadata.saveImageNodeId) {
        const saveImageNodeId = workflow.metadata.saveImageNodeId;
        if (workflowConfig.prompt[saveImageNodeId] && 
            workflowConfig.prompt[saveImageNodeId].class_type === 'SaveImage') {
          
          // 获取原始前缀
          const originalPrefix = workflowConfig.prompt[saveImageNodeId].inputs.filename_prefix || 'output';
          
          // 使用固定的序号格式 prefix_00001_，确保与前端正则匹配
          const formattedPrefix = `${originalPrefix}_00001_`;
          workflowConfig.prompt[saveImageNodeId].inputs.filename_prefix = formattedPrefix;
          console.log(`为SaveImage节点 ${saveImageNodeId} 设置文件名前缀: ${formattedPrefix}`);
        }
      }

      // 检查节点配置
      for (const [nodeId, node] of Object.entries(workflowConfig.prompt)) {
        console.log(`检查节点 ${nodeId}:`, {
          type: node.class_type,
          inputs: JSON.stringify(node.inputs, null, 2)
        });
      }

      console.log('准备执行工作流配置:', JSON.stringify(workflowConfig, null, 2));
      console.log('使用云实例URL:', this.client.baseURL);
      
      // 使用确认的API路径前缀
      const promptPath = `${this.client.apiPathPrefix || ''}/prompt`;
      console.log(`执行工作流API路径: ${promptPath}`);

      const response = await this.client.axiosInstance.post(promptPath, workflowConfig, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.client.apiKey}`,
          'X-Instance-ID': this.client.instanceId
        },
        timeout: 30000
      }).catch(error => {
        console.error('工作流执行请求失败:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          error: error.message,
          request: {
            url: error.config?.url,
            method: error.config?.method,
            data: error.config?.data
          }
        });

        if (error.response?.data) {
          const errorData = error.response.data;
          console.error('ComfyUI详细错误信息:', {
            error: errorData.error,
            node_errors: errorData.node_errors,
            executed: errorData.executed,
            detail: errorData.detail
          });
        }

        throw error;
      });

      if (response.status !== 200) {
        throw new Error(`执行失败: ${response.status}`);
      }

      console.log('工作流在云实例上执行成功，响应:', response.data);
      return response.data;
    } catch (error) {
      console.error('在云实例上执行工作流时出错:', {
        message: error.message,
        response: error.response?.data,
        config: error.config?.data,
        stack: error.stack
      });
      
      // 尝试从响应中获取详细错误信息
      if (error.response?.data) {
        const errorData = error.response.data;
        
        if (typeof errorData === 'string') {
          throw new Error(`云实例执行失败: ${errorData}`);
        } else if (errorData.error) {
          throw new Error(`云实例执行失败: ${errorData.error}`);
        } else if (errorData.detail) {
          throw new Error(`云实例执行失败: ${errorData.detail}`);
        } else if (errorData.node_errors) {
          const nodeErrors = Object.entries(errorData.node_errors)
            .map(([nodeId, error]) => `节点${nodeId}: ${error}`)
            .join('; ');
          throw new Error(`云实例执行失败: ${nodeErrors}`);
        } else {
          throw new Error(`云实例执行失败: ${JSON.stringify(errorData)}`);
        }
      }
      
      throw error;
    }
  }

  // 等待结果
  async waitForResult(promptId, timeout = 300000) { // 默认等待5分钟
    const startTime = Date.now();
    let lastStatus = null;
    let isExecuting = false;
    
    // 创建WebSocket连接，添加API密钥到URL查询参数
    const wsConnection = `${this.client.wsURL}/ws?api_key=${this.client.apiKey}&instance_id=${this.client.instanceId}`;
    console.log('尝试建立WebSocket连接:', wsConnection);
    const ws = new WebSocket(wsConnection);
    
    // 设置WebSocket事件处理
    ws.on('error', (error) => {
      console.error('WebSocket连接错误:', error);
    });

    ws.on('open', () => {
      console.log('WebSocket连接已建立');
      // 订阅进度更新
      ws.send(JSON.stringify({
        "type": "subscribe",
        "data": {
          "node": null,
          "prompt_id": promptId
        }
      }));
    });

    // 处理进度消息
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        
        switch (message.type) {
          case 'execution_start':
            console.log('\n工作流开始执行...');
            break;
          case 'execution_cached':
            console.log('使用缓存结果');
            break;
          case 'executing':
            const { node, prompt_id } = message.data;
            if (prompt_id === promptId) {
              console.log(`\n正在执行节点: ${node}`);
              isExecuting = true;
            }
            break;
          case 'progress':
            const { value, max } = message.data;
            const percent = Math.round((value / max) * 100);
            process.stdout.write(`\r处理进度: ${percent}% [${value}/${max}]`);
            break;
          case 'executed':
            const { node_id } = message.data;
            console.log(`\n节点 ${node_id} 执行完成`);
            break;
          case 'execution_error':
            console.error('\n执行出错:', message.data.error);
            break;
          case 'execution_complete':
            console.log('\n工作流执行完成，等待图片生成...');
            break;
        }
      } catch (error) {
        console.error('解析WebSocket消息时出错:', error);
      }
    });
    
    while (Date.now() - startTime < timeout) {
      try {
        const result = await this.client.getResult(promptId);
        const status = result[promptId]?.status;
        
        // 记录状态变化
        const currentStatus = JSON.stringify(status);
        if (currentStatus !== lastStatus) {
          console.log('工作流状态更新:', status);
          lastStatus = currentStatus;
          
          // 检查是否开始执行
          if (status?.executing && !isExecuting) {
            console.log('工作流开始执行...');
            isExecuting = true;
          }
        }

        if (status?.completed) {
          console.log('工作流状态: 执行完成');
          const outputs = result[promptId].outputs;
          
          if (!outputs || !Object.keys(outputs).length) {
            console.log('工作流执行完成，但输出还未准备好，继续等待...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            continue;
          }

          // 处理输出图片 - 特别处理多图片结果
          const images = [];
          console.log('处理工作流输出:', Object.keys(outputs).length, '个节点有输出');
          
          // 特别关注SaveImage节点 - 通常是ID为43的节点
          for (const [nodeId, output] of Object.entries(outputs)) {
            if (output.images && output.images.length > 0) {
              console.log(`节点 ${nodeId} 产生了 ${output.images.length} 张图片`);
              
              // 处理此节点的所有图片
              for (const [index, image] of output.images.entries()) {
                try {
                  if (!image.filename) {
                    console.error('输出中没有文件名:', image);
                    continue;
                  }
                  console.log(`检查图片文件 ${index+1}/${output.images.length}: ${image.filename}`);

                  const remainingTime = timeout - (Date.now() - startTime);
                  const checkInterval = 2000;
                  let fileFound = null;

                  // 首先尝试使用已知成功路径格式
                  if (this.client.lastSuccessfulPathFormat) {
                    try {
                      const successPath = this.client.lastSuccessfulPathFormat.replace('${filename}', image.filename);
                      console.log(`优先使用已知成功路径格式: ${successPath}`);
                      
                      const response = await this.client.axiosInstance.head(successPath, {
                        headers: {
                          'Authorization': `Bearer ${this.client.apiKey}`,
                          'X-Instance-ID': this.client.instanceId
                        },
                        validateStatus: function(status) {
                          return status === 200;
                        },
                        timeout: 3000
                      });
                      
                      if (response.status === 200) {
                        console.log(`成功使用缓存路径格式找到文件`);
                        fileFound = { type: 'api', path: successPath };
                      }
                    } catch (error) {
                      console.log(`使用缓存路径格式失败:`, error.message);
                      // 失败时继续使用常规方法
                    }
                  }
                  
                  // 如果使用缓存路径格式失败，则使用常规方法尝试多个路径
                  if (!fileFound) {
                    const uploadExtension = require('./upload')(this.client);
                    for (let elapsed = 0; elapsed < Math.min(remainingTime, 10000); elapsed += checkInterval) {
                      fileFound = await uploadExtension.checkOutputFile(image.filename);
                      if (fileFound) {
                        break;
                      }
                      console.log('未找到图片文件，等待2秒后重试...');
                      await new Promise(resolve => setTimeout(resolve, checkInterval));
                    }
                  }

                  if (!fileFound) {
                    throw new Error(`图片文件 ${image.filename} 在 ${timeout/1000} 秒内未生成完成`);
                  }

                  // 不再自动复制文件，而是返回找到的文件信息
                  console.log('找到图片文件:', fileFound);
                  images.push({
                    ...image,
                    fileInfo: fileFound,  // 返回文件信息供外部处理
                    filename: image.filename,
                    output_node: nodeId,  // 添加输出节点ID，便于识别来源
                    output_index: index   // 添加在输出图片数组中的索引
                  });
                } catch (error) {
                  console.error(`处理图片 ${image.filename} 时出错:`, error);
                  // 记录错误但继续处理其他图片
                  console.error(error.stack);
                }
              }
            }
          }

          if (images.length > 0) {
            // 关闭WebSocket连接
            ws.close();
            console.log(`工作流生成了 ${images.length} 张图片`);
            return { images };
          } else {
            console.error('工作流执行完成但未找到有效图片');
            // 继续等待，可能图片还在保存中
            await new Promise(resolve => setTimeout(resolve, 2000));
            continue;
          }
        }
        
        if (status?.error) {
          // 关闭WebSocket连接
          ws.close();
          throw new Error(status.error);
        }

        // 根据执行状态调整等待时间
        const waitTime = isExecuting ? 2000 : 1000;
        await new Promise(resolve => setTimeout(resolve, waitTime));
      } catch (error) {
        // 关闭WebSocket连接
        ws.close();
        console.error('等待结果时出错:', error);
        throw new Error(`等待结果失败: ${error.message}`);
      }
    }
    
    // 关闭WebSocket连接
    ws.close();
    throw new Error(`等待结果超时（${timeout/1000}秒）`);
  }
}

module.exports = function(client) {
  const workflowExtension = new WorkflowExtension(client);
  
  // 增加一个测试方法
  workflowExtension.testWorkflow = async function(workflowType, pageType, imageType, imagePath) {
    console.log(`测试工作流: 类型=${workflowType}, 页面=${pageType}, 图片类型=${imageType}`);
    
    try {
      // 1. 获取工作流配置
      const { getWorkflow } = require('../workflows');
      const workflow = getWorkflow(workflowType, pageType, imageType);
      console.log('获取到工作流配置');
      
      // 2. 上传测试图片
      let imageName;
      if (imagePath) {
        const uploadResult = await client.uploadImage(imagePath);
        imageName = uploadResult.name;
        console.log(`上传测试图片成功: ${imageName}`);
      } else {
        console.log('未提供测试图片路径，将使用工作流中默认图片');
      }
      
      // 3. 准备参数
      const params = {
        image: imageName,
        additionalParams: {}
      };
      
      // 4. 执行工作流
      console.log('执行工作流...');
      const result = await this.executeWorkflow(workflow, params);
      console.log('工作流执行响应:', result);
      
      // 5. 等待结果
      console.log('等待工作流执行结果...');
      const output = await this.waitForResult(result.prompt_id);
      console.log('工作流执行完成，输出:', output);
      
      return {
        success: true,
        workflowType,
        pageType,
        imageType,
        output
      };
    } catch (error) {
      console.error('工作流测试失败:', error);
      return {
        success: false,
        workflowType,
        pageType,
        imageType,
        error: error.message
      };
    }
  };
  
  return workflowExtension;
}; 
