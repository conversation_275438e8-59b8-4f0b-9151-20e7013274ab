import { useEffect } from 'react';
import { UNSAFE_NavigationContext as NavigationContext } from 'react-router-dom';
import { useContext } from 'react';

export default function usePrompt(message, when = true) {
  const navigator = useContext(NavigationContext).navigator;

  useEffect(() => {
    if (!when) return;

    const push = navigator.push;
    const replace = navigator.replace;

    const handler = (method) => (...args) => {
      if (window.confirm(message)) {
        method.apply(navigator, args);
      }
      // 否则什么都不做，阻止跳转
    };

    navigator.push = handler(push);
    navigator.replace = handler(replace);

    return () => {
      navigator.push = push;
      navigator.replace = replace;
    };
  }, [message, when, navigator]);
} 