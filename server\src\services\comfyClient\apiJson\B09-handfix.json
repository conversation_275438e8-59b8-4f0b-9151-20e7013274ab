{"1": {"inputs": {"guidance": 30, "conditioning": ["12", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "2": {"inputs": {"noise_mask": false, "positive": ["1", 0], "negative": ["3", 0], "vae": ["5", 0], "pixels": ["25", 1], "mask": ["25", 2]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "3": {"inputs": {"conditioning": ["12", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "4": {"inputs": {"model": ["13", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "5": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "9": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "10": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "11": {"inputs": {"needInput": true, "seed": 209613365183112, "steps": 30, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["4", 0], "positive": ["2", 0], "negative": ["2", 1], "latent_image": ["17", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "种子调用"}}, "12": {"inputs": {"text": "good hands", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "13": {"inputs": {"lora_name": "Flux/Detailed_Hands-000001.safetensors", "strength_model": 0.8500000000000002, "model": ["10", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "14": {"inputs": {"samples": ["11", 0], "vae": ["5", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "15": {"inputs": {"filename_prefix": "HandFix", "images": ["30", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "17": {"inputs": {"needInput": true, "amount": 1, "samples": ["2", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "图片数量"}}, "22": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "上传图片和蒙版"}}, "25": {"inputs": {"context_expand_pixels": 20, "context_expand_factor": 1, "fill_mask_holes": true, "blur_mask_pixels": 16, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "forced size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1, "min_width": 512, "min_height": 512, "max_width": 768, "max_height": 768, "padding": 32, "image": ["22", 0], "mask": ["22", 1]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "26": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["25", 0], "inpainted_image": ["29", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Stitch node)"}}, "29": {"inputs": {"image": ["14", 0]}, "class_type": "ImpactImageBatchToImageList", "_meta": {"title": "Image Batch to Image List"}}, "30": {"inputs": {"images": ["26", 0]}, "class_type": "ImageListToImageBatch", "_meta": {"title": "Image List to Image Batch"}}}