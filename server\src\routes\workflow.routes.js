/**
 * 工作流管理路由
 * 提供工作流的CRUD操作和管理功能
 */

const express = require('express');
const router = express.Router();
const { auth, requireRole } = require('../middleware/auth.middleware');
const Workflow = require('../models/Workflow');

// 中间件：检查是否为管理员
router.use(auth, requireRole('admin'));

/**
 * 获取工作流列表
 * GET /api/workflows
 */
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      category, 
      enabled, 
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // 构建查询条件
    const query = {};
    if (category) query.category = category;
    if (enabled !== undefined) query.enabled = enabled === 'true';
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { id: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // 构建排序
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // 分页查询
    const skip = (page - 1) * limit;
    const workflows = await Workflow.find(query)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Workflow.countDocuments(query);

    res.json({
      success: true,
      data: {
        workflows,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取工作流列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取工作流列表失败'
    });
  }
});

/**
 * 获取工作流详情
 * GET /api/workflows/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const workflow = await Workflow.findOne({ id: req.params.id })
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email');

    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: '工作流不存在'
      });
    }

    res.json({
      success: true,
      data: workflow
    });
  } catch (error) {
    console.error('获取工作流详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取工作流详情失败'
    });
  }
});

/**
 * 创建工作流
 * POST /api/workflows
 */
router.post('/', async (req, res) => {
  try {
    const {
      id,
      name,
      displayName,
      category,
      description,
      version,
      enabled,
      parameters,
      supportedPlatforms,
      recommendedPlatform,
      tags,
      priority
    } = req.body;

    // 验证必填字段
    if (!id || !name || !category) {
      return res.status(400).json({
        success: false,
        message: '工作流ID、名称和分类是必填的'
      });
    }

    // 检查ID是否已存在
    const existingWorkflow = await Workflow.findOne({ id });
    if (existingWorkflow) {
      return res.status(400).json({
        success: false,
        message: '工作流ID已存在'
      });
    }

    // 创建工作流
    const workflow = new Workflow({
      id,
      name,
      displayName,
      category,
      description,
      version,
      enabled: enabled !== undefined ? enabled : true,
      parameters: parameters ? new Map(Object.entries(parameters)) : new Map(),
      supportedPlatforms: supportedPlatforms || ['comfyui'],
      recommendedPlatform: recommendedPlatform || 'auto',
      tags: tags || [],
      priority: priority || 0,
      createdBy: req.user._id
    });

    await workflow.save();

    res.json({
      success: true,
      data: workflow,
      message: '工作流创建成功'
    });
  } catch (error) {
    console.error('创建工作流失败:', error);
    res.status(500).json({
      success: false,
      message: '创建工作流失败'
    });
  }
});

/**
 * 更新工作流
 * PUT /api/workflows/:id
 */
router.put('/:id', async (req, res) => {
  try {
    const {
      name,
      displayName,
      category,
      description,
      version,
      enabled,
      parameters,
      supportedPlatforms,
      recommendedPlatform,
      tags,
      priority
    } = req.body;

    const workflow = await Workflow.findOne({ id: req.params.id });
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: '工作流不存在'
      });
    }

    // 更新字段
    if (name) workflow.name = name;
    if (displayName !== undefined) workflow.displayName = displayName;
    if (category) workflow.category = category;
    if (description !== undefined) workflow.description = description;
    if (version) workflow.version = version;
    if (enabled !== undefined) workflow.enabled = enabled;
    if (parameters) workflow.parameters = new Map(Object.entries(parameters));
    if (supportedPlatforms) workflow.supportedPlatforms = supportedPlatforms;
    if (recommendedPlatform) workflow.recommendedPlatform = recommendedPlatform;
    if (tags) workflow.tags = tags;
    if (priority !== undefined) workflow.priority = priority;
    
    workflow.updatedBy = req.user._id;

    await workflow.save();

    res.json({
      success: true,
      data: workflow,
      message: '工作流更新成功'
    });
  } catch (error) {
    console.error('更新工作流失败:', error);
    res.status(500).json({
      success: false,
      message: '更新工作流失败'
    });
  }
});

/**
 * 删除工作流
 * DELETE /api/workflows/:id
 */
router.delete('/:id', async (req, res) => {
  try {
    const workflow = await Workflow.findOne({ id: req.params.id });
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: '工作流不存在'
      });
    }

    await Workflow.findOneAndDelete({ id: req.params.id });

    res.json({
      success: true,
      message: '工作流删除成功'
    });
  } catch (error) {
    console.error('删除工作流失败:', error);
    res.status(500).json({
      success: false,
      message: '删除工作流失败'
    });
  }
});

/**
 * 获取工作流分类列表
 * GET /api/workflows/categories
 */
router.get('/meta/categories', async (req, res) => {
  try {
    const categories = await Workflow.getCategories();
    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('获取工作流分类失败:', error);
    res.status(500).json({
      success: false,
      message: '获取工作流分类失败'
    });
  }
});

/**
 * 批量更新工作流状态
 * PUT /api/workflows/batch/status
 */
router.put('/batch/status', async (req, res) => {
  try {
    const { ids, enabled } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的工作流ID列表'
      });
    }

    await Workflow.updateMany(
      { id: { $in: ids } },
      { 
        enabled,
        updatedBy: req.user._id,
        updatedAt: new Date()
      }
    );

    res.json({
      success: true,
      message: `已${enabled ? '启用' : '禁用'}${ids.length}个工作流`
    });
  } catch (error) {
    console.error('批量更新工作流状态失败:', error);
    res.status(500).json({
      success: false,
      message: '批量更新工作流状态失败'
    });
  }
});

module.exports = router;
