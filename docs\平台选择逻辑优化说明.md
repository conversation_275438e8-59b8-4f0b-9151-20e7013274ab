# 平台选择逻辑优化说明

## 优化目标

根据用户需求，优化平台选择逻辑：
1. **不再区分用户类别**（VIP等），所有用户享受相同的平台选择策略
2. **基于ComfyUI机器使用情况**来智能选择平台
3. **随机分配负载**，当ComfyUI有可用实例时随机选择平台
4. **自动切换**，当ComfyUI所有机器都被使用时自动切换到RunningHub

## 核心修改

### 1. **平台选择逻辑重构**

#### 修改前的逻辑：
```javascript
// 基于用户类型的选择
if (context.user.isVip && rules.userRules.vipPreferRunningHub) {
  return 'runninghub';
}

if (!context.user.isPaid && rules.userRules.freeUserLimitations.preferredPlatform) {
  return preferredPlatform;
}
```

#### 修改后的逻辑：
```javascript
// 基于ComfyUI实例可用性的选择
if (context.comfyuiInstancesStatus) {
  const { availableInstances, totalInstances, allInstancesBusy } = context.comfyuiInstancesStatus;
  
  // 所有ComfyUI实例都被使用时，切换到RunningHub
  if (allInstancesBusy && isWorkflowSupportedOnPlatform(workflowName, 'runninghub')) {
    return 'runninghub';
  }
  
  // 有可用实例时，随机选择平台（50%概率）
  if (availableInstances > 0) {
    const useComfyUI = Math.random() < 0.5;
    return useComfyUI ? 'comfyui' : 'runninghub';
  }
}
```

### 2. **ComfyUI实例状态检查**

新增`getComfyUIInstancesStatus()`函数，实时检查：
- **总实例数量**：运行中的ComfyUI实例总数
- **可用实例数量**：空闲可用的实例数量
- **繁忙实例数量**：正在使用中的实例数量
- **是否全部繁忙**：所有实例都被占用的状态

```javascript
async function getComfyUIInstancesStatus() {
  // 获取所有运行中的实例
  const instances = response.data.data.appList.filter(instance => instance.status === 300);
  
  let availableInstances = 0;
  let busyInstances = 0;
  
  // 检查每个实例的使用状态
  for (const instance of instances) {
    const isInUse = await cache.get('instance_in_use_' + instance.appId);
    const isFinished = await cache.get('instance_finished_' + instance.appId);
    
    if (isFinished || !isInUse) {
      availableInstances++;
    } else {
      busyInstances++;
    }
  }
  
  return {
    totalInstances: instances.length,
    availableInstances,
    busyInstances,
    allInstancesBusy: instances.length > 0 && availableInstances === 0
  };
}
```

### 3. **统一工作流服务更新**

#### 异步平台选择：
```javascript
// 修改前
const platform = this.selectPlatform(workflowName, params, context);

// 修改后
const platform = await this.selectPlatform(workflowName, params, context);
```

#### 实时状态获取：
```javascript
async selectPlatform(workflowName, params, context = {}) {
  // 获取ComfyUI实例状态
  try {
    const comfyuiInstancesStatus = await getComfyUIInstancesStatus();
    context.comfyuiInstancesStatus = comfyuiInstancesStatus;
    
    console.log(`ComfyUI实例状态: 总计${comfyuiInstancesStatus.totalInstances}个，可用${comfyuiInstancesStatus.availableInstances}个`);
  } catch (error) {
    console.error('获取ComfyUI实例状态失败:', error);
  }
  
  return getRecommendedPlatform(workflowName, context);
}
```

## 新的平台选择策略

### 1. **优先级规则**

1. **强制规则**：检查工作流是否仅支持特定平台
   - `runninghub_only`：仅支持RunningHub的工作流
   - `comfyui_only`：仅支持ComfyUI的工作流

2. **实例可用性检查**：
   - 如果所有ComfyUI实例都被使用 → 使用RunningHub
   - 如果有可用的ComfyUI实例 → 随机选择（50%概率）

3. **工作流偏好**（后备选择）：
   - `runninghub_preferred`：优先使用RunningHub的工作流
   - `comfyui_preferred`：优先使用ComfyUI的工作流

4. **平台优先级**：按配置的优先级顺序选择

5. **默认平台**：最后的后备选择

### 2. **随机负载均衡**

当ComfyUI有可用实例时：
```javascript
if (availableInstances > 0) {
  const useComfyUI = Math.random() < 0.5;
  
  if (useComfyUI && isWorkflowSupportedOnPlatform(workflowName, 'comfyui')) {
    console.log(`随机选择ComfyUI (可用实例: ${availableInstances}/${totalInstances})`);
    return 'comfyui';
  } else if (isWorkflowSupportedOnPlatform(workflowName, 'runninghub')) {
    console.log(`随机选择RunningHub (ComfyUI可用实例: ${availableInstances}/${totalInstances})`);
    return 'runninghub';
  }
}
```

### 3. **智能切换机制**

当ComfyUI实例全部繁忙时：
```javascript
if (allInstancesBusy && isWorkflowSupportedOnPlatform(workflowName, 'runninghub')) {
  console.log(`所有ComfyUI实例都被使用 (${totalInstances}个实例)，切换到RunningHub`);
  return 'runninghub';
}
```

## 日志和监控

### 1. **详细的选择日志**
```javascript
console.log(`ComfyUI实例状态: 总计${totalInstances}个，可用${availableInstances}个，繁忙${busyInstances}个`);
console.log(`工作流 ${workflowName} 推荐使用平台: ${recommendedPlatform}`);
console.log(`随机选择ComfyUI (可用实例: ${availableInstances}/${totalInstances})`);
```

### 2. **任务记录更新**
```javascript
platformInfo: {
  selectedAt: new Date(),
  reason: `智能选择基于ComfyUI实例可用性和工作流特性`,
  comfyuiInstancesStatus: context.comfyuiInstancesStatus
}
```

## API更新

### 1. **平台推荐API**
`POST /api/comfyui/platform-recommendation`

#### 响应示例：
```json
{
  "success": true,
  "data": {
    "workflowName": "A01-trending",
    "recommendedPlatform": "runninghub",
    "reason": "基于ComfyUI实例可用性和工作流特性的智能推荐",
    "comfyuiInstancesStatus": {
      "totalInstances": 3,
      "availableInstances": 0,
      "busyInstances": 3,
      "allInstancesBusy": true
    }
  }
}
```

### 2. **平台状态API**
通过`getPlatformStatus()`获取完整的平台状态信息，包括ComfyUI实例状态。

## 配置保持

### 1. **保留的配置项**
- 工作流类型规则（`workflowTypeRules`）
- 负载均衡配置（`loadBalancing`）
- 平台优先级（`platformPriority`）
- 默认平台（`defaultPlatform`）

### 2. **移除的用户规则影响**
- 不再检查用户VIP状态
- 不再区分付费/免费用户
- 所有用户享受相同的平台选择策略

## 优势总结

### 1. **公平性**
- 所有用户享受相同的平台选择策略
- 不再基于用户等级区别对待

### 2. **智能性**
- 实时检查ComfyUI实例状态
- 基于实际资源可用性做决策

### 3. **负载均衡**
- 随机分配减少单一平台压力
- 自动切换确保服务可用性

### 4. **透明性**
- 详细的选择日志
- 清晰的决策原因记录

### 5. **可靠性**
- 容错机制处理状态获取失败
- 多层后备选择策略

## 测试建议

### 1. **功能测试**
- 测试ComfyUI实例全部繁忙时的自动切换
- 测试有可用实例时的随机选择
- 测试强制规则的优先级

### 2. **负载测试**
- 模拟高并发请求
- 验证负载均衡效果
- 检查实例状态检查的性能影响

### 3. **边界测试**
- 测试无可用实例的情况
- 测试实例状态获取失败的处理
- 测试不支持的工作流类型

通过这次优化，平台选择逻辑变得更加智能和公平，能够根据实际的资源状况动态调整，为所有用户提供最佳的服务体验。
