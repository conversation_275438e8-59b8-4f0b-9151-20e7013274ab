/**
 * 任务工厂模块
 * 提供统一的任务创建模板和工厂函数
 */

import { generateId, ID_TYPES } from '../../utils/idGenerator';
import { getCurrentUserId } from '../../api/request';

/**
 * 基础任务模板
 * 所有任务的通用结构
 */
export const baseTaskTemplate = {
  taskId: '', // 任务ID (唯一标识符)
  userId: '', // 用户ID
  status: 'processing', // 任务状态
  taskType: '', // 任务类型
  pageType: '', // 页面类型
  imageType: 'original', // 图片类型，默认为original
  createdAt: null, // 创建时间
  // 组件结构
  components: {}
};

/**
 * 创建任务数据对象
 * @param {string} pageType - 页面类型，如'matting', 'background'等
 * @param {string} taskType - 任务类型，如'mattingbg', 'background'等
 * @param {Object} baseData - 基础数据，包含userId, components等通用信息
 * @param {Object} pageSpecificData - 页面特定的数据
 * @returns {Object} 创建的任务数据对象
 */
export const createTaskData = (pageType, taskType, baseData, pageSpecificData = {}) => {
  // 确保基本数据不为空
  if (!baseData) {
    baseData = {};
  }

  // 获取用户ID，优先使用传入的，其次使用当前登录的
  const userId = baseData.userId || getCurrentUserId() || 'developer';
  
  // 确保有一个任务ID - 只使用taskId，如果没有则生成新的
  const taskId = baseData.taskId || generateId(ID_TYPES.TASK);
  
  // 检查任务是否需要图片
  // 优先使用baseData中的明确设置，否则根据pageType和taskType判断
  const requiresImage = baseData.requiresImage !== undefined ? 
    !!baseData.requiresImage : 
    !isImagelessTaskType(pageType, taskType);
  
  // 处理组件数据
  const components = baseData.components || {};
  
  // 创建任务对象
  let taskData = {
    ...baseTaskTemplate,
    taskId,
    userId,
    taskType,
    pageType,
    imageType: baseData.imageType || 'original',
    createdAt: new Date(),
    status: baseData.status || 'processing',
    requiresImage,    // 明确标记是否需要图片
    components,
    // 如果有生成图片列表，则包含
    ...(baseData.generatedImages ? { generatedImages: baseData.generatedImages } : {})
  };
  
  // 确保不包含id字段，特别是空的id
  if ('id' in taskData && (taskData.id === null || taskData.id === undefined)) {
    console.log('移除空的id字段，防止MongoDB索引冲突');
    delete taskData.id;
  }
  
  return taskData;
};

/**
 * 判断任务类型是否不需要图片上传
 * @param {string} pageType - 页面类型
 * @param {string} taskType - 任务类型
 * @returns {boolean} 是否不需要图片
 */
export const isImagelessTaskType = (pageType, taskType) => {
  // 这里列出所有不需要图片上传的任务类型
  const imagelessPageTypes = [
    'textToImage', // 文生图
    'chat',        // 聊天功能
    'settings',    // 设置页面
    'dashboard'    // 仪表盘
  ];
  
  const imagelessTaskTypes = [
    'text2img',    // 文生图任务
    'chat',        // 聊天任务
    'settings',    // 设置相关任务
    'system'       // 系统相关任务
  ];
  
  return imagelessPageTypes.includes(pageType) || imagelessTaskTypes.includes(taskType);
};

/**
 * 创建图片组件
 * @param {string} componentType - 组件类型，如'sourceImagePanel', 'clothingPanel'等
 * @param {Object} data - 组件数据
 * @param {boolean} isMainImage - 是否为主图片
 * @returns {Object} 组件对象
 */
export const createImageComponent = (componentType, data = {}, isMainImage = false) => {
  // 使用已有componentId或生成新ID
  const componentId = data.componentId || `${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  
  // 只使用显式提供的serverFileName，不再使用ID作为备选
  const serverFileName = data.serverFileName || '';
  
  return {
    componentType,  // 使用传入的标准componentType
    componentId,    // 仅保留componentId作为组件标识
    isMainImage,
    name: data.name || componentType,
    serverFileName, // 明确设置serverFileName字段
    url: data.url || '',
    fileInfo: data.fileInfo || {
      width: 0,
      height: 0,
      size: 0,
      type: '',
      serverFileName // 确保在fileInfo中也设置serverFileName
    }
  };
};

/**
 * 创建非图片组件
 * @param {string} componentType - 组件类型，如'randomSeedSelector', 'quantityPanel'等
 * @param {Object} data - 组件数据
 * @returns {Object} 组件对象
 */
export const createComponent = (componentType, data = {}) => {
  // 使用已有componentId或生成新ID
  const componentId = data.componentId || `${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  
  const component = {
    componentType,  // 使用传入的标准componentType
    componentId,
    name: data.name || componentType
  };
  
  // 复制其他属性，但不复制type字段
  for (const key in data) {
    if (key !== 'componentId' && key !== 'type') {
      component[key] = data[key];
    }
  }
  
  return component;
};

/**
 * 设置主图片
 * @param {Object} components - 组件映射对象
 * @param {string} componentKey - 要设置为主图片的组件键
 * @returns {Object} 更新后的组件映射对象
 */
export const setMainImageSource = (components, componentKey) => {
  const updatedComponents = { ...components };
  
  // 先将所有组件的isMainImage设为false
  Object.keys(updatedComponents).forEach(key => {
    if (updatedComponents[key] && updatedComponents[key].isMainImage) {
      updatedComponents[key] = { ...updatedComponents[key], isMainImage: false };
    }
  });
  
  // 将指定的组件设为主图片
  if (updatedComponents[componentKey]) {
    updatedComponents[componentKey] = { ...updatedComponents[componentKey], isMainImage: true };
  }
  
  return updatedComponents;
};

// 任务工厂对象
export const taskFactory = {
  baseTaskTemplate,
  createTaskData,
  createImageComponent,
  createComponent,
  setMainImageSource
};

export default taskFactory; 