# 任务生成逻辑统一化改造指南

## 一、概述

本指南详细说明如何将自动抠图页面中的任务生成逻辑应用到其他页面，确保整个应用使用统一的任务创建、监控和结果处理流程。**注意：所有页面已移除兼容设计，统一使用标准化实现。**

## 二、核心模块与框架

### 1. 关键文件结构

```
src/
├── services/
│   └── task/
│       ├── index.js       - 任务服务主模块
│       └── taskFactory.js - 任务数据结构工厂
├── utils/
│   └── taskAdapters.js    - 任务数据适配工具
├── api/
│   └── task.js            - 任务相关API
├── pages/
│   └── tools/
│       └── matting/
│           └── index.jsx  - 自动抠图页面(参考实现)
```

### 2. 核心服务与组件

- **taskService**: 任务创建和管理服务 (通用服务)
- **taskFactory**: 标准化任务数据结构工厂 (通用工具)
- **taskAdapters**: 任务数据格式转换和验证工具 (通用工具)
- **TaskPanel**: 任务显示组件 (通用组件)
- **ImageDetailsModal**: 图片详情弹窗组件 (通用组件)，用于查看任务生成图片的详细信息、进行放大/缩小操作和下载

## 三、核心数据结构

### 1. 标准任务结构

> **注意**: 这是所有页面共用的核心任务数据结构，使用数组格式的组件结构。

```javascript
{
  taskId: 'TASK_123456',        // 任务唯一标识符
  userId: 'user123',            // 用户ID
  status: 'processing',         // 任务状态：processing, completed, failed
  taskType: 'ocbg',             // 任务类型：根据功能定义
  pageType: 'matting',          // 页面类型：matting, background等
  imageType: 'original',        // 图片类型
  createdAt: Date,              // 创建时间
  
  // 标准格式：使用组件数组
  components: [
    {
      componentType: 'sourceImagePanel',  // 组件类型
      componentId: 'comp_123',           // 组件ID
      serverFileName: 'file_123.jpg',    // 服务器文件名
      name: '原始图片 #1',                // 组件名称
      status: 'completed',               // 组件状态
      url: '/path/to/image.jpg',         // 图片URL
      originalImage: '/path/to/original.jpg', // 原始图片
      fileInfo: { width: 800, height: 600 },  // 文件信息
      isMainImage: true                  // 是否主图片
    }
    // 可以包含多个组件
  ],
  
  // 生成的图片结果
  generatedImages: [
    {
      imageIndex: 0,              // 图片索引
      status: 'completed',        // 状态
      progress: 100,              // 进度
      url: '/path/to/result.jpg'  // 结果URL
    }
  ],
  
  // 进度信息
  progress: {
    step: 1,               // 当前步骤
    total: 2,              // 总步骤数
    percentage: 50,        // 进度百分比
    text: '处理中...'       // 进度文本
  }
}
```

## 四、改造步骤详解

### 步骤一：引入核心服务和工具

> **通用步骤**: 所有页面都需要引入这些核心模块。

在页面中引入以下核心模块：

```javascript
import { createPageTask } from '../../../services/task';
import { taskAdapters } from '../../../utils/taskAdapters';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import { getCurrentUserId } from '../../../api';
import ImageDetailsModal from '../../../components/ImageDetailsModal'; // 图片详情弹窗组件
```

### 步骤二：修改任务创建逻辑

> **通用模板**: 以下是通用的任务创建逻辑模板，各页面需要根据自身特点进行适配。

参照以下模板改造现有的任务生成函数：

```javascript
const handleGenerate = async () => {
  // 1. 获取当前活动面板
  const activePanels = getActivePanels();
  
  // 2. 验证输入
  if (activePanels.length === 0) {
    message.error('请先上传图片');
    return;
  }
  
  // 3. 创建任务ID和获取用户ID
  const taskId = generateId(ID_TYPES.TASK);
  const userId = getCurrentUserId() || 'developer';
  
  // 4. 准备本地任务状态
  const taskData = {
    taskId: taskId,
    userId: userId,
    createdAt: new Date(),
    status: 'processing',
    pageType: '当前页面类型', // 如'background', 'try-on'等
    
    // 5. 使用标准组件数组结构
    components: activePanels.map((panel, index) => ({
      componentType: 'sourceImagePanel',
      componentId: panel.id || `${Date.now()}_${index}`,
      serverFileName: panel.serverFileName,
      name: `原始图片 #${index + 1}`,
      status: 'completed',
      url: panel.url || panel.processedFile,
      originalImage: panel.originalImage || panel.url,
      fileInfo: panel.fileInfo || {},
      isMainImage: index === 0
    })),
    
    // 6. 创建结果占位
    generatedImages: activePanels.map((_, index) => ({
      imageIndex: index,
      status: 'processing',
      progress: 0
    })),
    
    // 7. 添加必要的统计信息
    imageCount: activePanels.length,
    progress: {
      step: 0,
      total: 2,
      percentage: 0,
      text: '初始化中...'
    }
  };
  
  // 8. 添加到本地状态
  setGenerationTasks(prev => [taskData, ...prev]);
  
  try {
    // 9. 准备页面特定参数
    const pageSpecificData = {
      generation: {
        count: activePanels.length,
        prompt: '页面特定的处理提示'
      }
      // 可以添加其他页面特定参数
    };
    
    // 10. 调用任务创建服务
    const result = await createPageTask(
      '页面类型',            // 如'matting', 'background'
      '工作流类型',          // 如'ocbg', 'background'
      {
        taskId: taskId,
        userId: userId,
        imageQuantity: activePanels.length,
        panels: activePanels.map(panel => ({
          ...panel,
          type: 'sourceImage'
        })),
        status: 'processing',
        components: taskData.components
      },
      pageSpecificData
    );
    
    // 11. 处理API返回
    console.log('API返回结果:', result);
    
    // 12. 处理直接返回的结果
    const processedResults = handleDirectResponse(result);
    if (processedResults && processedResults.length > 0) {
      updateTaskWithResults(taskId, result, processedResults);
    } else {
      // 13. 开始异步检查
      startAsyncCheck(taskId, result);
    }
  } catch (error) {
    handleTaskError(taskId, error);
  }
};
```

### 步骤三：实现结果处理函数

> **通用处理函数**: 这些函数提供了通用的结果处理逻辑，可以被所有页面复用。

```javascript
// 处理API直接返回的结果
const handleDirectResponse = (result) => {
  if (!result) return null;
  
  // 处理标准results数组格式
  if (result.results && Array.isArray(result.results) && result.results.length > 0) {
    return result.results
      .filter(item => item && item.url)
      .map((item, index) => ({
        url: formatImageUrl(item.url),
        imageIndex: index,
        status: 'completed',
        progress: 100
      }));
  }
  
  return null;
};

// 更新任务结果
const updateTaskWithResults = (taskId, apiResult, processedResults) => {
  setGenerationTasks(prev => 
    prev.map(task => {
      if (task.taskId === taskId) {
        return {
          ...task,
          status: 'completed',
          processInfo: apiResult || {},
          generatedImages: processedResults,
          progress: {
            step: 2,
            total: 2,
            percentage: 100,
            text: '处理完成'
          }
        };
      }
      return task;
    })
  );
  
  setIsProcessing(false);
  message.success(`成功生成 ${processedResults.length} 张图片`);
};

// 启动异步检查
const startAsyncCheck = (taskId, apiResult) => {
  setGenerationTasks(prev => 
    prev.map(task => {
      if (task.taskId === taskId) {
        return {
          ...task,
          processInfo: apiResult || {},
          progress: {
            step: 1,
            total: 2,
            percentage: 20,
            text: '处理中...'
          }
        };
      }
      return task;
    })
  );
  
  setTimeout(() => checkImageResults(taskId), 2000);
};

// 处理任务错误
const handleTaskError = (taskId, error) => {
  console.error('API调用失败:', error);
  
  setGenerationTasks(prev => 
    prev.map(task => {
      if (task.taskId === taskId) {
        return {
          ...task,
          status: 'failed',
          errorMessage: error.message || '未知错误'
        };
      }
      return task;
    })
  );
  
  setIsProcessing(false);
  message.error('创建任务失败: ' + (error.message || '未知错误'));
};
```

### 步骤四：实现任务结果检查逻辑

> **通用检查逻辑**: 这些函数提供了通用的任务结果检查逻辑，可以被所有页面复用。

```javascript
// 检查任务结果
const checkImageResults = async (taskId) => {
  try {
    console.log(`检查任务结果: ${taskId}`);
    
    // 在本地状态中找到对应任务
    const taskToCheck = generationTasks.find(t => t.taskId === taskId);
    if (!taskToCheck) {
      console.error(`未找到任务: ${taskId}`);
      return;
    }
    
    // 获取任务详情
    const taskResult = await getTaskById(taskId);
    console.log('任务检查结果:', taskResult);
    
    if (!taskResult) {
      // 如果没有找到任务，继续检查
      scheduleNextCheck(taskId, taskToCheck.checkCount);
      return;
    }
    
    // 处理任务状态
    if (taskResult.status === 'failed') {
      handleTaskFailed(taskId, taskResult);
    } else if (taskResult.status === 'completed') {
      handleTaskCompleted(taskId, taskResult);
    } else {
      // 任务仍在处理中，更新进度
      updateTaskProgress(taskId, taskResult);
      scheduleNextCheck(taskId, taskToCheck.checkCount);
    }
  } catch (error) {
    console.error('检查任务出错:', error);
    scheduleNextCheck(taskId, taskToCheck?.checkCount);
  }
};

// 安排下一次检查
const scheduleNextCheck = (taskId, currentCheckCount = 0) => {
  const checkCount = currentCheckCount + 1;
  
  // 更新检查次数
  setGenerationTasks(prev => 
    prev.map(task => {
      if (task.taskId === taskId) {
        return { ...task, checkCount };
      }
      return task;
    })
  );
  
  // 计算下一次检查的延迟
  const delay = Math.min(2000 + checkCount * 500, 10000);
  
  // 限制最大检查次数
  if (checkCount < 30) {
    setTimeout(() => checkImageResults(taskId), delay);
  } else {
    // 超过最大检查次数，标记为失败
    setGenerationTasks(prev => 
      prev.map(task => {
        if (task.taskId === taskId) {
          return {
            ...task,
            status: 'failed',
            errorMessage: '任务检查超时'
          };
        }
        return task;
      })
    );
  }
};
```

### 步骤五：实现辅助函数

> **通用辅助函数**: 可以复用的工具函数，需要根据页面特性进行适当调整。

```javascript
// 获取活动面板
const getActivePanels = () => {
  // 根据页面类型返回对应的面板数组
  // 例如：自动抠图页面返回sourcePanels
  return sourcePanels;
};

// 格式化图片URL
const formatImageUrl = (url) => {
  if (!url) return null;
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http')) return url;
  
  // 如果是相对路径，构建完整URL
  const baseUrl = process.env.REACT_APP_BACKEND_URL;
  
  // 确保路径始终以斜杠开头
  const path = url.startsWith('/') ? url : `/${url}`;
  
  return `${baseUrl}${path}`;
};

// 从任务中提取图片
const extractImagesFromTask = (task) => {
  if (!task) return [];
  
  // 处理标准化的generatedImages数组
  if (task.generatedImages && Array.isArray(task.generatedImages)) {
    return task.generatedImages
      .filter(img => img && img.url)
      .map(img => ({
        url: formatImageUrl(img.url),
        imageIndex: img.imageIndex || 0,
        status: 'completed',
        progress: 100
      }));
  }
  
  return [];
};
```

## 五、各页面特殊处理要点

> **重要**：每个页面都有自己特有的处理逻辑，应该根据具体页面功能进行分析和改造。

### 1. 自动抠图页面参考实现

自动抠图页面有以下特殊处理，可作为实现参考：

```javascript
// 1. 工作流类型选择逻辑 - 自动抠图页面特有
const workflowType = activePanelTab === 'operation' 
  ? (activePanels.length > 1 ? 'mattingbgfile' : 'mattingbg') 
  : (activePanels.length > 1 ? 'mattingclofile' : 'mattingclo');

// 2. 特有的页面特定参数
const pageSpecificData = {
  generation: {
    count: activePanels.length,
    prompt: workflowType === 'mattingclo' ? '将图片进行衣物抠图处理' : '将图片进行背景去除处理'
  }
};
```

### 2. 页面特殊处理的一般原则

在为其他页面实现任务生成逻辑时，需要考虑以下几个方面：

1. **页面特有的工作流类型**：根据页面功能确定适当的任务类型
2. **特殊的参数处理**：每个页面可能需要传递特定参数给API
3. **组件结构差异**：不同页面的组件结构可能不同，需针对性处理
4. **结果展示方式**：根据生成结果的特点选择合适的展示方式

### 3. 页面特殊处理的实现方法

在实际改造过程中，建议以下步骤：

1. **分析页面需求**：理解页面特定的功能和任务处理需求
2. **确定核心参数**：识别必要的业务参数和页面特有配置
3. **适配任务结构**：修改通用模板以适应页面特殊需求
4. **测试验证**：确保任务创建、监控和结果处理符合预期

## 六、通用组件与页面特有组件

### 1. 通用任务组件

以下组件可在所有页面中通用：

- **TaskPanel**: 显示任务信息和状态
- **SourceImagePanel**: 显示源图片
- **GenerationArea**: 显示生成结果区域
- **ImageDetailsModal**: 图片详情查看弹窗，提供以下功能：
  - 高清大图预览
  - 图片缩放和平移控制
  - 图片下载功能
  - 图片元数据显示（尺寸、格式等）
  - 处理特殊格式图片（如透明背景图片）

### 2. 页面特有组件

不同页面会有特有的组件，这些组件应该基于实际页面分析进行设计和实现。在改造时应当：

1. 分析当前页面使用的特殊组件
2. 评估是否可以复用现有组件
3. 必要时开发新的页面特有组件
4. 确保与通用任务处理逻辑良好集成

## 七、任务显示与结果展示

> **通用展示组件**: 以下是通用的任务展示组件，可以被所有页面复用。

### 1. 使用TaskPanel组件

```jsx
// 任务列表渲染
<div className="task-list">
  {generationTasks.map(task => (
    <TaskPanel
      key={task.taskId}
      task={task}
      onDelete={() => handleDeleteTask(task.taskId)}
      onDownload={() => handleBatchDownload(task)}
      onEdit={() => handleEditTask(task)}
    />
  ))}
</div>
```

### 2. 结果展示

```jsx
// 生成结果展示
<div className="generation-results">
  {currentTask && currentTask.generatedImages && (
    <div className="result-images">
      {currentTask.generatedImages.map((image, index) => (
        <div key={`result-${index}`} className="result-image-item">
          <img 
            src={image.url} 
            alt={`生成结果 ${index + 1}`}
            onClick={() => handleImageClick(image, index)}
          />
          <div className="image-controls">
            <Button 
              icon={<DownloadOutlined />} 
              onClick={() => handleDownloadImage(image.url, currentTask.taskId, index)}
            />
          </div>
        </div>
      ))}
    </div>
  )}
</div>
```

### 3. 图片详情弹窗

> **通用组件实现**: 图片详情弹窗在所有页面中的实现方式基本相同。

```jsx
// 状态定义
const [selectedImage, setSelectedImage] = useState(null);
const [isImageDetailsVisible, setIsImageDetailsVisible] = useState(false);

// 处理图片点击，打开详情弹窗
const handleImageClick = (image, index) => {
  setSelectedImage({
    ...image,
    index,
    taskId: currentTask.taskId
  });
  setIsImageDetailsVisible(true);
};

// 关闭图片详情弹窗
const handleCloseImageDetails = () => {
  setIsImageDetailsVisible(false);
};

// 组件渲染部分
{isImageDetailsVisible && selectedImage && (
  <ImageDetailsModal
    visible={isImageDetailsVisible}
    imageUrl={selectedImage.url}
    onClose={handleCloseImageDetails}
    onDownload={() => handleDownloadImage(selectedImage.url, selectedImage.taskId, selectedImage.index)}
    title={`生成结果 #${selectedImage.index + 1}`}
    taskId={selectedImage.taskId}
    imageIndex={selectedImage.index}
    // 可选：传递页面特有的渲染选项
    renderOptions={{
      showCheckboard: pageType === 'matting', // 抠图页面显示棋盘格背景
      showMetadata: true,                     // 显示元数据
      allowZoom: true                         // 允许缩放
    }}
  />
)}
```

## 八、旧代码迁移策略

### 1. 修改任务创建逻辑

1. 找到当前页面中的任务创建函数(通常命名为`handleGenerate`、`createTask`等)
2. 使用本指南中的模板替换原有实现
3. 保留页面特有的参数处理逻辑

### 2. 替换任务监控逻辑

1. 找到当前页面中的任务状态检查函数
2. 使用本指南中的`checkImageResults`函数替换原有实现
3. 调整回调函数适应页面特有需求

### 3. 更新UI组件引用

1. 使用统一的`TaskPanel`组件替换自定义任务显示组件
2. 确保结果展示组件使用标准的任务数据结构

## 九、测试与验证

### 1. 验证检查项

- 任务创建：验证任务是否成功创建并提交
- 状态监控：验证任务状态是否正确更新
- 结果展示：验证生成结果是否正确显示
- 错误处理：验证错误情况下的处理逻辑
- 兼容性：验证与其他页面和组件的兼容性

### 2. 调试提示

在开发过程中，可以添加以下调试代码：

```javascript
// 调试任务创建参数
console.log('任务创建参数:', {
  pageType: 'pageType',
  taskType: 'taskType',
  baseData: {
    taskId: taskId,
    panels: activePanels,
    components: components // 标准数组格式的组件
  },
  pageSpecificData
});
```

## 十、常见问题与解决方案

### 1. 任务创建失败

- 检查组件数据结构是否符合API要求
- 确保所有面板都有有效的`serverFileName`
- 验证用户ID是否正确设置

### 2. 结果无法显示

- 检查URL格式是否正确
- 使用`formatImageUrl`函数统一处理图片URL
- 确认API返回的结果格式是否符合标准

### 3. 任务状态未更新

- 检查`checkImageResults`函数的调用时机
- 确保任务ID正确传递
- 验证状态更新逻辑是否正确

## 十一、功能共性与差异

下表列出了任务生成逻辑中的共性与差异：

| 功能/组件 | 是否通用 | 说明 |
|----------|---------|------|
| 任务数据结构 | 通用 | 所有页面使用相同的核心任务数据结构，组件使用数组格式 |
| 任务创建基础流程 | 通用 | 创建ID、准备数据、调用API的基本流程相同 |
| 任务状态检查 | 通用 | 轮询检查任务状态的逻辑可复用 |
| 结果处理基础逻辑 | 通用 | 处理API返回结果的基本逻辑相同 |
| TaskPanel组件 | 通用 | 任务展示面板可在所有页面复用 |
| ImageDetailsModal | 通用，配置不同 | 图片详情弹窗通用，但配置可能因页面而异 |
| 工作流类型选择 | 页面特有 | 每个页面有自己特定的工作流类型 |
| 页面特定参数 | 页面特有 | 不同页面需要传递不同的特定参数 |
| 结果展示方式 | 基础共用，细节特有 | 基本展示逻辑相同，但可能有页面特定展示效果 |

## 十二、示例实现

参考自动抠图页面中的以下关键函数：

1. `handleGenerate`: 任务创建逻辑 (可复用核心流程，但需替换页面特有部分)
2. `handleDirectResponse`: 直接结果处理 (可直接复用)
3. `checkImageResults`: 任务结果检查 (可直接复用)
4. `extractImagesFromTask`: 标准化图片结果提取 (可直接复用)
5. `handleImageClick`: 图片详情显示 (可复用基本逻辑，需调整页面特有部分)

在改造过程中，可以直接参考这些函数的实现，并根据页面特性进行适当调整。

---

通过遵循本指南，你可以将自动抠图页面中的任务生成逻辑应用到其他页面，实现整个应用的任务处理流程统一化。具体到每个页面的实现细节，建议在实际改造过程中根据页面功能和特点进行针对性分析和开发。 