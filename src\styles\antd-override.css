/**
 * Ant Design 样式覆盖
 * 确保Ant Design组件使用一致的字体样式
 */

/* 覆盖所有Ant Design组件的字体 */
.ant-btn,
.ant-input,
.ant-select,
.ant-checkbox,
.ant-radio,
.ant-modal,
.ant-modal-content,
.ant-modal-header,
.ant-modal-title,
.ant-modal-body,
.ant-modal-footer,
.ant-message,
.ant-message-notice,
.ant-tooltip,
.ant-tooltip-inner,
.ant-dropdown,
.ant-dropdown-menu,
.ant-dropdown-menu-item,
.ant-menu,
.ant-menu-item,
.ant-tabs,
.ant-tabs-tab,
.ant-form,
.ant-form-item,
.ant-form-item-label > label,
.ant-alert,
.ant-notification,
.ant-progress-text,
.ant-badge,
.ant-tag,
.ant-card,
.ant-card-head,
.ant-card-head-title,
.ant-table,
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, <PERSON><PERSON>, sans-serif !important;
}

/* 覆盖Ant Design中文本相关组件 */
.ant-typography,
.ant-typography h1,
.ant-typography h2,
.ant-typography h3,
.ant-typography h4,
.ant-typography h5,
.ant-typography h6,
.ant-typography p,
.ant-typography span {
  font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif !important;
}

/* 确保弹窗和提示性组件也应用相同字体 */
.ant-message-custom-content,
.ant-notification-notice-message,
.ant-notification-notice-description,
.ant-modal-confirm-title,
.ant-modal-confirm-content {
  font-family: "Microsoft YaHei", "微软雅黑", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif !important;
}

/* 确保确认弹窗内容显示在最上层 */
.ant-modal-confirm .ant-modal-content,
.ant-modal-confirm .ant-modal-body,
.ant-modal-confirm .ant-modal-footer {
  z-index: 1000001 !important; /* 确保确认弹窗内容在遮罩层之上 */
}

/* 确保确认弹窗显示在最上层 */
.global-confirm-modal,
.ant-modal-confirm,
.ant-modal-confirm .ant-modal,
.ant-modal-confirm .ant-modal-mask,
.ant-modal-confirm .ant-modal-wrap {
  z-index: 1000000 !important; /* 大幅提高z-index，确保在所有弹窗之上 */
}

/* 统一确认弹窗按钮样式 */
.global-confirm-modal .ant-btn-primary,
.ant-modal-confirm .ant-btn-primary {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: #fff !important;
}

.global-confirm-modal .ant-btn-primary:hover,
.ant-modal-confirm .ant-btn-primary:hover {
  background-color: #ff7875 !important;
  border-color: #ff7875 !important;
  color: #fff !important;
}

.global-confirm-modal .ant-btn-primary:active,
.ant-modal-confirm .ant-btn-primary:active {
  background-color: #d9363e !important;
  border-color: #d9363e !important;
  color: #fff !important;
}

/* 取消按钮样式 */
.global-confirm-modal .ant-btn-default,
.ant-modal-confirm .ant-btn-default {
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

.global-confirm-modal .ant-btn-default:hover,
.ant-modal-confirm .ant-btn-default:hover {
  border-color: #40a9ff !important;
  color: #40a9ff !important;
}

/* 暗色主题下的确认弹窗样式 */
[data-theme="dark"] .ant-modal-confirm .ant-modal-content {
  background: #1f1f1f !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .ant-modal-confirm-title {
  color: rgba(255, 255, 255, 0.85) !important;
}

[data-theme="dark"] .ant-modal-confirm-content {
  color: rgba(255, 255, 255, 0.65) !important;
}

[data-theme="dark"] .ant-modal-confirm .ant-btn-default {
  background: #1f1f1f !important;
  border-color: #434343 !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

[data-theme="dark"] .ant-modal-confirm .ant-btn-default:hover {
  border-color: #40a9ff !important;
  color: #40a9ff !important;
}

[data-theme="dark"] .ant-modal-confirm .ant-btn-primary {
  background: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: #fff !important;
}

[data-theme="dark"] .ant-modal-confirm .ant-btn-primary:hover {
  background: #ff7875 !important;
  border-color: #ff7875 !important;
  color: #fff !important;
}

/* 移除了全局日期选择器样式，使用页面特定样式避免冲突 */

/* 分页器双箭头按钮和省略号样式全局覆盖 */
body .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon,
body .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon {
  color: var(--brand-primary) !important;
  transform: translateY(-2px) !important;
  display: inline-flex !important;
  align-items: center !important;
}

/* 调整省略号位置 */
body .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
body .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
  transform: translateY(-2px) !important;
  display: inline-flex !important;
  align-items: center !important;
}

/* 确保双箭头按钮在hover和active状态下也使用主题色 */
body .ant-pagination-jump-prev:hover .ant-pagination-item-container .ant-pagination-item-link-icon,
body .ant-pagination-jump-next:hover .ant-pagination-item-container .ant-pagination-item-link-icon,
body .ant-pagination-jump-prev:focus .ant-pagination-item-container .ant-pagination-item-link-icon,
body .ant-pagination-jump-next:focus .ant-pagination-item-container .ant-pagination-item-link-icon {
  color: var(--brand-primary) !important;
}

/* 确保SVG图标也使用主题色并对齐 */
body .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon svg,
body .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon svg {
  fill: var(--brand-primary) !important;
  stroke: var(--brand-primary) !important;
  vertical-align: middle !important;
} 