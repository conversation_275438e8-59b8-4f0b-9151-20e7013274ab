const CaptchaService = require('./captcha.service');

class CaptchaController {
  // 获取验证码
  static async getCaptcha(req, res) {
    try {
      const result = await CaptchaService.getCaptcha();
      res.json({
        success: true,
        data: {
          captcha: result.captcha,
          sessionId: result.sessionId
        }
      });
    } catch (error) {
      console.error('获取验证码错误:', error);
      res.status(500).json({
        success: false,
        message: error.message || '获取验证码失败'
      });
    }
  }

  // 验证验证码
  static async verifyCode(req, res) {
    try {
      const { sessionId, code } = req.body;

      if (!sessionId || !code) {
        return res.status(400).json({
          success: false,
          message: '请提供完整的验证信息'
        });
      }

      const result = await CaptchaService.verifyCaptcha(sessionId, code);
      
      res.json({
        success: true,
        message: '验证成功'
      });
    } catch (error) {
      console.error('验证码验证错误:', error);
      res.status(400).json({
        success: false,
        message: error.message || '验证失败'
      });
    }
  }
}

module.exports = CaptchaController; 