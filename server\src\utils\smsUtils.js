const Dysmsapi20170525 = require('@alicloud/dysmsapi20170525');
const OpenApi = require('@alicloud/openapi-client');
const Util = require('@alicloud/tea-util');
const Config = require('config');
// const redis = require('./redisUtils');
class AliyunSmsUtil {
  constructor() {
    // 从配置中读取阿里云短信配置
    this.config = {
      accessKeyId: Config.get('aliyunSms.accessKeyId'),
      accessKeySecret: Config.get('aliyunSms.accessKeySecret'),
      endpoint: Config.get('aliyunSms.endpoint') || 'dysmsapi.aliyuncs.com',
      signName: Config.get('aliyunSms.signName'),
      regionId: Config.get('aliyunSms.regionId') || 'cn-hangzhou',
    };

    // 初始化客户端
    this.client = this.createClient();
  }

  /**
   * 创建阿里云短信客户端
   */
  createClient() {
    const { accessKeyId, accessKeySecret, endpoint, regionId } = this.config;
    
    const config = new OpenApi.Config({
      accessKeyId,
      accessKeySecret,
      regionId,
      endpoint,
    });
    return new Dysmsapi20170525.default(config);
  }

  /**
   * 发送短信
   * @param {string} phoneNumbers 手机号，多个用逗号分隔
   * @param {string} templateCode 模板CODE
   * @param {object} templateParam 模板参数对象
   * @param {string} signName 签名名称（可选，不传使用配置的默认签名）
   * @returns {Promise<object>} 发送结果
   */
  async sendSms(phoneNumbers, templateCode, templateParam) {
    const sendSmsRequest = new Dysmsapi20170525.SendSmsRequest({
      phoneNumbers,
      signName: this.config.signName,
      templateCode,
      templateParam: JSON.stringify(templateParam),
    });
    console.log({
      phoneNumbers,
      signName: this.config.signName,
      templateCode,
      templateParam: JSON.stringify(templateParam),
    });

    // 存入后端缓存,有过期时间
    const cacheKey = `sms_${phoneNumbers}_${templateCode}`;
    const cacheValue = {
      templateParam,
      createdAt: new Date(),
    };
    // await redis.set(cacheKey, JSON.stringify(cacheValue), 'EX', 60 * 5);  

    try {
      const runtime = new Util.RuntimeOptions({});
      const response = await this.client.sendSmsWithOptions(sendSmsRequest, runtime);
      console.log(response);
      return {
        success: response.body.code === 'OK',
        code: response.body.code,
        message: response.body.message,
        bizId: response.body.bizId,
        requestId: response.body.requestId,
      };
    } catch (error) {
      console.error('阿里云短信发送失败:', error);
      return {
        success: false,
        code: error.code,
        message: error.message,
      };
    }
  }

  /**
   * 批量发送短信
   * @param {Array<string>} phoneNumbers 手机号数组
   * @param {Array<object>} signNames 签名名称数组（与手机号对应）
   * @param {string} templateCode 模板CODE
   * @param {Array<object>} templateParams 模板参数数组（与手机号对应）
   * @returns {Promise<object>} 发送结果
   */
  async sendBatchSms(phoneNumbers, signNames, templateCode, templateParams) {
    const sendBatchSmsRequest = new Dysmsapi20170525.SendBatchSmsRequest({
      phoneNumberJson: JSON.stringify(phoneNumbers),
      signNameJson: JSON.stringify(signNames),
      templateCode,
      templateParamJson: JSON.stringify(templateParams.map(param => JSON.stringify(param))),
    });

    try {
      const runtime = new Util.RuntimeOptions({});
      const response = await this.client.sendBatchSmsWithOptions(sendBatchSmsRequest, runtime);
      return {
        success: response.body.code === 'OK',
        code: response.body.code,
        message: response.body.message,
        bizId: response.body.bizId,
        requestId: response.body.requestId,
      };
    } catch (error) {
      console.error('阿里云批量短信发送失败:', error);
      return {
        success: false,
        code: error.code,
        message: error.message,
      };
    }
  }

  // 验证验证码
  async verifyCode(phone, code) {
    const cacheKey = `sms_${phone}_${code}`;
    // const cacheValue = await redis.get(cacheKey);
    if (!cacheValue) {
      throw createError(400, '验证码不存在');
    }
    const { templateParam, createdAt } = JSON.parse(cacheValue);
    if (createdAt < Date.now() - 60 * 5 * 1000) {
      throw createError(400, '验证码已过期');
    }
  } 

  /**
   * 查询短信发送记录
   * @param {string} phoneNumber 手机号
   * @param {string} bizId 发送回执ID
   * @param {string} sendDate 发送日期，格式yyyyMMdd
   * @param {number} pageSize 分页大小
   * @param {number} currentPage 当前页码
   * @returns {Promise<object>} 查询结果
   */
  async querySendDetails(phoneNumber, bizId, sendDate, pageSize = 10, currentPage = 1) {
    const querySendDetailsRequest = new Dysmsapi20170525.QuerySendDetailsRequest({
      phoneNumber,
      bizId,
      sendDate,
      pageSize,
      currentPage,
    });

    try {
      const runtime = new Util.RuntimeOptions({});
      const response = await this.client.querySendDetailsWithOptions(querySendDetailsRequest, runtime);
      return {
        success: response.body.code === 'OK',
        code: response.body.code,
        message: response.body.message,
        totalCount: response.body.totalCount,
        smsSendDetailDTOs: response.body.smsSendDetailDTOs,
      };
    } catch (error) {
      console.error('阿里云短信记录查询失败:', error);
      return {
        success: false,
        code: error.code,
        message: error.message,
      };
    }
  }
}

// 单例模式导出
module.exports = new AliyunSmsUtil();