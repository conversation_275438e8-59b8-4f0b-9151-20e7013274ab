/* 认证模态窗口样式 */

/* 登录模态窗口 */
.login-modal {
  width: fit-content;
  min-width: 400px;
  max-width: 480px;
  background-color: var(--bg-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  animation: slideIn 0.3s ease;
  position: relative;
  margin: auto;
}

/* 添加登录模态框标题样式 */
.login-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 20px 20px 0;
  margin: 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  background: var(--bg-primary);
}

/* 登录模态窗口主体样式 */
.login-modal .modal-body {
  padding: 24px;
  padding-bottom: 20px;
}

/* 修改密码模态窗口特定样式 */
.reset-modal {
  width: fit-content;
  min-width: 400px;
  max-width: 420px;
  background-color: var(--bg-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  animation: slideIn 0.3s ease;
  position: relative;
  margin: auto;
}

.reset-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 20px 20px 0;
  margin: 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  background: var(--bg-primary);
}

.reset-modal .modal-body {
  padding: 24px;
  padding-bottom: 20px;
}

/* 验证码模态窗口特定样式 */
.captcha-modal {
  width: 400px;
  background: var(--bg-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  position: relative;
}

/* 添加验证码模态框标题样式 */
.captcha-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 20px 20px 0;
  margin: 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  background: var(--bg-primary);
}

.captcha-modal .modal-body {
  padding: 24px;
}

/* 动画效果 */
@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .login-modal {
    min-width: 320px;
    max-width: 90%;
  }
}

@media screen and (max-width: 480px) {
  .login-modal {
    min-width: 280px;
    width: 95%;
  }
  
  .modal-body {
    padding: 20px;
  }
}

/* 覆盖全局 .form-group 的 margin-bottom，仅影响弹窗表单 */
.login-modal .form-group,
.register-modal .form-group,
.reset-modal .form-group {
  margin-bottom: 10px !important;
}
.login-modal .form-group:last-child,
.register-modal .form-group:last-child,
.reset-modal .form-group:last-child {
  margin-bottom: 0 !important;
}

/* 修复登录注册弹窗拖动时手型光标 */
.login-modal.dragging .modal-header,
.register-modal.dragging .modal-header,
.reset-modal.dragging .modal-header,
.captcha-modal.dragging .modal-header {
  cursor: grabbing !important;
} 