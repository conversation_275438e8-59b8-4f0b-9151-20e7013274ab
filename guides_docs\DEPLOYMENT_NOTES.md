# 部署注意事项文档

## 目录

1. [图片访问安全性改进](#图片访问安全性改进)
2. [环境变量配置](#环境变量配置)
3. [数据库配置](#数据库配置)
4. [测试数据清理](#测试数据清理)

## 图片访问安全性改进

### 当前问题

目前图片访问逻辑在开发环境可行，但存在安全隐患，不适合生产环境部署：

1. **单张图片下载绕过验证**
   - 单张图片下载直接通过前端从静态资源URL获取，绕过了用户认证和权限校验
   - 任何人知道URL就能下载图片，无法进行用户权限验证
   - 用户可能会尝试猜测其他用户的文件路径，造成数据泄露

2. **不一致的访问机制**
   - 单张下载和批量下载使用不同的获取方式，维护困难
   - 批量下载通过后端API处理，有权限检查，但单张下载没有

3. **测试数据路径问题**
   - 生产环境不应该有`/images/test/`这样的测试数据路径
   - 所有图片应该存储在用户特定的目录中，通过授权API访问

### 改进方案

#### A. 统一图片访问API

1. **创建单张图片下载API**

```javascript
// 在server/src/routes/download.js中添加
router.get('/image', auth, async (req, res) => {
  try {
    const { url } = req.query;
    const userId = req.user._id.toString();
    
    // 验证用户权限并获取实际文件路径
    const filePath = mapUrlToUserFilePath(url, userId);
    
    // 验证文件存在并返回
    if (fs.existsSync(filePath)) {
      return res.sendFile(filePath);
    }
    return res.status(404).json({ success: false, message: '文件不存在' });
  } catch (error) {
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
});
```

2. **修改前端单张图片下载逻辑**

```javascript
const handleDownloadImage = async (imageUrl, taskId, index) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`/api/download/image?url=${encodeURIComponent(imageUrl)}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    const blob = await response.blob();
    // ... 保存逻辑不变
  } catch (error) {
    message.error('下载失败');
  }
};
```

#### B. 统一资源URL格式

1. **采用明确的路径结构**
   - 私有资源：`/user/{userId}/{resourceType}/{filename}`
   - 公共资源：`/public/{resourceType}/{filename}`

2. **移除所有硬编码的测试路径**
   - 清理前端代码中的测试数据URL
   - 改用API获取数据时动态构建正确URL

#### C. 资源访问控制中间件增强

扩展当前的fileAccessControl中间件：
- 添加用户权限严格验证
- 处理路径映射，支持旧格式向新格式的过渡

### 实施计划

1. **API实现**
   - 在download.js中添加单张图片下载API
   - 添加URL验证和权限检查逻辑

2. **配置环境变量**
   - 添加`STRICT_ACCESS_CONTROL`环境变量
   - 生产环境必须设为true

3. **前端改造**
   - 修改ClothingPanel、GenerationArea等组件
   - 修改单张下载的fetch逻辑，使用API替代直接URL

4. **测试确认**
   - 测试单张和批量下载
   - 测试不同用户间的隔离
   - 测试未授权访问的拒绝机制

### 优先级

**上线前必须完成**，关系到用户数据安全和隐私保护。

### 相关文件

- 前端下载逻辑：`src/pages/model/try-on/index.jsx`
- 后端下载API：`server/src/routes/download.js`
- 文件访问控制：`server/src/middleware/fileAccess.js`

## 环境变量配置

*待完善*

## 数据库配置

*待完善*

## 测试数据清理

*待完善* 