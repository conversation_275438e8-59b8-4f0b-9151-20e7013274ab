{"7": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 1024, "background_color": "#000000", "image": ["39", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "10": {"inputs": {"needInput": true, "seed": 560113912989748, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["46", 0], "positive": ["49", 0], "negative": ["50", 0], "latent_image": ["36", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "随机种子"}}, "11": {"inputs": {"samples": ["10", 0], "vae": ["44", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "20": {"inputs": {"delimiter": ", ", "clean_whitespace": "true", "text_a": ["41", 0], "text_b": ["57", 0], "text_c": ["56", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "36": {"inputs": {"needInput": true, "amount": 1, "samples": ["43", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "图片数量"}}, "37": {"inputs": {"filename_prefix": "Background", "images": ["53", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "38": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "背景图片上传"}}, "39": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "前景图片上传"}}, "41": {"inputs": {"String": "Keep all details of the character unchanged except the lighting.", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "String", "_meta": {"title": "String"}}, "42": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 1536, "background_color": "#000000", "image": ["38", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "43": {"inputs": {"pixels": ["7", 0], "vae": ["44", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "44": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "45": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn_scaled.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "46": {"inputs": {"unet_name": "flux1-kontext-dev.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "47": {"inputs": {"conditioning": ["48", 0], "latent": ["36", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "48": {"inputs": {"text": ["20", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["45", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "49": {"inputs": {"guidance": 2.5, "conditioning": ["47", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "50": {"inputs": {"conditioning": ["48", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "51": {"inputs": {"image": ["39", 0]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}, "52": {"inputs": {"model_name": "RealESRGAN_x2.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "53": {"inputs": {"width": ["51", 0], "height": ["51", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": "crop", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 1, "device": "cpu", "image": ["54", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "54": {"inputs": {"upscale_model": ["52", 0], "image": ["11", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "56": {"inputs": {"String": "and the character blend naturally into the background.", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "String", "_meta": {"title": "String"}}, "57": {"inputs": {"caption_type": "Descriptive", "caption_length": "medium-length", "max_new_tokens": 512, "top_p": 0.9, "top_k": 0, "temperature": 0.6, "user_prompt": "Just describe the background and lighting.", "speak_and_recognation": {"__value__": [false, true]}, "image": ["42", 0], "joycaption_beta1_model": ["58", 0]}, "class_type": "LayerUtility: JoyCaptionBeta1", "_meta": {"title": "LayerUtility: JoyCaption Beta One (Advance)"}}, "58": {"inputs": {"model": "fancyfeast/llama-joycaption-beta-one-hf-llava", "quantization_mode": "nf4", "device": "cuda"}, "class_type": "LayerUtility: LoadJoyCaptionBeta1Model", "_meta": {"title": "LayerUtility: Load JoyCaption Beta One Model (Advance)"}}}