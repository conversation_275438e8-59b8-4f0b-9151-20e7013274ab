const logger = require('./logger');

/**
 * 自定义API错误类
 */
class ApiError extends Error {
  constructor(message, statusCode) {
    super(message);
    
    // 确保状态码是有效的数字
    let validStatusCode = 500; // 默认服务器错误
    
    if (statusCode) {
      // 尝试将状态码转换为数字
      const parsedCode = parseInt(statusCode, 10);
      
      // 验证状态码是否在有效范围内
      if (!isNaN(parsedCode) && parsedCode >= 100 && parsedCode < 600) {
        validStatusCode = parsedCode;
      }
    }
    
    this.statusCode = validStatusCode;
    this.status = `${validStatusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 创建API错误实例
 * @param {number} statusCode HTTP状态码
 * @param {string} message 错误消息
 * @returns {ApiError} API错误实例
 */
const createError = (statusCode, message) => {
  return new ApiError(message, statusCode);
};

/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  // 确保状态码是有效的数字
  let statusCode = 500; // 默认服务器错误
  
  if (err.statusCode) {
    // 尝试将状态码转换为数字
    const parsedCode = parseInt(err.statusCode, 10);
    
    // 验证状态码是否在有效范围内
    if (!isNaN(parsedCode) && parsedCode >= 100 && parsedCode < 600) {
      statusCode = parsedCode;
    }
  }
  
  // 更安全：确保一定是数字
  statusCode = Number(statusCode);
  
  // 设置错误状态
  const status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
  
  // 记录错误日志
  logger.error(`[${req.method}] ${req.path}`, {
    statusCode: statusCode,
    message: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    requestBody: req.body,
    requestParams: req.params,
    requestQuery: req.query
  });

  // 开发环境返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    return res.status(statusCode).json({
      success: false,
      status: status,
      message: err.message,
      stack: err.stack,
      error: err
    });
  }

  // 生产环境返回简洁错误信息
  return res.status(statusCode).json({
    success: false,
    status: status,
    message: err.isOperational ? err.message : '服务器内部错误'
  });
};

const errorMessages = {
  CREDIT_BALANCE_NOT_FOUND: '未找到用户算力余额',
  CREDIT_TRANSACTION_FAILED: '算力交易记录查询失败',
  INVALID_DATE_RANGE: '无效的日期范围',
  INVALID_TRANSACTION_TYPE: '无效的交易类型'
};

exports.createError = (status, message, originalError = null) => {
  const error = new Error(message || errorMessages[message]);
  error.status = status;
  if (originalError) {
    error.originalError = originalError;
    error.stack = originalError.stack;
  }
  return error;
};

module.exports = {
  ApiError,
  createError,
  errorHandler
}; 