import React, { useState } from 'react';
import './styles.css';
import { message } from 'antd';

interface ContactSupportProps {
  className?: string;
}

const ContactSupport: React.FC<ContactSupportProps> = ({ className }) => {
  const [copySuccess, setCopySuccess] = useState({
    email: false,
    phone: false,
    wechat: false
  });
  const [showLargeQR, setShowLargeQR] = useState(false);
  const email = '<EMAIL>';
  const phone = '188 1095 1512';
  const wechat = 'Artahasy';

  const handleCopy = async (type: 'email' | 'phone' | 'wechat') => {
    try {
      await navigator.clipboard.writeText(
        type === 'email' ? email : 
        type === 'phone' ? phone : 
        wechat
      );
      setCopySuccess(prev => ({ ...prev, [type]: true }));
      message.success(
        type === 'email' ? '邮箱已复制' : 
        type === 'phone' ? '手机号已复制' : 
        '微信号已复制'
      );
      setTimeout(() => setCopySuccess(prev => ({ ...prev, [type]: false })), 2000);
    } catch (err) {
      console.error('复制失败:', err);
      message.error('复制失败，请手动复制');
    }
  };

  const handleCopyAddress = async () => {
    try {
      await navigator.clipboard.writeText('辽宁省兴城市斯达威泳装超级产业园斯达威网科大厦11层');
      setCopySuccess(prev => ({ ...prev, address: true }));
      message.success('地址已复制');
      setTimeout(() => setCopySuccess(prev => ({ ...prev, address: false })), 2000);
    } catch (err) {
      console.error('复制失败:', err);
      message.error('复制失败，请手动复制');
    }
  };

  return (
    <div className={`contact-support ${className || ''}`}>
      <div className="contact-methods">
        <div className="contact-method qr">
          <div className="method-label">客服微信：</div>
          <div className="wechat-box">
            <span className="wechat-text">{wechat}</span>
            <button 
              className="copy-id-btn"
              onClick={() => handleCopy('wechat')}
              title="复制"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
              </svg>
            </button>
            <div className="qr-container">
              <img src="https://file.aibikini.cn/config/contact/wechat-qr.png" alt="微信二维码" />
              <button 
                className="zoom-btn"
                onClick={() => setShowLargeQR(true)}
              >
                <svg viewBox="0 0 24 24" width="10" height="10" stroke="currentColor" strokeWidth="2" fill="none">
                  <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div className="contact-method phone">
          <div className="method-label">客服电话：</div>
          <div className="phone-box">
            <span className="phone-text">{phone}</span>
            <button 
              className="copy-id-btn"
              onClick={() => handleCopy('phone')}
              title="复制"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>
        <div className="contact-method email">
          <div className="method-label">客服邮箱：</div>
          <div className="email-box">
            <span className="email-text">{email}</span>
            <button 
              className="copy-id-btn"
              onClick={() => handleCopy('email')}
              title="复制"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
      <div className="contact-address-row">
        <div className="contact-method address">
          <div className="method-label">联系地址：</div>
          <div className="address-box">
            <span className="address-text">辽宁省兴城市斯达威泳装超级产业园斯达威网科大厦11层</span>
            <button 
              className="copy-id-btn"
              onClick={() => handleCopyAddress()}
              title="复制"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {showLargeQR && (
        <div className="qr-modal" onClick={() => setShowLargeQR(false)}>
          <div className="qr-modal-content" onClick={e => e.stopPropagation()}>
            <img src="https://file.aibikini.cn/config/contact/wechat-qr.png" alt="微信二维码" />
            <button 
              className="close-modal"
              onClick={() => setShowLargeQR(false)}
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContactSupport; 