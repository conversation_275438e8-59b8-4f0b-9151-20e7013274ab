import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Md<PERSON><PERSON><PERSON>, MdExpandMore, MdExpandLess, MdClose, MdContentCopy, MdStar, MdStarOutline } from 'react-icons/md';
import { TbColorPicker } from 'react-icons/tb';
import { HexColorPicker } from 'react-colorful';
import { message, Modal } from 'antd';
import '../../styles/close-buttons.css';
import './index.css';
import { showDeleteConfirmModal } from '../../utils/modalUtils';

/**
 * 选色组件（颜色选择器弹窗）
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - 控制弹窗是否显示
 * @param {Function} props.onClose - 关闭弹窗的回调函数
 * @param {string} props.initialColor - 初始颜色值，格式为 #RRGGBB
 * @param {Function} props.onColorSelect - 颜色选择后的回调函数，参数为所选颜色值
 * @param {Array<string>} [props.presetColors] - 预设颜色列表，格式为 ['#RRGGBB', ...]
 * @param {Object} [props.style] - 弹窗容器的自定义样式
 */
const ColorPickerModal = ({
  isOpen,
  onClose,
  initialColor = '#FF3C6A',
  onColorSelect,
  style,
  presetColors = [
    "#FF0000", // 红色
    "#FF8A00", // 橙色
    "#FFD700", // 金色
    "#09D809", // 绿色
    "#00e0f3", // 青色
    "#0000FF", // 蓝色
    "#E000E0", // 紫色
    "#FF0093", // 玫红
    "#000000", // 黑色
    "#FFFFFF"  // 白色
  ]
}) => {
  // 标准化所有颜色格式为大写
  const standardizedPresetColors = useMemo(() => {
    return presetColors.map(color => color.toUpperCase());
  }, [presetColors]);

  const [selectedColor, setSelectedColor] = useState(initialColor.toUpperCase());
  const [hexInput, setHexInput] = useState(initialColor);
  const [isEyeDropperSupported, setIsEyeDropperSupported] = useState(false);
  const [colorMode, setColorMode] = useState('HEX'); // 新增：颜色模式
  const [colorModeOpen, setColorModeOpen] = useState(false); // 新增：颜色模式下拉菜单状态
  // 新增：清空状态标记
  const [isCleared, setIsCleared] = useState(false);
  // RGB 模式的状态
  const [rgbValues, setRgbValues] = useState({ r: 255, g: 60, b: 106 });
  // HSB 模式的状态
  const [hsbValues, setHsbValues] = useState({ h: 0, s: 0, b: 0 });
  
  // 显示哪个输入框的调节按钮
  const [activeInput, setActiveInput] = useState(null);

  // 新增：收藏状态
  const [isFavorite, setIsFavorite] = useState(false);
  // 新增：收藏的颜色列表
  const [favoriteColors, setFavoriteColors] = useState([]);

  // 新增：历史颜色状态
  const [historyColors, setHistoryColors] = useState([]);

  // 拖动相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });

  // 检测是否为移动设备
  const isMobileDevice = useMemo(() => {
    return window.matchMedia('(hover: none) and (pointer: coarse)').matches;
  }, []);

  // 初始化颜色值
  useEffect(() => {
    // 检测是否在安全上下文中
    if (window.isSecureContext) {
      // 安全上下文下可以使用吸管API
      console.log('运行在安全环境，吸管功能可用');
    } else {
      console.warn('非安全环境，吸管功能被禁用');
    }
    setSelectedColor(initialColor.toUpperCase());
    setHexInput(initialColor);
    // 初始化 RGB 和 HSB 值
    updateColorValues(initialColor);
    // 重置清空状态
    setIsCleared(false);
    // 重置拖动位置状态
    if (isOpen) {
      setModalPosition({ x: 0, y: 0 });
      setIsDragging(false);
    }
  }, [initialColor, isOpen]);

  // 从本地存储加载收藏的颜色，并检查当前颜色是否在收藏中
  useEffect(() => {
    if (isOpen) {
      try {
        const storedFavorites = localStorage.getItem('favoriteColors');
        if (storedFavorites) {
          const parsed = JSON.parse(storedFavorites);
          const favorites = Array.isArray(parsed) ? parsed : [];
          setFavoriteColors(favorites);
          setIsFavorite(favorites.includes(initialColor.toUpperCase()));
        } else {
          setFavoriteColors([]);
          setIsFavorite(false);
        }
      } catch (error) {
        console.error('Error loading favorite colors:', error);
        setFavoriteColors([]);
        setIsFavorite(false);
      }
    }
  }, [isOpen, initialColor]);

  // 当颜色变化时，检查是否在收藏列表中
  useEffect(() => {
    setIsFavorite(favoriteColors.includes(selectedColor));
  }, [selectedColor, favoriteColors]);

  // 添加对EyeDropper API的支持检测
  useEffect(() => {
    setIsEyeDropperSupported(!!window.EyeDropper);
  }, []);

  // 从本地存储加载历史颜色
  useEffect(() => {
    if (isOpen) {
      try {
        const storedHistory = localStorage.getItem('colorHistory');
        if (storedHistory) {
          const parsed = JSON.parse(storedHistory);
          const history = Array.isArray(parsed) ? parsed : [];
          setHistoryColors(history);
        } else {
          setHistoryColors([]);
        }
      } catch (error) {
        console.error('Error loading color history:', error);
        setHistoryColors([]);
      }
    }
  }, [isOpen]);

  // 更新所有颜色值（HEX、RGB、HSB）
  const updateColorValues = (hex) => {
    // 更新 RGB 值
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    setRgbValues({ r, g, b });
    
    // 更新 HSB 值
    const hsb = rgbToHsb(r, g, b);
    setHsbValues(hsb);
  };

  // RGB 转 HSB
  const rgbToHsb = (r, g, b) => {
    r /= 255;
    g /= 255;
    b /= 255;
    
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const delta = max - min;
    
    let h = 0;
    let s = max === 0 ? 0 : delta / max;
    let v = max;
    
    if (delta > 0) {
      if (max === r) {
        h = ((g - b) / delta) % 6;
      } else if (max === g) {
        h = (b - r) / delta + 2;
      } else {
        h = (r - g) / delta + 4;
      }
      
      h = Math.round(h * 60);
      if (h < 0) h += 360;
    }
    
    return { 
      h: Math.round(h), 
      s: Math.round(s * 100), 
      b: Math.round(v * 100) 
    };
  };
  
  // HSB 转 RGB
  const hsbToRgb = (h, s, b) => {
    h /= 60;
    s /= 100;
    b /= 100;
    
    const i = Math.floor(h);
    const f = h - i;
    const p = b * (1 - s);
    const q = b * (1 - s * f);
    const t = b * (1 - s * (1 - f));
    
    let r, g, bValue;
    
    switch (i % 6) {
      case 0: r = b; g = t; bValue = p; break;
      case 1: r = q; g = b; bValue = p; break;
      case 2: r = p; g = b; bValue = t; break;
      case 3: r = p; g = q; bValue = b; break;
      case 4: r = t; g = p; bValue = b; break;
      case 5: r = b; g = p; bValue = q; break;
      default: r = 0; g = 0; bValue = 0;
    }
    
    return { 
      r: Math.round(r * 255), 
      g: Math.round(g * 255), 
      b: Math.round(bValue * 255) 
    };
  };
  
  // 更精确的RGB到HEX转换
  const rgbToHex = (r, g, b) => {
    // 确保值在0-255范围内
    r = Math.max(0, Math.min(255, Math.round(r)));
    g = Math.max(0, Math.min(255, Math.round(g)));
    b = Math.max(0, Math.min(255, Math.round(b)));
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`.toUpperCase();
  };

  // 精确的颜色比较函数
  const getColorDistance = (color1, color2) => {
    // 将十六进制转换为RGB
    const r1 = parseInt(color1.slice(1, 3), 16);
    const g1 = parseInt(color1.slice(3, 5), 16);
    const b1 = parseInt(color1.slice(5, 7), 16);
    
    const r2 = parseInt(color2.slice(1, 3), 16);
    const g2 = parseInt(color2.slice(3, 5), 16);
    const b2 = parseInt(color2.slice(5, 7), 16);
    
    // 计算欧几里得距离
    return Math.sqrt(
      Math.pow(r1 - r2, 2) + 
      Math.pow(g1 - g2, 2) + 
      Math.pow(b1 - b2, 2)
    );
  };

  // 处理颜色变化
  const handleColorChange = (color) => {
    // 用户选择了新颜色，重置清空状态
    setIsCleared(false);
    setSelectedColor(color);
    setHexInput(color);
    updateColorValues(color);
  };

  // 处理十六进制输入变化
  const handleHexInputChange = (e) => {
    const value = e.target.value.toUpperCase();
    setHexInput(value);
    
    // 验证格式是否正确
    if (/^#[0-9A-F]{6}$/i.test(value)) {
      setSelectedColor(value.toUpperCase());
      updateColorValues(value);
      // 重置清空状态
      setIsCleared(false);
    }
  };

  // 处理十六进制输入失焦
  const handleHexInputBlur = () => {
    // 如果格式不正确，重置为当前选中的颜色
    if (!/^#[0-9A-F]{6}$/i.test(hexInput)) {
      setHexInput(selectedColor);
    }
  };
  
  // 处理 RGB 输入变化
  const handleRgbInputChange = (component, value) => {
    // 验证并限制输入范围 (0-255)
    let parsedValue = parseInt(value, 10);
    if (isNaN(parsedValue)) parsedValue = 0;
    if (parsedValue < 0) parsedValue = 0;
    if (parsedValue > 255) parsedValue = 255;
    
    const newRgbValues = { ...rgbValues, [component]: parsedValue };
    setRgbValues(newRgbValues);
    
    // 更新十六进制值和选中的颜色
    const newHexColor = rgbToHex(newRgbValues.r, newRgbValues.g, newRgbValues.b);
    setSelectedColor(newHexColor);
    setHexInput(newHexColor);
    
    // 更新 HSB 值
    setHsbValues(rgbToHsb(newRgbValues.r, newRgbValues.g, newRgbValues.b));
    
    // 重置清空状态
    setIsCleared(false);
  };
  
  // 处理 HSB 输入变化
  const handleHsbInputChange = (component, value) => {
    // 验证并限制输入范围
    let parsedValue = parseInt(value, 10);
    if (isNaN(parsedValue)) parsedValue = 0;
    
    const ranges = { h: 360, s: 100, b: 100 };
    if (parsedValue < 0) parsedValue = 0;
    if (parsedValue > ranges[component]) parsedValue = ranges[component];
    
    const newHsbValues = { ...hsbValues, [component]: parsedValue };
    setHsbValues(newHsbValues);
    
    // 转换为 RGB
    const rgb = hsbToRgb(newHsbValues.h, newHsbValues.s, newHsbValues.b);
    setRgbValues(rgb);
    
    // 更新十六进制值和选中的颜色
    const newHexColor = rgbToHex(rgb.r, rgb.g, rgb.b);
    setSelectedColor(newHexColor);
    setHexInput(newHexColor);
    
    // 重置清空状态
    setIsCleared(false);
  };
  
  // 增加或减少数值
  const adjustValue = (component, mode, increment) => {
    if (mode === 'RGB') {
      const newValue = rgbValues[component] + increment;
      handleRgbInputChange(component, newValue);
    } else if (mode === 'HSB') {
      const newValue = hsbValues[component] + increment;
      handleHsbInputChange(component, newValue);
    }
  };

  // 吸管工具点击
  const handleEyeDropperClick = async () => {
    if (!window.EyeDropper) {
      message.error('您的浏览器不支持吸管工具，请使用Chrome、Edge或其他支持EyeDropper API的浏览器');
      return;
    }

    // 存储鼠标点击前的数据
    const originalSelectedColor = selectedColor;
    let userClickedElement = null;
    
    // 为document添加一次性点击事件监听器，捕获用户点击的元素
    const clickHandler = (e) => {
      userClickedElement = e.target;
      e.preventDefault();
      e.stopPropagation();
    };
    
    try {
      
      // 添加点击事件捕获
      document.addEventListener('click', clickHandler, { once: true, capture: true });
      
      // 创建吸管实例
      const eyeDropper = new window.EyeDropper();
      
      // 打开吸管
      const result = await eyeDropper.open();
      
      // 清除点击事件
      document.removeEventListener('click', clickHandler, { capture: true });
      
      // 获取颜色值并转换为大写
      let pickedColor = result.sRGBHex.toUpperCase();
      
      // 如果用户点击了颜色预览器或预设颜色按钮，使用其data-color属性值
      if (userClickedElement) {
        // 检查是否点击了预设颜色按钮
        if (userClickedElement.classList.contains('preset-color-btn') && userClickedElement.dataset.color) {
          pickedColor = userClickedElement.dataset.color.toUpperCase();
        } 
        // 检查是否点击了颜色预览区域
        else if (userClickedElement.classList.contains('selected-color-preview') && userClickedElement.dataset.color) {
          pickedColor = userClickedElement.dataset.color.toUpperCase();
        }
        // 检查是否点击了react-colorful颜色选择器内部
        else if (userClickedElement.closest('.react-colorful')) {
          pickedColor = originalSelectedColor;
        }
      }
      
      // 应用选中的颜色
      setSelectedColor(pickedColor);
      setHexInput(pickedColor);
      updateColorValues(pickedColor);
    } catch (err) {
      // 清除点击事件
      document.removeEventListener('click', clickHandler, { capture: true });
      
      // 用户取消或发生错误
      if (err.name !== 'AbortError') {
        message.error('吸管工具出错，请重试');
        console.error('吸管工具错误:', err);
      }
    }
  };

  // 预设颜色选择
  const handlePresetColorSelect = (color) => {
    // 用户选择了预设颜色，重置清空状态
    setIsCleared(false);
    setSelectedColor(color);
    setHexInput(color);
    updateColorValues(color);
  };

  // 处理确认选择颜色
  const handleConfirm = () => {
    // 检查是否为清空状态
    if (isCleared) {
      // 如果是清空状态，则传递null给父组件
      onColorSelect?.(null);
    } else {
      // 否则传递选中的颜色
      onColorSelect?.(selectedColor);
    }
    onClose(); // 关闭弹窗
  };
  
  // 添加清除颜色的函数
  const handleClearColor = () => {
    // 标记为清空状态
    setIsCleared(true);
    // 临时将颜色设置为白色（或其他默认颜色）
    setSelectedColor('#FFFFFF');
    setHexInput('#FFFFFF');
    updateColorValues('#FFFFFF');
  };

  // 切换颜色模式
  const handleColorModeChange = (mode) => {
    setColorMode(mode);
    setColorModeOpen(false);
  };

  // 处理收藏颜色
  const handleFavoriteColor = () => {
    if (isFavorite) {
      // 取消收藏
      const updatedFavorites = favoriteColors.filter(color => color !== selectedColor);
      setFavoriteColors(updatedFavorites);
      setIsFavorite(false);
      
      // 保存到本地存储
      try {
        localStorage.setItem('favoriteColors', JSON.stringify(updatedFavorites));
      } catch (error) {
        console.error('Error saving favorite colors:', error);
      }
    } else {
      // 添加收藏，但限制最多10个
      if (favoriteColors.length >= 10) {
        message.warning('收藏数量已达上限（10个），请先取消一些收藏');
        return;
      }
      
      // 添加到收藏列表
      const updatedFavorites = [...favoriteColors, selectedColor];
      setFavoriteColors(updatedFavorites);
      setIsFavorite(true);
      
      // 保存到本地存储
      try {
        localStorage.setItem('favoriteColors', JSON.stringify(updatedFavorites));
      } catch (error) {
        console.error('Error saving favorite colors:', error);
      }
    }
  };

  // 处理删除收藏颜色
  const handleDeleteFavorite = (colorToDelete, e) => {
    e.stopPropagation(); // 阻止事件冒泡
    
    showDeleteConfirmModal({
      title: '确认删除',
      content: '确定要删除这个收藏的颜色吗？删除后将无法恢复。',
      onOk: () => {
        const updatedFavorites = favoriteColors.filter(color => color !== colorToDelete);
        setFavoriteColors(updatedFavorites);
        if (colorToDelete === selectedColor) {
          setIsFavorite(false);
        }
        
        // 保存到本地存储
        try {
          localStorage.setItem('favoriteColors', JSON.stringify(updatedFavorites));
        } catch (error) {
          console.error('Error saving favorite colors:', error);
        }
      }
    });
  };

  // 从收藏列表选择颜色
  const handleFavoriteColorSelect = (color) => {
    // 用户选择了收藏的颜色，重置清空状态
    setIsCleared(false);
    setSelectedColor(color);
    setHexInput(color);
    updateColorValues(color);
  };

  // 复制颜色值
  const handleCopyColor = () => {
    let textToCopy;
    let messageText = '已复制颜色值';
    
    switch (colorMode) {
      case 'RGB':
        textToCopy = `rgb(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b})`;
        messageText = '已复制RGB颜色值';
        break;
      case 'HSB':
        textToCopy = `hsb(${hsbValues.h}, ${hsbValues.s}%, ${hsbValues.b}%)`;
        messageText = '已复制HSB颜色值';
        break;
      case 'HEX':
      default:
        textToCopy = selectedColor;
        messageText = '已复制HEX颜色值';
    }
    
    navigator.clipboard.writeText(textToCopy);
    message.success(messageText);
  };
  
  // 鼠标进入输入框
  const handleInputMouseEnter = (inputName) => {
    setActiveInput(inputName);
  };
  
  // 鼠标离开输入框
  const handleInputMouseLeave = () => {
    setActiveInput(null);
  };

  // 处理历史颜色选择
  const handleHistoryColorSelect = (color) => {
    // 用户选择了历史颜色，重置清空状态
    setIsCleared(false);
    setSelectedColor(color);
    setHexInput(color);
    updateColorValues(color);
  };

  // 拖动相关处理函数
  const handleMouseDown = (e) => {
    // 移动端禁用拖动功能
    if (isMobileDevice) {
      return;
    }
    
    // 只在标题区域允许拖动
    if (e.target.closest('.modal-header') && !e.target.closest('button')) {
      setIsDragging(true);
      
      const modalElement = e.target.closest('.color-picker-modal');
      const rect = modalElement.getBoundingClientRect();
      
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
      
      // 防止文本选择
      document.body.classList.add('no-select');
      modalElement.classList.add('dragging');
      
      e.preventDefault();
    }
  };

  const handleMouseMove = useCallback((e) => {
    if (isDragging) {
      const newX = e.clientX - dragOffset.x;
      const newY = e.clientY - dragOffset.y;
      
      // 限制在视窗范围内
      const maxX = window.innerWidth - 500; // 弹窗宽度
      const maxY = window.innerHeight - 600; // 弹窗大概高度
      
      setModalPosition({
        x: Math.max(0, Math.min(newX, maxX)),
        y: Math.max(0, Math.min(newY, maxY))
      });
    }
  }, [isDragging, dragOffset]);

  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      
      // 恢复文本选择
      document.body.classList.remove('no-select');
      
      // 移除拖动状态类
      const modalElement = document.querySelector('.color-picker-modal');
      if (modalElement) {
        modalElement.classList.remove('dragging');
      }
    }
  }, [isDragging]);

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return isOpen ? (
    <div className="color-picker-wrapper">
      <div 
        className="modal-content color-picker-modal" 
        style={{
          ...style,
          ...(isMobileDevice ? {} : (modalPosition.x !== 0 || modalPosition.y !== 0 ? {
            left: modalPosition.x,
            top: modalPosition.y
          } : {})),
          cursor: isDragging ? 'grabbing' : 'default'
        }}
        onMouseDown={isMobileDevice ? undefined : handleMouseDown}
      >
        <div className="modal-header" style={{ cursor: isMobileDevice ? 'default' : 'grab' }}>
          <div className="tab-group">
            <button 
              className={`tab-btn ${colorMode === 'HEX' ? 'active' : ''}`}
              onClick={() => handleColorModeChange('HEX')}
            >
              HEX
            </button>
            <button 
              className={`tab-btn ${colorMode === 'RGB' ? 'active' : ''}`}
              onClick={() => handleColorModeChange('RGB')}
            >
              RGB
            </button>
            <button 
              className={`tab-btn ${colorMode === 'HSB' ? 'active' : ''}`}
              onClick={() => handleColorModeChange('HSB')}
            >
              HSB
            </button>
          </div>
          <button className="medium-close-button" onClick={onClose}>
            <MdClose />
          </button>
        </div>
        
        <div className="modal-body">
          <div className="color-picker-content">
            <div className="color-preview">
              <div className="color-preview-row">
                <div 
                  className="selected-color-preview" 
                  style={{ backgroundColor: selectedColor }}
                  data-color={selectedColor}
                ></div>
                
                <div className="color-value-container">
                  {colorMode === 'HEX' && (
                    <div className="hex-input-wrapper">
                      <input
                        type="text"
                        className="hex-input"
                        value={hexInput}
                        onChange={handleHexInputChange}
                        onBlur={handleHexInputBlur}
                        maxLength={7}
                        placeholder="#RRGGBB"
                      />
                      <button 
                        className="copy-color-btn"
                        onClick={handleCopyColor}
                        title="复制颜色值"
                      >
                        <MdContentCopy />
                      </button>
                      <button 
                        className={`favorite-color-btn ${isFavorite ? 'active' : ''}`}
                        onClick={handleFavoriteColor}
                        title={isFavorite ? "取消收藏" : "收藏颜色"}
                      >
                        {isFavorite ? <MdStar /> : <MdStarOutline />}
                      </button>
                      {(
                        <button 
                          className="eyedropper-btn"
                          onClick={handleEyeDropperClick}
                          title="使用吸管工具选择屏幕上的颜色"
                        >
                          <TbColorPicker />
                        </button>
                      )}
                    </div>
                  )}
                  
                  {colorMode === 'RGB' && (
                    <div className="color-inputs-container">
                      <div className="color-inputs-row">
                        <div 
                          className="number-input-wrapper"
                          onMouseEnter={() => handleInputMouseEnter('r')}
                          onMouseLeave={handleInputMouseLeave}
                        >
                          <span 
                            className="input-label" 
                            style={{
                              position: 'absolute',
                              left: '8px',
                              color: '#000000',
                              fontWeight: 'normal',
                              fontSize: '14px',
                              zIndex: 5,
                              display: 'block'
                            }}
                          >
                            R
                          </span>
                          <input
                            type="text"
                            className="number-input"
                            value={rgbValues.r}
                            onChange={(e) => handleRgbInputChange('r', e.target.value)}
                            maxLength={3}
                          />
                          {activeInput === 'r' && (
                            <div className="number-controls">
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('r', 'RGB', 1)}
                              >
                                <MdExpandLess />
                              </button>
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('r', 'RGB', -1)}
                              >
                                <MdExpandMore />
                              </button>
                            </div>
                          )}
                        </div>
                        <div 
                          className="number-input-wrapper"
                          onMouseEnter={() => handleInputMouseEnter('g')}
                          onMouseLeave={handleInputMouseLeave}
                        >
                          <span 
                            className="input-label" 
                            style={{
                              position: 'absolute',
                              left: '8px',
                              color: '#000000',
                              fontWeight: 'normal',
                              fontSize: '14px',
                              zIndex: 5,
                              display: 'block'
                            }}
                          >
                            G
                          </span>
                          <input
                            type="text"
                            className="number-input"
                            value={rgbValues.g}
                            onChange={(e) => handleRgbInputChange('g', e.target.value)}
                            maxLength={3}
                          />
                          {activeInput === 'g' && (
                            <div className="number-controls">
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('g', 'RGB', 1)}
                              >
                                <MdExpandLess />
                              </button>
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('g', 'RGB', -1)}
                              >
                                <MdExpandMore />
                              </button>
                            </div>
                          )}
                        </div>
                        <div 
                          className="number-input-wrapper"
                          onMouseEnter={() => handleInputMouseEnter('b')}
                          onMouseLeave={handleInputMouseLeave}
                        >
                          <span 
                            className="input-label" 
                            style={{
                              position: 'absolute',
                              left: '8px',
                              color: '#000000',
                              fontWeight: 'normal',
                              fontSize: '14px',
                              zIndex: 5,
                              display: 'block'
                            }}
                          >
                            B
                          </span>
                          <input
                            type="text"
                            className="number-input"
                            value={rgbValues.b}
                            onChange={(e) => handleRgbInputChange('b', e.target.value)}
                            maxLength={3}
                          />
                          {activeInput === 'b' && (
                            <div className="number-controls">
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('b', 'RGB', 1)}
                              >
                                <MdExpandLess />
                              </button>
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('b', 'RGB', -1)}
                              >
                                <MdExpandMore />
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                      <button 
                        className="copy-color-btn"
                        onClick={handleCopyColor}
                        title="复制颜色值"
                      >
                        <MdContentCopy />
                      </button>
                      <button 
                        className={`favorite-color-btn ${isFavorite ? 'active' : ''}`}
                        onClick={handleFavoriteColor}
                        title={isFavorite ? "取消收藏" : "收藏颜色"}
                      >
                        {isFavorite ? <MdStar /> : <MdStarOutline />}
                      </button>
                      {isEyeDropperSupported && (
                        <button 
                          className="eyedropper-btn"
                          onClick={handleEyeDropperClick}
                          title="使用吸管工具选择屏幕上的颜色"
                        >
                          <TbColorPicker />
                        </button>
                      )}
                    </div>
                  )}
                  
                  {colorMode === 'HSB' && (
                    <div className="color-inputs-container">
                      <div className="color-inputs-row">
                        <div 
                          className="number-input-wrapper"
                          onMouseEnter={() => handleInputMouseEnter('h')}
                          onMouseLeave={handleInputMouseLeave}
                        >
                          <span 
                            className="input-label" 
                            style={{
                              position: 'absolute',
                              left: '8px',
                              color: '#000000',
                              fontWeight: 'normal',
                              fontSize: '14px',
                              zIndex: 5,
                              display: 'block'
                            }}
                          >
                            H
                          </span>
                          <input
                            type="text"
                            className="number-input"
                            value={hsbValues.h}
                            onChange={(e) => handleHsbInputChange('h', e.target.value)}
                            maxLength={3}
                          />
                          {activeInput === 'h' && (
                            <div className="number-controls">
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('h', 'HSB', 1)}
                              >
                                <MdExpandLess />
                              </button>
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('h', 'HSB', -1)}
                              >
                                <MdExpandMore />
                              </button>
                            </div>
                          )}
                        </div>
                        <div 
                          className="number-input-wrapper"
                          onMouseEnter={() => handleInputMouseEnter('s')}
                          onMouseLeave={handleInputMouseLeave}
                        >
                          <span 
                            className="input-label" 
                            style={{
                              position: 'absolute',
                              left: '8px',
                              color: '#000000',
                              fontWeight: 'normal',
                              fontSize: '14px',
                              zIndex: 5,
                              display: 'block'
                            }}
                          >
                            S
                          </span>
                          <input
                            type="text"
                            className="number-input"
                            value={hsbValues.s}
                            onChange={(e) => handleHsbInputChange('s', e.target.value)}
                            maxLength={3}
                          />
                          {activeInput === 's' && (
                            <div className="number-controls">
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('s', 'HSB', 1)}
                              >
                                <MdExpandLess />
                              </button>
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('s', 'HSB', -1)}
                              >
                                <MdExpandMore />
                              </button>
                            </div>
                          )}
                        </div>
                        <div 
                          className="number-input-wrapper"
                          onMouseEnter={() => handleInputMouseEnter('b')}
                          onMouseLeave={handleInputMouseLeave}
                        >
                          <span 
                            className="input-label" 
                            style={{
                              position: 'absolute',
                              left: '8px',
                              color: '#000000',
                              fontWeight: 'normal',
                              fontSize: '14px',
                              zIndex: 5,
                              display: 'block'
                            }}
                          >
                            B
                          </span>
                          <input
                            type="text"
                            className="number-input"
                            value={hsbValues.b}
                            onChange={(e) => handleHsbInputChange('b', e.target.value)}
                            maxLength={3}
                          />
                          {activeInput === 'b' && (
                            <div className="number-controls">
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('b', 'HSB', 1)}
                              >
                                <MdExpandLess />
                              </button>
                              <button 
                                className="number-control-btn" 
                                onClick={() => adjustValue('b', 'HSB', -1)}
                              >
                                <MdExpandMore />
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                      <button 
                        className="copy-color-btn"
                        onClick={handleCopyColor}
                        title="复制颜色值"
                      >
                        <MdContentCopy />
                      </button>
                      <button 
                        className={`favorite-color-btn ${isFavorite ? 'active' : ''}`}
                        onClick={handleFavoriteColor}
                        title={isFavorite ? "取消收藏" : "收藏颜色"}
                      >
                        {isFavorite ? <MdStar /> : <MdStarOutline />}
                      </button>
                      {isEyeDropperSupported && (
                        <button 
                          className="eyedropper-btn"
                          onClick={handleEyeDropperClick}
                          title="使用吸管工具选择屏幕上的颜色"
                        >
                          <TbColorPicker />
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            <div className="color-input-container">
              <div className="custom-color-picker">
                <HexColorPicker 
                  color={selectedColor} 
                  onChange={handleColorChange}
                />
              </div>
            </div>

            <div className="preset-colors-container">
              <div className="preset-colors">
                <div className="preset-title">推荐</div>
                {standardizedPresetColors.map((color, index) => (
                  <button
                    key={index}
                    className={`preset-color-btn ${selectedColor === color ? 'active' : ''}`}
                    style={{ backgroundColor: color }}
                    onClick={() => handlePresetColorSelect(color)}
                    title={`预设颜色: ${color}`}
                    data-color={color}
                  >
                    {selectedColor === color && (
                      <span className="check-icon">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* 收藏颜色区域 */}
            <div className="favorite-colors-container">
              <div className="favorite-colors">
                <div className="favorite-title">收藏</div>
                {favoriteColors.length > 0 ? (
                  favoriteColors.slice(0, 10).map((color, index) => (
                    <div key={index} className="favorite-color-item">
                      <button
                        className={`preset-color-btn ${selectedColor === color ? 'active' : ''}`}
                        style={{ backgroundColor: color }}
                        onClick={() => handleFavoriteColorSelect(color)}
                        title={`收藏颜色: ${color}`}
                        data-color={color}
                      >
                        {selectedColor === color && (
                          <span className="check-icon">
                            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"
                                fill="currentColor"
                              />
                            </svg>
                          </span>
                        )}
                      </button>
                      <button
                        className="delete-favorite-btn"
                        onClick={(e) => handleDeleteFavorite(color, e)}
                        title="删除收藏"
                      >
                        <MdClose size={12} />
                      </button>
                    </div>
                  ))
                ) : (
                  <div className="no-favorites-message">暂无收藏颜色</div>
                )}
              </div>
            </div>

            {/* 历史颜色区域 */}
            <div className="history-colors-container">
              <div className="history-colors">
                <div className="history-title">历史</div>
                {historyColors.length > 0 ? (
                  historyColors.slice(0, 10).map((color, index) => (
                    <button
                      key={index}
                      className={`preset-color-btn ${selectedColor === color ? 'active' : ''}`}
                      style={{ backgroundColor: color }}
                      onClick={() => handleHistoryColorSelect(color)}
                      title={`历史颜色: ${color}`}
                      data-color={color}
                    >
                      {selectedColor === color && (
                        <span className="check-icon">
                          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"
                              fill="currentColor"
                            />
                          </svg>
                        </span>
                      )}
                    </button>
                  ))
                ) : (
                  <div className="no-history-message">暂无历史颜色</div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <div className="modal-footer">
          <button className="clear-btn" onClick={handleClearColor}>
            清空
          </button>
          <button className="save-settings-btn" onClick={handleConfirm}>
            确定
          </button>
        </div>
      </div>
    </div>
  ) : null;
};

export default ColorPickerModal; 