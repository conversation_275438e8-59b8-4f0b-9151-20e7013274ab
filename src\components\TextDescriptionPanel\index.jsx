import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { MdOutlineHelpOutline } from 'react-icons/md';
import './index.css';
import TipPopup from '../TipPopup';

/**
 * 文本描述组件 - 用于向后端传递描述词文本
 * 
 * 此组件包含文本输入框，用于向comfyUI工作流中的特定节点传递文本
 */
const TextDescriptionPanel = ({
  description,
  onChange,
  placeholder = '请输入对服装的文字描述，将用于辅助生成更符合要求的效果...',
  title = '文本描述'
}) => {
  // 本地状态，用于存储输入的文本
  const [text, setText] = useState(description || '');
  // 用于存储文本框高度
  const [textareaHeight, setTextareaHeight] = useState(120);
  // 用于UI反馈的拖动状态
  const [isDragging, setIsDragging] = useState(false);
  // 使用useRef存储拖动状态，避免闭包问题
  const isDraggingRef = useRef(false);
  const startYRef = useRef(0);
  const startHeightRef = useRef(0);
  
  // 提示弹窗相关状态
  const [isTipVisible, setIsTipVisible] = useState(false);
  const [tipPosition, setTipPosition] = useState({ left: 0, top: 0 });
  const tipButtonRef = useRef(null);
  
  // refs
  const textareaRef = useRef(null);
  const panelRef = useRef(null);
  const resizeHandleRef = useRef(null);
  
  // 检测滚动条并调整拖动按钮位置
  const checkScrollbarAndAdjustHandle = useCallback(() => {
    if (textareaRef.current && resizeHandleRef.current) {
      const textarea = textareaRef.current;
      const handle = resizeHandleRef.current;
      
      // 检测是否有垂直滚动条
      const hasScrollbar = textarea.scrollHeight > textarea.clientHeight;
      
      if (hasScrollbar) {
        // 当出现滚动条时，将拖动按钮向左移动，保持与滚动条的间距与边框间距一致
        handle.style.setProperty('right', '26px', 'important'); // 20px(间距) + 滚动条宽度 ≈ 26px
      } else {
        // 无滚动条时，恢复原位置
        handle.style.setProperty('right', '20px', 'important'); // 使用setProperty确保覆盖CSS
      }
    }
  }, []);
  
  // 当外部description变化时，更新本地状态
  useEffect(() => {
    if (description !== undefined) {
      setText(description);
    }
  }, [description]);

  // 监听文本内容和高度变化，检测滚动条
  useEffect(() => {
    checkScrollbarAndAdjustHandle();
  }, [text, textareaHeight, checkScrollbarAndAdjustHandle]);

  // 使用 ResizeObserver 监听 textarea 尺寸变化
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const resizeObserver = new ResizeObserver(() => {
      checkScrollbarAndAdjustHandle();
    });

    resizeObserver.observe(textarea);

    return () => {
      resizeObserver.disconnect();
    };
  }, [checkScrollbarAndAdjustHandle]);
  
  // 处理文本输入变化
  const handleTextChange = useCallback((e) => {
    const newText = e.target.value;
    setText(newText);
    
    // 通知父组件文本变化
    if (onChange) {
      onChange(newText);
    }
    
    // 使用 requestAnimationFrame 确保DOM已更新后再检测滚动条
    requestAnimationFrame(() => {
      checkScrollbarAndAdjustHandle();
    });
  }, [onChange, checkScrollbarAndAdjustHandle]);
  
  // 处理拖动事件（鼠标）
  const handleMouseMove = useCallback((e) => {
    if (!isDraggingRef.current) return;
    const deltaY = e.clientY - startYRef.current;
    const newTextareaHeight = Math.max(120, Math.min(300, startHeightRef.current + deltaY));
    setTextareaHeight(newTextareaHeight);
  }, []);

  const handleMouseUp = useCallback(() => {
    if (!isDraggingRef.current) return;
    isDraggingRef.current = false;
    setIsDragging(false);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    requestAnimationFrame(() => {
      checkScrollbarAndAdjustHandle();
    });
  }, [handleMouseMove, checkScrollbarAndAdjustHandle]);

  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    isDraggingRef.current = true;
    startYRef.current = e.clientY;
    startHeightRef.current = textareaHeight;
    setIsDragging(true);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [textareaHeight, handleMouseMove, handleMouseUp]);

  // === 新增：处理拖动事件（触摸） ===
  const handleTouchMove = useCallback((e) => {
    if (!isDraggingRef.current) return;
    if (!e.touches || e.touches.length === 0) return;
    const touch = e.touches[0];
    const deltaY = touch.clientY - startYRef.current;
    const newTextareaHeight = Math.max(120, Math.min(300, startHeightRef.current + deltaY));
    setTextareaHeight(newTextareaHeight);
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (!isDraggingRef.current) return;
    isDraggingRef.current = false;
    setIsDragging(false);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
    requestAnimationFrame(() => {
      checkScrollbarAndAdjustHandle();
    });
  }, [handleTouchMove, checkScrollbarAndAdjustHandle]);

  const handleTouchStart = useCallback((e) => {
    if (!e.touches || e.touches.length === 0) return;
    e.preventDefault();
    const touch = e.touches[0];
    isDraggingRef.current = true;
    startYRef.current = touch.clientY;
    startHeightRef.current = textareaHeight;
    setIsDragging(true);
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  }, [textareaHeight, handleTouchMove, handleTouchEnd]);
  
  // 清理函数 - 使用useEffect确保组件卸载时移除事件监听器
  useEffect(() => {
    // 只在组件卸载时执行清理
    return () => {
      if (isDraggingRef.current) {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      }
    };
  }, [handleMouseMove, handleMouseUp]);

  // 处理提示按钮点击
  const handleShowTip = () => {
    // 检测是否为移动端
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
      // 移动端：居中显示
      setTipPosition({
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)'
      });
    } else {
      // PC端：保持原有位置逻辑
    if (tipButtonRef.current) {
      const rect = tipButtonRef.current.getBoundingClientRect();
      setTipPosition({
        left: rect.left + rect.width + 28,
        top: rect.top - 8
      });
      }
    }
    setIsTipVisible(true);
  };

  // 处理关闭提示
  const handleCloseTip = () => {
    setIsTipVisible(false);
  };

  return (
    <>
      <div 
        className="text-description-setting" 
        ref={panelRef}
      >
        <div className="text-description-content">
          <div className="selected-description-preview">
            <img 
              src="https://file.aibikini.cn/config/icons/text-description.png" 
              alt="文本描述" 
              className="selected-description-thumbnail" 
            />
          </div>
          <div className="text-description-area">
            <div className="component-text">
              <h3>{title}</h3>
              <div className="component-content">
                <textarea
                  ref={textareaRef}
                  className="text-description-input"
                  value={text}
                  onChange={handleTextChange}
                  placeholder={placeholder}
                  style={{ height: `${textareaHeight}px` }}
                />
                <div 
                  className="textarea-resize-handle" 
                  onMouseDown={handleMouseDown}
                  onTouchStart={handleTouchStart}
                  title="拖动调整高度"
                  ref={resizeHandleRef}
                >
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 提示按钮 */}
        <button 
          className="tip-button-common text-description-tip-button"
          onClick={handleShowTip}
          ref={tipButtonRef}
          title="查看使用提示"
        >
          <span className="tip-text">点我</span>
          <MdOutlineHelpOutline />
        </button>
      </div>
      
      {/* 提示弹窗 */}
      <TipPopup 
        type="text-description"
        position={tipPosition}
        isVisible={isTipVisible}
        onClose={handleCloseTip}
        content={title === '文本对话' ? [
          "• 文本对话是一种更直观的交互方式，您可以用自然语言表达要如何调整款式",
          "• 与传统的文本描述不同，可以直接发出指令，如'把连体款式改为分体款式'",
          "• 指令提示词的质量决定图片生成效果",
          "• 指令的内容越详细，生成效果越符合预期",
          "• 支持中英文混合输入，但更推荐使用英文",
          "• 可使用图片取词功能，从图片中获取英文描述词后进行调整，效果会更好",
          "• 建议描述长度在50-200字之间",
          "• 建议查看详细教程进行学习 -->"
        ].join('\n') : [
          "• 文本描述是一种更传统的交互方式，您可以用自然语言描述调整款式的结果",
          "• 需要描述蒙版区域的调整结果，如'分体款式泳衣'",
          "• 描述提示词的质量决定图片生成效果",
          "• 描述的内容越详细，生成效果越符合预期",
          "• 支持中英文混合输入，但更推荐使用英文",
          "• 可使用图片取词功能，从图片中获取英文描述词后进行调整，效果会更好",
          "• 建议描述长度在50-200字之间",
          "• 建议查看详细教程进行学习 -->"
        ].join('\n')}
      />
    </>
  );
};

TextDescriptionPanel.propTypes = {
  description: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  title: PropTypes.string
};

export default TextDescriptionPanel; 