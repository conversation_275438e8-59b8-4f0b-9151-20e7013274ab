import React from 'react';
import PropTypes from 'prop-types';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

const ScenePanel = ({
  selectedScene,
  onExpandClick,
  pageType = 'fashion',
}) => {
  // 根据页面类型决定显示的标题
  const title = pageType === 'background' ? '背景' : '场景';
  
  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          {selectedScene ? (
            <ThumbnailWithPreview
              imageUrl={selectedScene.type === 'custom' ? 
                '/images/icons/scene-custom.png' : 
                (selectedScene.image || 'https://file.aibikini.cn/config/icons/scene.png')
              }
              alt={`${title} ${selectedScene.name}`}
              status="completed"
              featureName={`${title}预览`}
            />
          ) : (
            <img src="https://file.aibikini.cn/config/icons/scene.png" alt={title} className="component-icon" />
          )}
          <div className="component-text">
            <h3>{title}</h3>
            <div className="component-content">
              {selectedScene ? (
                <p>已选择 {selectedScene.type === 'custom' ? `自定义${title}` : selectedScene.name}</p>
              ) : (
                <p>选择合适的{title}</p>
              )}
            </div>
          </div>
        </div>
        <button 
          className="expand-btn"
          onClick={onExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

ScenePanel.propTypes = {
  selectedScene: PropTypes.shape({
    componentId: PropTypes.string,
    name: PropTypes.string,
    type: PropTypes.string,
    source: PropTypes.string,
    image: PropTypes.string,
  }),
  onExpandClick: PropTypes.func.isRequired,
  pageType: PropTypes.string,
};

export default ScenePanel; 