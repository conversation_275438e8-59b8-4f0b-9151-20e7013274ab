.edit-modal {
  position: fixed;
  left: 162px;
  top: 64px;
  background-color: var(--bg-primary);
  padding: 16px;
  width: 180px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  pointer-events: auto;
  border: 1px solid var(--border-color);
  max-height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  animation: slideIn 0.2s ease;
  z-index: 1000;
}

.edit-modal.sidebar-collapsed {
  left: 62px;
}

.edit-modal h3 {
  font-size: 0.8rem;
  color: var(--text-primary);
  margin: 0;
  margin-bottom: 8px;
}

.edit-modal h4 {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 8px 0 4px;
}

.edit-modal .feature-list {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  margin: 5px 0 0;
  flex: 1;
  padding-right: 12px; /* 为滚动条预留更多空间 */
}

/* 自定义滚动条样式 */
.edit-modal .feature-list::-webkit-scrollbar {
  width: 8px;
}

.edit-modal .feature-list::-webkit-scrollbar-track {
  background: transparent;
  margin-right: 8px; /* 滚动条轨道与内容之间的间距 */
}

.edit-modal .feature-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
  margin-right: 8px; /* 滚动条滑块与内容之间的间距 */
}

.edit-modal .feature-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

.edit-modal .feature-list .checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  cursor: pointer;
  font-size: 0.85rem !important;
  color: var(--text-primary);
}

.edit-modal .feature-list .checkbox-label input[type="checkbox"] {
  display: none;
}

.edit-modal .feature-list .checkbox-label .toggle-track {
  position: relative;
  width: 32px;
  height: 18px;
  background-color: var(--bg-secondary);
  border-radius: 9px;
  transition: background-color 0.2s ease;
}

.edit-modal .feature-list .checkbox-label .toggle-track::before {
  content: '';
  position: absolute;
  left: 2px;
  top: 2px;
  width: 14px;
  height: 14px;
  background-color: var(--text-inverse);
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.edit-modal .feature-list .checkbox-label input[type="checkbox"]:checked + .toggle-track {
  background-color: var(--brand-primary);
}

.edit-modal .feature-list .checkbox-label input[type="checkbox"]:checked + .toggle-track::before {
  transform: translateX(14px);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式样式 */
@media (max-width: 888px) {
  .edit-modal {
    position: fixed !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 280px !important;
    max-width: 90vw !important;
    max-height: 80vh !important;
    z-index: 1000 !important;
  }

  .edit-modal h4 {
    font-size: 0.75rem;
  }

  .edit-modal .feature-list .checkbox-label {
    font-size: 0.8rem !important;
    padding: 2px 6px;
    margin: 4px 0;
  }

  .edit-modal .feature-list .checkbox-label input[type="checkbox"] + .toggle-track {
    width: 28px;
    height: 16px;
  }

  .edit-modal .feature-list .checkbox-label input[type="checkbox"] + .toggle-track::before {
    width: 12px;
    height: 12px;
  }

  .edit-modal .feature-list .checkbox-label input[type="checkbox"]:checked + .toggle-track::before {
    transform: translateX(12px);
  }
} 