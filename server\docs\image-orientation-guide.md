# 图片方向校正功能使用指南

## 功能概述

本功能实现了Python代码中图片EXIF方向自动校正的JavaScript版本，主要功能包括：

1. **获取图片EXIF方向信息** - 读取图片的EXIF方向标签
2. **自动校正图片方向** - 根据EXIF信息自动旋转图片到正确方向
3. **保存原始元数据** - 在处理过程中保留图片的原始元数据信息
4. **临时文件管理** - 自动创建和清理临时文件，避免文件冲突

## 核心函数

### 1. getImageOrientation(imagePath)
获取图片的EXIF方向信息

```javascript
const orientation = await imageUtils.getImageOrientation('path/to/image.jpg');
// 返回值: 1-8 (1表示正常方向，其他值表示需要旋转)
```

### 2. correctImageOrientation(originalPath, outputPath)
自动校正图片方向

```javascript
const processedPath = await imageUtils.correctImageOrientation(
  'original.jpg', 
  'corrected.jpg' // 可选，不提供则创建临时文件
);
```

### 3. processImageWithMetadata(originalPath, outputPath)
处理图片并保存元数据（推荐使用）

```javascript
const result = await imageUtils.processImageWithMetadata('original.jpg');
console.log(result);
// 输出:
// {
//   originalPath: 'original.jpg',
//   processedPath: '/tmp/corrected_1234567890_image.jpg',
//   originalMetadata: { width: 1920, height: 1080, orientation: 6, ... },
//   processedMetadata: { width: 1080, height: 1920, orientation: 1, ... },
//   orientation: 6,
//   wasCorrected: true,
//   isTempFile: true
// }
```

### 4. cleanupTempFile(tempFilePath)
清理临时文件

```javascript
imageUtils.cleanupTempFile('/tmp/corrected_1234567890_image.jpg');
```

## 在上传接口中的集成

上传接口已经集成了图片方向校正功能，并自动处理临时文件：

### 单文件上传
```javascript
POST /api/oss/upload
Content-Type: multipart/form-data

// 响应中包含方向信息
{
  "success": true,
  "url": "https://oss.example.com/image.jpg",
  "fileInfo": {
    "width": 1920,
    "height": 1080,
    "size": 1024000,
    "mimetype": "image/jpeg",
    "orientationInfo": {
      "originalOrientation": 6,
      "wasCorrected": true
    }
  }
}
```

### 多文件上传
```javascript
POST /api/oss/uploads
Content-Type: multipart/form-data

// 响应中包含每个文件的方向信息
{
  "success": true,
  "urls": ["url1", "url2"],
  "fileInfos": [
    {
      "width": 1920,
      "height": 1080,
      "orientationInfo": { "originalOrientation": 6, "wasCorrected": true }
    }
  ]
}
```

## 方向值说明

| 值 | 描述 | 旋转角度 |
|---|---|---|
| 1 | 正常方向 | 0° |
| 2 | 水平翻转 | 0° |
| 3 | 旋转180° | 180° |
| 4 | 垂直翻转 | 0° |
| 5 | 水平翻转+逆时针90° | 90° |
| 6 | 顺时针90° | 90° |
| 7 | 水平翻转+顺时针90° | 270° |
| 8 | 逆时针90° | 270° |

## 临时文件处理机制

### 问题背景
Sharp库不允许输入和输出使用同一个文件，这会导致错误：`Cannot use same file for input and output`

### 解决方案
1. **自动创建临时文件** - 当不指定输出路径时，自动在系统临时目录创建临时文件
2. **临时文件命名** - 使用时间戳和原文件名生成唯一临时文件名
3. **自动清理** - 在上传完成后自动删除临时文件，避免磁盘空间浪费

### 临时文件路径示例
```
Windows: C:\Users\<USER>\AppData\Local\Temp\corrected_1703123456789_image.jpg
Linux/Mac: /tmp/corrected_1703123456789_image.jpg
```

## 使用示例

### 基本使用
```javascript
const imageUtils = require('./utils/imageUtils');

async function handleImageUpload(filePath) {
  try {
    // 处理图片方向
    const result = await imageUtils.processImageWithMetadata(filePath);
    
    if (result.wasCorrected) {
      console.log(`图片方向已校正: ${result.orientation} -> 1`);
    }
    
    // 使用校正后的图片路径
    return result.processedPath;
  } catch (error) {
    console.error('图片处理失败:', error);
    return filePath; // 返回原路径
  }
}
```

### 指定输出路径
```javascript
// 指定输出路径，避免创建临时文件
const result = await imageUtils.processImageWithMetadata(
  'input.jpg', 
  'output/corrected.jpg'
);
```

### 手动清理临时文件
```javascript
const result = await imageUtils.processImageWithMetadata('input.jpg');

// 使用处理后的文件
await uploadToOSS(result.processedPath);

// 如果是临时文件，手动清理
if (result.isTempFile) {
  imageUtils.cleanupTempFile(result.processedPath);
}
```

### 批量处理
```javascript
const imagePaths = ['image1.jpg', 'image2.jpg', 'image3.jpg'];
const results = await imageUtils.batchCorrectImageOrientation(imagePaths, 'output/');

results.forEach(result => {
  if (result.error) {
    console.error(`处理失败: ${result.originalPath} - ${result.error}`);
  } else {
    console.log(`处理成功: ${result.originalPath} -> ${result.processedPath}`);
  }
});
```

## 错误处理

### 常见错误及解决方案

1. **Cannot use same file for input and output**
   - **原因**: Sharp不允许输入输出使用同一文件
   - **解决**: 已修复，自动创建临时文件

2. **图片格式不支持**
   - **原因**: 某些图片格式可能不支持EXIF信息
   - **解决**: 自动跳过非图片文件，图片处理失败时使用原图

3. **权限不足**
   - **原因**: 无法创建或删除临时文件
   - **解决**: 检查系统临时目录权限

## 注意事项

1. **依赖要求**: 需要安装 `sharp` 包
2. **支持的格式**: JPEG, PNG, GIF, BMP, WebP
3. **性能考虑**: 大图片处理可能需要较长时间
4. **错误处理**: 如果校正失败，会继续使用原图
5. **文件权限**: 确保有读写文件的权限
6. **临时文件**: 系统会自动清理临时文件，无需手动处理
7. **磁盘空间**: 临时文件会占用额外磁盘空间，处理完成后自动释放

## 测试

运行测试脚本：
```bash
cd server
node test-image-orientation.js
```

## 与Python版本的对比

| 功能 | Python (PIL) | JavaScript (Sharp) |
|---|---|---|
| 获取方向 | `get_image_orientation()` | `getImageOrientation()` |
| 校正方向 | `ImageOps.exif_transpose()` | `sharp().rotate()` |
| 保存元数据 | 手动处理 | 自动获取 |
| 批量处理 | 循环处理 | `batchCorrectImageOrientation()` |
| 临时文件 | 手动管理 | 自动创建和清理 |
| 错误处理 | 需要手动处理 | 自动降级到原图 | 