import { getCSRFToken } from './security/csrf';
import { getCurrentUserId } from './request';

// API基础URL
const API_BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:3002/api'  // 开发环境
  : 'https://aibikini.vercel.app/api';  // 生产环境

/**
 * 上传图片到服务器
 * @param {File} file - 图片文件
 * @param {string} type - 上传类型 ('upload', 'mattingbg', 'clothing')
 * @param {string} pageType - 页面类型
 * @param {string} imageType - 图片类型
 * @param {string} fileId - 前端生成的文件ID，服务器将用此ID作为文件名
 * @returns {Promise<Object>} 上传结果
 */
export const uploadImage = async (file, type = 'upload', pageType = 'fashion', imageType = 'clothing', fileId = null) => {
  try {
    console.log(`\n==== 开始上传图片 ====`);
    console.log(`文件名: ${file.name}`);
    console.log(`文件大小: ${(file.size / 1024).toFixed(2)} KB`);
    console.log(`文件类型: ${file.type}`);
    console.log(`上传类型: ${type}`);
    console.log(`页面类型: ${pageType}`);
    console.log(`图片类型: ${imageType}`);
    console.log(`文件ID: ${fileId || '未提供'}`);
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);
    formData.append('workflow', type);  // 上传类型
    formData.append('pageType', pageType); // 页面类型
    formData.append('imageType', imageType); // 图片类型
    
    // 构建API URL - 如果提供了fileId，添加为查询参数
    const apiUrl = `${process.env.REACT_APP_BACKEND_URL}/api/process${fileId ? `?fileId=${fileId}` : ''}`;
    // console.log(`请求URL: ${apiUrl}`);
    
    // 记录FormData内容
    console.log('FormData内容:');
    for (const pair of formData.entries()) {
      if (pair[0] === 'file') {
        console.log(`${pair[0]}: [File ${pair[1].name}, ${pair[1].size} bytes]`);
      } else {
        console.log(`${pair[0]}: ${pair[1]}`);
      }
    }
    
    // 发送上传请求
    const response = await fetch(apiUrl, {
      method: 'POST',
      body: formData,
      headers: {
        // 不设置Content-Type，让浏览器自动添加，以包含boundary
        // 添加Authorization头
        ...(localStorage.getItem('token') ? { 'Authorization': `Bearer ${localStorage.getItem('token')}` } : {})
      }
    });
    
    // 处理响应
    const data = await response.json();
    // console.log('上传响应状态:', response.status);
    
    if (response.ok && data && data.success) {
      // console.log('上传成功，服务器返回:', data);
      // 解析返回的结果
      if (data.results && data.results.length > 0) {
        // 强调记录服务器文件名
        // console.log(`\n重要: 文件在服务器上的实际文件名: ${data.results[0].serverFileName}`);
        // console.log(`该文件名应在后续请求中用作文件引用`);
      }
      return data;
    } else {
      console.error('上传失败，服务器返回:', data);
      throw new Error(data.message || '图片上传失败');
    }
  } catch (error) {
    console.error('上传图片过程中出错:', error);
    throw error;
  }
};

/**
 * 上传多个图片到服务器
 * @param {Array<File>} files - 要上传的文件数组
 * @param {string} workflow - 工作流类型
 * @param {string} pageType - 页面类型
 * @param {Object} options - 其他选项
 * @returns {Promise<Array>} 上传结果数组
 */
export const uploadMultipleImages = async (files, workflow = 'mattingbg', pageType = 'mulimgvideo', options = {}) => {
  if (!files || !files.length) {
    throw new Error('没有提供文件');
  }

  const uploadResults = await Promise.all(
    files.map((file, index) => 
      uploadImage(
        file, 
        workflow, 
        pageType, 
        options.imageType || `image${index + 1}`,
        options.userId
      )
    )
  );
  
  return uploadResults;
};

export default {
  uploadImage,
  uploadMultipleImages
}; 