import React from 'react';
import { MdOutlineLightMode, MdOutlineDarkMode } from 'react-icons/md';
import './index.css';

// 提取的主题切换按钮组件
const ThemeToggle = ({ isDarkTheme, toggleTheme, sidebarStyle = false }) => {
  // 渲染图标函数
  const renderIcon = (IconComponent) => {
    return React.createElement(IconComponent);
  };

  // 如果是侧边栏样式，使用sidebar-bottom-btn样式
  if (sidebarStyle) {
    return (
      <button 
        className="sidebar-bottom-btn theme-toggle"
        onClick={toggleTheme}
        title={isDarkTheme ? '切换为亮色模式' : '切换为暗色模式'}
      >
        {renderIcon(isDarkTheme ? MdOutlineLightMode : MdOutlineDarkMode)}
        <span>主题切换</span>
      </button>
    );
  }
  
  // 默认样式（如果将来需要在其他地方使用）
  return (
    <button 
      className="theme-toggle-btn"
      onClick={toggleTheme}
      title={isDarkTheme ? '切换为亮色模式' : '切换为暗色模式'}
    >
      {renderIcon(isDarkTheme ? MdOutlineLightMode : MdOutlineDarkMode)}
    </button>
  );
};

export default ThemeToggle; 