import React from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

/**
 * 蒙版面板组件 - 用于展示服装原图和状态
 * 
 * 由 ModelMaskPanel 复制并重命名为 ClothingMaskPanel
 */
const ClothingMaskPanel = ({
  panel,
  onExpandClick,
  onDelete,
  onReupload,
  onStatusChange,
  isActive,
  onPanelsChange,
  onDrawMask,
  isEnhanced = false,
  pageType = 'try-on' // 默认为模特换装页面
}) => {
  const handleExpandClick = (e) => {
    const buttonRect = e.currentTarget.getBoundingClientRect();
    onExpandClick(panel, {
      top: buttonRect.top,
      left: buttonRect.left + buttonRect.width
    });
  };

  const handleDelete = () => {
    // 使用统一标准方案，仅使用componentId
    const panelId = panel.componentId;
    if (panelId) {
      onDelete?.(panelId);
      // 根据页面类型显示不同的成功消息
      const successMessage = pageType === 'optimize' ? '已删除款式图面板' : '已删除模特原图面板';
      message.success(successMessage);
    }
  };

  const handleReupload = () => {
    if (panel) {
      onReupload?.(panel);
    }
  };

  const getImageInfo = () => {
    if (!panel || !panel.processedFile) return null;

    if (panel.fileInfo) {
      return {
        size: `${(panel.fileInfo.size / (1024 * 1024)).toFixed(2)} MB`,
        resolution: `${panel.fileInfo.width} x ${panel.fileInfo.height} px`,
        format: panel.fileInfo.format
      };
    }

    return {
      size: '--',
      resolution: '--',
      format: '--'
    };
  };

  // 状态文本
  const getStatusText = () => {
    return '上传完成';
  };

  // 获取面板标题 - 根据页面类型
  const getPanelTitle = () => {
    // 细节还原页面显示服装
    if (pageType === 'detail-migration') {
      return '服装';
    }
    if (panel && panel.title) {
      return panel.title; // 如果面板本身已经有标题，优先使用
    }
    return '模特原图';
  };

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          <ThumbnailWithPreview
            imageUrl={panel.processedFile || panel.url}
            alt={`${getPanelTitle()} 上传结果`}
            status={panel.status}
            error={panel.error}
            onStatusChange={onStatusChange}
            featureName={pageType === 'detail-migration' ? '服装预览' : '模特原图预览'}
          />
          <div className="component-text">
            <h3>{getPanelTitle()}</h3>
            <div className="component-content">
              <p>
                {panel.status === 'completed' && (
                  <>
                    {getStatusText()}
                    {/* 蒙版相关提示 - 仅在换模特页面隐藏
                        如需在换模特页面也显示蒙版功能，请删除 pageType !== 'change-model' 条件 */}
                    {pageType !== 'change-model' && (
                      panel.hasMask ? (
                        <span className="mask-status">已手动绘制蒙版</span>
                      ) : pageType === 'optimize' || pageType === 'detail-migration' || pageType === 'try-on-other' ? (
                        <span className="mask-status warning">需要手动绘制蒙版</span>
                      ) : (
                        <span className="mask-status">将自动绘制蒙版</span>
                      )
                    )}
                    {/* 蒙版提示功能结束 */}
                  </>
                )}
                {panel.status === 'processing' && '处理中...'}
                {panel.status === 'error' && panel.error}
              </p>
            </div>
          </div>
        </div>
        <button 
          className={`expand-btn ${isActive ? 'active' : ''}`}
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

ClothingMaskPanel.propTypes = {
  panel: PropTypes.shape({
    // 只支持componentId作为标准ID
    componentId: PropTypes.string.isRequired,
    title: PropTypes.string,
    status: PropTypes.oneOf(['processing', 'completed', 'error']).isRequired,
    error: PropTypes.string,
    processedFile: PropTypes.string,
    url: PropTypes.string,
    serverFileName: PropTypes.string,
    originalImage: PropTypes.string,
    // 蒙版相关属性 - 临时隐藏但保留以便将来扩展
    hasMask: PropTypes.bool,
    fileInfo: PropTypes.shape({
      size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
      width: PropTypes.number,
      height: PropTypes.number,
      format: PropTypes.string,
      serverFileName: PropTypes.string
    }),
    showOriginal: PropTypes.bool,
    type: PropTypes.string,
    source: PropTypes.string,
  }).isRequired,
  onExpandClick: PropTypes.func.isRequired,
  onDelete: PropTypes.func,
  onReupload: PropTypes.func,
  onStatusChange: PropTypes.func,
  // 蒙版绘制功能 - 临时隐藏但保留以便将来扩展
  onDrawMask: PropTypes.func,
  isActive: PropTypes.bool,
  isEnhanced: PropTypes.bool,
  onPanelsChange: PropTypes.func,
  pageType: PropTypes.string,
};

export default ClothingMaskPanel; 