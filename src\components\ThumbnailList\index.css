.thumbnail-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  overflow-y: auto;
  max-height: 50vh;
}

.thumbnail-card {
  width: 100%;
  aspect-ratio: 3/4;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.thumbnail-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid transparent;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.thumbnail-card:hover::after {
  border-color: rgba(255, 60, 106, 0.5);
}

.thumbnail-card.active::after {
  border-color: #FF3C6A;
}

.thumbnail-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
} 