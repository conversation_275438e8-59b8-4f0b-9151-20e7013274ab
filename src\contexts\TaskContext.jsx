import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react';
import audioService from '../services/AudioService';

const TaskContext = createContext();

export const TaskProvider = ({ children }) => {
  const [tasks, setTasks] = useState([]);
  const [completedTaskIds, setCompletedTaskIds] = useState(new Set());
  const [failedTaskIds, setFailedTaskIds] = useState(new Set());
  // 使用ref跟踪已完成和失败的任务ID，避免依赖循环
  const completedTaskIdsRef = useRef(completedTaskIds);
  const failedTaskIdsRef = useRef(failedTaskIds);
  
  // 更新ref值
  useEffect(() => {
    completedTaskIdsRef.current = completedTaskIds;
  }, [completedTaskIds]);
  
  useEffect(() => {
    failedTaskIdsRef.current = failedTaskIds;
  }, [failedTaskIds]);
  
  // 处理新任务完成
  const handleTaskCompleted = (taskId) => {
    if (!completedTaskIdsRef.current.has(taskId)) {
      // 播放提示音
      audioService.playTaskComplete();
      
      // 更新已完成任务ID集合
      setCompletedTaskIds(prev => {
        const newSet = new Set(prev);
        newSet.add(taskId);
        return newSet;
      });
    }
  };
  
  // 处理新任务失败
  const handleTaskFailed = (taskId) => {
    if (!failedTaskIdsRef.current.has(taskId)) {
      // 播放错误提示音
      audioService.playTaskError();
      
      // 更新失败任务ID集合
      setFailedTaskIds(prev => {
        const newSet = new Set(prev);
        newSet.add(taskId);
        return newSet;
      });
    }
  };
  
  // 监控任务状态变化 - 移除这个导致循环的useEffect
  // useEffect(() => {
  //   // 检查是否有新的已完成任务
  //   tasks.forEach(task => {
  //     if (task.status === 'completed' && task.taskId && !completedTaskIds.has(task.taskId)) {
  //       handleTaskCompleted(task.taskId);
  //     }
  //   });
  // }, [tasks, completedTaskIds]);
  
  // 更新任务列表
  const updateTasks = useCallback((newTasks) => {
    setTasks(newTasks);
  }, []);
  
  // 添加单个任务
  const addTask = useCallback((task) => {
    setTasks(prev => [...prev, task]);
  }, []);
  
  // 更新单个任务 - 使用useCallback优化
  const updateTask = useCallback((updatedTask) => {
    if (!updatedTask || !updatedTask.taskId) return;
    
    setTasks(prev => {
      // 检查任务是否已存在
      const existingTaskIndex = prev.findIndex(task => task.taskId === updatedTask.taskId);
      
      // 如果任务已存在且状态相同，不进行更新
      if (existingTaskIndex >= 0 && prev[existingTaskIndex].status === updatedTask.status) {
        return prev;
      }
      
      // 创建新的任务数组
      const newTasks = [...prev];
      
      if (existingTaskIndex >= 0) {
        // 更新现有任务
        newTasks[existingTaskIndex] = updatedTask;
      } else {
        // 添加新任务
        newTasks.push(updatedTask);
      }
      
      return newTasks;
    });
    
    // 如果任务状态变为已完成，播放提示音
    if (updatedTask.status === 'completed' && updatedTask.taskId && !completedTaskIdsRef.current.has(updatedTask.taskId)) {
      handleTaskCompleted(updatedTask.taskId);
    }
    
    // 如果任务状态变为失败，播放错误提示音
    if (updatedTask.status === 'failed' && updatedTask.taskId && !failedTaskIdsRef.current.has(updatedTask.taskId)) {
      handleTaskFailed(updatedTask.taskId);
    }
  }, []);
  
  // 音频控制功能
  const toggleMute = useCallback(() => {
    return audioService.toggleMute();
  }, []);
  
  const isSoundMuted = useCallback(() => {
    return audioService.isSoundMuted();
  }, []);
  
  return (
    <TaskContext.Provider 
      value={{
        tasks,
        updateTasks,
        addTask,
        updateTask,
        toggleMute,
        isSoundMuted
      }}
    >
      {children}
    </TaskContext.Provider>
  );
};

// 自定义Hook便于使用TaskContext
export const useTaskContext = () => {
  const context = useContext(TaskContext);
  if (!context) {
    throw new Error('useTaskContext must be used within a TaskProvider');
  }
  return context;
};

export default TaskContext; 