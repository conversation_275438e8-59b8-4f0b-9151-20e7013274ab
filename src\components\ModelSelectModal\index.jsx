import React, { useState, useEffect, memo } from 'react';
import { MdClose, MdZoomOutMap } from 'react-icons/md';
import BaseModal from '../common/BaseModal';
import ImagePreviewModal from '../common/ImagePreviewModal';
import '../../styles/close-buttons.css';
import { PRESET_MODELS, getModelImagePath, getFilteredModels } from '../../data/models';
import { 
  GENDER_CATEGORIES, 
  AGE_CATEGORIES, 
  REGION_CATEGORIES, 
  BODY_TYPE_CATEGORIES 
} from '../../data/categories';
import './index.css';


// 将模特数据的计算提升到组件外部，只计算一次
const PRESET_MODEL_LIST = PRESET_MODELS.map(model => ({
  ...model,
  image: getModelImagePath(model.id),
  preview: getModelImagePath(model.id)
}));

// 骨架屏组件
const ModelSkeleton = () => (
  <div className="card-skeleton">
    {Array(12).fill(null).map((_, index) => (
      <div key={index} className="card-skeleton-item">
        <div className="card-skeleton-image"></div>
        <div className="card-skeleton-caption">
          <div className="card-skeleton-text"></div>
          <div className="card-skeleton-button"></div>
        </div>
      </div>
    ))}
  </div>
);

// 抽离模特列表为独立组件并使用 memo 包装
const ModelGrid = memo(({ models, selectedModelId, onModelSelect, onPreview }) => (
  <div className="models-grid">
    {models.map((model) => (
      <div 
        key={model.id} 
        className={`card-item ${selectedModelId === model.id ? 'selected' : ''}`}
        onClick={() => onModelSelect(model)}
      >
        <div className="card-preview">
          <img 
            src={model.image} 
            alt={`模特 ${model.id}`}
            loading="lazy"
          />
        </div>
        <div className="card-caption">
          <div className="card-info">
            <span className="card-number">{model.id === 'random' ? '#0' : `#${model.id}`}</span>
            <span className="card-name">{model.name}</span>
          </div>
          {model.id !== 'random' && (
            <button 
              className="preview-button"
              onClick={(e) => {
                e.stopPropagation();
                onPreview(model.id);
              }}
              title="放大预览"
            >
              <MdZoomOutMap />
            </button>
          )}
        </div>
      </div>
    ))}
  </div>
));

const ModelSelectModal = ({ 
  onClose, 
  onSelect, 
  selectedModelId: propSelectedModelId,
  savedSettings,
  onSettingsChange,
  pageType, // 添加 pageType 参数
  hasCustomModelPrompt // 添加已回填状态参数
}) => {
  const [activeTab, setActiveTab] = useState(pageType === 'virtual' ? 'random' : 'preset');
  const [selectedModelId, setSelectedModelId] = useState(propSelectedModelId || 'random');
  const [isLoading, setIsLoading] = useState(true);
  
  // 预设模特页面的筛选状态
  const [presetFilters, setPresetFilters] = useState({
    gender: 'all',
    age: 'all',
    region: 'all',
    bodyType: 'all'
  });

  // 如果没有选中模特，默认选择随机模特
  useEffect(() => {
    if (!propSelectedModelId && activeTab === 'preset') {
      // 创建随机模特对象
      const randomModel = {
        id: 'random',
        name: '随机模特',
        type: 'random',
        gender: 'all',
        age: 'all',
        region: 'all',
        bodyType: 'all',
        image: '/images/icons/model-random.png',
        preview: '/images/icons/model-random.png',
      };
      
      // 设置为随机模特
      setSelectedModelId('random');
      onSettingsChange?.(randomModel);
      onSelect?.(randomModel);
    }
  }, []);

  // 从savedSettings中恢复提示词
  const [promptText, setPromptText] = useState(savedSettings?.description || '');
  const [negativePromptText, setNegativePromptText] = useState(savedSettings?.negativeDescription || '');
  const [previewImage, setPreviewImage] = useState(null);

  // 添加 ESC 键监听
  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === 'Escape' && previewImage) {
        setPreviewImage(null);
      }
    };

    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [previewImage]);

  // 当prop更新时同步内部状态
  useEffect(() => {
    setSelectedModelId(propSelectedModelId);
  }, [propSelectedModelId]);

  // 模拟加载完成
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 300);
    return () => clearTimeout(timer);
  }, []);

  const handleFilterChange = (filterType, value) => {
    // 只处理预设模特部分的筛选
    if (activeTab === 'preset') {
      setPresetFilters(prev => ({
        ...prev,
        [filterType]: value
      }));
    }
  };

  const handlePreview = (modelId) => {
    setPreviewImage(getModelImagePath(modelId));
  };

  // 获取筛选后的模特列表
  const filteredModels = (() => {
    // 创建随机模特对象
    const randomModel = {
      id: 'random',
      name: '随机模特',
      type: 'random',
      gender: 'all',
      age: 'all',
      region: 'all',
      bodyType: 'all',
      image: '/images/icons/model-random.png',
      preview: '/images/icons/model-random.png',
    };

    // 获取筛选后的常规模特列表
    const regularModels = getFilteredModels(presetFilters).map(model => {
      const completeModel = PRESET_MODEL_LIST.find(m => m.id === model.id);
      return completeModel;
    });

    // 将随机模特添加到列表最前面
    return [randomModel, ...regularModels];
  })();

  // 确保选中的模特信息完整
  const selectedModel = selectedModelId ? 
    PRESET_MODEL_LIST.find(model => model.id === selectedModelId) : 
    null;

  const handleModelSelect = (model) => {
    setSelectedModelId(model.id);
    
    // 处理随机模特的情况
    if (model.id === 'random') {
      const randomModelSettings = {
        ...model,
        type: 'random',
        preview: model.preview
      };
      onSettingsChange?.(randomModelSettings);
      onSelect?.(randomModelSettings);
      return;
    }
    
    // 处理常规预设模特
    const completeModel = {
      ...model,
      type: 'preset',
      preview: model.preview || getModelImagePath(model.id)
    };
    onSettingsChange?.(completeModel);
    onSelect?.(completeModel);
  };

  const handlePromptChange = (e) => {
    setPromptText(e.target.value);
  };

  const handleNegativePromptChange = (e) => {
    setNegativePromptText(e.target.value);
  };

  const handleSaveRandomSettings = () => {
    if (promptText.trim()) {
      const customModelId = `custom_${Date.now()}`;
      // 不再从筛选器生成标签数组
      const tags = [];
      
      const settings = {
        id: customModelId,
        name: '自定义模特',
        type: 'custom',
        filters: {
          gender: 'all',
          age: 'all',
          region: 'all',
          bodyType: 'all'
        },
        description: promptText,
        negativeDescription: negativePromptText.trim(),
        preview: '/images/icons/model-custom.png',
        image: '/images/icons/model-custom.png',
        prompt: promptText,
        negative_prompt: negativePromptText.trim(),
        tags: tags // 空标签数组
      };
      // 更新临时保存的设置
      onSettingsChange?.(settings);
      onSelect?.(settings);
    }
  };

  const handleClear = () => {
    setPromptText('');
    setNegativePromptText('');
    
    // 创建随机模特对象
    const randomModel = {
      id: 'random',
      name: '随机模特',
      type: 'random',
      gender: 'all',
      age: 'all',
      region: 'all',
      bodyType: 'all',
      image: '/images/icons/model-random.png',
      preview: '/images/icons/model-random.png',
    };
    
    // 设置为随机模特
    setSelectedModelId('random');
    onSettingsChange?.(randomModel);
    onSelect?.(randomModel);
  };

  const tabs = pageType === 'virtual' ? [
    { key: 'random', label: '自定义模特' }
  ] : [
    { key: 'preset', label: '预设模特' },
    { key: 'random', label: '自定义模特' },
    { key: 'custom', label: '虚拟模特' }
  ];

  const footer = (
    <>
      <button 
        className="clear-btn"
        onClick={handleClear}
      >
        清空内容
      </button>
      <button 
        className="save-settings-btn"
        onClick={() => {
          if (activeTab === 'preset' && selectedModelId) {
            // 处理随机模特的情况
            if (selectedModelId === 'random') {
              onClose();
            } else {
              // 处理常规预设模特的情况
              const selectedModel = PRESET_MODEL_LIST.find(model => model.id === selectedModelId);
              if (selectedModel) {
                onClose();
              }
            }
          } else if (activeTab === 'random') {
            handleSaveRandomSettings();
            onClose();
          }
        }}
        disabled={
          (activeTab === 'preset' && !selectedModelId) ||
          (activeTab === 'random' && !promptText.trim())
        }
      >
        确认设置
      </button>
    </>
  );

  return (
    <>
      <BaseModal
        className="model-select-modal"
        onClose={onClose}
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        footer={footer}
        size="large"
      >
        {activeTab === 'preset' ? (
          <>
            <div className="filter-section">
              {/* 性别筛选 */}
              <div className="filter-group">
                <label>性别</label>
                <div className="filter-options">
                  <button 
                    className={`filter-option ${presetFilters.gender === 'all' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('gender', 'all')}
                  >
                    全部
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.gender === 'female' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('gender', 'female')}
                  >
                    女性
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.gender === 'male' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('gender', 'male')}
                  >
                    男性
                  </button>
                </div>
              </div>

              {/* 年龄筛选 */}
              <div className="filter-group">
                <label>年龄</label>
                <div className="filter-options">
                  <button 
                    className={`filter-option ${presetFilters.age === 'all' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('age', 'all')}
                  >
                    全部
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.age === 'adult' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('age', 'adult')}
                  >
                    成人
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.age === 'teen' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('age', 'teen')}
                  >
                    少年
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.age === 'bigKid' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('age', 'bigKid')}
                  >
                    大童
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.age === 'midKid' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('age', 'midKid')}
                  >
                    中童
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.age === 'smallKid' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('age', 'smallKid')}
                  >
                    小童
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.age === 'baby' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('age', 'baby')}
                  >
                    婴幼儿
                  </button>
                </div>
              </div>

              {/* 地区筛选 */}
              <div className="filter-group">
                <label>地区</label>
                <div className="filter-options">
                  <button 
                    className={`filter-option ${presetFilters.region === 'all' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('region', 'all')}
                  >
                    全部
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.region === 'western' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('region', 'western')}
                  >
                    欧美
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.region === 'eastAsia' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('region', 'eastAsia')}
                  >
                    东亚
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.region === 'africa' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('region', 'africa')}
                  >
                    非洲
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.region === 'latinAmerica' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('region', 'latinAmerica')}
                  >
                    拉美
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.region === 'middleEast' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('region', 'middleEast')}
                  >
                    中东
                  </button>
                </div>
              </div>

              {/* 身材筛选 */}
              <div className="filter-group">
                <label>身材</label>
                <div className="filter-options">
                  <button 
                    className={`filter-option ${presetFilters.bodyType === 'all' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('bodyType', 'all')}
                  >
                    全部
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.bodyType === 'standard' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('bodyType', 'standard')}
                  >
                    匀称型
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.bodyType === 'athletic' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('bodyType', 'athletic')}
                  >
                    运动型
                  </button>
                  <button 
                    className={`filter-option ${presetFilters.bodyType === 'plus' ? 'active' : ''}`}
                    onClick={() => handleFilterChange('bodyType', 'plus')}
                  >
                    丰满型
                  </button>
                </div>
              </div>
            </div>

            {isLoading ? (
              <ModelSkeleton />
            ) : (
              <ModelGrid 
                models={filteredModels}
                selectedModelId={selectedModelId}
                onModelSelect={handleModelSelect}
                onPreview={handlePreview}
              />
            )}
          </>
        ) : activeTab === 'random' ? (
          <div className="custom-model">
            {/* 提示词输入 */}
            <div className="filter-section">
              <h3 className="section-title">
                填写模特描述提示词
                {hasCustomModelPrompt && (
                  <span style={{ 
                    fontSize: '13px', 
                    color: 'var(--brand-primary)', 
                    marginLeft: '8px',
                    fontWeight: 'normal' 
                  }}>
                    (已回填上次提示词)
                  </span>
                )}
              </h3>
              <div className="prompt-input">
                <label>正面提示词</label>
                <textarea
                  placeholder="描述您期望的模特特征，例如：欧美女性，身高170cm，长发，微笑，甜美气质..."
                  rows={4}
                  className="prompt-textarea"
                  value={promptText}
                  onChange={handlePromptChange}
                />
              </div>
              <div className="prompt-input">
                <label>负面提示词（可选）</label>
                <textarea
                  placeholder="描述您不希望出现的特征，例如：年龄偏大、发型凌乱、表情夸张..."
                  rows={4}
                  className="prompt-textarea"
                  value={negativePromptText}
                  onChange={handleNegativePromptChange}
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="custom-models">
            <div className="empty-custom">
              <img src="https://file.aibikini.cn/config/icons/coming-soon.png" alt="开发中" className="coming-soon-icon" />
              <h3>虚拟模特功能开发中</h3>
              <p>敬请期待您的专属数字分身</p>
            </div>
          </div>
        )}
      </BaseModal>

      {/* 使用新的独立ImagePreviewModal组件 */}
      <ImagePreviewModal
        visible={!!previewImage}
        imageUrl={previewImage}
        onClose={() => setPreviewImage(null)}
        alt="模特预览"
        showHint={true}
        maxScale={4}
        minScale={0.5}
      />
    </>
  );
};

export default ModelSelectModal; 