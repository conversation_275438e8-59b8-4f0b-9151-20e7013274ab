.image-zoom-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px;
  background: transparent;
  width: 76px;
}

/* 百分比显示框 */
.zoom-scale {
  font-size: 13px;
  color: var(--text-secondary, #666);
  font-weight: 500;
  text-align: center;
  width: 76px;
  height: 34px;
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary, white);
  margin-bottom: 2px;
}

/* 放大缩小按钮容器 */
.zoom-buttons {
  display: flex;
  flex-direction: column;
  width: 76px;
  gap: 6px;
}

/* 放大缩小按钮行 */
.zoom-buttons-row {
  display: flex;
  justify-content: space-between;
  width: 76px;
}

/* 按钮基础样式 */
.zoom-button {
  height: 34px;
  border: 1px solid var(--border-light, #e8e8e8);
  border-radius: 6px;
  background: var(--bg-primary, #f5f5f5);
  color: var(--text-secondary, #666);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

/* 缩小和放大按钮样式 */
.zoom-button.zoom-in,
.zoom-button.zoom-out {
  width: 36px;
}

/* 复位按钮样式 */
.zoom-button.reset {
  width: 76px;
  margin-top: 0;
}

/* 预览按钮样式 */
.zoom-button.preview {
  width: 76px;
  margin-top: 0;
}

.zoom-button:hover:not(:disabled) {
  background: var(--bg-hover, #eee);
  color: var(--text-primary, #333);
  border-color: var(--border-color, #d0d0d0);
}

.zoom-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-button svg {
  width: 16px;
  height: 16px;
} 