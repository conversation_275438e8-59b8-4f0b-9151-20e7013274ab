import React from 'react';
import PropTypes from 'prop-types';
import { TbColorPicker } from 'react-icons/tb';
import './index.css';

// 默认图标路径，使用用户提供的图标
const DEFAULT_COLOR_ICON = 'https://file.aibikini.cn/config/icons/color.png';

const ColorPanel = ({
  selectedColor,
  onExpandClick,
  useCustomIcon = true, // 默认使用自定义图标
}) => {
  // 处理展开按钮点击，获取按钮位置
  const handleExpandClick = (e) => {
    const buttonRect = e.currentTarget.getBoundingClientRect();
    const panelRect = e.currentTarget.closest('.panel-component').getBoundingClientRect();
    
    // 计算弹窗位置，保持16px的水平间距
    let left = buttonRect.left + buttonRect.width + 16;
    
    // 防止弹窗右侧溢出屏幕
    // 假设弹窗宽度为450px (来自CSS)
    const windowWidth = window.innerWidth;
    if (left + 450 > windowWidth) {
      // 如果右侧会溢出，将弹窗放在左侧
      left = Math.max(20, buttonRect.left - 450 - 16);
    }
    
    // 弹窗位置：向上偏移180px，同时确保不会超出屏幕顶部
    const topPosition = Math.max(20, panelRect.top - 180);
    
    onExpandClick(null, {
      top: topPosition, // 向上偏移180px
      left: left // 动态计算的水平位置
    });
  };

  // 渲染默认图标
  const renderDefaultIcon = () => {
    if (useCustomIcon) {
      // 使用用户提供的自定义图标
      return <img src={DEFAULT_COLOR_ICON} alt="颜色选择" className="color-icon" />;
    } else {
      // 使用React Icons图标作为备选
      return <TbColorPicker size={40} className="color-picker-icon" />;
    }
  };

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          {selectedColor ? (
            // 当有选择颜色时，显示颜色预览
            <div className="color-preview-container">
              <div 
                className="color-preview-box" 
                style={{ backgroundColor: selectedColor }}
              />
            </div>
          ) : (
            // 无选择颜色时，显示默认图标
            <div className="color-icon-container">
              {renderDefaultIcon()}
            </div>
          )}
          <div className="component-text">
            <h3>颜色</h3>
            <div className="component-content">
              {selectedColor ? (
                <p>已选择色号 {selectedColor.toUpperCase()}</p>
              ) : (
                <p>选择要使用的颜色</p>
              )}
            </div>
          </div>
        </div>
        <button 
          className="expand-btn"
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

ColorPanel.propTypes = {
  selectedColor: PropTypes.string,
  onExpandClick: PropTypes.func.isRequired,
  useCustomIcon: PropTypes.bool,
};

export default ColorPanel; 