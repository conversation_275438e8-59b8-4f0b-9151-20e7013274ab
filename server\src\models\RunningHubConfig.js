/**
 * RunningHub配置数据模型
 */

const mongoose = require('mongoose');

const runningHubConfigSchema = new mongoose.Schema({
  // 配置名称
  name: {
    type: String,
    required: true,
    trim: true
  },
  
  // API密钥
  apiKey: {
    type: String,
    required: true,
    trim: true
  },
  
  // 工作流映射配置
  workflowMappings: {
    type: Map,
    of: String,
    default: new Map()
  },
  
  // 是否为默认配置
  isDefault: {
    type: Boolean,
    default: false
  },
  
  // 是否启用
  enabled: {
    type: Boolean,
    default: true
  },
  
  // 配置描述
  description: {
    type: String,
    trim: true
  },
  
  // 创建者（可选，用于向后兼容）
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false
  },

  // 是否为公共配置
  isPublic: {
    type: Boolean,
    default: false
  },
  
  // 最后测试时间
  lastTestedAt: {
    type: Date
  },
  
  // 测试状态
  testStatus: {
    type: String,
    enum: ['pending', 'success', 'failed'],
    default: 'pending'
  },
  
  // 测试错误信息
  testError: {
    type: String
  },
  
  // 账户信息缓存
  accountInfo: {
    balance: Number,
    memberType: String,
    totalUsage: Number,
    lastUpdated: Date
  },
  
  // 使用统计
  usage: {
    totalTasks: {
      type: Number,
      default: 0
    },
    successTasks: {
      type: Number,
      default: 0
    },
    failedTasks: {
      type: Number,
      default: 0
    },
    lastUsed: Date
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      return ret;
    }
  }
});

// 索引
runningHubConfigSchema.index({ createdBy: 1 });
runningHubConfigSchema.index({ isDefault: 1 });
runningHubConfigSchema.index({ enabled: 1 });
runningHubConfigSchema.index({ isPublic: 1 });
runningHubConfigSchema.index({ isPublic: 1, enabled: 1 });

// 静态方法：获取默认配置
runningHubConfigSchema.statics.getDefaultConfig = async function(userId = null) {
  // 优先查找用户的私有默认配置
  if (userId) {
    const userConfig = await this.findOne({
      createdBy: userId,
      isDefault: true,
      enabled: true
    });
    if (userConfig) {
      return userConfig;
    }
  }

  // 如果没有用户配置，查找公共默认配置
  return await this.findOne({
    isPublic: true,
    isDefault: true,
    enabled: true
  });
};

// 静态方法：设置默认配置
runningHubConfigSchema.statics.setDefaultConfig = async function(configId, userId = null) {
  const config = await this.findById(configId);
  if (!config) {
    throw new Error('配置不存在');
  }

  if (config.isPublic) {
    // 公共配置：取消所有公共默认配置
    await this.updateMany(
      { isPublic: true },
      { isDefault: false }
    );
  } else if (userId) {
    // 私有配置：取消该用户的所有默认配置
    await this.updateMany(
      { createdBy: userId },
      { isDefault: false }
    );
  }

  // 设置新的默认配置
  return await this.findByIdAndUpdate(
    configId,
    { isDefault: true },
    { new: true }
  );
};

// 静态方法：获取用户可用的所有配置
runningHubConfigSchema.statics.getAvailableConfigs = async function(userId = null, enabledOnly = true) {
  const query = {};

  if (enabledOnly) {
    query.enabled = true;
  }

  // 构建查询条件：公共配置 + 用户私有配置
  const orConditions = [{ isPublic: true }];
  if (userId) {
    orConditions.push({ createdBy: userId, isPublic: { $ne: true } });
  }

  query.$or = orConditions;

  return await this.find(query).sort({ 'usage.totalTasks': 1 });
};

// 实例方法：获取完整API密钥（仅用于内部调用）
runningHubConfigSchema.methods.getFullApiKey = function() {
  return this.apiKey;
};

// 实例方法：更新使用统计
runningHubConfigSchema.methods.updateUsage = async function(success = true) {
  this.usage.totalTasks += 1;
  if (success) {
    this.usage.successTasks += 1;
  } else {
    this.usage.failedTasks += 1;
  }
  this.usage.lastUsed = new Date();
  return await this.save();
};

// 实例方法：更新账户信息
runningHubConfigSchema.methods.updateAccountInfo = async function(accountData) {
  this.accountInfo = {
    ...accountData,
    lastUpdated: new Date()
  };
  return await this.save();
};

// 实例方法：获取工作流ID
runningHubConfigSchema.methods.getWorkflowId = function(workflowName) {
  return this.workflowMappings.get(workflowName);
};

// 实例方法：设置工作流映射
runningHubConfigSchema.methods.setWorkflowMapping = async function(workflowName, workflowId) {
  this.workflowMappings.set(workflowName, workflowId);
  return await this.save();
};

// 实例方法：批量设置工作流映射
runningHubConfigSchema.methods.setWorkflowMappings = async function(mappings) {
  Object.entries(mappings).forEach(([key, value]) => {
    this.workflowMappings.set(key, value);
  });
  return await this.save();
};

module.exports = mongoose.model('RunningHubConfig', runningHubConfigSchema);
