/**
 * RunningHub配置数据模型
 */

const mongoose = require('mongoose');

const runningHubConfigSchema = new mongoose.Schema({
  // 配置名称
  name: {
    type: String,
    required: true,
    trim: true
  },
  
  // API密钥
  apiKey: {
    type: String,
    required: true,
    trim: true
  },
  
  // 工作流映射配置
  workflowMappings: {
    type: Map,
    of: String,
    default: new Map()
  },
  
  // 是否为默认配置
  isDefault: {
    type: Boolean,
    default: false
  },
  
  // 是否启用
  enabled: {
    type: Boolean,
    default: true
  },
  
  // 配置描述
  description: {
    type: String,
    trim: true
  },
  
  // 创建者
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // 最后测试时间
  lastTestedAt: {
    type: Date
  },
  
  // 测试状态
  testStatus: {
    type: String,
    enum: ['pending', 'success', 'failed'],
    default: 'pending'
  },
  
  // 测试错误信息
  testError: {
    type: String
  },
  
  // 账户信息缓存
  accountInfo: {
    balance: Number,
    memberType: String,
    totalUsage: Number,
    lastUpdated: Date
  },
  
  // 使用统计
  usage: {
    totalTasks: {
      type: Number,
      default: 0
    },
    successTasks: {
      type: Number,
      default: 0
    },
    failedTasks: {
      type: Number,
      default: 0
    },
    lastUsed: Date
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      return ret;
    }
  }
});

// 索引
runningHubConfigSchema.index({ createdBy: 1 });
runningHubConfigSchema.index({ isDefault: 1 });
runningHubConfigSchema.index({ enabled: 1 });

// 静态方法：获取默认配置
runningHubConfigSchema.statics.getDefaultConfig = async function(userId) {
  return await this.findOne({ 
    createdBy: userId, 
    isDefault: true, 
    enabled: true 
  });
};

// 静态方法：设置默认配置
runningHubConfigSchema.statics.setDefaultConfig = async function(configId, userId) {
  // 先取消所有默认配置
  await this.updateMany(
    { createdBy: userId },
    { isDefault: false }
  );
  
  // 设置新的默认配置
  return await this.findByIdAndUpdate(
    configId,
    { isDefault: true },
    { new: true }
  );
};

// 实例方法：获取完整API密钥（仅用于内部调用）
runningHubConfigSchema.methods.getFullApiKey = function() {
  return this.apiKey;
};

// 实例方法：更新使用统计
runningHubConfigSchema.methods.updateUsage = async function(success = true) {
  this.usage.totalTasks += 1;
  if (success) {
    this.usage.successTasks += 1;
  } else {
    this.usage.failedTasks += 1;
  }
  this.usage.lastUsed = new Date();
  return await this.save();
};

// 实例方法：更新账户信息
runningHubConfigSchema.methods.updateAccountInfo = async function(accountData) {
  this.accountInfo = {
    ...accountData,
    lastUpdated: new Date()
  };
  return await this.save();
};

// 实例方法：获取工作流ID
runningHubConfigSchema.methods.getWorkflowId = function(workflowName) {
  return this.workflowMappings.get(workflowName);
};

// 实例方法：设置工作流映射
runningHubConfigSchema.methods.setWorkflowMapping = async function(workflowName, workflowId) {
  this.workflowMappings.set(workflowName, workflowId);
  return await this.save();
};

// 实例方法：批量设置工作流映射
runningHubConfigSchema.methods.setWorkflowMappings = async function(mappings) {
  Object.entries(mappings).forEach(([key, value]) => {
    this.workflowMappings.set(key, value);
  });
  return await this.save();
};

module.exports = mongoose.model('RunningHubConfig', runningHubConfigSchema);
