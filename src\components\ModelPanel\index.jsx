import React from 'react';
import PropTypes from 'prop-types';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

const ModelPanel = ({
  selectedModel,
  onExpandClick,
  pageType = 'default',
}) => {
  const getDisplayText = () => {
    if (!selectedModel) {
      if (pageType === 'virtual') {
        return '设置虚拟模特的自定义要求';
      }
      return '选择合适的模特';
    } else {
      if (pageType === 'virtual') {
        return '已设置自定义要求';
      }
      return `已选择 ${selectedModel.type === 'custom' ? '自定义模特' : `#${selectedModel.componentId} ${selectedModel.name}`}`;
    }
  };

  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          {selectedModel ? (
            <ThumbnailWithPreview
              imageUrl={selectedModel.type === 'custom' ? 
                '/images/icons/model-custom.png' : 
                (selectedModel.preview || '/images/icons/model.png')
              }
              alt={`模特 ${selectedModel.name || '自定义模特'}`}
              status="completed"
              featureName="模特预览"
            />
          ) : (
            <img src="/images/icons/model.png" alt="模特" className="component-icon" />
          )}
          <div className="component-text">
            <h3>模特</h3>
            <div className="component-content">
              <p>{getDisplayText()}</p>
            </div>
          </div>
        </div>
        <div className="panel-actions">
          <button 
            className="expand-btn"
            onClick={onExpandClick}
            title={pageType === 'virtual' ? "设置自定义要求" : "选择模特"}
          >
            <span></span>
          </button>
        </div>
      </div>
    </div>
  );
};

ModelPanel.propTypes = {
  selectedModel: PropTypes.shape({
    componentId: PropTypes.string,
    name: PropTypes.string,
    type: PropTypes.string,
    source: PropTypes.string,
    preview: PropTypes.string
  }),
  onExpandClick: PropTypes.func.isRequired,
  pageType: PropTypes.string,
};

export default ModelPanel; 