/* 虚拟模特页面样式 */
@import '../../../styles/scrollbars.css';
@import '../../../styles/buttons.css';
@import '../../../styles/common.css';

.virtual-page {
  width: 100%;
  height: calc(100vh - 68px);  /* 减去导航栏高度 */
  background: var(--bg-secondary);
  overflow: auto;
}

.virtual-container {
  display: flex;
  height: 100%;
  gap: 0;
  padding: 12px;
  user-select: none;
  overflow: auto;
}

/* 
  注意：.generation-area 相关样式已移至 src/components/GenerationArea/index.css
  这样可以确保样式的一致性，并避免重复定义
*/

/* 右侧生成区样式 */
.generation-area {
  flex: 1;
  margin-left: 12px;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
}

.result-header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 0 20px;
  margin: 0 -20px;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 8px;
  position: relative;
}

.result-header h2 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0;
  font-weight: 500;
}

.result-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 6px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  cursor: pointer;
  transition: var(--transition-normal);
}

.action-btn:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.empty-result {
  text-align: center;
  color: var(--text-tertiary);
  font-size: var(--font-size-md);
}

.showcase-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .virtual-container {
    flex-direction: column;
    padding: 8px;
    height: auto;
    min-height: calc(100vh - 76px);
  }
  
  .virtual-page {
    height: auto;
    min-height: calc(100vh - 60px);
  }
  
  /* 注意：.generation-area 的响应式样式已移至 GenerationArea 组件 */
}

/* 480px以下进一步优化 */
@media (max-width: 480px) {
  .virtual-container {
    padding: 6px;
    min-height: calc(100vh - 72px);
  }
  
  .virtual-page {
    min-height: calc(100vh - 60px);
  }
}

/* 360px以下极致优化 */
@media (max-width: 360px) {
  .virtual-container {
    padding: 4px;
    min-height: calc(100vh - 68px);
  }
  
  .virtual-page {
    min-height: calc(100vh - 60px);
  }
}

/* 开发中提示样式 */
.coming-soon {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
  color: var(--text-secondary);
}

.coming-soon-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 24px;
  opacity: 0.8;
}

.coming-soon h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0 0 12px;
  font-weight: 500;
}

.coming-soon p {
  font-size: var(--font-size-md);
  color: var(--text-tertiary);
  margin: 0;
  line-height: 1.5;
}

/* 添加上边距工具类 */
.mt-4 {
  margin-top: 16px;
}

/* 生成按钮样式 */
.generate-btn {
  width: 100%;
  padding: 12px 0;
  background: var(--brand-gradient);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-md);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--transition-normal);
}

/* 添加光效扫过效果 */
.generate-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s;
}

.generate-btn:hover:not(:disabled)::after {
  left: 100%;
}

.generate-btn:hover:not(:disabled) {
  background: var(--brand-gradient);
}

.generate-btn:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.7;
}

.generate-btn:disabled::after {
  display: none;
}

.control-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
  padding-bottom: 120px;
}

/* 调整服装上传区域的间距 */
.virtual-page .control-content .upload-area {
  margin-top: 0 !important; /* 覆盖mt-2类的设置 */
  padding-top: 0;
} 