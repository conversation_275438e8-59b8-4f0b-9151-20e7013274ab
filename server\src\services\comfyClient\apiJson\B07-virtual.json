{"2": {"inputs": {"width": 1536, "height": 1536, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "3": {"inputs": {"guidance": 15, "conditioning": ["4", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "4": {"inputs": {"strength": 0.8, "start_percent": 0, "end_percent": 0.4, "positive": ["24", 0], "negative": ["25", 0], "control_net": ["8", 0], "vae": ["23", 0], "image": ["42", 0]}, "class_type": "ControlNetApplySD3", "_meta": {"title": "Apply Controlnet with VAE"}}, "5": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": 1536, "height": 1536, "model": ["21", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "6": {"inputs": {"control_net_name": "flux1-dev-controlnet-union-pro2.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "7": {"inputs": {"seed": 172053136788837, "needInput": true, "steps": 25, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["5", 0], "positive": ["3", 0], "negative": ["4", 1], "latent_image": ["2", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "8": {"inputs": {"type": "openpose", "control_net": ["6", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "9": {"inputs": {"rows": 2, "columns": 3, "image": ["37", 0]}, "class_type": "FS: Crop Image Into Even Pieces", "_meta": {"title": "Crop Image Into Even Pieces (FS)"}}, "16": {"inputs": {"delimiter": ", ", "clean_whitespace": "true", "text_a": ["34", 0], "text_b": ["35", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "18": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "19": {"inputs": {"mode": "512x768"}, "class_type": "CoreMLDetailerHookProvider", "_meta": {"title": "CoreMLDetailerHookProvider"}}, "20": {"inputs": {"wildcard": "", "Select to add LoRA": "Select the LoRA to add to the text", "Select to add Wildcard": "Select the Wildcard to add to the text", "speak_and_recognation": {"__value__": [false, true]}, "model": ["21", 0], "clip": ["22", 0], "vae": ["23", 0], "positive": ["24", 0], "negative": ["25", 0], "bbox_detector": ["18", 0], "detailer_hook": ["19", 0]}, "class_type": "ToDetailerPipe", "_meta": {"title": "ToDetailerPipe"}}, "21": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "22": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "23": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "24": {"inputs": {"text": ["16", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["22", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "25": {"inputs": {"text": "", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["22", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "26": {"inputs": {"samples": ["7", 0], "vae": ["23", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "28": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": 12346, "steps": 20, "cfg": 1, "sampler_name": "deis", "scheduler": "beta", "denoise": 0.22, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 20, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 10, "refiner_ratio": 0.2, "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "tiled_encode": false, "tiled_decode": false, "image": ["9", 0], "detailer_pipe": ["20", 0]}, "class_type": "FaceDetailerPipe", "_meta": {"title": "FaceDetailer (pipe)"}}, "34": {"inputs": {"from_translate": "auto", "to_translate": "english", "add_proxies": false, "needInput": true, "proxies": "", "auth_data": "", "service": "GoogleTranslator", "text": "In this photograph, a young woman stands on a yacht, dressed in a chic, matching blue and white checkered bikini set . She wears white, heart-shaped sunglasses and has straight, light brown hair. The background features a lush, green hillside under a clear blue sky, with calm ocean water. The yacht is modern, white, and equipped with sleek lines. The image exudes a stylish, summery vibe.", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "提示词输入"}}, "35": {"inputs": {"String": "a character sheet, multiply poses and angles, including front view, side view, back view, portrait, it is a masterpiece, relistic photography, shot on iphone, smooth knee skin", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "String", "_meta": {"title": "String"}}, "36": {"inputs": {"model_name": "RealESRGAN_x4.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "37": {"inputs": {"upscale_by": 2, "seed": 384340151733828, "steps": 22, "cfg": 1, "sampler_name": "deis", "scheduler": "beta", "denoise": 0.2, "mode_type": "Linear", "tile_width": 1024, "tile_height": 1024, "mask_blur": 8, "tile_padding": 32, "seam_fix_mode": "None", "seam_fix_denoise": 1, "seam_fix_width": 64, "seam_fix_mask_blur": 8, "seam_fix_padding": 16, "force_uniform_tiles": true, "tiled_decode": false, "image": ["26", 0], "model": ["21", 0], "positive": ["4", 0], "negative": ["4", 1], "vae": ["23", 0], "upscale_model": ["36", 0]}, "class_type": "UltimateSDUpscale", "_meta": {"title": "Ultimate SD Upscale"}}, "41": {"inputs": {"filename_prefix": "Virtual", "images": ["28", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "42": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "骨骼图"}}}