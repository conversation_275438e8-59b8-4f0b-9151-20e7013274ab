/* 历史工具组件样式 */
@import '../../styles/theme.css';

.history-tools {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px 8px;
  background: var(--bg-primary);
  border-radius: 0;
  max-width: 100%;
}

.history-tools .tools-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-tools .tool-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  justify-content: center;
}

.history-tools .tool-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  width: auto;
  height: 36px;
  border-radius: var(--radius-sm);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-light);
  cursor: pointer;
  color: var(--text-secondary);
}

/* 历史工具按钮样式 */
.history-tool-btn,
.history-tool-btn:hover,
.history-tool-btn:active {
  transition: all 0.2s ease !important;
  position: static !important;
  top: auto !important;
  left: auto !important;
  margin: 0 !important;
  box-shadow: none !important;
  transform: none !important;
}

/* 普通按钮悬浮效果 */
.history-tools .tool-btn:hover:not(:disabled) {
  background-color: #eee !important;
  color: #333 !important;
  border-color: var(--border-light) !important;
}

/* 禁用按钮样式 */
.history-tools .tool-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 清除按钮特殊样式 */
.history-tools .clear-btn {
  margin-top: 8px;
  color: var(--error);
}

.history-tools .clear-btn:hover {
  background-color: var(--error-bg-light) !important;
  color: var(--error) !important;
  border-color: var(--error-border) !important;
}

.history-tools .tool-name {
  font-size: var(--font-size-sm);
  transition: color 0.2s ease !important;
} 