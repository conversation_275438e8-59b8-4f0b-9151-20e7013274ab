const workflow = {
  prompt: {
    "1": {
      "inputs": {
        "filename_prefix": "TryonManual",
        "images": [
          "18",
          0
        ]
      },
      "class_type": "SaveImage",
      "_meta": {
        "title": "Save Image"
      }
    },
    "2": {
      "inputs": {
        "guidance": 30,
        "conditioning": [
          "5",
          0
        ]
      },
      "class_type": "FluxGuidance",
      "_meta": {
        "title": "FluxGuidance"
      }
    },
    "3": {
      "inputs": {
        "strength": 1,
        "strength_type": "multiply",
        "conditioning": [
          "2",
          0
        ],
        "style_model": [
          "13",
          0
        ],
        "clip_vision_output": [
          "16",
          0
        ]
      },
      "class_type": "StyleModelApply",
      "_meta": {
        "title": "Apply Style Model"
      }
    },
    "4": {
      "inputs": {
        "text": "low quality,blurry,",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        },
        "clip": [
          "9",
          0
        ]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIP Text Encode (Prompt)"
      }
    },
    "5": {
      "inputs": {
        "text": [
          "41",
          0
        ],
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        },
        "clip": [
          "9",
          0
        ]
      },
      "class_type": "CLIPTextEncode",
      "_meta": {
        "title": "CLIP Text Encode (Prompt)"
      }
    },
    "6": {
      "inputs": {
        "model": [
          "24",
          0
        ]
      },
      "class_type": "DifferentialDiffusion",
      "_meta": {
        "title": "Differential Diffusion"
      }
    },
    "7": {
      "inputs": {
        "vae_name": "ae.safetensors"
      },
      "class_type": "VAELoader",
      "_meta": {
        "title": "Load VAE"
      }
    },
    "8": {
      "inputs": {
        "unet_name": "flux1-fill-dev.safetensors",
        "weight_dtype": "fp8_e4m3fn"
      },
      "class_type": "UNETLoader",
      "_meta": {
        "title": "Load Diffusion Model"
      }
    },
    "9": {
      "inputs": {
        "clip_name1": "clip_l.safetensors",
        "clip_name2": "t5xxl_fp16.safetensors",
        "type": "flux",
        "device": "default"
      },
      "class_type": "DualCLIPLoader",
      "_meta": {
        "title": "DualCLIPLoader"
      }
    },
    "10": {
      "inputs": {
        "text": [
          "19",
          0
        ],
        "old": "[prompt]",
        "new": [
          "20",
          0
        ],
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "Replace Text _O",
      "_meta": {
        "title": "Replace {Image1_desc}"
      }
    },
    "11": {
      "inputs": {
        "direction": "left-right",
        "pixels": 0,
        "image_1": [
          "25",
          0
        ],
        "image_2": [
          "14",
          0
        ],
        "mask_1": [
          "15",
          0
        ],
        "mask_2": [
          "15",
          0
        ]
      },
      "class_type": "easy makeImageForICLora",
      "_meta": {
        "title": "Make Image For ICLora"
      }
    },
    "12": {
      "inputs": {
        "clip_name": "sigclip_vision_patch14_384.safetensors"
      },
      "class_type": "CLIPVisionLoader",
      "_meta": {
        "title": "Load CLIP Vision"
      }
    },
    "13": {
      "inputs": {
        "style_model_name": "flux1-redux-dev.safetensors"
      },
      "class_type": "StyleModelLoader",
      "_meta": {
        "title": "Load Style Model"
      }
    },
    "14": {
      "inputs": {
        "width": [
          "25",
          1
        ],
        "height": [
          "25",
          2
        ],
        "interpolation": "lanczos",
        "method": "fill / crop",
        "condition": "always",
        "multiple_of": 0,
        "image": [
          "50",
          0
        ]
      },
      "class_type": "ImageResize+",
      "_meta": {
        "title": "🔧 Image Resize"
      }
    },
    "15": {
      "inputs": {
        "width": [
          "14",
          1
        ],
        "height": [
          "14",
          2
        ],
        "keep_proportions": false,
        "upscale_method": "bicubic",
        "crop": "disabled",
        "mask": [
          "50",
          1
        ]
      },
      "class_type": "ResizeMask",
      "_meta": {
        "title": "Resize Mask"
      }
    },
    "16": {
      "inputs": {
        "crop": "none",
        "clip_vision": [
          "12",
          0
        ],
        "image": [
          "25",
          0
        ]
      },
      "class_type": "CLIPVisionEncode",
      "_meta": {
        "title": "CLIP Vision Encode"
      }
    },
    "17": {
      "inputs": {
        "samples": [
          "27",
          0
        ],
        "vae": [
          "7",
          0
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE Decode"
      }
    },
    "18": {
      "inputs": {
        "width": [
          "25",
          1
        ],
        "height": [
          "25",
          2
        ],
        "x": [
          "25",
          1
        ],
        "y": 0,
        "image": [
          "17",
          0
        ]
      },
      "class_type": "ImageCrop",
      "_meta": {
        "title": "Image Crop"
      }
    },
    "19": {
      "inputs": {
        "text_1": [
          "22",
          0
        ],
        "text_2": [
          "23",
          0
        ]
      },
      "class_type": "LayerUtility: TextJoin",
      "_meta": {
        "title": "LayerUtility: TextJoin"
      }
    },
    "20": {
      "inputs": {
        "task": "more detailed caption",
        "text_input": "",
        "max_new_tokens": 1024,
        "num_beams": 3,
        "do_sample": false,
        "fill_mask": true,
        "florence2_model": [
          "21",
          0
        ],
        "image": [
          "25",
          0
        ]
      },
      "class_type": "LayerUtility: Florence2Image2Prompt",
      "_meta": {
        "title": "LayerUtility: Florence2 Image2Prompt(Advance)"
      }
    },
    "21": {
      "inputs": {
        "version": "large-PromptGen-v1.5"
      },
      "class_type": "LayerMask: LoadFlorence2Model",
      "_meta": {
        "title": "LayerMask: Load Florence2 Model(Advance)"
      }
    },
    "22": {
      "inputs": {
        "string": "this is a pair of images，the left side highlights [prompt]",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "String Literal",
      "_meta": {
        "title": "String Literal"
      }
    },
    "23": {
      "inputs": {
        "string": "the right girl put on this [cloth],",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "String Literal",
      "_meta": {
        "title": "String Literal"
      }
    },
    "24": {
      "inputs": {
        "lora_name": "Flux/comfyui_subject_lora16.safetensors",
        "strength_model": 1,
        "model": [
          "8",
          0
        ]
      },
      "class_type": "LoraLoaderModelOnly",
      "_meta": {
        "title": "LoraLoaderModelOnly"
      }
    },
    "25": {
      "inputs": {
        "width": 1026,
        "height": 1368,
        "interpolation": "lanczos",
        "method": "fill / crop",
        "condition": "always",
        "multiple_of": 0,
        "image": [
          "29",
          0
        ]
      },
      "class_type": "ImageResize+",
      "_meta": {
        "title": "🔧 Image Resize"
      }
    },
    "26": {
      "inputs": {
        "noise_mask": false,
        "positive": [
          "3",
          0
        ],
        "negative": [
          "4",
          0
        ],
        "vae": [
          "7",
          0
        ],
        "pixels": [
          "11",
          0
        ],
        "mask": [
          "11",
          1
        ]
      },
      "class_type": "InpaintModelConditioning",
      "_meta": {
        "title": "InpaintModelConditioning"
      }
    },
    "27": {
      "inputs": {
        "seed": 193930306732184,
        "steps": 25,
        "cfg": 1,
        "sampler_name": "euler",
        "scheduler": "simple",
        "denoise": 1,
        "model": [
          "6",
          0
        ],
        "positive": [
          "26",
          0
        ],
        "negative": [
          "26",
          1
        ],
        "latent_image": [
          "28",
          0
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "随机种子"
      }
    },
    "28": {
      "inputs": {
        "amount": 1,
        "samples": [
          "26",
          2
        ]
      },
      "class_type": "RepeatLatentBatch",
      "_meta": {
        "title": "图片数量"
      }
    },
    "29": {
      "inputs": {
        "fill_background": true,
        "background_color": "#FFFFFF",
        "RGBA_image": [
          "45",
          0
        ],
        "mask": [
          "47",
          0
        ]
      },
      "class_type": "LayerUtility: ImageRemoveAlpha",
      "_meta": {
        "title": "LayerUtility: ImageRemoveAlpha"
      }
    },
    "41": {
      "inputs": {
        "text": [
          "10",
          0
        ],
        "old": "[cloth]",
        "new": [
          "42",
          0
        ],
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "Replace Text _O",
      "_meta": {
        "title": "Replace {Image1_desc}"
      }
    },
    "42": {
      "inputs": {
        "String": "bra, panties",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "String",
      "_meta": {
        "title": "款式描述-服装"
      }
    },
    "45": {
      "inputs": {
        "threshold": 0.3,
        "detail_method": "VITMatte",
        "detail_erode": 6,
        "detail_dilate": 6,
        "black_point": 0.15,
        "white_point": 0.99,
        "process_detail": true,
        "prompt": [
          "42",
          0
        ],
        "device": "cuda",
        "max_megapixels": 2,
        "image": [
          "49",
          0
        ],
        "sam_models": [
          "46",
          0
        ]
      },
      "class_type": "LayerMask: SegmentAnythingUltra V3",
      "_meta": {
        "title": "LayerMask: SegmentAnythingUltra V3(Advance)"
      }
    },
    "46": {
      "inputs": {
        "sam_model": "sam_vit_h (2.56GB)",
        "grounding_dino_model": "GroundingDINO_SwinB (938MB)"
      },
      "class_type": "LayerMask: LoadSegmentAnythingModels",
      "_meta": {
        "title": "LayerMask: Load SegmentAnything Models(Advance)"
      }
    },
    "47": {
      "inputs": {
        "expand": 1,
        "tapered_corners": true,
        "mask": [
          "45",
          1
        ]
      },
      "class_type": "GrowMask",
      "_meta": {
        "title": "GrowMask"
      }
    },
    "49": {
      "inputs": {
        "url": "https://",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "服装图片上传"
      }
    },
    "50": {
      "inputs": {
        "url": "https://",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "模特图片上传+手动涂抹蒙版"
      }
    }
  }
};

module.exports = workflow; 