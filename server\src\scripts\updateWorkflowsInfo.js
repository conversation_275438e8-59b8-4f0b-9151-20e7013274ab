/**
 * 批量更新工作流信息脚本
 * 
 * 使用方法:
 * node server/src/scripts/updateWorkflowsInfo.js
 * 
 * 可选参数:
 * --dry-run: 仅显示将要更新的内容，不实际更新
 * --field=name: 只更新指定字段 (name, description, tags, priority)
 */

const mongoose = require('mongoose');
require('dotenv').config();

// 导入模型
const Workflow = require('../models/Workflow');

// 详细的工作流信息配置
const WORKFLOW_UPDATES = {
  'A01-trending': {
    name: '爆款开发',
    description: '基于市场趋势和流行元素，生成具有商业价值的爆款服装设计。分析当前时尚趋势，结合消费者喜好，创造具有市场潜力的服装款式。',
    tags: ['爆款', '趋势', '商业', '设计', '市场分析', 'AI设计'],
    priority: 90
  },
  'A02-optimize': {
    name: '款式优化',
    description: '对现有服装款式进行智能优化改进，提升设计质量和商业价值。通过AI分析优化设计细节、比例和整体效果。',
    tags: ['优化', '改进', '质量', '设计', '细节优化', '比例调整'],
    priority: 70
  },
  'A02b-optimizetext': {
    name: '文本优化',
    description: '优化服装设计相关的文本描述和标签，提升产品描述的准确性和吸引力。',
    tags: ['文本', '优化', '标签', '描述', '产品文案', 'SEO'],
    priority: 1
  },
  'A03-inspiration': {
    name: '灵感探索',
    description: '通过AI分析生成服装设计灵感和创意方向，为设计师提供多样化的创意启发和设计思路。',
    tags: ['灵感', '创意', '探索', '设计', '创意启发', '设计思路'],
    priority: 45
  },
  'A05-drawing': {
    name: '绘图设计',
    description: '生成专业的服装设计手绘图和技术图纸，包括款式图、结构图和细节图。',
    tags: ['绘图', '手绘', '技术图', '设计', '款式图', '结构图'],
    priority: 10
  },
  'A06-divergent': {
    name: '发散创意',
    description: '基于单一设计理念发散生成多种创意变体，探索不同的设计可能性和风格方向。',
    tags: ['发散', '创意', '变体', '多样性', '风格探索', '设计变化'],
    priority: 8
  },
  'B01-fashion': {
    name: '时尚大片',
    description: '生成高质量的时尚大片和产品展示图，提升服装的视觉表现力和商业价值。',
    tags: ['时尚', '大片', '展示', '摄影', '视觉效果', '商业摄影'],
    priority: 75
  },
  'B02-tryonauto': {
    name: '自动换装',
    description: '智能识别并自动进行服装试穿效果展示，快速生成不同服装搭配的试穿效果。',
    tags: ['试穿', '自动', '换装', 'AI', '智能识别', '快速试穿'],
    priority: 85
  },
  'B02-tryonmanual': {
    name: '手动换装',
    description: '手动指定区域进行精确的服装试穿，提供更精确的试穿效果和细节控制。',
    tags: ['试穿', '手动', '精确', '换装', '细节控制', '精准定位'],
    priority: 25
  },
  'B02-tryonother': {
    name: '其他换装',
    description: '其他类型的服装试穿和搭配展示，支持特殊场景和定制化试穿需求。',
    tags: ['试穿', '搭配', '展示', '其他', '特殊场景', '定制化'],
    priority: 1
  },
  'B03-changemodel': {
    name: '换模特',
    description: '更换模特同时保持服装效果一致，适用于不同模特类型的服装展示需求。',
    tags: ['换模特', '一致性', '展示', '模特替换', '效果保持'],
    priority: 40
  },
  'B04-recolor': {
    name: '重新配色',
    description: '对服装进行智能重新配色和色彩搭配，探索不同色彩方案的视觉效果。',
    tags: ['配色', '色彩', '搭配', '调色', '色彩方案', '视觉效果'],
    priority: 60
  },
  'B05-fabric': {
    name: '面料生成',
    description: '生成各种面料纹理和材质效果，为服装设计提供丰富的材质选择。',
    tags: ['面料', '纹理', '材质', '生成', '材质效果', '纹理设计'],
    priority: 35
  },
  'B06-background': {
    name: '背景处理',
    description: '更换或优化服装展示的背景环境，提升产品展示的整体效果和氛围。',
    tags: ['背景', '环境', '场景', '替换', '氛围营造', '展示效果'],
    priority: 50
  },
  'B07-virtual': {
    name: '虚拟展示',
    description: '生成虚拟服装展示和3D效果，提供沉浸式的服装展示体验。',
    tags: ['虚拟', '3D', '展示', '效果', '沉浸式', '虚拟现实'],
    priority: 15
  },
  'B08-detailmigration': {
    name: '细节迁移',
    description: '将服装细节从一个设计迁移到另一个，实现设计元素的智能融合和转移。',
    tags: ['细节', '迁移', '转移', '融合', '设计元素', '智能融合'],
    priority: 6
  },
  'B09-handfix': {
    name: '手部修复',
    description: '修复和优化模特手部的显示效果，提升整体图像的质量和专业度。',
    tags: ['手部', '修复', '优化', '细节', '图像质量', '专业度'],
    priority: 4
  },
  'B10-changeposture': {
    name: '姿态调整',
    description: '调整模特姿态和动作，创造更符合服装特点的展示效果。',
    tags: ['姿态', '动作', '调整', '变换', '展示效果', '动态调整'],
    priority: 2
  },
  'C01-extract': {
    name: '文本提取',
    description: '从图像中智能提取文本信息和标签，支持多语言文本识别和信息提取。',
    tags: ['提取', '文本', '识别', 'OCR', '多语言', '信息提取'],
    priority: 30
  },
  'C02-upscale': {
    name: '图像放大',
    description: '使用AI技术提升图像分辨率和质量，实现无损放大和细节增强。',
    tags: ['放大', '超分', '质量', '分辨率', '无损放大', '细节增强'],
    priority: 55
  },
  'C03-mattingbg': {
    name: '背景抠图',
    description: '智能抠除图像背景，支持复杂背景的精确分离和透明背景生成。',
    tags: ['抠图', '背景', '分离', '智能', '精确分离', '透明背景'],
    priority: 80
  },
  'C03-mattingbgfile': {
    name: '背景抠图(文件)',
    description: '批量处理图像背景抠除，支持大量图片的自动化背景处理。',
    tags: ['抠图', '批量', '文件', '处理', '自动化', '批量处理'],
    priority: 1
  },
  'C03-mattingclo': {
    name: '服装抠图',
    description: '精确抠除服装区域，专门针对服装图像的智能分离和提取。',
    tags: ['抠图', '服装', '精确', '分离', '服装提取', '智能分离'],
    priority: 1
  },
  'C04-extend': {
    name: '图像扩展',
    description: '智能扩展图像边界和内容，生成自然的图像延伸效果。',
    tags: ['扩展', '延伸', '补全', '生成', '边界扩展', '内容生成'],
    priority: 20
  }
};

class WorkflowUpdater {
  constructor() {
    this.dryRun = false;
    this.targetField = null;
  }

  // 解析命令行参数
  parseArgs() {
    const args = process.argv.slice(2);
    this.dryRun = args.includes('--dry-run');
    
    const fieldArg = args.find(arg => arg.startsWith('--field='));
    if (fieldArg) {
      this.targetField = fieldArg.split('=')[1];
    }
  }

  // 连接数据库
  async connectDB() {
    try {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/aibikini');
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error);
      process.exit(1);
    }
  }

  // 更新单个工作流
  async updateWorkflow(workflowId, updates) {
    try {
      const workflow = await Workflow.findOne({ id: workflowId });
      if (!workflow) {
        console.log(`⚠️  工作流不存在: ${workflowId}`);
        return { success: false, reason: 'not_found' };
      }

      // 准备更新数据
      const updateData = {};
      
      if (!this.targetField || this.targetField === 'name') {
        updateData.name = updates.name;
        updateData.displayName = updates.name;
      }
      
      if (!this.targetField || this.targetField === 'description') {
        updateData.description = updates.description;
      }
      
      if (!this.targetField || this.targetField === 'tags') {
        updateData.tags = updates.tags;
      }
      
      if (!this.targetField || this.targetField === 'priority') {
        updateData.priority = updates.priority;
      }

      if (this.dryRun) {
        console.log(`🔍 [DRY RUN] 将更新工作流: ${workflowId}`);
        console.log(`   当前名称: ${workflow.name} → ${updates.name}`);
        console.log(`   当前描述: ${workflow.description?.substring(0, 50)}... → ${updates.description?.substring(0, 50)}...`);
        console.log(`   当前标签: [${workflow.tags.join(', ')}] → [${updates.tags.join(', ')}]`);
        console.log(`   当前优先级: ${workflow.priority} → ${updates.priority}`);
        return { success: true, reason: 'dry_run' };
      }

      // 执行更新
      await Workflow.updateOne({ id: workflowId }, updateData);
      console.log(`✅ 更新工作流: ${workflowId} - ${updates.name}`);
      return { success: true, reason: 'updated' };

    } catch (error) {
      console.error(`❌ 更新工作流 ${workflowId} 失败:`, error.message);
      return { success: false, reason: 'error', error: error.message };
    }
  }

  // 批量更新工作流
  async updateAllWorkflows() {
    console.log(`\n🔄 开始批量更新工作流信息...`);
    if (this.targetField) {
      console.log(`📌 仅更新字段: ${this.targetField}`);
    }
    if (this.dryRun) {
      console.log(`🔍 DRY RUN 模式 - 不会实际更新数据`);
    }

    let updateCount = 0;
    let skipCount = 0;
    let errorCount = 0;

    for (const [workflowId, updates] of Object.entries(WORKFLOW_UPDATES)) {
      const result = await this.updateWorkflow(workflowId, updates);
      
      if (result.success) {
        if (result.reason === 'updated') {
          updateCount++;
        } else if (result.reason === 'dry_run') {
          updateCount++;
        }
      } else {
        if (result.reason === 'not_found') {
          skipCount++;
        } else {
          errorCount++;
        }
      }
    }

    console.log(`\n📊 更新结果:`);
    console.log(`   更新: ${updateCount} 个`);
    console.log(`   跳过: ${skipCount} 个`);
    console.log(`   失败: ${errorCount} 个`);
    console.log(`   总计: ${Object.keys(WORKFLOW_UPDATES).length} 个`);
  }

  // 显示当前工作流统计
  async showStats() {
    const workflows = await Workflow.find({}).select('category priority tags');
    
    console.log('\n📈 当前工作流统计:');
    
    // 分类统计
    const categoryStats = {};
    workflows.forEach(w => {
      categoryStats[w.category] = (categoryStats[w.category] || 0) + 1;
    });
    
    console.log('\n📂 分类统计:');
    Object.entries(categoryStats).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} 个`);
    });

    // 优先级分布
    const priorityRanges = {
      '高优先级 (70-100)': 0,
      '中优先级 (30-69)': 0,
      '低优先级 (1-29)': 0,
      '未设置 (0)': 0
    };

    workflows.forEach(w => {
      const priority = w.priority || 0;
      if (priority >= 70) priorityRanges['高优先级 (70-100)']++;
      else if (priority >= 30) priorityRanges['中优先级 (30-69)']++;
      else if (priority >= 1) priorityRanges['低优先级 (1-29)']++;
      else priorityRanges['未设置 (0)']++;
    });

    console.log('\n🎯 优先级分布:');
    Object.entries(priorityRanges).forEach(([range, count]) => {
      console.log(`   ${range}: ${count} 个`);
    });

    // 标签统计
    const tagStats = {};
    workflows.forEach(w => {
      w.tags.forEach(tag => {
        tagStats[tag] = (tagStats[tag] || 0) + 1;
      });
    });

    const topTags = Object.entries(tagStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);

    console.log('\n🏷️  热门标签 (前10):');
    topTags.forEach(([tag, count]) => {
      console.log(`   ${tag}: ${count} 次`);
    });
  }

  // 主执行方法
  async run() {
    try {
      console.log('🚀 开始工作流信息更新脚本\n');
      
      this.parseArgs();
      await this.connectDB();
      
      await this.updateAllWorkflows();
      await this.showStats();
      
      console.log('\n🎉 脚本执行完成!');
    } catch (error) {
      console.error('\n💥 脚本执行失败:', error.message);
      process.exit(1);
    } finally {
      await mongoose.disconnect();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const updater = new WorkflowUpdater();
  updater.run();
}

module.exports = WorkflowUpdater;
