import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { MdClose, MdLock, MdLockOpen } from 'react-icons/md';
import '../../styles/close-buttons.css';
import './index.css';

const ExtendSizeModal = ({
  visible,
  onClose,
  onApply,
  defaultScale = 2,
  originalWidth = null,
  originalHeight = null,
  originalImage = null,
  style,
  savedSettings = null,
  pageType = null
}) => {
  // 添加调试日志，输出接收到的props
  console.log('ExtendSizeModal接收到的props:', {
    visible,
    defaultScale,
    originalWidth,
    originalHeight,
    originalImage,
    style,
    savedSettings,
    pageType
  });
  
  const [activeTab, setActiveTab] = useState(pageType === 'change-model' ? 'scale' : 'platform');
  const [scale, setScale] = useState(null);
  const [width, setWidth] = useState(null);
  const [height, setHeight] = useState(null);
  const [hasOriginalImage, setHasOriginalImage] = useState(Boolean(originalWidth && originalHeight));
  const [aspectRatio, setAspectRatio] = useState(originalWidth && originalHeight ? originalWidth / originalHeight : null);
  const [customWidth, setCustomWidth] = useState('');
  const [customHeight, setCustomHeight] = useState('');
  // 添加activeRatio状态，用于记录用户选择的宽高比
  const [activeRatio, setActiveRatio] = useState(null);
  // 添加锁定宽高比状态
  const [lockAspectRatio, setLockAspectRatio] = useState(true);
  // 添加平台按钮的激活状态
  const [activePlatform, setActivePlatform] = useState(null);
  // 添加图片位置状态
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  // 添加拖拽状态
  const [isDragging, setIsDragging] = useState(false);
  // 添加拖拽起始点
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  // 添加状态用于检测扩图尺寸是否小于原图
  const [isAnySmallerThanOriginal, setIsAnySmallerThanOriginal] = useState(false);
  // 添加状态用于显示居中指示线
  const [showCenterX, setShowCenterX] = useState(false);
  const [showCenterY, setShowCenterY] = useState(false);
  // 添加状态用于存储图片边缘与画布边缘的距离（预览尺寸下）
  const [edgeDistances, setEdgeDistances] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  });
  // 添加状态用于存储图片边缘与画布边缘的实际距离（真实尺寸下）
  const [actualEdgeDistances, setActualEdgeDistances] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  });
  
  // 添加状态用于存储图片的实际尺寸
  const [actualWidth, setActualWidth] = useState(null);
  const [actualHeight, setActualHeight] = useState(null);
  const [isLoadingImageDimensions, setIsLoadingImageDimensions] = useState(false);
  
  // 在状态声明区域添加新状态
  const [exceedsMaxLimit, setExceedsMaxLimit] = useState(false);
  
  // 弹窗拖动相关状态
  const [isModalDragging, setIsModalDragging] = useState(false);
  const [modalDragOffset, setModalDragOffset] = useState({ x: 0, y: 0 });
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });
  
  // 引用DOM元素
  const canvasRef = useRef(null);
  const imageRef = useRef(null);

  // 添加ref用于跟踪visible的前一个状态
  const previousVisible = useRef(false);

  // 添加位置的ref，用于存储拖动时的位置而不触发重新渲染
  const imagePositionRef = useRef({ x: 0, y: 0 });
  
  // 同步imagePosition和imagePositionRef
  useEffect(() => {
    imagePositionRef.current = imagePosition;
  }, [imagePosition]);

  // 添加获取图片实际尺寸的逻辑
  useEffect(() => {
    if (originalImage && visible && (!originalWidth || !originalHeight || originalWidth === 800 && originalHeight === 1200)) {
      console.log('尝试获取图片实际尺寸:', originalImage);
      setIsLoadingImageDimensions(true);
      
      const img = new Image();
      img.onload = () => {
        const imgWidth = img.naturalWidth;
        const imgHeight = img.naturalHeight;
        console.log('获取到图片实际尺寸:', imgWidth, 'x', imgHeight);
        
        setActualWidth(imgWidth);
        setActualHeight(imgHeight);
        
        // 更新相关状态
        setHasOriginalImage(true);
        setAspectRatio(imgWidth / imgHeight);
        
        // 如果已设置了scale，重新计算结果尺寸
        if (scale) {
          const newWidth = Math.round(imgWidth * scale);
          const newHeight = Math.round(imgHeight * scale);
          setWidth(newWidth);
          setHeight(newHeight);
          setCustomWidth(newWidth);
          setCustomHeight(newHeight);
        }
        
        setIsLoadingImageDimensions(false);
      };
      
      img.onerror = () => {
        console.error('加载图片获取尺寸失败:', originalImage);
        setIsLoadingImageDimensions(false);
      };
      
      // 添加时间戳防止缓存
      const timestamp = Date.now();
      const separator = originalImage.includes('?') ? '&' : '?';
      img.src = `${originalImage}${separator}t=${timestamp}`;
    }
  }, [originalImage, originalWidth, originalHeight, scale, visible]);

  // 每次弹窗显示时，重置状态
  useEffect(() => {
    if (visible) {
      console.log('弹窗打开，重置状态，savedSettings:', savedSettings);
      
      // 重置超出限制状态
      setExceedsMaxLimit(false);
      
      // 如果有已保存的设置，恢复到已保存的状态
      if (savedSettings) {
        setScale(savedSettings.scale);
        setWidth(savedSettings.width);
        setHeight(savedSettings.height);
        setActiveRatio(savedSettings.activeRatio);
        setCustomWidth(savedSettings.width);
        setCustomHeight(savedSettings.height);
        setActiveTab(savedSettings.activeTab || (pageType === 'change-model' ? 'scale' : 'platform'));
        // 如果有保存的图片位置，恢复位置
        if (savedSettings.imagePosition) {
          setImagePosition(savedSettings.imagePosition);
        } else {
          // 否则重置为居中位置
          setImagePosition({ x: 0, y: 0 });
        }
      } else {
        // 如果没有已保存的设置，则重置为默认清空状态
        setScale(null);
        setWidth(null);
        setHeight(null);
        setActiveRatio(null);
        setCustomWidth('');
        setCustomHeight('');
        setActiveTab(pageType === 'change-model' ? 'scale' : 'platform');
        // 重置图片位置为居中
        setImagePosition({ x: 0, y: 0 });
      }
      
      // 计算是否有原图 - 使用实际尺寸或传入的尺寸
      const effectiveWidth = actualWidth || originalWidth;
      const effectiveHeight = actualHeight || originalHeight;
      const newHasOriginalImage = Boolean(effectiveWidth && effectiveHeight);
      setHasOriginalImage(newHasOriginalImage);
      
      // 计算原图宽高比 - 使用实际尺寸或传入的尺寸
      if (effectiveWidth && effectiveHeight) {
        setAspectRatio(effectiveWidth / effectiveHeight);
      } else {
        setAspectRatio(null);
      }
    } else if (previousVisible.current && !visible) {
      // 当弹窗从显示变为隐藏时，立即清除未保存的选择
      console.log('弹窗关闭，立即清除未保存的选择');
      setScale(null);
      setWidth(null);
      setHeight(null);
      setActiveRatio(null);
      setCustomWidth('');
      setCustomHeight('');
      // 重置图片位置
      setImagePosition({ x: 0, y: 0 });
    }
    
    // 记录当前的visible状态，用于下次比较
    previousVisible.current = visible;
  }, [visible, savedSettings, originalWidth, originalHeight, actualWidth, actualHeight]);

  // 处理倍数变化
  const handleScaleChange = (newScale) => {
    setScale(newScale);
    // 只有在有原图时才计算新尺寸
    // 使用实际尺寸或传入的尺寸
    const effectiveWidth = actualWidth || originalWidth;
    const effectiveHeight = actualHeight || originalHeight;
    
    if (effectiveWidth && effectiveHeight) {
      let newWidth = Math.round(effectiveWidth * newScale);
      let newHeight = Math.round(effectiveHeight * newScale);
      
      // 检查是否超出最大限制
      const exceedsLimit = newWidth > 3000 || newHeight > 3000;
      setExceedsMaxLimit(exceedsLimit);
      
      // 限制尺寸不超过3000px
      if (newWidth > 3000) {
        newWidth = 3000;
      }
      if (newHeight > 3000) {
        newHeight = 3000;
      }
      
      // 检查新尺寸是否小于原图
      const isSmallerThanOriginal = newWidth < effectiveWidth || newHeight < effectiveHeight;
      setIsAnySmallerThanOriginal(isSmallerThanOriginal);
      
      setWidth(newWidth);
      setHeight(newHeight);
      
      // 同时更新自定义尺寸标签页的值
      setCustomWidth(newWidth);
      setCustomHeight(newHeight);
    }
  };

  // 处理2倍按钮点击
  const handle2xClick = () => {
    setActiveRatio(null);
    handleScaleChange(2);
  };

  // 处理4倍按钮点击
  const handle4xClick = () => {
    handleScaleChange(4);
  };

  // 处理原始高清按钮点击
  const handleOriginalClick = () => {
    setActiveRatio(null);
    handleScaleChange(1);
  };

  // 添加新的处理函数
  const handle1_1xClick = () => {
    setActiveRatio(null);
    handleScaleChange(1.1);
  };

  const handle1_2xClick = () => {
    setActiveRatio(null);
    handleScaleChange(1.2);
  };

  const handle1_5xClick = () => {
    setActiveRatio(null);
    handleScaleChange(1.5);
  };
    
  // 处理各种宽高比的按钮点击
  const handleAspectRatio = (widthRatio, heightRatio) => {
    if (!originalWidth || !originalHeight) return;
    
    // 设置当前激活的宽高比
    setActiveRatio(`${widthRatio}:${heightRatio}`);
    
    // 计算目标宽高比
    const targetRatio = widthRatio / heightRatio;
    
    // 原始图片的宽高比
    const originalRatio = originalWidth / originalHeight;
    
    // 判断原图方向和目标方向
    const isOriginalLandscape = originalRatio >= 1; // 原图是否为横图
    const isTargetLandscape = targetRatio >= 1;     // 目标是否为横图
    
    let newWidth, newHeight;
    
    // 特殊处理1:1正方形比例
    if (widthRatio === 1 && heightRatio === 1) {
      // 使用原图较大的边作为新正方形的边长
      let maxSide = Math.max(originalWidth, originalHeight);
      
      // 检查是否超出最大限制
      const exceedsLimit = maxSide > 3000;
      setExceedsMaxLimit(exceedsLimit);
      
      // 限制尺寸不超过3000px
      if (maxSide > 3000) {
        maxSide = 3000;
      }
      
      newWidth = maxSide;
      newHeight = maxSide;
      
      // 为1:1比例单独设置一个缩放比例，确保按钮状态正确
      // 计算实际的缩放比例
      const widthScale = maxSide / originalWidth;
      const heightScale = maxSide / originalHeight;
      const actualScale = Math.max(widthScale, heightScale);
      
      console.log('正方形比例，计算的实际缩放比例:', actualScale);
      
      // 先更新状态
      setWidth(newWidth);
      setHeight(newHeight);
      
      // 设置实际缩放比例
      setScale(actualScale);
      
      // 检查新尺寸是否小于原图
      const isSmallerThanOriginal = newWidth < originalWidth || newHeight < originalHeight;
      setIsAnySmallerThanOriginal(isSmallerThanOriginal);
      
      // 更新自定义尺寸标签页的值
      setCustomWidth(newWidth);
      setCustomHeight(newHeight);
      
      // 提前返回，不执行后面的代码
      return;
    }
    // 处理方向转换（竖图变横图或横图变竖图）的情况
    else if ((isOriginalLandscape && !isTargetLandscape) || (!isOriginalLandscape && isTargetLandscape)) {
      // 方向转换时，使用较长的边作为基准
      if (originalWidth >= originalHeight) {
        // 如果原图宽大于高，以宽为基准
        newWidth = originalWidth;
        newHeight = Math.round(originalWidth / targetRatio);
      } else {
        // 如果原图高大于宽，以高为基准
        newHeight = originalHeight;
        newWidth = Math.round(originalHeight * targetRatio);
      }
    } else {
      // 方向不变的情况，确保所有尺寸不小于原图
      // 计算以宽为基准的尺寸（保持宽不变）
      const heightFromWidth = Math.round(originalWidth / targetRatio);
      
      // 计算以高为基准的尺寸（保持高不变）
      const widthFromHeight = Math.round(originalHeight * targetRatio);
      
      // 检查计算结果是否满足最小尺寸要求
      const isWidthBasedValid = heightFromWidth >= originalHeight;
      const isHeightBasedValid = widthFromHeight >= originalWidth;
      
      if (isWidthBasedValid && isHeightBasedValid) {
        // 两种方案都满足要求，选择面积较小的方案
        const areaWidthBased = originalWidth * heightFromWidth;
        const areaHeightBased = widthFromHeight * originalHeight;
        
        if (areaWidthBased <= areaHeightBased) {
          newWidth = originalWidth;
          newHeight = heightFromWidth;
        } else {
          newWidth = widthFromHeight;
          newHeight = originalHeight;
        }
      } else if (isWidthBasedValid) {
        // 只有以宽为基准的方案可行
        newWidth = originalWidth;
        newHeight = heightFromWidth;
      } else if (isHeightBasedValid) {
        // 只有以高为基准的方案可行
        newWidth = widthFromHeight;
        newHeight = originalHeight;
      } else {
        // 两种方案都无法满足所有维度不小于原图，需要按比例放大
        // 计算需要的放大倍数
        const scaleToMatchWidth = originalWidth / widthFromHeight;
        const scaleToMatchHeight = originalHeight / heightFromWidth;
        
        // 使用最大缩放比例以确保两个维度都不小于原图
        const scale = Math.max(scaleToMatchWidth, scaleToMatchHeight);
        
        if (scale > 1) {
          // 如果需要放大
          if (scaleToMatchWidth >= scaleToMatchHeight) {
            // 以原图宽度为基准
            newWidth = originalWidth;
            newHeight = Math.round(heightFromWidth * scale);
          } else {
            // 以原图高度为基准
            newHeight = originalHeight;
            newWidth = Math.round(widthFromHeight * scale);
          }
        } else {
          // 默认至少保持原始尺寸
          newWidth = Math.max(originalWidth, widthFromHeight);
          newHeight = Math.max(originalHeight, heightFromWidth);
        }
      }
    }
    
    console.log('目标比例:', targetRatio, '原始比例:', originalRatio, '新尺寸:', newWidth, 'x', newHeight);
    
    // 检查是否超出最大限制
    const exceedsLimit = newWidth > 3000 || newHeight > 3000;
    setExceedsMaxLimit(exceedsLimit);
    
    // 限制尺寸不超过3000px
    if (newWidth > 3000) {
      newWidth = 3000;
    }
    if (newHeight > 3000) {
      newHeight = 3000;
    }
    
    // 更新状态
    setWidth(newWidth);
    setHeight(newHeight);
    
    // 检查新尺寸是否小于原图
    const isSmallerThanOriginal = newWidth < originalWidth || newHeight < originalHeight;
    setIsAnySmallerThanOriginal(isSmallerThanOriginal);
    
    // 计算相对于原图的缩放比例
    const newScale = Math.min(newWidth / originalWidth, newHeight / originalHeight);
    setScale(newScale);
    
    // 更新自定义尺寸标签页的值
    setCustomWidth(newWidth);
    setCustomHeight(newHeight);
  };

  // 计算滑块填充宽度的百分比
  const calculateFillWidth = () => {
    const min = 1.0;
    const max = 4.0;
    const range = max - min;
    const position = scale - min;
    return position * (100 / range);
  };

  // 当originalWidth和originalHeight变化时，更新状态
  useEffect(() => {
    console.log('ExtendSizeModal useEffect - 检测到props变化:', {
      originalWidth,
      originalHeight,
      scale,
      activeRatio
    });
    
    const newHasOriginalImage = Boolean(originalWidth && originalHeight);
    console.log('计算得到hasOriginalImage:', newHasOriginalImage);
    
    setHasOriginalImage(newHasOriginalImage);
    
    if (originalWidth && originalHeight) {
      // 如果有激活的宽高比或倍数，才计算尺寸
      if (activeRatio || scale) {
        // 如果有激活的宽高比，不要在这里重新计算尺寸
        if (activeRatio) {
          console.log('检测到宽高比选择，跳过useEffect中的尺寸计算');
          return;
        }
        
        // 只在设置了scale时才计算尺寸
        if (scale) {
          console.log('更新宽高值:', {
            newWidth: Math.round(originalWidth * scale),
            newHeight: Math.round(originalHeight * scale)
          });
          let newWidth = Math.round(originalWidth * scale);
          let newHeight = Math.round(originalHeight * scale);
          
          // 检查是否超出最大限制
          const exceedsLimit = newWidth > 3000 || newHeight > 3000;
          setExceedsMaxLimit(exceedsLimit);
          
          // 限制尺寸不超过3000px
          if (newWidth > 3000) {
            newWidth = 3000;
          }
          if (newHeight > 3000) {
            newHeight = 3000;
          }
          
          setWidth(newWidth);
          setHeight(newHeight);
          setCustomWidth(newWidth);
          setCustomHeight(newHeight);
        }
      }
      
      setAspectRatio(originalWidth / originalHeight);
    } else {
      console.log('未设置宽高值，因为原图尺寸不可用');
      setWidth(null);
      setHeight(null);
      setCustomWidth('');
      setCustomHeight('');
      setAspectRatio(null);
      setActiveRatio(null);
    }
  }, [originalWidth, originalHeight, scale, activeRatio]);

  // 处理自定义宽度变化
  const handleCustomWidthChange = (e) => {
    const inputValue = e.target.value;
    const newWidth = inputValue === '' ? '' : parseInt(inputValue);
    setCustomWidth(newWidth);
    
    // 更新画布预览的width
    if (typeof newWidth === 'number' && newWidth > 0) {
      setWidth(newWidth); // 关键添加：更新width让画布预览更新
    }
    
    // 更新高度（仅当宽度为数字且锁定宽高比时）
    if (lockAspectRatio && aspectRatio && typeof newWidth === 'number' && newWidth > 0) {
      let calculatedHeight = Math.round(newWidth / aspectRatio);
      
      // 检查是否超出最大限制
      const exceedsLimit = newWidth > 3000 || calculatedHeight > 3000;
      setExceedsMaxLimit(exceedsLimit);
      
      // 限制计算高度不超过3000
      calculatedHeight = Math.min(calculatedHeight, 3000);
      setCustomHeight(calculatedHeight);
      
      // 同时更新画布预览的height
      setHeight(calculatedHeight);
      
      // 检查新尺寸是否有任一边小于原图
      const isSmallerThanOriginal = newWidth < originalWidth || calculatedHeight < originalHeight;
      setIsAnySmallerThanOriginal(isSmallerThanOriginal);
    } else if (typeof newWidth === 'number') {
      // 检查宽度是否超出最大限制
      const exceedsLimit = newWidth > 3000 || (customHeight !== '' && parseInt(customHeight) > 3000);
      setExceedsMaxLimit(exceedsLimit);
      
      // 仅检查宽度
      const isSmallerThanOriginal = newWidth < originalWidth || (customHeight !== '' && parseInt(customHeight) < originalHeight);
      setIsAnySmallerThanOriginal(isSmallerThanOriginal);
    }
  };
  
  // 处理自定义高度变化
  const handleCustomHeightChange = (e) => {
    const inputValue = e.target.value;
    const newHeight = inputValue === '' ? '' : parseInt(inputValue);
    setCustomHeight(newHeight);
    
    // 更新画布预览的height
    if (typeof newHeight === 'number' && newHeight > 0) {
      setHeight(newHeight); // 关键添加：更新height让画布预览更新
    }
    
    // 更新宽度（仅当高度为数字且锁定宽高比时）
    if (lockAspectRatio && aspectRatio && typeof newHeight === 'number' && newHeight > 0) {
      let calculatedWidth = Math.round(newHeight * aspectRatio);
      
      // 检查是否超出最大限制
      const exceedsLimit = calculatedWidth > 3000 || newHeight > 3000;
      setExceedsMaxLimit(exceedsLimit);
      
      // 限制计算宽度不超过3000
      calculatedWidth = Math.min(calculatedWidth, 3000);
      setCustomWidth(calculatedWidth);
      
      // 同时更新画布预览的width
      setWidth(calculatedWidth);
      
      // 检查新尺寸是否有任一边小于原图
      const isSmallerThanOriginal = calculatedWidth < originalWidth || newHeight < originalHeight;
      setIsAnySmallerThanOriginal(isSmallerThanOriginal);
    } else if (typeof newHeight === 'number') {
      // 检查高度是否超出最大限制
      const exceedsLimit = (customWidth !== '' && parseInt(customWidth) > 3000) || newHeight > 3000;
      setExceedsMaxLimit(exceedsLimit);
      
      // 仅检查高度
      const isSmallerThanOriginal = (customWidth !== '' && parseInt(customWidth) < originalWidth) || newHeight < originalHeight;
      setIsAnySmallerThanOriginal(isSmallerThanOriginal);
    }
  };
  
  // 处理宽度值失去焦点时的验证
  const handleCustomWidthBlur = () => {
    if (customWidth === '') {
      // 如果为空，设置为原始宽度
      if (originalWidth) {
        setCustomWidth(width);
      }
      return;
    }
    
    // 确保值不小于原始尺寸且不超过最大限制
    let validWidth = customWidth;
    if (originalWidth && customWidth < originalWidth) {
      validWidth = originalWidth;
    }
    if (validWidth > 3000) {
      validWidth = 3000;
    }
    
    if (validWidth !== customWidth) {
      setCustomWidth(validWidth);
      
      // 更新画布预览的width
      setWidth(validWidth);
      
      // 如果锁定宽高比，同时更新高度
      if (lockAspectRatio && aspectRatio) {
        const calculatedHeight = Math.round(validWidth / aspectRatio);
        const validHeight = Math.min(3000, calculatedHeight);
        setCustomHeight(validHeight);
        
        // 更新画布预览的height
        setHeight(validHeight);
        
        // 检查最终尺寸是否小于原图
        const isSmallerThanOriginal = validWidth < originalWidth || validHeight < originalHeight;
        setIsAnySmallerThanOriginal(isSmallerThanOriginal);
      } else {
        // 仅检查宽度
        const isSmallerThanOriginal = validWidth < originalWidth || (customHeight !== '' && parseInt(customHeight) < originalHeight);
        setIsAnySmallerThanOriginal(isSmallerThanOriginal);
      }
    } else {
      // 如果宽度没有变化，也要更新预览宽度
      setWidth(validWidth);
      
      // 如果宽度没有变化，也要检查
      const isSmallerThanOriginal = validWidth < originalWidth || (customHeight !== '' && parseInt(customHeight) < originalHeight);
      setIsAnySmallerThanOriginal(isSmallerThanOriginal);
    }
  };
  
  // 处理高度值失去焦点时的验证
  const handleCustomHeightBlur = () => {
    if (customHeight === '') {
      // 如果为空，设置为原始高度
      if (originalHeight) {
        setCustomHeight(height);
      }
      return;
    }
    
    // 确保值不小于原始尺寸且不超过最大限制
    let validHeight = customHeight;
    if (originalHeight && customHeight < originalHeight) {
      validHeight = originalHeight;
    }
    if (validHeight > 3000) {
      validHeight = 3000;
    }
    
    if (validHeight !== customHeight) {
      setCustomHeight(validHeight);
      
      // 更新画布预览的height
      setHeight(validHeight);
      
      // 如果锁定宽高比，同时更新宽度
      if (lockAspectRatio && aspectRatio) {
        const calculatedWidth = Math.round(validHeight * aspectRatio);
        const validWidth = Math.min(3000, calculatedWidth);
        setCustomWidth(validWidth);
        
        // 更新画布预览的width
        setWidth(validWidth);
        
        // 检查最终尺寸是否小于原图
        const isSmallerThanOriginal = validWidth < originalWidth || validHeight < originalHeight;
        setIsAnySmallerThanOriginal(isSmallerThanOriginal);
      } else {
        // 仅检查高度
        const isSmallerThanOriginal = (customWidth !== '' && parseInt(customWidth) < originalWidth) || validHeight < originalHeight;
        setIsAnySmallerThanOriginal(isSmallerThanOriginal);
      }
    } else {
      // 如果高度没有变化，也要更新预览高度
      setHeight(validHeight);
      
      // 如果高度没有变化，也要检查
      const isSmallerThanOriginal = (customWidth !== '' && parseInt(customWidth) < originalWidth) || validHeight < originalHeight;
      setIsAnySmallerThanOriginal(isSmallerThanOriginal);
    }
  };

  // 切换锁定宽高比状态
  const toggleLockAspectRatio = () => {
    const newLockState = !lockAspectRatio;
    setLockAspectRatio(newLockState);
    
    // 如果从解锁变为锁定，则更新宽高比
    if (newLockState && customWidth && customHeight) {
      const newAspectRatio = customWidth / customHeight;
      console.log('更新宽高比:', newAspectRatio);
      setAspectRatio(newAspectRatio);
    }
  };

  // 处理平台尺寸按钮点击
  const handlePlatformSize = (platformWidth, platformHeight, platformName) => {
    if (!hasOriginalImage) return;
    
    // 检查是否超出最大限制
    const exceedsLimit = platformWidth > 3000 || platformHeight > 3000;
    setExceedsMaxLimit(exceedsLimit);
    
    // 限制尺寸不超过3000px
    let finalWidth = platformWidth;
    let finalHeight = platformHeight;
    if (platformWidth > 3000) {
      finalWidth = 3000;
    }
    if (platformHeight > 3000) {
      finalHeight = 3000;
    }
    
    // 直接使用平台要求的尺寸（或限制后的尺寸），不计算倍数
    setWidth(finalWidth);
    setHeight(finalHeight);
    setActivePlatform(platformName);
    
    // 同时更新自定义尺寸标签页的值
    setCustomWidth(finalWidth);
    setCustomHeight(finalHeight);
    
    // 检查平台尺寸是否有任一边小于原图
    const isSmallerThanOriginal = finalWidth < originalWidth || finalHeight < originalHeight;
    setIsAnySmallerThanOriginal(isSmallerThanOriginal);
    
    // 重置宽高比选择
    setActiveRatio(null);
  };

  // 计算画布预览尺寸的缩放比例
  const calculatePreviewScale = () => {
    if (!width || !height) return 1;
    
    // 预览区域最大尺寸（包括留边距）
    const maxPreviewWidth = 280;
    const maxPreviewHeight = 280;
    
    // 计算缩放比例，取较小者确保画布完全可见
    const scaleX = maxPreviewWidth / width;
    const scaleY = maxPreviewHeight / height;
    const scale = Math.min(scaleX, scaleY);
    
    return scale;
  };

  // 获取画布预览尺寸
  const previewScale = calculatePreviewScale();
  const previewWidth = width ? Math.round(width * previewScale) : 0;
  const previewHeight = height ? Math.round(height * previewScale) : 0;

  // 计算原始图片的预览尺寸
  const originalPreviewWidth = originalWidth ? Math.round(originalWidth * previewScale) : 0;
  const originalPreviewHeight = originalHeight ? Math.round(originalHeight * previewScale) : 0;

  // 处理图片拖拽开始
  const handleDragStart = (e) => {
    if (!canvasRef.current || !imageRef.current) return;
    e.preventDefault();
    setIsDragging(true);
    
    // 记录鼠标起始位置
    const clientX = e.clientX || (e.touches && e.touches[0].clientX);
    const clientY = e.clientY || (e.touches && e.touches[0].clientY);
    
    // 设置拖动起始位置，保存当前图片位置和鼠标位置的差值
    setDragStart({
      x: clientX - imagePositionRef.current.x,
      y: clientY - imagePositionRef.current.y
    });
  };

  // 处理图片拖拽中 - 使用requestAnimationFrame优化性能
  const handleDrag = (e) => {
    if (!isDragging || !canvasRef.current || !imageRef.current) return;
    e.preventDefault();
    
    // 使用requestAnimationFrame优化渲染
    requestAnimationFrame(() => {
      // 获取当前鼠标位置
      const clientX = e.clientX || (e.touches && e.touches[0].clientX);
      const clientY = e.clientY || (e.touches && e.touches[0].clientY);
      
      // 计算新位置
      let newX = clientX - dragStart.x;
      let newY = clientY - dragStart.y;
      
      // 获取画布和图片的尺寸 - 缓存DOM查询以提高性能
      const canvasWidth = canvasRef.current.offsetWidth;
      const canvasHeight = canvasRef.current.offsetHeight;
      const imageWidth = imageRef.current.offsetWidth;
      const imageHeight = imageRef.current.offsetHeight;
      
      // 计算画布边缘到图片边缘的距离
      // 注意：这里必须先计算边距，然后才能用边距来判断是否居中
      const topDistance = newY + (canvasHeight - imageHeight) / 2;
      const bottomDistance = canvasHeight - (newY + imageHeight + (canvasHeight - imageHeight) / 2);
      const leftDistance = newX + (canvasWidth - imageWidth) / 2;
      const rightDistance = canvasWidth - (newX + imageWidth + (canvasWidth - imageWidth) / 2);
      
      // 使用边距差异来判断是否接近居中位置
      const snapThreshold = 10; // 吸附阈值（像素距离）
      
      // 计算左右边距的差异和上下边距的差异
      const horizontalDiff = Math.abs(leftDistance - rightDistance);
      const verticalDiff = Math.abs(topDistance - bottomDistance);
      
      // 如果边距差异小于阈值，认为在该方向接近居中，应用吸附效果
      const shouldSnapX = horizontalDiff < snapThreshold;
      const shouldSnapY = verticalDiff < snapThreshold;
      
      // 计算真正居中时的位置
      const centerX = (canvasWidth - imageWidth) / 2;
      const centerY = (canvasHeight - imageHeight) / 2;
      
      // 应用吸附效果
      if (shouldSnapX) {
        newX = 0; // 使用0作为x坐标，与初始位置一致，确保水平居中
        setShowCenterX(true);
      } else {
        setShowCenterX(false);
      }
      
      if (shouldSnapY) {
        newY = 0; // 使用0作为y坐标，与初始位置一致，确保垂直居中
        setShowCenterY(true);
      } else {
        setShowCenterY(false);
      }
      
      // 限制范围，确保图片不会移出画布
      // 如果图片比画布小，则限制图片完全在画布内
      // 如果图片比画布大，则确保画布区域内始终有图片内容
      if (imageWidth <= canvasWidth) {
        // 图片比画布窄，限制在画布宽度内
        newX = Math.max(Math.min(newX, (canvasWidth - imageWidth) / 2), -(canvasWidth - imageWidth) / 2);
      } else {
        // 图片比画布宽，确保画布区域内有图片
        newX = Math.min(Math.max(newX, canvasWidth - imageWidth), 0);
      }
      
      if (imageHeight <= canvasHeight) {
        // 图片比画布矮，限制在画布高度内
        newY = Math.max(Math.min(newY, (canvasHeight - imageHeight) / 2), -(canvasHeight - imageHeight) / 2);
      } else {
        // 图片比画布高，确保画布区域内有图片
        newY = Math.min(Math.max(newY, canvasHeight - imageHeight), 0);
      }
      
      // 重新计算边距（根据最终确定的位置）
      const finalTopDistance = newY + (canvasHeight - imageHeight) / 2;
      const finalBottomDistance = canvasHeight - (newY + imageHeight + (canvasHeight - imageHeight) / 2);
      const finalLeftDistance = newX + (canvasWidth - imageWidth) / 2;
      const finalRightDistance = canvasWidth - (newX + imageWidth + (canvasWidth - imageWidth) / 2);
      
      // 计算实际尺寸下的边距
      // 获取实际图片和画布尺寸（非预览尺寸）
      const realImageWidth = actualWidth || originalWidth;
      const realImageHeight = actualHeight || originalHeight;
      const realCanvasWidth = width; // 扩图后画布宽度
      const realCanvasHeight = height; // 扩图后画布高度
      
      // 直接根据实际画布和图片尺寸计算实际边距，而不是通过预览比例缩放
      // 先计算图片在实际尺寸下的位置比例
      const positionRatioX = newX / canvasWidth;
      const positionRatioY = newY / canvasHeight;
      
      // 根据比例计算实际尺寸下的位置
      const realPositionX = positionRatioX * realCanvasWidth;
      const realPositionY = positionRatioY * realCanvasHeight;
      
      // 简化实际尺寸下的边距计算，使用更直接的方式确保边距正确
      // 当图片居中时，确保上下边距相等、左右边距相等
      // 水平方向边距
      const realHorizontalSpace = realCanvasWidth - realImageWidth;
      
      // 计算最小左X坐标（贴左边界）和最大左X坐标（贴右边界）
      const minPossibleX = -(canvasWidth - imageWidth) / 2;
      const maxPossibleX = (canvasWidth - imageWidth) / 2;
      
      // 检查是否贴近边缘
      const edgeThreshold = 5; // 增加阈值，提高容错率
      const isNearLeftEdge = Math.abs(newX - minPossibleX) < edgeThreshold;
      const isNearRightEdge = Math.abs(newX - maxPossibleX) < edgeThreshold;
      
      // 根据位置计算边距
      let realLeftDistance, realRightDistance;
      if (isNearLeftEdge) {
        // 如果贴左边，左边距为0
        realLeftDistance = 0;
        realRightDistance = realHorizontalSpace;
      } else if (isNearRightEdge) {
        // 如果贴右边，右边距为0
        realLeftDistance = realHorizontalSpace;
        realRightDistance = 0;
      } else {
        // 正常计算边距
        realLeftDistance = Math.round(realPositionX + realHorizontalSpace / 2);
        realRightDistance = Math.round(realHorizontalSpace - realLeftDistance);
      }
      
      // 垂直方向边距
      const realVerticalSpace = realCanvasHeight - realImageHeight;
      
      // 计算最小上Y坐标（贴上边界）和最大上Y坐标（贴下边界）
      const minPossibleY = -(canvasHeight - imageHeight) / 2;
      const maxPossibleY = (canvasHeight - imageHeight) / 2;
      
      // 检查是否贴近上下边缘
      const isNearTopEdge = Math.abs(newY - minPossibleY) < edgeThreshold;
      const isNearBottomEdge = Math.abs(newY - maxPossibleY) < edgeThreshold;
      
      // 根据位置计算边距
      let realTopDistance, realBottomDistance;
      if (isNearTopEdge) {
        // 如果贴上边，上边距为0
        realTopDistance = 0;
        realBottomDistance = realVerticalSpace;
      } else if (isNearBottomEdge) {
        // 如果贴下边，下边距为0
        realTopDistance = realVerticalSpace;
        realBottomDistance = 0;
      } else {
        // 正常计算边距
        realTopDistance = Math.round(realPositionY + realVerticalSpace / 2);
        realBottomDistance = Math.round(realVerticalSpace - realTopDistance);
      }
      
      // 更新预览边距状态
      setEdgeDistances({
        top: Math.round(finalTopDistance),
        bottom: Math.round(finalBottomDistance),
        left: Math.round(finalLeftDistance),
        right: Math.round(finalRightDistance)
      });
      
      // 更新实际边距状态
      setActualEdgeDistances({
        top: realTopDistance,
        bottom: realBottomDistance,
        left: realLeftDistance,
        right: realRightDistance
      });
      
      // 直接更新DOM样式，避免React状态更新导致的重新渲染
      if (imageRef.current) {
        imageRef.current.style.transform = `translate(${newX}px, ${newY}px)`;
      }
      
      // 更新ref中的位置，但不触发重新渲染
      imagePositionRef.current = { x: newX, y: newY };
    });
  };

  // 处理图片拖拽结束
  const handleDragEnd = () => {
    setIsDragging(false);
    setShowCenterX(false);
    setShowCenterY(false);
    
    // 拖拽结束后，将ref中的位置同步到state，触发一次重新渲染
    const currentPosition = imagePositionRef.current;
    setImagePosition(currentPosition);
    
    // 手动计算并更新边距，确保与拖拽中的计算方式一致
    if (canvasRef.current && imageRef.current && originalImage) {
      const canvasWidth = canvasRef.current.offsetWidth;
      const canvasHeight = canvasRef.current.offsetHeight;
      const imageWidth = imageRef.current.offsetWidth;
      const imageHeight = imageRef.current.offsetHeight;
      
      // 获取实际图片和画布尺寸
      const realImageWidth = actualWidth || originalWidth;
      const realImageHeight = actualHeight || originalHeight;
      const realCanvasWidth = width;
      const realCanvasHeight = height;
      
      // 计算预览边距
      const finalTopDistance = currentPosition.y + (canvasHeight - imageHeight) / 2;
      const finalBottomDistance = canvasHeight - (currentPosition.y + imageHeight + (canvasHeight - imageHeight) / 2);
      const finalLeftDistance = currentPosition.x + (canvasWidth - imageWidth) / 2;
      const finalRightDistance = canvasWidth - (currentPosition.x + imageWidth + (canvasWidth - imageWidth) / 2);
      
      // 计算位置比例
      const positionRatioX = currentPosition.x / canvasWidth;
      const positionRatioY = currentPosition.y / canvasHeight;
      
      // 计算实际尺寸下的位置
      const realPositionX = positionRatioX * realCanvasWidth;
      const realPositionY = positionRatioY * realCanvasHeight;
      
      // 计算水平边距
      const realHorizontalSpace = realCanvasWidth - realImageWidth;
      
      // 计算最小左X坐标和最大左X坐标
      const minPossibleX = -(canvasWidth - imageWidth) / 2;
      const maxPossibleX = (canvasWidth - imageWidth) / 2;
      
      // 检查是否贴近边缘
      const edgeThreshold = 5; // 增加阈值，提高容错率
      const isNearLeftEdge = Math.abs(currentPosition.x - minPossibleX) < edgeThreshold;
      const isNearRightEdge = Math.abs(currentPosition.x - maxPossibleX) < edgeThreshold;
      
      // 根据位置计算边距
      let realLeftDistance, realRightDistance;
      if (isNearLeftEdge) {
        // 如果贴左边，左边距为0
        realLeftDistance = 0;
        realRightDistance = realHorizontalSpace;
      } else if (isNearRightEdge) {
        // 如果贴右边，右边距为0
        realLeftDistance = realHorizontalSpace;
        realRightDistance = 0;
      } else {
        // 正常计算边距
        realLeftDistance = Math.round(realPositionX + realHorizontalSpace / 2);
        realRightDistance = Math.round(realHorizontalSpace - realLeftDistance);
      }
      
      // 计算垂直边距
      const realVerticalSpace = realCanvasHeight - realImageHeight;
      
      // 计算最小上Y坐标和最大上Y坐标
      const minPossibleY = -(canvasHeight - imageHeight) / 2;
      const maxPossibleY = (canvasHeight - imageHeight) / 2;
      
      // 检查是否贴近上下边缘
      const isNearTopEdge = Math.abs(currentPosition.y - minPossibleY) < edgeThreshold;
      const isNearBottomEdge = Math.abs(currentPosition.y - maxPossibleY) < edgeThreshold;
      
      // 根据位置计算边距
      let realTopDistance, realBottomDistance;
      if (isNearTopEdge) {
        // 如果贴上边，上边距为0
        realTopDistance = 0;
        realBottomDistance = realVerticalSpace;
      } else if (isNearBottomEdge) {
        // 如果贴下边，下边距为0
        realTopDistance = realVerticalSpace;
        realBottomDistance = 0;
      } else {
        // 正常计算边距
        realTopDistance = Math.round(realPositionY + realVerticalSpace / 2);
        realBottomDistance = Math.round(realVerticalSpace - realTopDistance);
      }
      
      // 更新预览边距状态
      setEdgeDistances({
        top: Math.round(finalTopDistance),
        bottom: Math.round(finalBottomDistance),
        left: Math.round(finalLeftDistance),
        right: Math.round(finalRightDistance)
      });
      
      // 更新实际边距状态
      setActualEdgeDistances({
        top: realTopDistance,
        bottom: realBottomDistance,
        left: realLeftDistance,
        right: realRightDistance
      });
    }
  };

  // 添加拖拽事件监听
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleDrag);
      window.addEventListener('mouseup', handleDragEnd);
      window.addEventListener('touchmove', handleDrag);
      window.addEventListener('touchend', handleDragEnd);
    }
    
    return () => {
      window.removeEventListener('mousemove', handleDrag);
      window.removeEventListener('mouseup', handleDragEnd);
      window.removeEventListener('touchmove', handleDrag);
      window.removeEventListener('touchend', handleDragEnd);
    };
  }, [isDragging]);

  // 当尺寸变化时，重置图片位置到中心
  useEffect(() => {
    if (width && height) {
      setImagePosition({ x: 0, y: 0 });
    }
  }, [width, height]);

  // 处理确定按钮点击，添加图片位置和边距信息
  const handleApply = () => {
    // 根据当前活动标签页，创建不同的设置对象
    let settings;
    
    if (activeTab === 'scale') {
      // 倍数标签页的设置
      settings = {
        scale,
        width: width || 0,
        height: height || 0,
        hasOriginalImage,
        activeRatio, // 保存当前选中的宽高比
        activeTab,
        imagePosition, // 保存图片位置
        edgeDistances: actualEdgeDistances // 添加实际边距信息
      };
    } else if (activeTab === 'custom') {
      // 自定义尺寸标签页的设置 - 不计算scale
      settings = {
        width: parseInt(customWidth) || 0,
        height: parseInt(customHeight) || 0,
        hasOriginalImage,
        activeTab,
        imagePosition, // 保存图片位置
        edgeDistances: actualEdgeDistances, // 添加实际边距信息
        // 确保不传递scale，避免后续处理中计算倍数
        scale: null
      };
    } else if (activeTab === 'platform') {
      // 平台尺寸标签页的设置 - 不包含scale
      settings = {
        width: width || 0,
        height: height || 0,
        hasOriginalImage,
        activePlatform, // 保存当前选中的平台
        activeTab,
        imagePosition, // 保存图片位置
        edgeDistances: actualEdgeDistances, // 添加实际边距信息
        // 确保不传递scale，避免后续处理中计算倍数
        scale: null
      };
    }
    
    // 调用回调函数
    if (onApply) {
      console.log('ExtendSizeModal - 应用设置：', settings);
      onApply(settings);
    }
    
    // 关闭弹窗
    if (onClose) {
      onClose();
    }
  };

  // 判断尺寸是否相等的函数
  const isSameDimensions = () => {
    if (!hasOriginalImage) return false;
    
    // 根据当前标签页检查不同的尺寸变量
    if (activeTab === 'custom') {
      // 在自定义尺寸标签页中，检查customWidth和customHeight
      if (!customWidth || !customHeight) return false;
      return originalWidth === parseInt(customWidth) && originalHeight === parseInt(customHeight);
    } else if (activeTab === 'platform') {
      // 在平台尺寸标签页中，检查当前选中平台的尺寸
      if (!activePlatform) return false;
      
      // 获取当前选择的平台目标尺寸（通过高度判断即可，宽度相同时高度也相同说明目标相同）
      let platformRequiredWidth = 0;
      let platformRequiredHeight = 0;
      
      switch (activePlatform) {
        case 'default': 
          platformRequiredWidth = 1024; 
          platformRequiredHeight = 1024;
          break;
        case 'amazon': 
          platformRequiredWidth = 1600; 
          platformRequiredHeight = 1600;
          break;
        case 'walmart': 
        case 'etsy': 
        case 'wayfair': 
        case 'cdiscount': 
          platformRequiredWidth = 2000; 
          platformRequiredHeight = 2000;
          break;
        case 'mercado': 
        case 'temu': 
        case 'aliexpress': 
          platformRequiredWidth = 1200; 
          platformRequiredHeight = 1200;
          break;
        case 'ebay': 
          platformRequiredWidth = 1600; 
          platformRequiredHeight = 1600;
          break;
        case 'shopify-square': 
          platformRequiredWidth = 2048; 
          platformRequiredHeight = 2048;
          break;
        case 'shopify-landscape': 
          platformRequiredWidth = 2000; 
          platformRequiredHeight = 1800;
          break;
        case 'shopify-portrait': 
          platformRequiredWidth = 1600; 
          platformRequiredHeight = 2000;
          break;
        case 'shein': 
          platformRequiredWidth = 1340; 
          platformRequiredHeight = 1785;
          break;
        case 'shopee': 
        case 'lazada': 
          platformRequiredWidth = 1080; 
          platformRequiredHeight = 1080;
          break;
        case 'tiktok': 
        case 'douyin': 
        case 'taobao': 
        case 'jd': 
        case 'jumia': 
        case 'rakuten': 
        case 'wish': 
          platformRequiredWidth = 1024; 
          platformRequiredHeight = 1024;
          break;
        default:
          return false;
      }
      
      // 特殊情况：当原图尺寸已经符合或超过平台要求时
      if (originalWidth >= platformRequiredWidth && originalHeight >= platformRequiredHeight) {
        // 只有当平台要求的宽高比与原图完全相同时，才判断为相同
        return Math.abs(platformRequiredWidth/platformRequiredHeight - originalWidth/originalHeight) < 0.01;
      }
      
      return false;
    } else {
      // 在扩图比例标签页中，检查width和height
      if (!width || !height) return false;
      return originalWidth === width && originalHeight === height;
    }
  };

  // 判断扩图后尺寸是否任一边小于原图尺寸
  const isAnySmallerDimension = () => {
    if (!hasOriginalImage) return false;
    
    // 根据当前标签页检查不同的尺寸变量
    if (activeTab === 'custom') {
      // 在自定义尺寸标签页中，检查customWidth和customHeight
      if (!customWidth || !customHeight) return false;
      return parseInt(customWidth) < originalWidth || parseInt(customHeight) < originalHeight;
    } else if (activeTab === 'platform') {
      // 在平台尺寸标签页中，检查当前选中平台的尺寸
      if (!activePlatform || !width || !height) return false;
      return width < originalWidth || height < originalHeight;
    } else {
      // 在扩图比例标签页中，检查width和height
      if (!width || !height) return false;
      return width < originalWidth || height < originalHeight;
    }
  };

  // 添加useEffect监听activeTab的变化
  useEffect(() => {
    // 当标签页切换时，根据当前标签页重新设置width和height
    if (activeTab === 'platform' && activePlatform && hasOriginalImage) {
      // 更新平台尺寸标签页的width和height
      console.log('切换到平台尺寸标签页，更新尺寸显示');
    } else if (activeTab === 'custom' && hasOriginalImage) {
      // 更新自定义尺寸标签页的显示
      console.log('切换到自定义尺寸标签页，更新尺寸显示');
    } else if (activeTab === 'scale' && hasOriginalImage) {
      // 更新扩图比例标签页的显示
      console.log('切换到扩图比例标签页，更新尺寸显示');
    }
    // 不需要在这里执行其他操作，只需确保重新渲染以更新isSameDimensions()的结果
  }, [activeTab, activePlatform, customWidth, customHeight, width, height]);

  // 添加useEffect检查尺寸是否有任一边小于原图
  useEffect(() => {
    // 检查并更新状态
    if (hasOriginalImage) {
      setIsAnySmallerThanOriginal(isAnySmallerDimension());
    } else {
      setIsAnySmallerThanOriginal(false);
    }
  }, [activeTab, activePlatform, customWidth, customHeight, width, height, originalWidth, originalHeight, hasOriginalImage]);

  // 添加显示宽度和高度的变量，优先使用实际尺寸
  const displayWidth = actualWidth || originalWidth;
  const displayHeight = actualHeight || originalHeight;

  // 当图片位置或尺寸变化时，更新边缘距离
  useEffect(() => {
    if (canvasRef.current && imageRef.current && originalImage) {
      const canvasWidth = canvasRef.current.offsetWidth;
      const canvasHeight = canvasRef.current.offsetHeight;
      const imageWidth = imageRef.current.offsetWidth;
      const imageHeight = imageRef.current.offsetHeight;
      
      // 修正边距计算逻辑，确保准确计算图片到画布各边的距离
      const topDistance = imagePosition.y + (canvasHeight - imageHeight) / 2;
      const bottomDistance = canvasHeight - (imagePosition.y + imageHeight + (canvasHeight - imageHeight) / 2);
      const leftDistance = imagePosition.x + (canvasWidth - imageWidth) / 2;
      const rightDistance = canvasWidth - (imagePosition.x + imageWidth + (canvasWidth - imageWidth) / 2);
      
      // 获取实际图片和画布尺寸（非预览尺寸）
      const realImageWidth = actualWidth || originalWidth;
      const realImageHeight = actualHeight || originalHeight;
      const realCanvasWidth = width; // 扩图后画布宽度
      const realCanvasHeight = height; // 扩图后画布高度
      
      // 计算图片在实际尺寸下的位置比例
      const positionRatioX = imagePosition.x / canvasWidth;
      const positionRatioY = imagePosition.y / canvasHeight;
      
      // 根据比例计算实际尺寸下的位置
      const realPositionX = positionRatioX * realCanvasWidth;
      const realPositionY = positionRatioY * realCanvasHeight;
      
      // 简化实际尺寸下的边距计算，使用更直接的方式确保边距正确
      // 水平方向边距
      const realHorizontalSpace = realCanvasWidth - realImageWidth;
      
      // 计算最小左X坐标（贴左边界）和最大左X坐标（贴右边界）
      const minPossibleX = -(canvasWidth - imageWidth) / 2;
      const maxPossibleX = (canvasWidth - imageWidth) / 2;
      
      // 检查是否贴近边缘
      const edgeThreshold = 5; // 增加阈值，提高容错率
      const isNearLeftEdge = Math.abs(imagePosition.x - minPossibleX) < edgeThreshold;
      const isNearRightEdge = Math.abs(imagePosition.x - maxPossibleX) < edgeThreshold;
      
      // 根据位置计算边距
      let realLeftDistance, realRightDistance;
      if (isNearLeftEdge) {
        // 如果贴左边，左边距为0
        realLeftDistance = 0;
        realRightDistance = realHorizontalSpace;
      } else if (isNearRightEdge) {
        // 如果贴右边，右边距为0
        realLeftDistance = realHorizontalSpace;
        realRightDistance = 0;
      } else {
        // 正常计算边距
        realLeftDistance = Math.round(realPositionX + realHorizontalSpace / 2);
        realRightDistance = Math.round(realHorizontalSpace - realLeftDistance);
      }
      
      // 垂直方向边距
      const realVerticalSpace = realCanvasHeight - realImageHeight;
      
      // 计算最小上Y坐标（贴上边界）和最大上Y坐标（贴下边界）
      const minPossibleY = -(canvasHeight - imageHeight) / 2;
      const maxPossibleY = (canvasHeight - imageHeight) / 2;
      
      // 检查是否贴近上下边缘 - 使用预览尺寸下的位置进行判断
      const isNearTopEdge = Math.abs(imagePosition.y - minPossibleY) < edgeThreshold;
      const isNearBottomEdge = Math.abs(imagePosition.y - maxPossibleY) < edgeThreshold;
      
      // 根据位置计算边距
      let realTopDistance, realBottomDistance;
      if (isNearTopEdge) {
        // 如果贴上边，上边距为0
        realTopDistance = 0;
        realBottomDistance = realVerticalSpace;
      } else if (isNearBottomEdge) {
        // 如果贴下边，下边距为0
        realTopDistance = realVerticalSpace;
        realBottomDistance = 0;
      } else {
        // 正常计算边距
        realTopDistance = Math.round(realPositionY + realVerticalSpace / 2);
        realBottomDistance = Math.round(realVerticalSpace - realTopDistance);
      }
      
      // 更新预览边距状态
      setEdgeDistances({
        top: Math.round(topDistance),
        bottom: Math.round(bottomDistance),
        left: Math.round(leftDistance),
        right: Math.round(rightDistance)
      });
      
      // 更新实际边距状态
      setActualEdgeDistances({
        top: realTopDistance,
        bottom: realBottomDistance,
        left: realLeftDistance,
        right: realRightDistance
      });
    }
  }, [imagePosition, width, height, originalImage, previewScale, actualWidth, actualHeight, originalWidth, originalHeight]);

  // 弹窗拖动相关处理函数
  const handleModalMouseMove = useCallback((e) => {
    if (isModalDragging) {
      const newX = e.clientX - modalDragOffset.x;
      const newY = e.clientY - modalDragOffset.y;
      
      // 限制在视窗范围内
      const maxX = window.innerWidth - 800; // 弹窗宽度
      const maxY = window.innerHeight - 700; // 弹窗大概高度
      
      setModalPosition({
        x: Math.max(0, Math.min(newX, maxX)),
        y: Math.max(0, Math.min(newY, maxY))
      });
    }
  }, [isModalDragging, modalDragOffset]);

  const handleModalMouseUp = useCallback(() => {
    if (isModalDragging) {
      setIsModalDragging(false);
      
      // 恢复文本选择
      document.body.classList.remove('no-select');
      
      // 移除拖动状态类
      const modalElement = document.querySelector('.extend-size-modal');
      if (modalElement) {
        modalElement.classList.remove('dragging');
      }
    }
  }, [isModalDragging]);

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isModalDragging) {
      document.addEventListener('mousemove', handleModalMouseMove);
      document.addEventListener('mouseup', handleModalMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleModalMouseMove);
        document.removeEventListener('mouseup', handleModalMouseUp);
      };
    }
  }, [isModalDragging, handleModalMouseMove, handleModalMouseUp]);

  // 在弹窗显示时重置拖动位置
  useEffect(() => {
    if (visible) {
      setModalPosition({ x: 0, y: 0 });
      setIsModalDragging(false);
    }
  }, [visible]);

  // 处理点击外部区域关闭弹窗
  const handleOutsideClick = (e) => {
    // 如果点击的是外部容器而不是弹窗本身，则关闭弹窗
    if (e.target.className === 'extend-size-wrapper') {
      onClose();
    }
  };

  const handleModalMouseDown = (e) => {
    // 只在标题区域允许拖动
    if (e.target.closest('.modal-header') && !e.target.closest('button')) {
      setIsModalDragging(true);
      
      const modalElement = e.target.closest('.extend-size-modal');
      const rect = modalElement.getBoundingClientRect();
      
      setModalDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
      
      // 防止文本选择
      document.body.classList.add('no-select');
      modalElement.classList.add('dragging');
      
      e.preventDefault();
    }
  };

  // 在组件最上方添加：
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth <= 768);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return visible ? (
    <div className="extend-size-wrapper" onClick={handleOutsideClick}>
      <div 
        className="modal-content extend-size-modal" 
        style={{
          ...style,
          ...(modalPosition.x !== 0 || modalPosition.y !== 0 ? {
            left: modalPosition.x,
            top: modalPosition.y
          } : {}),
          cursor: isModalDragging ? 'grabbing' : 'default'
        }}
        onMouseDown={handleModalMouseDown}
      >
        <div className="modal-header" style={{ cursor: 'grab' }}>
          <div className="tab-group">
            <button 
              className={`tab-btn ${activeTab === 'platform' ? 'active' : ''}`}
              onClick={() => {
                // 切换到平台尺寸标签页
                setActiveTab('platform');
                // 重置临时状态，但如果有已保存的平台设置则恢复
                if (savedSettings && savedSettings.activeTab === 'platform' && savedSettings.activePlatform) {
                  setActivePlatform(savedSettings.activePlatform);
                  setWidth(savedSettings.width);
                  setHeight(savedSettings.height);
                  // 恢复保存的图片位置
                  if (savedSettings.imagePosition) {
                    setImagePosition(savedSettings.imagePosition);
                  } else {
                    // 重置图片位置到居中
                    setImagePosition({ x: 0, y: 0 });
                  }
                } else {
                  // 否则清空临时选择
                  setActivePlatform(null);
                  setWidth(null);
                  setHeight(null);
                  // 重置图片位置到居中
                  setImagePosition({ x: 0, y: 0 });
                }
                // 清除扩图比例和自定义尺寸的临时选择
                setScale(null);
                setActiveRatio(null);
                // 重置超出限制状态
                setExceedsMaxLimit(false);
              }}
            >
              平台要求尺寸
            </button>
            <button 
              className={`tab-btn ${activeTab === 'scale' ? 'active' : ''}`}
              onClick={() => {
                // 切换到扩图比例标签页
                setActiveTab('scale');
                // 重置临时状态，但如果有已保存的扩图比例设置则恢复
                if (savedSettings && savedSettings.activeTab === 'scale') {
                  setScale(savedSettings.scale);
                  setWidth(savedSettings.width);
                  setHeight(savedSettings.height);
                  setActiveRatio(savedSettings.activeRatio);
                  // 恢复保存的图片位置
                  if (savedSettings.imagePosition) {
                    setImagePosition(savedSettings.imagePosition);
                  } else {
                    // 重置图片位置到居中
                    setImagePosition({ x: 0, y: 0 });
                  }
                } else {
                  // 否则清空临时选择
                  setScale(null);
                  setWidth(null);
                  setHeight(null);
                  setActiveRatio(null);
                  // 重置图片位置到居中
                  setImagePosition({ x: 0, y: 0 });
                }
                // 清除平台和自定义尺寸的临时选择
                setActivePlatform(null);
              }}
            >
              扩图比例
            </button>
            <button 
              className={`tab-btn ${activeTab === 'custom' ? 'active' : ''}`}
              onClick={() => {
                // 切换到自定义尺寸标签页
                setActiveTab('custom');
                // 重置临时状态，但如果有已保存的自定义尺寸设置则恢复
                if (savedSettings && savedSettings.activeTab === 'custom') {
                  // 同时更新customWidth/customHeight和width/height
                  const savedWidth = savedSettings.width;
                  const savedHeight = savedSettings.height;
                  setCustomWidth(savedWidth);
                  setCustomHeight(savedHeight);
                  // 直接设置width和height以确保画布立即更新
                  setWidth(savedWidth);
                  setHeight(savedHeight);
                  
                  // 恢复保存的图片位置
                  if (savedSettings.imagePosition) {
                    setImagePosition(savedSettings.imagePosition);
                  } else {
                    // 重置图片位置到居中
                    setImagePosition({ x: 0, y: 0 });
                  }
                } else {
                  // 否则清空临时选择
                  setCustomWidth('');
                  setCustomHeight('');
                  // 如果有原图尺寸，使用原图尺寸作为初始值
                  if (originalWidth && originalHeight) {
                    setCustomWidth(originalWidth);
                    setCustomHeight(originalHeight);
                    setWidth(originalWidth);
                    setHeight(originalHeight);
                  } else {
                    // 否则清空width和height
                    setWidth(null);
                    setHeight(null);
                  }
                  // 重置图片位置到居中
                  setImagePosition({ x: 0, y: 0 });
                }
                // 清除扩图比例和平台的临时选择
                setScale(null);
                setActiveRatio(null);
                setActivePlatform(null);
                // 重置超出限制状态
                setExceedsMaxLimit(false);
              }}
            >
              自定义尺寸
            </button>
          </div>
          <button className="medium-close-button" onClick={onClose}>
            <MdClose />
          </button>
        </div>
        
        <div className="extend-size-modal-content">
          <div className="extend-size-left">
            <div className="modal-body">
              <div className="extend-size-content">
                {activeTab === 'scale' && (
                  <div className="tab-content scale-tab">
                    <div className="size-info">
                      <div className="original-size">
                        <span className="size-label">原图尺寸</span>
                        <span className="size-value">
                          {hasOriginalImage ? 
                            isLoadingImageDimensions ? '加载中...' : `${displayWidth} × ${displayHeight}px` 
                            : '未上传原图'}
                        </span>
                      </div>
                      {isSameDimensions() && (
                        <div className="size-equality-sign">
                          <span>=</span>
                        </div>
                      )}
                      <div className="upscaled-size">
                        <span className="size-label">扩图后尺寸</span>
                        <span className="size-value">
                          {hasOriginalImage && width && height ? `${width} × ${height}px` : '无法计算'}
                        </span>
                      </div>
                    </div>
                    
                    <div className="scale-buttons">
                      <button 
                        className={`scale-btn ${scale === 1.1 ? 'active' : ''}`}
                        onClick={handle1_1xClick}
                        disabled={!hasOriginalImage}
                      >
                        原比例 1.1倍
                      </button>
                      <button 
                        className={`scale-btn ${scale === 1.2 ? 'active' : ''}`}
                        onClick={handle1_2xClick}
                        disabled={!hasOriginalImage}
                      >
                        原比例 1.2倍
                      </button>
                      <button 
                        className={`scale-btn ${scale === 1.5 ? 'active' : ''}`}
                        onClick={handle1_5xClick}
                        disabled={!hasOriginalImage}
                      >
                        原比例 1.5倍
                      </button>
                    </div>
                    
                    <div className="scale-buttons">
                      <button 
                        className={`scale-btn ${scale === 2 ? 'active' : ''}`}
                        onClick={handle2xClick}
                        disabled={!hasOriginalImage}
                      >
                        原比例 2倍
                      </button>
                      <button 
                        className={`scale-btn ${activeRatio === '1:1' ? 'active' : ''}`}
                        onClick={() => {
                          // 直接设置激活状态并调用处理函数
                          handleAspectRatio(1, 1);
                        }}
                        disabled={!hasOriginalImage}
                      >
                        1 : 1
                      </button>
                    </div>
                    
                    <div className="scale-buttons">
                      <button 
                        className={`scale-btn ${activeRatio === '9:16' ? 'active' : ''}`}
                        onClick={() => handleAspectRatio(9, 16)}
                        disabled={!hasOriginalImage}
                      >
                        9 : 16
                      </button>
                      <button 
                        className={`scale-btn ${activeRatio === '3:4' ? 'active' : ''}`}
                        onClick={() => handleAspectRatio(3, 4)}
                        disabled={!hasOriginalImage}
                      >
                        3 : 4
                      </button>
                      <button 
                        className={`scale-btn ${activeRatio === '2:3' ? 'active' : ''}`}
                        onClick={() => handleAspectRatio(2, 3)}
                        disabled={!hasOriginalImage}
                      >
                        2 : 3
                      </button>
                    </div>
                    
                    <div className="scale-buttons">
                      <button 
                        className={`scale-btn ${activeRatio === '16:9' ? 'active' : ''}`}
                        onClick={() => handleAspectRatio(16, 9)}
                        disabled={!hasOriginalImage}
                      >
                        16 : 9
                      </button>
                      <button 
                        className={`scale-btn ${activeRatio === '4:3' ? 'active' : ''}`}
                        onClick={() => handleAspectRatio(4, 3)}
                        disabled={!hasOriginalImage}
                      >
                        4 : 3
                      </button>
                      <button 
                        className={`scale-btn ${activeRatio === '3:2' ? 'active' : ''}`}
                        onClick={() => handleAspectRatio(3, 2)}
                        disabled={!hasOriginalImage}
                      >
                        3 : 2
                      </button>
                    </div>
                    
                    <div className={`size-limit-notice ${isSameDimensions() ? 'warning' : ''} ${isAnySmallerThanOriginal ? 'size-limit-exceeded' : ''}`}>
                      {isSameDimensions() 
                        ? "注意：扩图后尺寸与原图尺寸相同，无需进行处理" 
                        : isAnySmallerThanOriginal
                          ? "注意：扩图后的尺寸需要大于原图，小于 3000px × 3000px。若超过 2000px × 2000px 将耗时较长，请耐心等待"
                          : "注意：扩图后的尺寸需要大于原图，小于 3000px × 3000px。若超过 2000px × 2000px 将耗时较长，请耐心等待"}
                    </div>
                    
                    {!hasOriginalImage && (
                      <div className="no-image-warning">
                        请先上传原图，才能设置扩图比例和尺寸
                      </div>
                    )}
                  </div>
                )}
                
                {activeTab === 'custom' && (
                  <div className="tab-content custom-tab">
                    <div className="size-info">
                      <div className="original-size">
                        <span className="size-label">原图尺寸</span>
                        <span className="size-value">
                          {hasOriginalImage ? 
                            isLoadingImageDimensions ? '加载中...' : `${displayWidth} × ${displayHeight}px` 
                            : '未上传原图'}
                        </span>
                      </div>
                    </div>
                    
                    {hasOriginalImage ? (
                      <div className="custom-size-inputs">
                        <div className="size-input-container">
                          <div className="size-input-group">
                            <span className="size-label">宽</span>
                            <input
                              type="number"
                              className="size-input"
                              value={customWidth}
                              onChange={handleCustomWidthChange}
                              onBlur={handleCustomWidthBlur}
                              min={originalWidth}
                              max={3000}
                              disabled={!hasOriginalImage}
                            />
                            <span className="size-unit">px</span>
                          </div>
                          
                          <button 
                            className="aspect-ratio-btn"
                            onClick={toggleLockAspectRatio}
                            title={lockAspectRatio ? "解锁长宽比" : "锁定长宽比"}
                            disabled={!hasOriginalImage}
                          >
                            {lockAspectRatio ? <MdLock /> : <MdLockOpen />}
                          </button>
                          
                          <div className="size-input-group">
                            <span className="size-label">高</span>
                            <input
                              type="number"
                              className="size-input"
                              value={customHeight}
                              onChange={handleCustomHeightChange}
                              onBlur={handleCustomHeightBlur}
                              min={originalHeight}
                              max={3000}
                              disabled={!hasOriginalImage}
                            />
                            <span className="size-unit">px</span>
                          </div>
                        </div>
                        
                        <div className={`size-limit-notice ${isSameDimensions() ? 'warning' : ''} ${isAnySmallerThanOriginal ? 'size-limit-exceeded' : ''} ${exceedsMaxLimit && customWidth && customHeight ? 'size-limit-exceeded' : ''}`}>
                          {isSameDimensions() 
                            ? "警告：扩图后尺寸与原图尺寸相同，无需进行处理" 
                            : isAnySmallerThanOriginal
                              ? "注意：扩图后的尺寸需要大于原图，小于 3000px × 3000px。若超过 2000px × 2000px 将耗时较长，请耐心等待"
                              : exceedsMaxLimit && customWidth && customHeight
                                ? "警告：尺寸已超出最大限制 3000px × 3000px，将被自动调整"
                                : "注意：扩图后的尺寸需要大于原图，小于 3000px × 3000px。若超过 2000px × 2000px 将耗时较长，请耐心等待"}
                        </div>
                      </div>
                    ) : (
                      <div className="no-image-warning">
                        请先上传原图，才能设置扩图比例和尺寸
                      </div>
                    )}
                  </div>
                )}
                
                {activeTab === 'platform' && (
                  <div className="tab-content platform-tab">
                    <div className="size-info platform-size-info">
                      <div className="original-size">
                        <span className="size-label">原图尺寸</span>
                        <span className="size-value">
                          {hasOriginalImage ? 
                            isLoadingImageDimensions ? '加载中...' : `${displayWidth} × ${displayHeight}px` 
                            : '未上传原图'}
                        </span>
                      </div>
                    </div>
                    
                    <div className="platform-section">
                      <div className="platform-grid">
                        <button 
                          className={`platform-btn ${activePlatform === 'default' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1024, 1024, 'default')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/default.png" alt="默认尺寸" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">默认尺寸</span>
                            <span className="platform-size">1024 × 1024</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'amazon' ? 'active' : ''}`}                          onClick={() => handlePlatformSize(1600, 1600, 'amazon')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/amazon.png" alt="亚马逊" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">亚马逊</span>
                            <span className="platform-size">1600 × 1600</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'shein' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1340, 1785, 'shein')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/shein.png" alt="SHEIN" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">SHEIN</span>
                            <span className="platform-size">1340 × 1785</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'temu' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1200, 1200, 'temu')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/temu.png" alt="Temu" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Temu</span>
                            <span className="platform-size">1200 × 1200</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'tiktok' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1024, 1024, 'tiktok')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/tiktok.png" alt="Tiktok Shop" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Tiktok Shop</span>
                            <span className="platform-size">1024 × 1024</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'aliexpress' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1200, 1200, 'aliexpress')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/aliexpress.png" alt="速卖通" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">速卖通</span>
                            <span className="platform-size">1200 × 1200</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'shopee' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1080, 1080, 'shopee')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/Shopee.png" alt="Shopee" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Shopee</span>
                            <span className="platform-size">1080 × 1080</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'lazada' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1080, 1080, 'lazada')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/lazada.png" alt="Lazada" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Lazada</span>
                            <span className="platform-size">1080 × 1080</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'walmart' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(2000, 2000, 'walmart')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/walmart.png" alt="沃尔玛" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">沃尔玛</span>
                            <span className="platform-size">2000 × 2000</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'etsy' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(2000, 2000, 'etsy')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/etsy.png" alt="Etsy" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Etsy</span>
                            <span className="platform-size">2000 × 2000</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'mercado' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1200, 1200, 'mercado')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/mercado.png" alt="美客多" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">美客多</span>
                            <span className="platform-size">1200 × 1200</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'ebay' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1600, 1600, 'ebay')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/ebay.png" alt="eBay" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">eBay</span>
                            <span className="platform-size">1600 × 1600</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'shopify-square' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(2048, 2048, 'shopify-square')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/shopify-square.png" alt="Shopify 方形" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Shopify 方形</span>
                            <span className="platform-size">2048 × 2048</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'shopify-landscape' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(2000, 1800, 'shopify-landscape')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/shopify-landscape.png" alt="Shopify 横版" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Shopify 横版</span>
                            <span className="platform-size">2000 × 1800</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'shopify-portrait' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1600, 2000, 'shopify-portrait')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/shopify-portrait.png" alt="Shopify 竖版" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Shopify 竖版</span>
                            <span className="platform-size">1600 × 2000</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'taobao' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1024, 1024, 'taobao')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/taobao.png" alt="淘宝/天猫" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">淘宝/天猫</span>
                            <span className="platform-size">1024 × 1024</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'jd' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1024, 1024, 'jd')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/jd.png" alt="京东" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">京东</span>
                            <span className="platform-size">1024 × 1024</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'douyin' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1024, 1024, 'douyin')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/douyin.png" alt="抖音小店" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">抖音小店</span>
                            <span className="platform-size">1024 × 1024</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'wayfair' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(2000, 2000, 'wayfair')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/wayfair.png" alt="Wayfair" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Wayfair</span>
                            <span className="platform-size">2000 × 2000</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'cdiscount' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(2000, 2000, 'cdiscount')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/Cdiscount.png" alt="Cdiscount" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Cdiscount</span>
                            <span className="platform-size">2000 × 2000</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'jumia' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1024, 1024, 'jumia')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/Jumia.png" alt="Jumia" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Jumia</span>
                            <span className="platform-size">1024 × 1024</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'rakuten' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1024, 1024, 'rakuten')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/rakuten.png" alt="Rakuten" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Rakuten</span>
                            <span className="platform-size">1024 × 1024</span>
                          </div>
                        </button>
                        
                        <button 
                          className={`platform-btn ${activePlatform === 'wish' ? 'active' : ''}`}
                          onClick={() => handlePlatformSize(1024, 1024, 'wish')}
                          disabled={!hasOriginalImage}
                        >
                          <div className="platform-icon">
                            <img src="https://file.aibikini.cn/config/icons/activePlatform/wish.png" alt="Wish" />
                          </div>
                          <div className="platform-text">
                            <span className="platform-name">Wish</span>
                            <span className="platform-size">1024 × 1024</span>
                          </div>
                        </button>
                      </div>
                    </div>
                    
                    <div className={`size-limit-notice ${isSameDimensions() ? 'warning' : ''} ${isAnySmallerThanOriginal ? 'size-limit-exceeded' : ''} ${exceedsMaxLimit && width && height ? 'size-limit-exceeded' : ''}`}>
                      {isSameDimensions() 
                        ? "警告：扩图后尺寸与原图尺寸相同，无需进行处理" 
                        : isAnySmallerThanOriginal
                          ? "注意：扩图后的尺寸需要大于原图，小于 3000px × 3000px。若超过 2000px × 2000px 将耗时较长，请耐心等待"
                          : exceedsMaxLimit && width && height
                            ? "警告：尺寸已超出最大限制 3000px × 3000px，将被自动调整"
                            : "注意：平台尺寸可能未及时更新，请以平台最新尺寸为准"}
                    </div>
                    
                    {!hasOriginalImage && (
                      <div className="no-image-warning">
                        请先上传原图，才能设置扩图比例和尺寸
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="extend-size-preview">
            {width && height ? (
              <>
                {originalImage && <div className="canvas-hint">拖动图片调整扩图位置</div>}
                <div 
                  ref={canvasRef}
                  className="canvas-preview canvas-preview-bg" 
                  style={{
                    width: `${previewWidth}px`,
                    height: `${previewHeight}px`,
                  }}
                >
                  {/* 添加居中指示线 */}
                  {isDragging && showCenterX && (
                    <div className="center-guide center-guide-vertical"></div>
                  )}
                  {isDragging && showCenterY && (
                    <div className="center-guide center-guide-horizontal"></div>
                  )}
                  
                  {/* 添加原始图片到画布中 - 使用ref直接控制transform */}
                  {originalImage && originalWidth && originalHeight && (
                    <div 
                      ref={imageRef}
                      className={`canvas-image-container ${isDragging ? 'dragging' : ''}`}
                      style={{
                        width: `${originalPreviewWidth}px`,
                        height: `${originalPreviewHeight}px`,
                        transform: `translate(${imagePosition.x}px, ${imagePosition.y}px)`,
                        willChange: 'transform' /* 添加willChange提示浏览器优化 */
                      }}
                      onMouseDown={handleDragStart}
                      onTouchStart={handleDragStart}
                    >
                      <img 
                        src={originalImage} 
                        alt="原始图片"
                        className="canvas-image"
                        draggable="false"
                      />
                    </div>
                  )}
                </div>
                
                {/* 添加边缘距离显示 */}
                {originalImage && (
                  <div className="edge-distances-display">
                    <div className="distances-row">
                      <div className="distance-item">
                        <span className="distance-label">上:</span>
                        <span className={`size-value ${actualEdgeDistances.top < 0 ? 'negative-value' : ''}`}>{actualEdgeDistances.top}px</span>
                      </div>
                      <div className="distance-item">
                        <span className="distance-label">下:</span>
                        <span className={`size-value ${actualEdgeDistances.bottom < 0 ? 'negative-value' : ''}`}>{actualEdgeDistances.bottom}px</span>
                      </div>
                    </div>
                    <div className="distances-row">
                      <div className="distance-item">
                        <span className="distance-label">左:</span>
                        <span className={`size-value ${actualEdgeDistances.left < 0 ? 'negative-value' : ''}`}>{actualEdgeDistances.left}px</span>
                      </div>
                      <div className="distance-item">
                        <span className="distance-label">右:</span>
                        <span className="size-value">{actualEdgeDistances.right}px</span>
                      </div>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="canvas-dimensions">
                {isMobile ? '请在上方设置扩图尺寸' : '请在左侧设置扩图尺寸'}
              </div>
            )}
          </div>
        </div>

        <div className="modal-footer">
          <button
            className="clear-btn"
            onClick={onClose}
          >
            取消
          </button>
          <button 
            className="save-settings-btn" 
            onClick={handleApply}
            disabled={!hasOriginalImage || !width || !height || (activeTab === 'custom' && (!customWidth || !customHeight)) || isAnySmallerThanOriginal}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  ) : null;
};

ExtendSizeModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onApply: PropTypes.func.isRequired,
  defaultScale: PropTypes.number,
  originalWidth: PropTypes.number,
  originalHeight: PropTypes.number,
  originalImage: PropTypes.string,
  style: PropTypes.object,
  savedSettings: PropTypes.object,
  pageType: PropTypes.string
};

export default ExtendSizeModal; 
