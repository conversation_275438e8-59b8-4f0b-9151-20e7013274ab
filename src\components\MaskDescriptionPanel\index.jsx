import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { MdOutlineHelpOutline } from 'react-icons/md';
import './index.css';
import TipPopup from '../TipPopup';

// 标签与实际传递给后端内容的映射关系
export const tagMapping = {
  '分体泳衣': 'bra, panties',
  '连体泳衣': 'swimsuit',
  '裙式泳衣': 'bra, skirt'
};

/**
 * 蒙版描述组件 - 用于向后端传递蒙版的描述词文本
 * 
 * 此组件包含标签按钮和文本输入框，用于向comfyUI工作流中的特定节点传递文本
 */
const MaskDescriptionPanel = ({
  panel,
  onPanelsChange,
  description,
  onChange,
  placeholder = '请输入对蒙版区域的优化描述，例如"白色蕾丝花边"'
}) => {
  // 默认关键词标签选项
  const tagOptions = ['分体泳衣', '连体泳衣', '裙式泳衣'];
  
  // 本地状态，用于存储选中的标签和输入的文本
  const [selectedTag, setSelectedTag] = useState(panel?.selectedTag || '');
  const [customText, setCustomText] = useState(panel?.customText || '');
  
  // 提示弹窗相关状态
  const [isTipVisible, setIsTipVisible] = useState(false);
  const [tipPosition, setTipPosition] = useState({ left: 0, top: 0 });
  const tipButtonRef = useRef(null);
  
  // 监听panel属性变化，同步更新内部状态
  useEffect(() => {
    // 当panel属性变化时，同步更新内部状态
    if (panel) {
      setSelectedTag(panel.selectedTag || '');
      setCustomText(panel.customText || '');
      console.log('MaskDescriptionPanel状态已同步：', panel);
    }
  }, [panel]);
  
  // 当外部description变化时，更新本地状态
  useEffect(() => {
    if (description) {
      setCustomText(description);
    }
  }, [description]);
  
  // 处理标签点击
  const handleTagClick = (tag) => {
    const newTag = selectedTag === tag ? '' : tag;
    setSelectedTag(newTag);
    
    // 如果选择了新标签，则清空自定义文本
    if (newTag) {
      setCustomText('');
    }
    
    // 获取映射后的实际传递内容
    const mappedValue = newTag ? tagMapping[newTag] : '';
    
    // 更新面板数据
    if (onPanelsChange) {
      onPanelsChange({
        ...panel,
        selectedTag: newTag,
        // 如果选择了标签，则清空自定义文本
        customText: newTag ? '' : customText,
        // 使用映射后的值作为描述内容传递给后端
        description: mappedValue
      });
    }
  };
  
  // 处理输入框点击 - 自动取消标签选择
  const handleInputClick = () => {
    if (selectedTag) {
      setSelectedTag('');
      
      // 更新面板数据
      if (onPanelsChange) {
        onPanelsChange({
          ...panel,
          selectedTag: '',
          customText: customText,
          description: customText
        });
      }
    }
  };
  
  // 处理文本输入变化
  const handleTextChange = (e) => {
    const newText = e.target.value;
    setCustomText(newText);
    
    // 如果输入了文本，则清除已选标签
    if (newText && selectedTag) {
      setSelectedTag('');
    }
    
    // 更新面板数据
    if (onPanelsChange) {
      onPanelsChange({
        ...panel,
        // 如果输入了文本，则清除标签选择
        selectedTag: newText ? '' : selectedTag,
        customText: newText,
        // 使用自定义文本作为描述
        description: newText
      });
    }
    
    // 通知父组件文本变化
    if (onChange) {
      onChange(newText);
    }
  };

  // 处理提示按钮点击
  const handleShowTip = () => {
    if (tipButtonRef.current) {
      const rect = tipButtonRef.current.getBoundingClientRect();
      setTipPosition({
        left: rect.left + rect.width + 28,
        top: rect.top - 8
      });
    }
    setIsTipVisible(true);
  };

  // 处理关闭提示
  const handleCloseTip = () => {
    setIsTipVisible(false);
  };

  return (
    <>
      <div className="mask-description-panel">
        <div className="mask-content">
          <div className="mask-label">
            <span>款式描述</span>
          </div>
          <div className="mask-area">
            <div className="mask-description-content">
              <div className="tags-options">
                {tagOptions.map((tag) => (
                  <button 
                    key={tag}
                    className={`tag-option ${selectedTag === tag ? 'active' : ''}`}
                    onClick={() => handleTagClick(tag)}
                  >
                    {tag}
                  </button>
                ))}
              </div>
              
              <input
                type="text"
                value={customText}
                onChange={handleTextChange}
                onClick={handleInputClick}
                placeholder="请选择上方标签或输入自定义描述"
                className="mask-description-input"
              />
            </div>
          </div>
        </div>
        
        {/* 提示按钮 */}
        <button 
          className="tip-button-common text-description-tip-button"
          onClick={handleShowTip}
          ref={tipButtonRef}
          title="查看使用提示"
        >
          <span className="tip-text">点我</span>
          <MdOutlineHelpOutline />
        </button>
      </div>
      
      {/* 提示弹窗 */}
      <TipPopup 
        type="mask-description"
        position={tipPosition}
        isVisible={isTipVisible}
        onClose={handleCloseTip}
        content={[
          "• 务必填写正确，否则影响生成效果",
          "• 建议使用简洁的英文或中文短语，用逗号分隔",
          "• 可以选择标签上的款式，也可以自己填写款式描述",
        ].join('\n')}
      />
    </>
  );
};

MaskDescriptionPanel.propTypes = {
  panel: PropTypes.shape({
    componentId: PropTypes.string.isRequired,
    selectedTag: PropTypes.string,
    customText: PropTypes.string,
    description: PropTypes.string
  }),
  onPanelsChange: PropTypes.func,
  description: PropTypes.string,
  onChange: PropTypes.func,
  placeholder: PropTypes.string
};

export default MaskDescriptionPanel; 