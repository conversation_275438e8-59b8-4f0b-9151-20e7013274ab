/* RandomSeedSelector组件独立样式 */
.random-seed-selector {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  height: 88px;
  display: flex;
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.random-seed-selector .selector-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  gap: var(--spacing-sm);
}

.random-seed-selector .selector-label {
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
}

.random-seed-selector .selector-label span {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

.random-seed-selector .selector-area {
  flex: 1;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
}

/* 组件文本容器 */
.random-seed-selector .component-text {
  width: 100%;
}

/* 为开关设置样式 */
.random-seed-selector h3 .toggle-switch {
  margin-left: 12px; /* 开关与标题的间距 */
}

/* 确保内容区域高度固定 */
.random-seed-selector .component-content {
  min-height: 28px; /* 保持与输入框相同的高度 */
}

/* 确保文本和输入框具有相同的垂直空间 */
.random-seed-selector .component-content p {
  margin: 4px 10px; /* 保持左右边距 */
  line-height: 20px; /* 匹配输入框内容高度 */
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* 种子输入框容器样式 */
.random-seed-selector .seed-input-container {
  display: flex;
  align-items: center;
  margin: 4px 10px; /* 保持左右边距 */
  width: calc(100% - 20px); /* 考虑左右边距后的宽度 */
  gap: 8px; /* 添加间距 */
}

/* 种子输入框包装器样式 */
.random-seed-selector .seed-input-wrapper {
  position: relative;
  display: inline-block;
  flex: 1; /* 允许伸缩 */
  min-width: 100px; /* 最小宽度改为100px */
  max-width: 280px; /* 最大宽度改为280px */
}

/* 种子值标签样式 */
.random-seed-selector .seed-label {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin-right: 10px;
  white-space: nowrap; /* 防止标签换行 */
  flex-shrink: 0; /* 防止标签被压缩 */
}

/* 种子输入框样式 */
.random-seed-selector .seed-input {
  width: 100%; /* 改为100%自适应 */
  flex: 1; /* 允许伸缩 */
  min-width: 100px; /* 最小宽度改为100px */
  max-width: 280px; /* 最大宽度改为280px */
  height: 24px; /* 设置高度为24px */
  line-height: 24px; /* 保持与高度一致 */
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: 0 24px 0 8px; /* 右侧留出空间给数值调节按钮 */
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  background: var(--bg-primary);
  text-align: center; /* 改为居中对齐 */
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
  /* 移除number类型输入框的上下箭头 */
  -webkit-appearance: none;
  -moz-appearance: textfield;
  appearance: textfield;
  /* 过渡效果 */
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center; /* 改为居中对齐 */
    /* 添加省略号样式，防止长数字被误解 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 数值调节按钮位置样式 - 适应RandomSeedSelector输入框 */
.random-seed-selector .number-controls {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 0;
  height: 24px; /* 与输入框高度一致 */
  justify-content: center;
}

/* RandomSeedSelector特定的数值调节按钮样式 */
.random-seed-selector .number-control-btn {
  width: 12px;
  height: 10px;
  padding: 0;
  border: none;
  background: transparent;
  color: var(--text-tertiary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
  line-height: 1;
  margin: 0;
}

.random-seed-selector .number-control-btn:hover {
  color: var(--brand-primary);
}

.random-seed-selector .number-control-btn svg {
  width: 10px;
  height: 10px;
}

/* 兼容Firefox */
.random-seed-selector .seed-input::-webkit-outer-spin-button,
.random-seed-selector .seed-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* 输入框激活样式 */
.random-seed-selector .seed-input:hover {
  border-color: var(--brand-primary);
}

.random-seed-selector .seed-input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(255, 60, 106, 0.1);
}

[data-theme="dark"] .random-seed-selector .seed-input:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

/* 复制按钮样式 */
.random-seed-selector .copy-seed-btn {
  width: 22px;
  height: 22px;
  min-width: 22px;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  padding: 0;
  flex-shrink: 0; /* 防止按钮被压缩 */
  margin-left: 8px;
}

/* 骰子按钮样式 */
.random-seed-selector .dice-seed-btn {
  width: 24px;
  height: 24px;
  min-width: 24px;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  padding: 0;
  flex-shrink: 0; /* 防止按钮被压缩 */
  margin-left: 0px; /* 向左移动4px，原来是4px */
}

.random-seed-selector .copy-seed-btn svg {
  width: 14px;
  height: 14px;
}

.random-seed-selector .dice-seed-btn svg {
  width: 16px;
  height: 16px;
}

.random-seed-selector .copy-seed-btn:hover,
.random-seed-selector .dice-seed-btn:hover {
  color: var(--brand-primary);
}

.random-seed-selector .copy-seed-btn:disabled,
.random-seed-selector .dice-seed-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 输入框禁用样式 */
.random-seed-selector .seed-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .seed-input {
    padding-right: 10px !important;
  }
  .random-seed-btn {
    right: 2px !important;
  }
  .seed-label {
    display: none !important;
  }
  /* 隐藏原生number输入框的上下调动按钮 */
  .seed-input::-webkit-outer-spin-button,
  .seed-input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
    display: none !important;
  }
  .seed-input[type="number"] {
    -moz-appearance: textfield !important;
  }
}
