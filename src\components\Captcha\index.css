.captcha-container {
  width: 100%;
  max-width: 360px;
  margin: 0 auto;
  padding: 0;
}

.captcha-box {
  background: var(--bg-primary, #fff);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  transition: all 0.3s ease;
}

[data-theme="dark"] .captcha-box {
  background: var(--bg-primary, #1f1f1f);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.captcha-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary, #333);
  margin: 0 0 20px;
  text-align: center;
}

[data-theme="dark"] .captcha-title {
  color: var(--text-primary, #e0e0e0);
}

.captcha-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.captcha-svg-container {
  width: 100%;
  height: 80px;
  background: var(--bg-secondary, #f7f7f7);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  margin-bottom: 12px;
  border: 1px solid var(--border-color, #eaeaea);
}

[data-theme="dark"] .captcha-svg-container {
  background: var(--bg-secondary, #2a2a2a);
  border-color: var(--border-color, #3a3a3a);
}

.captcha-svg {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.captcha-svg svg {
  width: 100%;
  height: 100%;
}

.refresh-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

[data-theme="dark"] .refresh-button {
  background: rgba(40, 40, 40, 0.8);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.refresh-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: rotate(30deg);
}

[data-theme="dark"] .refresh-button:hover {
  background: rgba(60, 60, 60, 1);
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-button::before {
  content: "↻";
  font-size: 18px;
  color: var(--text-secondary, #666);
}

[data-theme="dark"] .refresh-button::before {
  color: var(--text-secondary, #aaa);
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.input-container {
  position: relative;
  width: 100%;
}

.input-group input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 8px;
  font-size: 16px;
  background: var(--bg-primary, #fff);
  color: var(--text-primary, #333);
  transition: all 0.2s ease;
}

[data-theme="dark"] .input-group input {
  background: var(--bg-secondary, #2a2a2a);
  border-color: var(--border-color, #3a3a3a);
  color: var(--text-primary, #e0e0e0);
}

.input-group input:focus {
  outline: none;
  border-color: var(--brand-primary, #4a90e2);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

[data-theme="dark"] .input-group input:focus {
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

.verify-button {
  background: var(--brand-gradient, linear-gradient(135deg, #FF3C6A 0%, #FF9B5F 100%));
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 0;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.verify-button:hover:not(:disabled) {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.verify-button:active:not(:disabled) {
  transform: translateY(1px);
}

.verify-button:disabled {
  background: var(--bg-disabled, #ccc);
  color: var(--text-disabled, #888);
  cursor: not-allowed;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary, #666);
  font-size: 14px;
  height: 100%;
  width: 100%;
}

[data-theme="dark"] .loading {
  color: var(--text-secondary, #aaa);
}

.loading::after {
  content: '';
  width: 20px;
  height: 20px;
  margin-left: 10px;
  border: 2px solid var(--border-color, #ddd);
  border-top-color: var(--brand-primary, #4a90e2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error {
  color: var(--error-color, #ff4d4f);
  font-size: 14px;
  text-align: center;
  padding: 10px;
}

.error-message {
  color: var(--error-color, #ff4d4f);
  font-size: 13px;
  margin-top: 8px;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 