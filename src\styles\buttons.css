/**
 * 统一按钮样式定义
 * 
 * 包含以下按钮类型：
 * 1. 按钮组 (.button-group) - 用于模态框底部等多按钮布局
 * 2. 清空按钮 (.clear-btn) - 用于清空表单或重置状态
 * 3. 确认按钮 (.save-settings-btn) - 用于保存设置或确认操作
 * 4. 预览按钮 (.preview-button) - 用于图片预览等功能
 * 5. 过滤器按钮 (.filter-option) - 用于列表筛选
 * 6. 操作按钮 (.action-btn) - 用于普通操作
 * 7. 生成按钮 (.generate-btn) - 用于主要操作
 * 8. 开关按钮 (.toggle-switch) - 用于开关切换状态
 * 9. 复制按钮 (.copy-id-btn) - 用于复制文本内容
 * 10. 数值调节按钮 (.number-control-btn) - 用于数值输入框的上下调节
 * 
 * 使用示例：
 * <div class="button-group">
 *   <button class="clear-btn">清空</button>
 *   <button class="save-settings-btn">确认</button>
 * </div>
 * 
 * 开关按钮使用示例：
 * <label class="toggle-switch">
 *   <input type="checkbox" checked />
 *   <span class="toggle-track"></span>
 * </label>
 */

@import './theme.css';

/* 按钮容器样式 */
.button-group {
  margin-top: var(--spacing-lg);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

/* 清空按钮样式 - 用于次要的清除/取消操作 */
.clear-btn {
  padding: 0 var(--spacing-lg);
  height: 32px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.clear-btn:hover {
  background: var(--bg-active);
  color: var(--text-primary);
}

.clear-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 确认按钮样式 - 用于保存/确认等重要操作 */
.save-settings-btn {
  padding: 0 var(--spacing-lg);
  height: 32px;
  background: var(--brand-gradient);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.save-settings-btn:hover {
  filter: brightness(1.1);
}

.save-settings-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 预览按钮样式 - 用于图片预览等功能 */
.preview-button {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-sm);
  border: none;
  background: var(--bg-hover);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  padding: 0;
  flex-shrink: 0;
}

.preview-button:hover {
  background: var(--bg-active);
  color: var(--text-primary);
}

/* 过滤器按钮样式 - 用于列表筛选等场景 */
.filter-option {
  padding: var(--spacing-xxs) var(--spacing-sm);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-normal);
}

.filter-option:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.filter-option.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 通用操作按钮样式 - 用于普通操作 */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: none;
}

.action-btn:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 生成按钮样式 - 用于主要操作 */
.generate-btn {
  width: 100%;
  padding: var(--spacing-sm) 0;
  background: var(--brand-gradient); /* 使用品牌渐变背景 */
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-md);
  cursor: pointer;
  position: relative; /* 添加相对定位用于悬停效果 */
  overflow: hidden; /* 隐藏溢出内容 */
  transition: background 0.1s ease;
  display: flex; /* 添加flex布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  gap: var(--spacing-xs); /* 添加间距 */
}

/* 算力值显示样式 */
.generate-btn .compute-cost {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.generate-btn .compute-cost::before {
  content: '';
  font-size: var(--font-size-xs);
  opacity: 0.8;
}

.generate-btn .compute-cost::after {
  content: 'C';
  font-size: var(--font-size-xs);
  opacity: 0.8;
}

/* 添加光效扫过效果 */
.generate-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s;
}

.generate-btn:hover:not(:disabled)::after {
  left: 100%;
}

.generate-btn:hover:not(:disabled) {
  background: var(--brand-gradient); /* 保持渐变背景 */
}

/* 暗黑主题下的生成按钮样式 */
[data-theme="dark"] .generate-btn {
  color: var(--text-inverse); /* 暗黑模式下使用深色文本 */
  background: var(--brand-gradient); /* 使用品牌渐变背景，与注册按钮一致 */
  position: relative;
  overflow: hidden;
}

[data-theme="dark"] .generate-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s;
}

[data-theme="dark"] .generate-btn:hover:not(:disabled)::after {
  left: 100%;
}

[data-theme="dark"] .generate-btn:hover:not(:disabled) {
  background: var(--brand-gradient); /* 保持渐变背景 */
}

.generate-btn:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.7;
  position: relative;
}

.generate-btn:disabled::after {
  display: none; /* 禁用状态下不显示光效 */
}

/* =============== 开关按钮样式 =============== */
/* 开关容器 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 18px;
  vertical-align: middle;
}

/* 隐藏原始复选框 */
.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
  display: none;
}

/* 开关滑块的轨道 */
.toggle-track {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-active);
  transition: all 0.3s ease;
  border-radius: 10px;
  box-shadow: none;
}

/* 滑块上的圆形按钮 */
.toggle-track:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 2px;
  bottom: 2px;
  background-color: var(--bg-primary);
  transition: all 0.3s ease;
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

/* 选中时的样式 */
input:checked + .toggle-track {
  background: var(--brand-gradient);
  box-shadow: none;
}

/* 选中时滑块移动 */
input:checked + .toggle-track:before {
  transform: translateX(14px);
  background-color: var(--bg-primary);
  box-shadow: var(--shadow-sm);
}

/* 禁用状态 */
.toggle-switch input:disabled + .toggle-track {
  opacity: 0.6;
  cursor: not-allowed;
}

.toggle-switch input:disabled + .toggle-track:before {
  background-color: #f5f5f5;
}

/* =============== 文本区域拖动按钮样式 =============== */
.textarea-resize-handle {
  position: absolute;
  bottom: 3px;
  right: 3px;
  width: 12px;
  height: 12px;
  cursor: se-resize;
  opacity: 0.7;
  transition: opacity 0.2s;
  z-index: 10; /* 确保在输入框上层 */
}

.textarea-resize-handle:hover {
  opacity: 1;
}

/* 使用简单的右下角三角形标识 */
.textarea-resize-handle::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 0 10px 10px;
  border-color: transparent transparent var(--border-color) transparent;
}

.textarea-resize-handle:hover::after {
  border-color: transparent transparent var(--brand-primary) transparent;
}

/* =============== 复制按钮统一样式 =============== */
/* 统一复制按钮样式 - 全站使用 */
.copy-id-btn,
.task-card .copy-id-btn,
.details-sidebar .copy-id-btn,
.random-seed-selector .copy-id-btn,
.description-copy-btn {
  width: 22px !important;
  height: 22px !important;
  min-width: 22px !important;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  padding: 0;
  margin: 0;
  flex-shrink: 0;
}

.copy-id-btn svg,
.task-card .copy-id-btn svg,
.details-sidebar .copy-id-btn svg,
.random-seed-selector .copy-id-btn svg,
.description-copy-btn svg {
  width: 14px !important;
  height: 14px !important;
}

.copy-id-btn:hover,
.task-card .copy-id-btn:hover,
.details-sidebar .copy-id-btn:hover,
.random-seed-selector .copy-id-btn:hover,
.description-copy-btn:hover {
  color: var(--brand-primary);
}

.copy-id-btn:disabled,
.task-card .copy-id-btn:disabled,
.details-sidebar .copy-id-btn:disabled,
.random-seed-selector .copy-id-btn:disabled,
.description-copy-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 描述词复制按钮特殊定位样式 */
.description-copy-btn {
  position: absolute;
  right: 8px;
  top: 8px;
}

/* RandomSeedSelector复制按钮特殊边距 */
.random-seed-selector .copy-id-btn {
  margin-left: 8px !important;
}

/* =============== 数值调节按钮样式 =============== */
/* 数值输入框的上下调节按钮组合 - 用于颜色值、参数调节等场景 */
.number-controls {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.number-control-btn {
  width: 16px;
  height: 14px;
  padding: 0;
  border: none;
  background: transparent;
  color: var(--text-tertiary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.number-control-btn:hover {
  color: var(--brand-primary);
}

.number-control-btn svg {
  width: 14px;
  height: 14px;
}

/* 数值输入框包装器样式 - 提供相对定位基础 */
.number-input-wrapper {
  position: relative;
  display: inline-block;
}

/* 数值输入框基础样式 */
.number-input-wrapper .number-input {
  padding-right: 24px; /* 为调节按钮预留空间 */
}

/* 使用示例注释：
 * <div class="number-input-wrapper" 
 *      onMouseEnter={() => setActiveInput('value')}
 *      onMouseLeave={() => setActiveInput(null)}>
 *   <input type="text" className="number-input" value={value} />
 *   {activeInput === 'value' && (
 *     <div className="number-controls">
 *       <button className="number-control-btn" onClick={() => adjustValue(1)}>
 *         <MdExpandLess />
 *       </button>
 *       <button className="number-control-btn" onClick={() => adjustValue(-1)}>
 *         <MdExpandMore />
 *       </button>
 *     </div>
 *   )}
 * </div>
 */

/* 通用tip按钮样式（全局） */
.tip-button-common {
  position: absolute;
  top: 8px;
  right: 4px;
  min-width: 60px;
  height: 28px;
  border-radius: 14px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
  transition: color 0.2s ease;
  z-index: 10;
  box-shadow: none;
  padding: 0 8px;
  font-size: var(--font-size-xs);
}
.tip-button-common:hover {
  color: var(--text-primary);
  background: transparent;
  border: none;
}
.tip-button-common .tip-text {
  font-size: 12px;
  font-weight: 500;
}
.tip-button-common svg {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}
@media (max-width: 768px) {
  .tip-button-common {
    top: 4px;
    right: 8px;
    min-width: 50px;
    height: 24px;
    font-size: 11px;
  }
  .tip-button-common .tip-text {
    font-size: 11px;
  }
  .tip-button-common svg {
    width: 12px;
    height: 12px;
  }
}
@media (max-width: 480px) {
  .tip-button-common {
    display: flex;
    min-width: 50px;
    height: 24px;
    font-size: 11px;
    top: 2px;
    right: 4px;
    padding: 0 4px;
  }
  .tip-button-common .tip-text {
    font-size: 11px;
  }
  .tip-button-common svg {
    width: 12px;
    height: 12px;
  }
} 