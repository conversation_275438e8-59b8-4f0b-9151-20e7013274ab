import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { MdOutlineZoomOutMap } from 'react-icons/md';
import ImagePreviewModal from '../ImagePreviewModal';
import './index.css';

/**
 * 缩略图预览组件 - 用于显示图片缩略图并提供预览功能
 * 
 * 此组件提供：
 * - 图片缩略图显示
 * - 悬浮预览按钮
 * - 图片预览弹窗
 * - 处理状态指示器
 * - 错误状态显示
 */
const ThumbnailWithPreview = ({
  imageUrl,
  alt = "缩略图",
  status = 'completed',
  error = null,
  onError,
  onStatusChange,
  featureName = "图片预览",
  transparentBg = false,
  className = '',
  style = {}
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);

  // 处理预览按钮点击
  const handlePreviewClick = (e) => {
    e.stopPropagation(); // 阻止事件冒泡
    setPreviewVisible(true);
  };

  // 关闭预览弹窗
  const handlePreviewClose = () => {
    setPreviewVisible(false);
  };

  // 处理图片加载错误
  const handleImageError = (e) => {
    console.error('图片加载失败:', e.target.src);
    e.target.style.display = 'none';
    onError?.(e);
    onStatusChange?.('error');
  };

  return (
    <div className={`thumbnail-with-preview ${className}`} style={style}>
      <div className={`selected-model-preview ${transparentBg ? 'transparent-bg' : ''}`}>
        {status === 'processing' ? (
          <div className="processing-indicator">
            <div className="spinner"></div>
          </div>
        ) : status === 'completed' && imageUrl ? (
          <div className="thumbnail-container">
            <img 
              src={imageUrl}
              alt={alt} 
              className="selected-model-thumbnail" 
              onError={handleImageError}
            />
            {/* 悬浮预览按钮 */}
            <div 
              className="thumbnail-overlay"
              onClick={handlePreviewClick}
              title="预览图片"
            >
              <MdOutlineZoomOutMap />
            </div>
          </div>
        ) : status === 'error' ? (
          <div className="error-indicator">
            <span className="error-icon">!</span>
            <span>上传失败</span>
          </div>
        ) : null}
      </div>

      {/* 图片预览弹窗 */}
      <ImagePreviewModal
        visible={previewVisible}
        imageUrl={imageUrl}
        onClose={handlePreviewClose}
        alt={alt}
        featureName={featureName}
      />
    </div>
  );
};

ThumbnailWithPreview.propTypes = {
  imageUrl: PropTypes.string,
  alt: PropTypes.string,
  status: PropTypes.oneOf(['processing', 'completed', 'error']),
  error: PropTypes.string,
  onError: PropTypes.func,
  onStatusChange: PropTypes.func,
  featureName: PropTypes.string,
  transparentBg: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
};

export default ThumbnailWithPreview; 