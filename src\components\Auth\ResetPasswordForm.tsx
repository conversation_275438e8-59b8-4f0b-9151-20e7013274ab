import React, { useState } from 'react';

import { useAuth } from '../../contexts/AuthContext';
import ErrorMessage from '../ErrorMessage';

import CaptchaHandler from './CaptchaHandler';
import PasswordStrength, { checkPasswordStrength } from './PasswordStrength';

// 通知类型接口
interface Notification {
  type: string;
  message: string;
}

// 验证结果接口
interface VerifyResult {
  success: boolean;
  message?: string;
}

// 修改密码结果接口
interface ResetPasswordResult {
  success: boolean;
  message?: string;
}

// Auth类型定义
interface Auth {
  resetPassword: (phone: string, code: string, newPassword: string) => Promise<ResetPasswordResult>;
  sendVerificationCode: (phone: string) => Promise<{ success: boolean; message?: string }>;
  verifyCode: (phone: string, code: string, type?: string) => Promise<VerifyResult>;
  // 其他可能的方法...
}

interface ResetPasswordFormProps {
  onClose?: () => void;
  onResetSuccess?: () => void;
  onCaptchaRequest?: (phone: string) => void;
  className?: string;
}

interface ResetPasswordFormState {
  phone: string;
  captcha: string;
  newPassword: string;
  confirmPassword: string;
}

interface FormErrors {
  phone?: string;
  captcha?: string;
  newPassword?: string;
  confirmPassword?: string;
  submit?: string;
  passwordStrength?: {
    score: number;
    level: string;
    feedback: Array<{
      text: string;
      met: boolean;
    }>;
  };
}

/**
 * 修改密码表单组件
 * 处理用户修改密码的逻辑，包括表单验证、验证码发送和修改密码请求
 */
const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({
  onClose,
  onResetSuccess,
  onCaptchaRequest,
  className = ''
}) => {
  const auth = useAuth() as unknown as Auth;

  // 表单状态
  const [form, setForm] = useState<ResetPasswordFormState>({
    phone: '',
    captcha: '',
    newPassword: '',
    confirmPassword: ''
  });

  // 错误状态
  const [errors, setErrors] = useState<FormErrors>({});

  // 密码可见性状态
  const [newPasswordVisible, setNewPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 表单有效状态
  const [isFormValid, setIsFormValid] = useState(false);

  // 验证码状态
  const [captchaState, setCaptchaState] = useState({
    loading: false,
    countdown: 0
  });

  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({
      ...prev,
      [name]: value
    }));

    // 清除相关错误消息
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }

    // 检查密码强度
    if (name === 'newPassword' && value) {
      const strength = checkPasswordStrength(value);
      setErrors(prev => ({
        ...prev,
        passwordStrength: strength
      }));
    }

    // 在每次输入更改后更新表单有效性
    setTimeout(() => checkFormValidity(), 0);
  };

  // 检查表单有效性但不设置错误
  const checkFormValidity = (): void => {
    const isValid =
      !!form.phone &&
      /^1[3-9]\d{9}$/.test(form.phone) &&
      !!form.captcha &&
      !!form.newPassword &&
      checkPasswordStrength(form.newPassword).score >= 2 &&
      !!form.confirmPassword &&
      form.confirmPassword === form.newPassword;

    setIsFormValid(isValid);
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // 验证手机号
    if (!form.phone) {
      newErrors.phone = '请输入手机号';
    } else if (!/^1[3-9]\d{9}$/.test(form.phone)) {
      newErrors.phone = '请输入有效的手机号';
    }

    // 验证验证码
    if (!form.captcha&&process.env.ENABLE_PHONE_VERIFICATION === 'true') {
      newErrors.captcha = '请输入验证码';
    }

    // 验证新密码
    if (!form.newPassword) {
      newErrors.newPassword = '请输入新密码';
    } else {
      const strength = checkPasswordStrength(form.newPassword);
      if (strength.score < 2) {
        newErrors.newPassword = '密码强度不够';
      }
    }

    // 验证确认密码
    if (!form.confirmPassword) {
      newErrors.confirmPassword = '请确认新密码';
    } else if (form.confirmPassword !== form.newPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }

    setErrors(prev => ({
      ...prev,
      ...newErrors
    }));

    return Object.keys(newErrors).length === 0;
  };

  // 验证验证码
  const validateCaptcha = async (phone: string, code: string): Promise<boolean> => {
    if (!code&&process.env.ENABLE_PHONE_VERIFICATION === 'true') {
      setErrors(prev => ({
        ...prev,
        captcha: '请输入验证码'
      }));
      return false;
    }

    try {
      const result = await auth.verifyCode(phone, code, 'reset');

      if (result.success) {
        return true;
      } else {
        setErrors(prev => ({
          ...prev,
          captcha: result.message || '验证码错误'
        }));
        return false;
      }
    } catch (error) {
      setErrors(prev => ({
        ...prev,
        captcha: error instanceof Error ? error.message : '验证失败，请稍后重试'
      }));
      return false;
    }
  };

  // 处理验证码请求
  const handleCaptchaRequest = async (phone: string) => {
    try {
      setCaptchaState(prev => ({
        ...prev,
        loading: true
      }));

      // 验证手机号
      if (!phone) {
        setErrors(prev => ({
          ...prev,
          phone: '请输入手机号'
        }));
        setCaptchaState(prev => ({ ...prev, loading: false }));
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(phone)) {
        setErrors(prev => ({
          ...prev,
          phone: '请输入有效的手机号'
        }));
        setCaptchaState(prev => ({ ...prev, loading: false }));
        return;
      }

      // 如果提供了onCaptchaRequest回调，则使用它
      if (onCaptchaRequest) {
        await onCaptchaRequest(phone);
        // 验证成功后开始倒计时
        let countdown = 60;
        setCaptchaState({
          loading: false,
          countdown
        });

        const timer = setInterval(() => {
          countdown -= 1;
          if (countdown <= 0) {
            clearInterval(timer);
          }
          setCaptchaState(prev => ({
            ...prev,
            countdown
          }));
        }, 1000);
        return;
      }

      // 否则使用默认实现
      const result = await auth.sendVerificationCode(phone);

      if (result.success) {
        // 开始倒计时
        let countdown = 60;
        setCaptchaState({
          loading: false,
          countdown
        });

        const timer = setInterval(() => {
          countdown -= 1;
          if (countdown <= 0) {
            clearInterval(timer);
          }
          setCaptchaState(prev => ({
            ...prev,
            countdown
          }));
        }, 1000);

        // 显示成功通知
        if (onResetSuccess) {
          onResetSuccess();
        }
      } else {
        setCaptchaState(prev => ({
          ...prev,
          loading: false
        }));

        setErrors(prev => ({
          ...prev,
          captcha: result.message || '发送验证码失败'
        }));
      }
    } catch (error) {
      setCaptchaState({
        loading: false,
        countdown: 0
      });

      setErrors(prev => ({
        ...prev,
        captcha: error instanceof Error ? error.message : '发送验证码失败，请稍后重试'
      }));
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);

      // 验证验证码
      const isValid = await validateCaptcha(form.phone, form.captcha);
      if (!isValid) {
        setIsLoading(false);
        return;
      }

      // 调用修改密码API
      const result = await auth.resetPassword(form.phone, form.captcha, form.newPassword);

      if (result.success) {
        // 修改成功
        if (onResetSuccess) {
          onResetSuccess();
        }

        // 清空表单
        setForm({
          phone: '',
          captcha: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        // 修改失败
        setErrors({
          ...errors,
          submit: result.message || '修改密码失败，请稍后重试'
        });
      }
    } catch (error) {
      setErrors({
        ...errors,
        submit: error instanceof Error ? error.message : '修改密码失败，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form className={`reset-form ${className}`} onSubmit={handleSubmit}>
      <div className="form-group">
        <label>手机号</label>
        <div className="input-group">
          <input
            type="tel"
            name="phone"
            value={form.phone}
            onChange={handleChange}
            placeholder="请输入注册手机号"
            className={errors.phone ? 'error' : ''}
          />
          <CaptchaHandler
            phone={form.phone}
            type="reset"
            onCaptchaRequest={handleCaptchaRequest}
            countdown={captchaState.countdown}
            isLoading={captchaState.loading}
          />
        </div>
        {errors.phone && (
          <span className="error-message">{errors.phone}</span>
        )}
      </div>

      <div className="form-group">
        <label>验证码</label>
        <input
          type="text"
          name="captcha"
          placeholder="请输入验证码"
          value={form.captcha}
          onChange={handleChange}
          className={errors.captcha ? 'error' : ''}
          maxLength={6}
        />
        {errors.captcha && (
          <span className="error-message">{errors.captcha}</span>
        )}
      </div>

      <div className="form-group">
        <label>新密码</label>
        <div className="password-input">
          <input
            type={newPasswordVisible ? 'text' : 'password'}
            name="newPassword"
            placeholder="请输入新密码"
            value={form.newPassword}
            onChange={handleChange}
            className={errors.newPassword ? 'error' : ''}
          />
          <button
            type="button"
            className={`toggle-password ${newPasswordVisible ? 'visible' : ''}`}
            onClick={() => setNewPasswordVisible(prev => !prev)}
          />
        </div>
        {errors.newPassword && (
          <span className="error-message">{errors.newPassword}</span>
        )}

        {/* 密码强度显示 */}
        {form.newPassword && (
          <PasswordStrength strength={errors.passwordStrength} />
        )}
      </div>

      <div className="form-group">
        <label>确认密码</label>
        <div className="password-input">
          <input
            type={confirmPasswordVisible ? 'text' : 'password'}
            name="confirmPassword"
            placeholder="请再次输入新密码"
            value={form.confirmPassword}
            onChange={handleChange}
            className={errors.confirmPassword ? 'error' : ''}
          />
          <button
            type="button"
            className={`toggle-password ${confirmPasswordVisible ? 'visible' : ''}`}
            onClick={() => setConfirmPasswordVisible(prev => !prev)}
          />
        </div>
        {errors.confirmPassword && (
          <span className="error-message">{errors.confirmPassword}</span>
        )}
      </div>

      {errors.submit && (
        <span className="error-message">{errors.submit}</span>
      )}

      <button
        className="submit-btn"
        type="submit"
        disabled={isLoading}
      >
        <span>{isLoading ? '修改中...' : '修改密码'}</span>
      </button>
    </form>
  );
};

export default ResetPasswordForm;
