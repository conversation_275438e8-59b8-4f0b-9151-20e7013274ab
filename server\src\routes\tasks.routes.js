const express = require('express');
const router = express.Router();
const FlowTask = require('../models/FlowTask');
const { auth } = require('../modules/auth');
const CreditService = require('../services/credits/creditService');
const { CreditTransaction, CreditBalance } = require('../modules/admin/credits/credit.model');
const { createError } = require('../utils/error');
const { formatTokenToMD5 } = require('../utils/tokenUtils');

// 获取任务列表
router.get('/', auth, async (req, res) => {
    const userId = req.user._id;
    const { pageType, startDate, endDate, taskId } = req.query;

    // 构建查询条件
    let query = { userId, pageType };

    // 添加日期筛选
    if (startDate && endDate) {
        query.createdAt = {
            $gte: new Date(startDate + 'T00:00:00.000Z'),
            $lte: new Date(endDate + 'T23:59:59.999Z')
        };
    }

    // 添加任务ID搜索
    if (taskId) {
        query.taskId = { $regex: taskId, $options: 'i' };
    }

    const tasks = await FlowTask.find(query).sort({ createdAt: -1 });
    
    // 时间格式化
    tasks.forEach(task => {
        task.createdAt = task.createdAt.toLocaleString();
    }); 
    
    res.json(tasks);
});

// 检查用户使用流程余额
router.get('/check-balance', auth, async (req, res) => {
    const userId = req.user._id;
    const deviceToken = formatTokenToMD5(req.token);
    const { featureName, subType,count } = req.query;
    try {
        const result = await CreditService.checkUserBalance(userId, featureName, subType, deviceToken,count);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            canExecute: false,
            code: 500,
            message: error.message,
            type: 'system_error'
        });
    }
});


// 获取任务详情
router.get('/tid/:tid', auth, async (req, res) => {
    const userId = req.user._id;
    const task = await FlowTask.findOne({ taskId: req.params.tid, userId });
    if (!task) {
        return res.status(404).json({ message: '任务不存在' });
    }
    // 时间格式化
    task.createdAt = task.createdAt.toLocaleString();
    res.json(task);
});

// 获取任务详情
router.get('/:id', auth, async (req, res) => {
    const userId = req.user._id;
    const task = await FlowTask.findOne({ taskId: req.params.id, userId });
    if (!task) {
        return res.status(404).json({ message: '任务不存在' });
    }
    // 时间格式化
    task.createdAt = task.createdAt.toLocaleString();
    res.json(task);
});

// 创建任务
router.post('/', auth, async (req, res) => {
    const userId = req.user._id;
    const deviceToken = formatTokenToMD5(req.token);
    const task = await FlowTask.create({ ...req.body, userId ,deviceToken});
    res.json(task);
});

// 更新任务
router.put('/:id', auth, async (req, res) => {
    const userId = req.user._id;
    const taskId = req.params.id;
    
    try {
        // 获取原任务状态
        const oldTask = await FlowTask.findOne({ taskId, userId });
        if (!oldTask) {
            return res.status(404).json({ message: '任务不存在' });
        }

        // 更新任务
        const task = await FlowTask.findOneAndUpdate(
            { taskId, userId }, 
            req.body, 
            { new: true }
        );

        // 如果任务状态从非失败变为失败，返还算力
        if (oldTask.status !== 'failed' && task.status === 'failed') {
            try {
                await CreditService.refundCredits(userId, taskId);
            } catch (error) {
                console.error('返还算力失败:', error);
                // 不影响主流程，继续返回更新后的任务
            }
        }

        res.json(task);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 删除任务
router.delete('/:id', auth, async (req, res) => {
    const userId = req.user._id;
    const task = await FlowTask.findOneAndDelete({ taskId: req.params.id, userId });
    res.json(task);
}); 

module.exports = router;