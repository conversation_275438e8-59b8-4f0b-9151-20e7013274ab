.site-footer {
  width: 100%;
  padding: 12px 0;
  background-color: var(--bg-primary);
  color: var(--text-tertiary);
  font-size: 13px;
  margin-top: auto;
  border-top: 1px solid var(--border-color);
}

.footer-content {
  margin-left: 158px;
  width: calc(100% - 160px);
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

/* 当侧边栏折叠时 */
.sidebar-collapsed .footer-content {
  margin-left: 58px;
  width: calc(100% - 60px);
}

/* 移动端适配 */
@media (max-width: 888px) {
  .footer-content {
    margin-left: 0;
    width: 100%;
  }
}

.copyright-row {
  margin-bottom: 4px;
  color: var(--text-tertiary);
  opacity: 0.8;
  display: flex;
  justify-content: center;
  width: 100%;
}

.beian-row {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  color: var(--text-tertiary);
  opacity: 0.8;
  width: 100%;
}

.separator {
  color: var(--text-tertiary);
  opacity: 0.8;
}

/* 链接样式 */
.beian-row a {
  color: var(--text-tertiary);
  text-decoration: none;
  cursor: pointer;
  opacity: 0.8;
}

/* 移除悬浮效果 */
.beian-row a:hover {
  color: var(--text-tertiary);
}

/* 法律链接样式 */
.legal-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  color: var(--text-tertiary);
  opacity: 0.8;
  width: 100%;
  margin-top: 4px;
  font-size: 13px;
}

.legal-links a {
  color: var(--text-tertiary);
  text-decoration: none;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.legal-links a:hover {
  opacity: 1;
} 