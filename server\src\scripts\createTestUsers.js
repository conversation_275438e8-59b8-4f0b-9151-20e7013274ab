const mongoose = require('mongoose');
const User = require('../modules/auth/user.model');
require('dotenv').config();

async function createTestUsers() {
  try {
    console.log('开始创建测试用户...');
    console.log('连接到数据库:', process.env.MONGODB_URI);
    
    // 连接数据库
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('数据库连接成功');

    // 创建测试用户1
    const testUser1 = new User({
      username: 'user1',
      phone: '13811111111',
      password: 'User1@123',
      name: '测试用户1',
      role: 'user',
      status: 'active'
    });

    // 创建测试用户2
    const testUser2 = new User({
      username: 'user2',
      phone: '13822222222',
      password: 'User2@123',
      name: '测试用户2',
      role: 'user',
      status: 'active'
    });

    // 检查用户1是否已存在
    const existingUser1 = await User.findOne({ username: 'user1' });
    if (existingUser1) {
      console.log('测试用户1已存在，跳过创建');
    } else {
      // 保存用户1到数据库
      await testUser1.save();
      console.log('测试用户1创建成功:');
      console.log('用户名: user1');
      console.log('密码: User1@123');
      console.log('角色: user');
    }

    // 检查用户2是否已存在
    const existingUser2 = await User.findOne({ username: 'user2' });
    if (existingUser2) {
      console.log('测试用户2已存在，跳过创建');
    } else {
      // 保存用户2到数据库
      await testUser2.save();
      console.log('测试用户2创建成功:');
      console.log('用户名: user2');
      console.log('密码: User2@123');
      console.log('角色: user');
    }
    
    console.log('测试用户创建完成');
    await mongoose.connection.close();
    process.exit(0);
  } catch (error) {
    console.error('创建测试用户失败:', error);
    await mongoose.connection.close();
    process.exit(1);
  }
}

createTestUsers(); 