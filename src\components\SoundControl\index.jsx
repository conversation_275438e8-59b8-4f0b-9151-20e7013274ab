import React, { useState, useEffect } from 'react';
import { useTaskContext } from '../../contexts/TaskContext';
import { MdOutlineVolumeOff, MdOutlineVolumeUp } from 'react-icons/md';
import './index.css';
import audioService from '../../services/AudioService';

// 修改组件，使其适合在侧边栏使用
const SoundControl = ({ sidebarStyle = false }) => {
  const { toggleMute, isSoundMuted } = useTaskContext();
  const [muted, setMuted] = useState(true);
  
  useEffect(() => {
    // 初始化时同步静音状态
    setMuted(isSoundMuted());
  }, [isSoundMuted]);
  
  const handleToggle = () => {
    const newMutedState = toggleMute();
    setMuted(newMutedState);
    
    // 如果从静音状态切换到非静音状态，播放提示音作为示例
    if (!newMutedState) {
      // 短暂延迟确保状态已更新
      setTimeout(() => {
        // 先播放成功提示音
        audioService.playTaskComplete();
        // 1秒后播放错误提示音，让用户了解两种提示音的区别
        setTimeout(() => {
          audioService.playTaskError();
        }, 1000);
      }, 100);
    }
  };
  
  // 如果是侧边栏样式，使用不同的渲染方式
  if (sidebarStyle) {
    return (
      <button 
        className={`sidebar-bottom-btn sound-toggle-sidebar ${muted ? 'muted' : 'unmuted'}`}
        onClick={handleToggle}
        title={muted ? '打开提示音' : '关闭提示音'}
      >
        {muted ? <MdOutlineVolumeOff /> : <MdOutlineVolumeUp />}
        <span>任务提示音</span>
      </button>
    );
  }
  
  // 默认样式（固定在右下角）
  return (
    <div className="sound-control">
      <button 
        className={`sound-toggle-btn ${muted ? 'muted' : 'unmuted'}`}
        onClick={handleToggle}
        title={muted ? '打开提示音' : '关闭提示音'}
      >
        {muted ? <MdOutlineVolumeOff /> : <MdOutlineVolumeUp />}
        <span>任务提示音</span>
      </button>
    </div>
  );
};

export default SoundControl; 