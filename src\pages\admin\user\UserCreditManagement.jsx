import React, { useState, useEffect, useRef } from 'react';
import { Card, Table, Button, Modal, Form, Input, InputNumber, DatePicker, message, Tabs, Statistic, Row, Col, Select, Space, Radio, Tooltip, Divider } from 'antd';
import { PlusOutlined, ReloadOutlined, WalletOutlined, DownloadOutlined, MinusOutlined, ExportOutlined, FilterOutlined } from '@ant-design/icons';
import api from '../../../api';
import moment from 'moment';
import * as XLSX from 'xlsx';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const UserCreditManagement = () => {
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [stats, setStats] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [form] = Form.useForm();
  const [exportForm] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [transactionPagination, setTransactionPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [activeTab, setActiveTab] = useState('users');
  const [userSearchValue, setUserSearchValue] = useState('');
  const [userSearchLoading, setUserSearchLoading] = useState(false);
  const [userSearchResults, setUserSearchResults] = useState([]);
  const [operationType, setOperationType] = useState('recharge'); // 'recharge' 或 'deduct'
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [filterForm] = Form.useForm();

  // 获取用户列表
  const fetchUsers = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const queryString = `page=${page}&limit=${pageSize}`;
      const response = await api.get(`/admin/user?${queryString}`);
      console.log('获取用户列表响应:', response);
      
      if (!response || !response.data) {
        console.error('用户列表响应格式错误:', response);
        message.error('获取用户列表失败: 响应格式错误');
        setLoading(false);
        return;
      }
      
      // 处理用户数据，使用creditBalance字段
      const processedUsers = response.data.map(user => {
        console.log('处理用户数据:', user);
        return {
          _id: user._id,
          username: user.username,
          name: user.name,
          phone: user.phone,
          role: user.role,
          status: user.status,
          remark: user.remark,
          // 使用creditBalance字段，如果不存在则回退到credits字段
          creditBalance: user.creditBalance?.balance || user.credits || 0,
          totalRecharged: user.creditBalance?.totalRecharged || 0,
          totalConsumed: user.creditBalance?.totalConsumed || 0,
          lastRechargeDate: user.creditBalance?.lastRechargeDate || null
        };
      });
      
      console.log('处理后的用户数据:', processedUsers);
      setUsers(processedUsers);
      setPagination({
        current: page,
        pageSize,
        total: response.total || 0
      });
    } catch (error) {
      console.error('获取用户列表错误:', error);
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取算力统计数据
  const fetchStats = async () => {
    try {
      // 使用专门的算力统计API
      const response = await api.get(`/admin/credits/stats`);
      console.log('获取算力统计数据响应:', response);
      
      if (response) {
        setStats({
          totalUsers: response.totalUsers || 0,
          usersWithCredit: response.usersWithCredit || 0,
          totalBalance: response.totalBalance || 0,
          totalRecharged: response.totalRecharged || 0,
          totalConsumed: response.totalConsumed || 0,
          todayRecharged: response.todayRecharged || 0,
          todayConsumed: response.todayConsumed || 0
        });
      }
    } catch (error) {
      message.error('获取算力统计数据失败');
      console.error(error);
    }
  };

  // 获取用户算力交易记录
  const fetchTransactions = async (userId, page = 1, pageSize = 10, filters = {}) => {
    setLoading(true);
    try {
      // 构建查询参数
      let queryParams = `page=${page}&limit=${pageSize}`;
      
      // 添加类型过滤
      if (filters.type) {
        queryParams += `&type=${filters.type}`;
      }
      
      // 添加日期范围过滤
      if (filters.dateRange && filters.dateRange.length === 2) {
        const startDate = filters.dateRange[0].format('YYYY-MM-DD');
        const endDate = filters.dateRange[1].format('YYYY-MM-DD');
        queryParams += `&startDate=${startDate}&endDate=${endDate}`;
      }
      
      // 使用专门的算力交易记录API
      const response = await api.get(`/admin/user/${userId}/credits/transactions?${queryParams}`);
      console.log('获取用户算力交易记录响应:', response);
      
      if (response && response.data) {
        setTransactions(response.data);
        setTransactionPagination({
          current: response.page || 1,
          pageSize: response.limit || 10,
          total: response.total || 0
        });
      } else {
        setTransactions([]);
        setTransactionPagination({
          current: 1,
          pageSize: 10,
          total: 0
        });
      }
    } catch (error) {
      message.error('获取算力交易记录失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 搜索用户
  const handleUserSearch = async (value) => {
    if (!value || value.length < 2) {
      setUserSearchResults([]);
      return;
    }
    
    setUserSearchLoading(true);
    try {
      // 使用encodeURIComponent确保查询参数正确编码
      const response = await api.get(`/admin/user/search?query=${encodeURIComponent(value)}`);
      setUserSearchResults(response.users || []);
      
      // 如果没有找到用户，尝试通过手机号查询
      if (response.users.length === 0 && /^\d+$/.test(value)) {
        const phoneResponse = await api.get(`/admin/user/search?phone=${encodeURIComponent(value)}`);
        setUserSearchResults(phoneResponse.users || []);
      }
    } catch (error) {
      console.error('搜索用户失败:', error);
      message.error('搜索用户失败');
    } finally {
      setUserSearchLoading(false);
    }
  };

  // 处理充值/扣除算力
  const handleCreditOperation = async () => {
    try {
      const values = await form.validateFields();
      
      const data = {
        userId: selectedUser._id,
        amount: values.amount,
        description: values.description || (operationType === 'recharge' ? '管理员充值' : '管理员扣除')
      };
      
      console.log(`发送${operationType === 'recharge' ? '充值' : '扣除'}请求:`, data);
      
      // 根据操作类型选择不同的API路径
      const endpoint = operationType === 'recharge' 
        ? `/admin/user/${selectedUser._id}/credits/recharge` 
        : `/admin/user/${selectedUser._id}/credits/deduct`;
      
      const response = await api.post(endpoint, data);
      console.log(`${operationType === 'recharge' ? '充值' : '扣除'}响应:`, response);
      
      message.success(`${operationType === 'recharge' ? '充值' : '扣除'}算力成功`);
      
      setModalVisible(false);
      form.resetFields();
      
      // 刷新数据
      fetchUsers(pagination.current, pagination.pageSize);
      fetchStats();
      if (activeTab === 'transactions' && selectedUser) {
        fetchTransactions(selectedUser._id);
      }
    } catch (error) {
      console.error(`${operationType === 'recharge' ? '充值' : '扣除'}算力错误:`, error);
      message.error(`${operationType === 'recharge' ? '充值' : '扣除'}算力失败: ${error.message || '未知错误'}`);
    }
  };

  // 处理表格分页、排序、筛选变化
  const handleTableChange = (newPagination, filters, sorter) => {
    fetchUsers(newPagination.current, newPagination.pageSize);
  };

  // 处理交易记录表格分页、排序、筛选变化
  const handleTransactionTableChange = (newPagination, filters, sorter) => {
    if (selectedUser) {
      fetchTransactions(selectedUser._id, newPagination.current, newPagination.pageSize);
    }
  };

  // 查看用户算力交易记录
  const viewUserTransactions = (user) => {
    setSelectedUser(user);
    setActiveTab('transactions');
    fetchTransactions(user._id);
  };

  // 导出用户算力交易记录
  const handleExportUserTransactions = async () => {
    try {
      setExportLoading(true);
      const values = await exportForm.validateFields();
      
      // 构建查询参数
      let queryParams = '';
      
      // 添加类型过滤
      if (values.type) {
        queryParams += `&type=${values.type}`;
      }
      
      // 添加日期范围过滤
      if (values.dateRange && values.dateRange.length === 2) {
        const startDate = values.dateRange[0].format('YYYY-MM-DD');
        const endDate = values.dateRange[1].format('YYYY-MM-DD');
        queryParams += `&startDate=${startDate}&endDate=${endDate}`;
      }
      
      // 根据导出类型选择不同的API路径
      let response;
      if (values.exportType === 'user' && selectedUser) {
        // 导出当前用户的算力交易记录
        response = await api.get(`/admin/user/${selectedUser._id}/credits/transactions?limit=1000${queryParams}`);
      } else {
        // 导出所有用户的算力交易记录
        response = await api.get(`/admin/credits/transactions?limit=1000${queryParams}`);
      }
      
      if (!response || !response.data || response.data.length === 0) {
        message.info('没有符合条件的数据可导出');
        setExportLoading(false);
        setExportModalVisible(false);
        return;
      }
      
      // 处理导出数据
      const exportData = response.data.map(record => {
        // 格式化日期
        const createdAt = moment(record.createdAt).format('YYYY-MM-DD HH:mm:ss');
        
        // 格式化交易类型
        const typeMap = {
          'recharge': '充值',
          'consume': '消费',
          'refund': '退款',
          'bonus': '奖励',
          'deduct': '扣除'
        };
        
        // 构建导出行
        return {
          '用户ID': record.user,
          '用户名': record.username || '',
          '交易时间': createdAt,
          '交易类型': typeMap[record.type] || record.type,
          '金额': record.amount,
          '余额': record.balance,
          '说明': record.description || '',
          '操作人': record.metadata?.adminId ? '管理员' : '系统'
        };
      });
      
      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);
      
      // 设置列宽
      const colWidths = [
        { wch: 24 }, // 用户ID
        { wch: 15 }, // 用户名
        { wch: 20 }, // 交易时间
        { wch: 10 }, // 交易类型
        { wch: 10 }, // 金额
        { wch: 10 }, // 余额
        { wch: 30 }, // 说明
        { wch: 10 }  // 操作人
      ];
      ws['!cols'] = colWidths;
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '算力交易记录');
      
      // 生成文件名
      const fileName = values.exportType === 'user' && selectedUser
        ? `${selectedUser.username}_算力交易记录_${moment().format('YYYYMMDD')}.xlsx`
        : `全部用户_算力交易记录_${moment().format('YYYYMMDD')}.xlsx`;
      
      // 导出Excel文件
      XLSX.writeFile(wb, fileName);
      
      message.success('导出成功');
      setExportModalVisible(false);
    } catch (error) {
      console.error('导出算力交易记录错误:', error);
      message.error('导出失败: ' + (error.message || '未知错误'));
    } finally {
      setExportLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchUsers();
    fetchStats();
  }, []);

  // 用户列表列定义
  const userColumns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      sorter: (a, b) => (a.username || '').localeCompare(b.username || ''),
      // 可选：支持筛选
      // filters: [...new Set(users.map(u => u.username))].map(name => ({ text: name, value: name })),
      // onFilter: (value, record) => record.username === value,
      render: (_, record) => (
        <span>
          {record.username || '未知用户'}
          {record.remark && `（${record.remark}）`}
        </span>
      )
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      sorter: (a, b) => (a.phone || '').localeCompare(b.phone || ''),
    },
    {
      title: '算力余额',
      dataIndex: 'creditBalance',
      key: 'creditBalance',
      sorter: (a, b) => a.creditBalance - b.creditBalance,
      render: (text) => text || 0,
    },
    {
      title: '总充值算力',
      dataIndex: 'totalRecharged',
      key: 'totalRecharged',
      sorter: (a, b) => a.totalRecharged - b.totalRecharged,
      render: (text) => text || 0
    },
    {
      title: '总消费算力',
      dataIndex: 'totalConsumed',
      key: 'totalConsumed',
      sorter: (a, b) => a.totalConsumed - b.totalConsumed,
      render: (text) => text || 0
    },
    {
      title: '最后充值时间',
      dataIndex: 'lastRechargeDate',
      key: 'lastRechargeDate',
      sorter: (a, b) => new Date(a.lastRechargeDate || 0).getTime() - new Date(b.lastRechargeDate || 0).getTime(),
      render: (text) => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => {
              setSelectedUser(record);
              setOperationType('recharge');
              setModalVisible(true);
            }}
          >
            充值
          </Button>
          <Button 
            type="danger" 
            icon={<MinusOutlined />}
            onClick={() => {
              setSelectedUser(record);
              setOperationType('deduct');
              setModalVisible(true);
            }}
            disabled={!record.creditBalance || record.creditBalance <= 0}
          >
            扣除
          </Button>
          <Button 
            type="default" 
            icon={<WalletOutlined />}
            onClick={() => viewUserTransactions(record)}
          >
            明细
          </Button>
        </Space>
      )
    }
  ];

  // 交易记录列定义
  const transactionColumns = [
    {
      title: '时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (text) => {
        const typeMap = {
          'recharge': '充值',
          'consume': '消费',
          'refund': '退款',
          'bonus': '奖励',
          'deduct': '扣除'
        };
        return typeMap[text] || text;
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (text) => {
        const value = Number(text);
        return (
          <span style={{ color: value >= 0 ? 'green' : 'red' }}>
            {value >= 0 ? `+${value}` : value}
          </span>
        );
      }
    },
    {
      title: '余额',
      dataIndex: 'balance',
      key: 'balance'
    },
    {
      title: '说明',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '操作人',
      dataIndex: 'metadata',
      key: 'adminId',
      render: (metadata) => metadata?.adminId ? '管理员' : '系统'
    }
  ];

  return (
    <div className="user-credit-management" style={{ display: 'flex', flexDirection: 'column', height: '100%', minHeight: 0 }}>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="用户算力管理" key="users">
          <Card 
            title="用户算力管理" 
            extra={
              <Space>
                <Button 
                  icon={<ExportOutlined />}
                  onClick={() => {
                    exportForm.setFieldsValue({
                      exportType: 'all',
                    });
                    setExportModalVisible(true);
                  }}
                >
                  导出记录
                </Button>
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />} 
                  onClick={() => {
                    fetchUsers(pagination.current, pagination.pageSize);
                    fetchStats();
                  }}
                >
                  刷新
                </Button>
              </Space>
            }
            style={{ display: 'flex', flexDirection: 'column', height: '100%', minHeight: 0 }}
          >
            {stats && (
              <div className="stats-container" style={{ marginBottom: 20 }}>
                <Row gutter={16}>
                  <Col span={6}>
                    <Statistic title="总用户数" value={stats.totalUsers} />
                  </Col>
                  <Col span={6}>
                    <Statistic title="有算力用户数" value={stats.usersWithCredit} />
                  </Col>
                  <Col span={6}>
                    <Statistic title="总算力余额" value={stats.totalBalance} />
                  </Col>
                  <Col span={6}>
                    <Statistic title="总充值算力" value={stats.totalRecharged} />
                  </Col>
                </Row>
                <Row gutter={16} style={{ marginTop: 16 }}>
                  <Col span={6}>
                    <Statistic title="总消费算力" value={stats.totalConsumed} />
                  </Col>
                  <Col span={6}>
                    <Statistic title="今日充值算力" value={stats.todayRecharged} />
                  </Col>
                  <Col span={6}>
                    <Statistic title="今日消费算力" value={stats.todayConsumed} />
                  </Col>
                </Row>
              </div>
            )}
            <div style={{ flex: 1, minHeight: 0, display: 'flex', flexDirection: 'column' }}>
              <Table 
                columns={userColumns} 
                dataSource={users} 
                rowKey="_id"
                loading={loading}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`
                }}
                onChange={handleTableChange}
                scroll={{ y: '100%' }}
                style={{ flex: 1 }}
              />
            </div>
          </Card>
        </TabPane>
        
        <TabPane tab="算力交易记录" key="transactions">
          <Card 
            title={`算力交易记录 ${selectedUser ? `- ${selectedUser.username}` : ''}`}
            extra={
              <Space>
                <Button 
                  icon={<FilterOutlined />}
                  onClick={() => setFilterModalVisible(true)}
                >
                  筛选
                </Button>
                <Button 
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    exportForm.setFieldsValue({
                      exportType: 'user',
                      
                    });
                    setExportModalVisible(true);
                  }}
                >
                  导出
                </Button>
                <Button 
                  onClick={() => {
                    setActiveTab('users');
                    setSelectedUser(null);
                  }}
                >
                  返回用户列表
                </Button>
              </Space>
            }
          >
            {selectedUser && (
              <Table 
                columns={transactionColumns} 
                dataSource={transactions} 
                rowKey="_id"
                loading={loading}
                pagination={transactionPagination}
                onChange={handleTransactionTableChange}
                scroll={{ y: 600 }}
              />
            )}
          </Card>
        </TabPane>
      </Tabs>
      
      {/* 充值/扣除算力模态框 */}
      <Modal
        title={`${operationType === 'recharge' ? '给用户充值算力' : '扣除用户算力'} ${selectedUser?.username || ''}`}
        open={modalVisible}
        onOk={handleCreditOperation}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        maskClosable={false}
        destroyOnClose={true}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="amount"
            label={operationType === 'recharge' ? '充值金额' : '扣除金额'}
            rules={[
              { required: true, message: `请输入${operationType === 'recharge' ? '充值' : '扣除'}金额` },
              { type: 'number', min: 1, message: `${operationType === 'recharge' ? '充值' : '扣除'}金额必须大于0` }
            ]}
          >
            <InputNumber style={{ width: '100%' }} placeholder={`请输入${operationType === 'recharge' ? '充值' : '扣除'}金额`} />
          </Form.Item>
          
          <Form.Item
            name="description"
            label={operationType === 'recharge' ? '充值说明' : '扣除说明'}
          >
            <TextArea rows={3} placeholder={`请输入${operationType === 'recharge' ? '充值' : '扣除'}说明`} />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 导出算力记录模态框 */}
      <Modal
        title="导出算力记录"
        open={exportModalVisible}
        onOk={handleExportUserTransactions}
        onCancel={() => {
          setExportModalVisible(false);
          exportForm.resetFields();
        }}
        confirmLoading={exportLoading}
        maskClosable={false}
        destroyOnClose={true}
      >
        <Form
          form={exportForm}
          layout="vertical"
        >
          <Form.Item
            name="exportType"
            label="导出范围"
            initialValue={selectedUser ? 'user' : 'all'}
          >
            <Radio.Group>
              {selectedUser && (
                <Radio value="user">当前用户 ({selectedUser.username})</Radio>
              )}
              <Radio value="all">所有用户</Radio>
            </Radio.Group>
          </Form.Item>
          
          <Form.Item
            name="dateRange"
            label="日期范围"
            rules={[{ required: true, message: '请选择日期范围' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="type"
            label="交易类型"
          >
            <Select allowClear placeholder="选择交易类型">
              <Option value="recharge">充值</Option>
              <Option value="consume">消费</Option>
              <Option value="refund">退款</Option>
              <Option value="bonus">奖励</Option>
              <Option value="deduct">扣除</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 筛选交易记录模态框 */}
      <Modal
        title="筛选交易记录"
        open={filterModalVisible}
        onOk={() => {
          const values = filterForm.getFieldsValue();
          if (selectedUser) {
            fetchTransactions(selectedUser._id, 1, transactionPagination.pageSize, values);
          }
          setFilterModalVisible(false);
        }}
        onCancel={() => setFilterModalVisible(false)}
        maskClosable={false}
        destroyOnClose={true}
      >
        <Form
          form={filterForm}
          layout="vertical"
        >
          <Form.Item
            name="dateRange"
            label="日期范围"
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="type"
            label="交易类型"
          >
            <Select allowClear placeholder="选择交易类型">
              <Option value="recharge">充值</Option>
              <Option value="consume">消费</Option>
              <Option value="refund">退款</Option>
              <Option value="bonus">奖励</Option>
              <Option value="deduct">扣除</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserCreditManagement; 