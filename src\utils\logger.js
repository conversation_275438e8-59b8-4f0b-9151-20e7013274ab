/**
 * 日志工具模块
 * 
 * 提供统一的日志记录接口，支持不同级别的日志，
 * 并根据环境配置自动调整日志行为
 */

// 获取当前环境
const ENV = process.env.NODE_ENV || 'development';

// 日志级别定义
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4
};

// 根据环境设置默认日志级别
const DEFAULT_LEVEL = ENV === 'production' ? LOG_LEVELS.ERROR : LOG_LEVELS.DEBUG;

// 当前日志级别
let currentLevel = DEFAULT_LEVEL;

// 自定义日志处理函数
let customLogHandler = null;

/**
 * 格式化日志消息
 * @param {string} level 日志级别
 * @param {string} message 日志消息
 * @param {any} data 附加数据
 * @returns {string} 格式化后的日志消息
 */
const formatLogMessage = (level, message, data) => {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [${level}]`;
  
  if (data !== undefined) {
    return `${prefix} ${message} ${JSON.stringify(data, null, 2)}`;
  }
  
  return `${prefix} ${message}`;
};

/**
 * 处理日志输出
 * @param {string} level 日志级别
 * @param {string} message 日志消息
 * @param {any} data 附加数据
 */
const handleLog = (level, levelName, message, data) => {
  // 检查日志级别
  if (level < currentLevel) {
    return;
  }

  const formattedMessage = formatLogMessage(levelName, message, data);
  
  // 如果有自定义处理函数，使用它
  if (customLogHandler) {
    customLogHandler(levelName, message, data, formattedMessage);
    return;
  }

  // 默认日志输出
  switch (levelName) {
    case 'DEBUG':
      console.log(formattedMessage);
      break;
    case 'INFO':
      console.info(formattedMessage);
      break;
    case 'WARN':
      console.warn(formattedMessage);
      break;
    case 'ERROR':
      console.error(formattedMessage);
      break;
  }
};

/**
 * 日志工具对象
 */
const logger = {
  /**
   * 设置日志级别
   * @param {string} level 日志级别名称
   */
  setLevel: (level) => {
    if (LOG_LEVELS[level] !== undefined) {
      currentLevel = LOG_LEVELS[level];
    }
  },

  /**
   * 设置自定义日志处理函数
   * @param {Function} handler 处理函数
   */
  setCustomHandler: (handler) => {
    if (typeof handler === 'function') {
      customLogHandler = handler;
    }
  },

  /**
   * 记录调试级别日志
   * @param {string} message 日志消息
   * @param {any} data 附加数据
   */
  debug: (message, data) => {
    handleLog(LOG_LEVELS.DEBUG, 'DEBUG', message, data);
  },

  /**
   * 记录信息级别日志
   * @param {string} message 日志消息
   * @param {any} data 附加数据
   */
  info: (message, data) => {
    handleLog(LOG_LEVELS.INFO, 'INFO', message, data);
  },

  /**
   * 记录警告级别日志
   * @param {string} message 日志消息
   * @param {any} data 附加数据
   */
  warn: (message, data) => {
    handleLog(LOG_LEVELS.WARN, 'WARN', message, data);
  },

  /**
   * 记录错误级别日志
   * @param {string} message 日志消息
   * @param {any} data 附加数据
   */
  error: (message, data) => {
    handleLog(LOG_LEVELS.ERROR, 'ERROR', message, data);
  }
};

export default logger; 