import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, Select, message, Card, Space, Row, Col, Tag, Tooltip, Statistic, Progress, Drawer, Descriptions, Badge, Empty, Switch } from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  ReloadOutlined, 
  PlayCircleOutlined,
  PauseCircleOutlined,
  SyncOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  CloudServerOutlined,
  PoweroffOutlined
} from '@ant-design/icons';
import moment from 'moment';
import api from '../../../api';
import './InstanceManagement.css';

const { Option } = Select;
const { confirm } = Modal;

// 状态码映射
const STATUS_MAP = {
  100: { text: '部署中', color: 'processing', icon: <SyncOutlined spin /> },
  200: { text: '开机中', color: 'processing', icon: <SyncOutlined spin /> },
  300: { text: '运行中', color: 'success', icon: <CheckCircleOutlined /> },
  400: { text: '关机中', color: 'warning', icon: <SyncOutlined spin /> },
  500: { text: '重置中', color: 'warning', icon: <SyncOutlined spin /> },
  600: { text: '更换镜像中', color: 'warning', icon: <SyncOutlined spin /> },
  700: { text: '释放资源中', color: 'error', icon: <SyncOutlined spin /> },
  800: { text: '已关机', color: 'default', icon: <PauseCircleOutlined /> }
};

const InstanceManagement = () => {
  const [instances, setInstances] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingInstance, setEditingInstance] = useState(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [stats, setStats] = useState(null);
  const [queryParams, setQueryParams] = useState({
    page: 1,
    limit: 10
  });
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentInstance, setCurrentInstance] = useState(null);
  const [balance, setBalance] = useState(null);

  // 获取实例列表
  const fetchInstances = async (params = queryParams) => {
    setLoading(true);
    try {
      const queryString = Object.entries(params)
        .filter(([_, value]) => value !== undefined && value !== '')
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&');
      
      const response = await api.get(`/admin/instances?${queryString}`);
      setInstances(response.data || []);
      setQueryParams(params);
    } catch (error) {
      message.error('获取实例列表失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const response = await api.get('/admin/instances/stats');
      setStats(response.data);
    } catch (error) {
      message.error('获取统计数据失败');
      console.error(error);
    }
  };

  // 获取算力余额
  const fetchBalance = async () => {
    try {
      const response = await api.get('/admin/instances/balance');
      setBalance(response.data);
    } catch (error) {
      message.error('获取算力余额失败');
      console.error(error);
    }
  };

  useEffect(() => {
    fetchInstances();
    fetchStats();
    fetchBalance();
  }, []);
  
  // 添加自动刷新空闲时长的定时器
  useEffect(() => {
    const interval = setInterval(() => {
      // 只刷新有运行中实例的列表
      if (instances.some(instance => instance.status === 300)) {
        fetchInstances();
      }
    }, 60000); // 每分钟刷新一次

    return () => clearInterval(interval);
  }, [instances]);

  // 处理编辑实例
  const handleEdit = (instance) => {
    setEditingInstance(instance);
    form.setFieldsValue({
      name: instance.name,
      url: instance.url,
      apiKey: instance.apiKey,
      instanceId: instance.instanceId,
      status: instance.status,
      type: instance.type,
      description: instance.description
    });
    setModalVisible(true);
  };

  // 处理删除实例
  const handleDelete = (id) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个实例吗？删除后不可恢复。',
      onOk: async () => {
        try {
          await api.delete(`/admin/instances/${id}`);
          message.success('实例删除成功');
          fetchInstances();
          fetchStats();
        } catch (error) {
          message.error('删除实例失败');
          console.error(error);
        }
      }
    });
  };

  // 处理保存实例
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      if (editingInstance) {
        await api.put(`/admin/instances/${editingInstance._id}`, values);
        message.success('实例更新成功');
      } else {
        await api.post('/admin/instances', values);
        message.success('实例创建成功');
      }
      setModalVisible(false);
      form.resetFields();
      setEditingInstance(null);
      fetchInstances();
      fetchStats();
    } catch (error) {
      message.error('保存实例失败');
      console.error(error);
    }
  };

  // 处理测试连接
  const handleTestConnection = async (instance) => {
    try {
      await api.post(`/admin/instances/${instance._id}/test`);
      message.success('连接测试成功');
    } catch (error) {
      message.error('连接测试失败');
      console.error(error);
    }
  };

  // 添加查看详情方法
  const handleViewDetail = (instance) => {
    setCurrentInstance(instance);
    setDetailVisible(true);
  };

  const columns = [
    {
      title: '实例名称',
      dataIndex: 'customName',
      key: 'customName',
      render: (text, record) => (
        <Space>
          <span>{text}</span>
          {record.errCode !== 0 && (
            <Tooltip title={`错误码: ${record.errCode}`}>
              <Tag color="error">异常</Tag>
            </Tooltip>
          )}
        </Space>
      )
    },
    {
      title: '镜像信息',
      key: 'imageInfo',
      render: (_, record) => (
        <div>
          <div>{record.appImageName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.appImageAuthor} / {record.appImageVersion}
          </div>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusInfo = STATUS_MAP[status] || { text: '未知', color: 'default' };
        return <Tag icon={statusInfo.icon} color={statusInfo.color}>{statusInfo.text}</Tag>;
      }
    },
    {
      title: 'GPU类型',
      dataIndex: 'gpuType',
      key: 'gpuType',
      render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '系统资源',
      key: 'systemResource',
      render: (_, record) => (
        <Tooltip title={`已用: ${(record.systemDiskSizeUsed/1024).toFixed(2)}GB / 总计: ${(record.systemDiskSize/1024).toFixed(2)}GB`}>
          <Progress 
            percent={((record.systemDiskSizeUsed/record.systemDiskSize) * 100).toFixed(1)} 
            size="small"
            status={record.systemDiskSizeUsed/record.systemDiskSize > 0.8 ? "exception" : "normal"}
          />
        </Tooltip>
      )
    },
    {
      title: '计费信息',
      key: 'billing',
      render: (_, record) => (
        <div>
          <div>¥{record.price}/分钟</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.billType === 1 ? '包月' : record.billType === 2 ? '包天' : '按量计费'}
          </div>
        </div>
      )
    },
    {
      title: '运行时间',
      key: 'runtime',
      render: (_, record) => {
        if (record.status === 300) { // 运行中
          return <span>{moment.duration(record.runtime, 'seconds').humanize()}</span>;
        }
        return '-';
      }
    },
    {
      title: 'WebUI地址',
      dataIndex: 'webUIAddress',
      key: 'webUIAddress',
      render: (text) => text ? (
        <a href={`http://${text}`} target="_blank" rel="noopener noreferrer">
          {text}
        </a>
      ) : '-'
    },
    {
      title: '使用情况',
      key: 'usage',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <div>
            <Badge status={record.inUseWorkflows > 0 ? "processing" : "default"} />
            <span style={{ marginLeft: 8 }}>
              {record.inUseWorkflows} 个工作流运行中
            </span>
          </div>
          {record.workflowInfo && (
            <Tooltip title={`开始时间: ${moment(record.workflowInfo.createdAt).format('YYYY-MM-DD HH:mm:ss')}`}>
              <Tag color="blue">
                已运行 {moment(record.workflowInfo.createdAt).fromNow(true)}
              </Tag>
            </Tooltip>
          )}
          {record.idleInfo && record.idleInfo.isIdle && (
            <Tooltip title={`完成时间: ${record.idleInfo.finishedTime}`}>
              <Tag color={record.idleInfo.shouldStop ? "red" : "orange"}>
                已空闲 {record.idleInfo.idleMinutes} 分钟
                {record.idleInfo.shouldStop && " (即将关机)"}
              </Tag>
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: '常开',
      dataIndex: 'isAlwaysOn',
      key: 'isAlwaysOn',
      render: (isAlwaysOn, record) => (
        <Switch
          checked={isAlwaysOn || false}
          onChange={(checked) => handleToggleAlwaysOn(record.appId, checked)}
          loading={loading}
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          {record.idleInfo && record.idleInfo.isIdle && (
            <Button
              type="link"
              size="small"
              onClick={() => handleRefreshIdleInfo(record.appId)}
            >
              刷新空闲时长
            </Button>
          )}
          {record.status === 800 ? (
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              size="small"
              onClick={() => handleStartInstance(record.appId)}
            >
              启动
            </Button>
          ) : record.status === 300 ? (
            <Button
              danger
              icon={<PauseCircleOutlined />}
              size="small"
              onClick={() => handleStopInstance(record.appId)}
            >
              停止
            </Button>
          ) : null}
        </Space>
      )
    }
  ];

  // 启动实例
  const handleStartInstance = async (appId) => {
    try {
      await api.post(`/admin/instances/${appId}/start`);
      message.success('实例启动成功');
      fetchInstances();
    } catch (error) {
      message.error('启动实例失败');
      console.error(error);
    }
  };

  // 停止实例
  const handleStopInstance = async (appId) => {
    try {
      await api.post(`/admin/instances/${appId}/stop`);
      message.success('实例停止成功');
      fetchInstances();
    } catch (error) {
      message.error('停止实例失败');
      console.error(error);
    }
  };

  // 刷新实例空闲时长
  const handleRefreshIdleInfo = async (appId) => {
    try {
      const response = await api.get(`/admin/instances/${appId}/idle-info`);
      message.success('空闲时长信息已更新');
      fetchInstances(); // 重新获取实例列表
    } catch (error) {
      message.error('刷新空闲时长失败');
      console.error(error);
    }
  };

  // 切换常开状态
  const handleToggleAlwaysOn = async (appId, isAlwaysOn) => {
    try {
      await api.post(`/admin/instances/${appId}/always-on`, { isAlwaysOn });
      message.success('常开状态已更新');
      fetchInstances();
    } catch (error) {
      message.error('更新常开状态失败');
      console.error(error);
    }
  };

  // 修改统计卡片渲染方法
  const renderStatsCards = () => {
    const totalInstances = instances.length;
    const runningInstances = instances.filter(i => i.status === 300).length;
    const stoppedInstances = instances.filter(i => i.status === 800).length;
    const errorInstances = instances.filter(i => i.errCode !== 0).length;
    const idleInstances = instances.filter(i => i.idleInfo && i.idleInfo.isIdle).length;
    const shouldStopInstances = instances.filter(i => i.idleInfo && i.idleInfo.shouldStop).length;
    const alwaysOnInstances = instances.filter(i => i.isAlwaysOn).length;

    return (
      <Row gutter={16}>
        <Col span={4}>
          <Card>
            <Statistic 
              title="总实例数" 
              value={totalInstances}
              prefix={<CloudServerOutlined />} 
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic 
              title="运行中" 
              value={runningInstances}
              valueStyle={{ color: '#3f8600' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic 
              title="已停止" 
              value={stoppedInstances}
              valueStyle={{ color: '#cf1322' }}
              prefix={<PauseCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic 
              title="空闲实例" 
              value={idleInstances}
              valueStyle={{ color: '#faad14' }}
              prefix={<SyncOutlined />}
              suffix={shouldStopInstances > 0 ? ` (${shouldStopInstances}个即将关机)` : ''}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card>
            <Statistic 
              title="异常实例" 
              value={errorInstances}
              valueStyle={{ color: '#faad14' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card>
            <Statistic 
              title="常开实例" 
              value={alwaysOnInstances}
              valueStyle={{ color: '#1890ff' }}
              prefix={<PoweroffOutlined />}
            />
          </Card>
        </Col>
        <Col span={10}>
          <Card className="balance-card">
            <Row gutter={16}>
              <Col span={8}>
                <Statistic 
                  title="可用余额" 
                  value={balance?.availableBalance || '0.00'}
                  precision={2}
                  valueStyle={{ color: '#fff' }}
                  prefix="¥"
                />
              </Col>
              <Col span={8}>
                <Statistic 
                  title="代金券余额" 
                  value={balance?.availableVoucherCash || '0.00'}
                  precision={2}
                  valueStyle={{ color: '#fff' }}
                  prefix="¥"
                />
              </Col>
              <Col span={8}>
                <Statistic 
                  title="累计消费" 
                  value={balance?.consumeCashTotal || '0.00'}
                  precision={2}
                  valueStyle={{ color: '#fff' }}
                  prefix="¥"
                  suffix={
                    <Tooltip title="点击刷新">
                      <Button
                        type="link"
                        icon={<ReloadOutlined />}
                        onClick={fetchBalance}
                        style={{ marginLeft: 8 }}
                      />
                    </Tooltip>
                  }
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    );
  };

  // 添加详情抽屉组件
  const renderDetailDrawer = () => {
    if (!currentInstance) return null;

    const customPorts = currentInstance.customPort || [];

    return (
      <Drawer
        title="实例详情"
        placement="right"
        width={600}
        onClose={() => setDetailVisible(false)}
        open={detailVisible}
      >
        <Descriptions bordered column={1}>
          <Descriptions.Item label="实例ID">{currentInstance.appId}</Descriptions.Item>
          <Descriptions.Item label="实例名称">{currentInstance.customName}</Descriptions.Item>
          <Descriptions.Item label="状态">
            <Badge 
              status={STATUS_MAP[currentInstance.status]?.color || 'default'} 
              text={STATUS_MAP[currentInstance.status]?.text || '未知'}
            />
          </Descriptions.Item>
          <Descriptions.Item label="镜像信息">
            <div>{currentInstance.appImageName}</div>
            <div>作者: {currentInstance.appImageAuthor}</div>
            <div>版本: {currentInstance.appImageVersion}</div>
          </Descriptions.Item>
          <Descriptions.Item label="GPU类型">
            {currentInstance.gpuType}
          </Descriptions.Item>
          <Descriptions.Item label="系统资源">
            <div>磁盘总大小: {(currentInstance.systemDiskSize/1024).toFixed(2)} GB</div>
            <div>已使用空间: {(currentInstance.systemDiskSizeUsed/1024).toFixed(2)} GB</div>
            <Progress 
              percent={((currentInstance.systemDiskSizeUsed/currentInstance.systemDiskSize) * 100).toFixed(1)}
              status={currentInstance.systemDiskSizeUsed/currentInstance.systemDiskSize > 0.8 ? "exception" : "normal"}
            />
          </Descriptions.Item>
          <Descriptions.Item label="计费信息">
            <div>计费方式: {
              currentInstance.billType === 1 ? '包月' : 
              currentInstance.billType === 2 ? '包天' : '按量计费'
            }</div>
            <div>单价: ¥{currentInstance.price}/分钟</div>
            <div>预扣金额: ¥{currentInstance.prePrice}</div>
          </Descriptions.Item>
          <Descriptions.Item label="时间信息">
            <div>创建时间: {moment(currentInstance.createdAt * 1000).format('YYYY-MM-DD HH:mm:ss')}</div>
            {currentInstance.startedAt && (
              <div>最后启动时间: {moment(currentInstance.startedAt * 1000).format('YYYY-MM-DD HH:mm:ss')}</div>
            )}
            {currentInstance.stoppedAt && (
              <div>最后停止时间: {moment(currentInstance.stoppedAt * 1000).format('YYYY-MM-DD HH:mm:ss')}</div>
            )}
            {currentInstance.expiredAt && (
              <div>过期时间: {moment(currentInstance.expiredAt * 1000).format('YYYY-MM-DD HH:mm:ss')}</div>
            )}
            {currentInstance.status === 300 && (
              <div>已运行时长: {moment.duration(currentInstance.runtime, 'seconds').humanize()}</div>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="端口配置">
            {customPorts.length > 0 ? (
              customPorts.map((port, index) => (
                <div key={index}>
                  <div>类型: {port.type}</div>
                  <div>本地端口: {port.localPort}</div>
                  <div>访问地址: {port.subDomain}</div>
                </div>
              ))
            ) : (
              <span>暂无端口配置</span>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="WebUI地址">
            {currentInstance.webUIAddress ? (
              <a href={`http://${currentInstance.webUIAddress}`} target="_blank" rel="noopener noreferrer">
                {currentInstance.webUIAddress}
              </a>
            ) : '未配置'}
          </Descriptions.Item>
          {currentInstance.errCode !== 0 && (
            <Descriptions.Item label="错误信息" labelStyle={{ color: '#ff4d4f' }}>
              <div>错误码: {currentInstance.errCode}</div>
            </Descriptions.Item>
          )}
          <Descriptions.Item label="工作流使用情况">
            {currentInstance.inUseWorkflows > 0 ? (
              <>
                <div>
                  <Badge status="processing" />
                  <span style={{ marginLeft: 8 }}>
                    {currentInstance.inUseWorkflows} 个工作流运行中
                  </span>
                </div>
              </>
            ) : (
              <Empty description="当前无运行中的工作流" />
            )}
          </Descriptions.Item>
          {currentInstance.idleInfo && currentInstance.idleInfo.isIdle && (
            <Descriptions.Item label="空闲状态">
              <div>
                <Badge status={currentInstance.idleInfo.shouldStop ? "error" : "warning"} />
                <span style={{ marginLeft: 8 }}>
                  已空闲 {currentInstance.idleInfo.idleMinutes} 分钟
                </span>
              </div>
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                完成时间: {currentInstance.idleInfo.finishedTime}
              </div>
              {currentInstance.idleInfo.shouldStop && (
                <div style={{ fontSize: '12px', color: '#ff4d4f', marginTop: '4px' }}>
                  该实例已超过空闲时间限制，即将自动关机
                </div>
              )}
            </Descriptions.Item>
          )}
          <Descriptions.Item label="常开状态">
            <Switch
              checked={currentInstance.isAlwaysOn || false}
              onChange={(checked) => handleToggleAlwaysOn(currentInstance.appId, checked)}
              loading={loading}
            />
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              {currentInstance.isAlwaysOn ? '常开模式：实例不会被自动关机' : '普通模式：空闲超过10分钟将自动关机'}
            </div>
          </Descriptions.Item>
        </Descriptions>
      </Drawer>
    );
  };

  return (
    <div className="instance-management" style={{ display: 'flex', flexDirection: 'column', height: '100%', minHeight: 0 }}>
      <div className="stats-section">
        {renderStatsCards()}
      </div>

      <Card className="instance-table-card" style={{ display: 'flex', flexDirection: 'column', height: '100%', minHeight: 0 }}>
        <div className="table-header" style={{ flexShrink: 0 }}>
          <Space>
            <h2>实例列表</h2>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchInstances()}
            >
              刷新
            </Button>
          </Space>
        </div>

        <div style={{ flex: 1, minHeight: 0, display: 'flex', flexDirection: 'column' }}>
          <Table
            dataSource={instances}
            columns={columns}
            rowKey="appId"
            loading={loading}
            scroll={{ x: 'max-content', y: '100%' }}
            pagination={{
              total: instances.length,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
            className="instance-table-fixed"
            style={{ flex: 1 }}
          />
        </div>
      </Card>

      <Modal
        title={editingInstance ? '编辑实例' : '添加实例'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="实例名称"
                rules={[{ required: true, message: '请输入实例名称' }]}
              >
                <Input placeholder="请输入实例名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="实例类型"
                rules={[{ required: true, message: '请选择实例类型' }]}
              >
                <Select placeholder="请选择实例类型">
                  <Option value="stable">稳定版</Option>
                  <Option value="beta">测试版</Option>
                  <Option value="custom">自定义</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="url"
                label="实例URL"
                rules={[
                  { required: true, message: '请输入实例URL' },
                  { type: 'url', message: '请输入有效的URL' }
                ]}
              >
                <Input placeholder="请输入实例URL" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="apiKey"
                label="API密钥"
                rules={[{ required: true, message: '请输入API密钥' }]}
              >
                <Input.Password placeholder="请输入API密钥" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="instanceId"
                label="实例ID"
                rules={[{ required: true, message: '请输入实例ID' }]}
              >
                <Input placeholder="请输入实例ID" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={4} placeholder="请输入实例描述" />
          </Form.Item>
        </Form>
      </Modal>
      {renderDetailDrawer()}
    </div>
  );
};

export default InstanceManagement; 