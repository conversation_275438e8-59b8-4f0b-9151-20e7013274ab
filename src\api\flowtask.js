// /flowTask/
import request from './request';

// 获取任务列表
export function getFlowTasks(pageType, filters = {}) {
  const params = { pageType };
  
  if (filters.dateRange && filters.dateRange.length === 2) {
    params.startDate = filters.dateRange[0].format('YYYY-MM-DD');
    params.endDate = filters.dateRange[1].format('YYYY-MM-DD');
  }
  
  if (filters.taskId) {
    params.taskId = filters.taskId;
  }
  
  return request.get('/flowTask', params);
}

// 获取任务详情
export function getFlowTaskDetail(id) {
  return request.get(
    `/flowTask/${id}`,
  );
}
export function getFlowTaskDetailByTid(tid) {
  return request.get(
    `/flowTask/tid/${tid}`,
    {
      _: Date.now(), // 添加时间戳防止缓存
    }
  );
}
// 创建任务
export function createFlowTask(data) {
  return request.post(
    '/flowTask',
    data
  );
}

// 检查用户余额
export function checkUserBalance(featureName, subType, count) {
  return request.get(
    '/flowTask/check-balance',
    { featureName, subType, count, _: Date.now() }
  );
}

// 更新任务
export function updateFlowTask(id, data) {
  return request.put(
    `/flowTask/${id}`,
    data
  );
}

// 删除任务
export function deleteFlowTask(id) {
  return request.delete(
    `/flowTask/${id}`
    );
}
