const workflow = {
  prompt: {
    "1": {
      "inputs": {
        "ckpt_name": "dreamshaperXL_v21TurboDPMSDE.safetensors",
        "vae_name": "sdxl_vae.safetensors",
        "clip_skip": -2,
        "lora_name": "None",
        "lora_model_strength": 1,
        "lora_clip_strength": 1,
        "positive": [
          "22",
          0
        ],
        "negative": "deformed, bad anatomy, disfigured, poorly drawn face, mutated, extra limb, ugly, poorly drawn hands, missing limb, floating limbs, disconnected limbs, disconnected head, malformed hands, long neck, mutated hands and fingers, bad hands, missing fingers, cropped, worst quality, low quality, mutation, poorly drawn, huge calf, bad hands, fused hand, missing hand, disappearing arms, disappearing thigh, disappearing calf, disappearing legs, missing fingers, fused fingers, abnormal eye proportion, Abnormal hands, abnormal legs, abnormal feet, abnormal fingers, drawing, painting, crayon, sketch, graphite, impressionist, noisy, blurry, soft, deformed, ugly, anime, cartoon, graphic, text, painting, crayon, graphite, abstract, glitch",
        "token_normalization": "none",
        "weight_interpretation": "comfy",
        "empty_latent_width": 512,
        "empty_latent_height": 512,
        "batch_size": 1,
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "Efficient Loader",
      "_meta": {
        "title": "Efficient Loader"
      }
    },
    "3": {
      "inputs": {
        "scale": 0.9,
        "start_at": 0,
        "end_at": 10000,
        "model": [
          "12",
          0
        ],
        "vae": [
          "1",
          4
        ],
        "image": [
          "59",
          0
        ],
        "mask": [
          "59",
          1
        ],
        "brushnet": [
          "4",
          0
        ],
        "positive": [
          "20",
          0
        ],
        "negative": [
          "20",
          1
        ]
      },
      "class_type": "BrushNet",
      "_meta": {
        "title": "BrushNet"
      }
    },
    "4": {
      "inputs": {
        "brushnet": "segmentation_mask_brushnet_ckpt_sdxl_v1/segmentation_mask_brushnet_ckpt_sdxl_v1.safetensors",
        "dtype": "float16"
      },
      "class_type": "BrushNetLoader",
      "_meta": {
        "title": "BrushNet Loader"
      }
    },
    "6": {
      "inputs": {
        "aspect_ratio": "original",
        "proportional_width": 1,
        "proportional_height": 1,
        "fit": "crop",
        "method": "lanczos",
        "round_to_multiple": "8",
        "scale_to_side": "longest",
        "scale_to_length": 1536,
        "background_color": "#000000",
        "image": [
          "62",
          0
        ]
      },
      "class_type": "LayerUtility: ImageScaleByAspectRatio V2",
      "_meta": {
        "title": "LayerUtility: ImageScaleByAspectRatio V2"
      }
    },
    "11": {
      "inputs": {
        "preset": "PLUS (high strength)",
        "model": [
          "1",
          0
        ]
      },
      "class_type": "IPAdapterUnifiedLoader",
      "_meta": {
        "title": "IPAdapter Unified Loader"
      }
    },
    "12": {
      "inputs": {
        "weight": 0.9,
        "weight_type": "strong style transfer",
        "combine_embeds": "concat",
        "start_at": 0,
        "end_at": 1,
        "embeds_scaling": "V only",
        "model": [
          "11",
          0
        ],
        "ipadapter": [
          "11",
          1
        ],
        "image": [
          "61",
          0
        ]
      },
      "class_type": "IPAdapterAdvanced",
      "_meta": {
        "title": "IPAdapter Advanced"
      }
    },
    "13": {
      "inputs": {
        "control_net_name": "sdxl/controlnet_union_diffusion_pytorch_model_promax.safetensors"
      },
      "class_type": "ControlNetLoader",
      "_meta": {
        "title": "Load ControlNet Model"
      }
    },
    "14": {
      "inputs": {
        "preprocessor": "CannyEdgePreprocessor",
        "resolution": 1024,
        "image": [
          "6",
          0
        ]
      },
      "class_type": "AIO_Preprocessor",
      "_meta": {
        "title": "AIO Aux Preprocessor"
      }
    },
    "15": {
      "inputs": {
        "strength": 0.5,
        "start_percent": 0,
        "end_percent": 0.7000000000000001,
        "positive": [
          "1",
          1
        ],
        "negative": [
          "1",
          2
        ],
        "control_net": [
          "16",
          0
        ],
        "image": [
          "14",
          0
        ],
        "vae": [
          "1",
          4
        ]
      },
      "class_type": "ControlNetApplyAdvanced",
      "_meta": {
        "title": "Apply ControlNet"
      }
    },
    "16": {
      "inputs": {
        "type": "canny/lineart/anime_lineart/mlsd",
        "control_net": [
          "13",
          0
        ]
      },
      "class_type": "SetUnionControlNetType",
      "_meta": {
        "title": "SetUnionControlNetType"
      }
    },
    "17": {
      "inputs": {
        "type": "depth",
        "control_net": [
          "13",
          0
        ]
      },
      "class_type": "SetUnionControlNetType",
      "_meta": {
        "title": "SetUnionControlNetType"
      }
    },
    "19": {
      "inputs": {
        "preprocessor": "DepthAnythingV2Preprocessor",
        "resolution": 1024,
        "image": [
          "6",
          0
        ]
      },
      "class_type": "AIO_Preprocessor",
      "_meta": {
        "title": "AIO Aux Preprocessor"
      }
    },
    "20": {
      "inputs": {
        "strength": 0.4,
        "start_percent": 0,
        "end_percent": 0.8,
        "positive": [
          "15",
          0
        ],
        "negative": [
          "15",
          1
        ],
        "control_net": [
          "17",
          0
        ],
        "image": [
          "19",
          0
        ]
      },
      "class_type": "ControlNetApplyAdvanced",
      "_meta": {
        "title": "Apply ControlNet"
      }
    },
    "22": {
      "inputs": {
        "delimiter": ", ",
        "clean_whitespace": "true",
        "text_a": [
          "23",
          0
        ],
        "text_b": [
          "24",
          0
        ]
      },
      "class_type": "Text Concatenate",
      "_meta": {
        "title": "Text Concatenate"
      }
    },
    "23": {
      "inputs": {
        "model": "wd-v1-4-moat-tagger-v2",
        "threshold": 0.35,
        "character_threshold": 0.85,
        "replace_underscore": false,
        "trailing_comma": false,
        "exclude_tags": "",
        "image": [
          "61",
          0
        ]
      },
      "class_type": "WD14Tagger|pysssss",
      "_meta": {
        "title": "WD14 Tagger 🐍"
      }
    },
    "24": {
      "inputs": {
        "String": "hyperrealistic art {prompt}, extremely high-resolution details, photographic, realism pushed to extreme, fine texture, incredibly lifelike",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "String",
      "_meta": {
        "title": "String"
      }
    },
    "37": {
      "inputs": {
        "transparency": 0,
        "offset_x": 0,
        "offset_y": 50,
        "rotation_angle": 0,
        "overlay_scale_factor": 1,
        "back_image": [
          "40",
          0
        ],
        "overlay_image": [
          "55",
          0
        ]
      },
      "class_type": "CR Overlay Transparent Image",
      "_meta": {
        "title": "🌁 CR Overlay Transparent Image"
      }
    },
    "39": {
      "inputs": {
        "min_width": 513,
        "image": [
          "6",
          0
        ]
      },
      "class_type": "GetImageSize_",
      "_meta": {
        "title": "Get Image Size ♾️Mixlab"
      }
    },
    "40": {
      "inputs": {
        "panel_width": [
          "39",
          0
        ],
        "panel_height": [
          "39",
          1
        ],
        "fill_color": "black"
      },
      "class_type": "CR Color Panel",
      "_meta": {
        "title": "🌁 CR Color Panel"
      }
    },
    "42": {
      "inputs": {
        "invert_mask": false,
        "blend_mode": "add",
        "opacity": 35,
        "background_image": [
          "53",
          0
        ],
        "layer_image": [
          "44",
          0
        ],
        "layer_mask": [
          "59",
          1
        ]
      },
      "class_type": "LayerUtility: ImageBlend",
      "_meta": {
        "title": "修改opacity参数，调整颜色"
      }
    },
    "44": {
      "inputs": {
        "method": "invert",
        "images": [
          "37",
          0
        ],
        "mask": [
          "59",
          1
        ]
      },
      "class_type": "AlphaChanelAddByMask",
      "_meta": {
        "title": "AlphaChanelAddByMask"
      }
    },
    "52": {
      "inputs": {
        "seed": 530080423725031,
        "steps": 20,
        "cfg": 6,
        "sampler_name": "euler_ancestral",
        "scheduler": "karras",
        "denoise": 1,
        "model": [
          "3",
          0
        ],
        "positive": [
          "3",
          1
        ],
        "negative": [
          "3",
          2
        ],
        "latent_image": [
          "58",
          0
        ]
      },
      "class_type": "KSampler",
      "_meta": {
        "title": "随机种子"
      }
    },
    "53": {
      "inputs": {
        "samples": [
          "52",
          0
        ],
        "vae": [
          "1",
          4
        ]
      },
      "class_type": "VAEDecode",
      "_meta": {
        "title": "VAE Decode"
      }
    },
    "55": {
      "inputs": {
        "width": 1536,
        "height": 1536,
        "interpolation": "lanczos",
        "method": "fill / crop",
        "condition": "always",
        "multiple_of": 0,
        "image": [
          "61",
          0
        ]
      },
      "class_type": "ImageResize+",
      "_meta": {
        "title": "🔧 Image Resize"
      }
    },
    "56": {
      "inputs": {
        "filename_prefix": "Fabric",
        "images": [
          "42",
          0
        ]
      },
      "class_type": "SaveImage",
      "_meta": {
        "title": "Save Image"
      }
    },
    "58": {
      "inputs": {
        "amount": 2,
        "samples": [
          "3",
          3
        ]
      },
      "class_type": "RepeatLatentBatch",
      "_meta": {
        "title": "图片数量"
      }
    },
    "59": {
      "inputs": {
        "threshold": 0.3,
        "detail_method": "VITMatte",
        "detail_erode": 6,
        "detail_dilate": 6,
        "black_point": 0.15,
        "white_point": 0.99,
        "process_detail": true,
        "prompt": "swimsuit",
        "device": "cuda",
        "max_megapixels": 2,
        "image": [
          "6",
          0
        ],
        "sam_models": [
          "60",
          0
        ]
      },
      "class_type": "LayerMask: SegmentAnythingUltra V3",
      "_meta": {
        "title": "款式描述"
      }
    },
    "60": {
      "inputs": {
        "sam_model": "sam_vit_h (2.56GB)",
        "grounding_dino_model": "GroundingDINO_SwinB (938MB)"
      },
      "class_type": "LayerMask: LoadSegmentAnythingModels",
      "_meta": {
        "title": "LayerMask: Load SegmentAnything Models(Advance)"
      }
    },
    "61": {
      "inputs": {
        "url": "https://",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "面料图片上传"
      }
    },
    "62": {
      "inputs": {
        "url": "https://",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "服装图片上传"
      }
    }
  }
};

module.exports = workflow; 