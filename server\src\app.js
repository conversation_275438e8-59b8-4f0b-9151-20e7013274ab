// 首先加载环境变量
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

const express = require('express');
const cors = require('cors');
const session = require('express-session');
const multer = require('multer');
const ComfyUIClient = require('./services/comfyUI');
const workflows = require('./services/workflows');
const { WorkflowType } = require('./services/workflows');
const fs = require('fs');
const archiver = require('archiver');
const mongoose = require('mongoose');
// 从新的modules目录导入auth相关内容
const { authRoutes, User, auth, selectAuth } = require('./modules/auth');
const { errorHandler } = require('./utils/error');
// 更新CSRF中间件的导入路径
const { csrfMiddleware: { csrfProtection } } = require('./modules/security');
const { fileAccessControl } = require('./middleware/fileAccess');
const tasksRouter = require('./routes/tasks');
const uploadsRouter = require('./routes/uploads');
const downloadRouter = require('./routes/download');
const modelsRouter = require('./routes/models');
const captchaRoutes = require('./modules/captcha/captcha.routes'); // 添加新的captcha路由
const logger = require('./utils/logger');
const jwt = require('jsonwebtoken');
const sizeOf = require('image-size');
const Upload = require('./models/Upload'); // 导入Upload模型以支持文件状态管理
const adminUser = require('./modules/admin/user/user.routes');
// 导入路由

const app = express();
// 配置信任代理，以获取真实IP地址
app.set('trust proxy', true);

const comfyUI = new ComfyUIClient(
  process.env.COMFYUI_URL || 'http://htc-ksifabooi78t9jm6i-ewrdtowe-custom.service.onethingrobot.com:7860'
);

// CORS配置
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002','http://aibikini.cn','https://aibikini.cn'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'client-id', 'Accept', 'X-CSRF-Token'],
  credentials: true
}));

// 添加 session 中间件
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// 日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
  next();
});

// 请求日志中间件
app.use((req, res, next) => {
  const start = Date.now();
  
  // 请求结束后记录日志
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.httpRequest(req, res, duration);
  });
  
  next();
});

// 基础中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

  // 创建指定用户的文件夹结构
const createUserDirectories = (userId) => {
  const userDir = path.resolve(__dirname, `../storage/${userId}`);
  
  // 创建用户根目录
  if (!fs.existsSync(userDir)){
    fs.mkdirSync(userDir, { recursive: true, mode: 0o755 });
  }
  
  // 创建uploads目录
  const uploadDir = path.resolve(userDir, 'uploads');
  if (!fs.existsSync(uploadDir)){
    fs.mkdirSync(uploadDir, { recursive: true, mode: 0o755 });
  }
  
  // 创建临时目录
  const tempDir = path.resolve(userDir, 'temp');
  if (!fs.existsSync(tempDir)){
    fs.mkdirSync(tempDir, { recursive: true, mode: 0o755 });
  }
  
  // 创建model目录及子目录
  const modelDir = path.resolve(userDir, 'model');
  
  // 时尚大片页面目录
  const fashionDir = path.resolve(modelDir, 'fashion');
  const fashionGeneratedDir = path.resolve(fashionDir, 'generated');
  
  // 模特换装页面目录
  const tryOnDir = path.resolve(modelDir, 'try-on');
  const tryOnMaskDir = path.resolve(tryOnDir, 'mask');
  const tryOnGeneratedDir = path.resolve(tryOnDir, 'generated');
  
  // 服装复色页面目录
  const recolorDir = path.resolve(modelDir, 'recolor');
  const recolorGeneratedDir = path.resolve(recolorDir, 'generated');
  
  // 换面料页面目录
  const fabricDir = path.resolve(modelDir, 'fabric');
  const fabricGeneratedDir = path.resolve(fabricDir, 'generated');
  
  // 换背景页面目录
  const backgroundDir = path.resolve(modelDir, 'background');
  const backgroundGeneratedDir = path.resolve(backgroundDir, 'generated');
  
  // 虚拟模特页面目录
  const virtualDir = path.resolve(modelDir, 'virtual');
  const virtualGeneratedDir = path.resolve(virtualDir, 'generated');
  
  // 创建所有模特相关目录
  [
    // 基础目录
    modelDir,
    // 时尚大片页面
    fashionDir, fashionGeneratedDir,
    // 模特换装页面
    tryOnDir, tryOnMaskDir, tryOnGeneratedDir,
    // 服装复色页面
    recolorDir, recolorGeneratedDir,
    // 换面料页面
    fabricDir, fabricGeneratedDir,
    // 换背景页面
    backgroundDir, backgroundGeneratedDir,
    // 虚拟模特页面
    virtualDir, virtualGeneratedDir
  ].forEach(dir => {
    if (!fs.existsSync(dir)){
      fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
    }
  });
  
  // 创建style目录及子目录
  const styleDir = path.resolve(userDir, 'style');
  const inspirationDir = path.resolve(styleDir, 'inspiration');
  const inspirationGeneratedDir = path.resolve(inspirationDir, 'generated');
  const trendingDir = path.resolve(styleDir, 'trending');
  const trendingGeneratedDir = path.resolve(trendingDir, 'generated');
  
  // 爆款延伸页面目录
  const divergentDir = path.resolve(styleDir, 'divergent');
  const divergentGeneratedDir = path.resolve(divergentDir, 'generated');
  
  // 款式优化页面目录
  const optimizeDir = path.resolve(styleDir, 'optimize');
  const optimizeMaskDir = path.resolve(optimizeDir, 'mask');
  const optimizeGeneratedDir = path.resolve(optimizeDir, 'generated');
  
  [styleDir, inspirationDir, inspirationGeneratedDir, trendingDir, trendingGeneratedDir, divergentDir, divergentGeneratedDir, optimizeDir, optimizeMaskDir, optimizeGeneratedDir].forEach(dir => {
    if (!fs.existsSync(dir)){
      fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
    }
  });
  
  // 创建tools目录及子目录
  const toolsDir = path.resolve(userDir, 'tools');
  const extractDir = path.resolve(toolsDir, 'extract');
  const extractGeneratedDir = path.resolve(extractDir, 'generated');
  const upscaleDir = path.resolve(toolsDir, 'upscale');
  const upscaleGeneratedDir = path.resolve(upscaleDir, 'generated');
  const mattingDir = path.resolve(toolsDir, 'matting');
  const mattingGeneratedDir = path.resolve(mattingDir, 'generated');
  const extendDir = path.resolve(toolsDir, 'extend');
  const extendGeneratedDir = path.resolve(extendDir, 'generated');

  [toolsDir, mattingDir, mattingGeneratedDir, upscaleDir, upscaleGeneratedDir, extendDir, extendGeneratedDir, extractDir, extractGeneratedDir].forEach(dir => {
    if (!fs.existsSync(dir)){
      fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
    }
  });
  
  return {
    userDir,
    uploadDir,
    tempDir,
    modelDir,
    // 时尚大片页面
    fashionDir,
    fashionGeneratedDir,
    // 模特换装页面
    tryOnDir,
    tryOnMaskDir,
    tryOnGeneratedDir,
    // 服装复色页面
    recolorDir,
    recolorGeneratedDir,
    // 换面料页面
    fabricDir,
    fabricGeneratedDir,
    // 换背景页面
    backgroundDir,
    backgroundGeneratedDir,
    // 虚拟模特页面
    virtualDir,
    virtualGeneratedDir,
    // 款式优化页面
    optimizeDir,
    optimizeMaskDir,
    optimizeGeneratedDir,
    // 灵感探索页面
    inspirationDir,
    inspirationGeneratedDir,
    // 爆款开发页面
    trendingDir,
    trendingGeneratedDir,
    // 爆款延伸页面
    divergentDir,
    divergentGeneratedDir,
    // 图片取词页面
    extractDir,
    extractGeneratedDir,
    // 高清放大页面
    upscaleDir,
    upscaleGeneratedDir,
    // 自动抠图页面
    mattingDir,
    mattingGeneratedDir,
    // 智能扩图页面
    extendDir,
    extendGeneratedDir
  };
};

// 为开发阶段创建默认的developer用户目录
createUserDirectories('developer');

// 设置文件访问控制选项
const fileAccessOptions = {
  // 在生产环境禁止未登录用户访问developer目录
  allowDeveloper: process.env.NODE_ENV !== 'production',
  // 添加调试选项，打印更多日志
  debug: true
};

// 修改文件上传配置，添加用户ID路径
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // 获取用户ID，如果没有则使用'developer'
    const userId = req.user?._id?.toString() || 'developer';
    
    // 确保用户目录结构存在
    const userDirectories = createUserDirectories(userId);
    
    // 使用用户特定的上传目录
    cb(null, userDirectories.uploadDir);
  },
  filename: (req, file, cb) => {
    // 检查是否提供了fileId（从URL查询参数或字段名中获取）
    // 注意：在multer的filename函数中，req.body通常还未解析完成，所以从query参数获取更可靠
    const fileId = req.query.fileId || 
                   (file.originalname.includes('fileId-') ? file.originalname.split('fileId-')[1].split('.')[0] : null);
    
    if (fileId) {
      // 提取文件扩展名
      const ext = path.extname(file.originalname).toLowerCase();
      // 确保fileId有正确的扩展名
      const fileIdWithExt = fileId.endsWith(ext) ? fileId : `${fileId}${ext}`;
      
      console.log(`使用前端提供的fileId作为文件名: ${fileIdWithExt}`);
      cb(null, fileIdWithExt);
    } else {
      // 没有提供fileId，使用原来的生成逻辑
      const ext = path.extname(file.originalname).toLowerCase();
      const uniqueName = `${Date.now()}-${Math.random().toString(36).slice(2)}${ext}`;
      console.log(`使用服务器生成的文件名: ${uniqueName}`);
      cb(null, uniqueName);
    }
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/avif'];
    if (!allowedTypes.includes(file.mimetype)) {
      cb(new Error('不支持的文件类型'));
      return;
    }
    cb(null, true);
  }
});

// 添加针对developer目录的直接访问路径
app.use('/developer', express.static(path.resolve(__dirname, '../storage/developer'), {
  maxAge: '1d',
  dotfiles: 'ignore',
  etag: true
}));

// 添加对storage路径的直接访问
app.use('/storage', fileAccessControl({ baseDir: '', ...fileAccessOptions }), (req, res, next) => {
  // 记录请求路径用于调试
  console.log(`请求文件路径: ${req.path}`);
  
  // 检查文件是否存在
  const fullPath = path.resolve(__dirname, '../storage', req.path.slice(1));
  console.log(`完整文件路径: ${fullPath}`);
  
  if (fs.existsSync(fullPath)) {
    console.log(`文件存在: ${fullPath}`);
  } else {
    console.log(`文件不存在: ${fullPath}`);
  }
  
  next();
}, express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true,
  fallthrough: false, // 文件不存在时返回404
  dotfiles: 'allow' // 允许访问以点开头的文件
}));

// 为uploads目录添加一个单独的、无中间件的直接访问点
app.use('/direct-uploads', express.static(path.resolve(__dirname, '../storage/developer/uploads'), {
  maxAge: '1d',
  etag: true,
  fallthrough: false,
  dotfiles: 'allow'
}));

// 添加错误处理中间件，专门处理静态文件访问错误
app.use((err, req, res, next) => {
  if (req.path.startsWith('/storage/')) {
    console.error('静态文件访问错误:', req.path, err);
    return res.status(404).json({
      success: false,
      message: '文件不存在或无法访问',
      path: req.path,
      error: err.message
    });
  }
  next(err);
});

// 修改静态文件服务路由，使用文件访问控制中间件
// 用户上传目录
app.use('/uploads', fileAccessControl({ baseDir: 'uploads', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 生成图片的静态文件服务
app.use('/fashion/generated', fileAccessControl({ baseDir: 'model/fashion/generated', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 配置try-on相关的静态文件服务
app.use('/try-on/generated', fileAccessControl({ baseDir: 'model/try-on/generated', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 模特图片也使用用户特定目录
app.use('/try-on/models', fileAccessControl({ baseDir: 'uploads', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 向后兼容的generated路由
app.use('/generated', fileAccessControl({ baseDir: 'model/fashion/generated', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 为fashion页面的generated图片配置静态文件服务
app.use('/model/fashion/generated', fileAccessControl({ baseDir: 'model/fashion/generated', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 为try-on页面的generated图片配置静态文件服务
app.use('/model/try-on/generated', fileAccessControl({ baseDir: 'model/try-on/generated', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 为蒙版目录配置静态文件服务
app.use('/model/try-on/mask', fileAccessControl({ baseDir: 'model/try-on/mask', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 为虚拟模特目录配置静态文件服务
app.use('/model/virtual', fileAccessControl({ baseDir: 'model/virtual', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 为灵感探索目录配置静态文件服务
app.use('/style/inspiration/generated', fileAccessControl({ baseDir: 'style/inspiration/generated', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 为图片取词目录配置静态文件服务
app.use('/tools/extract/generated', fileAccessControl({ baseDir: 'tools/extract/generated', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 为高清放大目录配置静态文件服务
app.use('/tools/upscale/generated', fileAccessControl({ baseDir: 'tools/upscale/generated', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 为自动抠图目录配置静态文件服务
app.use('/tools/matting/generated', fileAccessControl({ baseDir: 'tools/matting/generated', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 为智能扩图目录配置静态文件服务
app.use('/tools/extend/generated', fileAccessControl({ baseDir: 'tools/extend/generated', ...fileAccessOptions }), express.static(path.resolve(__dirname, '../storage'), {
  maxAge: '1d',
  etag: true
}));

// 对其他API路由应用CSRF验证
app.use('/api', csrfProtection([
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/forgot-password',
  '/api/auth/reset-password',
  '/api/process',
  '/api/mask-upload',
  '/api/uploads/upload-mask',
  '/api/tasks/*',  // 确保任务删除路由始终排除CSRF验证
  // 在开发环境中添加更多排除项
  ...(process.env.NODE_ENV === 'development' ? [
    '/api/process',
    '/api/uploads/image',
    '/api/uploads/*'
  ] : [])
]));

// 挂载路由
app.use('/api/auth', authRoutes); // 修改认证路由的挂载点
app.use('/api/tasks', tasksRouter);
app.use('/api/uploads', uploadsRouter);
app.use('/api/download', downloadRouter);
app.use('/api/models', modelsRouter);
app.use('/api/captcha', captchaRoutes); // 添加新的captcha路由
app.use('/api/admin/user',adminUser);

// 导入实例管理路由
const instanceRoutes = require('./routes/instance.routes');
// 添加用户算力路由
const userCreditRoutes = require('./modules/user/credits/credit.routes');
app.use('/api/user', userCreditRoutes);

// 注册公告路由
const announcementRoutes = require('./modules/admin/announcement/announcement.routes');
app.use('/api', announcementRoutes);

// 注册路由
app.use('/api/admin/instances', instanceRoutes);
// 在app.js中添加
const comfyUIRoutes = require('./routes/comfyUI.routes');
app.use('/api/comfyui', comfyUIRoutes);

const ossUploadRoutes = require('./routes/ossUpload.routes');
app.use('/api/oss', ossUploadRoutes);

// 在其他路由注册之后添加
const subscriptionRoutes = require('./modules/admin/subscribe/subscription.routes');
const planRoutes = require('./modules/admin/subscribe/plan.routes');
const creditsRoutes = require('./modules/admin/credits/credit.routes')

// 注册订阅相关路由
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/plans', planRoutes);
app.use('/api/admin', creditsRoutes);

const flowTaskRoutes =require('./routes/tasks.routes.js')
app.use('/api/flowTask',flowTaskRoutes)

// 注册RunningHub路由
const runningHubRoutes = require('./routes/runningHub.routes');
app.use('/api/runninghub', runningHubRoutes);

// 注册RunningHub配置路由（普通用户）
const runningHubConfigRoutes = require('./routes/runningHubConfig.routes');
app.use('/api/runninghub/configs', runningHubConfigRoutes);

// 注册RunningHub管理路由
const runningHubAdminRoutes = require('./routes/runningHubAdmin.routes');
app.use('/api/runninghub/admin', runningHubAdminRoutes);

// 注册工作流管理路由
const workflowRoutes = require('./routes/workflow.routes');
app.use('/api/workflows', workflowRoutes);

// 添加GET方法处理/api/process请求
app.get('/api/process', selectAuth, async (req, res) => {
  try {
    console.log('处理查询参数:', req.query);
    
    // 获取用户ID
    const userId = req.user._id.toString();
    
    // 获取页面类型和图片类型
    const pageType = req.query.pageType || 'fashion';
    const imageType = req.query.imageType || 'clothing';
    
    // 返回处理结果
    return res.status(200).json({
      success: true,
      message: '查询参数处理成功',
      data: {
        userId,
        pageType,
        imageType
      }
    });
  } catch (error) {
    console.error('处理查询参数错误:', error);
    return res.status(500).json({
      success: false,
      message: '处理查询参数失败: ' + error.message
    });
  }
});

// 添加一个简单的测试路由来诊断问题
app.get('/api/test-models', (req, res) => {
  console.log('收到模型测试请求');
  res.json({ 
    success: true, 
    message: '路由测试成功',
    routes: {
      models: '/api/models/test',
      modelSave: '/api/models/save-virtual-model'
    }
  });
});

// 图片处理路由 
// 注意：此接口故意放在CSRF验证中间件之前，以避免在文件上传时可能出现的CSRF验证问题
// 这是因为某些情况下，特别是在跨域或使用FormData时，CSRF令牌可能无法正确传递
app.post('/api/process', selectAuth, upload.array('file', 9), async (req, res) => {
  try {
    console.log('收到处理请求，req.body类型:', typeof req.body);
    console.log('req.body键值:', Object.keys(req.body || {}));
    console.log('req.query:', req.query); // 检查URL查询参数
    
    // 获取用户ID
    const userId = req.user._id.toString();
    console.log(`处理用户 ${userId} 的图片请求`);
    
    // 确保用户目录结构存在，并获取目录路径
    const userDirectories = createUserDirectories(userId);
    const { uploadDir, tryOnGeneratedDir, fashionGeneratedDir } = userDirectories;
    
    // 获取工作流类型
    const workflowType = req.body?.workflow || 'mattingbg';  // 默认使用mattingbg工作流（原为ocbg）
    
    // 获取页面类型 - 尝试从body和query中获取
    const pageType = req.body?.pageType || req.query?.pageType || 'fashion';  // 默认为fashion页面
    console.log(`页面类型pageType=${pageType}，是否来自body:`, req.body && 'pageType' in req.body, 
                '是否来自query:', req.query && 'pageType' in req.query);
    
    // 获取图片类型 - 尝试从body和query中获取
    const imageType = req.body?.imageType || req.query?.imageType || 'clothing';  // 默认为服装图片
    console.log(`图片类型imageType=${imageType}，是否来自body:`, req.body && 'imageType' in req.body,
                '是否来自query:', req.query && 'imageType' in req.query);
    
    // 打印完整的请求体进行调试
    console.log('接收到请求体:', JSON.stringify(req.body || {}, null, 2));
    console.log('接收到查询参数:', JSON.stringify(req.query || {}, null, 2));
    
    console.log('处理请求参数:', {
      workflowType,
      pageType,
      imageType,
      fileCount: req.files ? req.files.length : 0
    });
    
    // 为当前请求创建ComfyUI客户端实例，使用当前用户ID
    const userComfyUI = new ComfyUIClient(
      process.env.COMFYUI_URL || 'http://htc-ksifabooi78t9jm6i-ewrdtowe-custom.service.onethingrobot.com:7860',
      userId
    );
    
    // 检查是否使用文件引用模式
    const useExistingFiles = req.body.useExistingFiles === 'true';
    const fileReferences = req.body.fileReferences ? 
      (Array.isArray(req.body.fileReferences) ? req.body.fileReferences : [req.body.fileReferences]) : 
      [];
      
    console.log('使用已存在文件模式:', useExistingFiles);
    console.log('文件引用列表:', fileReferences);
    console.log('文件引用详细信息:', {
      useExistingFiles: useExistingFiles,
      fileReferencesType: typeof req.body.fileReferences,
      fileReferencesValue: req.body.fileReferences,
      fileReferencesIsArray: Array.isArray(req.body.fileReferences),
      fileReferencesLength: fileReferences.length,
      bodyKeys: Object.keys(req.body)
    });
    
    // 使用工作流管理模块获取适合当前页面和图片类型的工作流
    let workflow;
    try {
      const { getWorkflow } = require('./services/workflows');
      workflow = getWorkflow(workflowType, pageType, imageType);
    } catch (error) {
      console.error('获取工作流配置失败:', error);
      return res.status(400).json({
        success: false,
        message: `无效的工作流类型: ${workflowType}`
      });
    }

    // 如果使用文件引用模式且没有上传新文件，则检查文件引用
    if (useExistingFiles && fileReferences.length > 0) {
      // 直接使用已有文件的处理逻辑
      const resolvedFiles = fileReferences.map(filename => {
        // 首先尝试在 uploadDir 中查找文件
        let filePath = path.join(uploadDir, filename);
        console.log(`在上传目录中检查引用文件: ${filePath}`);
        
        // 统一使用generated目录查找图片
        
        if (!fs.existsSync(filePath)) {
          console.error(`引用的文件不存在: ${filePath}`);
          return null;
        }
        
        return {
          filename,
          path: filePath,
          size: fs.statSync(filePath).size,
          mimetype: path.extname(filename).toLowerCase() === '.png' ? 'image/png' : 'image/jpeg',
          originalname: filename
        };
      }).filter(Boolean);
      
      if (resolvedFiles.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的图片文件引用'
        });
      }
      
      console.log(`已解析 ${resolvedFiles.length} 个文件引用`);
      req.files = resolvedFiles; // 使用解析的文件引用替代上传的文件
    } else if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请上传图片文件'
      });
    }

    console.log('处理上传文件:', {
      workflowType,
      pageType,
      imageType,
      fileCount: req.files.length,
      files: req.files.map(file => ({
        filename: file.filename,
        size: file.size,
        mimetype: file.mimetype,
        path: file.path,
        absolutePath: path.resolve(file.path)
      }))
    });

    // 如果用户已登录，保存上传记录到数据库
    if (req.headers.authorization && !useExistingFiles) { // 只有实际上传新文件时才创建记录
      try {
        const token = req.headers.authorization.split(' ')[1];
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const userId = decoded.userId;
        
        // 为每个文件创建上传记录
        for (const file of req.files) {
          const upload = new Upload({
            userId,
            filename: file.filename,
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            pageType,
            imageType
          });
          
          await upload.save();
          console.log(`已保存上传记录: ${file.filename} 用户ID: ${userId} 页面类型: ${pageType} 图片类型: ${imageType}`);
        }
      } catch (error) {
        console.error('保存上传记录失败:', error);
        // 不中断主流程，继续处理图片
      }
    }

    // 判断是否需要ComfyUI服务处理
    const needsComfyUI = workflowType !== 'upload' && !(
      (pageType === 'try-on' && imageType === 'model') ||
      (pageType === 'try-on' && imageType === 'clothing') || // 添加服装图片到不需要ComfyUI处理的条件
      (pageType === 'background' && imageType === 'foreground') ||
      (pageType === 'recolor' && imageType === 'clothing') ||
      (pageType === 'fabric' && imageType === 'clothing')
    );
    console.log(`是否需要ComfyUI服务: ${needsComfyUI} (页面类型: ${pageType}, 图片类型: ${imageType}, 工作流: ${workflowType})`);

    // 仅在需要时检查ComfyUI服务
    if (needsComfyUI) {
      console.log('正在检查ComfyUI服务状态...');
      const isComfyUIAvailable = await userComfyUI.checkService();
      if (!isComfyUIAvailable) {
        return res.status(503).json({
          success: false,
          message: 'AI服务不可用，请稍后重试'
        });
      }
      console.log('ComfyUI服务可用状态:', isComfyUIAvailable);
    } else {
      console.log('跳过ComfyUI服务检查，直接处理图片');
    }

    // 定义目标目录
    // 使用ComfyUI客户端的方法获取正确的目录
    let targetDir;
    // 已移除抠图功能，所有图片都使用generated目录
    targetDir = userComfyUI.getTargetGeneratedDir(pageType, imageType);
    console.log(`${pageType}页面使用${workflowType || '默认'}工作流，保存到generated目录:`, targetDir);
    
    console.log('目标处理目录:', {
      pageType,
      imageType,
      workflowType,
      targetDir
    });

    // 处理所有文件
    const results = [];
    
    // 检查是否为rmbgfile批量处理工作流
    if ((workflowType === 'mattingbgfile' || workflowType === 'mattingclofile') && req.files.length > 1) {  // 从cutclothingfile改为mattingclofile
      console.log('批量处理工作流检测到，将处理所有图片：', req.files.length);
      console.log(`检测到mattingbgfile批量处理工作流，将收集所有${req.files.length}张图片一次性处理`);
      
      // 所有文件都不需要在上传阶段自动抠图处理
      const filesToProcess = [];
      for (const file of req.files) {
          // 所有文件直接添加到结果中
          console.log(`文件 ${file.filename} 无需自动抠图处理，直接添加到结果`);
          const stats = fs.statSync(file.path);
          let dimensions;
          
          try {
            dimensions = sizeOf(file.path);
          } catch (sizeError) {
            console.error(`获取图片尺寸失败:`, sizeError);
            throw new Error(`无法获取图片尺寸: ${sizeError.message}`);
          }
          
          const relativePath = file.path.split('storage')[1].replace(/\\/g, '/');
          
          results.push({
            serverFileName: file.filename, // 使用serverFileName替代旧字段
            processedFile: file.filename,
            relativePath: `/storage${relativePath}`,
            fileInfo: {
              size: stats.size,
              width: dimensions.width,
              height: dimensions.height,
              type: dimensions.type,
              serverFileName: file.filename // 确保在fileInfo中也设置serverFileName
            }
          });
      }
      
      if (filesToProcess.length === 0) {
        console.log('没有需要处理的文件');
        return res.json({ success: true, results });
      }
      
      // 上传所有文件到ComfyUI
      const uploadedImages = [];
      for (let i = 0; i < filesToProcess.length; i++) {
        const file = filesToProcess[i];
        // 确保文件名按顺序排序
        const uniquePrefix = i.toString().padStart(5, '0');
        const fileName = `${uniquePrefix}_${file.filename.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
        
        try {
          console.log(`上传文件到ComfyUI: ${file.path}`);
          const uploadResult = await userComfyUI.uploadImage(file.path, fileName);
          
          if (uploadResult && uploadResult.name) {
            uploadedImages.push({
              file: file,
              name: uploadResult.name,
              index: i
            });
          } else {
            throw new Error(`上传文件到ComfyUI失败: ${fileName}`);
          }
        } catch (error) {
          console.error(`上传文件到ComfyUI失败: ${error.message}`);
          throw new Error(`上传文件到ComfyUI失败: ${error.message}`);
        }
      }
      
      if (uploadedImages.length === 0) {
        throw new Error('没有成功上传的文件');
      }
      
      // 准备批量处理工作流参数
      const workflowParams = {
        images: uploadedImages.map(img => img.name),
        additionalParams: {}
      };
      
      console.log(`执行rmbgfile批量处理工作流，处理 ${uploadedImages.length} 张图片`);
      
      // 执行工作流
      const promptResult = await userComfyUI.executeWorkflow(workflow, workflowParams);
      console.log('工作流执行结果:', promptResult);
      
      // 等待处理结果
      let outputs;
      try {
        outputs = await userComfyUI.waitForResult(promptResult.prompt_id);
        console.log('处理输出:', outputs);
      } catch (wsError) {
        console.error('等待结果时出错:', wsError);
        throw new Error(`等待处理结果失败: ${wsError.message}`);
      }
      
      if (!outputs || !outputs.images || outputs.images.length === 0) {
        console.error('未收到有效的图像处理结果');
        throw new Error('未收到有效的图像处理结果');
      }
      
      // 处理所有生成的图片
      const processedImages = outputs.images;
      console.log(`收到 ${processedImages.length} 张处理后的图片`);
      
      for (let i = 0; i < processedImages.length; i++) {
        const processedImage = processedImages[i];
        const processedFilename = processedImage.filename;
        
        // 获取文件信息
        const fileInfo = processedImage.fileInfo;
        if (!fileInfo) {
          console.error(`未找到文件 ${processedFilename} 的信息，跳过处理`);
          continue;
        }
        
        try {
          // 确保目标目录存在
          if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true, mode: 0o755 });
          }
          
          const finalFilePath = path.join(targetDir, processedFilename);
          
          // 复制输出文件
          await userComfyUI.copyOutputFile(fileInfo, processedFilename, pageType, imageType, workflowType);
          
          // 获取文件信息
          const stats = fs.statSync(finalFilePath);
          const dimensions = sizeOf(finalFilePath);
          
          // 计算相对路径
          const relativePath = finalFilePath.split('storage')[1].replace(/\\/g, '/');
          const fullUrl = `http://localhost:3002${relativePath}`;
          
          // 确定原始文件 - 根据文件名中的索引
          let serverFileName = null;
          const match = processedFilename.match(/_0*(\d+)_/);
          if (match && match[1]) {
            const index = parseInt(match[1]);
            if (index >= 0 && index < uploadedImages.length) {
              serverFileName = uploadedImages[index].file.filename;
            } else {
              serverFileName = uploadedImages[0].file.filename;
            }
          } else {
            serverFileName = uploadedImages[0].file.filename;
          }
          
          results.push({
            serverFileName: serverFileName, // 使用serverFileName替代旧字段
            processedFile: processedFilename,
            relativePath: relativePath,
            url: fullUrl,
            pageType: pageType,
            imageType: imageType,
            fileInfo: {
              size: stats.size,
              width: dimensions.width,
              height: dimensions.height,
              type: dimensions.type,
              fullPath: finalFilePath,
              relativePath: relativePath,
              url: fullUrl,
              index: i,
              serverFileName: serverFileName // 确保在fileInfo中也设置serverFileName
            }
          });
        } catch (error) {
          console.error(`处理ComfyUI输出文件 ${processedFilename} 时出错:`, error);
        }
      }
      
      // 返回处理结果
      return res.json({
        success: true,
        results
      });
    }
    
    // 原有的单图处理逻辑
    for (const file of req.files) {
      try {
        console.log(`开始处理文件: ${file.filename} 页面类型: ${pageType} 图片类型: ${imageType} 工作流: ${workflowType}`);
        
        // 如果是'upload'工作流，或是无需抠图的特定类型，直接使用原图
        if (workflowType === 'upload' || 
            (pageType === 'try-on' && imageType === 'model') ||
            (pageType === 'try-on' && imageType === 'clothing') || // 添加服装图片类型到无需抠图的条件中
            (pageType === 'background' && imageType === 'foreground') ||
            (pageType === 'recolor' && imageType === 'clothing') ||
            (pageType === 'fabric' && imageType === 'clothing')) {
          
          console.log(`使用upload工作流或特殊类型，跳过ComfyUI处理: ${pageType} - ${imageType}`);
          
          // 获取文件信息
          const stats = fs.statSync(file.path);
          let dimensions;
          
          try {
            dimensions = sizeOf(file.path);
            console.log(`图片尺寸: ${dimensions.width}x${dimensions.height}`);
          } catch (sizeError) {
            console.error(`获取图片尺寸失败:`, sizeError);
            throw new Error(`无法获取图片尺寸: ${sizeError.message}`);
          }
          
          // 构建相对路径 - 这里直接使用uploads目录的文件，不再进行复制
          const relativePath = file.path.split('storage')[1].replace(/\\/g, '/');
          
          results.push({
            serverFileName: file.filename, // 使用serverFileName替代旧字段
            processedFile: file.filename, // 直接使用原始文件名
            relativePath: `/storage${relativePath}`, // 用于前端直接访问
            fileInfo: {
              size: stats.size,
              width: dimensions.width,
              height: dimensions.height,
              type: dimensions.type,
              serverFileName: file.filename // 确保在fileInfo中也设置serverFileName
            }
          });
          
          continue; // 跳过抠图处理
        }
        
        // 上传图片到ComfyUI
        const uploadResult = await userComfyUI.uploadImage(file.path);
        console.log('上传到ComfyUI成功:', uploadResult);

        // 构建工作流参数
        let workflowParams = {};
        

        // 检查当前是否是模特换装页面 + tryonauto工作流
        if (pageType === 'try-on' && workflowType === 'tryonauto') {
          // 这里的文件应该是服装图片
          if (imageType === 'clothing') {
            // 存储当前上传的服装图片信息
            workflowParams.clothing = uploadResult.name;
            console.log('设置服装图片:', workflowParams.clothing);
            
            // 获取模特图片信息 - 更全面地检查所有可能的来源
            let modelFileName = null;
            
            // 第1种可能：从顶层model字段获取
            if (req.body.model && req.body.model.serverFileName) {
              modelFileName = req.body.model.serverFileName;
              console.log('1-从顶层model.serverFileName获取模特文件名:', modelFileName);
            }
            // 第2种可能：从顶层model.processInfo.serverFileName获取
            else if (req.body.model && req.body.model.processInfo && req.body.model.processInfo.serverFileName) {
              modelFileName = req.body.model.processInfo.serverFileName;
              console.log('2-从顶层model.processInfo.serverFileName获取模特文件名:', modelFileName);
            }
            // 第3种可能：从components.uploadBox_Model获取
            else if (req.body.components?.uploadBox_Model?.serverFileName) {
              modelFileName = req.body.components.uploadBox_Model.serverFileName;
              console.log('3-从components.uploadBox_Model.serverFileName获取模特文件名:', modelFileName);
            }
            // 第4种可能：从components.modelPanel获取
            else if (req.body.components?.modelPanel?.serverFileName) {
              modelFileName = req.body.components.modelPanel.serverFileName;
              console.log('4-从components.modelPanel.serverFileName获取模特文件名:', modelFileName);
            }
            // 第5种可能：从components.modelPanel.processInfo获取
            else if (req.body.components?.modelPanel?.processInfo?.serverFileName) {
              modelFileName = req.body.components.modelPanel.processInfo.serverFileName;
              console.log('5-从components.modelPanel.processInfo.serverFileName获取模特文件名:', modelFileName);
            }
            
            // 打印整个请求体，帮助诊断
            console.log('完整请求体:', JSON.stringify(req.body, null, 2));
            
            if (modelFileName) {
              // 只在uploads目录寻找模特图片，不尝试其他目录
              const modelPath = path.join(uploadDir, modelFileName);
              console.log('模特图片完整路径:', modelPath);
              
              // 检查文件是否存在
              if (fs.existsSync(modelPath)) {
                console.log('找到模特图片文件，准备上传到ComfyUI:', modelPath);
                
                try {
                  // 上传模特图片到ComfyUI
                  const modelUploadResult = await userComfyUI.uploadImage(modelPath);
                  workflowParams.model = modelUploadResult.name;
                  console.log('模特图片上传到ComfyUI成功:', workflowParams.model);
                  
                  // 添加节点映射信息
                  workflowParams.nodeMapping = {
                    clothingNode: '31', // 服装图片注入到节点31
                    modelNode: '34'     // 模特图片注入到节点34
                  };
                  console.log('添加节点映射:', workflowParams.nodeMapping);
                } catch (modelError) {
                  console.error('上传模特图片到ComfyUI失败:', modelError);
                  throw new Error(`上传模特图片失败: ${modelError.message}`);
                }
              } else {
                console.error('未找到模特图片文件:', modelPath);
                // 紧急措施：尝试查找最近上传的模特图片
                try {
                  console.log('尝试查找最近上传的模特图片...');
                  const modelFiles = fs.readdirSync(uploadDir)
                    .filter(file => file.endsWith('.jpg') || file.endsWith('.png'))
                    .map(file => ({
                      name: file,
                      time: fs.statSync(path.join(uploadDir, file)).mtime.getTime()
                    }))
                    .sort((a, b) => b.time - a.time);
                  
                  if (modelFiles.length > 0) {
                    const recentModelFile = modelFiles[0].name;
                    console.log(`找到最近上传的图片: ${recentModelFile}`);
                    
                    const recentModelPath = path.join(uploadDir, recentModelFile);
                    const modelUploadResult = await userComfyUI.uploadImage(recentModelPath);
                    workflowParams.model = modelUploadResult.name;
                    console.log('成功上传最近的模特图片:', workflowParams.model);
                    
                    // 添加节点映射信息
                    workflowParams.nodeMapping = {
                      clothingNode: '31',
                      modelNode: '34'
                    };
                    console.log('添加节点映射:', workflowParams.nodeMapping);
                  } else {
                    throw new Error('找不到任何可用的模特图片');
                  }
                } catch (fallbackError) {
                  console.error('尝试备用方案也失败:', fallbackError);
                  throw new Error(`未找到模特图片文件: ${modelFileName}`);
                }
              }
            } else {
              console.error('未能确定模特图片文件名，尝试查找最近上传的模特图片...');
              
              // 紧急措施：尝试查找最近上传的模特图片
              try {
                console.log('尝试查找最近上传的模特图片...');
                const modelFiles = fs.readdirSync(uploadDir)
                  .filter(file => file.endsWith('.jpg') || file.endsWith('.png'))
                  .map(file => ({
                    name: file,
                    time: fs.statSync(path.join(uploadDir, file)).mtime.getTime()
                  }))
                  .sort((a, b) => b.time - a.time);
                
                if (modelFiles.length > 0) {
                  const recentModelFile = modelFiles[0].name;
                  console.log(`找到最近上传的图片: ${recentModelFile}`);
                  
                  const recentModelPath = path.join(uploadDir, recentModelFile);
                  const modelUploadResult = await userComfyUI.uploadImage(recentModelPath);
                  workflowParams.model = modelUploadResult.name;
                  console.log('成功上传最近的模特图片:', workflowParams.model);
                  
                  // 添加节点映射信息
                  workflowParams.nodeMapping = {
                    clothingNode: '31',
                    modelNode: '34'
                  };
                  console.log('添加节点映射:', workflowParams.nodeMapping);
                } else {
                  throw new Error('找不到任何可用的模特图片');
                }
              } catch (fallbackError) {
                console.error('尝试备用方案也失败:', fallbackError);
                throw new Error('未找到模特图片信息');
              }
            }
          }
        } else {
          // 其他工作流使用通用参数设置
          workflowParams = {
            image: uploadResult.name,
            additionalParams: {}
          };
        }
        
        console.log('最终工作流参数:', workflowParams);
        
        // 如果是tryonauto工作流但没有模特图片，返回错误
        if (pageType === 'try-on' && workflowType === 'tryonauto' && !workflowParams.model) {
          return res.status(400).json({
            success: false,
            message: '缺少必要的模特图片，无法执行模特换装工作流'
          });
        }

        // 解析用户提供的其他参数（如有）
        if (req.body?.additionalParams) {
          try {
            const additionalParams = typeof req.body.additionalParams === 'string' 
              ? JSON.parse(req.body.additionalParams) 
              : req.body.additionalParams;
            
            // 合并用户提供的参数
            workflowParams.additionalParams = {
              ...(workflowParams.additionalParams || {}),
              ...additionalParams
            };
            
            console.log('使用用户提供的额外参数:', workflowParams.additionalParams);
          } catch (error) {
            console.error('解析额外参数失败:', error);
          }
        }
        
        // 确保设置节点映射
        if (pageType === 'try-on' && workflowType === 'tryonauto' && !workflowParams.nodeMapping) {
          workflowParams.nodeMapping = {
            clothingNode: '31',  // 服装图片注入到节点31
            modelNode: '34'      // 模特图片注入到节点34
          };
          console.log('设置默认节点映射:', workflowParams.nodeMapping);
        }
        
        // 如果请求体自己指定了节点映射，使用请求体中的映射
        if (req.body.nodeMapping) {
          workflowParams.nodeMapping = {
            ...(workflowParams.nodeMapping || {}),
            ...req.body.nodeMapping
          };
          console.log('使用请求体中的节点映射:', workflowParams.nodeMapping);
        }
        
        console.log('工作流最终参数:', workflowParams);

        // 处理需要输入文件夹的特殊工作流
        if (workflow.metadata?.requiresInputFolder) {
          console.log('检测到需要使用输入文件夹的工作流:', workflowType);
          
          // 创建文件列表 - 从components或mainFile中提取
          const allFiles = [];
          
          // 确保components字段正确解析
          let componentsArray = [];
          
          // 尝试从请求体中获取组件数据
          try {
            // 如果components是字符串，尝试解析为JSON
            if (req.body.components && typeof req.body.components === 'string') {
              componentsArray = JSON.parse(req.body.components);
              console.log(`从请求体中解析到${componentsArray.length}个组件`);
            } 
            // 如果components已经是数组
            else if (req.body.components && Array.isArray(req.body.components)) {
              componentsArray = req.body.components;
              console.log(`请求体中包含${componentsArray.length}个组件`);
            }
            // 如果有taskData字段，尝试从中提取组件信息
            else if (req.body.taskData && typeof req.body.taskData === 'string') {
              const taskData = JSON.parse(req.body.taskData);
              if (taskData.components && Array.isArray(taskData.components)) {
                componentsArray = taskData.components;
                console.log(`从taskData中提取到${componentsArray.length}个组件`);
              }
            }
          } catch (error) {
            console.error('解析组件数据失败:', error);
          }
          
          // 如果没有找到任何组件数据，则使用已上传的文件
          if (componentsArray.length === 0) {
            console.warn('未找到组件数据，将使用已上传的文件');
            if (file) {
              allFiles.push({
                filePath: path.join(uploadDir, file.filename),
                filename: file.filename,
                serverFileName: file.filename,
                componentIndex: 0
              });
              console.log(`添加已上传文件: ${file.filename}`);
            }
          } else {
            // 从组件中提取所有图片文件
            console.log(`从${componentsArray.length}个组件中提取图片文件`);
            componentsArray.forEach((component, index) => {
              // 只使用serverFileName作为唯一文件引用
              if (component.serverFileName) {
                // 使用serverFileName作为文件名
                const fileName = component.serverFileName;
                // 尝试找到该文件的本地路径
                const filePath = path.join(uploadDir, fileName);
                
                if (fs.existsSync(filePath)) {
                  allFiles.push({
                    filePath,
                    filename: fileName,
                    serverFileName: fileName,
                    componentIndex: index
                  });
                  console.log(`添加组件${index}文件: ${filePath} (使用serverFileName)`);
                } else {
                  console.error(`组件${index}的文件不存在: ${filePath}`);
                }
              } else {
                console.error(`组件${index}缺少serverFileName，无法处理`);
              }
            });
          }
          
          if (allFiles.length === 0) {
            throw new Error('没有找到要处理的文件');
          }
          
          console.log(`找到${allFiles.length}个文件需要处理`);
          
          // 确定ComfyUI输入文件夹路径
          const comfyInputFolder = path.join(process.env.COMFYUI_PATH || '/root/ComfyUI', 'input');
          console.log(`确定的ComfyUI输入文件夹路径: ${comfyInputFolder}`);
          
          // 尝试确保ComfyUI输入文件夹存在，但可能无法直接操作云容器中的文件夹
          // 这里只记录意图，实际创建可能需要在容器内完成
          console.log(`应该确保ComfyUI输入文件夹存在: ${comfyInputFolder}`);
          
          // 复制文件到ComfyUI输入文件夹
          // 因为可能无法直接操作云容器内的文件，所以我们将文件上传到ComfyUI API
          const copiedFiles = [];
          for (let i = 0; i < allFiles.length; i++) {
            const file = allFiles[i];
            // 使用数字序列作为文件名，确保LoadImagesFromFolderKJ按顺序加载
            const uniquePrefix = i.toString().padStart(5, '0');
            const fileName = `${uniquePrefix}_${file.filename.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
            
            try {
              // 上传文件到ComfyUI
              console.log(`准备上传文件到ComfyUI: ${file.filePath}`);
              const uploadResult = await userComfyUI.uploadImage(file.filePath, fileName);
              
              if (uploadResult && uploadResult.name) {
                copiedFiles.push({
                  originalPath: file.filePath,
                  name: uploadResult.name,
                  serverFileName: file.serverFileName,
                  componentIndex: file.componentIndex
                });
                console.log(`成功上传文件到ComfyUI: ${uploadResult.name}`);
              } else {
                throw new Error(`上传文件到ComfyUI失败: ${fileName}`);
              }
            } catch (error) {
              console.error(`上传文件到ComfyUI失败: ${error.message}`);
              throw new Error(`上传文件到ComfyUI失败: ${error.message}`);
            }
          }
          
          // 更新工作流参数，添加images数组
          workflowParams.images = copiedFiles.map(file => file.name);
          console.log(`已添加${workflowParams.images.length}个图片路径到工作流参数:`, workflowParams.images);
        }

        // 执行工作流，传递参数
        const promptResult = await userComfyUI.executeWorkflow(workflow, workflowParams);
        console.log('工作流执行结果:', promptResult);

        // 等待处理结果
        let outputs;
        try {
          outputs = await userComfyUI.waitForResult(promptResult.prompt_id);
          console.log('处理输出:', outputs);
        } catch (wsError) {
          console.error('等待结果时出错:', wsError);
          throw new Error(`等待处理结果失败: ${wsError.message}`);
        }
        
        if (!outputs || !outputs.images || outputs.images.length === 0) {
          console.error('未收到有效的图像处理结果');
          throw new Error('未收到有效的图像处理结果');
        }
        
        // 获取所有处理后的图片信息  TODO 返回的图片 处理不保存到本地
        const processedImages = outputs.images;
        console.log(`收到 ${processedImages.length} 张处理后的图片`);

        // 处理所有生成的图片
        for (let i = 0; i < processedImages.length; i++) {
          const processedImage = processedImages[i];
          const processedFilename = processedImage.filename;
          console.log(`处理第 ${i + 1}/${processedImages.length} 张图片, 文件名: ${processedFilename}`);

          // 获取文件信息
          const fileInfo = processedImage.fileInfo;
          if (!fileInfo) {
            console.error(`未找到文件 ${processedFilename} 的信息，跳过处理`);
            continue;
          }
          
          // 使用comfyUI客户端的方法复制文件到指定目录
          try {
            // 确保目标目录存在
            if (!fs.existsSync(targetDir)) {
              fs.mkdirSync(targetDir, { recursive: true, mode: 0o755 });
              console.log(`创建目标目录: ${targetDir}`);
            }
            
            // 直接保存到正确的目标目录
            const finalFilePath = path.join(targetDir, processedFilename);
            
            // 复制输出文件
            await userComfyUI.copyOutputFile(fileInfo, processedFilename, pageType, imageType, workflowType);
            
            // 获取文件信息
            const stats = fs.statSync(finalFilePath);
            
            // 获取图片尺寸
            const sizeOf = require('image-size');
            const dimensions = sizeOf(finalFilePath);
            
            // 计算相对URL路径，便于前端访问
            // 从路径中提取相关部分，例如 /model/try-on/generated/filename.png
            const relativePath = finalFilePath.split('storage')[1].replace(/\\/g, '/');
            console.log('计算的相对路径:', relativePath);
            
            // 构建完整URL
            const fullUrl = `http://localhost:3002${relativePath}`;
            
            // 尝试确定对应的原始文件
            // 如果是批量处理工作流，尝试匹配索引
            let serverFileName = null;
            
            // 从请求体中获取组件数据
            const components = req.body.components ? 
              (typeof req.body.components === 'string' ? JSON.parse(req.body.components) : req.body.components) : 
              [];
            
            if (workflow.metadata?.requiresInputFolder && components && Array.isArray(components)) {
              // 从文件名中提取索引，修改正则表达式以匹配新格式 rmbgfile_00001_.png 或 cutclothingfile_00001_.png
              const match = processedFilename.match(/_0*(\d+)_/);
              if (match && match[1]) {
                const index = parseInt(match[1]) - 1; // 转换为0基索引
                if (components[index]) {
                  const matchedComponent = components[index];
                  if (matchedComponent.serverFileName) {
                    serverFileName = matchedComponent.serverFileName;
                    console.log(`根据索引 ${match[1]} (位置: ${index}) 匹配到原始文件: ${serverFileName}`);
                  } else {
                    console.error(`组件 ${index} 缺少serverFileName`);
                  }
                } else {
                  console.error(`索引 ${index} 超出组件数组范围(0-${components.length-1})`);
                }
              } else {
                console.error(`无法从处理后的文件名 ${processedFilename} 中提取有效索引`);
              }
            } else if (file && file.filename) {
              serverFileName = file.filename;
            }
            
            if (!serverFileName) {
              console.error(`无法确定文件 ${processedFilename} 对应的原始文件`);
              continue; // 跳过此图片，不添加到结果中
            }
            
            results.push({
              serverFileName: serverFileName, // 使用serverFileName替代旧字段
              processedFile: processedFilename,
              relativePath: relativePath,   // 添加相对路径
              url: fullUrl,                 // 直接添加可访问的URL
              pageType: pageType,           // 返回页面类型
              imageType: imageType,         // 返回图片类型
              fileInfo: {
                size: stats.size,
                width: dimensions.width,
                height: dimensions.height,
                type: dimensions.type,
                fullPath: finalFilePath,    // 返回完整路径用于调试
                relativePath: relativePath, // 同时在fileInfo中也提供
                url: fullUrl,               // 在fileInfo中也提供URL
                index: i                    // 添加索引信息
              }
            });
          } catch (error) {
            console.error(`处理ComfyUI输出文件 ${processedFilename} 时出错:`, error);
            console.error(error.stack);
            // 继续处理其他图片，不中断整个过程
          }
        }
      } catch (error) {
        console.error(`处理文件 ${file.filename} 时出错:`, error);
        results.push({
          serverFileName: file.filename, // 使用serverFileName替代旧字段
        });
      }
    }

    // 标记相关文件为已使用状态并关联任务ID
    try {
      // 获取clothing相关信息
      if (req.body.clothing && req.body.clothing.serverFileName) {
        // 查找并更新clothing文件状态
        await Upload.updateMany(
          { 
            userId: req.user._id, 
            filename: { $in: [req.body.clothing.serverFileName, req.body.clothing.processedFile].filter(Boolean) } 
          },
          { 
            $set: { 
              status: 'used',
              taskId: req.body.taskId 
            } 
          }
        );
      }
      
      // 如果有模特数据，也更新模特相关文件
      if (req.body.model && req.body.model.serverFileName) {
        await Upload.updateMany(
          { 
            userId: req.user._id, 
            filename: { $in: [req.body.model.serverFileName, req.body.model.processedFile].filter(Boolean) } 
          },
          { 
            $set: { 
              status: 'used',
              taskId: req.body.taskId 
            } 
          }
        );
        
        // 如果有蒙版文件，也更新它
        if (req.body.model.maskFileName) {
          await Upload.updateMany(
            { 
              userId: req.user._id, 
              filename: req.body.model.maskFileName 
            },
            { 
              $set: { 
                status: 'used',
                taskId: req.body.taskId 
              } 
            }
          );
        }
      }
      
      console.log(`已将相关文件标记为已使用并关联任务ID: ${req.body.taskId}`);
    } catch (error) {
      console.error('更新文件状态失败:', error);
      // 不中断流程，继续执行
    }

    res.json({
      success: true,
      results
    });
  } catch (error) {
    console.error('处理请求时出错:', error);
    res.status(500).json({
      success: false,
      message: error.message || '服务器内部错误'
    });
  }
});

// 图像生成API端点
app.post('/api/generate', auth, async (req, res) => {
  try {
    const { clothing, imageQuantity = 4, advancedSettings, model, scene } = req.body;
    
    // 验证必要参数
    if (!clothing || !clothing.id) {
      return res.status(400).json({
        success: false,
        message: '缺少必要的服装参数'
      });
    }
    
    // 创建任务ID
    const taskId = req.body.taskId || `TASK${Date.now()}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
    
    // 引入Task模型
    const Task = require('./models/Task');
    
    // 从配置文件引入页面-组件映射关系
    const PAGE_COMPONENT_MAP = require('./config/pageComponentMap');
    
    // 确定任务所需的页面类型
    const taskPageType = req.body.pageType || 'fashion';
    
    // 获取该页面需要的组件列表
    const neededComponents = PAGE_COMPONENT_MAP[taskPageType.toLowerCase()] || [];
    console.log(`页面 ${taskPageType} 需要的组件:`, neededComponents);
    
    // 从utils/idGenerator中导入ID生成函数
    const { generateId, ID_TYPES } = require('./utils/idGenerator');
    
    // 准备组件对象，使用新的组件结构
    const taskComponents = {};
    
    // 添加服装组件
    if (clothing && neededComponents.includes('clothingpanel')) {
      taskComponents.clothingPanel = {
        componentType: 'clothingPanel',
        componentId: `${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        name: clothing.name || '服装图片',
        isMainImage: true,
        serverFileName: clothing.serverFileName,
        url: clothing.url,
        fileInfo: clothing.fileInfo || {},
        status: 'completed'
      };
    }
    
    // 添加模型组件
    if (model && neededComponents.includes('modelpanel')) {
      taskComponents.modelPanel = {
        componentType: 'modelPanel',
        id: `${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        name: model.name || '模型设置',
        type: model.type || 'custom',
        preview: model.preview,
        prompt: model.prompt,
        negative_prompt: model.negative_prompt,
        description: model.description,
        negativeDescription: model.negativeDescription,
        tags: model.tags || [],
        status: 'completed'
      };
    }
    
    // 添加场景组件
    if (scene && neededComponents.includes('scenepanel')) {
      taskComponents.scenePanel = {
        componentType: 'scenePanel',
        id: `${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        name: scene.name || '场景设置',
        type: scene.type || 'custom',
        preview: scene.preview,
        prompt: scene.prompt,
        description: scene.description,
        status: 'completed'
      };
    }
    
    // 添加高级设置组件
    if (advancedSettings && neededComponents.includes('advancedpanel')) {
      taskComponents.advancedPanel = {
        componentType: 'advancedPanel',
        id: `${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        name: '高级设置',
        options: advancedSettings,
        status: 'completed'
      };
    }
    
    // 添加数量组件
    if (neededComponents.includes('quantitypanel')) {
      taskComponents.quantityPanel = {
        componentType: 'quantityPanel',
        id: `${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        name: '生成数量',
        value: imageQuantity || 4,
        status: 'completed'
      };
    }
    
    // 添加随机种子组件
    if (neededComponents.includes('randomseedselector')) {
      taskComponents.randomSeedSelector = {
        componentType: 'randomSeedSelector',
        id: `${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        name: '随机种子',
        useRandom: true,
        value: -1,
        status: 'completed'
      };
    }
    
    // 添加图片尺寸组件
    if (neededComponents.includes('imagesizeselector')) {
      taskComponents.imageSizeSelector = {
        componentType: 'imageSizeSelector',
        id: `${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        name: '图片尺寸',
        useDefault: true,
        width: 1024,
        height: 1024,
        status: 'completed'
      };
    }
    
    // 创建任务记录 - 使用新的组件结构
    const newTask = new Task({
      userId: req.user._id,
      taskId: taskId,
      status: 'pending',
      pageType: taskPageType,
      imageCount: imageQuantity || 4,
      components: taskComponents
    });
    
    // 保存任务
    await newTask.save();
    
    // 标记相关文件为已使用状态并关联任务ID
    try {
      // 获取clothing相关信息
      if (clothing && clothing.serverFileName) {
        // 查找并更新clothing文件状态
        await Upload.updateMany(
          { 
            userId: req.user._id, 
            filename: { $in: [clothing.serverFileName, clothing.processedFile].filter(Boolean) } 
          },
          { 
            $set: { 
              status: 'used',
              taskId: taskId 
            } 
          }
        );
      }
      
      // 如果有模特数据，也更新模特相关文件
      if (req.body.model && req.body.model.serverFileName) {
        await Upload.updateMany(
          { 
            userId: req.user._id, 
            filename: { $in: [req.body.model.serverFileName, req.body.model.processedFile].filter(Boolean) } 
          },
          { 
            $set: { 
              status: 'used',
              taskId: taskId 
            } 
          }
        );
        
        // 如果有蒙版文件，也更新它
        if (req.body.model.maskFileName) {
          await Upload.updateMany(
            { 
              userId: req.user._id, 
              filename: req.body.model.maskFileName 
            },
            { 
              $set: { 
                status: 'used',
                taskId: taskId 
              } 
            }
          );
        }
      }
      
      console.log(`已将相关文件标记为已使用并关联任务ID: ${taskId}`);
    } catch (error) {
      console.error('更新文件状态失败:', error);
      // 不中断流程，继续执行
    }
    
    // 这里会调用实际的图像生成逻辑（之后实现）
    // 目前只返回任务ID，前端可以通过轮询任务状态获取结果
    
    res.json({
      success: true,
      message: '生成任务已创建',
      taskId: taskId
    });
    
  } catch (error) {
    console.error('创建生成任务失败:', error);
    res.status(500).json({
      success: false,
      message: '创建生成任务失败: ' + error.message
    });
  }
});

// 批量下载路由 - 使用专用的download路由器，不在app.js中直接实现
app.use('/api/download', auth, downloadRouter);

// 添加测试路由，添加用户认证
app.get('/api/test-mask-upload', auth, (req, res) => {
  // 获取用户ID
  const userId = req.user._id.toString();
  
  // 检查mask目录是否存在 - 使用用户特定目录
  const maskDir = path.resolve(__dirname, `../../server/storage/${userId}/model/try-on/mask`);
  let dirExists = false;
  
  try {
    if (fs.existsSync(maskDir)) {
      dirExists = true;
      // 检查权限
      fs.accessSync(maskDir, fs.constants.R_OK | fs.constants.W_OK);
    } else {
      // 如果目录不存在，尝试创建它
      fs.mkdirSync(maskDir, { recursive: true });
      dirExists = fs.existsSync(maskDir);
    }
  } catch (error) {
    return res.json({
      success: false,
      message: '蒙版目录不存在或无权限访问',
      error: error.message,
      maskDir
    });
  }
  
  // 测试临时文件创建
  const testFile = path.join(maskDir, 'test_' + Date.now() + '.txt');
  let fileCreated = false;
  
  try {
    fs.writeFileSync(testFile, 'Test file for mask upload');
    fileCreated = true;
    
    // 删除测试文件
    fs.unlinkSync(testFile);
  } catch (error) {
    return res.json({
      success: false,
      message: '无法在蒙版目录中创建文件',
      error: error.message,
      maskDir,
      testFile
    });
  }
  
  res.json({
    success: true,
    message: '蒙版上传功能测试通过',
    maskDir,
    dirExists,
    fileCreated
  });
});

// 添加一个工作流测试路由
app.get('/api/test-workflow', async (req, res) => {
  try {
    const workflowType = req.query.workflow || 'mattingbg';  // 默认工作流从ocbg改为mattingbg
    const pageType = req.query.pageType || 'fashion';
    const imageType = req.query.imageType || 'clothing';
    const imagePath = req.query.imagePath;
    
    console.log(`收到工作流测试请求:`, { workflowType, pageType, imageType, imagePath });
    
    // 检查ComfyUI服务
    const isComfyUIAvailable = await comfyUI.checkService();
    if (!isComfyUIAvailable) {
      return res.status(503).json({
        success: false,
        message: 'ComfyUI服务不可用'
      });
    }
    
    // 执行工作流测试
    const result = await comfyUI.testWorkflow(workflowType, pageType, imageType, imagePath);
    
    return res.json(result);
  } catch (error) {
    console.error('工作流测试失败:', error);
    return res.status(500).json({
      success: false,
      message: `测试失败: ${error.message}`
    });
  }
});

// 配置错误处理中间件
app.use(errorHandler);

// 定期清理临时文件的函数
const cleanupTemporaryFiles = async () => {
  try {
    console.log('开始清理临时文件...');
    // 设置截止日期为7天前
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 7);
    
    // 查找需要清理的文件
    const filesToDelete = await Upload.find({
      status: 'temporary',
      createdAt: { $lt: cutoffDate }
    });
    
    console.log(`找到 ${filesToDelete.length} 个过期的临时文件需要清理`);
    
    let deletedCount = 0;
    let errorCount = 0;
    
    // 删除文件
    for (const file of filesToDelete) {
      try {
        // 构建文件路径
        const userId = file.userId.toString();
        const baseDir = path.resolve(__dirname, '../storage');
        
        // 尝试在可能的几个位置查找文件
        const possiblePaths = [
          path.join(baseDir, userId, 'uploads', file.filename),
          path.join(baseDir, userId, 'model', 'fashion', 'generated', file.filename),
          path.join(baseDir, userId, 'model', 'try-on', 'generated', file.filename),
          path.join(baseDir, userId, 'model', 'try-on', 'mask', file.filename)
        ];
        
        // 尝试删除文件
        for (const filePath of possiblePaths) {
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`已删除文件: ${filePath}`);
            break;
          }
        }
        
        // 从数据库中删除记录
        await Upload.deleteOne({ _id: file._id });
        deletedCount++;
      } catch (err) {
        console.error(`删除文件失败: ${file.filename}`, err);
        errorCount++;
      }
    }
    
    console.log(`临时文件清理完成: 成功删除 ${deletedCount} 个文件, ${errorCount} 个失败`);
  } catch (error) {
    console.error('清理临时文件时出错:', error);
  }
};

// 设置定期清理任务 - 每天执行一次
const ONE_DAY = 24 * 60 * 60 * 1000; // 24小时的毫秒数
setInterval(cleanupTemporaryFiles, ONE_DAY);

// 启动时也执行一次清理
if (process.env.NODE_ENV === 'production') {
  // 生产环境直接清理
  cleanupTemporaryFiles();
} else {
  // 开发环境延迟5分钟后清理，避免影响开发
  setTimeout(cleanupTemporaryFiles, 5 * 60 * 1000);
}

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: '未找到请求的API路径'
  });
});

// 设置日志级别
if (process.env.NODE_ENV === 'production') {
  logger.setLevel('INFO');
} else if (process.env.NODE_ENV === 'test') {
  logger.setLevel('WARN');
} else {
  logger.setLevel('DEBUG');
}

// 连接MongoDB
mongoose.connect(process.env.MONGODB_URI,

)
  .then(() => {
    console.log('MongoDB连接成功');
    // 启动服务器
    const PORT = process.env.PORT || 3002;
    app.listen(PORT, () => {
      // 创建默认用户的目录结构
      createUserDirectories('developer');

      console.log(`【INFO】服务器运行在端口 ${PORT}`, {
        "environment": process.env.NODE_ENV || "development",
        "startTime": new Date().toISOString()
      });
      
      // 打印完整目录结构信息
      // 模特服务相关目录
      console.log('\n模特相关目录(model目录):');
      console.log('- 时尚大片页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\model\\fashion\\generated');
      console.log('- 模特换装页面：');
      console.log('  蒙版目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\model\\try-on\\mask');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\model\\try-on\\generated');
      console.log('- 服装复色页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\model\\recolor\\generated');
      console.log('- 换面料页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\model\\fabric\\generated');
      console.log('- 换背景页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\model\\background\\generated');
      console.log('- 虚拟模特页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\model\\virtual\\generated');

      // 款式服务相关目录
      console.log('\n款式服务相关目录(style目录):');
      console.log('- 款式优化页面：');
      console.log('  蒙版目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\style\\optimize\\mask');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\style\\optimize\\generated');
      console.log('- 灵感探索页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\style\\inspiration\\generated');
      console.log('- 爆款开发页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\style\\trending\\generated');

      // 工具相关目录
      console.log('\n工具相关目录(tools目录):');
      console.log('- 图片取词页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\tools\\extract\\generated');
      console.log('- 高清放大页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\tools\\upscale\\generated');
      console.log('- 自动抠图页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\tools\\matting\\generated');
      console.log('- 智能扩图页面：');
      console.log('  生成目录：E:\\AIBIKINI\\Project\\aibikini_0.1.0\\server\\storage\\developer\\tools\\extend\\generated');

      console.log('====================================================\n');
      
      // 连接到ComfyUI
      connectToComfyUI(); // 恢复调用，因为函数已经正确实现
    });
  })
  .catch(err => {
    console.error('MongoDB连接失败:', err);
    process.exit(1);
  }); 

// 连接到ComfyUI的函数定义
const connectToComfyUI = async () => {
  try {
    console.log('尝试连接到ComfyUI云服务...');
    console.log(`ComfyUI云实例地址: ${comfyUI.baseURL}`);
    console.log(`使用API密钥: ${comfyUI.apiKey ? '已配置' : '未配置'}`);
    
    const isAvailable = await comfyUI.checkService();
    if (isAvailable) {
      console.log('ComfyUI云服务连接成功!');
    } else {
      console.warn('ComfyUI云服务连接失败，某些功能可能不可用');
    }
  } catch (error) {
    console.error('连接ComfyUI云服务时出错:', error);
    console.warn('ComfyUI云服务不可用，但服务器将继续运行');
  }
}; 
