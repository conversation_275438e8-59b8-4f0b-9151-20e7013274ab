/* 导入统一样式 */
@import '../../styles/theme.css';
@import '../../styles/buttons.css';
@import '../../styles/inputs.css';
@import '../../styles/panels.css';

/* 为取词选项组件覆盖面板高度 */
.extraction-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  min-height: 88px; /* 改为最小高度而非固定高度 */
  display: flex;
  margin-bottom: var(--spacing-xxs);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.extraction-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
}

/* 左侧标签样式 */
.extraction-label {
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
  flex-shrink: 0; /* 防止标签区域被压缩 */
}

.extraction-label span {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

/* 调整内容区域样式 */
.extraction-panel .component-text {
  flex: 1;
  padding: 0;
  margin: 0;
}

.extraction-panel .component-content {
  padding: 0;
  margin: 0;
  height: 100%;
  display: flex;
  align-items: center;
}

/* 调整标题样式 */
.extraction-panel .component-text h3 {
  margin: 12px 10px;
}

/* 为开关设置样式 */
h3 .toggle-switch {
  margin-left: 12px; /* 开关与标题的间距 */
}

/* 确保内容区域有合适的样式 */
.extraction-options-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 12px 16px; /* 修改为上下内边距也为12px，与TypeSelector完全一致 */
  justify-content: center; /* 垂直居中 */
  gap: 6px; /* 减小行间距 */
}

/* 行容器样式 */
.extraction-options-row {
  display: flex;
  width: 100%;
  justify-content: flex-start; /* 改为从左侧开始，而不是两端对齐 */
  gap: 6px 24px; /* 增加选项之间的水平间距 */
  flex-wrap: wrap; /* 允许内容换行 */
}

/* 选项项样式 */
.extraction-option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px 0; /* 减小上下内边距 */
  width: auto; /* 改为自动宽度，不再固定宽度百分比 */
}

.extraction-option-item .option-label {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin-right: 8px; /* 增加文本和开关之间的间距，与TypeSelector一致 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 400; /* 添加字重设置，与TypeSelector一致 */
}

/* 开关样式 */
.extraction-option-item .toggle-switch {
  flex-shrink: 0; /* 防止开关被压缩 */
}

/* 自定义文本输入框容器样式 - 恢复较小的边距 */
.custom-text-container {
  margin-top: 6px;
  width: 100%;
}

.custom-text-container .component-content {
  position: relative; /* 为拖动按钮提供定位上下文 */
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0px 0px 4px 0px; /* 下方4px，左右0，和右侧保持一致 */
}

/* 自定义文本输入框样式 - 与 TextDescriptionPanel 完全一致 */
.custom-text-input {
  width: 100%;
  /* 高度由JS动态设置 */
  min-height: 60px;
  max-height: 300px;
  padding: 15px 2px 15px 12px; /* 右侧padding从12px缩小到2px */
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: var(--transition-normal);
  resize: none;
  line-height: 1.5;
  box-sizing: border-box; /* 确保内边距不影响宽度计算 */
  margin: 0; /* 移除margin，因为父容器已经有padding */
}

.custom-text-input:hover {
  border-color: var(--brand-primary);
}

.custom-text-input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

.custom-text-input::placeholder {
  color: var(--text-tertiary);
}

/* 适配暗色主题 - 与 TextDescriptionPanel 完全一致 */
[data-theme="dark"] .custom-text-input {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .custom-text-input:hover {
  border-color: var(--brand-primary);
}

[data-theme="dark"] .custom-text-input:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(255, 60, 106, 0.15);
}

/* 覆盖全局拖动按钮样式 - 保持与原始设计一致的间距 */
.custom-text-container .component-content .textarea-resize-handle {
  position: absolute !important;
  bottom: 8px !important;
  right: 4px !important;
  z-index: 5 !important;
  transition: var(--transition-normal) !important;
  opacity: 0.7;
  width: 12px !important;
  height: 12px !important;
  cursor: se-resize !important;
}

.custom-text-container .component-content .textarea-resize-handle:hover {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .extraction-options-row {
    flex-wrap: wrap;
  }
  
  .extraction-option-item {
    margin-bottom: 2px; /* 减小下边距 */
  }
}

@media (max-width: 480px) {
  .extraction-option-item {
    width: 100%;
    margin-bottom: 2px; /* 减小下边距 */
  }
} 