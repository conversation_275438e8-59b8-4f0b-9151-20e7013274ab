# RunningHub集成测试说明

## 概述

本文档说明如何测试RunningHub平台与现有系统的集成功能。

## 功能特性

### 1. 数据库集成
- ✅ RunningHub配置存储到MongoDB
- ✅ 工作流映射配置管理
- ✅ 配置测试状态跟踪
- ✅ 使用统计记录

### 2. 统一工作流执行
- ✅ 智能平台选择（ComfyUI/RunningHub）
- ✅ 基于用户类型的平台推荐
- ✅ 工作流参数自动转换
- ✅ 统一的结果处理

### 3. 管理界面
- ✅ 配置CRUD操作
- ✅ 配置测试功能
- ✅ 工作流映射管理
- ✅ 任务监控

## 测试步骤

### 1. 启动服务

```bash
# 启动后端服务
cd server
npm start

# 启动前端服务
cd ..
npm start
```

### 2. 访问管理页面

1. 登录系统（需要管理员权限）
2. 进入管理后台：`http://localhost:3000/admin`
3. 点击左侧菜单中的"RunningHub管理"

### 3. 配置管理测试

#### 3.1 添加配置
1. 点击"添加配置"按钮
2. 填写配置信息：
   - 配置名称：`测试配置`
   - API密钥：`your-32-character-api-key-here`
   - 描述：`测试用配置`
   - 工作流映射：
     - A01-trending: `1850925505116598274`
     - B01-fashion: `your-fashion-workflow-id`
     - C01-extract: `your-extract-workflow-id`
   - 设为默认配置：`开启`
3. 点击确定保存

#### 3.2 测试配置
1. 在配置列表中点击"测试"按钮
2. 系统会调用RunningHub API验证配置
3. 查看测试状态和账户信息

#### 3.3 编辑配置
1. 点击"编辑"按钮
2. 修改配置信息
3. 保存更改

### 4. 工作流执行测试

#### 4.1 通过现有接口测试
```bash
# 创建任务（系统会自动选择平台）
curl -X POST http://localhost:3002/api/comfyui/execute2?workflowName=A01-trending&taskId=test-task-123 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "prompt": "fashion design",
    "subInfo": {
      "count": 1
    }
  }'
```

#### 4.2 查看平台选择
```bash
# 获取工作流平台推荐
curl -X POST http://localhost:3002/api/comfyui/platform-recommendation \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "workflowName": "A01-trending"
  }'
```

#### 4.3 查看平台状态
```bash
# 获取平台状态
curl -X GET http://localhost:3002/api/comfyui/platform-status \
  -H "Authorization: Bearer your-jwt-token"
```

### 5. 数据库验证

#### 5.1 检查配置存储
```javascript
// 在MongoDB中查询配置
db.runninghubconfigs.find().pretty()
```

#### 5.2 检查任务记录
```javascript
// 查询任务记录，验证平台信息
db.flowtasks.find({platform: "runninghub"}).pretty()
```

## 预期结果

### 1. 配置管理
- 配置能够正常保存到数据库
- 测试功能能够验证API密钥有效性
- 工作流映射配置正确存储

### 2. 平台选择
- 系统根据配置自动选择执行平台
- VIP用户优先使用RunningHub
- 工作流映射存在时使用RunningHub

### 3. 任务执行
- 任务能够在RunningHub平台上成功创建
- 任务状态正确更新
- 结果能够正确获取和处理

### 4. 数据一致性
- 配置信息正确存储
- 任务记录包含平台信息
- 使用统计正确更新

## 故障排除

### 1. 配置测试失败
- 检查API密钥是否正确（32位字符）
- 验证网络连接
- 查看服务器日志

### 2. 工作流执行失败
- 确认工作流映射配置正确
- 检查RunningHub工作流ID是否有效
- 验证用户权限

### 3. 数据库连接问题
- 确认MongoDB服务运行正常
- 检查数据库连接配置
- 验证数据库权限

## 环境变量配置

```bash
# 在server/.env文件中添加
RUNNINGHUB_ENABLED=true
RUNNINGHUB_API_KEY=your-default-api-key
DEFAULT_WORKFLOW_PLATFORM=comfyui
```

## API文档

### 配置管理API
- `GET /api/runninghub/admin/configs` - 获取配置列表
- `POST /api/runninghub/admin/configs` - 创建配置
- `PUT /api/runninghub/admin/configs/:id` - 更新配置
- `DELETE /api/runninghub/admin/configs/:id` - 删除配置
- `POST /api/runninghub/admin/configs/:id/test` - 测试配置

### 工作流执行API
- `POST /api/comfyui/execute2` - 统一工作流执行接口
- `GET /api/comfyui/platform-status` - 获取平台状态
- `POST /api/comfyui/platform-recommendation` - 获取平台推荐

## 注意事项

1. 确保RunningHub API密钥有效且有足够余额
2. 工作流ID必须在RunningHub平台上存在
3. 管理员权限才能访问配置管理功能
4. 测试时注意API调用频率限制

## 联系支持

如果在测试过程中遇到问题，请：
1. 查看服务器日志文件
2. 检查浏览器控制台错误
3. 验证数据库连接状态
4. 联系技术支持团队
