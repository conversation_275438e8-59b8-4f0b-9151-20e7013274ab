import React, { useState, useEffect, useRef, useCallback } from 'react';
import { message, Modal } from 'antd';
import { MdClose } from 'react-icons/md';
import './index.css';
import '../../styles/close-buttons.css';
import '../../styles/tabs.css';
import request from '../../api/request';
import { getCurrentUserId } from '../../api';
import VirtualModelManager from '../VirtualModelManager';
import { 
  GENDER_CATEGORIES, 
  AGE_CATEGORIES, 
  REGION_CATEGORIES, 
  BODY_TYPE_CATEGORIES 
} from '../../data/categories';

// 导入虚拟模特数量上限常量
const { MAX_MODELS_COUNT } = VirtualModelManager;

/**
 * 模特登记弹窗组件
 * 
 * @param {Object} props
 * @param {boolean} props.isOpen - 控制弹窗是否显示
 * @param {Function} props.onClose - 关闭弹窗的回调函数
 * @param {Object} props.modelData - 初始的模特数据（从生成的图片中获取）
 * @param {Function} props.onSaved - 保存成功后的回调函数
 */
const ModelRegistrationModal = ({
  isOpen,
  onClose,
  modelData = {},
  onSaved
}) => {
  console.log('ModelRegistrationModal渲染', { isOpen, modelData });

  // 拖动相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragTransform, setDragTransform] = useState({ x: 0, y: 0 });
  const modalRef = useRef(null);
  const dragStartMouse = useRef({ x: 0, y: 0 });
  const dragStartTransform = useRef({ x: 0, y: 0 });

  // 生成唯一ID
  const generateModelId = () => {
    // 使用更简短的格式：V + 当前时间的秒数（最后3位） + 2位随机数字
    const seconds = Math.floor(Date.now() / 1000) % 1000; // 获取当前秒数的最后3位
    const randomNum = Math.floor(Math.random() * 100); // 生成0-99的随机数
    return `V${seconds}${randomNum < 10 ? '0' + randomNum : randomNum}`; // 格式：V12345
  };

  // 获取当前日期和时间
  const getCurrentDateTime = () => {
    const now = new Date();
    return now.toLocaleString(); // 返回本地化的日期和时间字符串，格式如：2023/6/15 14:30:45
  };

  // 表单状态
  const [formData, setFormData] = useState({
    id: generateModelId(),
    createdAt: getCurrentDateTime(),
    name: '',
    tags: {
      gender: GENDER_CATEGORIES[0],
      age: AGE_CATEGORIES[0],
      region: REGION_CATEGORIES[0],
      bodyType: BODY_TYPE_CATEGORIES[0]
    }
  });

  // 添加错误状态
  const [errors, setErrors] = useState({
    name: ''
  });

  // 从传入的模特数据中初始化表单
  useEffect(() => {
    if (modelData && Object.keys(modelData).length > 0) {
      // 检查是否为编辑模式（是否有id字段）
      const isEditMode = modelData.id !== undefined;
      
      // 只提取需要的字段，并保留其他表单默认值
      const newFormData = {
        // 在编辑模式下保留原始ID和创建时间，否则使用新生成的
        id: isEditMode ? modelData.id : formData.id,
        createdAt: isEditMode ? modelData.createdAt : formData.createdAt,
        name: modelData.name || '',
        imageUrl: modelData.url || modelData.imageUrl,
        prompt: modelData.prompt || '',
        negative_prompt: modelData.negative_prompt || ''
      };

      // 处理标签信息
      newFormData.tags = {
        gender: modelData.gender || (modelData.tags && modelData.tags.gender) || formData.tags.gender,
        age: modelData.age || (modelData.tags && modelData.tags.age) || formData.tags.age,
        region: modelData.region || (modelData.tags && modelData.tags.region) || formData.tags.region,
        bodyType: modelData.bodyType || (modelData.tags && modelData.tags.bodyType) || formData.tags.bodyType
      };
      
      // 如果有标签数组，从中提取分类信息
      if (!isEditMode && modelData.tags && Array.isArray(modelData.tags)) {
        const extractedTags = {
          gender: formData.tags.gender,
          age: formData.tags.age,
          region: formData.tags.region,
          bodyType: formData.tags.bodyType
        };

        modelData.tags.forEach(tag => {
          if (GENDER_CATEGORIES.includes(tag)) {
            extractedTags.gender = tag;
          } else if (AGE_CATEGORIES.includes(tag)) {
            extractedTags.age = tag;
          } else if (REGION_CATEGORIES.includes(tag)) {
            extractedTags.region = tag;
          } else if (BODY_TYPE_CATEGORIES.includes(tag)) {
            extractedTags.bodyType = tag;
          }
        });

        newFormData.tags = extractedTags;
      }

      setFormData(newFormData);
    }
  }, [modelData]);

  // 处理表单输入变化
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 验证模特名称长度
    if (name === 'name') {
      if (value.length > 20) {
        setErrors(prev => ({
          ...prev,
          name: `超出字符限制，最多输入20个字符（当前${value.length}个）`
        }));
      } else {
        setErrors(prev => ({
          ...prev,
          name: ''
        }));
      }
    }
  };

  // 处理标签选择
  const handleTagSelect = (category, value) => {
    setFormData(prev => ({
      ...prev,
      tags: {
        ...prev.tags,
        [category]: value
      }
    }));
  };

  // 添加清空内容功能
  const handleClear = () => {
    // 重置表单数据，但保留模特ID和创建时间
    setFormData(prev => ({
      ...prev,
      name: '',
      tags: {
        gender: GENDER_CATEGORIES[0],
        age: AGE_CATEGORIES[0],
        region: REGION_CATEGORIES[0],
        bodyType: BODY_TYPE_CATEGORIES[0]
      }
    }));
    
    // 清空错误信息
    setErrors({
      name: ''
    });
    
    // 提示用户
    message.success('已清空内容');
  };

  // 判断是否为编辑模式
  const isEditMode = modelData && modelData.id !== undefined;

  // 拖动事件处理
  const handleModalMouseDown = (e) => {
    if (e.target.closest('.modal-header')) {
      setIsDragging(true);
      // 记录拖动起始点和当前变换位置
      dragStartMouse.current = { x: e.clientX, y: e.clientY };
      dragStartTransform.current = { ...dragTransform };
      e.preventDefault();
    }
  };

  const handleModalMouseMove = useCallback((e) => {
    if (isDragging) {
      const deltaX = e.clientX - dragStartMouse.current.x;
      const deltaY = e.clientY - dragStartMouse.current.y;
      const newX = dragStartTransform.current.x + deltaX;
      const newY = dragStartTransform.current.y + deltaY;
      setDragTransform({ x: newX, y: newY });
    }
  }, [isDragging]);

  const handleModalMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 绑定全局鼠标事件
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleModalMouseMove);
      document.addEventListener('mouseup', handleModalMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleModalMouseMove);
        document.removeEventListener('mouseup', handleModalMouseUp);
      };
    }
  }, [isDragging, handleModalMouseMove, handleModalMouseUp]);

  // 处理表单提交
  const handleSubmit = async () => {
    // 验证表单数据
    if (!formData.name?.trim()) {
      message.error('请输入模特姓名');
      return;
    }
    // 构建模特信息对象
      let saveResponse;
      try {
        saveResponse = await request.post(
          '/models/save-virtual-model',
          {...modelData,...formData}
        );
        if (saveResponse && saveResponse.success) {
          message.success(isEditMode ? '虚拟模特信息已更新' : '虚拟模特已添加');
          
          // 调用保存成功的回调函数
          if (typeof onSaved === 'function') {
            onSaved(modelData);
          }
        } else {
          throw new Error(saveResponse?.message || '保存到服务器失败');
        }
        console.log('服务器响应:', saveResponse);
      } catch (error) {
        console.error('请求失败:', error.message, error.response?.data, error.response?.status);
        throw error;
      }
    // 关闭弹窗
    onClose();
  };

  return isOpen ? (
    <div className="model-registration-wrapper" onClick={onClose}>
      <div 
        className={`modal-content model-registration-modal ${isDragging ? 'dragging' : ''}`}
        onClick={(e) => e.stopPropagation()}
        ref={modalRef}
        onMouseDown={handleModalMouseDown}
        style={{
          transform: `translate(${dragTransform.x}px, ${dragTransform.y}px)`,
          transition: isDragging ? 'none' : 'transform 0.2s ease'
        }}
      >
        <div className="modal-header">
          <div className="tab-group">
            <button className="tab-btn active">
              {isEditMode ? '编辑虚拟模特' : '虚拟模特登记'}
            </button>
          </div>
          <button className="medium-close-button" onClick={onClose}>
            <MdClose />
          </button>
        </div>
        
        <div className="modal-body registration-content">
          {/* 模特图片预览 */}
          {formData.imageUrl && (
            <div className="model-preview">
              <img src={formData.imageUrl} alt="模特预览" />
            </div>
          )}
  
          {/* 基本信息区域（包含输入框和缩略图） */}
          <div className="basic-info-container">
            <div className="form-inputs">
              {/* 模特编号（自动生成，只读） */}
              <div className="form-item">
                <label>模特编号</label>
                <div className="model-id">{formData.id}</div>
              </div>
    
              {/* 创建时间（自动生成，只读） */}
              <div className="form-item">
                <label>创建时间</label>
                <div className="model-id">{formData.createdAt}</div>
              </div>
    
              {/* 模特名称（用户输入） */}
              <div className="form-item">
                <label>模特名称</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="请输入模特名称"
                  maxLength={20}
                  className={errors.name ? 'input-error' : ''}
                />
                {errors.name && <div className="message-error">{errors.name}</div>}
              </div>
            </div>
            
            {/* 模特缩略图 */}
            {formData.imageUrl && (
              <div className="model-thumbnail-wrapper" style={{ width: '176px' }}>
                <div className="model-thumbnail" style={{ height: '234px' }}>
                  <img src={formData.imageUrl} alt="模特缩略图" />
                </div>
              </div>
            )}
          </div>
  
          {/* 标签选择区域 */}
          <div className="tags-container">
            <div className="tags-title">模特标签</div>
  
            {/* 标签选项卡区域 */}
            <div className="tab-content">
              {/* 性别标签 */}
              <div className="tags-section">
                <label>性别</label>
                <div className="tags-options">
                  {GENDER_CATEGORIES.map((gender) => (
                    <button 
                      key={gender}
                      className={`tag-option ${formData.tags.gender === gender ? 'active' : ''}`}
                      onClick={() => handleTagSelect('gender', gender)}
                    >
                      {gender}
                    </button>
                  ))}
                </div>
              </div>
  
              {/* 年龄标签 */}
              <div className="tags-section">
                <label>年龄</label>
                <div className="tags-options">
                  {AGE_CATEGORIES.map((age) => (
                    <button 
                      key={age}
                      className={`tag-option ${formData.tags.age === age ? 'active' : ''}`}
                      onClick={() => handleTagSelect('age', age)}
                    >
                      {age}
                    </button>
                  ))}
                </div>
              </div>
  
              {/* 地区标签 */}
              <div className="tags-section">
                <label>地区</label>
                <div className="tags-options">
                  {REGION_CATEGORIES.map((region) => (
                    <button 
                      key={region}
                      className={`tag-option ${formData.tags.region === region ? 'active' : ''}`}
                      onClick={() => handleTagSelect('region', region)}
                    >
                      {region}
                    </button>
                  ))}
                </div>
              </div>
  
              {/* 身材标签 */}
              <div className="tags-section">
                <label>身材</label>
                <div className="tags-options">
                  {BODY_TYPE_CATEGORIES.map((bodyType) => (
                    <button 
                      key={bodyType}
                      className={`tag-option ${formData.tags.bodyType === bodyType ? 'active' : ''}`}
                      onClick={() => handleTagSelect('bodyType', bodyType)}
                    >
                      {bodyType}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="modal-footer">
          <button className="clear-btn" onClick={handleClear}>
            清空内容
          </button>
          <button className="save-settings-btn" onClick={handleSubmit}>
            {isEditMode ? '保存更改' : '登记模特'}
          </button>
        </div>
      </div>
    </div>
  ) : null;
};

export default ModelRegistrationModal; 