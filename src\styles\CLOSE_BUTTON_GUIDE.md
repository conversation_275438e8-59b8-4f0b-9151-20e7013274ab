# 关闭按钮样式更新指南

本文档提供了如何在项目中统一更新弹窗关闭按钮样式的指导。

## 背景

我们需要统一弹窗的关闭按钮样式，使用三种标准样式：
1. **大型关闭按钮**：适用于中大型弹窗，样式参考自图片详情弹窗
2. **中型关闭按钮**：专用于订阅弹窗和登录注册弹窗
3. **小型关闭按钮**：适用于小型弹窗

三种按钮样式保持一致，仅尺寸不同：
- 大型按钮：32px × 32px
- 中型按钮：28px × 28px
- 小型按钮：24px × 24px

## 更新步骤

### 步骤 1: 导入关闭按钮样式

在每个弹窗组件的顶部导入关闭按钮样式：

```jsx
import '../../styles/close-buttons.css';
```

### 步骤 2: 更新关闭按钮类名

#### 对于中大型弹窗（需要使用大型关闭按钮）:

以下组件需要使用 `large-close-button` 类：
- 图片上传指导弹窗 (UploadGuideModal)
- 模特选择弹窗 (ModelSelectModal)
- 场景选择弹窗 (SceneSelectModal)
- 高级自定义弹窗 (AdvancedCustomModal)

示例代码：
```jsx
<button 
  className="large-close-button"
  onClick={onClose}
>
  <MdClose />
</button>
```

#### 对于订阅和认证弹窗（需要使用中型关闭按钮）:

以下组件需要使用 `medium-close-button` 类：
- 订阅弹窗
- 登录注册弹窗

示例代码：
```jsx
<button 
  className="medium-close-button"
  onClick={onClose}
>
  <MdClose />
</button>
```

#### 对于小型弹窗（需要使用小型关闭按钮）:

以下组件需要使用 `small-close-button` 类：
- 图片信息弹窗 (ImageInfoModal)
- 文本弹窗 (TextPopup)

示例代码：
```jsx
<button 
  className="small-close-button"
  onClick={onClose}
>
  <MdClose />
</button>
```

### 步骤 3: 移除组件特有的关闭按钮样式

检查每个组件的CSS文件，移除特定组件对关闭按钮的样式定义，以避免样式冲突。

## 示例

以上传指导弹窗为例：

1. 导入关闭按钮样式：
```jsx
import '../../styles/close-buttons.css';
```

2. 更新关闭按钮类名：
```jsx
<button 
  className="large-close-button"
  onClick={() => {
    resetState();
    onClose();
  }}
>
  <MdClose />
</button>
```

3. 移除组件CSS文件中的关闭按钮样式。

## 注意事项

- 确保移除每个组件CSS文件中对关闭按钮的特定样式定义
- 保持关闭按钮的功能逻辑不变，只更新样式
- 确保每个组件都导入了`close-buttons.css` 