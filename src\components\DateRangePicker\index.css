/* 日期选择器基础样式 */
.account-unique-range-picker.ant-picker.ant-picker-range {
  height: 28px !important;
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-sm) !important;
  color: var(--text-primary) !important;
  font-size: var(--font-size-sm) !important;
  transition: var(--transition-normal) !important;
  box-shadow: none !important;
}

/* 日期选择器活动指示条 - 使用品牌色 */
.account-unique-range-picker.ant-picker.ant-picker-range .ant-picker-active-bar {
  background: var(--brand-primary) !important;
  background-color: var(--brand-primary) !important;
  height: 2px !important;
  bottom: 0 !important;
  transition: var(--transition-normal) !important;
}

/* 确保在不同状态下都使用品牌色 */
.account-unique-range-picker.ant-picker-focused .ant-picker-active-bar,
.account-unique-range-picker.ant-picker-focused:hover .ant-picker-active-bar {
  background: var(--brand-primary) !important;
  background-color: var(--brand-primary) !important;
  height: 2px !important;
  bottom: 0 !important;
}

/* 悬停状态 */
.account-unique-range-picker:hover {
  border-color: var(--brand-primary) !important;
}

/* 聚焦状态 */
.account-unique-range-picker.ant-picker-focused {
  border-color: var(--brand-primary) !important;
  box-shadow: none !important;
}

/* 输入框样式 */
.account-unique-range-picker .ant-picker-input > input {
  color: var(--text-primary) !important;
  font-size: var(--font-size-sm) !important;
}

.account-unique-range-picker .ant-picker-input > input::placeholder {
  color: var(--text-secondary) !important;
}

/* 清除按钮样式 */
.account-unique-range-picker .ant-picker-clear {
  color: var(--text-secondary) !important;
  background: var(--bg-primary) !important;
}

.account-unique-range-picker .ant-picker-clear:hover {
  color: var(--text-primary) !important;
}

/* 分隔符样式 - 使用算力明细页面中的完善样式 */
.account-unique-range-picker .ant-picker-separator {
  color: var(--text-secondary) !important;
  display: inline-flex !important;
  align-items: center !important;
  margin: 0 16px 0 0 !important;
  font-size: 16px !important;
}

/* 图标样式 */
.account-unique-range-picker .ant-picker-suffix {
  color: var(--text-secondary) !important;
}

.account-unique-range-picker:hover .ant-picker-suffix {
  color: var(--brand-primary) !important;
}

/* 日期选择面板翻页箭头样式 */
.account-date-dropdown .ant-picker-header-super-prev-btn,
.account-date-dropdown .ant-picker-header-super-next-btn,
.account-date-dropdown .ant-picker-header-prev-btn,
.account-date-dropdown .ant-picker-header-next-btn {
  color: var(--text-secondary) !important;
  transition: var(--transition-normal) !important;
}

.account-date-dropdown .ant-picker-header-super-prev-btn:hover,
.account-date-dropdown .ant-picker-header-super-next-btn:hover,
.account-date-dropdown .ant-picker-header-prev-btn:hover,
.account-date-dropdown .ant-picker-header-next-btn:hover {
  color: var(--brand-primary) !important;
}

/* 确保箭头图标样式不被覆盖 */
.account-date-dropdown .ant-picker-header-super-prev-btn .anticon,
.account-date-dropdown .ant-picker-header-super-next-btn .anticon,
.account-date-dropdown .ant-picker-header-prev-btn .anticon,
.account-date-dropdown .ant-picker-header-next-btn .anticon {
  color: inherit !important;
}

/* 当日期选择器有值时，隐藏日历图标，显示清除图标 */
.account-unique-range-picker.ant-picker-range:not(.ant-picker-range-empty) .ant-picker-suffix {
  display: none !important;
}

.account-unique-range-picker.ant-picker-range:not(.ant-picker-range-empty) .ant-picker-clear {
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
}

/* 当日期选择器为空时，隐藏清除图标，显示日历图标 */
.account-unique-range-picker.ant-picker-range.ant-picker-range-empty .ant-picker-clear {
  display: none !important;
}

.account-unique-range-picker.ant-picker-range.ant-picker-range-empty .ant-picker-suffix {
  display: inline-block !important;
} 

/* 移动端日期输入框样式 */
.mobile-date-range-picker {
  display: flex;
  align-items: center;
  width: 100%;
}

.mobile-date-input {
  transition: border-color 0.3s ease;
}

.mobile-date-input:focus {
  border-color: var(--brand-primary) !important;
  outline: none;
}

.mobile-date-input::placeholder {
  color: var(--text-tertiary);
  font-size: 13px;
}

@media (max-width: 480px) {
  .mobile-date-input {
    font-size: 13px !important;
    padding: 3px 6px !important;
  }
  
  .mobile-date-input::placeholder {
    font-size: 12px;
  }
}

@media (max-width: 360px) {
  .mobile-date-input {
    font-size: 12px !important;
    padding: 2px 4px !important;
  }
  
  .mobile-date-input::placeholder {
    font-size: 11px;
  }
} 