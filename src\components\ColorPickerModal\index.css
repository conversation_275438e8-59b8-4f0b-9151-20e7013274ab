/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/buttons.css';
@import '../../styles/tabs.css';
@import '../../styles/close-buttons.css';

/* 新的包装器样式，不再使用遮罩层 */
.color-picker-wrapper {
  position: fixed;
  top: 10px;
  left: 0;
  width: 100%;
  height: 100%;
  /* 使用dvh确保在移动端正确显示 */
  height: 100dvh;
  z-index: 99999; /* 大幅提高z-index，确保在所有内容之上，包括移动端导航栏 */
  pointer-events: auto; /* 改为auto，使背景区域可点击 */
}

/* 颜色选择器模态框样式 */
.color-picker-modal {
  position: fixed; /* 使用固定定位 */
  width: 500px;
  max-width: 90vw;
  max-height: 90vh;
  background: var(--bg-primary);
  pointer-events: auto; /* 可点击 */
  box-shadow: var(--shadow-md);
  border-radius: 12px;
  user-select: none; /* 防止拖动时选中文本 */
  z-index: 100000; /* 确保弹窗内容在包装器之上 */
  /* 位置由传入的style决定，不在这里设置默认位置 */
}

/* 拖动状态下的样式 */
.color-picker-modal.dragging {
  user-select: none;
  pointer-events: auto;
}

/* 修复弹窗拖动时手型光标 */
.color-picker-modal.dragging .modal-header {
  cursor: grabbing !important;
}

/* 标题区域可拖动的提示 */
.color-picker-modal .modal-header:hover {
  background: transparent;
}

[data-theme="dark"] .color-picker-modal .modal-header:hover {
  background: transparent;
}

/* 拖动时禁止全局文本选择 */
body.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 暗色主题下添加白边 */
[data-theme="dark"] .color-picker-modal {
  background: var(--bg-primary);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
}

/* 删除无效的遮罩层样式 */
.color-picker-modal :global(.modal-overlay) {
  display: none;
}

/* 修改modal-body样式，确保不会被tab遮挡 */
.color-picker-modal .modal-body {
  padding-top: var(--spacing-md);
  margin-top: 0;
}

/* 颜色选择器模态框的标签页样式覆盖 */
.color-picker-modal .modal-tabs {
  width: auto !important;
  margin-left: 20px !important;
  flex-shrink: 0;
}

.color-picker-modal .tab-group {
  width: auto !important;
  margin-left: 20px !important;
  flex-shrink: 0;
}

.color-picker-modal .tab-btn {
  flex-shrink: 0;
  min-width: auto;
}

/* 模态框内容样式调整 */
.color-picker-content {
  padding: 0 var(--spacing-sx);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.color-preview {
  margin-bottom: var(--spacing-xs);
}

.color-preview-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
}

.selected-color-preview {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  flex-shrink: 0;
}

/* 颜色模式选择器样式 */
.color-mode-container {
  position: relative;
  width: 80px;
  flex-shrink: 0;
}

.color-mode-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.color-mode-selector:hover {
  border-color: #FF3C6A;
}

.color-mode-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  margin-top: 4px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.color-mode-option {
  padding: 8px 10px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.color-mode-option:hover {
  background-color: #f5f5f5;
  color: #FF3C6A;
}

.color-mode-option.active {
  background-color: #fff1f3;
  color: #FF3C6A;
}

.color-value-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  flex-grow: 1;
}

.hex-input-wrapper {
  display: flex;
  width: 100%;
  position: relative;
  align-items: center;
  gap: var(--spacing-sm);
}

.hex-input {
  width: calc(100% - 46px);
  padding: var(--spacing-xxs) var(--spacing-xs);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-family: sans-serif;
  text-transform: uppercase;
  transition: var(--transition-normal);
  background-color: var(--bg-primary);
}

.hex-input:hover {
  border-color: var(--brand-primary);
}

.hex-input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(255, 60, 106, 0.1);
}

[data-theme="dark"] .hex-input {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .hex-input:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

/* 复制按钮样式 */
.copy-color-btn {
  width: 24px;
  height: 24px;
  min-width: 24px;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: none;
  padding: 0;
  flex-shrink: 0;
}

.copy-color-btn:hover {
  color: var(--brand-primary);
}

.copy-color-btn svg {
  width: 16px;
  height: 16px;
}

/* RGB 和 HSB 输入框容器 */
.color-inputs-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-xxs);
  width: 100%;
}

/* RGB 和 HSB 输入框样式 */
.color-inputs-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  width: calc(100% - 30px);
}

.number-input-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  flex-grow: 1;
}

.input-label {
  position: absolute !important;
  left: 8px !important;  /* 使用更小的值确保靠左 */
  top: 50% !important;
  transform: translateY(-45%) !important; /* 稍微向下偏移一点，以实现更好的视觉居中 */
  font-size: 14px !important;
  color: #000000 !important;
  pointer-events: none !important;
  text-align: left !important;
  font-weight: normal !important;
  z-index: 5 !important;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  background: transparent !important;
}

/* 为暗色主题添加特殊规则 */
[data-theme="dark"] .input-label {
  color: #ffffff !important; /* 暗色主题下使用白色 */
}

/* 使用非常具体的选择器确保样式优先级高于全局样式 */
.color-picker-modal .color-picker-content .color-value-container .color-inputs-container .color-inputs-row .number-input-wrapper .input-label {
  position: absolute !important;
  left: 8px !important;
  top: 50% !important;
  transform: translateY(-45%) !important; /* 调整Y轴偏移量，使标签看起来垂直居中 */
  font-size: 14px !important;
  color: #000000 !important;
  pointer-events: none !important;
  text-align: left !important;
  font-weight: normal !important; /* 改为normal，去掉粗体 */
  z-index: 5 !important;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  background: transparent !important;
  width: auto !important;
}

/* 为暗色主题添加特殊规则 */
[data-theme="dark"] .color-picker-modal .color-picker-content .color-value-container .color-inputs-container .color-inputs-row .number-input-wrapper .input-label {
  color: #ffffff !important;
}

.number-input {
  width: calc(100% - 6px) !important;
  padding: var(--spacing-xxs) var(--spacing-xs) var(--spacing-xxs) var(--spacing-lg) !important;
  padding-left: 32px !important; /* 减小左侧内边距 */
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-sm) !important;
  font-size: var(--font-size-sm) !important;
  color: var(--text-primary) !important;
  font-family: sans-serif !important;
  text-align: left !important;
  transition: var(--transition-normal) !important;
  background-color: var(--bg-primary) !important;
}

.number-input:hover {
  border-color: var(--brand-primary) !important;
}

.number-input:focus {
  outline: none !important;
  border-color: var(--brand-primary) !important;
  box-shadow: 0 0 0 2px rgba(255, 60, 106, 0.1) !important;
}

[data-theme="dark"] .number-input {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .number-input:focus {
  border-color: var(--brand-primary) !important;
  box-shadow: 0 0 0 2px var(--brand-primary-light) !important;
}

.color-input-container {
  width: 100%;
}

.custom-color-picker {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.react-colorful {
  width: 100% !important;
  height: 200px !important;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

.react-colorful__saturation {
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.react-colorful__hue {
  height: 24px !important;
  margin-top: var(--spacing-xs);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
}

.react-colorful__saturation-pointer,
.react-colorful__hue-pointer {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.color-input-label {
  display: none;
}

.preset-colors-container {
  margin-top: var(--spacing-sm);
}

.preset-colors-title {
  display: none;
}

.preset-colors {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  justify-content: flex-start;
}

.preset-color-btn {
  width: 28px;
  height: 28px;
  border: 1px solid var(--border-lighter);
  border-radius: var(--radius-md);
  cursor: pointer;
  position: relative;
  transition: none;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  margin: 2px;
}

.preset-color-btn:hover {
  /* 使用双层边框效果：外圈为主体色，内圈为白色 */
  border: 2px solid white;
  box-shadow: 0 0 0 2px var(--brand-primary);
}

.preset-color-btn.active {
  /* 选中状态也使用相同的双层边框样式 */
  border: 2px solid white;
  box-shadow: 0 0 0 2px var(--brand-primary);
  transform: none;
}

.check-icon {
  color: white;
  position: absolute;
  font-size: 18px;
}

/* 白色背景上的勾选图标 */
.preset-color-btn[style*="background-color: #FFFFFF"] .check-icon,
.preset-color-btn[style*="background-color: #FFF"] .check-icon,
.preset-color-btn[style*="background-color: rgb(255, 255, 255)"] .check-icon {
  color: #333;
}

/* 添加吸管按钮样式 - 只保留图标 */
.eyedropper-btn {
  width: 24px;
  height: 24px;
  min-width: 24px;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: color 0.2s ease;
  padding: 0;
  flex-shrink: 0;
}

.eyedropper-btn:hover {
  color: var(--brand-primary);
}

.eyedropper-btn svg {
  width: 20px;
  height: 20px;
}

/* 媒体查询适配移动设备 */
@media (max-width: 768px) {
  .color-picker-modal {
    width: 95%;
    max-width: none;
    max-height: calc(100vh - 40px);
    max-height: calc(100dvh - 40px);
  }
    .color-picker-content {
    padding: 0 0px;
  }
  .color-value-container {
    flex-direction: row !important;
  }
  .color-preview-row {
    flex-wrap: nowrap !important;
  }
  .color-inputs-row {
    flex-wrap: nowrap !important;
    gap: 2px;
  }
  .eyedropper-btn {
    display: none !important;
  }
}

@media (max-width: 576px) {
  .color-picker-content {
    padding: 0 0px; 
    gap: var(--spacing-sm);
  }
  .color-value-container {
    flex-direction: row !important;
  }
  .color-preview-row {
    flex-wrap: nowrap !important;
  }
  .color-inputs-row {
    flex-wrap: nowrap !important;
    gap: 1px;
  }

  .selected-color-preview {
    width: 40px;
    height: 40px;
  }

  .color-mode-container {
    width: 70px;
  }

  .color-mode-selector {
    padding: 4px 8px;
    font-size: 13px;
  }

  .hex-input {
    font-size: var(--font-size-xs);
  }

  .react-colorful {
    height: 160px !important;
  }

  .preset-colors {
    gap: var(--spacing-xxs);
  }

  .preset-color-btn {
    width: 30px;
    height: 30px;
  }

  /* 移动端隐藏吸管工具 */
  .eyedropper-btn {
    display: none !important;
  }
}

.modal-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-md);
  box-shadow: none; /* 移除阴影 */
}

[data-theme="dark"] .preset-color-btn:hover {
  /* 暗黑主题下使用与背景色相同的内边框 */
  border-color: var(--bg-primary);
}

.preset-color-btn.active {
  /* 选中状态也使用相同的双层边框样式 */
  border: 2px solid white;
  box-shadow: 0 0 0 2px var(--brand-primary);
  transform: none;
}

[data-theme="dark"] .preset-color-btn.active {
  /* 暗黑主题下使用与背景色相同的内边框 */
  border-color: var(--bg-primary);
}

/* 收藏按钮样式 */
.favorite-color-btn {
  width: 24px;
  height: 24px;
  min-width: 24px;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: color 0.2s ease;
  padding: 0;
  flex-shrink: 0;
}

.favorite-color-btn:hover {
  color: var(--brand-primary);
}

.favorite-color-btn svg {
  width: 20px;
  height: 20px;
}

/* 已收藏状态样式 */
.favorite-color-btn:has(svg[data-icon="MdStar"]) {
  color: var(--brand-primary);
}

/* 兼容性写法，某些浏览器不支持:has选择器 */
.favorite-color-btn.active {
  color: var(--brand-primary);
}

/* 预设颜色区域标题样式 */
.preset-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-right: 4px;
  font-weight: 500;
  display: flex;
  align-items: center;
  height: 28px;
  /* 使标题占据与一个色块相同的宽度，确保后续色块对齐 */
  min-width: 28px;
  padding-left: 2px;
}

/* 收藏颜色区域样式 */
.favorite-colors-container {
  margin-top: var(--spacing-sm);
}

.favorite-colors {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  justify-content: flex-start;
}

.favorite-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-right: 4px;
  font-weight: 500;
  display: flex;
  align-items: center;
  height: 28px;
  /* 使标题占据与一个色块相同的宽度，确保后续色块对齐 */
  min-width: 28px;
  padding-left: 2px;
}

.no-favorites-message {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  padding: var(--spacing-xs) 0;
  font-style: italic;
}

/* 媒体查询适配移动设备中添加收藏区域的响应式样式 */
@media (max-width: 576px) {
  .favorite-colors {
    gap: var(--spacing-xxs);
  }
}

/* 收藏色块容器样式 */
.favorite-color-item {
  position: relative;
  display: inline-block;
}

/* 删除按钮样式 */
.delete-favorite-btn {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  display: none;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  z-index: 10;
}

.delete-favorite-btn:hover {
  color: var(--brand-primary);
  border-color: var(--brand-primary);
  transform: scale(1.1);
}

.favorite-color-item:hover .delete-favorite-btn {
  display: flex;
}

[data-theme="dark"] .delete-favorite-btn {
  background: var(--bg-secondary);
  border-color: var(--border-light);
}

[data-theme="dark"] .delete-favorite-btn:hover {
  border-color: var(--brand-primary);
}

/* 历史颜色区域样式 */
.history-colors-container {
  margin-top: var(--spacing-sm);
}

.history-colors {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  justify-content: flex-start;
}

.history-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-right: 4px;
  font-weight: 500;
  display: flex;
  align-items: center;
  height: 28px;
  /* 使标题占据与一个色块相同的宽度，确保后续色块对齐 */
  min-width: 28px;
  padding-left: 2px;
}

.no-history-message {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  padding: var(--spacing-xs) 0;
  font-style: italic;
}

/* 媒体查询适配移动设备中添加历史区域的响应式样式 */
@media (max-width: 576px) {
  .history-colors {
    gap: var(--spacing-xxs);
  }
}

/* 数值调节按钮位置样式 */
.number-controls {
  position: absolute;
  right: var(--spacing-xs);
  top: 50%;
  transform: translateY(-50%);
}

/* 添加移动端适配样式 */
@media (max-width: 480px) {
  .color-picker-modal {
    width: 98%;
    max-height: calc(100vh - 20px);
    max-height: calc(100dvh - 20px);
  }
  .color-picker-content {
    padding: 0 0px;
  }
  .color-value-container {
    flex-direction: row !important;
  }
  .color-preview-row {
    flex-wrap: nowrap !important;
  }
  .color-inputs-row {
    flex-wrap: nowrap !important;
    gap: 0px;
  }
}

@media (max-width: 360px) {
  .color-picker-modal {
    width: 100%;
    max-height: 100vh;
    max-height: 100dvh;
    border-radius: 0;
  }
  .color-picker-content {
    padding: 0 0px;
  }
  .color-value-container {
    flex-direction: row !important;
  }
  .color-preview-row {
    flex-wrap: nowrap !important;
  }
  .color-inputs-row {
    flex-wrap: nowrap !important;
    gap: 0px;
  }
}

/* 真实移动设备适配 */
@media (hover: none) and (pointer: coarse) {
  .color-picker-wrapper {
    /* 使用动态视口单位避免被地址栏等遮挡 */
    height: 100dvh !important;
    padding: env(safe-area-inset-top, 0) env(safe-area-inset-right, 0) env(safe-area-inset-bottom, 0) env(safe-area-inset-left, 0);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .color-picker-modal {
    width: 95% !important;
    max-width: 500px !important;
    max-height: calc(100dvh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px) - 60px) !important;
    /* 确保水平垂直居中 */
    position: relative !important;
    left: auto !important;
    top: auto !important;
    transform: none !important;
    margin: 30px 0 !important;
  }

  /* 移动端标签页样式优化 */
  .color-picker-modal .modal-tabs {
    width: auto !important;
    margin-left: 20px !important;
    flex-shrink: 0;
  }

  .color-picker-modal .tab-group {
    width: auto !important;
    margin-left: 20px !important;
    flex-shrink: 0;
  }

  .color-picker-modal .tab-btn {
    padding: 0 16px !important;
    font-size: 12px !important;
    height: 32px !important;
    flex-shrink: 0;
    min-width: auto;
  }

  .color-picker-modal .tab-btn.active::after {
    width: calc(100% - 32px) !important;
    margin: 0 16px !important;
  }

  /* 移动端隐藏吸管工具 */
  .eyedropper-btn {
    display: none !important;
  }
  .color-value-container {
    flex-direction: row !important;
  }
  .color-preview-row {
    flex-wrap: nowrap !important;
  }
  .color-inputs-row {
    flex-wrap: nowrap !important;
    gap: 1px;
  }
}

/* 横屏模式适配 */
@media (hover: none) and (pointer: coarse) and (orientation: landscape) {
  .color-picker-modal {
    max-height: calc(100dvh - 40px) !important;
    margin: 20px 0 !important;
  }
} 

@media (max-width: 768px), (max-width: 576px), (max-width: 480px), (max-width: 360px), (hover: none) and (pointer: coarse) {
  .number-controls,
  .number-control-btn {
    display: none !important;
  }
} 