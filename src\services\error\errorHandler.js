import logger from '../../utils/logger';

/**
 * 全局错误处理函数
 * @param {Error} error - 错误对象
 * @param {string} context - 错误发生的上下文
 * @param {Object} additionalInfo - 附加信息
 */
export const handleError = (error, context = '未知', additionalInfo = {}) => {
  // 使用logger替代console.error
  logger.error(`[${context}] 错误`, { 
    message: error.message, 
    stack: error.stack,
    ...additionalInfo
  });

  // 可以在这里添加错误上报逻辑
  // reportErrorToService(error, context, additionalInfo);
  
  // 返回用户友好的错误信息
  return {
    success: false,
    message: getErrorMessage(error, context)
  };
};

/**
 * 获取用户友好的错误信息
 * @param {Error} error - 错误对象
 * @param {string} context - 错误发生的上下文
 * @returns {string} 用户友好的错误信息
 */
const getErrorMessage = (error, context) => {
  // 根据错误类型和上下文返回适当的错误信息
  if (error.response) {
    // 服务器响应错误
    const status = error.response.status;
    
    if (status === 401) {
      return '登录已过期，请重新登录';
    } else if (status === 403) {
      return '您没有权限执行此操作';
    } else if (status === 404) {
      return '请求的资源不存在';
    } else if (status === 429) {
      return '请求过于频繁，请稍后再试';
    } else if (status >= 500) {
      return '服务器错误，请稍后再试';
    }
    
    // 使用服务器返回的错误信息
    return error.response.data?.message || '请求失败';
  } else if (error.request) {
    // 请求发送但没有收到响应
    if (error.code === 'ECONNABORTED') {
      return '请求超时，请检查您的网络连接或稍后再试';
    } else if (error.code === 'ERR_NETWORK') {
      return '网络连接失败，请检查您的网络是否正常连接';
    } else if (error.code === 'ERR_CONNECTION_REFUSED') {
      return '服务器拒绝连接，请确认服务器是否正常运行';
    } else if (error.code === 'ERR_NAME_NOT_RESOLVED') {
      return '域名解析失败，请检查您的网络DNS设置';
    } else if (navigator.onLine === false) {
      return '您当前处于离线状态，请连接网络后重试';
    }
    
    // 根据上下文提供更具体的错误信息
    switch (context) {
      case '登录':
        return '登录服务暂时不可用，请稍后再试或联系客服';
      case '注册':
        return '注册服务暂时不可用，请稍后再试或联系客服';
      case '上传':
        return '上传失败，请检查您的网络连接或文件大小是否超出限制';
      case '下载':
        return '下载失败，请检查您的网络连接或存储空间是否充足';
      case '生成':
        return '生成请求失败，服务器可能正忙，请稍后再试';
      default:
        return '网络连接失败，请检查您的网络连接或稍后再试';
    }
  } else {
    // 请求设置错误或其他错误
    if (error.name === 'AbortError') {
      return '请求已被取消';
    } else if (error.name === 'TypeError' && error.message.includes('JSON')) {
      return '服务器返回的数据格式错误，请联系客服';
    } else if (error.message && error.message.includes('timeout')) {
      return '请求超时，请检查您的网络连接速度';
    }
    
    // 根据上下文提供更具体的错误信息
    switch (context) {
      case '登录':
        return '登录失败，请检查您的账号和密码';
      case '注册':
        return '注册失败，请检查您的注册信息';
      case '上传':
        return '上传失败，请检查文件格式是否正确';
      case '下载':
        return '下载失败，请稍后再试';
      case '生成':
        return '生成失败，请检查您的参数设置';
      default:
        return '操作失败，请稍后再试';
    }
  }
};

export default handleError; 