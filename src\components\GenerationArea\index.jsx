import React, { useState, useMemo, useRef, forwardRef, useImperativeHandle, useEffect } from 'react';
import ComfyUITaskTracker, { globalWebSocketManager } from '../../utils/comfyUITaskTracker';
import { getFlowTaskDetailByTid } from '../../api/flowtask';
import './index.css';
import TaskPanel from '../TaskPanel';
import Showcase from '../Showcase';
import Pagination from '../Pagination';
import VirtualModelManager from '../VirtualModelManager';
import { showDeleteConfirmModal } from '../../utils/modalUtils';
import { message, Input, Button } from 'antd';
import { SearchOutlined, ReloadOutlined, CloseOutlined } from '@ant-design/icons';
import DateRangePicker from '../DateRangePicker';
import { MdClose } from 'react-icons/md';
import { useTaskContext } from '../../contexts/TaskContext';

// 导入TaskTextPanel组件
import TaskTextPanel from '../TaskTextPanel';
import {deleteFlowTask, getFlowTasks} from "../../api/flowtask";
// 导入ImageDetailsModal组件
import ImageDetailsModal from '../ImageDetailsModal';
// 导入ImagePreviewModal组件
import ImagePreviewModal from '../common/ImagePreviewModal';

// 页面类型到展示标签的映射
const pageTypeToTagMap = {
  'fashion': 'fashion',
  'try-on': 'tryon',
  'recolor': 'recolor',
  'fabric': 'fabric',
  'background': 'background',
  'virtual': 'virtual',
  'trending': 'trending',
  'divergent': 'divergent',
  'optimize': 'optimize',
  'inspiration': 'inspiration',
  'upscale': 'upscale',
  'matting': 'matting',
  'extend': 'extend',
  'inpaint': 'inpaint',
  'change-model': 'change-model', 
  'change-posture': 'change-posture',
  'detail-migration': 'detail-migration', 
  'hand-fix': 'hand-fix',
  'drawing': 'drawing'
  // 如果有其他页面类型和标签的映射，可以在这里添加
};

// 获取页面演示动图的URL
const getDemoGifUrl = (pageType) => {
  const baseUrl = 'https://file.aibikini.cn/config/demo';
  return `${baseUrl}/${pageType}/01.gif`;
};

// 获取页面说明文字
const getPageDescription = (pageType) => {
  return '生成的内容将在这里显示';
};

// 使用全局WebSocket管理器，不再创建本地实例
// const tracker = new ComfyUITaskTracker();

// 同步串行轮询控制
const pollingTaskSet = new Set();

function pollTaskDetailSync(taskId, getDetailFn, onFinish) {
  if (pollingTaskSet.has(taskId)) {
    // 已经在轮询中，直接返回
    return;
  }
  pollingTaskSet.add(taskId);

  let retryCount = 0;
  const maxRetries = 40;
  const retryInterval = 6000; // 6秒

  const poll = () => {
    getDetailFn(taskId)
      .then(res => {
        if (res.status !== 'processing') {
          onFinish(res);
          pollingTaskSet.delete(taskId);
        } else if (retryCount < maxRetries) {
          retryCount++;
          setTimeout(() => poll(), retryInterval); // 必须等上一次返回后再setTimeout
        } else {
          pollingTaskSet.delete(taskId);
        }
      })
      .catch((error) => {
        console.error(`轮询任务 ${taskId} 失败 (重试 ${retryCount}/${maxRetries}):`, error);

        if (retryCount < maxRetries) {
          retryCount++;
          setTimeout(() => poll(), retryInterval);
        } else {
          console.error(`任务 ${taskId} 轮询达到最大重试次数，停止轮询`);
          pollingTaskSet.delete(taskId);

          // 调用onFinish回调，传递失败状态
          if (onFinish && typeof onFinish === 'function') {
            onFinish({
              taskId: taskId,
              status: 'failed',
              errorMessage: `轮询失败: ${error.message || '网络错误'}`
            });
          }
        }
      });
  };
  poll();
}

const completedTasksRef = React.createRef();

const GenerationArea = forwardRef(({
  activeTab,
  onTabChange,
  onEditTask,
  setIsProcessing,
  onDownloadImage,
  onViewDetails,
  onBatchDownload,
  pageType,
  onAddAsExclusiveModel,
  onEditModel,
  onModelSaved,
  disableBatchDownload,
  generationTasks
}, ref) => {
  // 使用TaskContext
  const { updateTask } = useTaskContext();
  
  // 检测是否为移动端
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);
  
  // 添加分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5; // 每页显示6条任务（已从10条减少到6条以节省网络资源）
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [tasks, setTasks] = useState([]);
  // 添加ref用于访问VirtualModelManager的方法
  const virtualModelManagerRef = useRef(null);
  const moreMenuRef = useRef(null);

  // 添加搜索相关状态
  const [filters, setFilters] = useState({
    dateRange: null,
    taskId: null
  });

  // 添加图片预览相关状态
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState('');
  const [previewImageAlt, setPreviewImageAlt] = useState('');
  const [previewFeatureName, setPreviewFeatureName] = useState('');

  // 获取任务列表
  const fetchTasks = async () => {
    try {
      console.log('执行筛选，filters:', filters);
      const tasksRes = await getFlowTasks(pageType, filters);
      if(tasksRes) {
        onTabChange('result', tasksRes);
        console.log('筛选后获取tasks:', tasksRes);
        setTasks(tasksRes);
        setCurrentPage(1); // 重置到第一页
      }
    } catch(err) {
      console.error('获取任务列表失败:', err);
    }
  };

  const initTasks = async () => {
    try {
      const tasksRes = await getFlowTasks(pageType);
      if(tasksRes) {
        onTabChange('result', tasksRes);
        console.log('初始化tasks:', tasksRes);
        setTasks(tasksRes);
      }
    } catch(err) {
      console.error('获取任务列表失败:', err);
    }
  };

  // 处理筛选变化
  const handleFilterChange = () => {
    fetchTasks();
  };

  // 初始化获取任务列表
  useEffect(() => {
    if(pageType) {
      initTasks();
    }
  }, [pageType]);
  
  // 彻底移除测试数据相关逻辑，只保留正式任务流
  const displayTasks = tasks;

  // 过滤当前页面的任务（现在只用于前端分页）
  const filteredCurrentTasks = useMemo(() => {
    if (!displayTasks?.length) return [];
    return displayTasks.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  }, [displayTasks, currentPage, pageSize]);

  // 当页面类型为extract且当前标签为showcase时，自动切换到result标签
  useEffect(() => {
    if (pageType === 'extract' && activeTab === 'showcase') {
      onTabChange('result');
    }
  }, [pageType, activeTab, onTabChange]);

  // 点击外部关闭更多菜单
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (moreMenuRef.current && !moreMenuRef.current.contains(e.target)) {
        setShowMoreMenu(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 向父组件暴露方法
  useImperativeHandle(ref, () => ({
    // 提供刷新虚拟模特列表的方法
    refreshModelManager: () => {
      if (virtualModelManagerRef.current) {
        virtualModelManagerRef.current.refresh();
      }
    },
    setGenerationTasks: (task) => {
      // 更新本地状态
      setTasks(prevTasks => {
        const index = prevTasks.findIndex(t => t.taskId === task.taskId);
        console.log('更新tasks:', prevTasks, task, index);
        return index !== -1
          ? [...prevTasks.slice(0, index), task, ...prevTasks.slice(index + 1)]
          : [task, ...prevTasks];
      });
      
      // 同时通知TaskContext更新任务状态，确保提示音能够播放
      if (task && task.taskId) {
        updateTask(task);
      }
    },
    // 获取虚拟模特管理器引用
    getVirtualModelManager: () => virtualModelManagerRef.current
  }));

  const onDeleteTask = (taskId, skipConfirm = false) => {
    deleteFlowTask(taskId, skipConfirm);
    setTasks(prevTasks => prevTasks.filter(task => task.taskId !== taskId));
  }
  // 处理页码变化
  const handlePageChange = (page) => {
    setCurrentPage(page);
    // 切换页面时滚动到顶部
    const taskList = document.querySelector('.task-list');
    if (taskList) {
      taskList.scrollTop = 0;
    }
  };
  
  // 定义一个包装函数，用于传递给VirtualModelManager
  const handleEditModelWithRefresh = (model) => {
    // 调用原有的编辑函数
    onEditModel(model);
  };
  
  // 模型保存后刷新列表
  const handleModelSaved = (modelData) => {
    // 如果当前是模型管理标签页，刷新列表
    if (activeTab === 'models' && virtualModelManagerRef.current) {
      virtualModelManagerRef.current.refresh();
    }
    
    // 调用父组件的保存回调（如果存在）
    if (typeof onModelSaved === 'function') {
      onModelSaved(modelData);
    }
  };

  // 处理删除所有任务
  const handleDeleteAllTasks = () => {
    if (!tasks || tasks.length === 0) return;
    
    showDeleteConfirmModal({
      title: '删除全部记录',
      content: '确定要删除全部生成记录吗？删除后将无法恢复。',
      onOk: () => {
        // 删除所有任务
        tasks.forEach(task => {
          // 跳过二次确认，直接删除所有任务
          onDeleteTask(task.taskId, true);
        });
      },
      okButtonProps: {
        danger: true
      }
    });
  };

  // 处理清除无效记录
  const handleClearInvalidTasks = () => {
    if (!tasks || tasks.length === 0) return;
    
    // 筛选出无效记录，只考虑当前页面类型的任务
    const invalidTasks = tasks.filter(task => {
      // 首先确保任务与当前页面类型匹配
      const isMatchingPageType = task.pageType === pageType;
                              
      if (!isMatchingPageType) return false;
      
      // 状态不是"completed"或者任务创建时间超过6秒的视为无效
      return task.status !== 'completed' &&
        (task.createdAt && new Date(task.createdAt).getTime() < new Date().getTime() - 6 * 1000)
    });
    if (invalidTasks.length === 0) {
      // 如果没有无效记录，提示用户
      message.info('没有找到无效记录');
      return;
    }
    
    showDeleteConfirmModal({
      title: '清除无效记录',
      content: `确定要清除 ${invalidTasks.length} 条无效记录吗？删除后将无法恢复。`,
      onOk: async () => {
        // 显示正在处理的消息
        message.loading({ content: '正在清除无效记录...', key: 'clearInvalid' });
        
        // 使用Promise.all确保所有删除操作都完成

        // invalidTasks 最新的不处理
        const latestInvalidTasks = invalidTasks.filter(task => task.createdAt > new Date().getTime() - 6 * 1000 || task.status === 'failed');
        const promises = latestInvalidTasks.map(task => {
          return new Promise(resolve => {
            // 添加延迟，避免API并发请求过多
            setTimeout(() => {
              deleteFlowTask(task.taskId);
              resolve();
            }, 100);
          });
        });
        // 删除任务列表
        setTasks(prevTasks => prevTasks.filter(task => !latestInvalidTasks.includes(task)));
        
        await Promise.all(promises);
        message.success({ content: '无效记录已清除', key: 'clearInvalid' });
      },
      okButtonProps: {
        danger: true
      },
      animation: false,
      transitionName: '',
      maskTransitionName: ''
    });
  };

  // 添加图片详情相关状态
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  
  // 处理查看图片详情
  const handleViewDetails = async (image, task, index) => {
    if (typeof onViewDetails === 'function') {
      // 如果传入了外部处理函数，则调用它
      const viewData= onViewDetails(image, task, index)
      console.log('<UNK>viewDetails:', {...viewData,...image,imageIndex:index});

      // 检查是否有跳过标记，如果有则不显示GenerationArea的弹窗
      if (viewData && viewData._skipGenerationAreaModal) {
        console.log('跳过GenerationArea弹窗显示');
        return;
      }

      setSelectedImage({...viewData,...image,imageIndex:index});
      setShowImageDetails(true);
    } else {
      // 否则使用内部处理逻辑
      setSelectedImage(image);
      setShowImageDetails(true);
    }
  };
  
  // 处理关闭图片详情
  const handleCloseImageDetails = () => {
    setShowImageDetails(false);
    setSelectedImage(null);
  };

  // 处理优秀案例图片预览
  const handleShowcaseImagePreview = (item) => {
    // 优先使用previewImage（带-a后缀的高清图片），如果没有则使用image
    const imageUrl = item.previewImage || item.image;
    setPreviewImageUrl(imageUrl);
    setPreviewImageAlt(item.alt);
    setPreviewFeatureName(item.title);
    setPreviewModalVisible(true);
  };

  // 关闭图片预览
  const handleCloseImagePreview = () => {
    setPreviewModalVisible(false);
    setPreviewImageUrl('');
    setPreviewImageAlt('');
    setPreviewFeatureName('');
  };

  const setGenerationTasksInner = (task) => {
    setTasks(prevTasks => {
      const index = prevTasks.findIndex(t => t.taskId === task.taskId);
      console.log('更新tasks:', prevTasks, task, index);
      return index !== -1
        ? [...prevTasks.slice(0, index), task, ...prevTasks.slice(index + 1)]
        : [task, ...prevTasks];
    });
  }

  // 初始化全局WebSocket连接（只在组件挂载时执行一次）
  useEffect(() => {
    let mounted = true;

    const initializeGlobalWebSocket = async () => {
      try {
        // 确保全局WebSocket连接已建立
        await globalWebSocketManager.connect();
        
        if (mounted) {
          console.log('全局WebSocket连接已初始化');
        }
      } catch (error) {
        console.error('初始化全局WebSocket连接失败:', error);
      }
    };

    initializeGlobalWebSocket();

    // 组件卸载时不关闭WebSocket连接，保持全局连接
    return () => {
      mounted = false;
      // 注意：这里不调用disconnect()，保持全局连接
    };
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 处理任务订阅（使用全局WebSocket管理器）
  useEffect(() => {
    if (!completedTasksRef.current) completedTasksRef.current = new Set();

    const processingTasks = tasks.filter(task => task.status === 'processing');

    console.log('检查处理中的任务:', {
      总任务数: tasks.length,
      处理中任务数: processingTasks.length,
      处理中任务: processingTasks.map(t => ({
        taskId: t.taskId,
        platform: t.platform,
        hasNetWssUrl: !!t.netWssUrl,
        hasUrl: !!t.url,
        hasInstanceId: !!t.instanceId,
        promptId: t.promptId
      }))
    });

    processingTasks.forEach(async (task) => {
      // 检测任务平台类型 - 添加详细调试
      console.log('平台检测详情:', {
        taskId: task.taskId,
        'task.platform': task.platform,
        'task.netWssUrl': task.netWssUrl,
        'task.platform === "runninghub"': task.platform === 'runninghub',
        '!!task.netWssUrl': !!task.netWssUrl
      });

      const isRunningHubTask = task.platform === 'runninghub' || task.netWssUrl;
      const platform = isRunningHubTask ? 'runninghub' : 'comfyui';

      console.log('平台检测结果:', {
        taskId: task.taskId,
        isRunningHubTask,
        finalPlatform: platform
      });

      // 不同平台的订阅条件
      const shouldSubscribe = !globalWebSocketManager.subscribedTasks.has(task.taskId) &&
        task.status === 'processing' &&
        !completedTasksRef.current.has(task.taskId) &&
        task.promptId && // 所有平台都需要promptId
        (isRunningHubTask ?
          // RunningHub任务：需要netWssUrl
          task.netWssUrl :
          // ComfyUI任务：需要url和instanceId
          (task.url && task.instanceId)
        );

      console.log(`任务 ${task.taskId} 订阅检查:`, {
        已订阅: globalWebSocketManager.subscribedTasks.has(task.taskId),
        状态: task.status,
        已完成: completedTasksRef.current.has(task.taskId),
        promptId: !!task.promptId,
        'task.platform': task.platform,
        isRunningHubTask,
        detectedPlatform: platform,
        netWssUrl: !!task.netWssUrl,
        url: !!task.url,
        instanceId: !!task.instanceId,
        shouldSubscribe
      });

      if (shouldSubscribe) {
        console.log('开始监听新任务:', {
          taskId: task.taskId,
          platform: platform,
          hasNetWssUrl: !!task.netWssUrl,
          url: task.url,
          instanceId: task.instanceId
        });

        try {
          // 准备平台特定的任务数据
          const taskData = isRunningHubTask ? {
            netWssUrl: task.netWssUrl,
            clientId: task.clientId,
            taskStatus: task.taskStatus,
            promptTips: task.promptTips
          } : {};

          await globalWebSocketManager.subscribeTask(
            task.taskId,
            task.promptId,
            task.instanceId || '',
            task.url,
            {
              onProgress: (progress, max, progressPlatform) => {
                const actualPlatform = progressPlatform || platform;
                console.log(`onProgress (${actualPlatform}):`, {
                  原始进度值: progress,
                  最大值: max,
                  平台: actualPlatform,
                  任务ID: task.taskId
                });

                setTasks(prevTasks => {
                  const currentTask = prevTasks.find(t => t.taskId === task.taskId);
                  if (!currentTask) return prevTasks;
                  
                  // 根据平台类型计算进度
                  let newProgress;
                  if (actualPlatform === 'runninghub') {
                    // RunningHub: 使用模拟进度，每次收到消息就增加0.01
                    // 到达0.97后停止累加，等待完成信号
                    const currentProgress = currentTask.progress || 0;

                    // 如果当前进度已经达到0.97，则不再增加
                    if (currentProgress >= 0.97) {
                      newProgress = currentProgress;
                    } else {
                      // 每次增加0.01，但不超过0.97
                      newProgress = Math.min(currentProgress + 0.01, 0.97);
                    }
                  } else {
                    // ComfyUI: 保持原有逻辑，每次增加1%
                    const currentProgress = currentTask.progress || 0;
                    newProgress = Math.min(currentProgress + 0.01, 0.97);
                  }

                  // 更新任务进度
                  const updatedTask = {
                    ...currentTask,
                    progress: newProgress,
                    status: 'processing',
                    platform: actualPlatform,
                    progressDetails: {
                      current: progress,
                      total: max || 100,
                      platform: actualPlatform
                    }
                  };

                  console.log(`任务 ${task.taskId} (${actualPlatform}) 进度更新:`, {
                    收到消息: `${progress}/${max}`,
                    当前进度: currentTask.progress,
                    新进度: newProgress,
                    进度百分比: `${Math.round(newProgress * 100)}%`,
                    模拟进度: actualPlatform === 'runninghub' ? '是' : '否'
                  });

                  return prevTasks.map(t =>
                    t.taskId === task.taskId ? updatedTask : t
                  );
                });
              },
              onCompleted: async (outputs) => {
                console.log('onCompleted:', outputs);
                const promptId = outputs.prompt_id;
                const updatedTask = {
                  ...task,
                  progress: 1,
                  status: 'processing',
                  progressDetails: {
                    current: 1,
                    total: 1
                  }
                };
                setTasks(prevTasks => 
                  prevTasks.map(t => 
                    t.taskId === task.taskId ? updatedTask : t
                  )
                );
                try {
                  // 找到 promptId 相同任务并更新状态
                  const completedTask = tasks.find(t => t.promptId === promptId);
                  if (completedTask) {
                    pollTaskDetailSync(
                      completedTask.taskId,
                      getFlowTaskDetailByTid,
                      (res) => {
                        setGenerationTasksInner(res);
                        if(task.newTask==true){
                          setIsProcessing(false);
                        }
                      }
                    );
                  }
                } catch (error) {
                  console.error('获取完成任务详情失败:', error);
                  
                  // 如果获取任务详情失败，将任务标记为失败状态
                  const failedTask = {
                    ...task,
                    status: 'failed',
                    errorMessage: '获取任务详情失败: ' + (error.message || '未知错误')
                  };
                  
                  // 更新本地状态
                  setTasks(prevTasks => 
                    prevTasks.map(t => 
                      t.taskId === task.taskId ? failedTask : t
                    )
                  );
                  
                  // 调用updateTask以触发失败提示音
                  updateTask(failedTask);
                }
                completedTasksRef.current.add(task.taskId); // 标记为已完成，后续不再订阅
              }
            },
            platform,
            taskData
          );
        } catch (error) {
          console.error(`订阅任务 ${task.taskId} 失败:`, error);

          // 如果订阅失败，将任务标记为失败状态
          const failedTask = {
            ...task,
            status: 'failed',
            errorMessage: `订阅任务失败: ${error.message || '未知错误'}`
          };

          // 更新本地状态
          setTasks(prevTasks =>
            prevTasks.map(t =>
              t.taskId === task.taskId ? failedTask : t
            )
          );

          // 调用updateTask以触发失败提示音
          updateTask(failedTask);
        }
      }
    });
  }, [tasks]); // 依赖于 tasks 变化

  const [showDemoPreview, setShowDemoPreview] = useState(false);

  const [popupPosition, setPopupPosition] = useState({ x: 0, y: 0 });
  const [popupVisible, setPopupVisible] = useState(false);

  const getGuideImageUrl = () => {
    // Implement the logic to return the URL of the guide image based on the pageType
    // This is a placeholder and should be replaced with the actual implementation
    return getDemoGifUrl(pageType);
  };

  return (
    <div className="generation-area">
      <div className="result-header">
        <div className="modal-tabs">
          <button 
            className={`tab-button ${activeTab === 'result' ? 'active' : ''}`}
            onClick={() => onTabChange('result')}
          >
            生成结果
          </button>
          {pageType === 'virtual' && (
            <button 
              className={`tab-button ${activeTab === 'models' ? 'active' : ''}`}
              onClick={() => onTabChange('models')}
            >
              虚拟模特管理
            </button>
          )}
          {pageType !== 'extract' && (
            <button 
              className={`tab-button ${activeTab === 'showcase' ? 'active' : ''}`}
              onClick={() => onTabChange('showcase')}
            >
              优秀案例
            </button>
          )}
        </div>
        
        {activeTab === 'result' && (
          <div className="header-actions">
            <div className="search-container">
              <Input
                placeholder=" 输入任务ID搜索"
                value={filters.taskId || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, taskId: e.target.value }))}
                prefix={<SearchOutlined className="search-icon" />}
                allowClear
                className="task-search-input"
              />
            </div>
            {!isMobile && (
            <DateRangePicker 
              value={filters.dateRange}
              onChange={(dates) => setFilters(prev => ({ ...prev, dateRange: dates }))}
            />
            )}
            <Button
              onClick={handleFilterChange}
              className="filter-btn"
              title="应用筛选条件"
            >
              筛选
            </Button>
            {!isMobile && (
            <button 
              className="edit-btn clear-invalid-btn"
              onClick={handleClearInvalidTasks}
              title="清除未完成或生成失败的任务记录"
            >
              清除无效记录
            </button>
            )}
            <div className="more-actions" ref={moreMenuRef}>
              <button 
                className="more-btn"
                onClick={() => setShowMoreMenu(!showMoreMenu)}
                title="更多选项"
              >
                <span></span>
              </button>
              {showMoreMenu && (
                <div className="dropdown-menu show">
                  {isMobile && (
                    <button 
                      className="dropdown-item"
                      onClick={() => {
                        handleClearInvalidTasks();
                        setShowMoreMenu(false);
                      }}
                    >
                      清除无效记录
                    </button>
                  )}
                  <button 
                    className="dropdown-item"
                    onClick={() => {
                      setShowDemoPreview(true);
                      setShowMoreMenu(false);
                    }}
                  >
                    功能演示
                  </button>
                  <button 
                    className={`dropdown-item delete ${(!tasks || tasks.length === 0) ? 'disabled' : ''}`}
                    onClick={() => {
                      // 关闭下拉菜单
                      setShowMoreMenu(false);
                      
                      if (!tasks || tasks.length === 0) {
                        message.info('当前没有任务记录可删除');
                        return;
                      }
                      
                      let secondsLeft = 3;
                      
                      // 创建一个Modal实例的引用
                      let modalInstance = null;
                      
                      const modal = showDeleteConfirmModal({
                        title: '删除全部记录',
                        content: '确定要删除全部生成记录吗？删除后将无法恢复！',
                        okButtonProps: {
                          danger: true,
                          disabled: true // 初始禁用确认按钮
                        },
                        onOk: async () => {
                          // 创建一个提示
                          message.loading({ content: '正在删除所有记录...', key: 'deleteAll' });
                          
                          // 使用Promise.all确保所有删除操作都完成
                          const promises = tasks.map(task => {
                            return new Promise(resolve => {
                              // 添加延迟，避免API并发请求过多
                              setTimeout(() => {
                                // 传递skipConfirm参数为true，避免每个任务都弹出确认框
                        onDeleteTask(task.taskId, true);
                                resolve();
                              }, 100);
                            });
                          });
                          
                          await Promise.all(promises);
                          message.success({ content: '所有记录已删除', key: 'deleteAll' });
                        },
                        animation: false,
                        transitionName: '',
                        maskTransitionName: ''
                      });
                      
                      // 保存Modal实例
                      if (modal && modal.update) {
                        modalInstance = modal;
                      }
                      
                      // 等待模态框打开后开始倒计时
                      setTimeout(() => {
                        // 查找内容区域
                        const contentElement = document.querySelector('.ant-modal-confirm-content');
                        
                        if (!contentElement) return;
                        
                        // 创建倒计时元素
                        const countdownContainer = document.createElement('div');
                        countdownContainer.style.color = 'var(--brand-error, #ff4d4f)';
                        countdownContainer.style.marginTop = '10px';
                        countdownContainer.textContent = `确认按钮将在 ${secondsLeft} 秒后可用`;
                        contentElement.appendChild(countdownContainer);
                        
                        // 开始倒计时
                        const timer = setInterval(() => {
                          secondsLeft -= 1;
                          countdownContainer.textContent = `确认按钮将在 ${secondsLeft} 秒后可用`;
                          
                          if (secondsLeft <= 0) {
                            // 倒计时结束，启用确认按钮
                            clearInterval(timer);
                            countdownContainer.textContent = ''; // 清空提示文本
                            
                            // 使用Modal的API更新按钮状态
                            if (modalInstance && modalInstance.update) {
                              modalInstance.update({
                                okButtonProps: {
                                  danger: true,
                                  disabled: false
                                }
                              });
                            }
                          }
                        }, 1000);
                        
                        // 监听模态框关闭，清除定时器
                        const modalElement = document.querySelector('.ant-modal');
                        if (modalElement) {
                          const observer = new MutationObserver((mutations) => {
                            mutations.forEach((mutation) => {
                              if (mutation.type === 'attributes' && 
                                  mutation.attributeName === 'style' &&
                                  modalElement.style.display === 'none') {
                                clearInterval(timer);
                                observer.disconnect();
                              }
                            });
                          });
                          
                          observer.observe(modalElement, { attributes: true });
                        }
                      }, 100); // 给Modal一点时间渲染
                    }}
                  >
                    删除全部记录
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {activeTab === 'result' ? (
        <>
          <div className="result-content">
            {(!displayTasks || displayTasks.length === 0) ? (
              <div className="result-placeholder">
                <div className="demo-content">
                  <div className="demo-gif-container">
                    <img 
                      src={getDemoGifUrl(pageType)} 
                      alt={`${pageType}功能演示`}
                      className="demo-gif"
                      onError={(e) => {
                        e.target.style.display = 'none';
                      }}
                    />
                  </div>
                  <div className="demo-text">
                    <p className="demo-description">生成的内容将在这里显示</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="task-list">
                {Array.isArray(filteredCurrentTasks) && filteredCurrentTasks.map((task) => {
                  if (!task) return null;
                  // 确保任务ID不为空，只使用标准的taskId字段
                  if (!task.taskId) {
                    console.error('任务缺少有效的taskId字段:', task);
                    // 继续渲染但标记为错误
                    task.hasInvalidId = true;
                  }
                  
                  const validTask = {
                    ...task,
                    // 确保使用标准taskId字段
                    taskId: task.taskId
                  };
                  
                  // 针对不同类型的任务使用不同的面板组件
                  if (pageType === 'extract') {
                    return (
                      <TaskTextPanel
                        key={validTask.taskId}
                        task={validTask}
                        onEditTask={onEditTask}
                        onDeleteTask={onDeleteTask}
                        pageType={pageType}
                      />
                    );
                  } else if ((pageType === 'virtual' || pageType === 'inspiration') && validTask.taskType === 'text') {
                    return (
                      <TaskTextPanel
                        key={validTask.taskId}
                        task={validTask}
                        onEditTask={onEditTask}
                        onDeleteTask={onDeleteTask}
                        pageType={pageType}
                      />
                    );
                  } else {
                    return (
                      <TaskPanel
                        key={validTask.taskId}
                        task={validTask}
                        onEditTask={onEditTask}
                        onDownloadImage={onDownloadImage}
                        onViewDetails={handleViewDetails}
                        onBatchDownload={onBatchDownload}
                        onDeleteTask={onDeleteTask}
                        pageType={pageType}
                        onAddAsExclusiveModel={onAddAsExclusiveModel}
                        disableBatchDownload={disableBatchDownload}
                      />
                    );
                  }
                })}
              </div>            )}
          </div>
          {displayTasks && displayTasks.length > 0 && (
            <Pagination
              current={currentPage}
              total={displayTasks.length}
              pageSize={pageSize}
              onChange={handlePageChange}
            />
          )}
        </>
            ) : activeTab === 'models' && pageType === 'virtual' ? (        <VirtualModelManager           ref={virtualModelManagerRef}          onEditModel={handleEditModelWithRefresh}         />      ) : (        <Showcase           tag={pageTypeToTagMap[pageType] || 'all'}           onImagePreview={handleShowcaseImagePreview}         />      )}

      {showImageDetails && selectedImage ? (
        <ImageDetailsModal
          selectedImage={selectedImage}
          generationTasks={tasks}
          onClose={handleCloseImageDetails}
          onEditTask={onEditTask}
          pageType={pageType}
        />
      ) : null}

      {/* 优秀案例图片预览弹窗 */}
      <ImagePreviewModal
        visible={previewModalVisible}
        imageUrl={previewImageUrl}
        onClose={handleCloseImagePreview}
        alt={previewImageAlt}
        featureName={previewFeatureName}
        showHint={false}
      />

      {showDemoPreview && (
        <div className="image-preview-modal" onClick={() => setShowDemoPreview(false)}>
          <div className="preview-content image-preview-content" onClick={e => e.stopPropagation()} style={{ position: 'relative' }}>
            <button className="preview-close-button" onClick={() => setShowDemoPreview(false)}>
              <MdClose />
            </button>
            <img
              src={getDemoGifUrl(pageType)}
              alt="功能演示"
              style={{ width: '100%', maxWidth: '512px', height: 'auto', objectFit: 'cover', borderRadius: '8px' }}
            />
          </div>
        </div>
      )}

      <div 
        className="sidebar-guide-popup"
        style={{
          position: 'fixed',
          left: popupPosition.x + 60, // 向右偏移，避免遮挡按钮
          top: popupPosition.y - 112.5, // 垂直居中（225/2）
          zIndex: 9999,
          opacity: popupVisible ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out',
          pointerEvents: popupVisible ? 'auto' : 'none'
        }}
      >
        <div className="guide-popup-container" style={{ width: '320px', height: '245px' }}>
          <button 
            className="preview-close-button"
            onClick={() => setPopupVisible(false)}
            style={{
              position: 'absolute',
              top: '8px',
              right: '8px',
              zIndex: 1,
              background: 'rgba(0, 0, 0, 0.5)',
              border: 'none',
              borderRadius: '50%',
              width: '24px',
              height: '24px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              color: 'white',
              fontSize: '14px'
            }}
          >
            <MdClose />
          </button>
          <img 
            src={getGuideImageUrl()} 
            alt="功能演示"
            style={{ 
              width: '300px', 
              height: '225px',
              objectFit: 'cover',
              borderRadius: '8px'
            }}
          />
          <div className="guide-popup-pointer"></div>
        </div>
      </div>
    </div>
  );
});

export default GenerationArea; 