/**
 * 工作流模型
 * 用于管理系统中的工作流定义
 */

const mongoose = require('mongoose');

const workflowSchema = new mongoose.Schema({
  // 工作流ID（唯一标识）
  id: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  
  // 工作流名称
  name: {
    type: String,
    required: true,
    trim: true
  },
  
  // 显示名称
  displayName: {
    type: String,
    trim: true
  },
  
  // 工作流分类
  category: {
    type: String,
    required: true,
    enum: ['款式设计', '模特图', '工具', '其他'],
    default: '其他'
  },
  
  // 工作流描述
  description: {
    type: String,
    trim: true
  },
  
  // 工作流版本
  version: {
    type: String,
    default: '1.0.0'
  },
  
  // 是否启用
  enabled: {
    type: Boolean,
    default: true
  },
  
  // 工作流参数配置
  parameters: {
    type: Map,
    of: mongoose.Schema.Types.Mixed,
    default: new Map()
  },
  
  // 支持的平台
  supportedPlatforms: [{
    type: String,
    enum: ['comfyui', 'runninghub'],
    default: ['comfyui']
  }],
  
  // 推荐平台
  recommendedPlatform: {
    type: String,
    enum: ['comfyui', 'runninghub', 'auto'],
    default: 'auto'
  },
  
  // 工作流标签
  tags: [{
    type: String,
    trim: true
  }],
  
  // 优先级（数字越大优先级越高）
  priority: {
    type: Number,
    default: 0
  },
  
  // 创建者
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // 最后更新者
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // 使用统计
  usageStats: {
    totalRuns: {
      type: Number,
      default: 0
    },
    successRuns: {
      type: Number,
      default: 0
    },
    failedRuns: {
      type: Number,
      default: 0
    },
    lastUsed: {
      type: Date
    }
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      // 转换Map为普通对象
      if (ret.parameters instanceof Map) {
        ret.parameters = Object.fromEntries(ret.parameters);
      }
      return ret;
    }
  },
  toObject: { 
    virtuals: true,
    transform: function(doc, ret) {
      if (ret.parameters instanceof Map) {
        ret.parameters = Object.fromEntries(ret.parameters);
      }
      return ret;
    }
  }
});

// 索引
workflowSchema.index({ id: 1 });
workflowSchema.index({ category: 1 });
workflowSchema.index({ enabled: 1 });
workflowSchema.index({ createdBy: 1 });
workflowSchema.index({ 'usageStats.totalRuns': -1 });

// 虚拟字段
workflowSchema.virtual('successRate').get(function() {
  if (this.usageStats.totalRuns === 0) return 0;
  return (this.usageStats.successRuns / this.usageStats.totalRuns * 100).toFixed(2);
});

// 实例方法
workflowSchema.methods.updateUsage = function(success = true) {
  this.usageStats.totalRuns += 1;
  if (success) {
    this.usageStats.successRuns += 1;
  } else {
    this.usageStats.failedRuns += 1;
  }
  this.usageStats.lastUsed = new Date();
  return this.save();
};

// 静态方法
workflowSchema.statics.findByCategory = function(category) {
  return this.find({ category, enabled: true }).sort({ priority: -1, name: 1 });
};

workflowSchema.statics.findEnabled = function() {
  return this.find({ enabled: true }).sort({ category: 1, priority: -1, name: 1 });
};

workflowSchema.statics.getCategories = function() {
  return this.distinct('category');
};

// 中间件
workflowSchema.pre('save', function(next) {
  if (!this.displayName) {
    this.displayName = this.name;
  }
  next();
});

workflowSchema.pre('findOneAndUpdate', function(next) {
  this.set({ updatedAt: new Date() });
  next();
});

module.exports = mongoose.model('Workflow', workflowSchema);
