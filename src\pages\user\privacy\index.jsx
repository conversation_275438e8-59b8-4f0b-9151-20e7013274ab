import React from 'react';
import './index.css';

const PrivacyPolicy = () => {
  return (
    <div className="legal-page">
      <div className="legal-container">
        {/* 
          直接加载.htm文件。
          注意：如果在开发环境中出现乱码，这是因为开发服务器强制以UTF-8提供文件。
          在标准的生产服务器上，浏览器会正确读取文件内的编码声明并正常显示。
        */}
        <div className="legal-content-iframe">
          <iframe 
            src="/words/privacy/privacy.htm"
            title="隐私政策"
            className="legal-iframe"
            frameBorder="0"
            scrolling="auto"
          />
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy; 