import websocketLifecycleManager from './websocketLifecycleManager';
import { globalWebSocketManager } from './comfyUITaskTracker';

/**
 * WebSocket工具函数
 */
export const websocketUtils = {
  /**
   * 用户退出登录时调用，关闭WebSocket连接
   */
  handleUserLogout() {
    console.log('用户退出登录，关闭WebSocket连接');
    websocketLifecycleManager.handleUserLogout();
  },

  /**
   * 获取WebSocket连接状态
   */
  getConnectionStatus() {
    return globalWebSocketManager.getConnectionStatus();
  },

  /**
   * 获取WebSocket生命周期管理器状态
   */
  getLifecycleStatus() {
    return websocketLifecycleManager.getStatus();
  },

  /**
   * 手动重新连接WebSocket
   */
  async reconnect() {
    try {
      console.log('手动重新连接WebSocket');
      await globalWebSocketManager.connect();
      return true;
    } catch (error) {
      console.error('手动重连失败:', error);
      return false;
    }
  },

  /**
   * 获取所有订阅的任务
   */
  getSubscribedTasks() {
    return Array.from(globalWebSocketManager.subscribedTasks.keys());
  },

  /**
   * 取消订阅所有任务
   */
  unsubscribeAllTasks() {
    const taskIds = Array.from(globalWebSocketManager.subscribedTasks.keys());
    taskIds.forEach(taskId => {
      globalWebSocketManager.unsubscribeTask(taskId);
    });
    console.log(`已取消订阅 ${taskIds.length} 个任务`);
  }
};

export default websocketUtils; 