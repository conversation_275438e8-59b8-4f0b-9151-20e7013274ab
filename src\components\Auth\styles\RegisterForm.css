/* Register Form Styles */

.register-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-bottom: 20px;
}

.register-form .form-group input::placeholder {
  transition: none !important;
  animation: none !important;
  transform: none !important;
  opacity: 1;
}

/* 添加简单错误消息的样式 */
.register-form .simple-error-message {
  color: var(--error-color);
  font-size: 12px;
  line-height: 1.5;
  margin: 2px 0 0 0;
  display: block;
  text-align: left;
}

.register-form .submit-error {
  margin-top: -10px;
  margin-bottom: 10px;
}

/* 隐私政策同意选项样式 - 复用登录表单的remember-me样式 */
.register-form .form-footer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 12px;
  margin-bottom: 0;
}

/* 覆盖input-group的margin-bottom，让错误提示紧贴 */
.register-form .input-group {
  margin-bottom: 0;
}

/* 覆盖AuthModals.css中的form-group margin-bottom设置 */
.register-form .form-group {
  margin-bottom: 0 !important;
}

.register-form .remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
}

.register-form .remember-me input[type="checkbox"] {
  width: 16px;
  height: 16px;
  border: 1px solid var(--border-color);
  border-radius: 2px;
  margin: 0;
  padding: 0;
  appearance: none;
  background-color: var(--bg-primary);
  position: relative;
  cursor: pointer;
}

.register-form .remember-me input[type="checkbox"]:checked {
  background-color: var(--brand-primary);
  border-color: var(--brand-primary);
}

.register-form .remember-me input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.register-form .remember-me input[type="checkbox"]:hover {
  border-color: var(--brand-primary);
}

/* 链接样式 */
.register-form .remember-me a {
  color: var(--brand-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.register-form .remember-me a:hover {
  text-decoration: underline;
} 