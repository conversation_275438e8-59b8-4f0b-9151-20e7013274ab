import React from 'react';
import './index.css';

const ThumbnailList = ({ 
  images = [], 
  selectedIndex = 0,
  onSelect,
  className = '',
  style = {}
}) => {
  return (
    <div className={`thumbnail-list ${className}`} style={style}>
      {images.map((image, index) => (
        <div 
          key={index}
          className={`thumbnail-card ${selectedIndex === index ? 'active' : ''}`}
          onClick={() => onSelect?.(image, index)}
        >
          <img src={image.url} alt={`缩略图 ${index + 1}`} />
        </div>
      ))}
    </div>
  );
};

export default ThumbnailList; 