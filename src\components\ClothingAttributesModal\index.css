/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/buttons.css';
@import '../../styles/inputs.css';
@import '../../styles/tabs.css';
@import '../../styles/panels.css';
@import '../../styles/cards.css';
@import '../../styles/scrollbars.css';
@import '../../styles/common.css';

.clothing-attributes-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* 使用dvh确保在移动端正确显示 */
  height: 100dvh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999; /* 大幅提高z-index，确保在所有内容之上，包括移动端导航栏 */
  background-color: var(--bg-mask);
}

.clothing-attributes-modal .modal-content {
  pointer-events: auto;
  width: 1200px;
  height: calc(100vh - 150px);
  min-height: 600px;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  z-index: 100000; /* 确保弹窗内容在包装器之上 */
}

.clothing-attributes-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  margin-top: 0;
  padding-bottom: 80px;
  will-change: transform;
  -webkit-overflow-scrolling: touch;
}

/* 更新标签页样式 */
.clothing-attributes-modal .modal-header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 20px 20px 0;
  margin: 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  background: var(--bg-primary);
  cursor: grab;
}

/* 特殊的模态框底部样式（与模特弹窗、场景弹窗一致） */
.clothing-attributes-modal .modal-footer {
  box-shadow: none;
  border-top: 1px solid var(--border-color);
}

/* 添加筛选区域样式 */
.clothing-attributes-modal .filter-section {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.clothing-attributes-modal .section-title {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  font-weight: 500;
}

.clothing-attributes-modal .filter-group {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.clothing-attributes-modal .filter-group label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  min-width: 60px;
  margin-top: 6px;
}

.clothing-attributes-modal .filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  flex: 1;
}

.clothing-attributes-modal .filter-option {
  padding: var(--spacing-xxs) var(--spacing-sm);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-normal);
}

.clothing-attributes-modal .filter-option:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.clothing-attributes-modal .filter-option.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 提示词输入区域样式 */
.clothing-attributes-modal .prompt-input {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
  position: relative;
}

.clothing-attributes-modal .prompt-input label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.clothing-attributes-modal .prompt-textarea {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  resize: none;
  transition: var(--transition-normal);
  position: relative;
}

.clothing-attributes-modal .prompt-textarea:focus {
  outline: none;
  border-color: var(--brand-primary);
  background: var(--bg-primary);
}

.clothing-attributes-modal .prompt-textarea::placeholder {
  color: var(--text-tertiary);
  font-size: 12px;
}

.clothing-attributes-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.empty-attributes {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
  padding: var(--spacing-xl);
}

.empty-attributes .coming-soon-icon {
  width: 120px;
  height: 120px;
  margin-bottom: var(--spacing-xl);
  opacity: 0.8;
}

.empty-attributes h3 {
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.empty-attributes p {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.attributes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  padding: 0;
  width: 100%;
}

.attribute-item {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  cursor: pointer;
  padding: 0;
  width: 100%;
  display: block;
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
}

.attribute-item:hover {
  border-color: var(--brand-primary);
  box-shadow: var(--shadow-brand);
}

.attribute-item.selected {
  border-color: var(--brand-primary);
  box-shadow: var(--shadow-brand);
}

.attribute-caption {
  position: relative;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-xs);
  background: var(--bg-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0;
}

.attribute-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.attribute-name {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
}

/* 图片预览弹窗样式 - 已迁移到独立组件 */

/* ClothingAttributesModal 拖动按钮动态位置调整 */
.clothing-attributes-modal .prompt-input .textarea-resize-handle {
  /* 添加平滑过渡效果，让位置变化更优雅 */
  transition: right 0.2s ease;
}

/* 拖动时手型光标 */
.clothing-attributes-modal .modal-header {
  cursor: grab;
}
.clothing-attributes-modal .modal-content.dragging .modal-header {
  cursor: grabbing !important;
}

/* 覆盖全局 modal-large 的 margin-left，确保完美居中 */
.clothing-attributes-modal .modal-content.modal-large {
  margin-left: 0 !important;
  width: 1200px;
}

/* 添加移动端适配样式 */
@media (max-width: 768px) {
  .clothing-attributes-modal .modal-content {
    width: 95% !important;
    min-height: 400px !important;
    max-height: calc(100vh - 40px) !important;
    max-height: calc(100dvh - 40px) !important;
    /* 缩小内容与边框的间距 */
    padding: 4px !important;
  }
  
  /* 移动端缩小文字和间距 */
  .clothing-attributes-modal .modal-body {
    padding: 8px !important;
    padding-bottom: 8px !important;
  }
  
  .clothing-attributes-modal .modal-header {
    padding: 12px 12px 0;
  }
  
  .clothing-attributes-modal .section-title {
    font-size: 14px;
    margin-bottom: 8px;
  }
  
  .clothing-attributes-modal .filter-group label {
    font-size: 12px;
    min-width: 50px;
  }
  
  .clothing-attributes-modal .filter-option {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .clothing-attributes-modal .prompt-input label {
    font-size: 12px;
  }
  
  .clothing-attributes-modal .prompt-textarea {
    font-size: 12px;
    padding: 8px;
  }
  
  .clothing-attributes-modal .attribute-name {
    font-size: 12px;
  }
  
  .clothing-attributes-modal .filter-section {
    padding: 12px;
    gap: 8px;
  }
  
  .clothing-attributes-modal .attribute-description {
    padding: 12px 16px !important;
    font-size: 13px !important;
    margin-top: 8px !important;
  }
  
  .clothing-attributes-modal .filter-group {
    gap: 6px;
    margin-bottom: 8px;
  }
  
  .clothing-attributes-modal .filter-options {
    gap: 4px;
  }
  
  .clothing-attributes-modal .prompt-input {
    gap: 6px;
    margin-bottom: 8px;
  }
  
  .attributes-grid {
    gap: 12px;
    margin-top: 12px;
  }
  
  .attribute-caption {
    padding: 6px;
  }
  
  .attribute-info {
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .clothing-attributes-modal .modal-content {
    width: 98% !important;
    min-height: 450px !important;
    max-height: calc(100vh - 40px) !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    padding: 2px !important;
  }
  
  /* 更小屏幕进一步缩小 */
  .clothing-attributes-modal .modal-body {
    padding: 4px !important;
    padding-bottom: 2px !important;
  }
  
  .clothing-attributes-modal .modal-header {
    padding: 10px 10px 0;
  }
  
  .clothing-attributes-modal .section-title {
    font-size: 13px;
    margin-bottom: 2px;
  }
  
  .clothing-attributes-modal .filter-group label {
    font-size: 11px;
    min-width: 45px;
  }
  
  .clothing-attributes-modal .filter-option {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .clothing-attributes-modal .prompt-input label {
    font-size: 11px;
  }
  
  .clothing-attributes-modal .prompt-textarea {
    font-size: 11px;
    padding: 6px;
  }
  
  .clothing-attributes-modal .attribute-name {
    font-size: 11px;
  }
  
  .clothing-attributes-modal .filter-section {
    padding: 10px;
    gap: 6px;
  }
  
  .clothing-attributes-modal .attribute-description {
    padding: 10px 12px !important;
    font-size: 12px !important;
    margin-top: 6px !important;
  }
  
  .clothing-attributes-modal .filter-group {
    gap: 4px;
    margin-bottom: 6px;
  }
  
  .clothing-attributes-modal .filter-options {
    gap: 3px;
  }
  
  .clothing-attributes-modal .prompt-input {
    gap: 4px;
    margin-bottom: 6px;
  }
  
  .attributes-grid {
    gap: 10px;
    margin-top: 10px;
  }
  
  .attribute-caption {
    padding: 5px;
  }
  
  .attribute-info {
    gap: 4px;
  }
}

@media (max-width: 360px) {
  .clothing-attributes-modal .modal-content {
    width: 100% !important;
    min-height: 400px !important;
    max-height: calc(100vh - 20px) !important;
    border-radius: var(--radius-md) !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    padding: 0 !important;
  }
  
  /* 最小屏幕进一步优化 */
  .clothing-attributes-modal .modal-body {
    padding: 2px !important;
    padding-bottom: 20px !important;
  }
  
  .clothing-attributes-modal .modal-header {
    padding: 8px 8px 0;
  }
  
  .clothing-attributes-modal .section-title {
    font-size: 12px;
    margin-bottom: 4px;
  }
  
  .clothing-attributes-modal .filter-group label {
    font-size: 10px;
    min-width: 40px;
  }
  
  .clothing-attributes-modal .filter-option {
    font-size: 10px;
    padding: 2px 4px;
  }
  
  .clothing-attributes-modal .prompt-input label {
    font-size: 10px;
  }
  
  .clothing-attributes-modal .prompt-textarea {
    font-size: 10px;
    padding: 4px;
  }
  
  .clothing-attributes-modal .attribute-name {
    font-size: 10px;
  }
  
  .clothing-attributes-modal .filter-section {
    padding: 8px;
    gap: 4px;
  }
  
  .clothing-attributes-modal .attribute-description {
    padding: 8px 10px !important;
    font-size: 11px !important;
    margin-top: 4px !important;
  }
  
  .clothing-attributes-modal .filter-group {
    gap: 3px;
    margin-bottom: 4px;
  }
  
  .clothing-attributes-modal .filter-options {
    gap: 2px;
  }
  
  .clothing-attributes-modal .prompt-input {
    gap: 3px;
    margin-bottom: 4px;
  }
  
  .attributes-grid {
    gap: 8px;
    margin-top: 8px;
  }
  
  .attribute-caption {
    padding: 4px;
  }
  
  .attribute-info {
    gap: 3px;
  }
}

/* 真实移动设备适配 */
@media (hover: none) and (pointer: coarse) {
  .clothing-attributes-modal {
    padding: env(safe-area-inset-top, 0px) env(safe-area-inset-right, 0px) env(safe-area-inset-bottom, 0px) env(safe-area-inset-left, 0px);
  }

  .clothing-attributes-modal .modal-content {
    max-height: calc(100dvh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px) - 20px) !important;
  }
  
  /* 移动设备触摸优化 */
  .clothing-attributes-modal .filter-option {
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .clothing-attributes-modal .attribute-item {
    min-height: 120px;
  }
  
  .clothing-attributes-modal .prompt-textarea {
    min-height: 80px;
  }
  
  /* 增加触摸目标大小 */
  .clothing-attributes-modal .attribute-caption {
    min-height: 40px;
  }
  
  .clothing-attributes-modal .filter-group label {
    min-height: 24px;
    display: flex;
    align-items: center;
  }
}

/* 横屏模式适配 */
@media (hover: none) and (pointer: coarse) and (orientation: landscape) {
  .clothing-attributes-modal .modal-content {
    height: calc(100dvh - 20px) !important;
    max-height: calc(100dvh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px) - 20px) !important;
  }
  
  /* 横屏时进一步优化布局 */
  .clothing-attributes-modal .modal-body {
    padding: 8px;
    padding-bottom: 30px;
  }
  
  .clothing-attributes-modal .filter-section {
    padding: 8px;
  }
  
  .clothing-attributes-modal .attribute-description {
    padding: 6px 8px !important;
    font-size: 10px !important;
    margin-top: 3px !important;
  }
  
  .clothing-attributes-modal .section-title {
    font-size: 11px;
    margin-bottom: 4px;
  }
  
  .clothing-attributes-modal .filter-group label {
    font-size: 10px;
    min-width: 35px;
  }
  
  .clothing-attributes-modal .filter-option {
    font-size: 10px;
    padding: 2px 4px;
    min-height: 28px;
  }
  
  .clothing-attributes-modal .prompt-input label {
    font-size: 10px;
  }
  
  .clothing-attributes-modal .prompt-textarea {
    font-size: 10px;
    padding: 4px;
    min-height: 60px;
  }
  
  .clothing-attributes-modal .attribute-name {
    font-size: 10px;
  }
  
  .attributes-grid {
    gap: 6px;
    margin-top: 6px;
  }
  
  .attribute-caption {
    padding: 3px;
    min-height: 32px;
  }
} 