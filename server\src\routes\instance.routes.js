const express = require('express');

const router = express.Router();
const ResourceAPI = require('../config/ResourceAPI');
const { isAuthenticated, isAdmin,auth } = require('../middleware/auth.middleware');
const Instance = require('../models/Instance');
const MongoDBCache = require('../utils/cacheUtils');
const {createError} = require('../utils/error');

const axios = require('axios');
const Config = require('config');

const  offUrl =Config.get('comfyUi').offUrl;
const instanceAxios = require('../utils/instanceAxios');
const cache = new MongoDBCache({
  collectionName: 'instance_status_cache'
});

// 中间件：需要管理员权限
// 中间件：检查是否为管理员
router.use(isAuthenticated,auth, isAdmin);
// 获取实例列表
router.get('/', async (req, res, next) => {
    try {
      const response = await instanceAxios.get(ResourceAPI.INSTANCE_LIST.url, { params: req.query });

      // 获取实例使用情况
      const instanceService = require('../services/instance/instanceService');
      const inUseInstances = await instanceService.getAllInstanceInUseInfo();
      const idleInstances = await instanceService.getAllInstanceIdleInfo();

            // 将使用情况数据整合到实例列表中
      const appList = await Promise.all(response.data.data.appList.map(async (instance) => {
        const inUseInfo = inUseInstances.find(
          info => info.instanceId === instance.appId
        );
        const idleInfo = idleInstances.find(
          info => info.instanceId === instance.appId
        );

        // 获取实例的常开状态
        const instanceConfig = await Instance.findOne({ appId: instance.appId });

        return {
          ...instance,
          inUseWorkflows: inUseInfo ? inUseInfo.value : 0, // 如果存在使用记录则计数为1
          workflowInfo: inUseInfo ? {
            createdAt: inUseInfo.createdAt,
            expiresAt: inUseInfo.expiresAt,
            value: inUseInfo.value
          } : null,
          // 添加空闲时长信息
          idleInfo: idleInfo ? {
            isIdle: idleInfo.isIdle,
            idleMinutes: idleInfo.idleMinutes,
            finishedTime: idleInfo.finishedTime,
            status: idleInfo.status,
            shouldStop: idleInfo.shouldStop
          } : null,
          // 添加常开状态
          isAlwaysOn: instanceConfig ? instanceConfig.isAlwaysOn : false
        };
      }));

      res.json({
        success: true,
        data: appList,
        pagination: {
          total: response.data.data.pagination.total,
          page: parseInt(response.data.data.pagination.page),
          limit: parseInt(response.data.data.pagination.pageSize)
        }
      });
    } catch (error) {
      next(createError(500, `获取实例列表失败: ${error.message}`));
    }
});

// 获取实例统计信息
router.get('/stats', async (req, res, next) => {
  try {
    const stats = await Instance.aggregate([
      {
        $group: {
          _id: null,
          totalInstances: { $sum: 1 },
          runningInstances: {
            $sum: { $cond: [{ $eq: ['$status', 'running'] }, 1, 0] }
          },
          stoppedInstances: {
            $sum: { $cond: [{ $eq: ['$status', 'stopped'] }, 1, 0] }
          },
          errorInstances: {
            $sum: { $cond: [{ $eq: ['$status', 'error'] }, 1, 0] }
          }
        }
      }
    ]);

    res.json({
      success: true,
      data: stats[0] || {
        totalInstances: 0,
        runningInstances: 0,
        stoppedInstances: 0,
        errorInstances: 0
      }
    });
  } catch (error) {
    next(createError(500, `获取实例统计信息失败: ${error.message}`));
  }
});

// 获取单个实例的空闲时长信息
router.get('/:id/idle-info', async (req, res, next) => {
  try {
    const instanceId = req.params.id;
    const instanceService = require('../services/instance/instanceService');
    const idleInfo = await instanceService.getInstanceIdleTime(instanceId);

    res.json({
      success: true,
      data: {
        instanceId: instanceId,
        ...idleInfo
      }
    });
  } catch (error) {
    next(createError(500, `获取实例空闲信息失败: ${error.message}`));
  }
});

// 创建新实例
router.post('/', async (req, res, next) => {
  try {
    const { name, url, apiKey, instanceId, type, description } = req.body;

    // 验证必填字段
    if (!name || !url || !apiKey || !instanceId || !type) {
      throw createError(400, '缺少必要参数');
    }

    // 检查实例名称是否已存在
    const existingInstance = await Instance.findOne({ name });
    if (existingInstance) {
      throw createError(409, '实例名称已存在');
    }

    // 创建新实例
    const instance = new Instance({
      name,
      url,
      apiKey,
      instanceId,
      type,
      description,
      status: 'stopped', // 默认状态为停止
      createdBy: req.user._id
    });

    await instance.save();

    res.status(201).json({
      success: true,
      message: '实例创建成功',
      data: instance
    });
  } catch (error) {
    next(createError(error.status || 500, `创建实例失败: ${error.message}`));
  }
});

// 更新实例
router.put('/:id', async (req, res, next) => {
  try {
    const { name, url, apiKey, instanceId, type, description } = req.body;

    // 检查实例是否存在
    const instance = await Instance.findById(req.params.id);
    if (!instance) {
      throw createError(404, '实例不存在');
    }

    // 如果要更改名称，检查新名称是否已被使用
    if (name && name !== instance.name) {
      const existingInstance = await Instance.findOne({ name });
      if (existingInstance) {
        throw createError(409, '实例名称已存在');
      }
    }

    // 更新实例信息
    const updatedInstance = await Instance.findByIdAndUpdate(
      req.params.id,
      {
        name,
        url,
        apiKey,
        instanceId,
        type,
        description,
        updatedAt: new Date(),
        updatedBy: req.user._id
      },
      { new: true }
    );

    res.json({
      success: true,
      message: '实例更新成功',
      data: updatedInstance
    });
  } catch (error) {
    next(createError(error.status || 500, `更新实例失败: ${error.message}`));
  }
});

// 删除实例
router.delete('/:id', async (req, res, next) => {
  try {
    const instance = await Instance.findById(req.params.id);
    if (!instance) {
      throw createError(404, '实例不存在');
    }

    await instance.remove();

    res.json({
      success: true,
      message: '实例删除成功'
    });
  } catch (error) {
    next(createError(error.status || 500, `删除实例失败: ${error.message}`));
  }
});

// 测试实例连接
router.post('/:id/test', async (req, res, next) => {
  try {
    const instance = await Instance.findById(req.params.id);
    if (!instance) {
      throw createError(404, '实例不存在');
    }

    // 测试连接
    try {
      const response = await axios.get(instance.url, {
        headers: {
          'Authorization': `Bearer ${instance.apiKey}`,
          'X-Instance-ID': instance.instanceId
        },
        timeout: 5000 // 5秒超时
      });

      // 更新实例状态
      instance.status = 'running';
      instance.lastChecked = new Date();
      await instance.save();

      res.json({
        success: true,
        message: '连接测试成功',
        data: {
          status: 'running',
          response: response.data
        }
      });
    } catch (error) {
      // 更新实例状态为错误
      instance.status = 'error';
      instance.lastChecked = new Date();
      instance.lastError = error.message;
      await instance.save();

      throw createError(503, `连接测试失败: ${error.message}`);
    }
  } catch (error) {
    next(createError(error.status || 500, error.message));
  }
});
// 启动实例
router.post('/:id/start', async (req, res, next) => {
  try {
    const instanceId = req.params.id;

    // 调用云服务API启动实例
    try {
      // 发送启动请求到云服务
      const response = await instanceAxios.put(
        ResourceAPI.INSTANCE_START.url+instanceId,
      );

      // 检查响应状态
      if (response.data.code==0) {
        res.json({
          success: true,
          message: '实例启动指令已发送',
          data: {
            appId: instanceId,
            status: 200,
            statusText: '开机中'
          }
        });
      } else {
        throw new Error(response.data.message || '启动实例失败');
      }
    } catch (error) {
      throw createError(500, `调用云服务API失败: ${error.message}`);
    }
  } catch (error) {
    next(createError(error.status || 500, error.message));
  }
});

// 停止实例
router.post('/:id/stop', async (req, res, next) => {
  try {
    const instanceId = req.params.id;
    // 删除工作流状态
    const instanceService = require('../services/instance/instanceService');
    await instanceService.checkInstanceCache(instanceId);

    // 调用云服务API停止实例
    try {
      const response = await instanceAxios.put(
        ResourceAPI.INSTANCE_STOP.url+instanceId,
      );

      if (response.data.code==0) {
        res.json({
          success: true,
          message: '实例停止指令已发送',
          data: {
            appId: instanceId,
            status: 400,
            statusText: '关机中'
          }
        });

      } else {
        throw new Error(response.data.message || '停止实例失败' );
      }
    } catch (error) {
      throw createError(500, `调用云服务API失败: ${error.message}`);
    }
  } catch (error) {
    next(createError(error.status || 500, error.message)  );
  }
});

// 查询GPU使用率
router.get('/:id/gpuutil', async (req, res, next) => {
  try {
    const instanceId = req.params.id;
    // 开始时间和结束时间 如果没有传默认查30分钟
    const startTime = req.query.startTime || new Date(Date.now() - 30 * 60 * 1000);
    const endTime = req.query.endTime || new Date();
    const uri = ResourceAPI.GPU_UTIL.url.replace(':id', instanceId);
    const response = await instanceAxios.get(uri, { params: { startTime, endTime } });
    res.json({
      success: true,
      message: 'GPU使用率查询成功',
      data: response.data.data
    });
  } catch (error) {
    next(createError(error.status || 500, error.message));
  }
});

// 查询GPU内存使用率
router.get('/:id/gpu', async (req, res, next) => {
  try {
    const instanceId = req.params.id;
    // 开始时间和结束时间 如果没有传默认查30分钟
    const startTime = req.query.startTime || new Date(Date.now() - 30 * 60 * 1000);
    const endTime = req.query.endTime || new Date();
    const uri = ResourceAPI.GPU_USAGE.url.replace(':id', instanceId);
    // 调用云服务API查询GPU使用率
    const response = await instanceAxios.get(
      uri,
      { params: { startTime, endTime } }
    );

    res.json({
      success: true,
      message: 'GPU使用率查询成功',
      data: response.data.data
    });
  } catch (error) {
    next(createError(error.status || 500, error.message));
  }
});

// 查询余额
router.get('/balance', async (req, res, next) => {
  try {
    const response = await instanceAxios.get(ResourceAPI.BALANCE.url);
    res.json({
      success: true,
      message: '余额查询成功',
      data: response.data.data
    });
  } catch (error) {
    next(createError(error.status || 500, error.message));
  }
});

// 设置实例常开模式
router.post('/:id/always-on', async (req, res, next) => {
  try {
    const { isAlwaysOn } = req.body;
    const instanceId = req.params.id;

    // 首先尝试查找实例
    let instance = await Instance.findOne({ appId: instanceId });

    if (!instance) {
      // 如果实例不存在，则创建新实例记录
      try {
        // 从云服务API获取实例信息
        const response = await instanceAxios.get(ResourceAPI.INSTANCE_LIST.url, {
          params: { appId: instanceId }
        });

        if (response.data.code === 0 && response.data.data.appList.length > 0) {
          const cloudInstance = response.data.data.appList[0];

          // 创建新的实例记录
          instance = new Instance({
            name: cloudInstance.customName || `实例-${instanceId}`,
            url: cloudInstance.webUIAddress || '',
            apiKey: cloudInstance.apiKey || '',
            instanceId: cloudInstance.appId,
            appId: cloudInstance.appId, // 添加appId字段
            type: 'stable',
            description: '从云服务自动创建的实例记录',
            isAlwaysOn: isAlwaysOn,
            status: 'running',
            createdBy: req.user._id,
            updatedBy: req.user._id
          });

          await instance.save();
          console.log(`自动创建实例记录: ${instanceId}`);
        } else {
          throw createError(404, '在云服务中未找到该实例');
        }
      } catch (error) {
        throw createError(404, `实例不存在且无法从云服务获取: ${error.message}`);
      }
    } else {
      // 如果实例存在，更新常开设置
      instance = await Instance.findOneAndUpdate(
        { appId: instanceId },
        {
          isAlwaysOn: isAlwaysOn,
          updatedAt: new Date(),
          updatedBy: req.user._id
        },
        { new: true }
      );
    }

    res.json({
      success: true,
      message: `实例${isAlwaysOn ? '已设置为常开模式' : '已取消常开模式'}`,
      data: {
        instanceId: instanceId,
        isAlwaysOn: instance.isAlwaysOn
      }
    });
  } catch (error) {
    next(createError(error.status || 500, `设置常开模式失败: ${error.message}`));
  }
});

// 获取实例常开状态
router.get('/:id/always-on', async (req, res, next) => {
  try {
    const instanceId = req.params.id;

    const instance = await Instance.findOne({ appId: instanceId });
    if (!instance) {
      // 如果实例不存在，返回默认状态
      res.json({
        success: true,
        data: {
          instanceId: instanceId,
          isAlwaysOn: false
        }
      });
      return;
    }

    res.json({
      success: true,
      data: {
        instanceId: instanceId,
        isAlwaysOn: instance.isAlwaysOn || false
      }
    });
  } catch (error) {
    next(createError(error.status || 500, `获取常开状态失败: ${error.message}`));
  }
});

module.exports = router;
