# 上传组件修改总结

## 概述

根据《服务器文件名标准化指南》的要求，我们对上传组件进行了修改，统一使用 `serverFileName` 字段，彻底移除对 `originalFile` 的依赖。这是标准化方案第一阶段的实施，确保所有图片上传后都正确设置 `serverFileName`。

## 1. UploadBox 组件修改

**文件路径**: `src/components/UploadBox/index.jsx`

**主要修改**:

1. 将所有 `originalFile: file.name` 替换为 `serverFileName: file.name`
2. 在 `fileInfo` 对象中添加 `serverFileName: file.name` 字段
3. 添加 `processInfo` 对象并设置 `serverFileName: file.name` 字段
4. 同时修改了单文件上传和多文件上传的两个函数

**修改前后对比**:
```javascript
// 修改前
const panel = {
  componentId: imageId,
  title: getPanelTitle(),
  status: 'completed',
  originalFile: file.name,
  url: imageUrl,
  fileInfo: {
    name: file.name,
    size: file.size,
    type: file.type
  },
  type: getPanelType(),
  file: file
};

// 修改后
const panel = {
  componentId: imageId,
  title: getPanelTitle(),
  status: 'completed',
  serverFileName: file.name,
  url: imageUrl,
  fileInfo: {
    name: file.name,
    size: file.size,
    type: file.type,
    serverFileName: file.name
  },
  type: getPanelType(),
  file: file,
  processInfo: {
    serverFileName: file.name
  }
};
```

## 2. UploadBox_Model 组件修改

**文件路径**: `src/components/UploadBox_Model/index.jsx`

**主要修改**:

1. 将所有 `originalFile: file.name` 替换为 `serverFileName: file.name`
2. 在 `fileInfo` 对象中添加 `serverFileName: file.name` 字段
3. 添加 `processInfo` 对象并设置 `serverFileName: file.name` 字段
4. 修改了拖放文件和上传文件两个处理函数中的数据结构

**修改前后对比**:
```javascript
// 修改前
const completedPanels = [{
  id: imageId,
  title: getPanelTitle(),
  status: 'completed',
  originalFile: file.name,
  url: imageUrl,
  fileInfo: {
    name: file.name,
    size: file.size,
    type: file.type
  },
  file: file,
  type: getApiImageType(),
  source: 'upload'
}];

// 修改后
const completedPanels = [{
  id: imageId,
  title: getPanelTitle(),
  status: 'completed',
  serverFileName: file.name,
  url: imageUrl,
  fileInfo: {
    name: file.name,
    size: file.size,
    type: file.type,
    serverFileName: file.name
  },
  file: file,
  type: getApiImageType(),
  source: 'upload',
  processInfo: {
    serverFileName: file.name
  }
}];
```

## 3. UploadGuideModal 组件修改

**文件路径**: `src/components/UploadGuideModal/index.jsx`

**主要修改**:

1. 将所有初始化面板时使用的 `originalFile: file.name` 替换为 `serverFileName: file.name`
2. 在上传成功后，修改了从服务器响应中获取文件名的代码：
   ```javascript
   // 修改前
   serverFileName = processedResult.originalFile;
   
   // 修改后
   serverFileName = processedResult.originalFile || processedResult.serverFileName;
   ```
3. 在更新面板时，修改了处理信息的数据结构：
   ```javascript
   // 修改前
   updateData.processInfo = {
     originalFile: serverFileName,
     uploadResult: result
   };
   
   // 修改后
   updateData.processInfo = {
     serverFileName: serverFileName,
     uploadResult: result
   };
   ```
4. 修改了多个不同类型面板的处理部分，包括：
   - 模特图片上传处理
   - 前景图片和虚拟模特图片上传处理
   - 版型图片和面料印花图片上传处理
   - 服装图片上传处理

5. 对于服装图片上传，修改了上传逻辑，从仅本地预览改为直接上传到服务器，确保获取正确的 `serverFileName`

## 总结

通过这些修改，我们确保了所有图片上传后都能正确设置 `serverFileName` 字段，且在嵌套对象中也设置了一致的值。这些修改是落实《服务器文件名标准化指南》的第一步，接下来需要继续修改各页面组件中的 `handleGenerate` 函数，确保任务创建时使用正确的字段。 