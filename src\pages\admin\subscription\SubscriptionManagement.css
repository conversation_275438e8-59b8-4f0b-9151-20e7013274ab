.subscription-management {
  padding: 24px;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  flex: 1;
}

.stats-card {
  margin-bottom: 24px;
}

.stats-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 24px;
}

.stat-item {
  flex: 1;
  min-width: 200px;
  margin-bottom: 16px;
}

.plan-tag {
  margin-right: 8px;
}

.plan-tag-design {
  color: #108ee9;
  background: #e6f7ff;
  border-color: #91d5ff;
}

.plan-tag-model {
  color: #722ed1;
  background: #f9f0ff;
  border-color: #d3adf7;
}

.plan-tag-full {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
}

.plan-tag-enterprise {
  color: #722ed1;
  background: #f9f0ff;
  border-color: #d3adf7;
}

.plan-tag-free {
  color: #52c41a;
  background: #f6ffed;
  border-color: #b7eb8f;
}

.plan-tag-light, .plan-tag-basic {
  color: #13c2c2;
  background: #e6fffb;
  border-color: #87e8de;
}

.plan-tag-standard, .plan-tag-premium {
  color: #fa8c16;
  background: #fff7e6;
  border-color: #ffd591;
}

.status-tag-active {
  color: #2ecc71;
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.1) 60%, rgba(39, 174, 96, 0.1) 100%);
  border-color: #2ecc71;
}

.status-tag-pending {
  color: #f39c12;
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.1) 60%, rgba(230, 126, 34, 0.1) 100%);
  border-color: #f39c12;
}

.status-tag-expired {
  color: #e74c3c;
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 60%, rgba(192, 57, 43, 0.1) 100%);
  border-color: #e74c3c;
}

.status-tag-canceled {
  color: #b2bec3;
  background: linear-gradient(135deg, rgba(178, 190, 195, 0.1) 60%, rgba(99, 110, 114, 0.1) 100%);
  border-color: #b2bec3;
}

.status-tag-not-started {
  color: #3498db;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 60%, rgba(41, 128, 185, 0.1) 100%);
  border-color: #3498db;
}

@media (max-width: 768px) {
  .subscription-management {
    padding: 12px;
  }
  
  .stat-item {
    min-width: 100%;
  }
} 