import React, { useState, useEffect, useCallback } from 'react';
import { DatePicker, message } from 'antd';
import dayjs from 'dayjs';
import locale from 'antd/es/date-picker/locale/zh_CN';
import './index.css';

const { RangePicker } = DatePicker;

const DateRangePicker = ({ value, onChange, className }) => {
  const [isMobile, setIsMobile] = useState(false);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 日期格式校正函数
  const formatDateInput = useCallback((input) => {
    if (!input || typeof input !== 'string') return null;
    
    // 移除所有非数字字符，只保留数字
    const numbers = input.replace(/\D/g, '');
    
    // 如果长度不足，返回null
    if (numbers.length < 6) return null;
    
    let year, month, day;
    
    // 根据输入长度判断格式
    if (numbers.length === 6) {
      // YYMMDD 格式
      year = parseInt('20' + numbers.substr(0, 2));
      month = parseInt(numbers.substr(2, 2));
      day = parseInt(numbers.substr(4, 2));
    } else if (numbers.length === 8) {
      // YYYYMMDD 格式
      year = parseInt(numbers.substr(0, 4));
      month = parseInt(numbers.substr(4, 2));
      day = parseInt(numbers.substr(6, 2));
    } else {
      return null;
    }
    
    // 验证日期有效性
    if (year < 1900 || year > 2100) return null;
    if (month < 1 || month > 12) return null;
    if (day < 1 || day > 31) return null;
    
    // 创建dayjs对象并验证
    const date = dayjs(`${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`);
    
    if (!date.isValid()) return null;
    
    return date;
  }, []);

  // 处理移动端输入变化
  const handleMobileInput = useCallback((e, type) => {
    const inputValue = e.target.value.trim();
    
    if (!inputValue) {
      // 清空对应的日期
      if (type === 'start') {
        onChange([null, value?.[1] || null]);
      } else {
        onChange([value?.[0] || null, null]);
      }
      return;
    }
    
    const formattedDate = formatDateInput(inputValue);
    
    if (formattedDate) {
      // 格式化成功，更新值
      if (type === 'start') {
        onChange([formattedDate, value?.[1] || null]);
      } else {
        onChange([value?.[0] || null, formattedDate]);
      }
      
      // 自动填充标准格式到输入框
      e.target.value = formattedDate.format('YYYY-MM-DD');
    } else if (inputValue.length >= 6) {
      // 输入长度足够但格式错误，显示错误提示
      message.warning('请输入正确的日期格式，如：20231201 或 2023-12-01');
    }
  }, [value, onChange, formatDateInput]);

  // 如果是移动端，返回带有输入校正的组件
  if (isMobile) {
    return (
      <div className="mobile-date-range-picker">
        <input
          type="text"
          placeholder="开始日期 (如:20231201)"
          defaultValue={value?.[0]?.format('YYYY-MM-DD') || ''}
          onBlur={(e) => handleMobileInput(e, 'start')}
          className="mobile-date-input"
          style={{
            width: '48%',
            padding: '4px 8px',
            border: '1px solid var(--border-color)',
            borderRadius: '4px',
            fontSize: '14px',
            backgroundColor: 'var(--bg-primary)',
            color: 'var(--text-primary)'
          }}
        />
        <span style={{ margin: '0 2%', color: 'var(--text-secondary)' }}>-</span>
        <input
          type="text"
          placeholder="结束日期 (如:20231201)"
          defaultValue={value?.[1]?.format('YYYY-MM-DD') || ''}
          onBlur={(e) => handleMobileInput(e, 'end')}
          className="mobile-date-input"
          style={{
            width: '48%',
            padding: '4px 8px',
            border: '1px solid var(--border-color)',
            borderRadius: '4px',
            fontSize: '14px',
            backgroundColor: 'var(--bg-primary)',
            color: 'var(--text-primary)'
          }}
        />
      </div>
    );
  }

  // 桌面端保持原有功能
  return (
    <RangePicker
      value={value}
      onChange={onChange}
      placeholder={['开始日期', '结束日期']}
      dropdownClassName="account-date-dropdown"
      className={`account-unique-range-picker ${className || ''}`}
      locale={locale}
    />
  );
};

export default DateRangePicker; 