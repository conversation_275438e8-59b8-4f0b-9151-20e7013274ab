/**
 * ImagePreviewModal 组件样式
 * 
 * 包含预览弹窗的所有相关样式：
 * 1. 预览模态框 (.image-preview-modal)
 * 2. 预览内容 (.preview-content)
 * 3. 关闭按钮 (.preview-close-button)
 * 4. 缩放提示 (.preview-hint)
 * 5. 图片容器 (.preview-image-container)
 */

@import '../../../styles/theme.css';

/* 预览模态框 */
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.36);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999; /* 提升优先级，确保在所有弹窗之上 */
  animation: previewFadeIn 0.3s ease;
  padding: 0;
  margin: 0;
  width: 100vw; /* 确保完全覆盖视口宽度 */
  height: 100vh; /* 确保完全覆盖视口高度 */
}

/* 预览内容区 */
.preview-content {
  max-width: 90vw; /* 限制最大宽度为视口宽度的90% */
  max-height: 90vh; /* 限制最大高度为视口高度的90% */
  margin: 0 auto;
  position: relative;
  animation: previewZoomIn 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                    linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  background-color: var(--bg-primary);
  transform-origin: center center;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 预览图片容器 */
.preview-image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image-container img {
  display: block;
  max-width: 90vw; /* 限制最大宽度为视口宽度的90% */
  max-height: 90vh; /* 限制最大高度为视口高度的90% */
  width: auto;
  height: auto;
  border-radius: var(--radius-md);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  background: none !important;
  pointer-events: none;
  object-fit: contain; /* 确保图片按比例缩放 */
}

/* 预览关闭按钮 - 跟随整个预览内容变换 */
.preview-close-button {
  position: absolute;
  top: -45px; /* 放置在图片上方，增加距离确保完全可见 */
  right: 0;
  width: 36px; /* 增加按钮尺寸 */
  height: 36px;
  border-radius: var(--radius-full);
  border: none;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  transition: background-color 0.2s ease, transform 0.2s ease;
  pointer-events: auto; /* 确保按钮可以点击 */
}

.preview-close-button:hover {
  background: #ffffff;
}

.preview-close-button svg {
  width: 20px; /* 增加图标尺寸 */
  height: 20px;
  color: #333;
}

/* 功能名称样式 */
.preview-feature-name {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
  white-space: nowrap;
  z-index: 10;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); /* 增加文字阴影提高辨识度 */
}

/* 缩放提示样式 */
.preview-hint {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  opacity: 0.8;
  white-space: nowrap;
  z-index: 10;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); /* 增加文字阴影提高辨识度 */
}

/* 暗色主题适配 */
[data-theme="dark"] .preview-close-button {
  background: rgba(50, 50, 50, 0.9);
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .preview-close-button:hover {
  background: rgba(60, 60, 60, 1);
}

[data-theme="dark"] .preview-close-button svg {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .preview-feature-name {
  background: rgba(255, 255, 255, 0.9);
  color: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  text-shadow: none; /* 移除文字阴影，因为背景已经是浅色 */
}

[data-theme="dark"] .preview-hint {
  background: rgba(255, 255, 255, 0.9);
  color: rgba(0, 0, 0, 0.9);
  text-shadow: none; /* 移除文字阴影，因为背景已经是浅色 */
}

/* 动画定义 */
@keyframes previewFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes previewZoomIn {
  from {
    transform: scale(0.95);
  }
  to {
    transform: scale(1);
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .image-preview-modal {
    touch-action: none; /* 禁用默认触摸行为，防止页面滚动 */
  }
  
  .preview-content {
    max-width: 95%;
    touch-action: none; /* 确保触摸事件被正确处理 */
  }
  
  .preview-image-container img {
    max-height: 80vh;
  }
  
  .preview-close-button {
    top: -45px;
    right: -8px;
    width: 32px;
    height: 32px;
    touch-action: auto; /* 允许按钮的触摸事件 */
  }
  
  .preview-close-button svg {
    width: 18px;
    height: 18px;
  }
  
  .preview-feature-name {
    font-size: 12px;
    padding: 6px 12px;
    bottom: 10px;
    border-radius: 4px;
  }
  
  .preview-hint {
    font-size: 11px;
    padding: 6px 10px;
    bottom: 8px;
  }
} 