{"name": "aibikini", "version": "0.1.0", "license": "ISC", "dependencies": {"@alicloud/pop-core": "^1.8.0", "@ant-design/icons": "^5.6.0", "@babel/core": "^7.16.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@mui/icons-material": "^5.16.14", "@mui/material": "^5.16.14", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.82", "antd": "^5.24.0", "archiver": "^7.0.1", "axios": "^1.7.9", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bcryptjs": "^3.0.2", "bfj": "^7.0.2", "body-parser": "^1.20.3", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "cors": "^2.8.5", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "file-loader": "^6.2.0", "form-data": "^4.0.2", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "image-size": "^1.2.0", "imagesloaded": "^5.0.0", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "masonry-layout": "^4.2.2", "mini-css-extract-plugin": "^2.4.5", "moment": "^2.30.1", "mongoose": "^8.10.1", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "react": "^18.2.0", "react-app-polyfill": "^3.0.0", "react-colorful": "^5.6.1", "react-dev-utils": "^12.0.1", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "react-image-size": "^2.4.0", "react-refresh": "^0.11.0", "react-router-dom": "^6.22.0", "redis": "^4.7.0", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass-loader": "^12.3.0", "semver": "^7.3.5", "sharp": "^0.33.5", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "styled-components": "^6.1.8", "svg-captcha": "^1.4.0", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "web-vitals": "^2.1.4", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1", "ws": "^8.18.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "cross-env": "^7.0.3", "typescript": "^4.9.5"}, "scripts": {"start": "node scripts/start.js", "dev": "cross-env NODE_ENV=development node scripts/start.js", "build": "node scripts/build.js", "test": "node scripts/test.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": [], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": ["D:\\Development\\project\\xy-demo\\AIBIKINI\\src"], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}}