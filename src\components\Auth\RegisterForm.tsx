import React, { useState, useEffect } from 'react';

import { useAuth } from '../../contexts/AuthContext';
import ErrorMessage from '../ErrorMessage';

import Capt<PERSON><PERSON>andler from './CaptchaHandler';
import PasswordStrength, { checkPasswordStrength } from './PasswordStrength';

// 通知类型接口
interface Notification {
  type: string;
  message: string;
}

// 注册参数类型
interface RegisterParams {
  username: string;
  phone: string;
  password: string;
  verifyCode: string; 
}

// 注册结果接口
interface RegisterResult {
  success: boolean;
  error?: string;
  message?: string;
}

// Auth类型定义
interface Auth {
  register: (userData: RegisterParams) => Promise<RegisterResult>;
  sendVerificationCode: (phone: string) => Promise<{ success: boolean; message?: string }>;
  verifyCode: (phone: string, code: string, type?: string) => Promise<{ success: boolean; message?: string }>;
  // 其他可能的方法...
}

interface RegisterFormProps {
  onClose?: () => void;
  onRegisterSuccess?: (notification: Notification) => void;
  onCaptchaRequest?: (phone: string) => void;
  className?: string;
}

interface RegisterFormState {
  username: string;
  phone: string;
  password: string;
  confirmPassword: string;
  captcha: string;
  nickname: string;
  agreeToTerms: boolean;
}

interface FormErrors {
  username?: string;
  phone?: string;
  password?: string;
  confirmPassword?: string;
  captcha?: string;
  submit?: string;
  passwordStrength?: {
    score: number;
    level: string;
    feedback: Array<{
      text: string;
      met: boolean;
    }>;
  };
  nickname?: string;
  agreeToTerms?: string;
}

/**
 * 注册表单组件
 * 处理用户注册逻辑，包括表单验证、验证码发送和注册请求
 */
const RegisterForm: React.FC<RegisterFormProps> = ({
  onClose,
  onRegisterSuccess,
  onCaptchaRequest,
  className = ''
}) => {
  const auth = useAuth() as unknown as Auth;

  // 表单状态
  const [form, setForm] = useState<RegisterFormState>({
    username: '',
    phone: '',
    password: '',
    confirmPassword: '',
    captcha: '',
    nickname: '',
    agreeToTerms: false
  });

  // 错误状态
  const [errors, setErrors] = useState<FormErrors>({});

  // 密码可见性状态
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 验证码状态
  const [captchaState, setCaptchaState] = useState({
    loading: false,
    countdown: 0
  });

  const [isFormValid, setIsFormValid] = useState(false);

  // 表单校验
  useEffect(() => {
    setIsFormValid(
      !!form.phone &&
      !!form.captcha &&
      !!form.nickname &&
      !!form.password &&
      !!form.confirmPassword &&
      form.agreeToTerms &&
      !errors.phone &&
      !errors.captcha &&
      !errors.nickname &&
      !errors.password
    );
  }, [form, errors]);

  // 处理表单输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({
      ...prev,
      [name]: value
    }));

    // 清除相关错误消息
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }

    // 检查密码强度
    if (name === 'password' && value) {
      const strength = checkPasswordStrength(value);
      setErrors(prev => ({
        ...prev,
        passwordStrength: strength
      }));
    }
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // 验证用户名
    if (!form.username) {
      newErrors.username = '请输入用户名';
    } else if (!/^[\u4e00-\u9fa5a-zA-Z0-9]{2,12}$/.test(form.username)) {
      newErrors.username = '用户名只能包含中文、英文、数字，长度2-12位';
    }

    // 验证手机号
    if (!form.phone) {
      newErrors.phone = '请输入手机号';
    } else if (!/^1[3-9]\d{9}$/.test(form.phone)) {
      newErrors.phone = '请输入有效的手机号';
    }

    // 验证验证码
    if (!form.captcha&&process.env.ENABLE_PHONE_VERIFICATION === 'true') {
      newErrors.captcha = '请输入验证码';
    }

    // 验证密码
    if (!form.password) {
      newErrors.password = '请输入密码';
    } else if (form.password.length < 8) {
      newErrors.password = '密码长度不能小于8位';
    } else if (form.password.length > 32) {
      newErrors.password = '密码长度不能超过32位';
    }

    // 验证确认密码
    if (!form.confirmPassword) {
      newErrors.confirmPassword = '请确认密码';
    } else if (form.confirmPassword !== form.password) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }

    // 验证同意条款
    if (!form.agreeToTerms) {
      newErrors.agreeToTerms = '请阅读并同意隐私政策和服务协议';
    }

    setErrors(prev => ({
      ...prev,
      ...newErrors
    }));

    return Object.keys(newErrors).length === 0;
  };

  // 处理验证码请求
  const handleCaptchaRequest = async (phone: string) => {
    try {
      // 设置加载状态
      setCaptchaState(prev => ({
        ...prev,
        loading: true
      }));

      // 验证手机号
      if (!phone) {
        setErrors(prev => ({
          ...prev,
          phone: '请输入手机号'
        }));
        setCaptchaState(prev => ({ ...prev, loading: false }));
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(phone)) {
        setErrors(prev => ({
          ...prev,
          phone: '请输入有效的手机号'
        }));
        setCaptchaState(prev => ({ ...prev, loading: false }));
        return;
      }

      // 如果提供了onCaptchaRequest回调，则使用它
      if (onCaptchaRequest) {
        await onCaptchaRequest(phone);
        // 验证成功后开始倒计时
        let countdown = 60;
        setCaptchaState({
          loading: false,
          countdown
        });

        const timer = setInterval(() => {
          countdown -= 1;
          if (countdown <= 0) {
            clearInterval(timer);
          }
          setCaptchaState(prev => ({
            ...prev,
            countdown
          }));
        }, 1000);
        return;
      }

      // 否则使用默认实现
      const result = await auth.sendVerificationCode(phone);

      if (result.success) {
        // 开始倒计时
        let countdown = 60;
        setCaptchaState({
          loading: false,
          countdown
        });

        const timer = setInterval(() => {
          countdown -= 1;
          if (countdown <= 0) {
            clearInterval(timer);
          }
          setCaptchaState(prev => ({
            ...prev,
            countdown
          }));
        }, 1000);

        // 显示成功通知
        if (onRegisterSuccess) {
          onRegisterSuccess({
            type: 'success',
            message: '验证码已发送到您的手机'
          });
        }
      } else {
        setCaptchaState(prev => ({
          ...prev,
          loading: false
        }));

        setErrors(prev => ({
          ...prev,
          captcha: result.message || '发送验证码失败'
        }));
      }
    } catch (error) {
      setCaptchaState({
        loading: false,
        countdown: 0
      });

      setErrors(prev => ({
        ...prev,
        captcha: error instanceof Error ? error.message : '发送验证码失败，请稍后重试'
      }));
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);

      // 验证验证码
      if (process.env.ENABLE_PHONE_VERIFICATION === 'true') {
        const verifyResult = await auth.verifyCode(form.phone, form.captcha, 'register');
        if (!verifyResult.success) {
          setErrors({
            ...errors,
            captcha: verifyResult.message || '验证码验证失败'
          });
          return;
        }
      }

      // const verifyResult = await auth.verifyCode(form.phone, form.captcha, 'register');
      // if (!verifyResult.success) {
      //   setErrors({
      //     ...errors,
      //     captcha: verifyResult.message || '验证码验证失败'
      //   });
      //   return;
      // }

      // 调用注册API
      const { username, phone, password,captcha } = form;
      const result = await auth.register({ username, phone, password,verifyCode:captcha});

      if (result.success) {
        // 注册成功
        if (onRegisterSuccess) {
          onRegisterSuccess({
            type: 'success',
            message: '注册成功，请登录'
          });
        }

        // 清空表单
        setForm({
          username: '',
          phone: '',
          password: '',
          confirmPassword: '',
          captcha: '',
          nickname: '',
          agreeToTerms: false
        });
      } else {
        // 注册失败
        setErrors({
          ...errors,
          submit: result.error || '注册失败，请稍后重试'
        });
      }
    } catch (error) {
      setErrors({
        ...errors,
        submit: error instanceof Error ? error.message : '注册失败，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuccess = (notification: Notification) => {
    if (onRegisterSuccess) {
      onRegisterSuccess(notification);
    }
  };

  return (
    <form className={`register-form ${className}`} onSubmit={handleSubmit}>
      <div className="form-group">
        <label>用户名</label>
        <input
          type="text"
          name="username"
          value={form.username}
          onChange={handleChange}
          placeholder="请输入用户名"
        />
        {errors.username && (
          <span className="simple-error-message">{errors.username}</span>
        )}
      </div>

      <div className="form-group">
        <label>手机号</label>
        <div className="input-group">
          <input
            type="tel"
            name="phone"
            value={form.phone}
            onChange={handleChange}
            placeholder="请输入手机号"
          />
          <CaptchaHandler
            phone={form.phone}
            type="register"
            onCaptchaRequest={handleCaptchaRequest}
            countdown={captchaState.countdown}
            isLoading={captchaState.loading}
          />
        </div>
        {errors.phone && (
          <span className="simple-error-message">{errors.phone}</span>
        )}
      </div>

      <div className="form-group">
        <label>验证码</label>
        <input
          type="text"
          name="captcha"
          placeholder="请输入验证码"
          value={form.captcha}
          onChange={handleChange}
        />
        {errors.captcha && (
          <span className="simple-error-message">{errors.captcha}</span>
        )}
      </div>

      <div className="form-group">
        <label>密码</label>
        <div className="password-input">
          <input
            type={passwordVisible ? 'text' : 'password'}
            name="password"
            placeholder="请输入密码"
            value={form.password}
            onChange={handleChange}
          />
          <button
            type="button"
            className={`toggle-password ${passwordVisible ? 'visible' : ''}`}
            onClick={() => setPasswordVisible(prev => !prev)}
          />
        </div>
        {errors.password && (
          <span className="simple-error-message">{errors.password}</span>
        )}

        {/* 密码强度显示 */}
        {form.password && (
          <PasswordStrength strength={errors.passwordStrength} />
        )}
      </div>

      <div className="form-group">
        <label>确认密码</label>
        <div className="password-input">
          <input
            type={confirmPasswordVisible ? 'text' : 'password'}
            name="confirmPassword"
            placeholder="请确认密码"
            value={form.confirmPassword}
            onChange={handleChange}
          />
          <button
            type="button"
            className={`toggle-password ${confirmPasswordVisible ? 'visible' : ''}`}
            onClick={() => setConfirmPasswordVisible(prev => !prev)}
          />
        </div>
        {errors.confirmPassword && (
          <span className="simple-error-message">{errors.confirmPassword}</span>
        )}
      </div>

      {errors.submit && (
        <div className="submit-error">
          <ErrorMessage
            type="auth"
            message={errors.submit}
            showIcon={false}
            className=""
            onRetry={() => setErrors(prev => ({...prev, submit: undefined}))}
            remainingAttempts={0}
            lockoutTime={0}
            simple={true}
          />
        </div>
      )}

      {/* 隐私政策和服务协议同意选项 */}
      <div className="form-footer">
        <label className="remember-me" style={{ position: 'relative', paddingLeft: '24px', cursor: 'pointer' }}>
          <input
            type="checkbox"
            name="agreeToTerms"
            checked={form.agreeToTerms}
            onChange={(e) => {
              const { name, checked } = e.target;
              setForm(prev => ({
                ...prev,
                [name]: checked
              }));
              // 清除相关错误消息
              if (errors[name as keyof FormErrors]) {
                setErrors(prev => ({
                  ...prev,
                  [name]: undefined
                }));
              }
            }}
            style={{ position: 'absolute', left: 0, top: '50%', transform: 'translateY(-50%)', zIndex: 1, opacity: 0, width: '16px', height: '16px', margin: 0, padding: 0 }}
          />
          <span className="custom-checkbox" style={{ position: 'absolute', left: 0, top: '50%', transform: 'translateY(-50%)', width: '16px', height: '16px', border: '1.5px solid var(--border-color)', borderRadius: '2px', background: 'var(--bg-primary)', display: 'flex', alignItems: 'center', justifyContent: 'center', pointerEvents: 'none' }}>
            {form.agreeToTerms && (
              <span className="checkmark" style={{ color: '#FF6B6B', fontSize: '1rem', fontWeight: 'bold', fontFamily: 'inherit', lineHeight: 1, pointerEvents: 'none' }}>✓</span>
            )}
          </span>
          <span style={{ marginLeft: '5px', fontSize: '12px', color: 'var(--text-secondary)' }}>
            我已阅读并同意
            <a href="/privacy" target="_blank" style={{ color: 'var(--brand-primary)', textDecoration: 'none', margin: '0 2px' }}>《隐私政策》</a>、
            <a href="/terms" target="_blank" style={{ color: 'var(--brand-primary)', textDecoration: 'none', margin: '0 2px' }}>《服务协议》</a>
          </span>
        </label>
        {errors.agreeToTerms && (
          <span className="simple-error-message" style={{ marginTop: '4px', width: '100%' }}>{errors.agreeToTerms}</span>
        )}
      </div>

      <button
        className="submit-btn"
        type="submit"
        disabled={isLoading}
      >
        <span>{isLoading ? '注册中...' : '注册账号'}</span>
      </button>
    </form>
  );
};

export default RegisterForm;
