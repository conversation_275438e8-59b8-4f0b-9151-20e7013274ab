const { BILLING_CONFIG } = require('../../config/billing');
const FlowTask = require('../../models/FlowTask');
const Task = require('../../models/Task');
const { CreditTransaction, CreditBalance } = require('../../modules/admin/credits/credit.model');
const Plan = require('../../modules/admin/subscribe/plan.model');
const Subscription = require('../../modules/admin/subscribe/subscription.model');
const { createError } = require('../../utils/error');

class CreditService {
  /**
   * 消费用户算力
   * @param {string} userId 用户ID
   * @param {string} featureName 功能名称
   * @param {string} subType 子类型(可选)
   * @param {string} description 消费描述
   * @param {string} taskId 任务ID(可选)
   * @param {string} deviceToken 设备Token(可选)
   */
  static async consumeCredits(userId, featureName, subType = null, description = '', taskId = null, deviceToken = null, count = 1) {
    try {
      // 并发校验（如有需要）
      if (deviceToken) {
        const checkResult = await this.checkUserBalance(userId, featureName, subType, deviceToken, count);
        if (!checkResult.canExecute) {
          console.log('并发校验失败');
          throw createError(checkResult.code || 400, checkResult.message || '并发校验失败');
        }
      }
      const arr = subType.split('.');
      // 获取功能对应的算力消费值
      const creditCost = this.getBillingCost(arr[0], arr.length >1 ? arr[1] : '') * count;
      if (creditCost <= 0) {
        console.log(`无效的功能计费配置: ${featureName}`);
        throw createError(400, `无效的功能计费配置: ${featureName}`);
      }

      // 查找用户算力余额
      const creditBalance = await CreditBalance.findOne({ user: userId });
      if (!creditBalance) {
        console.log('未找到用户算力余额');
        throw createError(404, '未找到用户算力余额');
      }

      // 检查余额是否充足
      if (creditBalance.balance < creditCost) {
        console.log('算力余额不足');
        throw createError(402, '算力余额不足');
      }

      // 创建交易记录
      const transaction = new CreditTransaction({
        user: userId,
        type: 'consume',
        amount: -creditCost,
        balance: creditBalance.balance - creditCost,
        description: description || `使用${featureName}功能消费${creditCost}算力`,
        metadata: {
          taskId: taskId
        }
      });

      // 更新余额
      creditBalance.balance -= creditCost;
      creditBalance.totalConsumed += creditCost;
      console.log('更新余额开始==================');
      let lastError;
      for (let i = 0; i < 3; i++) {
        try {
          await Promise.all([
            transaction.save(),
            creditBalance.save()
          ]);
          lastError = null;
          break; // 成功则跳出循环
        } catch (err) {
          lastError = err;
          // 检查是否是连接问题，必要时重连
          if (err.message && err.message.match(/ECONN|not connected|connection|network|topology/i)) {
            const mongoose = require('mongoose');
            if (mongoose.connection.readyState !== 1) {
              try {
                await mongoose.disconnect();
              } catch (_) {}
              await mongoose.connect(process.env.MONGODB_URI, {
                useNewUrlParser: true,
                useUnifiedTopology: true
              });
            }
          }
          // 等待一会再重试
          await new Promise(res => setTimeout(res, 500 * (i + 1)));
        }
      }
      if (lastError) throw lastError;
      console.log('更新余额成功==================');

      return {
        cost: creditCost,
        balance: creditBalance.balance,
        transaction: transaction
      };
    } catch (error) {
      throw createError(500, '算力扣除失败', error);
    }
  }

  // 检查用户余额是否可以执行功能
  static async checkUserBalance(userId, featureName, subType = null, deviceToken = null, count = 1) {
    try {
      // 1. 查用户订阅（不限制状态，因为我们要自动更新）
      let subscription = await Subscription.findOne({
        user: userId
      }).sort({ createdAt: -1 });

      // 如果找到订阅，自动更新状态
      if (subscription) {
        const actualStatus = await Subscription.updateSubscriptionStatus(subscription._id);
        if (actualStatus) {
          // 重新获取更新后的订阅
          subscription = await Subscription.findById(subscription._id);
        }
      }

      // 检查是否有有效的活跃订阅
      const hasValidSubscription = subscription &&
        subscription.status === 'active' &&
        subscription.endDate > new Date();

      if (!hasValidSubscription) {
        return { canExecute: false, code: 403, message: '无有效订阅', type: 'subscription_required' };
      }

      // 2. 查最新订阅计划
      const plan = await Plan.findOne({ code: subscription.plan });
      if (!plan) {
        return { canExecute: false, code: 404, message: '订阅计划不存在', type: 'plan_not_found' };
      }
      // 3. 权限校验、并发校验都用 plan.features/plan.usageQuota
      const maxConcurrent = plan.usageQuota?.maxConcurrentTasks || 1;
      if (!deviceToken) {
        return { canExecute: false, code: 400, message: '缺少设备Token', type: 'device_token_required' };
      }
      const runningCount = await FlowTask.countDocuments({
        userId,
        deviceToken,
        status: { $in: ['pending', 'processing'] }
      });
      if (runningCount >= maxConcurrent) {
        return {
          canExecute: false,
          code: 429,
          message: `该设备已达最大并发任务数(${maxConcurrent})，请稍后再试`,
          type: 'concurrent_limit_exceeded',
          currentRunning: runningCount,
          maxAllowed: maxConcurrent
        };
      }
      // 4. 功能权限校验（用plan.features）
      let feature = subType;
      if(subType === 'matting.tab1' || subType === 'matting.tab2'){
        feature = 'matting';
      }
      let hasAccess = true;
      if (this._isDesignFeature(feature)) {
        hasAccess = plan.features.design[feature] && plan.features.design.enabled;
      } else if (this._isModelFeature(feature)) {
        hasAccess = plan.features.model[feature] && plan.features.model.enabled;
      } else if (this._isToolFeature(feature)) {
        hasAccess = plan.features.tools[feature] && plan.features.tools.enabled;
      }
      if (!hasAccess) {
        return {
          canExecute: false,
          code: 403,
          message: `当前订阅计划不包含此功能：${featureName}`,
          type: 'feature_not_allowed',
          currentPlan: plan.code,
          requiredFeature: feature,
          featureName: featureName
        };
      }
      // 5. 其它校验（如算力余额等，仍可查用户余额表）
      const creditBalance = await CreditBalance.findOne({ user: userId });
      if (!creditBalance) {
        return {
          canExecute: false,
          code: 404,
          message: '未找到用户算力余额',
          type: 'balance_not_found'
        };
      }
      const arr = subType.split('.');
      const creditCost = this.getBillingCost(arr[0], arr.length > 1 ? arr[1] : '') * count;
      if (creditCost <= 0) {
        return {
          canExecute: false,
          code: 400,
          message: `无效的功能计费配置: ${featureName}`,
          type: 'invalid_billing_config'
        };
      }
      if (creditBalance.balance < creditCost) {
        return {
          canExecute: false,
          code: 402,
          message: '算力余额不足',
          type: 'insufficient_balance',
          currentBalance: creditBalance.balance,
          requiredCost: creditCost,
          shortfall: creditCost - creditBalance.balance
        };
      }
      // 6. 所有检查通过
      return {
        canExecute: true,
        code: 200,
        message: '可以执行任务',
        type: 'success',
        currentRunning: runningCount,
        maxAllowed: maxConcurrent,
        plan: plan.code,
        currentBalance: creditBalance.balance,
        requiredCost: creditCost,
        remainingBalance: creditBalance.balance - creditCost
      };
    } catch (error) {
      return { canExecute: false, code: 500, message: '检查失败：' + error.message, type: 'system_error' };
    }
  }

  /**
   * 判断是否为设计功能
   * @private
   */
  static _isDesignFeature(feature) {
    const designFeatures = ['trending', 'optimize', 'inspiration','drawing'];
    return designFeatures.includes(feature);
  }

  /**
   * 判断是否为模特功能
   * @private
   */
  static _isModelFeature(feature) {
    const modelFeatures = ['fashion', 'try-on', 'change-model', 'recolor', 'background', 'virtual', 'detail-migration', 'hand-fix','change-posture'];
    return modelFeatures.includes(feature);
  }

  /**
   * 判断是否为工具功能
   * @private
   */
  static _isToolFeature(feature) {
    const toolFeatures = ['extract', 'upscale', 'matting', 'extend', 'inpaint'];
    return toolFeatures.includes(feature);
  }

  /**
   * 从工作流名称提取功能类型
   * @private
   */
  static _extractFeatureFromWorkflow(workflowName) {
    // 工作流名称格式: B01-fashion, C01-extract 等
    const match = workflowName.match(/[A-Z]\d+-(.+)/);
    if (match && match[1]) {
      return match[1];
    }
    // 如果不是标准格式，直接返回原名称
    return workflowName;
  }

  /**
   * 获取功能计费值
   */
  static getBillingCost(featureName, subType = null) {
    const config = BILLING_CONFIG[featureName];
    if (typeof config === 'number') {
      return config;
    } else if (typeof config === 'object' && config !== null) {
      if (subType && config[subType] !== undefined) {
        return config[subType];
      }
      return config._default || 0;
    }
    return 0;
  }

  /**
   * 返还算力
   * @param {string} userId 用户ID
   * @param {string} taskId 任务ID
   * @param {string} description 返还描述
   * @returns {Promise<Object>} 返还结果
   */
  static async refundCredits(userId, taskId, description = '') {
    try {
      // 查找该任务相关的算力消费记录
      const transaction = await CreditTransaction.findOne({
        'metadata.taskId': taskId,
        type: 'consume'
      }).sort({ createdAt: -1 });

      if (!transaction) {
        throw createError(404, '未找到相关的算力消费记录');
      }

      // 查找用户算力余额
      const creditBalance = await CreditBalance.findOne({ user: userId });
      if (!creditBalance) {
        throw createError(404, '未找到用户算力余额');
      }

      // 计算返还金额
      const refundAmount = Math.abs(transaction.amount);
      const newBalance = creditBalance.balance + refundAmount;

      // 更新余额
      creditBalance.balance = newBalance;
      creditBalance.totalConsumed -= refundAmount;
      creditBalance.updatedAt = new Date();
      await creditBalance.save();

      // 创建返还交易记录
      const refundTransaction = new CreditTransaction({
        user: userId,
        amount: refundAmount,
        type: 'refund',
        balance: newBalance,
        description: description || `任务失败返还算力: ${taskId}`,
        metadata: {
          taskId: taskId
        },
        createdAt: new Date()
      });

      await refundTransaction.save();

      return {
        success: true,
        refundAmount,
        newBalance,
        transaction: refundTransaction
      };
    } catch (error) {

    }
  }
}

module.exports = CreditService;