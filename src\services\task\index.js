/**
 * 任务服务
 * 处理任务创建、更新、查询等业务逻辑
 */

import { createTask, deleteTask, getTasks, getTaskById } from '../../api/task';
import { handleError } from '../error';
import { createTaskData, createImageComponent, createComponent, setMainImageSource, isImagelessTaskType } from './taskFactory';

/**
 * 创建任务服务
 * @param {Object} taskData - 任务数据
 * @returns {Promise<Object>} 创建结果
 */
export const createTaskService = async (taskData) => {
  try {
    return await createTask(taskData);
  } catch (error) {
    return handleError(error, '创建任务');
  }
};

/**
 * 使用工厂模式创建页面任务
 * @param {string} pageType - 页面类型，如'matting', 'background'等
 * @param {string} taskType - 任务类型，如'mattingbg', 'background'等
 * @param {Object} baseData - 基础数据，包含userId, panels, components等通用信息
 * @param {Object} pageSpecificData - 页面特定的数据
 * @returns {Promise<Object>} 创建的任务
 */
export const createPageTask = async (pageType, taskType, baseData, pageSpecificData = {}) => {
  try {
    // 处理面板数据，提取组件信息
    const processedBaseData = { ...baseData };
    
    // 移除可能导致MongoDB索引冲突的id字段
    if ('id' in processedBaseData && (processedBaseData.id === null || processedBaseData.id === undefined)) {
      console.log('在createPageTask中移除空的id字段，防止MongoDB索引冲突');
      delete processedBaseData.id;
    }
    
    // 检查任务是否需要图片
    const requiresImage = baseData.requiresImage !== undefined ? 
      !!baseData.requiresImage : 
      !isImagelessTaskType(pageType, taskType);
    
    // 设置requiresImage标志
    processedBaseData.requiresImage = requiresImage;
    
    // 如果任务需要图片且没有提供components，则从panels创建components
    if (requiresImage && !processedBaseData.components && processedBaseData.panels) {
      const components = {};
      
      // 遍历面板数组，转换为组件格式
      if (Array.isArray(processedBaseData.panels)) {
        processedBaseData.panels.forEach((panel, index) => {
          // 根据panel类型创建不同的组件，使用componentType而非type
          if (panel.type === 'image' || panel.type === 'sourceImage') {
            // 对于图片类型面板，创建图片组件
            const componentKey = `sourceImagePanel${index > 0 ? index + 1 : ''}`;
            const sourceImagePanel = createImageComponent('sourceImagePanel', {
              ...panel,
              componentType: 'sourceImagePanel'  // 显式设置标准字段
            }, index === 0);
            components[componentKey] = sourceImagePanel;
          } else if (panel.type === 'clothing') {
            // 对于服装类型面板
            components['clothingPanel'] = createImageComponent('clothingPanel', {
              ...panel,
              componentType: 'clothingPanel'  // 显式设置标准字段
            }, pageType === 'try-on');
          } else if (panel.type === 'model') {
            // 对于模特类型面板
            components['modelPanel'] = createImageComponent('modelPanel', {
              ...panel,
              componentType: 'modelPanel'  // 显式设置标准字段
            }, pageType !== 'try-on');
          } else if (panel.type === 'pattern') {
            // 对于图案类型面板
            components['patternPanel'] = createImageComponent('patternPanel', {
              ...panel,
              componentType: 'patternPanel'  // 显式设置标准字段
            }, false);
          } else if (panel.type === 'seed') {
            // 对于随机种子选择器
            components['randomSeedSelector'] = createComponent('randomSeedSelector', {
              ...panel,
              componentType: 'randomSeedSelector'  // 显式设置标准字段
            });
          } else if (panel.type === 'quantity') {
            // 对于数量选择器
            components['quantityPanel'] = createComponent('quantityPanel', {
              ...panel,
              componentType: 'quantityPanel'  // 显式设置标准字段
            });
          } else {
            // 对于其他未知类型，创建通用组件
            const genericType = panel.type || 'genericPanel';
            components[`${genericType}${index + 1}`] = createComponent(genericType, {
              ...panel,
              componentType: genericType  // 显式设置标准字段
            });
          }
        });
      }
      
      // 确保至少有一个组件被设置为主图片
      let hasMainImage = false;
      for (const key in components) {
        if (components[key].isMainImage) {
          hasMainImage = true;
          break;
        }
      }
      
      // 如果没有主图片，根据页面类型设置默认主图片
      if (!hasMainImage && Object.keys(components).length > 0) {
        if (pageType === 'try-on' && components['clothingPanel']) {
          components['clothingPanel'].isMainImage = true;
        } else if (components['sourceImage']) {
          components['sourceImage'].isMainImage = true;
        } else if (components['modelPanel']) {
          components['modelPanel'].isMainImage = true;
        } else {
          // 如果没有找到合适的主图片，使用第一个图片组件
          for (const key in components) {
            if (components[key].componentType.includes('Panel') && 
                components[key].url) {
              components[key].isMainImage = true;
              break;
            }
          }
        }
      }
      
      processedBaseData.components = components;
    } else if (!requiresImage) {
      // 如果任务不需要图片，确保有一个空的components对象
      processedBaseData.components = processedBaseData.components || {};
      
      // 对于不需要图片的任务，可能需要添加其他类型的组件
      if (pageType === 'textToImage' || taskType === 'text2img') {
        // 例如：文本到图片的任务需要文本输入组件
        if (!processedBaseData.components['textInput']) {
          processedBaseData.components['textInput'] = createComponent('TextInputPanel', {
            name: '文本输入',
            text: processedBaseData.text || ''
          });
        }
      } else if (pageType === 'chat' || taskType === 'chat') {
        // 聊天任务需要消息组件
        if (!processedBaseData.components['chatMessages']) {
          processedBaseData.components['chatMessages'] = createComponent('ChatMessagesPanel', {
            name: '聊天消息',
            messages: processedBaseData.messages || []
          });
        }
      }
    }
    
    // 使用工厂函数创建任务数据
    const taskData = createTaskData(pageType, taskType, processedBaseData, pageSpecificData);
    
    // 调用API创建任务
    return await createTask(taskData, processedBaseData.userId);
  } catch (error) {
    return handleError(error, `创建${pageType}任务`);
  }
};

/**
 * 删除任务服务
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteTaskService = async (taskId) => {
  try {
    return await deleteTask(taskId);
  } catch (error) {
    return handleError(error, '删除任务');
  }
};

/**
 * 获取任务列表服务
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 任务列表
 */
export const getTasksService = async (params) => {
  try {
    return await getTasks(params);
  } catch (error) {
    return handleError(error, '获取任务列表');
  }
};

/**
 * 获取单个任务详情服务
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务详情
 */
export const getTaskByIdService = async (taskId) => {
  try {
    return await getTaskById(taskId);
  } catch (error) {
    return handleError(error, '获取任务详情');
  }
};

/**
 * 设置任务中的主图片
 * @param {Object} task - 任务对象
 * @param {string} componentKey - 要设置为主图片的组件键
 * @returns {Object} 更新后的任务对象
 */
export const setTaskMainImageSource = (task, componentKey) => {
  if (!task || !task.components) return task;
  
  // 使用setMainImageSource函数更新组件
  const updatedComponents = setMainImageSource(task.components, componentKey);
  
  // 返回更新后的任务
  return {
    ...task,
    components: updatedComponents
  };
};

// 创建任务服务对象
export const taskService = {
  createTaskService,
  createPageTask,
  deleteTaskService,
  getTasksService,
  getTaskByIdService,
  setTaskMainImageSource
};

export default taskService; 