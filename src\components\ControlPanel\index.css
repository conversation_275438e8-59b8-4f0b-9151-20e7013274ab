/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/tabs.css';
@import '../../styles/scrollbars.css';

.control-panel {
  width: 28%;
  flex-shrink: 0;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-right: 12px;
  position: relative;
  overflow: hidden;
  padding: 0;
  min-width: 380px;
}

.control-header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 20px 0px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  flex-shrink: 0;
}

.control-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
  padding-bottom: 120px;
  composes: custom-scrollbar;
}

.control-content > * {
  flex-shrink: 0;
}

.control-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0 20px 20px;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  box-shadow: none;
  z-index: 10;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.generate-btn {
  width: 100%;
  padding: 12px 0;
  background: var(--brand-gradient);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-md);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--transition-normal);
}

/* 添加光效扫过效果 */
.generate-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s;
}

.generate-btn:hover:not(:disabled)::after {
  left: 100%;
}

.generate-btn:hover:not(:disabled) {
  background: var(--brand-gradient);
}

.generate-btn:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.7;
}

.generate-btn:disabled::after {
  display: none;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .control-panel {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 12px;
    min-width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  .control-content {
    padding: 0 16px 20px;
    gap: 12px;
    box-sizing: border-box;
  }
  
  .control-footer {
    padding: 0 16px 20px;
    box-sizing: border-box;
  }
}

/* 480px以下进一步优化 */
@media (max-width: 480px) {
  .control-panel {
    margin-bottom: 8px;
    margin-right: 0 !important;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  .control-content {
    padding: 0 12px 20px;
    gap: 10px;
    box-sizing: border-box;
  }
  
  .control-footer {
    padding: 0 12px 20px;
    box-sizing: border-box;
  }
  
  .control-header {
    padding: 20px 0px 0;  /* 保持与PC端一致的padding */
    margin-bottom: 8px;
    box-sizing: border-box;
  }
}

/* 360px以下极致优化 */
@media (max-width: 360px) {
  .control-panel {
    margin-right: 0 !important;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  .control-content {
    padding: 0 8px 20px;
    gap: 8px;
    box-sizing: border-box;
  }
  
  .control-footer {
    padding: 0 8px 20px;
    box-sizing: border-box;
  }
  
  .control-header {
    padding: 20px 0px 0;  /* 保持与PC端一致的padding */
    margin-bottom: 6px;
    box-sizing: border-box;
  }
}

/* 开发中提示样式 */
.coming-soon {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
  color: var(--text-secondary);
}

.coming-soon-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 24px;
  opacity: 0.8;
}

.coming-soon h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin: 0 0 12px;
  font-weight: 500;
}

.coming-soon p {
  font-size: var(--font-size-md);
  color: var(--text-tertiary);
  margin: 0;
  line-height: 1.5;
} 