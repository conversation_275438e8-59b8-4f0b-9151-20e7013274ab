const express = require('express');
const router = express.Router();
const creditController = require('./credit.controller');
const { auth } = require('../../../middleware/auth.middleware');

// 所有路由都需要用户登录
router.use(auth);

// 获取用户算力余额
router.get('/credits', creditController.getUserCreditBalance);

// 获取用户算力交易记录
router.get('/credits/transactions', creditController.getUserCreditTransactions);

module.exports = router; 