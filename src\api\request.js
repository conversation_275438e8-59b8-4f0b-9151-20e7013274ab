import axios from 'axios';
import { message } from 'antd';
import { useAuth } from '../contexts/AuthContext';

// 创建axios实例
console.log(process.env);
const instance = axios.create({
  baseURL:  process.env.REACT_APP_BACKEND_URL ,
  timeout: 10000, // 缩短超时时间到10秒
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    // 如果有token则添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 设置登录请求的超时时间 - 仅作为安全网
    if (config.url && config.url.includes('/auth/login')) {
      config.timeout = 3000; // 登录请求3秒超时（安全网）
    }
    if(config.url.includes('/oss/upload')){
      config.headers['Content-Type'] = 'multipart/form-data';
    } else if(config.url.includes('/oss/uploads')){
      config.headers['Content-Type'] = 'multipart/form-data';
    }
    
    // 设置登录请求的自定义验证处理
    if (config.url && config.url.includes('/auth/login') && config.method === 'post') {
      // 为登录请求添加预验证
      let loginData;
      
      // 根据config.data的类型适当处理
      if (typeof config.data === 'string') {
        try {
          loginData = JSON.parse(config.data);
        } catch (e) {
          return Promise.reject({
            status: 400,
            message: '请求数据格式错误'
          });
        }
      } else if (typeof config.data === 'object') {
        loginData = config.data;
      } else {
        loginData = {};
      }
      
      const { loginId, password } = loginData;
      
      // 如果没有提供用户名或密码，立即拒绝请求
      if (!loginId || !password) {
        return Promise.reject({
          status: 400,
          message: '请输入用户名和密码'
        });
      }
      
      // 密码长度验证
      if (password.length < 8) {
        return Promise.reject({
          status: 400,
          message: '密码长度不能小于8位'
        });
      }
      
      if (password.length > 32) {
        return Promise.reject({
          status: 400,
          message: '密码长度不能超过32位'
        });
      }
      
      // 用户名格式验证
      if (loginId.length < 2) {
        return Promise.reject({
          status: 400,
          message: '用户名格式不正确'
        });
      }
    }
    
    return config;
  },
  (error) => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 直接返回响应数据
    return response.data;
  },
  (error) => {
    console.error('响应错误:', error);
    
    // 处理错误响应
    if (error.response) {
      // 服务器返回了错误状态码
      switch (error.response.status) {
        case 401:
          // 检查错误信息是否与登录相关
          if (error.config && error.config.url && error.config.url.includes('/auth/login')) {
            // 登录时的401错误表示账号或密码错误
            message.error('账号或密码错误', 1); // 1秒显示
            return Promise.reject({
              status: 401,
              message: '账号或密码错误'
            });
          } else {
            // 获取后端返回的具体错误消息
            const serverMessage = error.response.data?.message;
            
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('tokenExpires');
            const CSRF_TOKEN_KEY = 'csrf_token';
            // 清除CSRF令牌
            localStorage.removeItem(CSRF_TOKEN_KEY);
            
            // 优先使用后端返回的消息，如果没有则使用默认消息
            const displayMessage = serverMessage || '登录已过期，请重新登录';
            message.error(displayMessage);
            
            setTimeout(() => {
              window.location.href = '/';
            }, 1000);
          }
          break;
          
        case 403:
          message.error('没有权限访问此资源');
          break;
          
        case 404:
          break;
          
        case 500:
        case 502:
        case 503:
        case 504:
          message.error('服务器错误，请稍后再试');
          break;
          
        default:
          // 尝试从响应中提取错误消息
          let errorMsg = error.response.data?.message || error.response.data?.error || '请求失败';
          message.error(errorMsg);
      }
      
      return Promise.reject(error.response.data);
    }
    
    if (error.request) {
      // 请求已发出，但没有收到响应
      if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
        // 登录请求超时可能是网络问题或服务器问题
        if (error.config && error.config.url && error.config.url.includes('/auth/login')) {
          // 尝试解析是否为账号密码错误引起的超时
          console.log('登录请求超时，尝试提示可能的密码错误');
          message.error('账号或密码错误', 1); // 1秒显示
          return Promise.reject({
            status: 401,
            message: '账号或密码错误'
          });
        } else {
          message.error('请求超时，请检查网络连接');
          return Promise.reject({
            status: 408,
            message: '请求超时，请检查网络连接'
          });
        }
      } else {
        message.error('网络错误，请检查网络连接');
        return Promise.reject({
          status: 0,
          message: '网络错误，请检查网络连接'
        });
      }
    } else {
      // 请求设置时发生错误
      message.error('请求错误: ' + error.message);
      return Promise.reject({
        status: 400,
        message: error.message || '请求错误'
      });
    }
  }
);

// 获取当前用户ID
export const getCurrentUserId = () => {
  // 从localStorage获取用户信息
  const userStr = localStorage.getItem('user');
  if (!userStr) return null;
  
  try {
    const user = JSON.parse(userStr);
    return user.id || null;
  } catch (e) {
    console.error('解析用户信息失败:', e);
    return null;
  }
};

// 封装请求方法
const request = {
  get: (url, params = {}) => instance.get(url, { params }),
  post: (url, data) => instance.post(url, data),
  put: (url, data) => instance.put(url, data),
  delete: (url) => instance.delete(url),
  patch: (url, data) => instance.patch(url, data),
  // 上传文件 60秒超时
  upload: (url, data) => instance.post(url, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000
  }),
  // 执行flow 18分钟超时
  flow: (url, data) => instance.post(url, data, {
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 18*60000
  })
};

// 添加API请求跟踪
function logApiRequest(method, url, data) {
  if (url.includes('/tasks/')) {
    console.log(`【API请求跟踪】${method.toUpperCase()} ${url}`, data || '');
  }
}

// 添加API响应跟踪
function logApiResponse(method, url, response, startTime) {
  if (url.includes('/tasks/')) {
    const duration = Date.now() - startTime;
    console.log(`【API响应跟踪】${method.toUpperCase()} ${url} - 耗时 ${duration}ms`, 
      response ? 
        `状态: ${response.success ? '成功' : '失败'}, 数据大小: ${JSON.stringify(response).length}字节` 
        : '');
  }
}

export default request; 