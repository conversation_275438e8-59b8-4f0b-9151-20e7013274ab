import React, { useState, useEffect, memo, useRef, useCallback } from 'react';
import { MdClose, MdZoomOutMap } from 'react-icons/md';
import BaseModal from '../common/BaseModal';
import ImagePreviewModal from '../common/ImagePreviewModal';
import '../../styles/close-buttons.css';
import './index.css';
import { 
  GENDER_CATEGORIES, 
  AGE_CATEGORIES, 
  REGION_CATEGORIES, 
  BODY_TYPE_CATEGORIES,
  SUIT_TYPES,
  STYLES
} from '../../data/categories';
import PropTypes from 'prop-types';

// 标签与实际传递给后端内容的映射关系
export const tagMappings = {
  // 性别映射
  gender: {
    '女性': 'photograph of one woman',
    '男性': 'photograph of one man'
  },
  // 年龄映射
  age: {
    '随机': '',
    '成人': 'adult',
    '少年': 'teenager',
    '儿童': 'child',
    '婴幼儿': 'toddler'
  },
  // 地区映射
  region: {
    '随机': '',
    '欧美': 'European, white skin',
    '东亚': 'East Asian, yellow skin',
    '非洲': 'african, black skin',
    '拉美': 'latin american, brown skin',
    '中东': 'middle eastern, dark skin'
  },
  // 身材映射
  bodyType: {
    '随机': '',
    '匀称型': 'proportional body',
    '运动型': 'athletic body',
    '丰满型': 'curvy body'
  },
  // 泳装类型映射
  suitType: {
    '随机': '',
    '分体泳衣（比基尼、套装泳衣）': 'bikini, swimsuit set',
    '连体泳衣（一件式泳衣）': 'one-piece swimsuit',
    '长袖泳衣': 'long sleeve swimsuit',
    '和服罩衫': 'bikini, kimono cover-up',
    '裹裙': 'bikini, long slim fit transparent tulle skirt'
  },
  // 风格映射
  style: {
    '随机': '',
    '度假': 'vacation style',
    '甜美': 'sweet style',
    '性感': 'sexy style',
    '节日派对': 'festive party style',
    '简约': 'minimalist style',
    '黑人': 'afro style',
    '成熟': 'mature style',
    '复古': 'vintage style',
    '街头': 'street style',
    '运动': 'sporty style',
    '民族': 'ethnic style',
    '暗黑': 'dark gothic style',
    '废土': 'post-apocalyptic style',
    '中式传统': 'traditional chinese style'
  }
};

// 骨架屏组件
const AttributesSkeleton = () => (
  <div className="card-skeleton">
    {Array(12).fill(null).map((_, index) => (
      <div key={index} className="card-skeleton-item">
        <div className="card-skeleton-image"></div>
        <div className="card-skeleton-caption">
          <div className="card-skeleton-text"></div>
          <div className="card-skeleton-button"></div>
        </div>
      </div>
    ))}
  </div>
);

// 空状态组件
const EmptyAttributes = () => (
  <div className="empty-attributes">
    <img src="https://file.aibikini.cn/config/icons/coming-soon.png" alt="即将推出" className="coming-soon-icon" />
    <h3>服装属性设置功能即将推出</h3>
    <p>我们正在开发更全面的服装属性设置功能，敬请期待！</p>
  </div>
);

const ClothingAttributesModal = ({ 
  onClose, 
  onSelect, 
  savedSettings,
  onSettingsChange,
  pageType = 'inspiration' // 默认为灵感探索页面
}) => {
  const [activeTab, setActiveTab] = useState('attributes');
  const [selectedAttributeId, setSelectedAttributeId] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [previewImage, setPreviewImage] = useState(null);
  const [textareaHeight, setTextareaHeight] = useState(150);
  const [isDragging, setIsDragging] = useState(false);
  const isDraggingRef = useRef(false);
  const startYRef = useRef(0);
  const startHeightRef = useRef(0);
  const textareaRef = useRef(null);
  const resizeHandleRef = useRef(null);
  const [isModalDragging, setIsModalDragging] = useState(false);
  const [modalDragOffset, setModalDragOffset] = useState({ x: 0, y: 0 });
  const modalRef = useRef(null);
  const dragStartMouse = useRef({ x: 0, y: 0 });
  const dragStartTransform = useRef({ x: 0, y: 0 });
  const [dragTransform, setDragTransform] = useState({ x: 0, y: 0 });

  // 从savedSettings中恢复数据
  const [attributeFilters, setAttributeFilters] = useState(
    savedSettings?.attributeFilters || {
      gender: '女性',
      age: '随机',
      region: '随机',
      bodyType: '随机',
      suitType: '随机',
      style: '随机'
    }
  );

  // 从savedSettings中恢复描述文本 - 合并正负面提示词
  const [promptText, setPromptText] = useState((savedSettings?.description || '') + 
    (savedSettings?.negativeDescription ? '\n\n' + savedSettings?.negativeDescription : ''));

  // 检测滚动条并调整拖动按钮位置
  const checkScrollbarAndAdjustHandle = useCallback(() => {
    if (textareaRef.current && resizeHandleRef.current) {
      const textarea = textareaRef.current;
      const handle = resizeHandleRef.current;
      
      // 检测是否有垂直滚动条
      const hasScrollbar = textarea.scrollHeight > textarea.clientHeight;
      
      if (hasScrollbar) {
        // 计算滚动条的实际宽度
        const scrollbarWidth = textarea.offsetWidth - textarea.clientWidth;
        // 拖动按钮与滚动条保持3px距离
        const rightPosition = scrollbarWidth + 3;
        handle.style.right = `${rightPosition}px`;
      } else {
        // 无滚动条时，恢复原位置
        handle.style.right = '3px';
      }
    }
  }, []);
  
  // 当外部savedSettings变化时，更新本地状态
  useEffect(() => {
    const newPromptText = (savedSettings?.description || '') + 
      (savedSettings?.negativeDescription ? '\n\n' + savedSettings?.negativeDescription : '');
    setPromptText(newPromptText);
  }, [savedSettings]);

  // 监听文本内容和高度变化，检测滚动条
  useEffect(() => {
    checkScrollbarAndAdjustHandle();
  }, [promptText, textareaHeight, checkScrollbarAndAdjustHandle]);

  // 使用 ResizeObserver 监听 textarea 尺寸变化
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const resizeObserver = new ResizeObserver(() => {
      checkScrollbarAndAdjustHandle();
    });

    resizeObserver.observe(textarea);

    return () => {
      resizeObserver.disconnect();
    };
  }, [checkScrollbarAndAdjustHandle]);

  // 处理拖动事件（鼠标）
  const handleMouseMove = useCallback((e) => {
    if (!isDraggingRef.current) return;
    const deltaY = e.clientY - startYRef.current;
    const newTextareaHeight = Math.max(80, Math.min(300, startHeightRef.current + deltaY));
    setTextareaHeight(newTextareaHeight);
  }, []);

  const handleMouseUp = useCallback(() => {
    if (!isDraggingRef.current) return;
    isDraggingRef.current = false;
    setIsDragging(false);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    setTimeout(checkScrollbarAndAdjustHandle, 0);
  }, [handleMouseMove, checkScrollbarAndAdjustHandle]);

  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    isDraggingRef.current = true;
    startYRef.current = e.clientY;
    startHeightRef.current = textareaHeight;
    setIsDragging(true);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [textareaHeight, handleMouseMove, handleMouseUp]);

  // === 新增：处理拖动事件（触摸） ===
  const handleTouchMove = useCallback((e) => {
    if (!isDraggingRef.current) return;
    if (!e.touches || e.touches.length === 0) return;
    const touch = e.touches[0];
    const deltaY = touch.clientY - startYRef.current;
    const newTextareaHeight = Math.max(80, Math.min(300, startHeightRef.current + deltaY));
    setTextareaHeight(newTextareaHeight);
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (!isDraggingRef.current) return;
    isDraggingRef.current = false;
    setIsDragging(false);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
    setTimeout(checkScrollbarAndAdjustHandle, 0);
  }, [handleTouchMove, checkScrollbarAndAdjustHandle]);

  const handleTouchStart = useCallback((e) => {
    if (!e.touches || e.touches.length === 0) return;
    e.preventDefault();
    const touch = e.touches[0];
    isDraggingRef.current = true;
    startYRef.current = touch.clientY;
    startHeightRef.current = textareaHeight;
    setIsDragging(true);
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  }, [textareaHeight, handleTouchMove, handleTouchEnd]);
  
  // 清理函数 - 使用useEffect确保组件卸载时移除事件监听器
  useEffect(() => {
    return () => {
      if (isDraggingRef.current) {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      }
    };
  }, [handleMouseMove, handleMouseUp]);

  // 添加 ESC 键监听
  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === 'Escape') {
        if (previewImage) {
          setPreviewImage(null);
        } else {
          onClose?.();
        }
      }
    };

    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [onClose, previewImage]);
  
  // 模拟加载完成
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 300);
    return () => clearTimeout(timer);
  }, []);

  const handleFilterChange = (filterType, value) => {
    setAttributeFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handlePromptChange = (e) => {
    setPromptText(e.target.value);
    
    // 延迟检测滚动条，确保DOM已更新
    setTimeout(checkScrollbarAndAdjustHandle, 0);
  };

  const handleSaveAttributes = () => {
    // 获取所有选中的标签，排除"随机"选项
    const tags = Object.entries(attributeFilters)
      .filter(([_, value]) => value && value !== '随机')
      .map(([_, value]) => value);
    
    // 根据选中的标签，生成映射后的英文描述词
    const mappedTags = Object.entries(attributeFilters)
      .filter(([key, value]) => value && value !== '随机')
      .map(([key, value]) => tagMappings[key][value])
      .filter(tag => tag); // 过滤掉空字符串
      
    // 合并所有映射后的标签，生成一个描述字符串
    let mappedDescription = mappedTags.join(', ');
    
    // 如果有用户输入的描述词，将其也添加到映射描述中
    if (promptText.trim()) {
      // 如果已经有标签描述，则用逗号分隔
      if (mappedDescription) {
        mappedDescription += ', ' + promptText.trim();
      } else {
        mappedDescription = promptText.trim();
      }
    }
    
    // 只有在有标签或提示词的情况下才保存
    if (tags.length > 0 || promptText.trim()) {
      const settings = {
        type: 'custom',
        description: promptText,
        negativeDescription: '',
        image: 'https://file.aibikini.cn/config/icons/inspiration-direction-active.png',
        attributeFilters,
        attributes: {
          tags,
          description: promptText,
          negativeDescription: '',
          // 添加映射后的描述
          mappedTags,
          mappedDescription
        }
      };
      
      onSettingsChange?.(settings);
      onSelect?.(settings);
    }
    
    onClose?.();
  };

  const handleClear = () => {
    setAttributeFilters({
      gender: '女性',
      age: '随机',
      region: '随机',
      bodyType: '随机',
      suitType: '随机',
      style: '随机'
    });
    setPromptText('');
    onSettingsChange?.(null);
    onSelect?.(null);
  };

  const footer = (
    <>
      <button 
        className="clear-btn"
        onClick={handleClear}
      >
        清空内容
      </button>
      <button 
        className="save-settings-btn"
        onClick={handleSaveAttributes}
        disabled={Object.values(attributeFilters).every(val => !val || val === '随机') && !promptText.trim()}
      >
        确认设置
      </button>
    </>
  );

  const renderContent = () => {
    if (isLoading) {
      return <AttributesSkeleton />;
    }
    
    return (
      <div className="clothing-attributes-content">
        {/* 说明文本 */}
        <div className="attribute-description" style={{
          padding: '0',
          margin: '0',
          textAlign: 'center',
          color: 'var(--text-secondary)',
          borderBottom: '1px solid var(--border-color-light)',
          fontSize: '14px'
        }}>
          选择服装属性，为灵感探索设定一些方向
        </div>
        
        {/* 人群部分 */}
        <div className="filter-section">
          <h3 className="section-title">选择人群</h3>
          
          {/* 性别选择 */}
          <div className="filter-group">
            <label>性别</label>
            <div className="filter-options">
              {GENDER_CATEGORIES.map((gender) => (
                <button 
                  key={gender}
                  className={`filter-option ${attributeFilters.gender === gender ? 'active' : ''}`}
                  onClick={() => handleFilterChange('gender', gender)}
                >
                  {gender}
                </button>
              ))}
            </div>
          </div>
          
          {/* 年龄选择 */}
          <div className="filter-group">
            <label>年龄</label>
            <div className="filter-options">
              <button 
                className={`filter-option ${attributeFilters.age === '随机' ? 'active' : ''}`}
                onClick={() => handleFilterChange('age', '随机')}
              >
                随机
              </button>
              {AGE_CATEGORIES.map((age) => (
                <button 
                  key={age}
                  className={`filter-option ${attributeFilters.age === age ? 'active' : ''}`}
                  onClick={() => handleFilterChange('age', age)}
                >
                  {age}
                </button>
              ))}
            </div>
          </div>
          
          {/* 地区选择 */}
          <div className="filter-group">
            <label>地区</label>
            <div className="filter-options">
              <button 
                className={`filter-option ${attributeFilters.region === '随机' ? 'active' : ''}`}
                onClick={() => handleFilterChange('region', '随机')}
              >
                随机
              </button>
              {REGION_CATEGORIES.map((region) => (
                <button 
                  key={region}
                  className={`filter-option ${attributeFilters.region === region ? 'active' : ''}`}
                  onClick={() => handleFilterChange('region', region)}
                >
                  {region}
                </button>
              ))}
            </div>
          </div>
          
          {/* 身材选择 */}
          <div className="filter-group">
            <label>身材</label>
            <div className="filter-options">
              <button 
                className={`filter-option ${attributeFilters.bodyType === '随机' ? 'active' : ''}`}
                onClick={() => handleFilterChange('bodyType', '随机')}
              >
                随机
              </button>
              {BODY_TYPE_CATEGORIES.map((bodyType) => (
                <button 
                  key={bodyType}
                  className={`filter-option ${attributeFilters.bodyType === bodyType ? 'active' : ''}`}
                  onClick={() => handleFilterChange('bodyType', bodyType)}
                >
                  {bodyType}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* 款式部分 */}
        <div className="filter-section">
          <h3 className="section-title">选择款式</h3>
          
          {/* 泳装类型选择 */}
          <div className="filter-group">
            <div className="filter-options">
              <button 
                className={`filter-option ${attributeFilters.suitType === '随机' ? 'active' : ''}`}
                onClick={() => handleFilterChange('suitType', '随机')}
              >
                随机
              </button>
              {SUIT_TYPES.map((suitType) => (
                <button 
                  key={suitType}
                  className={`filter-option ${attributeFilters.suitType === suitType ? 'active' : ''}`}
                  onClick={() => handleFilterChange('suitType', suitType)}
                >
                  {suitType}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* 风格部分 */}
        <div className="filter-section">
          <h3 className="section-title">选择风格</h3>
          
          {/* 风格选择 */}
          <div className="filter-group">
            <div className="filter-options">
              <button 
                className={`filter-option ${attributeFilters.style === '随机' ? 'active' : ''}`}
                onClick={() => handleFilterChange('style', '随机')}
              >
                随机
              </button>
              {STYLES.map((style) => (
                <button 
                  key={style}
                  className={`filter-option ${attributeFilters.style === style ? 'active' : ''}`}
                  onClick={() => handleFilterChange('style', style)}
                >
                  {style}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* 描述提示词部分 */}
        <div className="filter-section">
          <h3 className="section-title">
            描述提示词（非必填）
            {(savedSettings?.description || savedSettings?.negativeDescription) && (
              <span style={{ 
                fontSize: '13px', 
                color: 'var(--brand-primary)', 
                marginLeft: '8px',
                fontWeight: 'normal' 
              }}>
                (已回填上次提示词)
              </span>
            )}
          </h3>
          <div className="prompt-input">
            <textarea
              placeholder="描述您期望和不期望的服装属性特征，例如：比基尼、亮片装饰、露背设计、荷叶边，不要褪色、不要老旧款式等..."
              rows={6}
              className="prompt-textarea"
              value={promptText}
              onChange={handlePromptChange}
              style={{ height: textareaHeight }}
              ref={textareaRef}
            />
            <div 
              className="textarea-resize-handle" 
              title="拖动调整高度"
              onMouseDown={handleMouseDown}
              onTouchStart={handleTouchStart}
              ref={resizeHandleRef}
            >
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 拖动事件
  const handleModalMouseDown = (e) => {
    if (e.target.closest('.modal-header')) {
      setIsModalDragging(true);
      // 记录拖动起始点和当前变换位置
      dragStartMouse.current = { x: e.clientX, y: e.clientY };
      dragStartTransform.current = { ...dragTransform };
      const modal = modalRef.current;
      if (modal) {
        document.body.classList.add('no-select');
        modal.classList.add('dragging');
      }
      e.preventDefault();
    }
  };

  const handleModalMouseMove = useCallback((e) => {
    if (isModalDragging) {
      const deltaX = e.clientX - dragStartMouse.current.x;
      const deltaY = e.clientY - dragStartMouse.current.y;
      const newX = dragStartTransform.current.x + deltaX;
      const newY = Math.max(-dragStartTransform.current.y, dragStartTransform.current.y + deltaY); // 只限制顶部边界
      setDragTransform({ x: newX, y: newY });
    }
  }, [isModalDragging]);

  const handleModalMouseUp = useCallback(() => {
    if (isModalDragging) {
      setIsModalDragging(false);
      document.body.classList.remove('no-select');
      const modal = modalRef.current;
      if (modal) {
        modal.classList.remove('dragging');
      }
    }
  }, [isModalDragging]);

  useEffect(() => {
    if (isModalDragging) {
      document.addEventListener('mousemove', handleModalMouseMove);
      document.addEventListener('mouseup', handleModalMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleModalMouseMove);
        document.removeEventListener('mouseup', handleModalMouseUp);
      };
    }
  }, [isModalDragging, handleModalMouseMove, handleModalMouseUp]);

  return (
    <>
      <div className="clothing-attributes-modal">
        <BaseModal
          className="model-select-modal"
          title="服装属性设置"
          tabs={[
            { key: 'attributes', label: '服装属性' }
          ]}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          onClose={onClose}
          footer={footer}
          size="large"
          dragRef={modalRef}
          dragStyle={{ 
            transform: `translate(${dragTransform.x}px, ${dragTransform.y}px)`,
            cursor: isModalDragging ? 'grabbing' : 'default' 
          }}
          onDragMouseDown={handleModalMouseDown}
        >
          <div className="modal-body">
            {renderContent()}
          </div>
        </BaseModal>
      </div>

      {/* 使用新的独立ImagePreviewModal组件 */}
      <ImagePreviewModal
        visible={!!previewImage}
        imageUrl={previewImage}
        onClose={() => setPreviewImage(null)}
        alt="属性预览"
        showHint={true}
        maxScale={4}
        minScale={0.5}
      />
    </>
  );
};

ClothingAttributesModal.propTypes = {
  onClose: PropTypes.func.isRequired,
  onSelect: PropTypes.func,
  savedSettings: PropTypes.object,
  onSettingsChange: PropTypes.func,
  pageType: PropTypes.string // 添加pageType属性
};

export default ClothingAttributesModal; 
