.theme-toggle {
  /* 继承自sidebar-bottom-btn，样式写在主CSS中 */
  margin-bottom: 0.25rem !important;
}

/* 独立使用的主题切换按钮样式（备用） */
.theme-toggle-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.theme-toggle-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.theme-toggle-btn svg {
  font-size: 1.15rem;
  color: var(--text-secondary);
} 