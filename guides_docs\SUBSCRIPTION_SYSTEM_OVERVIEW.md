# AIBIKINI 订阅系统概述

## 系统架构

AIBIKINI 订阅系统是一个基于 MongoDB 的 SaaS 订阅管理系统，采用 MVC 架构模式。

### 核心组件

1. **数据模型层 (Models)**
   - `Plan`: 订阅计划模型
   - `Subscription`: 用户订阅模型
   - `User`: 用户模型（包含订阅信息）

2. **控制器层 (Controllers)**
   - `plan.controller.js`: 订阅计划管理
   - `subscription.controller.js`: 用户订阅管理

3. **路由层 (Routes)**
   - `plan.routes.js`: 订阅计划 API 路由
   - `subscription.routes.js`: 用户订阅 API 路由

4. **配置层 (Config)**
   - `billing.js`: 计费标准配置
   - `constants.js`: 系统常量定义

## 订阅计划类型

| 计划类型 | 代码 | 描述 | 主要功能 |
|---------|------|------|----------|
| 免费版 | `free` | 基础功能免费使用 | 基础工具功能 |
| 设计版 | `design` | 专注于设计功能 | 款式设计、优化、灵感探索 |
| 模特版 | `model` | 专注于模特功能 | 模特换装、换模特、虚拟模特 |
| 完整版 | `full` | 包含所有功能 | 设计+模特+高级支持 |
| 企业版 | `enterprise` | 企业级定制 | 所有功能+企业级支持 |

## 功能权限矩阵

| 功能模块 | 免费版 | 设计版 | 模特版 | 完整版 | 企业版 |
|---------|--------|--------|--------|--------|--------|
| 基础工具 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 款式设计 | ❌ | ✅ | ❌ | ✅ | ✅ |
| 模特功能 | ❌ | ❌ | ✅ | ✅ | ✅ |
| 高级支持 | ❌ | ❌ | ❌ | ✅ | ✅ |
| 企业支持 | ❌ | ❌ | ❌ | ❌ | ✅ |

## 计费系统

### 算力值单位
系统使用算力值 (C) 作为计费单位，不同功能有不同的扣费标准：

- **设计功能**: 30C (爆款开发、款式优化、灵感探索)
- **模特功能**: 20-150C (根据功能复杂度)
- **工具功能**: 10-60C (基础工具)
- **视频功能**: 300-400C (高级功能)

### 使用量配额
- **总请求数**: 限制用户的总使用次数
- **每日请求数**: 限制用户的每日使用次数
- **剩余请求数**: 实时跟踪用户剩余可用次数

## 订阅状态管理

| 状态 | 描述 | 功能访问 |
|------|------|----------|
| `pending` | 待激活 | 无访问权限 |
| `active` | 活跃 | 正常访问 |
| `expired` | 已过期 | 无访问权限 |
| `canceled` | 已取消 | 无访问权限 |

## 开发文件结构

```
server/src/modules/admin/subscribe/
├── plan.model.js              # 订阅计划数据模型
├── plan.controller.js          # 订阅计划控制器
├── plan.routes.js             # 订阅计划路由
├── subscription.model.js       # 用户订阅数据模型
├── subscription.controller.js  # 用户订阅控制器
└── subscription.routes.js      # 用户订阅路由

server/src/config/
├── billing.js                 # 计费配置
└── constants.js               # 系统常量

server/src/services/
└── credits/
    └── creditService.js       # 积分/算力值服务
```

## 核心 API 接口

### 订阅计划管理
- `GET /api/plans/public` - 获取公开订阅计划
- `GET /api/plans/code/:code` - 根据代码获取计划
- `POST /api/plans` - 创建订阅计划 (管理员)
- `PUT /api/plans/:id` - 更新订阅计划 (管理员)
- `DELETE /api/plans/:id` - 删除订阅计划 (管理员)

### 用户订阅管理
- `POST /api/subscriptions/check-access` - 检查功能访问权限
- `GET /api/subscriptions/user/:userId` - 获取用户订阅信息
- `POST /api/subscriptions` - 创建用户订阅 (管理员)
- `PUT /api/subscriptions/:id` - 更新用户订阅 (管理员)
- `POST /api/subscriptions/:id/cancel` - 取消订阅 (管理员)
- `GET /api/subscriptions/stats` - 获取订阅统计 (管理员)

## 数据流程

1. **用户注册** → 自动创建免费订阅
2. **功能使用** → 检查权限 → 扣除算力值
3. **订阅升级** → 更新功能权限 → 调整使用配额
4. **订阅过期** → 自动禁用功能 → 发送通知
5. **自动续费** → 检查支付状态 → 延长订阅期

## 安全机制

1. **权限验证**: 基于 JWT 的身份验证
2. **角色控制**: 管理员/用户权限分离
3. **使用量限制**: 防止滥用和超量使用
4. **数据验证**: 输入数据格式和范围验证
5. **审计日志**: 记录所有订阅相关操作 