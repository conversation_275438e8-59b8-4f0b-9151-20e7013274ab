# 删除按钮标准化实施指南

## 背景说明

在我们的应用中，目前在各个功能页面中的"清除无效记录"和"删除全部记录"按钮行为不一致：

1. 在模特换装页面(try-on)中，点击这些按钮会出现多次提醒，但能够正常删除任务
2. 在自动抠图页面(matting)中，虽然没有多次确认，但点击确认后无法正常清除任务
3. 其他功能页面可能也存在类似问题

这些按钮是GenerationArea组件的一部分，但它们依赖于各页面提供的`handleDeleteTask`函数来实际执行删除操作。

## 问题原因

问题主要有两个方面：

1. **组件级别**：GenerationArea组件中的"删除全部记录"和"清除无效记录"按钮执行删除任务时没有使用异步方式，导致大量任务删除时界面卡顿和多次提醒
2. **页面级别**：各页面的`handleDeleteTask`函数实现不一致，有些没有正确处理`skipConfirm`参数，或者API调用顺序不同

## 标准化修改

我们需要在两个层面上进行修改：

### 1. 组件级修改（已完成）

GenerationArea组件中的"删除全部记录"和"清除无效记录"按钮的修改已经完成，所有使用该组件的页面都会自动继承这些改进：

- 使用异步方式处理批量删除
- 添加延迟避免并发请求过多
- 提供更清晰的用户反馈
- 确保有确认弹窗保护用户操作

### 2. 页面级修改（需要在每个功能页面实施）

每个功能页面的`handleDeleteTask`函数需要标准化，确保它们：

- 正确处理`skipConfirm`参数
- 遵循统一的删除流程
- 提供一致的用户体验

## 修改步骤

对于每个功能页面，请按照以下步骤修改：

### 步骤1：检查页面是否使用GenerationArea组件

确认该页面是否使用了GenerationArea组件并传递了`onDeleteTask`属性。如果是，则需要继续修改。

### 步骤2：标准化handleDeleteTask函数

找到页面中的`handleDeleteTask`函数，并按照以下模板修改：

```javascript
const handleDeleteTask = (taskId, skipConfirm = false) => {
  // 定义删除任务的函数
  const deleteTaskFunction = async () => {
    try {
      // 获取当前用户ID，如果未登录则使用开发者ID
      const userId = getCurrentUserId() || 'developer'; 
      
      // 显示操作中的消息
      message.loading({ content: '正在删除...', key: `delete-${taskId}` });
      
      // 先从本地状态中移除，提供即时反馈
      setGenerationTasks(prev => prev.filter(task => task.taskId !== taskId));
      
      // 调用API进行删除
      const success = await deleteTask(taskId, userId);
      
      if (success) {
        message.success({ content: '记录已删除', key: `delete-${taskId}` });
      } else {
        // 如果API返回失败但UI已更新，只在控制台记录警告
        console.warn('API删除任务可能失败，但前端已更新状态');
      }
    } catch (error) {
      console.error('删除任务失败:', error);
      message.error({ 
        content: '删除任务失败: ' + error.message,
        key: `delete-${taskId}`
      });
      
      // 即使API调用失败，仍从本地状态中移除
      setGenerationTasks(prev => prev.filter(task => task.taskId !== taskId));
    }
  };
  
  // 如果跳过确认，直接删除
  if (skipConfirm) {
    deleteTaskFunction();
    return;
  }
  
  // 否则显示确认对话框
  showDeleteConfirmModal({
    title: '确认删除',
    content: '确定要删除这条生成记录吗？删除后将无法恢复。',
    okText: '确认删除',
    cancelText: '取消',
    okButtonProps: {
      danger: true,
    },
    animation: false,
    transitionName: '',
    maskTransitionName: '',
    onOk: deleteTaskFunction
  });
};
```

### 步骤3：确保引入所需依赖

确保已引入所需的依赖：

```javascript
import { message } from 'antd';
import { showDeleteConfirmModal } from '../../utils/modalUtils';
import { deleteTask } from '../../api/task';
import { getCurrentUserId } from '../../api';
```

### 步骤4：确保GenerationArea组件使用正确

确认GenerationArea组件调用时传入了正确的props：

```jsx
<GenerationArea
  activeTab={activeTab}
  onTabChange={setActiveTab}
  tasks={Array.isArray(generationTasks) ? generationTasks : []} // 统一使用数组检查
  onEditTask={handleEditTask}
  onDownloadImage={handleDownloadImage}
  onViewDetails={handleViewDetails}
  onBatchDownload={handleBatchDownload}
  onDeleteTask={handleDeleteTask} // 确保传入了修改后的handleDeleteTask函数
  pageType="[当前页面类型]" // 确保使用正确的页面类型
/>
```

## 关键注意事项

1. **状态更新顺序**：先更新UI状态，后调用API，这样能提供更好的用户体验
2. **错误处理**：确保即使API调用失败，UI状态也能正确更新
3. **skipConfirm参数**：这个参数非常重要，允许"清除无效记录"和"删除全部记录"按钮绕过单个任务的确认提示
4. **消息通知**：使用唯一的key(`delete-${taskId}`)避免消息堆叠或闪烁，确保使用完全一致的提示文本
5. **异步处理**：确保所有删除操作都是异步的，避免界面卡顿
6. **统一配置参数**：所有showDeleteConfirmModal调用必须包含完全相同的动画相关参数
7. **任务数据类型检查**：传递给GenerationArea的tasks属性必须包含数组类型检查

## 测试验证

修改完成后，请测试以下功能点：

1. 单击任务卡片上的"删除记录"按钮，确认显示确认弹窗
2. 点击"清除无效记录"按钮，确认只删除无效任务且有确认弹窗
3. 点击"删除全部记录"按钮，确认所有任务被删除且有确认弹窗
4. 确认删除操作执行过程中有适当的加载状态提示
5. 确认任务成功删除后有成功提示

## 重要的代码解释

### "删除全部记录"按钮逻辑

此按钮现在使用了异步处理和Promise.all来管理多个删除操作，确保界面不会因为大量删除请求而卡顿：

```javascript
showDeleteConfirmModal({
  title: '删除全部记录',
  content: '确定要删除全部生成记录吗？删除后将无法恢复。',
  onOk: async () => {
    message.loading({ content: '正在删除所有记录...', key: 'deleteAll' });
    
    const promises = tasks.map(task => {
      return new Promise(resolve => {
        setTimeout(() => {
          onDeleteTask(task.taskId, true);
          resolve();
        }, 100);
      });
    });
    
    await Promise.all(promises);
    message.success({ content: '所有记录已删除', key: 'deleteAll' });
  },
  okButtonProps: { danger: true },
  animation: false,
  transitionName: '',
  maskTransitionName: ''
});
```

### "清除无效记录"按钮逻辑

与"删除全部记录"类似，但只处理无效任务：

```javascript
const invalidTasks = tasks.filter(task => {
  const isMatchingPageType = task.pageType === pageType;
  if (!isMatchingPageType) return false;
  
  return task.status !== 'completed' || 
         !task.generatedImages || 
         task.generatedImages.length === 0 || 
         !task.generatedImages.some(img => img && img.url);
});

// 类似的异步处理和确认逻辑
```

## 结论

通过标准化删除按钮的行为，我们可以确保用户在所有功能页面中都有一致的体验。这些修改不仅提高了用户体验，也使代码更加健壮和可维护。

## 标准化检查清单

在完成修改后，使用此清单检查是否完全符合标准：

- [ ] handleDeleteTask函数包含skipConfirm参数
- [ ] 删除操作使用异步函数实现
- [ ] 先更新UI状态，后调用API
- [ ] 为loading和success消息使用唯一key
- [ ] 使用统一的消息文本
- [ ] GenerationArea组件包含数组类型检查
- [ ] showDeleteConfirmModal包含所有必需的动画相关参数
- [ ] 错误处理确保UI状态始终更新 