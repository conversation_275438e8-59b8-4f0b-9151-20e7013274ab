/* 导入统一面板样式 */
@import '../../styles/panels.css';

/* 确保selected-model-preview有正确的定位上下文 */
.selected-model-preview {
  position: relative;
  background-image: linear-gradient(45deg, #d1d1d1 25%, transparent 25%),
                    linear-gradient(-45deg, #d1d1d1 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #d1d1d1 75%),
                    linear-gradient(-45deg, transparent 75%, #d1d1d1 75%);
  background-size: 12px 12px;
  background-position: 0 0, 0 6px, 6px -6px, -6px 0px;
  background-color: var(--bg-primary);
}
[data-theme="dark"] .selected-model-preview {
  background-image: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                    linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
  background-size: 12px 12px;
  background-position: 0 0, 0 6px, 6px -6px, -6px 0px;
  background-color: var(--bg-primary);
}

/* 处理状态指示器样式，确保在selected-model-preview内正确居中 */
.selected-model-preview .processing-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 88px;
  height: 88px;
  color: #666;
}

.processing-indicator span {
  font-size: 13px;
  color: #666;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #FF3C6A;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态指示器样式，确保在selected-model-preview内正确居中 */
.selected-model-preview .error-indicator {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff2f0;
  gap: 8px;
}

.error-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #ff4d4f;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
}

.error-indicator span:last-child {
  font-size: 13px;
  color: #ff4d4f;
}

/* 展开按钮激活状态样式 */
.expand-btn.active {
  background: transparent;
}

.expand-btn.active span {
  border-color: #FF3C6A;
}

.expand-btn.active:hover {
  background: transparent;
} 