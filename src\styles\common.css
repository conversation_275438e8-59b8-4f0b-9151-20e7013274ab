/**
 * 通用样式定义
 * 
 * 包含以下类别：
 * 1. 布局工具类 - flex布局相关
 * 2. 间距工具类 - gap, margin, padding
 * 3. 文本工具类 - 字体大小、粗细
 * 4. 颜色工具类 - 文本颜色、背景色
 * 5. 圆角工具类 - 不同大小的圆角
 * 6. 开发中提示样式 - 开发阶段的提示界面
 * 7. 动画效果 - 淡入、滑入等过渡动画
 * 8. 响应式布局 - 移动端适配
 * 9. 定位工具类 - 绝对定位、相对定位等
 * 10. 阴影工具类 - 不同层级的阴影效果
 * 11. 边框工具类 - 边框样式和颜色
 * 12. 溢出控制类 - 内容溢出处理
 * 
 * 使用示例：
 * <div class="flex items-center justify-between gap-16 p-16">
 *   <span class="text-sm text-gray">标题</span>
 *   <div class="bg-white rounded p-16">内容</div>
 * </div>
 */


 @import './theme.css';

/* =============== 布局工具类 =============== */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

/* =============== 间距工具类 =============== */
/* 保持现有间距类 */
.gap-8 { gap: 8px; }
.gap-12 { gap: 12px; }
.gap-16 { gap: 16px; }

.mt-16 { margin-top: 16px; }
.mb-16 { margin-bottom: 16px; }
.p-16 { padding: 16px; }

/* 新增更多间距选项 */
.m-0 { margin: 0; }
.p-0 { padding: 0; }

/* =============== 文本工具类 =============== */
/* 保持现有文本类 */
.text-sm { font-size: 13px; }
.text-base { font-size: 14px; }
.text-lg { font-size: 16px; }
.font-medium { font-weight: 500; }

/* 新增文本对齐方式 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* =============== 颜色工具类 =============== */
/* 保持现有颜色类 */
.text-primary { color: #FF3C6A; }
.text-gray { color: #666; }
.bg-white { background: white; }
.bg-gray { background: #f5f5f5; }

/* 使用CSS变量重写颜色类 */
.text-brand { color: var(--brand-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.bg-primary { background: var(--bg-primary); }
.bg-secondary { background: var(--bg-secondary); }

/* =============== 圆角工具类 =============== */
/* 保持现有圆角类 */
.rounded-sm { border-radius: 4px; }
.rounded { border-radius: 6px; }
.rounded-lg { border-radius: 8px; }

/* 使用CSS变量重写圆角类 */
.radius-sm { border-radius: var(--radius-sm); }
.radius-md { border-radius: var(--radius-md); }
.radius-lg { border-radius: var(--radius-lg); }

/* =============== 开发中提示样式 =============== */
/* 保持现有开发中提示样式不变 */
.coming-soon {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.coming-soon-icon {
  width: 120px;
  height: 120px;
  margin-bottom: var(--spacing-lg);
  opacity: 0.8;
}

.coming-soon h3 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm);
  font-weight: 500;
}

.coming-soon p {
  font-size: var(--font-size-md);
  color: var(--text-tertiary);
  margin: 0;
  line-height: 1.5;
}

/* =============== 通用工具类 =============== */
/* 保持现有工具类 */
.mt-4 { margin-top: 16px; }
.mt-2 { margin-top: 8px; }

/* 新增显示控制类 */
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.invisible { visibility: hidden; }
.visible { visibility: visible; }

/* =============== 动画效果 =============== */
/* 保持现有动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 新增动画类 */
.animate-fade-in { animation: fadeIn 0.2s ease; }
.animate-slide-in { animation: slideIn 0.3s ease; }

/* =============== 响应式布局 =============== */
/* 保持现有响应式类 */
@media (max-width: 768px) {
  .hide-on-mobile { display: none !important; }
}

@media (min-width: 769px) {
  .show-on-mobile { display: none !important; }
}

/* 新增响应式断点 */
@media (max-width: 480px) {
  .hide-on-small { display: none !important; }
}

@media (min-width: 1024px) {
  .hide-on-desktop { display: none !important; }
}

/* =============== 定位工具类 =============== */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* =============== 阴影工具类 =============== */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* =============== 边框工具类 =============== */
.border { border: 1px solid var(--border-color); }
.border-t { border-top: 1px solid var(--border-color); }
.border-r { border-right: 1px solid var(--border-color); }
.border-b { border-bottom: 1px solid var(--border-color); }
.border-l { border-left: 1px solid var(--border-color); }

.border-none { border: none; }
.border-solid { border-style: solid; }
.border-dashed { border-style: dashed; }

/* =============== 溢出控制类 =============== */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.break-words { word-wrap: break-word; }
.break-all { word-break: break-all; }

/* =============== 鼠标样式类 =============== */
.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-wait { cursor: wait; }
.cursor-move { cursor: move; }

/* =============== 过渡效果类 =============== */
.transition { transition: var(--transition-normal); }
.transition-fast { transition: var(--transition-fast); }
.transition-slow { transition: var(--transition-slow); }

/* =============== 不透明度类 =============== */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* =============== 用户选择类 =============== */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

/* 移除开关按钮样式，已迁移到buttons.css */

/* =============== 生成状态指示器样式（页面右下角） - 全局隐藏 =============== */
.generating-status {
  display: none !important; /* 强制隐藏生成状态指示器 */
}

