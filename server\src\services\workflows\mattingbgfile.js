const workflow = {
  prompt: {
    "11": {
      "inputs": {
        "rem_mode": "RMBG-2.0",
        "image_output": "Hide",
        "save_prefix": "mattingbgfile",  // 从rmbgfile改为mattingbgfile
        "torchscript_jit": false,
        "add_background": "none",
        "images": [
          "24",
          0
        ]
      },
      "class_type": "easy imageRemBg",
      "_meta": {
        "title": "Image Remove Bg"
      }
    },
    "24": {
      "inputs": {
        "image": [
          "46",
          0
        ]
      },
      "class_type": "ImpactImageBatchToImageList",
      "_meta": {
        "title": "Image Batch to Image List"
      }
    },
    "36": {
      "inputs": {
        "images": [
          "11",
          0
        ]
      },
      "class_type": "ImageListToImageBatch",
      "_meta": {
        "title": "Image List to Image Batch"
      }
    },
    "43": {
      "inputs": {
        "filename_prefix": "MattingBgFile",  // 从rmbgfile改为mattingbgfile
        "images": [
          "36",
          0
        ]
      },
      "class_type": "SaveImage",
      "_meta": {
        "title": "保存图像"
      }
    },
    "46": {
      "inputs": {
        "folder": "/root/ComfyUI/input/",
        "image_load_cap": 20, // 减少处理容量，只处理上传的图片
        "start_index": 0
      },
      "class_type": "LoadImagesFromFolderKJ",
      "_meta": {
        "title": "Load Images From Folder (KJ)"
      }
    }
  }
};

// 添加额外的元数据，用于帮助后端处理
workflow.metadata = {
  requiresInputFolder: true,  // 标记这个工作流需要使用输入文件夹
  inputFolder: "/input/",     // ComfyUI的输入文件夹路径
  folderNodeId: "46",         // 加载文件夹的节点ID
  saveImageNodeId: "43"       // 保存图像节点的ID，用于在执行前动态修改文件名前缀
};

module.exports = workflow; 