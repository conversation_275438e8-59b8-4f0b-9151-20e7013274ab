.text-popup {
  position: fixed;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-md);
  width: 280px;
  z-index: 100000000;
  opacity: 0;
  transform: translateX(10px);
  animation: popupFadeIn var(--transition-normal);
  pointer-events: none;
}

.text-popup.show {
  pointer-events: auto;
  opacity: 1;
  transform: translateX(0);
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.text-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.text-popup-title {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
}

.text-popup-content {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 模特弹窗特定样式 */
.text-popup.with-tags .popup-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-lg);
}

.text-popup.with-tags .popup-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px var(--spacing-xs);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.text-popup.with-tags .description-section {
  border-top: 1px solid var(--border-light);
  padding-top: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.text-popup.with-tags .section-title {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  font-weight: 500;
  margin: 0 0 var(--spacing-sm) 0;
}

.prompt-section {
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.prompt-section:last-child {
  margin-bottom: 0;
}

.prompt-section strong {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.prompt-section p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  line-height: 1.6;
  word-break: break-all;
}

.text-popup-footer {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-xxs);
  border-top: 1px solid var(--border-light);
  padding-top: var(--spacing-xs);
  margin-top: var(--spacing-md);
}

/* 添加右对齐样式，用于只有一个按钮的情况 */
.text-popup-footer.right-aligned {
  justify-content: flex-end;
}

.text-button {
  background: none;
  border: none;
  padding: var(--spacing-xxs);
  cursor: pointer;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
}

.text-button:hover {
  color: var(--brand-primary);
  transform: none;
}

.text-button svg {
  width: var(--font-size-md);
  height: var(--font-size-md);
}

.copy-text-btn {
  background: none;
  border: none;
  padding: var(--spacing-xxs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-xxs);
  transition: none;
}

.copy-text-btn:hover {
  color: var(--brand-primary);
}

.copy-text-btn svg {
  width: var(--font-size-md);
  height: var(--font-size-md);
}

/* 关闭按钮样式已迁移到统一的close-buttons.css */