/**
 * 日志工具的类型定义
 * 提供不同级别的日志记录功能
 */

interface Logger {
  /**
   * 设置日志级别
   * @param level 日志级别: 'debug', 'info', 'warn', 'error'
   */
  setLevel(level: string): void;
  
  /**
   * 设置自定义日志处理器
   * @param handler 自定义日志处理函数
   */
  setCustomHandler(handler: (level: string, message: string, data?: any) => void): void;
  
  /**
   * 记录调试级别的日志
   * @param message 日志消息
   * @param data 可选的数据对象
   */
  debug(message: string, data?: any): void;
  
  /**
   * 记录信息级别的日志
   * @param message 日志消息
   * @param data 可选的数据对象
   */
  info(message: string, data?: any): void;
  
  /**
   * 记录警告级别的日志
   * @param message 日志消息
   * @param data 可选的数据对象
   */
  warn(message: string, data?: any): void;
  
  /**
   * 记录错误级别的日志
   * @param message 日志消息
   * @param data 可选的数据对象
   */
  error(message: string, data?: any): void;
}

declare const logger: Logger;

export default logger; 