import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Form, Input, Select, DatePicker, InputNumber, Switch, message, Card, Tabs, Row, Col, Statistic, Tag, Space } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ExclamationCircleOutlined, ReloadOutlined, UserOutlined, ClockCircleOutlined, SearchOutlined, FilterOutlined } from '@ant-design/icons';
import api from '../../../api';
import moment from 'moment';
import dayjs from 'dayjs';
import './SubscriptionManagement.css';

const { TabPane } = Tabs;
const { Option } = Select;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

const SubscriptionManagement = () => {
  const [subscriptions, setSubscriptions] = useState([]);
  const [plans, setPlans] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSubscription, setEditingSubscription] = useState(null);
  const [form] = Form.useForm();
  const [planModalVisible, setPlanModalVisible] = useState(false);
  const [editingPlan, setEditingPlan] = useState(null);
  const [planForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('subscriptions');
  const [users, setUsers] = useState([]);
  const [searchParams, setSearchParams] = useState({
    username: '',
    plan: '',
    status: '',
    dateRange: null
  });
  const [searchForm] = Form.useForm();
  const [showFilter, setShowFilter] = useState(false);
  const [userSearchLoading, setUserSearchLoading] = useState(false);
  const [userSearchResults, setUserSearchResults] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 获取订阅列表 - 修复参数格式问题
  const fetchSubscriptions = async (pageNum = 1, pageSize = 10) => {
    setLoading(true);
    try {
      // 构建查询参数
      const params = {
        page: pageNum,
        limit: pageSize
      };
      
      // 只有在有搜索条件时才添加到查询参数
      if (searchParams.username) params.username = searchParams.username;
      if (searchParams.plan) params.plan = searchParams.plan;
      if (searchParams.status) params.status = searchParams.status;
      if (searchParams.dateRange && searchParams.dateRange.length === 2) {
        params.startDate = searchParams.dateRange[0].format('YYYY-MM-DD');
        params.endDate = searchParams.dateRange[1].format('YYYY-MM-DD');
      }
      
      console.log('获取订阅列表参数:', params); // 添加日志，帮助调试

      // 修复：直接将参数作为URL查询字符串，而不是作为params对象传递
      const queryString = Object.entries(params)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');
      
      const response = await api.get(`/subscriptions?${queryString}`);
      console.log('获取订阅列表响应:', response); // 添加日志，帮助调试
      
      // 设置分页数据
      setPagination({
        current: response.page || 1,
        pageSize: response.limit || 10,
        total: response.total || 0
      });
      
      // 设置订阅数据
      setSubscriptions(response.data || []);
    } catch (error) {
      message.error('获取订阅列表失败');
      console.error('获取订阅列表错误:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取计划列表
  const fetchPlans = async () => {
    try {
      const data = await api.get('/plans');
      setPlans(data || []);
    } catch (error) {
      message.error('获取计划列表失败');
      console.error(error);
    }
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const response = await api.get('/subscriptions/stats');
      setStats(response.data);
    } catch (error) {
      message.error('获取统计数据失败');
      console.error(error);
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const response = await api.get('/admin/user');
      setUsers(response.data?.users || []);
    } catch (error) {
      message.error('获取用户列表失败');
      console.error(error);
    }
  };

  // 处理搜索 - 修复参数格式问题
  const handleSearch = async () => {
    try {
      const values = await searchForm.validateFields();
      console.log('搜索表单值:', values); // 添加日志，帮助调试
      
      // 更新搜索参数状态
      setSearchParams(values);
      
      // 搜索时重置到第一页
      setPagination(prev => ({
        ...prev,
        current: 1
      }));
      
      // 构建查询参数
      const params = {
        page: 1,
        limit: pagination.pageSize
      };
      
      // 用户名查询
      if (values.username) {
        params.username = values.username;
      }
      
      // 计划查询
      if (values.plan) {
        params.plan = values.plan;
      }
      
      // 状态查询
      if (values.status) {
        params.status = values.status;
      }
      
      // 日期范围查询
      if (values.dateRange && values.dateRange.length === 2) {
        params.startDate = values.dateRange[0].format('YYYY-MM-DD');
        params.endDate = values.dateRange[1].format('YYYY-MM-DD');
      }
      
      console.log('发送的查询参数:', params); // 添加日志，帮助调试
      
      // 修复：直接将参数作为URL查询字符串，而不是作为params对象传递
      const queryString = Object.entries(params)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');
      
      // 发送请求
      const response = await api.get(`/subscriptions?${queryString}`);
      
      // 设置分页数据
      setPagination({
        current: response.page || 1,
        pageSize: response.limit || 10,
        total: response.total || 0
      });
      
      // 设置订阅数据
      setSubscriptions(response.data || []);
    } catch (error) {
      console.error('搜索表单验证失败:', error);
      message.error('搜索失败，请检查输入');
    }
  };

  // 重置搜索
  const handleResetSearch = () => {
    // 重置表单
    searchForm.resetFields();
    
    // 重置搜索参数
    const resetParams = {
      username: '',
      plan: '',
      status: '',
      dateRange: null
    };
    
    setSearchParams(resetParams);
    
    // 重置分页
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
    
    // 使用空参数重新获取数据
    fetchSubscriptions(1, pagination.pageSize);
  };

  // 初始化数据
  useEffect(() => {
    fetchSubscriptions();
    fetchPlans();
    fetchStats();
    fetchUsers();
  }, []);

  // 处理编辑订阅
  const handleEditSubscription = (subscription) => {
    console.log(subscription);
    setEditingSubscription(subscription);
    
    
    form.setFieldsValue({
      userId: subscription.user._id,
      plan: subscription.plan,
      status: subscription.status,
      dateRange: [dayjs(subscription.startDate), dayjs(subscription.endDate)],
      price: subscription.price,
      autoRenew: subscription.autoRenew,
      paymentMethod: subscription.paymentMethod,
      paymentId: subscription.paymentId
    });
    setModalVisible(true);
  };

  // 处理创建订阅
  const handleCreateSubscription = () => {
    setEditingSubscription(null);
    form.resetFields();
    form.setFieldsValue({
      status: 'pending',
      autoRenew: false,
      paymentMethod: 'manual'
    });
    setModalVisible(true);
  };

  // 处理保存订阅
  const handleSaveSubscription = async () => {
    try {
      const values = await form.validateFields();
      
      // 处理日期范围
      const [startDate, endDate] = values.dateRange;
      // 构建提交数据
      const subscriptionData = {
        userId: values.userId,  // 确保使用正确的字段名
        plan: values.plan,
        status: values.status,
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
        price: values.price || 0,  // 确保价格有默认值
        autoRenew: values.autoRenew || false,
        paymentMethod: values.paymentMethod || 'manual',
        paymentId: values.paymentId || ''
      };
      
      console.log('提交的订阅数据:', subscriptionData);  // 添加日志，帮助调试
      
      if (editingSubscription) {
        await api.put(`/subscriptions/${editingSubscription._id}`, subscriptionData);
        message.success('订阅更新成功');
      } else {
        await api.post('/subscriptions', subscriptionData);
        message.success('订阅创建成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      setEditingSubscription(null);
      fetchSubscriptions();
      fetchStats();  // 更新统计数据
    } catch (error) {
      const errorMsg = error.response?.data?.message || error.message || '保存订阅失败';
      message.error(errorMsg);
      console.error('保存订阅错误:', error);
      
      // 显示更详细的错误信息，帮助调试
      if (error.response?.data) {
        console.error('服务器返回的错误数据:', error.response.data);
      }
    }
  };

  // 处理取消订阅
  const handleCancelSubscription = (id) => {
    confirm({
      title: '确认取消',
      icon: <ExclamationCircleOutlined />,
      content: '确定要取消这个订阅吗？',
      onOk: async () => {
        try {
          await api.post(`/subscriptions/${id}/cancel`);
          message.success('订阅取消成功');
          fetchSubscriptions();
          fetchStats();
        } catch (error) {
          message.error('取消订阅失败');
          console.error(error);
        }
      }
    });
  };

  // 处理删除订阅
  const handleDeleteSubscription = (id) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个订阅吗？此操作不可恢复。',
      onOk: async () => {
        try {
          await api.delete(`/subscriptions/${id}`);
          message.success('订阅删除成功');
          fetchSubscriptions();
          fetchStats();
        } catch (error) {
          message.error('删除订阅失败');
          console.error(error);
        }
      }
    });
  };

  // 处理编辑计划
  const handleEditPlan = (plan) => {
    setEditingPlan(plan);
    planForm.setFieldsValue({
      code: plan.code,
      name: plan.name,
      description: plan.description,
      price: plan.price,
      features: plan.features,
      usageQuota: plan.usageQuota,
      isPublic: plan.isPublic,
      sortOrder: plan.sortOrder,
      isRecommended: plan.isRecommended
    });
    setPlanModalVisible(true);
  };

  // 处理创建计划
  const handleCreatePlan = () => {
    setEditingPlan(null);
    planForm.resetFields();
    planForm.setFieldsValue({
      isPublic: true,
      sortOrder: 0,
      isRecommended: false,
      price: {
        monthly: 0,
        yearly: 0,
        discount: 0
      },
      features: {
        design: {
          enabled: false
        },
        model: {
          enabled: false
        },
        tools: {
          enabled: true,
          upscale: true,
          matting: true,
          extend: true
        },
        support: {
          level: 'standard',
          responseTime: '5x8'
        }
      },
      usageQuota: {
        totalRequests: -1,
        dailyRequests: -1
      }
    });
    setPlanModalVisible(true);
  };

  // 处理删除计划
  const handleDeletePlan = (id) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这个计划吗？如果有用户正在使用此计划，将无法删除。',
      onOk: async () => {
        try {
          await api.delete(`/plans/${id}`);
          message.success('计划删除成功');
          fetchPlans();
        } catch (error) {
          message.error('删除计划失败: ' + (error.response?.data?.message || error.message || '未知错误'));
          console.error(error);
        }
      }
    });
  };

  // 处理保存计划
  const handleSavePlan = async () => {
    try {
      const values = await planForm.validateFields();
      
      // 移除不需要的字段
      const planData = { ...values };
      delete planData.userId; // 删除userId字段，因为后端不需要
      
      if (editingPlan) {
        await api.put(`/plans/${editingPlan._id}`, planData);
        message.success('计划更新成功');
      } else {
        await api.post('/plans', planData);
        message.success('计划创建成功');
      }
      
      setPlanModalVisible(false);
      planForm.resetFields();
      setEditingPlan(null);
      fetchPlans();
    } catch (error) {
      // 增强错误处理，显示更详细的错误信息
      const errorMsg = error.response?.data?.message || error.message || '保存计划失败';
      message.error(errorMsg);
      console.error('保存计划错误详情:', error);
    }
  };

  // 处理模态框取消
  const handleModalCancel = () => {
    setModalVisible(false);
    form.resetFields();
    setEditingSubscription(null);
  };

  // 处理计划模态框取消
  const handlePlanModalCancel = () => {
    setPlanModalVisible(false);
    planForm.resetFields();
    setEditingPlan(null);
  };

  // 用户搜索函数 - 支持手机号查询
  const handleUserSearch = async (value) => {
    if (!value || value.length < 2) {
      setUserSearchResults([]);
      return;
    }
    
    setUserSearchLoading(true);
    try {
      // 使用encodeURIComponent确保查询参数正确编码
      const response = await api.get(`/admin/user/search?query=${encodeURIComponent(value)}`);
      setUserSearchResults(response.users || []);
      
      // 如果没有找到用户，尝试通过手机号查询
      if (response.users.length === 0 && /^\d+$/.test(value)) {
        const phoneResponse = await api.get(`/admin/user/search?phone=${encodeURIComponent(value)}`);
        setUserSearchResults(phoneResponse.users || []);
      }
    } catch (error) {
      console.error('搜索用户失败:', error);
      message.error('搜索用户失败');
    } finally {
      setUserSearchLoading(false);
    }
  };

  // 处理分页变化
  const handleTableChange = (pagination) => {
    fetchSubscriptions(pagination.current, pagination.pageSize);
  };

  // 批量更新过期订阅
  const handleBatchUpdateExpired = async () => {
    try {
      setLoading(true);
      await api.post('/subscriptions/batch-update-expired');
      message.success('过期订阅更新成功');
      fetchSubscriptions();
      fetchStats();
    } catch (error) {
      message.error('批量更新失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 订阅表格列定义
  const subscriptionColumns = [
    {
      title: '用户',
      dataIndex: ['user', 'username'],
      key: 'username',
      sorter: (a, b) => (a.user?.username || '').localeCompare(b.user?.username || ''),
      // 可选：支持筛选
      // filters: [...new Set(subscriptions.map(s => s.user?.username))].map(name => ({ text: name, value: name })),
      // onFilter: (value, record) => record.user?.username === value,
      render: (_, record) => (
        <span>
          {record.user?.username || '未知用户'}
          {record.user?.remark && `（${record.user.remark}）`}
        </span>
      )
    },
    {
      title: '计划',
      dataIndex: 'plan',
      key: 'plan',
      filters: plans.map(plan => ({ text: plan.name, value: plan.code })),
      onFilter: (value, record) => record.plan === value,
      sorter: (a, b) => (a.plan || '').localeCompare(b.plan || ''),
      render: (plan) => {
        const planName = plans.find(p => p.code === plan)?.name || plan;
        let className = `plan-tag plan-tag-${plan}`;
        return <Tag className={className}>{planName}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: '活跃', value: 'active' },
        { text: '待处理', value: 'pending' },
        { text: '已过期', value: 'expired' },
        { text: '已取消', value: 'canceled' },
        { text: '未开始', value: 'not_started' }
      ],
      onFilter: (value, record) => record.status === value,
      sorter: (a, b) => (a.status || '').localeCompare(b.status || ''),
      render: (status) => {
        const statusMap = {
          active: { text: '活跃', className: 'status-tag-active' },
          pending: { text: '待处理', className: 'status-tag-pending' },
          expired: { text: '已过期', className: 'status-tag-expired' },
          canceled: { text: '已取消', className: 'status-tag-canceled' },
          not_started: { text: '未开始', className: 'status-tag-not-started' }
        };
        const { text, className } = statusMap[status] || { text: status, className: '' };
        return <Tag className={className}>{text}</Tag>;
      }
    },
    {
      title: '开始日期',
      dataIndex: 'startDate',
      key: 'startDate',
      sorter: (a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime(),
      render: (date) => moment(date).format('YYYY-MM-DD')
    },
    {
      title: '结束日期',
      dataIndex: 'endDate',
      key: 'endDate',
      sorter: (a, b) => new Date(a.endDate).getTime() - new Date(b.endDate).getTime(),
      render: (date) => moment(date).format('YYYY-MM-DD')
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      sorter: (a, b) => a.price - b.price,
      render: (price) => `¥${price.toFixed(2)}`
    },
    {
      title: '自动续费',
      dataIndex: 'autoRenew',
      key: 'autoRenew',
      filters: [
        { text: '是', value: true },
        { text: '否', value: false }
      ],
      onFilter: (value, record) => record.autoRenew === value,
      render: (autoRenew) => (
        autoRenew ? <Tag color="green">是</Tag> : <Tag color="red">否</Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEditSubscription(record)}
          >
            编辑
          </Button>
          {record.status === 'active' && (
            <Button 
              type="link" 
              danger 
              icon={<DeleteOutlined />}
              onClick={() => handleCancelSubscription(record._id)}
            >
              取消
            </Button>
          )}
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteSubscription(record._id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  // 计划表格列定义
  const planColumns = [
    {
      title: '代码',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '月付价格',
      dataIndex: ['price', 'monthly'],
      key: 'monthlyPrice',
      render: (price) => `¥${price.toFixed(2)}`
    },
    {
      title: '年付价格',
      dataIndex: ['price', 'yearly'],
      key: 'yearlyPrice',
      render: (price) => `¥${price.toFixed(2)}`
    },
    {
      title: '折扣',
      dataIndex: ['price', 'discount'],
      key: 'discount',
      render: (discount) => `${discount}%`
    },
    {
      title: '公开',
      dataIndex: 'isPublic',
      key: 'isPublic',
      render: (isPublic) => (
        isPublic ? '是' : '否'
      )
    },
    {
      title: '推荐',
      dataIndex: 'isRecommended',
      key: 'isRecommended',
      render: (isRecommended) => (
        isRecommended ? '是' : '否'
      )
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <div>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEditPlan(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />}
            onClick={() => handleDeletePlan(record._id)}
          >
            删除
          </Button>
        </div>
      )
    }
  ];

  // 渲染搜索表单
  const renderSearchForm = () => {
    return (
      <Card className="search-card" style={{ marginBottom: 16, display: showFilter ? 'block' : 'none' }}>
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
        >
          <Form.Item name="username" label="用户名">
            <Input placeholder="输入用户名" allowClear />
          </Form.Item>
          
          <Form.Item name="plan" label="订阅计划">
            <Select placeholder="选择计划" allowClear style={{ width: 120 }}>
              {plans.map(plan => (
                <Option key={plan.code} value={plan.code}>
                  {plan.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="status" label="状态">
            <Select placeholder="选择状态" allowClear style={{ width: 120 }}>
              <Option value="active">活跃</Option>
              <Option value="pending">待处理</Option>
              <Option value="not_started">未开始</Option>
              <Option value="expired">已过期</Option>
              <Option value="canceled">已取消</Option>
            </Select>
          </Form.Item>
          
          <Form.Item name="dateRange" label="日期范围">
            <RangePicker />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              搜索
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={handleResetSearch}>
              重置
            </Button>
          </Form.Item>
        </Form>
      </Card>
    );
  };

  return (
    <div className="subscription-management">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="订阅管理" key="subscriptions">
          <Card style={{ display: 'flex', flexDirection: 'column', height: '100%', minHeight: 0 }}>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />} 
                  onClick={handleCreateSubscription}
                  style={{ marginRight: 8 }}
                >
                  添加订阅
                </Button>
                <Button 
                  icon={<FilterOutlined />} 
                  onClick={() => setShowFilter(!showFilter)}
                  style={{ marginRight: 8 }}
                  type={showFilter ? 'primary' : 'default'}
                >
                  筛选
                </Button>
                <Button 
                  icon={<ClockCircleOutlined />} 
                  onClick={handleBatchUpdateExpired}
                  style={{ marginRight: 8 }}
                  loading={loading}
                >
                  更新过期订阅
                </Button>
              </div>
              <Button onClick={fetchSubscriptions} icon={<ReloadOutlined />}>刷新</Button>
            </div>
            
            {renderSearchForm()}
            
            <div style={{ flex: 1, minHeight: 0, display: 'flex', flexDirection: 'column' }}>
              <Table 
                columns={subscriptionColumns} 
                dataSource={subscriptions || []} 
                rowKey="_id"
                loading={loading}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`
                }}
                onChange={handleTableChange}
                scroll={{ y: '100%' }}
                locale={{
                  emptyText: '没有找到符合条件的订阅数据'
                }}
                style={{ flex: 1 }}
              />
            </div>
          </Card>
        </TabPane>
        
        <TabPane tab="计划管理" key="plans">
          <Card>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={handleCreatePlan}
              >
                添加计划
              </Button>
              <Button onClick={fetchPlans} icon={<ReloadOutlined />}>刷新</Button>
            </div>
            
            <Table 
              columns={planColumns} 
              dataSource={plans} 
              rowKey="_id"
              pagination={{ pageSize: 10 }}
              scroll={{ y: 600 }}
            />
          </Card>
        </TabPane>
        
        <TabPane tab="统计概览" key="stats">
          <Card className="stats-card">
            <div className="stats-row">
              <div className="stat-item">
                <Statistic 
                  title="活跃订阅" 
                  value={stats?.activeSubscriptions || 0} 
                  prefix={<UserOutlined />} 
                />
              </div>
              <div className="stat-item">
                <Statistic 
                  title="本月新增" 
                  value={stats?.newThisMonth || 0} 
                  prefix={<PlusOutlined />} 
                />
              </div>
              <div className="stat-item">
                <Statistic 
                  title="即将到期" 
                  value={stats?.expiringCount || 0} 
                  prefix={<ClockCircleOutlined />} 
                />
              </div>
              <div className="stat-item">
                <Statistic 
                  title="总收入" 
                  value={stats?.totalRevenue || 0} 
                  prefix="¥" 
                  precision={2} 
                />
              </div>
            </div>
            
            <h3>按计划分布</h3>
            <div className="stats-row">
              {stats?.byPlan && Object.entries(stats.byPlan).map(([plan, count]) => (
                <div className="stat-item" key={plan}>
                  <Statistic 
                    title={plans.find(p => p.code === plan)?.name || plan} 
                    value={count} 
                  />
                </div>
              ))}
            </div>
          </Card>
        </TabPane>
      </Tabs>
      
      {/* 订阅编辑/创建模态框 */}
      <Modal
        title={editingSubscription ? '编辑订阅' : '创建订阅'}
        open={modalVisible}
        onOk={handleSaveSubscription}
        onCancel={handleModalCancel}
        maskClosable={false}
        destroyOnClose={true}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="userId"
                label="用户"
                rules={[{ required: true, message: '请选择用户' }]}
              >
                <Select
                  showSearch
                  placeholder="搜索用户名或手机号"
                  loading={userSearchLoading}
                  onSearch={handleUserSearch}
                  filterOption={false}
                  notFoundContent={userSearchLoading ? <div>搜索中...</div> : <div>未找到用户</div>}
                >
                  {userSearchResults.map(user => (
                    <Option key={user._id} value={user._id}>
                      {user.username} {user.name && `(${user.name})`} {user.phone && `- ${user.phone}`}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="plan"
                label="订阅计划"
                rules={[{ required: true, message: '请选择订阅计划' }]}
              >
                <Select placeholder="选择订阅计划">
                  {plans.map(plan => (
                    <Option key={plan.code} value={plan.code}>
                      {plan.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="选择状态">
                  <Option value="active">活跃</Option>
                  <Option value="pending">待处理</Option>
                  <Option value="not_started">未开始</Option>
                  <Option value="expired">已过期</Option>
                  <Option value="canceled">已取消</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="dateRange"
                label="订阅期限"
                rules={[{ required: true, message: '请选择订阅期限' }]}
              >
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="price"
                label="价格"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => {
                    if (value === undefined) return 0;
                    const parsedValue = value.replace(/\¥\s?|(,*)/g, '');
                    return parseFloat(parsedValue) || 0;
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="autoRenew"
                label="自动续费"
                valuePropName="checked"
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="paymentMethod"
                label="支付方式"
              >
                <Select placeholder="选择支付方式">
                  <Option value="manual">手动支付</Option>
                  <Option value="alipay">支付宝</Option>
                  <Option value="wechat">微信支付</Option>
                  <Option value="bank">银行转账</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="paymentId"
                label="支付ID"
              >
                <Input placeholder="支付交易ID" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      
      {/* 计划编辑/创建模态框 */}
      <Modal
        title={editingPlan ? '编辑计划' : '创建计划'}
        visible={planModalVisible}
        onOk={handleSavePlan}
        onCancel={handlePlanModalCancel}
        maskClosable={false}
        destroyOnClose={true}
        width={800}
      >
        <Form
          form={planForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="计划代码"
                rules={[{ required: true, message: '请输入计划代码' }]}
                tooltip="唯一标识符，如 'basic', 'pro', 'enterprise'"
              >
                <Input placeholder="计划代码" disabled={!!editingPlan} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="计划名称"
                rules={[{ required: true, message: '请输入计划名称' }]}
              >
                <Input placeholder="计划名称" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="计划描述"
          >
            <Input.TextArea rows={2} placeholder="计划描述" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['price', 'monthly']}
                label="月付价格"
                rules={[{ required: true, message: '请输入月付价格' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => {
                    if (value === undefined) return 0;
                    const parsedValue = value.replace(/\¥\s?|(,*)/g, '');
                    return parseFloat(parsedValue) || 0;
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['price', 'yearly']}
                label="年付价格"
                rules={[{ required: true, message: '请输入年付价格' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => {
                    if (value === undefined) return 0;
                    const parsedValue = value.replace(/\¥\s?|(,*)/g, '');
                    return parseFloat(parsedValue) || 0;
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['price', 'discount']}
                label="折扣百分比"
                tooltip="年付相对于月付的折扣百分比"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={100}
                  formatter={value => `${value}%`}
                  parser={(value) => {
                    if (value === undefined) return 0;
                    const parsedValue = value.replace('%', '');
                    const numValue = parseFloat(parsedValue) || 0;
                    return numValue > 100 ? 100 : numValue;
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="isPublic"
                label="公开显示"
                valuePropName="checked"
                tooltip="是否在订阅页面公开显示"
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="isRecommended"
                label="推荐计划"
                valuePropName="checked"
                tooltip="是否作为推荐计划高亮显示"
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="sortOrder"
                label="排序顺序"
                tooltip="数字越小排序越靠前"
              >
                <InputNumber style={{ width: '100%' }} min={0} />
              </Form.Item>
            </Col>
          </Row>
          
          <Tabs defaultActiveKey="1">
            <TabPane tab="款式功能" key="1">
              <Form.Item name={['features', 'design', 'enabled']} valuePropName="checked">
                <Switch checkedChildren="启用款式功能" unCheckedChildren="禁用款式功能" />
              </Form.Item>
              <Form.Item name={['features', 'design', 'trending']} valuePropName="checked">
                <Switch checkedChildren="爆款开发" unCheckedChildren="禁用爆款开发" />
              </Form.Item>
              <Form.Item name={['features', 'design', 'optimize']} valuePropName="checked">
                <Switch checkedChildren="款式优化" unCheckedChildren="禁用款式优化" />
              </Form.Item>
              <Form.Item name={['features', 'design', 'inspiration']} valuePropName="checked">
                <Switch checkedChildren="灵感探索" unCheckedChildren="禁用灵感探索" />
              </Form.Item>
              <Form.Item name={['features', 'design', 'divergent']} valuePropName="checked">
                <Switch checkedChildren="爆款延伸" unCheckedChildren="禁用爆款延伸" />
              </Form.Item>
              <Form.Item name={['features', 'model', 'fabric']} valuePropName="checked">
                <Switch checkedChildren="换面料" unCheckedChildren="禁用换面料" />
              </Form.Item>
              <Form.Item name={['features', 'design', 'drawing']} valuePropName="checked">
                <Switch checkedChildren="生成线稿" unCheckedChildren="禁用生成线稿" />
              </Form.Item>
            </TabPane>
            <TabPane tab="模特功能" key="2">
              <Form.Item name={['features', 'model', 'enabled']} valuePropName="checked">
                <Switch checkedChildren="启用模特功能" unCheckedChildren="禁用模特功能" />
              </Form.Item>
              <Form.Item name={['features', 'model', 'fashion']} valuePropName="checked">
                <Switch checkedChildren="时尚大片" unCheckedChildren="禁用时尚大片" />
              </Form.Item>
              <Form.Item name={['features', 'model', 'try-on']} valuePropName="checked">
                <Switch checkedChildren="模特换装" unCheckedChildren="禁用模特换装" />
              </Form.Item>
              <Form.Item name={['features', 'model', 'change-model']} valuePropName="checked">
                <Switch checkedChildren="换模特" unCheckedChildren="禁用换模特" />
              </Form.Item>
              <Form.Item name={['features', 'model', 'recolor']} valuePropName="checked">
                <Switch checkedChildren="服装复色" unCheckedChildren="禁用服装复色" />
              </Form.Item>
              <Form.Item name={['features', 'model', 'background']} valuePropName="checked">
                <Switch checkedChildren="换背景" unCheckedChildren="禁用换背景" />
              </Form.Item>
              <Form.Item name={['features', 'model', 'change-posture']} valuePropName="checked">
                <Switch checkedChildren="换姿势" unCheckedChildren="禁用换姿势" />
              </Form.Item>
              <Form.Item name={['features', 'model', 'virtual']} valuePropName="checked">
                <Switch checkedChildren="虚拟模特" unCheckedChildren="禁用虚拟模特" />
              </Form.Item>
              <Form.Item name={['features', 'model', 'detail-migration']} valuePropName="checked">
                <Switch checkedChildren="细节还原" unCheckedChildren="禁用细节还原" />
              </Form.Item>
              <Form.Item name={['features', 'model', 'hand-fix']} valuePropName="checked">
                <Switch checkedChildren="手部修复" unCheckedChildren="禁用手部修复" />
              </Form.Item>
            </TabPane>
            <TabPane tab="工具功能" key="3">
              <Form.Item name={['features', 'tools', 'enabled']} valuePropName="checked">
                <Switch checkedChildren="启用工具功能" unCheckedChildren="禁用工具功能" />
              </Form.Item>
              <Form.Item name={['features', 'tools', 'extract']} valuePropName="checked">
                <Switch checkedChildren="图片取词" unCheckedChildren="禁用图片取词" />
              </Form.Item>
              <Form.Item name={['features', 'tools', 'upscale']} valuePropName="checked">
                <Switch checkedChildren="高清放大" unCheckedChildren="禁用高清放大" />
              </Form.Item>
              <Form.Item name={['features', 'tools', 'matting']} valuePropName="checked">
                <Switch checkedChildren="自动抠图" unCheckedChildren="禁用自动抠图" />
              </Form.Item>
              <Form.Item name={['features', 'tools', 'extend']} valuePropName="checked">
                <Switch checkedChildren="智能扩图" unCheckedChildren="禁用智能扩图" />
              </Form.Item>
              <Form.Item name={['features', 'tools', 'inpaint']} valuePropName="checked">
                <Switch checkedChildren="消除笔" unCheckedChildren="禁用消除笔" />
              </Form.Item>
            </TabPane>
            <TabPane tab="视频功能" key="4">
              <Form.Item name={['features', 'video', 'enabled']} valuePropName="checked">
                <Switch checkedChildren="启用视频功能" unCheckedChildren="禁用视频功能" />
              </Form.Item>
              <Form.Item name={['features', 'video', 'imgtextvideo']} valuePropName="checked">
                <Switch checkedChildren="图文成片" unCheckedChildren="禁用图文成片" />
              </Form.Item>
              <Form.Item name={['features', 'video', 'mulimgvideo']} valuePropName="checked">
                <Switch checkedChildren="多图成片" unCheckedChildren="禁用多图成片" />
              </Form.Item>
            </TabPane>
            <TabPane tab="支持服务" key="5">
              <Form.Item name={['features', 'support', 'level']} label="支持级别">
                <Select>
                  <Option value="standard">标准支持</Option>
                  <Option value="premium">高级支持</Option>
                  <Option value="enterprise">企业支持</Option>
                </Select>
              </Form.Item>
              <Form.Item name={['features', 'support', 'responseTime']} label="响应时间">
                <Select>
                  <Option value="5x8">5x8（工作日）</Option>
                  <Option value="7x24">7x24（全天候）</Option>
                </Select>
              </Form.Item>
            </TabPane>
            <TabPane tab="使用限制" key="6">
              <Form.Item name={['usageQuota', 'totalRequests']} label="总请求次数" tooltip="-1表示无限制">
                <InputNumber style={{ width: '100%' }} min={-1} />
              </Form.Item>
              <Form.Item name={['usageQuota', 'dailyRequests']} label="每日请求次数" tooltip="-1表示无限制">
                <InputNumber style={{ width: '100%' }} min={-1} />
              </Form.Item>
              <Form.Item name={['usageQuota', 'maxConcurrentTasks']} label="最大并发任务数" tooltip="每个用户可同时运行的最大任务数">
                <InputNumber style={{ width: '100%' }} min={1} />
              </Form.Item>
            </TabPane>
          </Tabs>
        </Form>
      </Modal>
    </div>
  );
};

export default SubscriptionManagement;