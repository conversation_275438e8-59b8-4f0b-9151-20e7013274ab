# 前端多平台任务进度适配

## 概述

本文档说明了如何修改前端代码以支持多平台（ComfyUI和RunningHub）的任务进度信息处理，实现统一的用户体验。

## 1. 主要改进

### 1.1 WebSocket管理器增强

#### **支持多平台订阅：**
```javascript
// src/utils/comfyUITaskTracker.js

// 增强的订阅方法
async subscribeTask(taskId, promptId, instanceId, instanceWsUrl, callbacks, 
                   platform = 'comfyui', taskData = {}) {
  const subscriptionData = {
    task_id: taskId,
    prompt_id: promptId,
    instance_id: instanceId,
    instance_ws_url: instanceWsUrl,
    platform: platform,           // 🔑 平台标识
    task_data: taskData           // 🔑 平台特定数据
  };
  
  console.log(`订阅${platform}任务:`, {
    taskId,
    platform,
    hasNetWssUrl: !!(taskData.netWssUrl)
  });
}
```

#### **增强的消息发送：**
```javascript
sendSubscriptionMessage(subscriptionData) {
  const message = {
    type: 'subscribe_task',
    task_id: subscriptionData.task_id,
    prompt_id: subscriptionData.prompt_id,
    instance_id: subscriptionData.instance_id,
    instance_ws_url: subscriptionData.instance_ws_url,
    platform: subscriptionData.platform || 'comfyui',  // 🔑 平台信息
    task_data: subscriptionData.task_data || {}         // 🔑 任务数据
  };
  
  console.log('发送订阅消息:', message);
  this.ws.send(JSON.stringify(message));
}
```

### 1.2 平台工具函数

#### **创建了专门的工具函数库：**
```javascript
// src/utils/platformUtils.js

// 检测任务平台类型
export function detectTaskPlatform(task) {
  if (task.platform) return task.platform;
  if (task.netWssUrl) return 'runninghub';
  if (task.url && task.url.includes('runninghub')) return 'runninghub';
  return 'comfyui';
}

// 准备平台特定数据
export function prepareTaskData(task, platform) {
  if (platform === 'runninghub') {
    return {
      netWssUrl: task.netWssUrl,
      clientId: task.clientId,
      taskStatus: task.taskStatus,
      promptTips: task.promptTips
    };
  }
  return {};
}

// 计算任务进度
export function calculateTaskProgress(currentTask, progress, max, platform) {
  if (platform === 'runninghub') {
    // RunningHub: 直接使用百分比
    return Math.min((progress || 0) / 100, 0.97);
  } else {
    // ComfyUI: 每次增加1%
    const currentProgress = currentTask.progress || 0;
    return Math.min(currentProgress + 0.01, 0.97);
  }
}
```

### 1.3 统一的订阅参数创建

#### **createSubscriptionParams函数：**
```javascript
export function createSubscriptionParams(task) {
  const platform = detectTaskPlatform(task);
  const taskData = prepareTaskData(task, platform);
  const validation = validateTaskForPlatform(task, platform);
  
  if (!validation.isValid) {
    throw new Error(`任务验证失败: ${validation.errors.join(', ')}`);
  }
  
  return {
    taskId: task.taskId,
    promptId: task.promptId,
    instanceId: task.instanceId || '',
    instanceWsUrl: task.url || '',
    platform: platform,
    taskData: taskData,
    validation: validation
  };
}
```

## 2. GenerationArea组件适配

### 2.1 简化的任务订阅逻辑

#### **使用工具函数简化代码：**
```javascript
// src/components/GenerationArea/index.jsx

try {
  // 使用工具函数创建订阅参数
  const subscriptionParams = createSubscriptionParams(task);
  
  logPlatformEvent('info', '开始监听任务', {
    taskId: subscriptionParams.taskId,
    platform: subscriptionParams.platform,
    hasNetWssUrl: !!(subscriptionParams.taskData.netWssUrl)
  });

  await globalWebSocketManager.subscribeTask(
    subscriptionParams.taskId,
    subscriptionParams.promptId,
    subscriptionParams.instanceId,
    subscriptionParams.instanceWsUrl,
    {
      onProgress: (progress, max, progressPlatform) => {
        const actualPlatform = progressPlatform || subscriptionParams.platform;
        
        // 使用工具函数更新任务进度
        setTasks(prevTasks => {
          const currentTask = prevTasks.find(t => t.taskId === subscriptionParams.taskId);
          if (!currentTask) return prevTasks;
          
          const updatedTask = updateTaskProgress(currentTask, progress, max, actualPlatform);
          
          return prevTasks.map(t => 
            t.taskId === subscriptionParams.taskId ? updatedTask : t
          );
        });
      },
      onCompleted: async (outputs) => {
        // 任务完成处理逻辑
      }
    },
    subscriptionParams.platform,
    subscriptionParams.taskData
  );
} catch (error) {
  const errorInfo = handlePlatformError(error, task.platform || 'unknown', task.taskId);
  console.error(`订阅任务失败:`, errorInfo);
}
```

### 2.2 平台特定的进度处理

#### **不同平台的进度计算：**
```javascript
export function updateTaskProgress(task, progress, max, platform) {
  const newProgress = calculateTaskProgress(task, progress, max, platform);
  
  return {
    ...task,
    progress: newProgress,
    status: 'processing',
    platform: platform,
    progressDetails: {
      current: progress,
      total: max || 100,
      platform: platform,
      displayText: formatProgressText({ current: progress, total: max }, platform)
    },
    lastUpdated: new Date().toISOString()
  };
}
```

## 3. 消息格式标准化

### 3.1 WebSocket订阅消息

#### **统一的订阅消息格式：**
```json
{
  "type": "subscribe_task",
  "task_id": "01175362530856327",
  "prompt_id": "1949465676051324929",
  "instance_id": "",
  "instance_ws_url": "wss://www.runninghub.cn",
  "platform": "runninghub",
  "task_data": {
    "netWssUrl": "wss://www.runninghub.cn:443/ws/c_instance?...",
    "clientId": "88f6947dc37e94b20654a5c5e67fce69",
    "taskStatus": "RUNNING",
    "promptTips": "{\"result\": true, \"outputs_to_execute\": [\"62\", \"37\"]}"
  }
}
```

### 3.2 进度更新消息

#### **平台标识的进度消息：**
```json
{
  "type": "progress",
  "data": {
    "value": 50,
    "max": 100,
    "prompt_id": "1949465676051324929",
    "platform": "runninghub"
  },
  "task_id": "01175362530856327"
}
```

## 4. 平台检测逻辑

### 4.1 自动平台检测

#### **检测优先级：**
```javascript
function detectTaskPlatform(task) {
  // 1. 明确指定的平台
  if (task.platform) {
    return task.platform;
  }
  
  // 2. 有netWssUrl说明是RunningHub
  if (task.netWssUrl) {
    return 'runninghub';
  }
  
  // 3. URL包含runninghub域名
  if (task.url && task.url.includes('runninghub')) {
    return 'runninghub';
  }
  
  // 4. 默认为ComfyUI
  return 'comfyui';
}
```

### 4.2 任务验证

#### **平台特定验证：**
```javascript
export function validateTaskForPlatform(task, platform) {
  const result = { isValid: true, errors: [], warnings: [] };
  
  if (platform === 'runninghub') {
    if (!task.netWssUrl) {
      result.isValid = false;
      result.errors.push('RunningHub任务缺少netWssUrl');
    }
    if (!task.clientId) {
      result.warnings.push('RunningHub任务缺少clientId');
    }
  } else if (platform === 'comfyui') {
    if (!task.url) {
      result.isValid = false;
      result.errors.push('ComfyUI任务缺少url');
    }
  }
  
  return result;
}
```

## 5. 错误处理增强

### 5.1 平台特定错误处理

#### **智能错误提示：**
```javascript
export function handlePlatformError(error, platform, taskId) {
  const errorInfo = {
    platform,
    taskId,
    error: error.message,
    timestamp: new Date().toISOString()
  };
  
  if (platform === 'runninghub') {
    if (error.message.includes('netWssUrl')) {
      errorInfo.suggestion = '请检查RunningHub任务的netWssUrl配置';
    } else if (error.message.includes('WebSocket')) {
      errorInfo.suggestion = '请检查RunningHub WebSocket连接';
    }
  } else if (platform === 'comfyui') {
    if (error.message.includes('timeout')) {
      errorInfo.suggestion = '请检查ComfyUI实例是否正常运行';
    }
  }
  
  return errorInfo;
}
```

### 5.2 日志记录

#### **结构化日志：**
```javascript
export function logPlatformEvent(level, message, data = {}) {
  const logData = {
    timestamp: new Date().toISOString(),
    level,
    message,
    ...data
  };
  
  console[level](`[Platform ${level.toUpperCase()}] ${message}`, logData);
}
```

## 6. 使用示例

### 6.1 RunningHub任务订阅

```javascript
// 任务对象包含RunningHub特定信息
const runningHubTask = {
  taskId: "01175362530856327",
  promptId: "1949465676051324929",
  platform: "runninghub",
  netWssUrl: "wss://www.runninghub.cn:443/ws/c_instance?...",
  clientId: "88f6947dc37e94b20654a5c5e67fce69",
  taskStatus: "RUNNING",
  url: "wss://www.runninghub.cn"
};

// 自动检测平台并订阅
const subscriptionParams = createSubscriptionParams(runningHubTask);
await globalWebSocketManager.subscribeTask(
  subscriptionParams.taskId,
  subscriptionParams.promptId,
  subscriptionParams.instanceId,
  subscriptionParams.instanceWsUrl,
  callbacks,
  subscriptionParams.platform,
  subscriptionParams.taskData
);
```

### 6.2 ComfyUI任务订阅（向后兼容）

```javascript
// 传统ComfyUI任务
const comfyUITask = {
  taskId: "comfyui_task_123",
  promptId: "comfyui_prompt_456",
  instanceId: "instance_789",
  url: "ws://comfyui-instance:8188/ws"
};

// 自动检测为ComfyUI平台
const subscriptionParams = createSubscriptionParams(comfyUITask);
// 订阅逻辑相同，平台自动检测
```

## 7. 总结

### 7.1 主要改进

- ✅ **统一接口**：前端使用相同的API订阅不同平台任务
- ✅ **自动检测**：智能检测任务平台类型
- ✅ **数据验证**：平台特定的任务数据验证
- ✅ **进度处理**：不同平台的进度计算逻辑
- ✅ **错误处理**：平台特定的错误提示和处理
- ✅ **向后兼容**：保持对现有ComfyUI任务的完全兼容

### 7.2 技术特性

- **工具函数库**：模块化的平台处理工具
- **类型安全**：完整的任务验证机制
- **日志记录**：结构化的平台事件日志
- **错误恢复**：智能的错误处理和建议

### 7.3 用户体验

- **透明切换**：用户无需关心底层平台差异
- **实时进度**：准确的多平台进度显示
- **错误提示**：清晰的平台特定错误信息
- **性能优化**：高效的WebSocket连接管理

通过这些改进，前端现在能够无缝支持ComfyUI和RunningHub两个平台，为用户提供一致的任务进度监控体验。🎉
