.model-page {
  padding: 2rem;
  width: 100%;
  margin: 0 auto;
}

.model-header {
  text-align: center;
  margin-bottom: 3rem;
}

.model-header h1 {
  font-size: 2.5rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.model-header p {
  font-size: 1.1rem;
  color: var(--text-secondary);
}

.page-feature-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
  margin: 0 auto;
}

.page-feature-card {
  width: calc(20% - 12.8px); /* 5列布局 */
  background: var(--bg-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  transform: translate3d(0, 0, 0);
  will-change: transform, box-shadow;
  transition: transform 0.15s cubic-bezier(0.2, 0, 0.15, 1), box-shadow 0.15s cubic-bezier(0.2, 0, 0.15, 1);
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
  perspective: 1000px;
  cursor: pointer;
}

.page-feature-card:hover {
  transform: translate3d(0, -5px, 0);
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.feature-icon img {
  width: 100%;
  aspect-ratio: 1 / 1;
  object-fit: cover;
  display: block;
}

.page-feature-card img {
  width: 100%;
  aspect-ratio: 1 / 1;
  object-fit: cover;
  display: block;
}

.page-feature-card h3 {
  margin: 0 0 10px 0;
  font-size: 1.3rem;
  color: var(--text-primary);
  padding: 20px 20px 0;
}

.page-feature-card p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: clamp(0.9rem, 1.5vw, 1rem);
  padding: 0 20px 20px;
}

.feature-tip {
  text-align: center;
  color: var(--text-tertiary);
  font-size: 0.9rem;
  margin-top: 3rem;
  letter-spacing: 0.02em;
  position: relative;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
  padding: 0.5rem 0;
  font-weight: 300;
}

.feature-tip::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 120%;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 60, 106, 0.2) 25%, 
    rgba(255, 60, 106, 0.2) 75%, 
    transparent 100%
  );
  transition: width 0.3s ease;
}

.feature-tip:hover::after {
  width: 140%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 60, 106, 0.3) 25%, 
    rgba(255, 60, 106, 0.3) 75%, 
    transparent 100%
  );
}

/* 响应式布局 */
@media (max-width: 1800px) {
  .page-feature-card {
    width: calc(25% - 12px); /* 4列 */
  }
}

@media (max-width: 1400px) {
  .page-feature-card {
    width: calc(33.333% - 11px); /* 3列 */
  }
}

@media (max-width: 1024px) {
  .page-feature-card {
    width: calc(33.333% - 11px); /* 3列 - 移动端至少显示3列 */
  }
}

@media (max-width: 768px) {
  .page-feature-card {
    width: calc(25% - 12px); /* 4列 - 中等移动端显示4列 */
  }
  
  .model-page {
    padding: 1rem;
  }
  
  .model-header h1 {
    font-size: 1.2rem;
  }
  
  .model-header p {
    font-size: 0.85rem;
  }
  
  .page-feature-card h3 {
    font-size: 0.9rem;
    margin-bottom: 6px;
    padding: 12px 12px 0;
  }
  
  .page-feature-card p {
    font-size: 0.75rem;
    line-height: 1.3;
    padding: 0 12px 12px;
  }
  
  .feature-tip {
    font-size: 0.8rem;
    margin-top: 2rem;
    padding: 0.4rem 0;
  }
}

@media (max-width: 480px) {
  .page-feature-card {
    width: calc(33.333% - 11px); /* 3列 - 小屏幕移动端显示3列 */
  }
  
  .model-header h1 {
    font-size: 1.2rem;
  }
  
  .model-header p {
    font-size: 0.85rem;
  }
  
  .page-feature-card h3 {
    font-size: 0.85rem;
    margin-bottom: 4px;
    padding: 8px 8px 0;
  }
  
  .page-feature-card p {
    font-size: 0.7rem;
    padding: 0 8px 8px;
  }
}

.error-container {
  text-align: center;
  padding: 2rem;
  margin: 2rem auto;
  max-width: 600px;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
}

.error-container h2 {
  color: var(--error-color);
  margin-bottom: 1rem;
}

.error-container p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.error-container button {
  padding: 0.5rem 2rem;
  background: var(--brand-gradient);
  color: var(--text-inverse);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-container button:hover {
  filter: brightness(1.1);
} 