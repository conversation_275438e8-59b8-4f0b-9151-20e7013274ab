# 用户实例历史记录功能使用指南

## 功能概述

本功能实现了"检查半小时内容的任务使用的实例，优先选择之前使用的实例"的需求，主要功能包括：

1. **用户实例使用历史记录** - 记录用户使用过的实例信息
2. **智能实例选择** - 根据用户历史优先选择之前使用过的实例
3. **历史记录自动清理** - 30分钟后的历史记录自动过期清理
4. **降级策略** - 当历史实例不可用时，自动降级到随机选择

## 核心功能

### 1. 记录用户实例使用历史
```javascript
await instanceService.recordUserInstanceHistory(userId, instanceId);
```

### 2. 获取用户历史记录
```javascript
const history = await instanceService.getUserInstanceHistory(userId);
// 返回格式: [{ instanceId, timestamp, lastUsed }]
```

### 3. 根据历史优先选择实例
```javascript
const selectedInstance = await instanceService.prioritizeInstanceByUserHistory(
  availableInstances, 
  userId
);
```

### 4. 获取可用实例（集成历史选择）
```javascript
const instance = await instanceService.getAvailableInstance(userId);
```

## 工作流程

### 实例选择优先级
1. **历史实例优先** - 如果用户30分钟内有使用过的实例且当前可用，优先选择
2. **最近使用优先** - 在历史实例中，优先选择最近使用的实例
3. **随机选择降级** - 如果历史实例都不可用，随机选择可用实例
4. **实例状态优先级** - 可用实例 > 关机实例 > 运行中实例

### 历史记录管理
- **记录时机**: 每次用户获取实例时自动记录
- **过期时间**: 30分钟自动过期
- **存储方式**: MongoDB缓存存储
- **清理策略**: 获取历史时自动清理过期记录

## 使用示例

### 基本使用
```javascript
// 在WorkflowService中
const comfyClient = await ComfyClientFactory.createClient(userId);
```

### 手动记录历史
```javascript
// 记录用户使用实例
await instanceService.recordUserInstanceHistory('user123', 'instance_001');

// 获取用户历史
const history = await instanceService.getUserInstanceHistory('user123');
console.log('用户历史:', history);
```

### 自定义实例选择
```javascript
const availableInstances = [
  { instanceId: 'instance_001', name: 'Instance 1' },
  { instanceId: 'instance_002', name: 'Instance 2' }
];

const selected = await instanceService.prioritizeInstanceByUserHistory(
  availableInstances, 
  'user123'
);
```

## 配置参数

### 历史记录过期时间
```javascript
// 在InstanceService构造函数中
this.USER_INSTANCE_HISTORY_TTL = 30 * 60 * 1000; // 30分钟
```

### 缓存配置
```javascript
// 使用MongoDB缓存存储历史记录
const cache = new MongoDBCache({
  collectionName: 'instance_status_cache',
  defaultTTL: 60 * 1000
});
```

## 数据结构

### 历史记录格式
```javascript
{
  instanceId: 'instance_001',    // 实例ID
  timestamp: 1703123456789,      // 记录时间戳
  lastUsed: 1703123456789        // 最后使用时间戳
}
```

### 缓存键格式
```javascript
// 用户历史记录缓存键
`user_instance_history_${userId}`
```

## 日志输出

### 实例选择日志
```
用户 user123 优先选择历史使用过的实例: instance_001
从 5 个可用实例中选择: instance_001
用户 user123 没有历史记录，随机选择实例
用户 user123 的历史实例都不可用，随机选择实例
```

### 历史记录日志
```
记录用户 user123 使用实例 instance_001 的历史
```

## 错误处理

### 常见错误及处理
1. **缓存连接失败** - 降级到随机选择
2. **历史记录损坏** - 自动清理并重新开始
3. **实例不可用** - 自动选择其他可用实例

### 降级策略
```javascript
try {
  const selected = await instanceService.prioritizeInstanceByUserHistory(instances, userId);
  return selected;
} catch (error) {
  console.error('根据用户历史选择实例失败:', error);
  // 出错时随机选择
  const randomIndex = Math.floor(Math.random() * instances.length);
  return instances[randomIndex];
}
```

## 性能考虑

### 缓存优化
- 历史记录使用MongoDB缓存，避免频繁数据库查询
- 自动清理过期记录，避免缓存膨胀
- 批量操作减少网络开销

### 选择算法优化
- 使用Map结构快速查找实例
- 按时间排序，优先选择最近使用的实例
- 避免重复计算和查询

## 测试

### 运行测试脚本
```bash
cd server
node test-user-instance-history.js
```

### 测试覆盖
- 历史记录创建和更新
- 历史记录获取和清理
- 优先选择逻辑
- 新用户随机选择
- 历史记录过期处理

## 监控和维护

### 监控指标
- 历史记录命中率
- 实例选择成功率
- 缓存命中率
- 错误率

### 维护任务
- 定期检查缓存连接状态
- 监控历史记录清理效果
- 分析用户实例使用模式

## 与现有系统的集成

### 已修改的文件
1. `server/src/services/instance/instanceService.js` - 添加历史记录功能
2. `server/src/services/comfyClient/ComfyClientFactory.js` - 集成用户ID参数
3. `server/src/services/comfyClient/service/WorkflowService.js` - 传递用户ID

### 向后兼容性
- 所有修改都保持向后兼容
- 不传递userId时使用原有逻辑
- 历史记录功能不影响现有实例管理

## 注意事项

1. **用户ID要求** - 需要有效的用户ID才能记录历史
2. **时间精度** - 使用毫秒级时间戳确保准确性
3. **并发安全** - 使用MongoDB保证并发安全
4. **资源清理** - 自动清理过期记录避免内存泄漏
5. **错误恢复** - 出错时自动降级到随机选择 