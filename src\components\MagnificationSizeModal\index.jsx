import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { MdClose, MdLock } from 'react-icons/md';
import '../../styles/close-buttons.css';
import './index.css';

const MagnificationSizeModal = ({
  visible,
  onClose,
  onApply,
  defaultScale = 2,
  originalWidth = null,
  originalHeight = null,
  originalImage = null,
  style,
  savedSettings = null
}) => {
  // 添加调试日志，输出接收到的props
  console.log('MagnificationSizeModal接收到的props:', {
    visible,
    defaultScale,
    originalWidth,
    originalHeight,
    originalImage,
    style,
    savedSettings
  });
  
  const [activeTab, setActiveTab] = useState('scale');
  const [scale, setScale] = useState(defaultScale);
  const [width, setWidth] = useState(null);
  const [height, setHeight] = useState(null);
  const [hasOriginalImage, setHasOriginalImage] = useState(Boolean(originalWidth && originalHeight));
  const [aspectRatio, setAspectRatio] = useState(originalWidth && originalHeight ? originalWidth / originalHeight : null);
  const [customWidth, setCustomWidth] = useState('');
  const [customHeight, setCustomHeight] = useState('');
  // 添加activeRatio状态，用于记录用户选择的宽高比
  const [activeRatio, setActiveRatio] = useState(null);
  // 添加超出尺寸限制的状态
  const [isSizeExceeded, setIsSizeExceeded] = useState(false);
  
  // 添加ref用于跟踪visible的前一个状态
  const previousVisible = useRef(false);
  
  // 添加状态用于存储图片的实际尺寸
  const [actualWidth, setActualWidth] = useState(null);
  const [actualHeight, setActualHeight] = useState(null);
  const [isLoadingImageDimensions, setIsLoadingImageDimensions] = useState(false);
  
  // 弹窗拖动相关状态
  const [isModalDragging, setIsModalDragging] = useState(false);
  const [modalDragOffset, setModalDragOffset] = useState({ x: 0, y: 0 });
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });
  
  // 检查尺寸是否超过限制
  const checkSizeLimit = (w, h) => {
    return w > 8000 || h > 8000;
  };
  
  // 添加获取图片实际尺寸的逻辑
  useEffect(() => {
    if (originalImage && visible && (!originalWidth || !originalHeight || originalWidth === 800 && originalHeight === 1200)) {
      console.log('尝试获取图片实际尺寸:', originalImage);
      setIsLoadingImageDimensions(true);
      
      // 先检查originalImage是否有效
      const isValidUrl = (typeof originalImage === 'string' && 
                         (originalImage.startsWith('data:') ||
                          originalImage.startsWith('http:') || 
                          originalImage.startsWith('https:') ||
                          originalImage.startsWith('/')));
      
      // 如果是Blob URL或无效URL，直接跳过，因为这些URL可能已过期
      if (!isValidUrl) {
        console.log('跳过尝试加载可能无效的URL:', originalImage);
        setIsLoadingImageDimensions(false);
        return;
      }
      
      const img = new Image();
      img.crossOrigin = "Anonymous"; // 添加跨域支持
      
      img.onload = () => {
        const imgWidth = img.naturalWidth;
        const imgHeight = img.naturalHeight;
        console.log('获取到图片实际尺寸:', imgWidth, 'x', imgHeight);
        
        setActualWidth(imgWidth);
        setActualHeight(imgHeight);
        
        // 更新相关状态
        setHasOriginalImage(true);
        setAspectRatio(imgWidth / imgHeight);
        
        // 如果已设置了scale，重新计算结果尺寸
        if (scale) {
          const newWidth = Math.round(imgWidth * scale);
          const newHeight = Math.round(imgHeight * scale);
          setWidth(newWidth);
          setHeight(newHeight);
          setCustomWidth(newWidth);
          setCustomHeight(newHeight);
          setIsSizeExceeded(checkSizeLimit(newWidth, newHeight));
        }
        
        setIsLoadingImageDimensions(false);
      };
      
      img.onerror = () => {
        console.error('加载图片获取尺寸失败:', originalImage);
        setIsLoadingImageDimensions(false);
        
        // 如果已有originalWidth和originalHeight，使用这些值
        if (originalWidth && originalHeight) {
          setHasOriginalImage(true);
          setAspectRatio(originalWidth / originalHeight);
          
          // 使用已有尺寸计算
          if (scale) {
            const newWidth = Math.round(originalWidth * scale);
            const newHeight = Math.round(originalHeight * scale);
            setWidth(newWidth);
            setHeight(newHeight);
            setCustomWidth(newWidth);
            setCustomHeight(newHeight);
            setIsSizeExceeded(checkSizeLimit(newWidth, newHeight));
          }
        }
      };
      
      try {
        // 如果URL是以data:开头的Data URL，则直接使用
        if (originalImage.startsWith('data:')) {
          img.src = originalImage;
        } else {
          // 对于其他类型的URL，添加时间戳防止缓存
          const timestamp = Date.now();
          const separator = originalImage.includes('?') ? '&' : '?';
          img.src = `${originalImage}${separator}t=${timestamp}`;
        }
      } catch (error) {
        console.error('设置图片URL失败:', error);
        setIsLoadingImageDimensions(false);
        
        // 如果使用URL失败，但有宽高信息，仍然使用这些信息
        if (originalWidth && originalHeight) {
          setHasOriginalImage(true);
          setAspectRatio(originalWidth / originalHeight);
        }
      }
    }
  }, [originalImage, originalWidth, originalHeight, scale, visible]);
  
  // 每次弹窗显示时，重置状态
  useEffect(() => {
    if (visible) {
      console.log('弹窗打开，重置状态，savedSettings:', savedSettings);
      
      // 如果有已保存的设置，恢复到已保存的状态
      if (savedSettings) {
        setScale(savedSettings.scale);
        setWidth(savedSettings.width);
        setHeight(savedSettings.height);
        setActiveRatio(savedSettings.activeRatio);
        setCustomWidth(savedSettings.width);
        setCustomHeight(savedSettings.height);
        setActiveTab(savedSettings.activeTab || 'scale');
        
        // 检查恢复的设置是否超出限制
        setIsSizeExceeded(checkSizeLimit(savedSettings.width, savedSettings.height));
      } else {
        // 如果没有已保存的设置，则使用默认值
        setScale(defaultScale);
        setWidth(null);
        setHeight(null);
        setActiveRatio(null);
        setCustomWidth('');
        setCustomHeight('');
        setActiveTab('scale');
        setIsSizeExceeded(false);
      }
      
      // 计算是否有原图 - 使用实际尺寸或传入的尺寸
      const effectiveWidth = actualWidth || originalWidth;
      const effectiveHeight = actualHeight || originalHeight;
      const newHasOriginalImage = Boolean(effectiveWidth && effectiveHeight);
      setHasOriginalImage(newHasOriginalImage);
      
      // 计算原图宽高比 - 使用实际尺寸或传入的尺寸
      if (effectiveWidth && effectiveHeight) {
        setAspectRatio(effectiveWidth / effectiveHeight);
      } else {
        setAspectRatio(null);
      }
    } else if (previousVisible.current && !visible) {
      // 当弹窗从显示变为隐藏时，立即清除未保存的选择
      console.log('弹窗关闭，立即清除未保存的选择');
      setScale(null);
      setWidth(null);
      setHeight(null);
      setActiveRatio(null);
      setCustomWidth('');
      setCustomHeight('');
      setIsSizeExceeded(false);
    }
    
    // 记录当前的visible状态，用于下次比较
    previousVisible.current = visible;
  }, [visible, savedSettings, originalWidth, originalHeight, actualWidth, actualHeight]);

  // 当originalWidth和originalHeight变化时，更新状态
  useEffect(() => {
    console.log('MagnificationSizeModal useEffect - 检测到props变化:', {
      originalWidth,
      originalHeight,
      actualWidth,
      actualHeight,
      scale,
      activeRatio
    });
    
    // 使用实际尺寸或传入的尺寸
    const effectiveWidth = actualWidth || originalWidth;
    const effectiveHeight = actualHeight || originalHeight;
    
    const newHasOriginalImage = Boolean(effectiveWidth && effectiveHeight);
    console.log('计算得到hasOriginalImage:', newHasOriginalImage);
    
    setHasOriginalImage(newHasOriginalImage);
    
    if (effectiveWidth && effectiveHeight) {
      // 如果有激活的宽高比或倍数，才计算尺寸
      if (activeRatio || scale) {
        // 如果有激活的宽高比，不要在这里重新计算尺寸
        if (activeRatio) {
          console.log('检测到宽高比选择，跳过useEffect中的尺寸计算');
          return;
        }
        
        // 只在设置了scale时才计算尺寸
        if (scale) {
          console.log('更新宽高值:', {
            newWidth: Math.round(effectiveWidth * scale),
            newHeight: Math.round(effectiveHeight * scale)
          });
          const newWidth = Math.round(effectiveWidth * scale);
          const newHeight = Math.round(effectiveHeight * scale);
          
          setWidth(newWidth);
          setHeight(newHeight);
          setCustomWidth(newWidth);
          setCustomHeight(newHeight);
          
          // 检查新的尺寸是否超出限制
          setIsSizeExceeded(checkSizeLimit(newWidth, newHeight));
        }
      }
      
      setAspectRatio(effectiveWidth / effectiveHeight);
    } else {
      console.log('未设置宽高值，因为原图尺寸不可用');
      setWidth(null);
      setHeight(null);
      setCustomWidth('');
      setCustomHeight('');
      setAspectRatio(null);
      setActiveRatio(null);
      setIsSizeExceeded(false);
    }
  }, [originalWidth, originalHeight, actualWidth, actualHeight, scale, activeRatio]);

  // 处理倍数变化
  const handleScaleChange = (newScale) => {
    setScale(newScale);
    // 只有在有原图时才计算新尺寸
    // 使用实际尺寸或传入的尺寸
    const effectiveWidth = actualWidth || originalWidth;
    const effectiveHeight = actualHeight || originalHeight;
    
    if (effectiveWidth && effectiveHeight) {
      const newWidth = Math.round(effectiveWidth * newScale);
      const newHeight = Math.round(effectiveHeight * newScale);
      
      setWidth(newWidth);
      setHeight(newHeight);
      
      // 检查新的尺寸是否超出限制
      setIsSizeExceeded(checkSizeLimit(newWidth, newHeight));
    }
  };

  // 处理2倍按钮点击
  const handle2xClick = () => {
    handleScaleChange(2);
  };

  // 处理4倍按钮点击
  const handle4xClick = () => {
    handleScaleChange(4);
  };

  // 处理原始高清按钮点击
  const handleOriginalClick = () => {
    handleScaleChange(1);
  };

  // 计算滑块填充宽度的百分比
  const calculateFillWidth = () => {
    const min = 1.0;
    const max = 4.0;
    const range = max - min;
    const currentScale = scale || 2.0; // 使用默认值2.0，避免scale为null
    const position = currentScale - min;
    return position * (100 / range);
  };

  // 处理自定义宽度变化
  const handleCustomWidthChange = (e) => {
    const inputValue = e.target.value;
    const newWidth = inputValue === '' ? '' : parseInt(inputValue);
    setCustomWidth(newWidth);
    
    // 更新高度（仅当宽度为数字时）
    if (aspectRatio && typeof newWidth === 'number' && newWidth > 0) {
      const calculatedHeight = Math.round(newWidth / aspectRatio);
      setCustomHeight(calculatedHeight);
      
      // 检查新的尺寸是否超出限制
      setIsSizeExceeded(checkSizeLimit(newWidth, calculatedHeight));
    }
  };
  
  // 处理自定义高度变化
  const handleCustomHeightChange = (e) => {
    const inputValue = e.target.value;
    const newHeight = inputValue === '' ? '' : parseInt(inputValue);
    setCustomHeight(newHeight);
    
    // 更新宽度（仅当高度为数字时）
    if (aspectRatio && typeof newHeight === 'number' && newHeight > 0) {
      const calculatedWidth = Math.round(newHeight * aspectRatio);
      setCustomWidth(calculatedWidth);
      
      // 检查新的尺寸是否超出限制
      setIsSizeExceeded(checkSizeLimit(calculatedWidth, newHeight));
    }
  };
  
  // 处理自定义宽度失去焦点
  const handleCustomWidthBlur = () => {
    // 将空值转换为0
    if (customWidth === '') {
      setCustomWidth(0);
      return;
    }
    
    // 确保值是数字
    let validWidth = typeof customWidth === 'number' ? customWidth : parseInt(customWidth, 10);
    
    // 确保值不小于原图宽度
    if (originalWidth && validWidth < originalWidth) {
      validWidth = originalWidth;
    }
    
    // 确保值不大于8000
    if (validWidth > 8000) {
      validWidth = 8000;
    }
    
    if (validWidth !== customWidth) {
      setCustomWidth(validWidth);
      
      // 如果锁定宽高比，同时更新高度
      if (aspectRatio) {
        const calculatedHeight = Math.round(validWidth / aspectRatio);
        const validHeight = Math.min(8000, calculatedHeight);
        setCustomHeight(validHeight);
        
        // 更新是否超出限制
        setIsSizeExceeded(checkSizeLimit(validWidth, validHeight));
      }
    }
  };
  
  // 处理自定义高度失去焦点
  const handleCustomHeightBlur = () => {
    // 将空值转换为0
    if (customHeight === '') {
      setCustomHeight(0);
      return;
    }
    
    // 确保值是数字
    let validHeight = typeof customHeight === 'number' ? customHeight : parseInt(customHeight, 10);
    
    // 确保值不小于原图高度
    if (originalHeight && validHeight < originalHeight) {
      validHeight = originalHeight;
    }
    
    // 确保值不大于8000
    if (validHeight > 8000) {
      validHeight = 8000;
    }
    
    if (validHeight !== customHeight) {
      setCustomHeight(validHeight);
      
      // 如果锁定宽高比，同时更新宽度
      if (aspectRatio) {
        const calculatedWidth = Math.round(validHeight * aspectRatio);
        const validWidth = Math.min(8000, calculatedWidth);
        setCustomWidth(validWidth);
        
        // 更新是否超出限制
        setIsSizeExceeded(checkSizeLimit(validWidth, validHeight));
      }
    }
  };

  // 处理确定按钮点击
  const handleApply = () => {
    // 如果尺寸超出限制，禁止保存
    if (isSizeExceeded) {
      return;
    }
    
    // 根据当前活动标签页，创建不同的设置对象
    let settings;
    
    if (activeTab === 'scale') {
      // 倍数标签页的设置
      settings = {
        scale: scale || 2.0, // 提供默认值
        width: width || 0,
        height: height || 0,
        hasOriginalImage,
        activeRatio, // 保存当前选中的宽高比
        activeTab
      };
    } else {
      // 自定义尺寸标签页的设置
      // 计算实际的缩放比例
      const customScale = originalWidth ? customWidth / originalWidth : scale || 2.0;
      
      settings = {
        scale: customScale,
        width: customWidth || 0,
        height: customHeight || 0,
        hasOriginalImage,
        activeRatio, // 保存当前选中的宽高比
        activeTab
      };
    }
    
    // 调用回调函数
    if (onApply) {
      onApply(settings);
    }
    
    // 关闭弹窗
    if (onClose) {
      onClose();
    }
  };

  // 弹窗拖动相关处理函数
  const handleModalMouseMove = useCallback((e) => {
    if (isModalDragging) {
      const newX = e.clientX - modalDragOffset.x;
      const newY = e.clientY - modalDragOffset.y;
      
      // 限制在视窗范围内
      const maxX = window.innerWidth - 500; // 弹窗宽度
      const maxY = window.innerHeight - 600; // 弹窗大概高度
      
      setModalPosition({
        x: Math.max(0, Math.min(newX, maxX)),
        y: Math.max(0, Math.min(newY, maxY))
      });
    }
  }, [isModalDragging, modalDragOffset]);

  const handleModalMouseUp = useCallback(() => {
    if (isModalDragging) {
      setIsModalDragging(false);
      
      // 恢复文本选择
      document.body.classList.remove('no-select');
      
      // 移除拖动状态类
      const modalElement = document.querySelector('.magnification-size-modal');
      if (modalElement) {
        modalElement.classList.remove('dragging');
      }
    }
  }, [isModalDragging]);

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isModalDragging) {
      document.addEventListener('mousemove', handleModalMouseMove);
      document.addEventListener('mouseup', handleModalMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleModalMouseMove);
        document.removeEventListener('mouseup', handleModalMouseUp);
      };
    }
  }, [isModalDragging, handleModalMouseMove, handleModalMouseUp]);

  // 在弹窗显示时重置拖动位置
  useEffect(() => {
    if (visible) {
      setModalPosition({ x: 0, y: 0 });
      setIsModalDragging(false);
    }
  }, [visible]);

  // 使用实际尺寸或传入的尺寸
  const displayWidth = actualWidth || originalWidth;
  const displayHeight = actualHeight || originalHeight;

  // 处理点击外部区域关闭弹窗
  const handleOutsideClick = (e) => {
    // 如果点击的是外部容器而不是弹窗本身，则关闭弹窗
    if (e.target.className === 'magnification-size-wrapper') {
      onClose();
    }
  };

  const handleModalMouseDown = (e) => {
    // 只在标题区域允许拖动
    if (e.target.closest('.modal-header') && !e.target.closest('button')) {
      setIsModalDragging(true);
      
      const modalElement = e.target.closest('.magnification-size-modal');
      const rect = modalElement.getBoundingClientRect();
      
      setModalDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
      
      // 防止文本选择
      document.body.classList.add('no-select');
      modalElement.classList.add('dragging');
      
      e.preventDefault();
    }
  };

  return visible ? (
    <div className="magnification-size-wrapper" onClick={handleOutsideClick}>
      <div 
        className="modal-content magnification-size-modal" 
        style={{
          ...style,
          ...(modalPosition.x !== 0 || modalPosition.y !== 0 ? {
            left: modalPosition.x,
            top: modalPosition.y
          } : {}),
          cursor: isModalDragging ? 'grabbing' : 'default'
        }}
        onMouseDown={handleModalMouseDown}
      >
        <div className="modal-header" style={{ cursor: 'grab' }}>
          <div className="tab-group">
            <button 
              className="tab-btn active"
              onClick={() => setActiveTab('scale')}
            >
              放大倍数
            </button>
            {/* 暂时隐藏自定义尺寸标签 
            <button 
              className={`tab-btn ${activeTab === 'custom' ? 'active' : ''}`}
              onClick={() => setActiveTab('custom')}
            >
              自定义尺寸
            </button>
            */}
          </div>
          <button className="medium-close-button" onClick={onClose}>
            <MdClose />
          </button>
        </div>
        
        <div className="modal-body">
          <div className="magnification-size-content">
            {activeTab === 'scale' && (
              <div className="tab-content scale-tab">
                <div className="size-info">
                  <div className="original-size">
                    <span className="size-label">原图尺寸</span>
                    <span className="size-value">
                      {hasOriginalImage ? 
                        isLoadingImageDimensions ? '加载中...' : `${displayWidth} × ${displayHeight}px` 
                        : '未上传原图'}
                    </span>
                  </div>
                  <div className="upscaled-size">
                    <span className="size-label">放大后尺寸</span>
                    <span className="size-value">
                      {hasOriginalImage ? `${width} × ${height}px` : '无法计算'}
                    </span>
                  </div>
                </div>
                
                <div className="scale-buttons">
                  <button 
                    className={`scale-btn ${scale === 1 ? 'active' : ''}`}
                    onClick={handleOriginalClick}
                    disabled={!hasOriginalImage}
                  >
                    原尺寸变高清
                  </button>
                  <button 
                    className={`scale-btn ${scale === 2 ? 'active' : ''}`}
                    onClick={handle2xClick}
                    disabled={!hasOriginalImage}
                  >
                    变高清 + 2倍放大
                  </button>
                  <button 
                    className={`scale-btn ${scale === 4 ? 'active' : ''}`}
                    onClick={handle4xClick}
                    disabled={!hasOriginalImage}
                  >
                    变高清 + 4倍放大
                  </button>
                </div>
                
                <div className="scale-slider">
                  <div className="custom-scale-header">
                    <span className="custom-scale-label">自定义倍数</span>
                    <span className="scale-value">{scale ? `${scale.toFixed(1)}x` : '2.0x'}</span>
                  </div>
                  <div className="slider-track">
                    <div 
                      className="slider-fill" 
                      style={{ width: `${calculateFillWidth()}%` }}
                    ></div>
                    <input 
                      type="range" 
                      min={1.0} 
                      max={4.0} 
                      step={0.1}
                      value={scale || 2.0}
                      onChange={(e) => handleScaleChange(parseFloat(e.target.value))}
                      className="slider-input"
                      disabled={!hasOriginalImage}
                    />
                  </div>
                </div>
                
                <div className={`size-limit-notice ${isSizeExceeded ? 'size-limit-exceeded' : ''}`}>
                  注意：放大后的尺寸需要大于原图，小于 8000px × 8000px
                </div>
                
                {!hasOriginalImage && (
                  <div className="no-image-warning">
                    请先上传原图，才能设置放大倍数和尺寸
                  </div>
                )}
              </div>
            )}
            
            {/* 暂时隐藏自定义尺寸内容
            {activeTab === 'custom' && (
              <div className="tab-content custom-tab">
                <div className="size-info">
                  <div className="original-size">
                    <span className="size-label">原图尺寸</span>
                    <span className="size-value">
                      {hasOriginalImage ? `${originalWidth} × ${originalHeight}px` : '未上传原图'}
                    </span>
                  </div>
                </div>
                
                {hasOriginalImage ? (
                  <div className="custom-size-inputs">
                    <div className="size-input-container">
                      <div className="size-input-group">
                        <span className="size-label">宽</span>
                        <input
                          type="number"
                          className="size-input"
                          value={customWidth}
                          onChange={handleCustomWidthChange}
                          onBlur={handleCustomWidthBlur}
                          min={originalWidth}
                          max={8000}
                          disabled={!hasOriginalImage}
                        />
                        <span className="size-unit">px</span>
                      </div>
                      
                      <div className="aspect-ratio-icon" title="宽高比已锁定">
                        <MdLock />
                      </div>
                      
                      <div className="size-input-group">
                        <span className="size-label">高</span>
                        <input
                          type="number"
                          className="size-input"
                          value={customHeight}
                          onChange={handleCustomHeightChange}
                          onBlur={handleCustomHeightBlur}
                          min={originalHeight}
                          max={8000}
                          disabled={!hasOriginalImage}
                        />
                        <span className="size-unit">px</span>
                      </div>
                    </div>
                    
                    <div className={`size-limit-notice ${isSizeExceeded ? 'size-limit-exceeded' : ''}`}>
                      注意：放大后的尺寸需要大于原图，小于 8000px × 8000px
                    </div>
                  </div>
                ) : (
                  <div className="no-image-warning">
                    请先上传原图，才能设置放大倍数和尺寸
                  </div>
                )}
              </div>
            )}
            */}
          </div>
        </div>
        
        <div className="modal-footer">
          <button className="clear-btn" onClick={onClose}>取消</button>
          <button 
            className="save-settings-btn" 
            onClick={handleApply}
            disabled={!hasOriginalImage || isSizeExceeded}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  ) : null;
};

MagnificationSizeModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onApply: PropTypes.func,
  defaultScale: PropTypes.number,
  originalWidth: PropTypes.number,
  originalHeight: PropTypes.number,
  originalImage: PropTypes.string,
  style: PropTypes.object,
  savedSettings: PropTypes.object
};

export default MagnificationSizeModal; 