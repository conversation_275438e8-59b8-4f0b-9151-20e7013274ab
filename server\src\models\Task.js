const mongoose = require('mongoose');

/**
 * 组件通用结构 - 所有组件的基础结构
 */
const componentSchema = new mongoose.Schema({
  componentType: {
    type: String,
    required: true
  },
  componentId: {
    type: String,
    required: true
  },
  name: String,
  isMainImage: {
    type: Boolean,
    default: false
  },
  // 确保添加 serverFileName 字段到组件基础结构中
  serverFileName: {
    type: String
  }
}, { _id: false, strict: false }); // 使用strict: false允许存储任意其他字段

/**
 * 图片组件结构
 */
const imageComponentSchema = new mongoose.Schema({
  serverFileName: String, // 使用serverFileName替代旧字段
  url: String,
  fileInfo: {
    width: Number,
    height: Number,
    size: Number,
    type: String
  }
}, { _id: false, strict: false });

/**
 * 任务模型定义
 * 注意: 系统已完全迁移到components结构，不再使用旧的settings结构
 * 所有任务配置都存储在components对象中
 */
const taskSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.Mixed,
    ref: 'User',
    required: true
  },
  taskId: {
    type: String,
    required: true,
    unique: true
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  },
  taskType: {
    type: String,
    default: 'unknown'
  },
  pageType: {
    type: String,
    default: 'unknown'
  },
  imageType: {
    type: String,
    default: 'original'  // 更改默认值为original
  },
  // 标记任务是否需要图片
  requiresImage: {
    type: Boolean,
    default: true // 默认需要图片
  },
  // 主要图片文件的服务器文件名
  primaryImageFileName: {
    type: String,
    default: null
  },
  // 服务器存储的文件名，与primaryImageFileName保持一致
  serverFileName: {
    type: String,
    default: null
  },
  imageCount: {
    type: Number,
    default: 1,  // 默认值为1
  },
  // 组件列表 - 改为数组结构
  components: {
    type: [mongoose.Schema.Types.Mixed], // 数组类型
    default: []
  },
  // 移除对 o旧字段 的引用，改用 serverFileName
  generatedImages: [{
    filename: String,
    url: String, // 使用url字段替代path
    serverFileName: String, // 添加serverFileName字段
    createdAt: Date
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  strict: false // 允许保存Schema中未定义的字段
});

// 添加索引以提高查询性能
taskSchema.index({ userId: 1, createdAt: -1 });
taskSchema.index({ taskId: 1 }, { unique: true });
taskSchema.index({ primaryImageFileName: 1 });
taskSchema.index({ serverFileName: 1 }); // 添加serverFileName索引

// 添加一个保存前的钩子
taskSchema.pre('save', function(next) {  
  // 确保id字段不为null，如果为null则删除它
  if (this.id === null || this.id === undefined) {
    delete this.id;
  }
  
  // 确保imageCount字段有值
  if (!this.imageCount) {
    // 如果有images数组，使用其长度
    if (this.images && Array.isArray(this.images) && this.images.length > 0) {
      this.imageCount = this.images.length;
    } 
    // 从组件中获取数量
    else if (this.components && Array.isArray(this.components)) {
      // 从数组组件中计算sourceImagePanel的数量
      const sourceImagePanelsCount = this.components.filter(comp => 
        comp.componentType === 'sourceImagePanel' || comp.type === 'sourceImagePanel'
      ).length;
      
      if (sourceImagePanelsCount > 0) {
        this.imageCount = sourceImagePanelsCount;
      } else {
        this.imageCount = 1;
      }
    }
    // 如果都没有，则使用默认值1
    else {
      this.imageCount = 1;
    }
  }

  // 只有当任务需要图片时才设置primaryImageFileName和serverFileName
  if (this.requiresImage && this.components && this.components.length > 0) {
    // 查找标记为主图像的组件
    const mainImageComponent = this.components.find(comp => comp.isMainImage && comp.serverFileName);
    
    if (mainImageComponent) {
      this.primaryImageFileName = mainImageComponent.serverFileName;
      // 同时更新serverFileName，确保两者一致
      this.serverFileName = mainImageComponent.serverFileName;
    }
  }
  
  // 如果primaryImageFileName存在但serverFileName不存在，则同步更新
  if (this.primaryImageFileName && !this.serverFileName) {
    this.serverFileName = this.primaryImageFileName;
  }
  
  // 反之亦然，确保两个字段一致
  if (this.serverFileName && !this.primaryImageFileName) {
    this.primaryImageFileName = this.serverFileName;
  }
  
  next();
});

const Task = mongoose.model('Task', taskSchema);

module.exports = Task; 