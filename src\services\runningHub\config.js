/**
 * RunningHub配置管理器
 * 管理API密钥、工作流配置等
 */

/**
 * RunningHub配置类
 */
class RunningHubConfig {
  constructor() {
    this.configs = new Map();
    this.defaultConfig = null;
  }

  /**
   * 添加配置
   * @param {string} name - 配置名称
   * @param {Object} config - 配置对象
   * @param {string} config.apiKey - API密钥
   * @param {string} config.workflowId - 工作流ID
   * @param {string} config.description - 配置描述
   * @param {Object} config.metadata - 元数据
   * @param {boolean} isDefault - 是否设为默认配置
   */
  addConfig(name, config, isDefault = false) {
    if (!config.apiKey || !config.workflowId) {
      throw new Error('API密钥和工作流ID是必需的');
    }

    const configData = {
      name,
      apiKey: config.apiKey,
      workflowId: config.workflowId,
      description: config.description || '',
      metadata: config.metadata || {},
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.configs.set(name, configData);

    if (isDefault || this.configs.size === 1) {
      this.defaultConfig = name;
    }

    return configData;
  }

  /**
   * 获取配置
   * @param {string} name - 配置名称，如果为空则返回默认配置
   * @returns {Object|null} 配置对象
   */
  getConfig(name = null) {
    const configName = name || this.defaultConfig;
    return this.configs.get(configName) || null;
  }

  /**
   * 更新配置
   * @param {string} name - 配置名称
   * @param {Object} updates - 更新的配置项
   * @returns {Object|null} 更新后的配置对象
   */
  updateConfig(name, updates) {
    const config = this.configs.get(name);
    if (!config) {
      return null;
    }

    const updatedConfig = {
      ...config,
      ...updates,
      updatedAt: new Date()
    };

    this.configs.set(name, updatedConfig);
    return updatedConfig;
  }

  /**
   * 删除配置
   * @param {string} name - 配置名称
   * @returns {boolean} 是否删除成功
   */
  removeConfig(name) {
    const deleted = this.configs.delete(name);
    
    // 如果删除的是默认配置，重新设置默认配置
    if (deleted && this.defaultConfig === name) {
      const remainingConfigs = Array.from(this.configs.keys());
      this.defaultConfig = remainingConfigs.length > 0 ? remainingConfigs[0] : null;
    }

    return deleted;
  }

  /**
   * 设置默认配置
   * @param {string} name - 配置名称
   * @returns {boolean} 是否设置成功
   */
  setDefaultConfig(name) {
    if (this.configs.has(name)) {
      this.defaultConfig = name;
      return true;
    }
    return false;
  }

  /**
   * 获取所有配置
   * @returns {Array} 配置列表
   */
  getAllConfigs() {
    return Array.from(this.configs.values());
  }

  /**
   * 获取配置名称列表
   * @returns {Array} 配置名称列表
   */
  getConfigNames() {
    return Array.from(this.configs.keys());
  }

  /**
   * 检查配置是否存在
   * @param {string} name - 配置名称
   * @returns {boolean} 是否存在
   */
  hasConfig(name) {
    return this.configs.has(name);
  }

  /**
   * 获取默认配置名称
   * @returns {string|null} 默认配置名称
   */
  getDefaultConfigName() {
    return this.defaultConfig;
  }

  /**
   * 清空所有配置
   */
  clearAll() {
    this.configs.clear();
    this.defaultConfig = null;
  }

  /**
   * 从JSON导入配置
   * @param {Object} configsData - 配置数据
   */
  importConfigs(configsData) {
    if (!configsData || typeof configsData !== 'object') {
      throw new Error('无效的配置数据');
    }

    if (configsData.configs && Array.isArray(configsData.configs)) {
      configsData.configs.forEach(config => {
        this.addConfig(config.name, config, config.name === configsData.defaultConfig);
      });
    }
  }

  /**
   * 导出配置为JSON
   * @returns {Object} 配置数据
   */
  exportConfigs() {
    return {
      defaultConfig: this.defaultConfig,
      configs: Array.from(this.configs.values()),
      exportedAt: new Date()
    };
  }

  /**
   * 验证配置
   * @param {string} name - 配置名称
   * @returns {Promise<Object>} 验证结果
   */
  async validateConfig(name) {
    const config = this.getConfig(name);
    if (!config) {
      return {
        valid: false,
        error: '配置不存在'
      };
    }

    try {
      // 这里可以添加实际的API验证逻辑
      // 例如调用RunningHub API验证密钥和工作流ID
      
      return {
        valid: true,
        config: config
      };
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }
}

// 预定义的工作流配置模板
export const WORKFLOW_TEMPLATES = {
  TEXT_TO_IMAGE: {
    name: '文本生图',
    description: '基于文本提示生成图像',
    nodeInfoList: [
      {
        nodeId: '6',
        fieldName: 'text',
        fieldValue: ''
      }
    ]
  },
  IMAGE_TO_IMAGE: {
    name: '图像转换',
    description: '基于输入图像进行转换',
    nodeInfoList: [
      {
        nodeId: '10',
        fieldName: 'image',
        fieldValue: ''
      }
    ]
  },
  STYLE_TRANSFER: {
    name: '风格迁移',
    description: '将一种风格应用到另一张图像上',
    nodeInfoList: [
      {
        nodeId: '12',
        fieldName: 'content_image',
        fieldValue: ''
      },
      {
        nodeId: '13',
        fieldName: 'style_image',
        fieldValue: ''
      }
    ]
  }
};

// 创建全局配置管理器实例
const runningHubConfig = new RunningHubConfig();

// 导出配置管理器和模板
export { runningHubConfig, WORKFLOW_TEMPLATES };
export default runningHubConfig;
