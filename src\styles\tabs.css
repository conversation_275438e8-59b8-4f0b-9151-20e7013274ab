/**
 * 统一标签页样式定义
 * 
 * 包含以下组件：
 * 1. 标签页容器 (.modal-tabs, .tab-group) - 用于组织和布局标签按钮
 * 2. 标签按钮 (.tab-button, .tab-btn) - 可点击的标签页按钮
 * 3. 激活状态 (.active) - 当前选中标签的样式
 * 4. 悬停效果 - 标签按钮的交互反馈
 * 
 * 特点：
 * - 支持多个标签页并排显示
 * - 自适应宽度和内容
 * - 圆角设计
 * - 平滑过渡动画
 * - 支持禁用状态
 * 
 * 使用示例：
 * <div class="tab-group">
 *   <button class="tab-btn active">标签1</button>
 *   <button class="tab-btn">标签2</button>
 *   <button class="tab-btn" disabled>标签3</button>
 * </div>
 */

@import './theme.css';

/* 标签页容器 */
.modal-tabs,
.tab-group {
  display: flex;
  gap: 0;
  height: 36px;
  margin-bottom: -1px;
  margin-left: 16px;
  width: 100%;
  border-bottom: none;
}

/* 标签按钮 */
.tab-button,
.tab-btn {
  padding: 0 24px;
  border: 1px solid transparent;
  background: transparent;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  position: relative;
  transition: var(--transition-fast);
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  margin-right: 1px;
  user-select: none;
  white-space: nowrap;
}

/* 第一个和最后一个标签按钮的特殊边距 */
.tab-button:first-child,
.tab-btn:first-child {
  margin-left: 4px;
}

.tab-button:last-child,
.tab-btn:last-child {
  margin-right: 4px;
}

/* 激活状态 */
.tab-button.active,
.tab-btn.active {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-color);
  border-bottom-color: var(--bg-primary);
  font-weight: 500;
}

/* 激活标签的底部指示器 */
.tab-button.active::after,
.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--brand-primary);
  width: calc(100% - 48px);
  margin: 0 24px;
  transition: transform 0.2s ease;
}

/* 标签非激活状态 */
.tab-button:not(.active),
.tab-btn:not(.active) {
  background: transparent;
  border-color: transparent;
}

/* 添加标签按钮悬浮效果 */
.tab-button:hover,
.tab-btn:hover {
  background: transparent;
  color: var(--text-primary);
  position: relative;
}

/* 确保激活标签按钮保持其样式，不受悬浮影响 */
.tab-button.active:hover,
.tab-btn.active:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.tab-button:not(.active):hover::before,
.tab-btn:not(.active):hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(100% - 2px);
  height: 28px;
  background: var(--bg-hover);
  border-radius: var(--radius-md);
}

/* 确保激活状态的标签按钮没有悬浮效果 */
.tab-button.active:hover::before,
.tab-btn.active:hover::before {
  display: none;
}

/* 特殊模态框标签页样式 - 用于模特、场景和高级定制模态框 */
.model-select-modal .modal-tabs,
.scene-select-modal .modal-tabs,
.advanced-custom-modal .modal-tabs {
  margin-left: 0;
  margin-bottom: 0;
  margin-top: 0px;
  position: relative;
}

.model-select-modal .tab-button,
.scene-select-modal .tab-button,
.advanced-custom-modal .tab-button {
  bottom: -1px;
}

.model-select-modal .tab-button:first-child,
.scene-select-modal .tab-button:first-child,
.advanced-custom-modal .tab-button:first-child {
  margin-left: 0;
}

.model-select-modal .tab-button.active,
.scene-select-modal .tab-button.active,
.advanced-custom-modal .tab-button.active {
  z-index: 1;
}

.model-select-modal .tab-button.active::after,
.scene-select-modal .tab-button.active::after,
.advanced-custom-modal .tab-button.active::after {
  bottom: 0;
  z-index: 2;
}

/* 移动设备适配 */
@media (max-width: 768px) {
  .modal-tabs,
  .tab-group {
    height: 32px;
    margin-left: 16px;  /* 保持与PC端一致的边距 */
  }
  
  .tab-button,
  .tab-btn {
    padding: 0 24px;  /* 保持与PC端一致的padding */
    font-size: 12px;
    height: 32px;
  }
  
  .tab-button.active::after,
  .tab-btn.active::after {
    width: calc(100% - 48px);  /* 保持与PC端一致的宽度 */
    margin: 0 24px;  /* 保持与PC端一致的边距 */
  }
} 