/**
 * 初始化订阅计划脚本
 * 设置各套餐的功能权限和并发任务数
 */

const mongoose = require('mongoose');
const Plan = require('../modules/admin/subscribe/plan.model');
const Subscription = require('../modules/admin/subscribe/subscription.model');

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/aibikini';

const plans = [
  {
    code: 'free',
    name: '免费版',
    description: '基础功能免费使用，适合个人用户',
    price: { monthly: 0, yearly: 0, discount: 0 },
    features: {
      design: { enabled: false },
      model: { enabled: false },
      tools: { enabled: true },
      video: { enabled: false },
      support: { level: 'standard', responseTime: '5x8' }
    },
    usageQuota: { 
      totalRequests: 10, 
      dailyRequests: 2,
      maxConcurrentTasks: 1
    },
    isPublic: true,
    sortOrder: 1,
    isRecommended: false
  },
  {
    code: 'design',
    name: '设计版',
    description: '专注于服装设计功能，适合设计师',
    price: { monthly: 99, yearly: 999, discount: 15 },
    features: {
      design: { 
        enabled: true,
        trending: true,
        optimize: true,
        inspiration: true
      },
      model: { enabled: false },
      tools: { enabled: true },
      video: { enabled: false },
      support: { level: 'standard', responseTime: '5x8' }
    },
    usageQuota: { 
      totalRequests: 500, 
      dailyRequests: 20,
      maxConcurrentTasks: 2
    },
    isPublic: true,
    sortOrder: 2,
    isRecommended: true
  },
  {
    code: 'model',
    name: '模特版',
    description: '专注于模特换装功能，适合服装品牌',
    price: { monthly: 199, yearly: 1999, discount: 20 },
    features: {
      design: { enabled: false },
      model: { 
        enabled: true,
        fashion: true,
        'try-on': true,
        'change-model': true,
        recolor: true,
        fabric: true,
        background: true,
        virtual: true,
        'detail-migration': true,
        'hand-fix': true
      },
      tools: { enabled: true },
      video: { enabled: false },
      support: { level: 'standard', responseTime: '5x8' }
    },
    usageQuota: { 
      totalRequests: 1000, 
      dailyRequests: 50,
      maxConcurrentTasks: 3
    },
    isPublic: true,
    sortOrder: 3,
    isRecommended: true
  },
  {
    code: 'full',
    name: '完整版',
    description: '包含所有功能，适合专业用户',
    price: { monthly: 299, yearly: 2999, discount: 20 },
    features: {
      design: { 
        enabled: true,
        trending: true,
        optimize: true,
        inspiration: true
      },
      model: { 
        enabled: true,
        fashion: true,
        'try-on': true,
        'change-model': true,
        recolor: true,
        fabric: true,
        background: true,
        virtual: true,
        'detail-migration': true,
        'hand-fix': true
      },
      tools: { enabled: true },
      video: { 
        enabled: true,
        imgtextvideo: true,
        mulimgvideo: true
      },
      support: { level: 'premium', responseTime: '7x24' }
    },
    usageQuota: { 
      totalRequests: 2000, 
      dailyRequests: 100,
      maxConcurrentTasks: 5
    },
    isPublic: true,
    sortOrder: 4,
    isRecommended: true
  },
  {
    code: 'enterprise',
    name: '企业版',
    description: '企业级定制服务，适合大型企业',
    price: { monthly: 999, yearly: 9999, discount: 25 },
    features: {
      design: { 
        enabled: true,
        trending: true,
        optimize: true,
        inspiration: true
      },
      model: { 
        enabled: true,
        fashion: true,
        'try-on': true,
        'change-model': true,
        recolor: true,
        fabric: true,
        background: true,
        virtual: true,
        'detail-migration': true,
        'hand-fix': true
      },
      tools: { enabled: true },
      video: { 
        enabled: true,
        imgtextvideo: true,
        mulimgvideo: true
      },
      support: { level: 'enterprise', responseTime: '7x24' }
    },
    usageQuota: { 
      totalRequests: -1, // 无限制
      dailyRequests: -1, // 无限制
      maxConcurrentTasks: 10
    },
    isPublic: true,
    sortOrder: 5,
    isRecommended: false
  }
];

async function initPlans() {
  try {
    console.log('开始初始化订阅计划...');
    
    for (const planData of plans) {
      const existingPlan = await Plan.findOne({ code: planData.code });
      
      if (existingPlan) {
        console.log(`更新计划: ${planData.name}`);
        await Plan.findOneAndUpdate(
          { code: planData.code },
          planData,
          { new: true }
        );
      } else {
        console.log(`创建计划: ${planData.name}`);
        const newPlan = new Plan(planData);
        await newPlan.save();
      }
    }
    
    console.log('订阅计划初始化完成！');
    
    // 显示所有计划
    const allPlans = await Plan.find().sort({ sortOrder: 1 });
    console.log('\n当前所有订阅计划:');
    allPlans.forEach(plan => {
      console.log(`- ${plan.name} (${plan.code}): ${plan.price.monthly}元/月, 并发数: ${plan.usageQuota.maxConcurrentTasks}`);
    });
    
  } catch (error) {
    console.error('初始化订阅计划失败:', error);
  }
}

async function updateExistingSubscriptions() {
  try {
    console.log('\n开始更新现有订阅的并发数配置...');
    
    // 为现有订阅设置默认并发数
    const result = await Subscription.updateMany(
      { 'usageQuota.maxConcurrentTasks': { $exists: false } },
      { $set: { 'usageQuota.maxConcurrentTasks': 1 } }
    );
    
    console.log(`更新了 ${result.modifiedCount} 个订阅的并发数配置`);
    
  } catch (error) {
    console.error('更新现有订阅失败:', error);
  }
}

async function main() {
  try {
    // 连接数据库
    await mongoose.connect(MONGODB_URI);
    console.log('数据库连接成功');
    
    // 初始化计划
    await initPlans();
    
    // 更新现有订阅
    await updateExistingSubscriptions();
    
    console.log('\n所有操作完成！');
    
  } catch (error) {
    console.error('操作失败:', error);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { initPlans, updateExistingSubscriptions }; 