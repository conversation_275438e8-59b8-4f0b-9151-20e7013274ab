import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { MdRefresh, MdExpandLess, MdExpandMore, MdDone } from 'react-icons/md';
import './index.css';

// 修改默认值，全部设为0
const DEFAULT_VALUES = {
  hue: 0,         // -180 to 180
  saturation: 0,  // -1.00 to 1.00
  brightness: 0,  // -1.00 to 1.00
  contrast: 0     // -1.00 to 1.00
};

// 颜色转换辅助函数
// HEX转RGB
const hexToRgb = (hex) => {
  // 去掉可能的#前缀并转换为大写
  hex = hex.replace('#', '').toUpperCase();
  
  // 处理简写形式 (#RGB)
  if (hex.length === 3) {
    hex = hex.split('').map(c => c + c).join('');
  }
  
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  return { r, g, b };
};

// RGB转HSL
const rgbToHsl = (r, g, b) => {
  r /= 255;
  g /= 255;
  b /= 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h, s, l = (max + min) / 2;
  
  if (max === min) {
    h = s = 0;
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
      default: break;
    }
    
    h /= 6;
  }
  
  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  };
};

// HSL转CSS色值
const hslToColorString = (h, s, l) => {
  // 处理h值可能超出0-360范围的情况
  h = ((h % 360) + 360) % 360; // 确保h在0-360之间
  return `hsl(${h}, ${s}%, ${l}%)`;
};

// 将RGB转为16进制颜色
const toHex = (r, g, b) => {
  return '#' + [r, g, b].map(x => {
    const hex = x.toString(16).padStart(2, '0');
    return hex.toUpperCase(); // 确保使用大写字母
  }).join('');
};

const ColorAdjustPanel = ({
  initialValues = DEFAULT_VALUES,
  onChange,
  selectedColor = '#FF3C6A', // 默认使用品牌色
  onSave // 新增保存回调
}) => {
  const [values, setValues] = useState(initialValues);
  const [activeInput, setActiveInput] = useState(null);
  const [baseHue, setBaseHue] = useState(0);
  const prevColorRef = useRef(selectedColor);
  const [showTip, setShowTip] = useState(false); // 添加提示消息显示状态
  
  // 当selectedColor变化时，计算出它的基础色相和在需要时重置调整
  useEffect(() => {
    if (selectedColor) {
      const rgb = hexToRgb(selectedColor);
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
      setBaseHue(hsl.h);
      
      // 只有当颜色真正变化时，才重置调整值
      if (selectedColor !== prevColorRef.current) {
        prevColorRef.current = selectedColor;
        // 重置调整值
        setValues(DEFAULT_VALUES);
        // 通知父组件调整值已重置
        onChange && onChange(DEFAULT_VALUES);
      }
    }
  }, [selectedColor, onChange]);

  // 处理滑块变化
  const handleSliderChange = (property, value) => {
    const newValues = { ...values, [property]: value };
    setValues(newValues);
    onChange && onChange(newValues);
    setShowTip(true); // 当用户调整时显示提示
  };

  // 处理输入框变化
  const handleInputChange = (property, e) => {
    let value;
    
    // 根据不同属性处理数值
    if (property === 'hue') {
      value = parseInt(e.target.value) || 0;
      value = Math.max(-180, Math.min(180, value));
    } else {
      // 对于小数值属性，保留两位小数
      value = parseFloat(parseFloat(e.target.value).toFixed(2)) || 0;
      value = Math.max(-1.00, Math.min(1.00, value));
    }
    
    handleSliderChange(property, value);
    setShowTip(true); // 当用户调整时显示提示
  };

  // 重置所有值
  const handleReset = () => {
    setValues(DEFAULT_VALUES);
    onChange && onChange(DEFAULT_VALUES);
    setShowTip(false); // 重置后隐藏提示
  };

  // 添加增加或减少数值的函数
  const adjustValue = (property, increment) => {
    let value;
    
    if (property === 'hue') {
      // 色调调整幅度为1
      value = values[property] + increment;
      value = Math.max(-180, Math.min(180, value));
    } else {
      // 其他调整幅度为0.01
      value = parseFloat((values[property] + increment).toFixed(2));
      value = Math.max(-1.00, Math.min(1.00, value));
    }
    
    handleSliderChange(property, value);
    setShowTip(true); // 当用户调整时显示提示
  };

  // 计算滑块填充宽度（从中间开始）
  const calculateFillStyle = (property, value) => {
    if (property === 'hue') {
      // 动态生成基于当前选中颜色的色调渐变
      const gradientSteps = [];
      const stepCount = 7; // 渐变颜色数量
      
      // 起始色相和饱和度、亮度
      const rgb = hexToRgb(selectedColor);
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
      const s = hsl.s;
      const l = hsl.l;
      
      // 生成色相环上均匀分布的颜色
      for (let i = 0; i < stepCount; i++) {
        // 计算当前位置应该对应的色相偏移值
        // -180到+180之间均匀分布7个点
        const offset = -180 + (360 / (stepCount - 1)) * i;
        // 计算实际色相值（考虑循环）
        const hue = ((baseHue + offset) % 360 + 360) % 360;
        // 添加到渐变数组
        gradientSteps.push(hslToColorString(hue, s, l));
      }
      
      // 返回线性渐变
      return {
        background: `linear-gradient(to right, ${gradientSteps.join(', ')})`
      };
    } else if (property === 'saturation') {
      // 饱和度滑块 - 动态基于当前选中颜色计算
      const rgb = hexToRgb(selectedColor);
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
      
      // 使用当前颜色的色相和亮度，但创建从0%到100%饱和度的渐变
      const desaturatedColor = hslToColorString(hsl.h, 0, hsl.l); // 灰色（完全去饱和）
      const midSaturationColor = hslToColorString(hsl.h, 50, hsl.l); // 中等饱和度
      const fullySaturatedColor = hslToColorString(hsl.h, 100, hsl.l); // 完全饱和
      
      // 返回三点渐变
      return {
        background: `linear-gradient(to right, 
          ${desaturatedColor}, 
          ${midSaturationColor},
          ${fullySaturatedColor})`
      };
    } else if (property === 'brightness') {
      // 亮度滑块 - 动态基于当前选中颜色计算
      const rgb = hexToRgb(selectedColor);
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
      
      // 保持相同的色相和饱和度，但创建不同亮度的变体
      // 注意：亮度在HSL中是0-100%，其中0%是黑色，50%是正常亮度，100%是白色
      const darkColor = hslToColorString(hsl.h, hsl.s, 0); // 最暗（黑色）
      const midColor = hslToColorString(hsl.h, hsl.s, 50); // 中等亮度
      const brightColor = hslToColorString(hsl.h, hsl.s, 100); // 最亮（白色）
      
      // 返回三点渐变
      return {
        background: `linear-gradient(to right, 
          ${darkColor}, 
          ${midColor},
          ${brightColor})`
      };
    } else if (property === 'contrast') {
      // 对比度滑块 - 使用默认渐变
      return {}; // 使用CSS中定义的渐变
    }
    
    return {}; // 默认返回空样式
  };
  
  // 渲染单个调节器
  const renderAdjuster = (property, label, min, max, step) => {
    const value = values[property];
    
    // 格式化显示值
    const formatDisplayValue = () => {
      if (property === 'hue') {
        return value;
      } else {
        return value.toFixed(2);
      }
    };
    
    return (
      <div className="color-adjuster" data-property={property}>
        <div className="adjuster-label">
          <span>{label}</span>
          <div className="adjuster-value-input"
               onMouseEnter={() => setActiveInput(property)}
               onMouseLeave={() => setActiveInput(null)}>
            <input
              type="number"
              value={formatDisplayValue()}
              onChange={(e) => handleInputChange(property, e)}
              min={min}
              max={max}
              step={step}
            />
            {activeInput === property && (
              <div className="number-controls">
                <button 
                  className="number-control-btn" 
                  onClick={() => adjustValue(property, property === 'hue' ? 1 : 0.01)}
                >
                  <MdExpandLess />
                </button>
                <button 
                  className="number-control-btn" 
                  onClick={() => adjustValue(property, property === 'hue' ? -1 : -0.01)}
                >
                  <MdExpandMore />
                </button>
              </div>
            )}
          </div>
        </div>
        <div className="adjuster-slider">
          <div className="color-adjust-slider-track">
            {/* 标记0位置 */}
            <div className="color-adjust-slider-zero-mark"></div>
            {/* 填充部分 - 设置为填满整个轨道 */}
            <div 
              className="color-adjust-slider-fill" 
              style={calculateFillStyle(property, value)}
            ></div>
            <input 
              type="range" 
              min={min} 
              max={max} 
              step={step}
              value={value}
              onChange={(e) => {
                const val = property === 'hue' 
                  ? parseInt(e.target.value) 
                  : parseFloat(e.target.value);
                handleSliderChange(property, val);
              }}
              className="color-adjust-slider-input"
            />
          </div>
        </div>
      </div>
    );
  };

  // 添加保存调整的处理函数
  const handleSave = () => {
    // 调用保存回调，传递当前的调整值
    if (onSave) {
      onSave(values);
    }
    setShowTip(false); // 保存后隐藏提示
  };

  return (
    <div className="color-adjust-panel">
      <div className="panel-header">
        <h3>颜色微调（非必填）</h3>
        <div className="header-content">
          {showTip && (
            <span className="submit-tip">点击 √ 提交调整结果</span>
          )}
          <div className="panel-buttons">
            <button className="reset-button" onClick={handleReset} title="重置所有调整">
              <MdRefresh />
            </button>
            <button className="color-adjust-save-button" onClick={handleSave} title="保存调整结果">
              <MdDone />
            </button>
          </div>
        </div>
      </div>
      <div className="adjusters-container">
        {renderAdjuster('hue', '色调', -180, 180, 1)}
        {renderAdjuster('saturation', '饱和度', -1.00, 1.00, 0.01)}
        {renderAdjuster('brightness', '亮度', -1.00, 1.00, 0.01)}
        {renderAdjuster('contrast', '对比度', -1.00, 1.00, 0.01)}
      </div>
    </div>
  );
};

ColorAdjustPanel.propTypes = {
  initialValues: PropTypes.shape({
    hue: PropTypes.number,
    saturation: PropTypes.number,
    brightness: PropTypes.number,
    contrast: PropTypes.number
  }),
  onChange: PropTypes.func,
  selectedColor: PropTypes.string,
  onSave: PropTypes.func
};

export default ColorAdjustPanel; 