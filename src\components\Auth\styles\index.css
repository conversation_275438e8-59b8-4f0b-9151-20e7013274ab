/* 导入所有认证相关样式 */

/* 通用样式 */
@import './common.css';

/* 组件特定样式 */
@import './LoginForm.css';
@import './RegisterForm.css';
@import './ResetPasswordForm.css';
@import './PasswordStrength.css';
@import './CaptchaHandler.css';
@import './PasswordInput.css';
@import './AuthModals.css';
@import './Tabs.css';
@import './MessageStyles.css';

/* 动画效果 */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* 错误状态的动画 */
.shake {
  animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
}

.modal-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  max-height: 90vh;
  overflow-y: auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.send-code-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
} 