# 实例常开功能说明

## 功能概述

实例常开功能允许管理员将特定的实例设置为常开模式。设置为常开的实例不会被系统的定时任务自动关机，即使空闲时间超过设定的阈值（默认10分钟）。

## 功能特点

1. **常开模式设置**：管理员可以为任意实例开启或关闭常开模式
2. **自动关机豁免**：常开实例不会被定时任务自动关机
3. **状态显示**：在实例列表中显示常开状态
4. **统计信息**：在统计面板中显示常开实例数量
5. **详情查看**：在实例详情中显示常开状态和说明

## 数据库变更

### Instance模型新增字段

```javascript
// 添加常开字段
isAlwaysOn: {
  type: Boolean,
  default: false,
  description: '是否设置为常开模式，常开实例不会被自动关机'
}

// 添加appId字段（用于关联云服务实例）
appId: {
  type: String,
  required: true,
  unique: true,
  description: '云服务中的实例ID，用于关联云服务实例'
}
```

### 新增索引

```javascript
instanceSchema.index({ isAlwaysOn: 1 }); // 添加常开字段索引
instanceSchema.index({ appId: 1 }); // 添加appId字段索引
```

## API接口

### 设置实例常开模式

**POST** `/admin/instances/:id/always-on`

**请求体：**
```json
{
  "isAlwaysOn": true
}
```

**响应：**
```json
{
  "success": true,
  "message": "实例已设置为常开模式",
  "data": {
    "instanceId": "instance-123",
    "isAlwaysOn": true
  }
}
```

### 获取实例常开状态

**GET** `/admin/instances/:id/always-on`

**响应：**
```json
{
  "success": true,
  "data": {
    "instanceId": "instance-123",
    "isAlwaysOn": false
  }
}
```

## 前端界面

### 实例列表

- 新增"常开"列，显示开关控件
- 可以直接在列表中切换常开状态
- 常开实例会有特殊标识

### 实例详情

- 在详情抽屉中显示常开状态
- 提供状态说明文字
- 可以直接在详情中切换常开状态

### 统计面板

- 新增"常开实例"统计卡片
- 显示当前常开实例的数量
- 使用蓝色主题色区分

## 后端逻辑变更

### instanceService.js

1. **checkAndStopIdleInstance方法**：
   - 在检查空闲实例前，先检查是否为常开实例
   - 如果是常开实例，跳过自动关机检查

2. **定时任务**：
   - 在定期检查空闲实例时，排除常开实例
   - 避免对常开实例执行关机操作

### 实例列表API

- 在返回实例列表时，包含常开状态信息
- 通过查询Instance模型获取常开配置

### 自动创建实例记录

- 当设置常开模式时，如果实例不在数据库中，会自动从云服务API获取实例信息并创建记录
- 确保所有云服务实例都可以设置常开模式，即使之前没有在数据库中记录

## 使用场景

1. **重要实例保护**：对于重要的生产实例，设置为常开模式避免意外关机
2. **开发测试**：开发人员使用的测试实例可以设置为常开模式
3. **特殊需求**：某些需要持续运行的实例可以设置为常开模式

## 注意事项

1. **成本考虑**：常开实例会持续产生费用，请谨慎使用
2. **资源管理**：建议定期检查常开实例的使用情况
3. **权限控制**：只有管理员可以设置常开模式
4. **监控建议**：建议对常开实例进行额外的监控和告警

## 测试

可以使用提供的测试文件验证功能：

```bash
cd server
node test-always-on.js
```

## 配置说明

- **空闲超时时间**：默认10分钟（可在instanceService.js中修改IDLE_TIMEOUT）
- **检查频率**：默认每分钟检查一次（可在定时任务中修改）
- **权限要求**：需要管理员权限

## 未来扩展

1. **批量操作**：支持批量设置常开模式
2. **时间限制**：支持设置常开模式的过期时间
3. **自动恢复**：支持在特定条件下自动取消常开模式
4. **使用统计**：统计常开实例的使用情况和成本 