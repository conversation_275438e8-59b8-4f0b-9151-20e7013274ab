{"1": {"inputs": {"upscale_model": ["2", 0], "image": ["16", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "2": {"inputs": {"model_name": "RealESRGAN_x4.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "6": {"inputs": {"filename_prefix": "Upscale", "images": ["11", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "11": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["12", 1], "image": ["1", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "12": {"inputs": {"expression": "a/4", "speak_and_recognation": {"__value__": [false, true]}, "a": ["15", 0]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "15": {"inputs": {"needInput": true, "Number": "2"}, "class_type": "Float", "_meta": {"title": "放大倍数"}}, "16": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "原始图片上传"}}}