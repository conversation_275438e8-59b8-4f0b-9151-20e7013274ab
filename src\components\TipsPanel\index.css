.tips-setting {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  height: 88px;
  display: flex;
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.tips-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
}

.tips-label {
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
}

.tips-label span {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

.tips-area {
  flex: 1;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
}

.tips-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.7;
  overflow-y: auto;
  max-height: 80px;
  padding: 8px 0;
  width: 100%;
} 