/* 算力明细页面独立样式 */
.account-page {
  width: 100%;
  min-height: calc(100vh - 68px);
  background: var(--bg-secondary);
  padding: 12px;
}

.account-container {
  max-width: 1800px;
  margin: 0 auto;
  display: block;
}

.stats-card {
  margin-bottom: 12px !important;
}

.transactions-card {
  margin-bottom: 0 !important;
}

/* 统计数值全局样式 - 确保数值能够正确换行 */
.account-page .ant-statistic-content {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: baseline !important;
  gap: 2px !important;
}

.account-page .ant-statistic-content-value {
  word-wrap: break-word !important;
  word-break: break-all !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
  max-width: 100% !important;
}

.account-page .ant-statistic-content-suffix {
  word-wrap: break-word !important;
  word-break: break-all !important;
  overflow-wrap: break-word !important;
  flex-shrink: 0 !important;
}

/* 统计卡片样式 */
.account-page .ant-statistic-title {
  font-size: 14px;
  color: var(--text-secondary);
}

.account-page .ant-statistic-content {
  font-size: 24px;
  color: var(--text-primary);
}

/* 表格样式 */
.account-page .transactions-card .account-transactions-table {
  background: var(--bg-primary) !important;
}

.account-page .transactions-card .account-transactions-table-inner {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* 表格头部样式 - 恢复原始短竖线分隔设计，与内容区域颜色一致*/
.account-page .transactions-card .account-transactions-table-header {
  background: var(--bg-primary) !important;
}

.account-page .transactions-card .account-transactions-table-header-row {
  background: var(--bg-primary) !important;
}

.account-page .transactions-card .account-transactions-table-header-cell {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
  border-bottom: 1px solid var(--border-color) !important;
  border-right: none !important;
  padding: 16px !important;
  text-align: left !important;
  position: relative !important;
}

/* 表头短竖线分隔 - 使用更强优先级确保主题变量生成*/
.account-page .transactions-card .account-transactions-table-header-cell:not(:last-child)::after {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  right: 0 !important;
  transform: translateY(-50%) !important;
  width: 1px !important;
  height: 1.6em !important;
  background-color: var(--border-color) !important;
  background: var(--border-color) !important;
  border: none !important;
  opacity: 0.6 !important;
  z-index: 999 !important;
}

/* 确保伪元素不被其他样式覆盖*/
.account-page .transactions-card .account-transactions-table-header-cell:not(:last-child):after {
  background-color: var(--border-color) !important;
  background: var(--border-color) !important;
}

/* 表格内容样式 */
.account-page .transactions-card .account-transactions-table-body {
  background: var(--bg-primary) !important;
}

.account-page .transactions-card .account-transactions-table-body-row {
  background: var(--bg-primary) !important;
}

.account-page .transactions-card .account-transactions-table-body-row:hover {
  background: var(--bg-hover) !important;
}

.account-page .transactions-card .account-transactions-table-body-cell {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-color) !important;
  border-right: none !important;
  padding: 16px !important;
}

.account-page .transactions-card .account-transactions-table-body-cell:last-child {
  border-right: none !important;
}

/* 表格分页样式 */
.account-page .transactions-card .account-transactions-table .account-pagination {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-prev .ant-pagination-item-link,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-next .ant-pagination-item-link,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-ellipsis {
  min-width: 28px !important;
  height: 28px !important;
  line-height: 28px !important;
  font-size: var(--font-size-xs) !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item {
  background: var(--bg-primary) !important;
  border-color: var(--border-color) !important;
}

.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item a {
  color: var(--text-primary) !important;
}

.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-active {
  border-color: var(--brand-primary) !important;
}

.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-active a {
  color: var(--brand-primary) !important;
}

/* 分页按钮图标大小调整 */
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-prev .ant-pagination-item-link .anticon,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-next .ant-pagination-item-link .anticon {
  font-size: var(--font-size-md) !important;
  width: var(--font-size-md) !important;
  height: var(--font-size-md) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 分页器快速跳转输入框样式调整 */
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-quick-jumper input {
  width: 40px !important;
  height: 28px !important;
  font-size: var(--font-size-xs) !important;
  padding: 0 var(--spacing-xs) !important;
  text-align: center !important;
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-color) !important;
}

/* 分页器快速跳转输入框聚焦和hover状态 */
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-quick-jumper input:hover,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-quick-jumper input:focus {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-color: var(--brand-primary) !important;
  box-shadow: 0 0 0 2px rgba(var(--brand-primary-rgb), 0.2) !important;
}

/* 分页器每页条数选择器样式调整*/
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-size-changer .ant-select-selector {
  height: 28px !important;
  line-height: 28px !important;
  font-size: var(--font-size-xs) !important;
  padding: 0 var(--spacing-xs) !important;
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-color) !important;
  min-width: 88px !important;
}

/* PC端分页器垂直对齐修复 */
@media (min-width: 769px) {
  .account-page .transactions-card .account-transactions-table .account-pagination {
    align-items: center !important;
  }
  
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
  }
  
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-quick-jumper,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-size-changer {
    display: flex !important;
    align-items: center !important;
  }
  
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-total-text {
    display: flex !important;
    align-items: center !important;
  }
}

/* 移动端隐藏分页器额外功能 */
@media (max-width: 768px) {
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options,
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-quick-jumper,
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-size-changer {
    display: none !important;
  }
  
  /* 移动端只显示基本分页信息 */
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-total-text {
    display: none !important;
  }
  
  /* 移动端分页器样式重置 */
  .account-page .transactions-card .account-transactions-table .account-pagination {
    text-align: center !important;
    align-items: center !important;
  }
  
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item,
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-prev,
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-next,
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-ellipsis {
    margin: 0 4px !important;
  }
  
  /* 移动端分页器元素垂直居中修复 - 重置line-height以覆盖基础样式的28px */
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-prev .ant-pagination-item-link,
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-next .ant-pagination-item-link,
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-ellipsis {
    line-height: 1 !important;
  }
}

/* 分页器上一页下一页按钮样式 - 直接针对图标 */
html body .account-page .transactions-card .account-transactions-table .ant-pagination-prev .ant-pagination-item-link .anticon,
html body .account-page .transactions-card .account-transactions-table .ant-pagination-next .ant-pagination-item-link .anticon {
  color: var(--text-secondary) !important;
}

html body .account-page .transactions-card .account-transactions-table .ant-pagination-prev:hover .ant-pagination-item-link .anticon,
html body .account-page .transactions-card .account-transactions-table .ant-pagination-next:hover .ant-pagination-item-link .anticon {
  color: var(--text-primary) !important;
}

html body .account-page .transactions-card .account-transactions-table .ant-pagination-prev.ant-pagination-disabled .ant-pagination-item-link .anticon,
html body .account-page .transactions-card .account-transactions-table .ant-pagination-next.ant-pagination-disabled .ant-pagination-item-link .anticon {
  color: var(--text-tertiary) !important;
}

/* 分页省略号样式*/
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-quick-jumper,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-size-changer {
  color: var(--text-primary) !important;
}

.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-ellipsis {
  color: var(--text-secondary) !important;
}

.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-ellipsis:hover {
  color: var(--text-primary) !important;
}

/* 分页省略号悬停时显示的箭头符号样式*/
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-ellipsis:hover .anticon {
  color: var(--text-primary) !important;
}

.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-ellipsis .anticon {
  color: var(--text-secondary) !important;
}

/* 表格空状态样式*/
.account-page .transactions-card .account-transactions-table .ant-empty {
  background: var(--bg-primary) !important;
  color: var(--text-secondary) !important;
}

.account-page .transactions-card .account-transactions-table .ant-empty-image {
  opacity: 0.8;
}

.account-page .transactions-card .account-transactions-table .ant-empty-description {
  color: var(--text-secondary) !important;
}

/* 表格加载状态样式*/
.account-page .transactions-card .account-transactions-table .ant-spin {
  background: var(--bg-primary) !important;
}

.account-page .transactions-card .account-transactions-table .ant-spin-dot-item {
  background-color: var(--brand-primary) !important;
}

.account-page .transactions-card .account-transactions-table .ant-spin-text {
  color: var(--text-secondary) !important;
}

/* 表格标签样式 */
.account-page .transactions-card .ant-tag {
  color: var(--text-primary) !important;
}

.account-page .transactions-card .ant-tag-success {
  background: var(--success-bg) !important;
  border-color: var(--success-color) !important;
  color: var(--success-color) !important;
}

.account-page .transactions-card .ant-tag-processing {
  background: var(--processing-bg) !important;
  border-color: var(--processing-color) !important;
  color: var(--processing-color) !important;
}

.account-page .transactions-card .ant-tag-warning {
  background: var(--warning-bg) !important;
  border-color: var(--warning-color) !important;
  color: var(--warning-color) !important;
}

.account-page .transactions-card .ant-tag-error {
  background: var(--bg-primary) !important;
  border-color: var(--brand-primary) !important;
  color: var(--brand-primary) !important;
}

.account-page .transactions-card .ant-tag-default {
  background: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-secondary) !important;
}

/* 卡片样式 */
.account-page .ant-card {
  margin: 0 0 12px 0 !important;
  box-shadow: none !important;
  border-radius: var(--radius-md) !important;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
}

.account-page .ant-card-head {
  border-bottom: 1px solid var(--border-color);
}

.account-page .ant-card-head-title {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 500;
}

/* 选择器和日期选择器样式*/
.account-page .ant-select .ant-select-selector {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.account-page .ant-picker {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.account-page .ant-select .ant-select-selection-item {
  color: var(--text-primary);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .account-page {
    padding: 12px;
  }
  
  .account-page .ant-card-extra {
    margin-top: 12px;
    width: 100%;
  }
  
  .account-page .ant-space {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .account-page .ant-space-item {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .account-page .ant-picker {
    width: 100%;
  }
  
  .account-page .ant-select {
    width: 100% !important;
  }

  /* 移动端统计卡片布局改为两行两列 */
  .stats-card .ant-row {
    display: flex !important;
    flex-wrap: wrap !important;
    margin: 0 -8px !important;
  }

  .stats-card .ant-col {
    flex: 0 0 50% !important;
    max-width: 50% !important;
    padding: 0 8px !important;
    margin-bottom: 16px !important;
    width: 50% !important;
  }

  .stats-card .ant-col:nth-child(1) {
    order: 3 !important;
  }

  .stats-card .ant-col:nth-child(2) {
    order: 1 !important;
  }

  .stats-card .ant-col:nth-child(3) {
    order: 2 !important;
  }

  .stats-card .ant-col:nth-child(4) {
    order: 4 !important;
  }

  /* 确保统计卡片内容在移动端正确显示 */
  .stats-card .ant-statistic {
    width: 100% !important;
    text-align: center !important;
  }

  .stats-card .ant-statistic-content {
    justify-content: center !important;
  }

  /* 移动端统计卡片标题字号微调，只比原始大1px */
  .account-page .stats-card .ant-statistic-title,
  .account-page .subscription-statistic-container .ant-statistic-title {
    font-size: 13px !important;
  }

  .account-page .stats-card .ant-statistic-content {
    font-size: 18px !important;
  }

  /* 移动端第一个区域数字字号进一步优化 */
  .account-page .stats-card .ant-statistic-content {
    font-size: 16px !important;
  }

  .account-page .ant-statistic-content-value {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
  }

  .account-page .ant-statistic-content-suffix {
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
  }

  /* 余额区域数值换行优化 */
  .balance-statistic-container .ant-statistic-content {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
  }

  .balance-statistic-container .ant-statistic-content-value {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
    line-height: 1.2 !important;
  }

  /* 订阅计划区域数值换行优化 */
  .subscription-statistic-container .ant-statistic-content {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
  }

  .subscription-statistic-container .ant-statistic-content-value {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
    line-height: 1.2 !important;
  }

  /* 移动端表格字号优化 */
  .account-page .transactions-card .account-transactions-table-header-cell {
    font-size: 12px !important;
    padding: 12px 8px !important;
  }

  .account-page .transactions-card .account-transactions-table-body-cell {
    font-size: 12px !important;
    padding: 12px 8px !important;
  }

  /* 移动端分页字号优化 */
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item,
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-prev .ant-pagination-item-link,
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-next .ant-pagination-item-link {
    font-size: 11px !important;
    min-width: 24px !important;
    height: 24px !important;
    line-height: 24px !important;
  }

  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-quick-jumper,
  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-size-changer {
    font-size: 11px !important;
    height: 24px !important;
    line-height: 24px !important;
  }

  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-quick-jumper input {
    font-size: 11px !important;
    width: 36px !important;
    height: 24px !important;
  }

  .account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-options-size-changer .ant-select-selector {
    font-size: 11px !important;
    height: 24px !important;
    line-height: 24px !important;
  }

  /* 移动端标签字号优化 */
  .account-page .transactions-card .ant-tag {
    font-size: 11px !important;
    padding: 2px 6px !important;
  }

  /* 移动端卡片标题字号优化 */
  .account-page .ant-card-head-title {
    font-size: 14px !important;
  }

  /* 移动端选择器和日期选择器字号优化 */
  .account-page .ant-select .ant-select-selection-item {
    font-size: 12px !important;
  }

  .account-page .transactions-card .ant-select-selection-placeholder {
    font-size: 12px !important;
  }

  .account-page .transactions-card .ant-picker-input > input {
    font-size: 12px !important;
  }

  /* 移动端刷新按钮字号优化 */
  .account-page .account-refresh-btn.ant-btn {
    font-size: 11px !important;
    padding: 4px 8px !important;
  }

  /* 移动端余额和订阅计划容器调整 */
  .balance-statistic-container,
  .subscription-statistic-container {
    width: 100% !important;
    align-items: center !important;
  }

  .balance-statistic-container .ant-statistic,
  .subscription-statistic-container .ant-statistic {
    width: 100% !important;
    text-align: center !important;
  }

  /* 移动端充值按钮尺寸进一步缩小 */
  .recharge-btn,
  .recharge-btn.ant-btn,
  .recharge-btn.ant-btn-primary {
    height: 16px !important;
    min-height: 16px !important;
    padding: 0 6px !important;
    border-radius: 3px !important;
    font-size: 11px !important;
    margin-left: 4px !important;
    line-height: 16px !important;
  }

  /* 移动端统计卡片标题字号强制提升，防止被覆盖 */
  .account-page .stats-card .ant-statistic-title,
  .account-page .subscription-statistic-container .ant-statistic-title {
    font-size: 16px !important;
  }

  /* 移动端筛选区域第二行布局优化：交易类型和筛选按钮在同一行 */
  .account-page .transactions-card .ant-space {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .account-page .transactions-card .ant-space-item:nth-child(2) {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    width: 100%;
    margin-bottom: 8px;
  }
  
  .account-page .transactions-card .ant-space-item:nth-child(2) .ant-select {
    flex: 1;
    width: auto !important;
    min-width: 100px;
  }
  
  .account-page .transactions-card .ant-space-item:nth-child(2) .account-refresh-btn {
    flex-shrink: 0;
    width: auto !important;
    min-width: 60px;
  }
}

@media (max-width: 480px) {
  .account-page {
    padding: 8px !important;
  }

  .account-page .ant-card {
    margin-bottom: 8px !important;
  }

  .account-page .ant-card-head {
    padding: 0 12px !important;
    min-height: 40px !important;
  }

  .account-page .ant-card-body {
    padding: 12px !important;
  }

  .account-page .ant-card-extra {
    margin-top: 8px !important;
  }

  .account-page .ant-space-item {
    margin-bottom: 6px !important;
  }

  /* 480px以下统计卡片布局优化 */
  .stats-card .ant-row {
    display: flex !important;
    flex-wrap: wrap !important;
    margin: 0 -6px !important;
  }

  .stats-card .ant-col {
    flex: 0 0 50% !important;
    max-width: 50% !important;
    padding: 0 6px !important;
    margin-bottom: 12px !important;
    width: 50% !important;
  }

  /* 480px以下统计卡片顺序调整 */
  .stats-card .ant-col:nth-child(1) {
    order: 3 !important;
  }

  .stats-card .ant-col:nth-child(2) {
    order: 1 !important;
  }

  .stats-card .ant-col:nth-child(3) {
    order: 2 !important;
  }

  .stats-card .ant-col:nth-child(4) {
    order: 4 !important;
  }

  /* 480px以下统计卡片内容优化 */
  .stats-card .ant-statistic {
    width: 100% !important;
    text-align: center !important;
  }

  .stats-card .ant-statistic-content {
    justify-content: center !important;
  }

  .account-page .ant-statistic-title {
    font-size: 13px !important;
  }

  .account-page .ant-statistic-content {
    font-size: 16px !important;
  }

  .account-page .stats-card .ant-statistic-content {
    font-size: 14px !important;
  }

  .account-page .stats-card .ant-statistic-title {
    font-size: 12px !important;
  }

  /* 480px以下订阅计划版本名称进一步优化 */
  .account-page .subscription-statistic-container .ant-statistic-content {
    font-size: 12px !important;
  }

  .account-page .subscription-statistic-container .ant-statistic-title {
    font-size: 12px !important;
  }

  /* 480px以下数值宽度限制和换行显示 */
  .account-page .ant-statistic-content-value {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
  }

  .account-page .ant-statistic-content-suffix {
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
  }

  /* 480px以下余额区域数值换行优化 */
  .balance-statistic-container .ant-statistic-content {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
  }

  .balance-statistic-container .ant-statistic-content-value {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
    line-height: 1.1 !important;
  }

  /* 480px以下隐藏钱包图标和皇冠图标 */
  .account-page .ant-statistic .md-outline-account-balance-wallet-icon,
  .account-page .account-balance-wallet-icon,
  .account-page .subscription-crown-icon {
    display: none !important;
  }

  /* 480px以下充值按钮进一步缩小 */
  .recharge-btn,
  .recharge-btn.ant-btn,
  .recharge-btn.ant-btn-primary {
    height: 20px !important;
    padding: 0 10px !important;
    font-size: 10px !important;
    margin-left: 6px !important;
  }

  /* 480px以下订阅状态和到期时间文字缩小 */
  .subscription-status-text {
    font-size: 10px !important;
  }

  .subscription-expiry {
    font-size: 10px !important;
    margin-top: 8px !important; /* 增加上边距，使订阅计划内容与左侧算力余额数值对齐 */
    gap: 1px !important;
  }

  .subscription-expiry-row {
    font-size: 10px !important;
    gap: 8px !important;
    margin-bottom: 1px !important;
  }

  .subscription-expiry-date {
    font-size: 10px !important;
  }

  .subscription-status-row {
    font-size: 10px !important;
  }

  /* 480px以下筛选按钮缩小 */
  .account-page .transactions-card .ant-picker {
    font-size: 10px !important;
  }

  .account-page .transactions-card .ant-picker-input > input {
    font-size: 10px !important;
  }

  .account-page .transactions-card .ant-select-selection-placeholder {
    font-size: 10px !important;
  }

  .account-page .transactions-card .ant-select-selection-item {
    font-size: 10px !important;
  }

  .account-page .account-refresh-btn.ant-btn {
    font-size: 10px !important;
    padding: 3px 6px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell {
    font-size: 11px !important;
    padding: 8px 4px !important;
  }

  .account-page .transactions-card .account-transactions-table-body-cell {
    font-size: 11px !important;
    padding: 8px 4px !important;
  }

  .account-page .ant-card-head-title {
    font-size: 13px !important;
  }

  /* 480px以下统计卡片间距优化 */
  .stats-card {
    margin-bottom: 8px !important;
  }

  .transactions-card {
    margin-bottom: 0 !important;
  }

  /* 480px以下余额容器间距优化 */
  .balance-statistic-container {
    gap: 2px !important;
  }

  .balance-bottom-row {
    gap: 6px !important;
  }

  /* 480px以下订阅容器间距优化 */
  .subscription-statistic-container {
    gap: 2px !important;
  }

  /* 480px以下表格横向滚动支持 */
  .account-page .transactions-card .account-transactions-table {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .account-page .transactions-card .account-transactions-table-inner {
    min-width: 490px !important;
    overflow-x: auto !important;
  }

  .account-page .transactions-card .account-transactions-table-header,
  .account-page .transactions-card .account-transactions-table-body {
    min-width: 490px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-row,
  .account-page .transactions-card .account-transactions-table-body-row {
    min-width: 490px !important;
    display: table !important;
    width: 100% !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell,
  .account-page .transactions-card .account-transactions-table-body-cell {
    white-space: normal !important; /* 允许换行 */
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
    vertical-align: top !important; /* 内容顶部对齐 */
    line-height: 1.3 !important; /* 适应多行文本 */
  }

  /* 时间列特殊换行处理：优先在空格处换行，保持日期时间的完整性 */
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(1) {
    word-break: normal !important; /* 移除强制断词，允许在空格处自然换行 */
  }

  /* 设置各列的固定宽度，确保不会相互挤压 */
  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(1),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(1) {
    width: 45px !important; /* 时间列固定宽度 - 20% */
    min-width: 45px !important;
    max-width: 45px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(2),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(2) {
    width: 32px !important; /* 类型列固定宽度 - 15% */
    min-width: 32px !important;
    max-width: 32px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(3),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(3) {
    width: 45px !important; /* 任务ID列固定宽度 - 20% */
    min-width: 45px !important;
    max-width: 45px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(4),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(4) {
    width: 36px !important; /* 算力值 C列固定宽度 - 15% */
    min-width: 36px !important;
    max-width: 36px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(5),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(5) {
    width: 45px !important; /* 余额 C列固定宽度 - 12% */
    min-width: 45px !important;
    max-width: 45px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(6),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(6) {
    width: 75px !important; /* 说明列固定宽度 - 33%，最重要信息 */
    min-width: 75px !important;
    max-width: 75px !important;
  }

  /* 移动端统计卡片标题字号强制提升，防止被覆盖 */
  .account-page .stats-card .ant-statistic-title,
  .account-page .subscription-statistic-container .ant-statistic-title {
    font-size: 16px !important;
  }

  /* 480px以下筛选区域第二行布局优化：交易类型和筛选按钮在同一行 */
  .account-page .transactions-card .ant-space-item:nth-child(2) {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 6px;
    width: 100%;
    margin-bottom: 6px;
  }
  
  .account-page .transactions-card .ant-space-item:nth-child(2) .ant-select {
    flex: 1;
    width: auto !important;
    min-width: 80px;
  }
  
  .account-page .transactions-card .ant-space-item:nth-child(2) .account-refresh-btn {
    flex-shrink: 0;
    width: auto !important;
    min-width: 50px;
    font-size: 10px !important;
    padding: 3px 6px !important;
  }
}

@media (max-width: 360px) {
  .account-page {
    padding: 6px !important;
  }

  .account-page .ant-card {
    margin-bottom: 6px !important;
  }

  .account-page .ant-card-head {
    padding: 0 8px !important;
    min-height: 36px !important;
  }

  .account-page .ant-card-body {
    padding: 8px !important;
  }

  .account-page .ant-card-extra {
    margin-top: 6px !important;
  }

  .account-page .ant-space-item {
    margin-bottom: 4px !important;
  }

  /* 360px以下统计卡片布局优化 */
  .stats-card .ant-row {
    display: flex !important;
    flex-wrap: wrap !important;
    margin: 0 -4px !important;
  }

  .stats-card .ant-col {
    flex: 0 0 50% !important;
    max-width: 50% !important;
    padding: 0 4px !important;
    margin-bottom: 8px !important;
    width: 50% !important;
  }

  /* 360px以下统计卡片顺序调整 */
  .stats-card .ant-col:nth-child(1) {
    order: 3 !important;
  }

  .stats-card .ant-col:nth-child(2) {
    order: 1 !important;
  }

  .stats-card .ant-col:nth-child(3) {
    order: 2 !important;
  }

  .stats-card .ant-col:nth-child(4) {
    order: 4 !important;
  }

  /* 360px以下统计卡片内容优化 */
  .stats-card .ant-statistic {
    width: 100% !important;
    text-align: center !important;
  }

  .stats-card .ant-statistic-content {
    justify-content: center !important;
  }

  .account-page .ant-statistic-title {
    font-size: 11px !important;
  }

  .account-page .ant-statistic-content {
    font-size: 14px !important;
  }

  .account-page .stats-card .ant-statistic-content {
    font-size: 12px !important;
  }

  .account-page .stats-card .ant-statistic-title {
    font-size: 11px !important;
  }

  .account-page .subscription-statistic-container .ant-statistic-content {
    font-size: 10px !important;
  }

  .account-page .subscription-statistic-container .ant-statistic-title {
    font-size: 11px !important;
  }

  /* 360px以下数值宽度限制和换行显示 */
  .account-page .ant-statistic-content-value {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
  }

  .account-page .ant-statistic-content-suffix {
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
  }

  /* 360px以下余额区域数值换行优化 */
  .balance-statistic-container .ant-statistic-content {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
  }

  .balance-statistic-container .ant-statistic-content-value {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
    line-height: 1.0 !important;
  }

  /* 360px以下订阅计划区域数值换行优化 */
  .subscription-statistic-container .ant-statistic-content {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
  }

  .subscription-statistic-container .ant-statistic-content-value {
    max-width: 100% !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
    line-height: 1.0 !important;
  }

  /* 360px以下隐藏钱包图标和皇冠图标 */
  .account-page .ant-statistic .md-outline-account-balance-wallet-icon,
  .account-page .account-balance-wallet-icon,
  .account-page .subscription-crown-icon {
    display: none !important;
  }

  /* 360px以下充值按钮进一步缩小 */
  .recharge-btn,
  .recharge-btn.ant-btn,
  .recharge-btn.ant-btn-primary {
    height: 18px !important;
    padding: 0 8px !important;
    font-size: 9px !important;
    margin-left: 4px !important;
  }

  /* 360px以下订阅状态和到期时间文字缩小 */
  .subscription-status-text {
    font-size: 9px !important;
  }

  .subscription-expiry {
    font-size: 9px !important;
    margin-top: 8px !important; /* 增加上边距，使订阅计划内容与左侧算力余额数值对齐 */
    gap: 0 !important;
  }

  .subscription-expiry-row {
    font-size: 9px !important;
    gap: 6px !important;
    margin-bottom: 0 !important;
  }

  .subscription-expiry-date {
    font-size: 9px !important;
  }

  .subscription-status-row {
    font-size: 9px !important;
  }

  /* 360px以下筛选按钮最终缩小 */
  .account-page .transactions-card .ant-picker {
    font-size: 9px !important;
  }

  .account-page .transactions-card .ant-picker-input > input {
    font-size: 9px !important;
  }

  .account-page .transactions-card .ant-select-selection-placeholder {
    font-size: 9px !important;
  }

  .account-page .transactions-card .ant-select-selection-item {
    font-size: 9px !important;
  }

  .account-page .account-refresh-btn.ant-btn {
    font-size: 9px !important;
    padding: 2px 4px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell {
    font-size: 10px !important;
    padding: 6px 3px !important;
  }

  .account-page .transactions-card .account-transactions-table-body-cell {
    font-size: 10px !important;
    padding: 6px 3px !important;
  }

  .account-page .ant-card-head-title {
    font-size: 12px !important;
  }

  /* 360px以下统计卡片间距最终优化 */
  .stats-card {
    margin-bottom: 6px !important;
  }

  .transactions-card {
    margin-bottom: 0 !important;
  }

  /* 360px以下余额容器间距最终优化 */
  .balance-statistic-container {
    gap: 1px !important;
  }

  .balance-bottom-row {
    gap: 4px !important;
  }

  /* 360px以下订阅容器间距最终优化 */
  .subscription-statistic-container {
    gap: 1px !important;
  }

  /* 360px以下表格横向滚动支持 */
  .account-page .transactions-card .account-transactions-table {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .account-page .transactions-card .account-transactions-table-inner {
    min-width: 400px !important; /* 调整为各列宽度总和 80+60+80+70+110=400 */
    overflow-x: auto !important;
  }

  .account-page .transactions-card .account-transactions-table-header,
  .account-page .transactions-card .account-transactions-table-body {
    min-width: 400px !important; /* 调整为各列宽度总和 */
  }

  .account-page .transactions-card .account-transactions-table-header-row,
  .account-page .transactions-card .account-transactions-table-body-row {
    min-width: 400px !important; /* 调整为各列宽度总和 */
    display: table !important;
    width: 100% !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell,
  .account-page .transactions-card .account-transactions-table-body-cell {
    white-space: normal !important; /* 允许换行 */
    word-wrap: break-word !important;
    word-break: break-all !important;
    overflow-wrap: break-word !important;
    vertical-align: top !important; /* 内容顶部对齐 */
    line-height: 1.2 !important; /* 适应多行文本 */
  }

  /* 时间列特殊换行处理：优先在空格处换行，保持日期时间的完整性 */
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(1) {
    word-break: normal !important; /* 移除强制断词，允许在空格处自然换行 */
  }

  /* 设置各列的固定宽度，确保不会相互挤压 */
  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(1),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(1) {
    width: 68px !important; /* 时间列固定宽度 - 20% */
    min-width: 68px !important;
    max-width: 68px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(2),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(2) {
    width: 51px !important; /* 类型列固定宽度 - 15% */
    min-width: 51px !important;
    max-width: 51px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(3),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(3) {
    width: 68px !important; /* 任务ID列固定宽度 - 20% */
    min-width: 68px !important;
    max-width: 68px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(4),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(4) {
    width: 41px !important; /* 算力值 C列固定宽度 - 15% */
    min-width: 41px !important;
    max-width: 41px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(5),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(5) {
    width: 51px !important; /* 余额 C列固定宽度 - 12% */
    min-width: 51px !important;
    max-width: 51px !important;
  }

  .account-page .transactions-card .account-transactions-table-header-cell:nth-child(6),
  .account-page .transactions-card .account-transactions-table-body-cell:nth-child(6) {
    width: 112px !important; /* 说明列固定宽度 - 33%，最重要的信息列 */
    min-width: 112px !important;
    max-width: 112px !important;
  }

  /* 移动端统计卡片标题字号强制提升，防止被覆盖 */
  .account-page .stats-card .ant-statistic-title,
  .account-page .subscription-statistic-container .ant-statistic-title {
    font-size: 15px !important;
  }

  /* 360px以下筛选区域第二行布局优化：交易类型和筛选按钮在同一行 */
  .account-page .transactions-card .ant-space-item:nth-child(2) {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
    width: 100%;
    margin-bottom: 4px;
  }
  
  .account-page .transactions-card .ant-space-item:nth-child(2) .ant-select {
    flex: 1;
    width: auto !important;
    min-width: 70px;
  }
  
  .account-page .transactions-card .ant-space-item:nth-child(2) .account-refresh-btn {
    flex-shrink: 0;
    width: auto !important;
    min-width: 45px;
    font-size: 9px !important;
    padding: 2px 4px !important;
  }
}

/* 表格筛选区域样式*/
.account-page .transactions-card .ant-card-extra {
  color: var(--text-primary);
}

/* 日期选择器样式*/
.account-page .transactions-card .ant-picker {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
}

.account-page .transactions-card .ant-picker-input > input {
  color: var(--text-primary) !important;
}

.account-page .transactions-card .ant-picker-suffix {
  color: var(--text-secondary) !important;
}

.account-page .transactions-card .ant-picker-clear {
  background: var(--bg-primary) !important;
  color: var(--text-secondary) !important;
}

.account-page .transactions-card .ant-picker-dropdown {
  background: var(--bg-primary) !important;
}

.account-page .transactions-card .ant-picker-dropdown .ant-picker-panel {
  background: var(--bg-primary) !important;
}

.account-page .transactions-card .ant-picker-dropdown .ant-picker-date-panel .ant-picker-header {
  color: var(--text-primary) !important;
}

.account-page .transactions-card .ant-picker-dropdown .ant-picker-date-panel .ant-picker-content th {
  color: var(--text-secondary) !important;
}

.account-page .transactions-card .ant-picker-dropdown .ant-picker-cell {
  color: var(--text-primary) !important;
}

.account-page .transactions-card .ant-picker-dropdown .ant-picker-cell-in-view {
  color: var(--text-primary) !important;
}

.account-page .transactions-card .ant-picker-dropdown .ant-picker-cell:not(.ant-picker-cell-in-view) {
  color: var(--text-tertiary) !important;
}

.account-page .transactions-card .ant-picker-dropdown .ant-picker-cell:hover .ant-picker-cell-inner {
  background: var(--bg-hover) !important;
}

/* 选择器样式*/
.account-page .transactions-card .ant-select {
  background: var(--bg-primary) !important;
}

.account-page .transactions-card .ant-select-selector {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
}

.account-page .transactions-card .ant-select-selection-placeholder {
  color: var(--text-secondary) !important;
}

.account-page .transactions-card .ant-select-selection-item {
  color: var(--text-primary) !important;
}

.account-page .transactions-card .ant-select-arrow {
  color: var(--text-secondary) !important;
}

.account-page .transactions-card .ant-select-clear {
  background: var(--bg-primary) !important;
  color: var(--text-secondary) !important;
}

.account-page .transactions-card .ant-select-dropdown {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
}

.account-page .transactions-card .ant-select-item {
  color: var(--text-primary) !important;
}

.account-page .transactions-card .ant-select-item-option-selected {
  background: var(--bg-hover) !important;
  color: var(--text-primary) !important;
}

.account-page .transactions-card .ant-select-item-option-active {
  background: var(--bg-hover) !important;
}

/* 表格悬浮效果 */
.account-page .transactions-card .ant-table-tbody > tr:hover > td {
  background: var(--bg-hover) !important;
}

.account-page .transactions-card .ant-table-tbody > tr.ant-table-row-hover > td {
  background: var(--bg-hover) !important;
}

/* 日期选择器占位符文字 */
.account-page .transactions-card .ant-picker-input > input::placeholder {
  color: var(--text-secondary) !important;
}

/* 专用交易类型下拉菜单样式 - 使用特定的dropdownClassName避免全局影响 */
.account-type-dropdown.ant-select-dropdown {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
}

.account-type-dropdown.ant-select-dropdown .ant-select-item {
  background: var(--bg-primary) !important;
}

.account-type-dropdown.ant-select-dropdown .ant-select-item-option {
  background: var(--bg-primary) !important;
}

.account-type-dropdown.ant-select-dropdown .ant-select-item-option-content {
  color: var(--text-primary) !important;
}

.account-type-dropdown.ant-select-dropdown .ant-select-item-option-selected {
  background: var(--bg-hover) !important;
}

.account-type-dropdown.ant-select-dropdown .ant-select-item-option-selected .ant-select-item-option-content {
  color: var(--text-primary) !important;
}

.account-type-dropdown.ant-select-dropdown .ant-select-item-option-active {
  background: var(--bg-hover) !important;
}

/* 专用刷新按钮样式 - 使用简单朴素的配色风格 */
.account-page .account-refresh-btn.ant-btn {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
  padding: 6px 12px !important;
  border-radius: var(--radius-sm) !important;
  cursor: pointer !important;
  transition: var(--transition-normal) !important;
  white-space: nowrap !important;
  font-size: var(--font-size-sm) !important;
  font-weight: 500 !important;
  box-shadow: none !important;
  outline: none !important;
}

.account-page .account-refresh-btn.ant-btn:hover,
.account-page .account-refresh-btn.ant-btn:focus {
  background: var(--bg-hover) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
  box-shadow: none !important;
  outline: none !important;
}

.account-page .account-refresh-btn.ant-btn:active {
  background: var(--bg-hover) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 专用刷新按钮文字样式 */
.account-page .account-refresh-btn.ant-btn,
.account-page .account-refresh-btn.ant-btn span,
.account-page .account-refresh-btn.ant-btn .anticon {
  font-size: var(--font-size-sm) !important;
  font-weight: 500 !important;
  color: var(--text-primary) !important;
}

.account-page .account-refresh-btn.ant-btn:hover span,
.account-page .account-refresh-btn.ant-btn:hover .anticon,
.account-page .account-refresh-btn.ant-btn:focus span,
.account-page .account-refresh-btn.ant-btn:focus .anticon {
  color: var(--text-primary) !important;
}

/* 统一筛选区其他元素字号 */
/* 统一筛选区其他元素字号 */
.account-page .transactions-card .ant-picker,
.account-page .transactions-card .ant-picker-input > input,
.account-page .transactions-card .ant-picker-input > input::placeholder,
.account-page .transactions-card .ant-select,
.account-page .transactions-card .ant-select-selector,
.account-page .transactions-card .ant-select-selection-item,
.account-page .transactions-card .ant-select-selection-placeholder {
  font-size: var(--font-size-sm) !important;
}

.account-page .ant-statistic .md-outline-account-balance-wallet-icon {
  vertical-align: middle;
  font-size: 18px;
  margin-right: 2px;
}

.account-page .account-balance-wallet-icon {
  vertical-align: middle;
  font-size: 18px;
  margin-right: 2px;
  display: inline-block;
}

/* 日期选择器相关样式 - 合并版本 */
.account-date-dropdown .ant-picker-cell-in-view {
  color: var(--text-primary) !important;
}

.account-date-dropdown .ant-picker-cell:not(.ant-picker-cell-in-view) {
  color: var(--text-tertiary) !important;
}

.account-date-dropdown .ant-picker-header,
.account-date-dropdown .ant-picker-year-btn,
.account-date-dropdown .ant-picker-month-btn,
.account-date-dropdown .ant-picker-decade-btn {
  color: var(--text-primary) !important;
}

.account-date-dropdown .ant-picker-content th {
  color: var(--text-secondary) !important;
}

.account-date-dropdown .ant-picker-panel {
  background: var(--bg-primary) !important;
}

.account-date-dropdown .ant-picker-dropdown {
  background: var(--bg-primary) !important;
}

/* === 日期选择器主题色配置 - 最终合并版=== */
/* 使用最高优先级确保样式生效 */
html body .account-page .account-unique-range-picker.ant-picker.ant-picker-range {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* 日期选择器活动指示条 - 使用主题色*/
html body .account-page .account-unique-range-picker.ant-picker.ant-picker-range .ant-picker-active-bar {
  background: var(--brand-primary) !important;
  background-color: var(--brand-primary) !important;
}

/* 确保在不同状态下都使用主题色 */
html body .account-page .account-unique-range-picker.ant-picker-focused .ant-picker-active-bar,
html body .account-page .account-unique-range-picker.ant-picker-range .ant-picker-active-bar {
  background: var(--brand-primary) !important;
  background-color: var(--brand-primary) !important;
}

/* 日期选择器下拉面板主题色配置 - 使用CSS变量确保主题适配 */
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-start .ant-picker-cell-inner,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-end .ant-picker-cell-inner {
  background: var(--brand-primary-light) !important;
  color: var(--text-primary) !important;
  border-radius: var(--radius-sm) !important;
}

html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-start-single .ant-picker-cell-inner {
  background: var(--brand-primary-light) !important;
  color: var(--text-primary) !important;
  border-radius: var(--radius-sm) !important;
}

/* 为开始和结束日期添加细边 - 使用主题色*/
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-start .ant-picker-cell-inner::before,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-end .ant-picker-cell-inner::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  border: 1px solid var(--brand-primary) !important;
  border-radius: var(--radius-sm) !important;
  pointer-events: none !important;
}

html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-start-single .ant-picker-cell-inner::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  border: 1px solid var(--brand-primary) !important;
  border-radius: var(--radius-sm) !important;
  pointer-events: none !important;
}

html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-in-range .ant-picker-cell-inner {
  background: var(--brand-primary-light) !important;
  color: var(--text-primary) !important;
}

/* 今天日期 - 仅主题色文字，无边框 */
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-today .ant-picker-cell-inner {
  color: var(--brand-primary) !important;
}
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-today .ant-picker-cell-inner::before {
  border: none !important;
}

/* 彻底移除范围背景 - 使用最高优先级 */
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-in-range,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-start,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-end,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-hover {
  background: transparent !important;
  background-color: transparent !important;
}

/* 移除所有可能的伪元素背景*/
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-in-range::before,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-in-range::after,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-start::before,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-start::after,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-end::before,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-end::after {
  background: transparent !important;
  background-color: transparent !important;
  display: none !important;
}

/* 悬停效果 */
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell:not(.ant-picker-cell-in-view):hover .ant-picker-cell-inner,
html body .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-in-view:hover .ant-picker-cell-inner {
  background: var(--brand-primary-lighter) !important;
  color: var(--text-primary) !important;
}

/* 额外的高优先级选择器确保样式生成*/
html body #root .account-page .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-in-range,
html body #root .account-page .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-start,
html body #root .account-page .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-end,
html body #root .account-page .account-date-dropdown.ant-picker-dropdown .ant-picker-cell-range-hover {
  background: transparent !important;
  background-color: transparent !important;
}

/* 移除输入框的焦点边框效果 */
.account-page .transactions-card .ant-picker-focused,
.account-page .transactions-card .ant-select-focused .ant-select-selector,
.account-page .transactions-card .ant-pagination-options-size-changer .ant-select-focused .ant-select-selector {
  border-color: var(--border-color) !important;
}

/* 移除输入框和选择器的蓝色阴影效果 */
.account-page .transactions-card .ant-picker,
.account-page .transactions-card .ant-select-selector,
.account-page .transactions-card .ant-pagination-options-size-changer .ant-select-selector {
  box-shadow: none !important;
}

.account-page .transactions-card .ant-picker-focused,
.account-page .transactions-card .ant-select-focused .ant-select-selector,
.account-page .transactions-card .ant-pagination-options-size-changer .ant-select-selector {
  box-shadow: none !important;
}

/* 确保下拉框也没有阴影 */
.account-page .transactions-card .ant-picker-dropdown,
.account-page .transactions-card .ant-select-dropdown,
.account-page .transactions-card .ant-pagination-options-size-changer .ant-select-dropdown {
  box-shadow: none !important;
}

/* 分页省略号和前后翻页按钮大小调整 */
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-ellipsis,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-prev,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-next {
  min-width: 28px !important;
  height: 28px !important;
  line-height: 28px !important;
  font-size: var(--font-size-xs) !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 省略号按钮内部样式*/
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-item-ellipsis .anticon {
  font-size: var(--font-size-md) !important;
  width: var(--font-size-md) !important;
  height: var(--font-size-md) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 前后翻页按钮内部样式 */
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-prev .ant-pagination-item-link,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-next .ant-pagination-item-link {
  min-width: 28px !important;
  height: 28px !important;
  line-height: 28px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 前后翻页按钮图标样式 */
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-prev .ant-pagination-item-link .anticon,
.account-page .transactions-card .account-transactions-table .account-pagination .ant-pagination-next .ant-pagination-item-link .anticon {
  font-size: var(--font-size-md) !important;
  width: var(--font-size-md) !important;
  height: var(--font-size-md) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 使用更具体的类名修改金额颜色 */
html body .account-page .transactions-card .account-transactions-table .account-negative-amount,
html body .account-page .transactions-card .account-transactions-table .ant-table-cell .account-negative-amount {
  color: var(--brand-primary) !important;
}

/* 使用更具体的类名修改标签样式 */
html body .account-page .transactions-card .account-transactions-table .account-tag-error {
  background: var(--brand-primary-light) !important;
  border-color: var(--brand-primary) !important;
  color: var(--brand-primary) !important;
}

/* 使用更具体的类名修改金额单元格样式*/
html body .account-page .transactions-card .account-transactions-table .account-amount-cell,
html body .account-page .transactions-card .account-transactions-table .account-amount-cell.negative {
  color: var(--brand-primary) !important;
}

/* 使用更具体的类名修改累计消费金额样式 */
html body .account-page .transactions-card .account-transactions-table .account-total-amount {
  color: var(--brand-primary) !important;
}

/* 使用更具体的类名修改统计卡片中的金额样式 */
html body .account-page .stats-card .ant-statistic-content-value .account-negative-value {
  color: var(--brand-primary) !important;
}

/* 统一筛选区元素高度和样式*/
.account-page .transactions-card .ant-picker,
.account-page .transactions-card .ant-select,
.account-page .transactions-card .account-refresh-btn.ant-btn {
  height: 32px !important;
  line-height: 32px !important;
  font-size: var(--font-size-sm) !important;
}

/* 确保日期选择器输入框高度一致*/
.account-page .transactions-card .ant-picker-input {
  height: 32px !important;
  line-height: 32px !important;
}

/* 确保选择器内部元素高度一致*/
.account-page .transactions-card .ant-select-selector {
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 11px !important;
}

/* 确保刷新按钮内部元素对齐 */
.account-page .transactions-card .account-refresh-btn.ant-btn {
  padding: 0 12px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 充值按钮样式 */
.balance-statistic-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}

.balance-statistic-container .ant-statistic-content {
  width: 100% !important;
  max-width: 100% !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  overflow-wrap: break-word !important;
}

.balance-statistic-container .ant-statistic-content-value {
  word-wrap: break-word !important;
  word-break: break-all !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
  line-height: 1.1 !important;
}

.balance-title {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 2px;
  font-weight: 500;
}

.balance-bottom-row {
  display: flex;
  align-items: flex-end; /* 数字和按钮底部对齐 */
  width: 100%;
}

.balance-value {
  font-size: 24px;
  color: var(--text-primary);
  line-height: 1;
  display: flex;
  align-items: flex-end;
  word-wrap: break-word !important;
  word-break: break-all !important;
  overflow-wrap: break-word !important;
}

.recharge-btn,
.recharge-btn.ant-btn,
.recharge-btn.ant-btn-primary {
  background: var(--brand-gradient) !important;
  color: var(--text-inverse) !important;
  border: none !important;
  border-radius: var(--radius-sm) !important;
  height: 26px !important;
  min-height: 20px !important;
  line-height: 28px !important;
  padding: 0 18px !important;
  font-size: 13px;
  margin-left: 28px;
}

.recharge-btn:hover {
  filter: brightness(1.1);
  box-shadow: none;
  transform: none;
  border: none;
}

.recharge-btn.ant-btn-primary:hover,
.recharge-btn:hover {
  filter: brightness(1.1) !important;
  box-shadow: none !important;
  transform: none !important;
  border: none !important;
  background: var(--brand-gradient) !important;
  color: var(--text-inverse) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .balance-bottom-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  .recharge-btn {
    align-self: flex-start;
    margin-left: 0;
    margin-top: 8px;
  }
}

.balance-value-row .recharge-btn,
.balance-value-row .ant-btn {
  margin-left: 8px;
  height: 24px;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 4px;
  margin-left: 28px;
  position: relative;
  top: -3px; /* 向上微调3px，可根据实际效果调整 */
}

/* 移动端覆盖 balance-value-row 中的按钮样式 */
@media (max-width: 768px) {
  .balance-value-row .recharge-btn,
  .balance-value-row .ant-btn {
    height: 22px !important;
    padding: 0 10px !important;
    font-size: 11px !important;
    margin-left: 6px !important;
  }
}

@media (max-width: 480px) {
  .balance-value-row .recharge-btn,
  .balance-value-row .ant-btn {
    height: 22px !important;
    padding: 0 10px !important;
    font-size: 10px !important;
    margin-left: 6px !important;
  }
}

@media (max-width: 360px) {
  .balance-value-row .recharge-btn,
  .balance-value-row .ant-btn {
    height: 18px !important;
    padding: 0 6px !important;
    font-size: 10px !important;
    margin-left: 4px !important;
  }
}

/* 订阅计划相关样式 */
.subscription-statistic-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}

.subscription-statistic-container .ant-statistic-content {
  width: 100% !important;
  max-width: 100% !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  overflow-wrap: break-word !important;
}

.subscription-statistic-container .ant-statistic-content-value {
  word-wrap: break-word !important;
  word-break: break-all !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
  line-height: 1.1 !important;
}

.subscription-crown-icon {
  color: var(--warning-color);
  font-size: 18px;
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subscription-statistic-container {
    align-items: center;
  }
  
  .subscription-expiry {
    align-items: center;
    text-align: center;
    margin-top: 8px; /* 增加上边距，使订阅计划内容与左侧算力余额数值对齐 */
  }
}

/* 订阅状态极简圆点+文字样式 */
.subscription-status-row {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0.2px;
  margin-top: 0;
  margin-left: 0;
  padding-left: 0;
}
.subscription-status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
  margin-left: 6px;
  box-shadow: 0 0 2px rgba(0,0,0,0.08);
}
.subscription-status-dot.active {
  background: linear-gradient(135deg, #2ecc71 60%, #27ae60 100%);
}
.subscription-status-dot.expired {
  background: linear-gradient(135deg, #e74c3c 60%, #c0392b 100%);
}
.subscription-status-dot.pending {
  background: linear-gradient(135deg, #f39c12 60%, #e67e22 100%);
}
.subscription-status-dot.canceled {
  background: linear-gradient(135deg, #b2bec3 60%, #636e72 100%);
}
.subscription-status-dot.not-started {
  background: linear-gradient(135deg, #3498db 60%, #2980b9 100%);
}
.subscription-status-text {
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0.2px;
}

.subscription-expiry-row {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 2px;
  width: 100%;
}
.subscription-expiry-date {
  font-size: 12px;
  font-weight: 400;
  color: var(--text-secondary);
  letter-spacing: 0.2px;
}

.subscription-expiry {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 4px;
  font-size: 12px;
  color: var(--text-secondary);
  gap: 2px;
  width: 100%;
}

.subscription-expiry > div:first-child {
  margin-bottom: 2px;
}

.subscription-expiry .ant-tag {
  margin: 0;
  font-size: 10px;
  height: 16px;
  line-height: 14px;
  padding: 0 4px;
  border-radius: 2px;
}

/* 移动端统计卡片标题字号微调，只比原始大1px */
@media (max-width: 768px) {
  .account-page .stats-card .ant-statistic-title,
  .account-page .subscription-statistic-container .ant-statistic-title {
    font-size: 13px !important;
  }
}
@media (max-width: 480px) {
  .account-page .stats-card .ant-statistic-title,
  .account-page .subscription-statistic-container .ant-statistic-title {
    font-size: 12px !important;
  }
}
@media (max-width: 360px) {
  .account-page .stats-card .ant-statistic-title,
  .account-page .subscription-statistic-container .ant-statistic-title {
    font-size: 11px !important;
  }
}

/* 移动端筛选区域布局强制优化 - 在文件末尾确保最高优先级 */
@media (max-width: 768px) {
  /* 确保移动端筛选区域整体布局 */
  .account-page .transactions-card .ant-card-extra {
    width: 100% !important;
  }
  
  .account-page .transactions-card .ant-card-extra .ant-space {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
  }
  
  /* 第二行：交易类型和筛选按钮同行显示 */
  .account-page .transactions-card .ant-card-extra .ant-space > div:nth-child(2),
  .account-page .transactions-card .ant-card-extra .ant-space .ant-space-item:nth-child(2) {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 8px !important;
    width: 100% !important;
    margin-bottom: 8px !important;
  }
  
  /* 交易类型选择器占据剩余空间 */
  .account-page .transactions-card .ant-card-extra .ant-space > div:nth-child(2) .ant-select,
  .account-page .transactions-card .ant-card-extra .ant-space .ant-space-item:nth-child(2) .ant-select,
  .account-page .transactions-card .ant-card-extra .ant-select {
    flex: 1 !important;
    width: auto !important;
    min-width: 100px !important;
  }
  
  /* 筛选按钮固定宽度 */
  .account-page .transactions-card .ant-card-extra .ant-space > div:nth-child(2) .account-refresh-btn,
  .account-page .transactions-card .ant-card-extra .ant-space .ant-space-item:nth-child(2) .account-refresh-btn,
  .account-page .transactions-card .ant-card-extra .account-refresh-btn {
    flex-shrink: 0 !important;
    width: auto !important;
    min-width: 60px !important;
  }
}

@media (max-width: 480px) {
  /* 480px以下的优化 */
  .account-page .transactions-card .ant-card-extra .ant-space > div:nth-child(2),
  .account-page .transactions-card .ant-card-extra .ant-space .ant-space-item:nth-child(2) {
    gap: 6px !important;
    margin-bottom: 6px !important;
  }
  
  .account-page .transactions-card .ant-card-extra .ant-select {
    min-width: 80px !important;
  }
  
  .account-page .transactions-card .ant-card-extra .account-refresh-btn {
    min-width: 50px !important;
    font-size: 10px !important;
    padding: 3px 6px !important;
  }
}

@media (max-width: 360px) {
  /* 360px以下的优化 */
  .account-page .transactions-card .ant-card-extra .ant-space > div:nth-child(2),
  .account-page .transactions-card .ant-card-extra .ant-space .ant-space-item:nth-child(2) {
    gap: 4px !important;
    margin-bottom: 4px !important;
  }
  
  .account-page .transactions-card .ant-card-extra .ant-select {
    min-width: 70px !important;
  }
  
  .account-page .transactions-card .ant-card-extra .account-refresh-btn {
    min-width: 45px !important;
    font-size: 9px !important;
    padding: 2px 4px !important;
  }
}

/* 移动端筛选容器专用样式 */
.mobile-filter-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 8px;
  margin-bottom: 12px; /* 增加下边距，避免与分割线重合 */
}

.mobile-filter-row {
  display: flex;
  width: 100%;
}

.mobile-filter-inline {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 8px !important;
}

.mobile-type-select {
  flex: 0.7 !important; /* 缩小交易类型筛选框的占比 */
  min-width: 90px !important;
}

.mobile-type-select .ant-select-selector {
  height: 28px !important; /* 降低输入框高度 */
  padding: 0 8px !important;
}

.mobile-type-select .ant-select-selection-item {
  line-height: 26px !important; /* 调整文字垂直居中 */
}

.mobile-filter-btn {
  flex: 0.3 !important; /* 增大筛选按钮的占比 */
  flex-shrink: 0 !important;
  min-width: 70px !important; /* 增大按钮最小宽度 */
  height: 28px !important; /* 降低按钮高度 */
  padding: 0 8px !important;
  font-size: 12px !important;
  line-height: 1 !important;
}

@media (max-width: 480px) {
  .mobile-filter-container {
    gap: 6px;
    margin-bottom: 10px;
  }
  
  .mobile-filter-inline {
    gap: 6px !important;
  }
  
  .mobile-type-select {
    flex: 0.7 !important;
    min-width: 70px !important; /* 480px下进一步缩小 */
  }
  
  .mobile-type-select .ant-select-selector {
    height: 26px !important;
    padding: 0 6px !important;
  }
  
  .mobile-type-select .ant-select-selection-item {
    line-height: 24px !important;
  }
  
  .mobile-filter-btn {
    flex: 0.3 !important;
    min-width: 60px !important; /* 480px下增大按钮宽度 */
    height: 26px !important;
    font-size: 10px !important;
    padding: 0 6px !important;
  }
}

@media (max-width: 360px) {
  .mobile-filter-container {
    gap: 4px;
    margin-bottom: 8px;
  }
  
  .mobile-filter-inline {
    gap: 4px !important;
  }
  
  .mobile-type-select {
    flex: 0.7 !important;
    min-width: 60px !important; /* 360px下进一步缩小 */
  }
  
  .mobile-type-select .ant-select-selector {
    height: 24px !important;
    padding: 0 4px !important;
  }
  
  .mobile-type-select .ant-select-selection-item {
    line-height: 22px !important;
  }
  
  .mobile-filter-btn {
    flex: 0.3 !important;
    min-width: 55px !important; /* 360px下增大按钮宽度 */
    height: 24px !important;
    font-size: 9px !important;
    padding: 0 4px !important;
  }
}

/* 移动端交易类型下拉菜单选项字号调整 */
@media (max-width: 768px) {
  .account-type-dropdown .ant-select-item {
    font-size: 13px !important; /* 下拉选项字号调小一丢丢 */
  }
}

@media (max-width: 480px) {
  .account-type-dropdown .ant-select-item {
    font-size: 13px !important;
  }
}

@media (max-width: 360px) {
  .account-type-dropdown .ant-select-item {
    font-size: 12px !important;
  }
}

 
