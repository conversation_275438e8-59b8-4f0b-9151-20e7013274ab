# 缓存和错误处理机制重构计划

## 问题描述

当前项目中存在一个显著的技术债务：在不同页面组件中（如 `matting/index.jsx`、`upscale/index.jsx` 等）重复实现了相似的缓存和错误处理机制。这导致以下问题：

1. **代码重复**：每个页面都单独实现了 `taskRequestCache` 对象和相关的缓存检查、更新逻辑
2. **维护困难**：修改一处逻辑需要在所有页面组件中同步更新
3. **不一致性风险**：各页面的实现细节可能随时间推移而不同步，导致用户体验和错误处理的不一致
4. **错误处理不统一**：每个页面可能以稍微不同的方式处理错误情况（如 "未找到源图像组件" 等）

## 建议解决方案

将重复的缓存和错误处理逻辑抽取为共享的自定义 Hook 或工具函数：

### 1. 缓存管理 Hook

```jsx
// src/hooks/useTaskCache.js
import { useState, useCallback } from 'react';

export function useTaskCache() {
  const [taskRequestCache] = useState({});
  
  const checkTaskCache = useCallback((taskId, imageIndex = 0) => {
    const now = Date.now();
    const cacheKey = `${taskId}_${imageIndex}`;
    
    if (
      taskRequestCache[cacheKey] && 
      now - taskRequestCache[cacheKey].timestamp < 5000 &&
      taskRequestCache[cacheKey].status === 'pending'
    ) {
      return { cached: true, cacheKey };
    }
    
    taskRequestCache[cacheKey] = {
      timestamp: now,
      status: 'pending'
    };
    
    return { cached: false, cacheKey };
  }, [taskRequestCache]);
  
  const updateTaskCache = useCallback((cacheKey, status) => {
    if (taskRequestCache[cacheKey]) {
      taskRequestCache[cacheKey].status = status;
    }
  }, [taskRequestCache]);
  
  return { checkTaskCache, updateTaskCache };
}
```

### 2. 统一的错误处理工具

```jsx
// src/utils/errorHandling.js
import { message } from 'antd';

export const handleComponentNotFound = (componentType, setShowImageDetails = null) => {
  console.error(`未找到${componentType}组件，无法显示详情`);
  message.error(`无法加载图片详情：${componentType}组件数据缺失`);
  
  // 可选关闭详情弹窗
  if (setShowImageDetails) {
    setShowImageDetails(false);
  }
  
  return false;
};

export const handleTaskFetchError = (error, cacheKey, updateTaskCache, setShowImageDetails) => {
  updateTaskCache(cacheKey, 'error');
  console.error('获取最新任务数据失败:', error.message || '未知错误');
  message.error({ content: '获取任务详情失败', key: 'loadingDetails', duration: 2 });
  setShowImageDetails(false);
};
```

### 3. 完整的任务详情获取 Hook

```jsx
// src/hooks/useTaskDetails.js
import { useCallback } from 'react';
import { message } from 'antd';
import { getTaskById } from '../api/task';
import { getCurrentUserId } from '../api';
import { useTaskCache } from './useTaskCache';
import { handleComponentNotFound, handleTaskFetchError } from '../utils/errorHandling';

export function useTaskDetails() {
  const { checkTaskCache, updateTaskCache } = useTaskCache();

  const fetchTaskDetails = useCallback(async (
    task, 
    imageIndex = 0, 
    onSuccess, 
    setShowImageDetails
  ) => {
    // 获取任务ID
    const taskId = task.taskId || task.id || task.apiTaskId;
    if (!taskId) {
      console.error('无法获取有效的任务ID');
      message.error('无法获取任务详情：任务ID无效');
      return;
    }
    
    // 检查缓存
    const { cached, cacheKey } = checkTaskCache(taskId, imageIndex);
    if (cached) {
      console.log(`跳过重复请求: ${taskId}`);
      return;
    }
    
    // 显示加载状态
    message.loading({ content: '正在加载任务详情...', key: 'loadingDetails' });
    
    // 获取用户ID
    const userId = task.userId || getCurrentUserId() || 'developer';
    
    try {
      // 获取任务数据
      const latestTask = await getTaskById(taskId, userId);
      updateTaskCache(cacheKey, 'completed');
      
      if (!latestTask) {
        console.error('无法从数据库获取最新任务数据');
        message.error({ content: '无法加载任务详情', key: 'loadingDetails', duration: 2 });
        setShowImageDetails(false);
        return;
      }
      
      // 处理组件数据
      const components = Array.isArray(latestTask.components) ? 
        latestTask.components : Object.values(latestTask.components || {});
      
      // 成功回调
      onSuccess(latestTask, components);
    } catch (error) {
      handleTaskFetchError(error, cacheKey, updateTaskCache, setShowImageDetails);
    }
  }, [checkTaskCache, updateTaskCache]);

  return { fetchTaskDetails };
}
```

## 实施步骤

1. **创建共享组件**：
   - 实现上述 Hook 和工具函数
   - 添加单元测试确保功能正确

2. **渐进式重构**：
   - 选择一个页面（如 `matting/index.jsx`）作为试点
   - 引入新的 Hook 和工具函数，替换原有实现
   - 测试功能确保行为一致
   - 逐步扩展到其他页面

3. **页面列表**（需重构）：
   - src/pages/tools/matting/index.jsx
   - src/pages/tools/upscale/index.jsx
   - src/pages/tools/extract/index.jsx
   - src/pages/tools/extend/index.jsx
   - src/pages/style/inspiration/index.jsx
   - src/pages/style/trending/index.jsx
   - src/pages/style/optimize/index.jsx
   - src/pages/model/recolor/index.jsx
   - src/pages/model/virtual/index.jsx
   - src/pages/model/try-on/index.jsx
   - src/pages/model/fashion/index.jsx
   - src/pages/model/fabric/index.jsx
   - src/pages/model/background/index.jsx

## 预期收益

1. **代码量减少**：每个页面组件的代码将减少约 50-100 行
2. **维护成本降低**：缓存和错误处理逻辑的修改只需在一处进行
3. **一致性提高**：所有页面将有相同的缓存策略和错误处理方式
4. **可测试性增强**：抽离的逻辑更容易进行单元测试
5. **可扩展性提升**：未来添加新功能页面时可直接复用已有的 Hook 和工具函数

## 优先级与时间估计

- **优先级**：中
- **预估工作量**：
  - Hook 和工具函数开发：1 天
  - 首个页面重构与测试：0.5 天
  - 每个额外页面重构：0.2 天
  - 总计约 3-4 天工作量

## 风险与缓解策略

- **重构风险**：可能引入新的错误或行为变化
  - 缓解：增量式重构，每完成一个页面进行充分测试
- **性能影响**：共享 Hook 可能在某些情况下有轻微性能差异
  - 缓解：在实施过程中进行性能比较，确保无明显退化 