import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

/**
 * 服装面板组件 - 用于展示上传的服装图片和状态
 * 
 * 此组件在多个页面中被复用，根据pageType参数显示不同的内容：
 * - fashion(时尚大片)：显示"抠图完成 - 上传完成"状态文本
 * - try-on(模特换装)：显示"抠图完成 - 上传完成"状态文本，与时尚大片保持一致
 * - recolor(服装复色)：显示"上传完成"简单状态文本
 * 
 * 注意：在不同页面中名称可能不同：
 * - 在fashion和try-on页面中，作为"服装图"组件
 * - 在recolor页面中，作为"服装复色图"组件，内部标记为recolorClothing类型
 */
const ClothingPanel = ({
  panel,
  onExpandClick,
  onDelete,
  onReupload,
  onStatusChange,
  isActive,
  onPanelsChange,
  pageType = 'fashion' // 默认为时尚大片页面
}) => {
  // 处理上传结果
  const handleUploadResult = (results) => {
    console.log('处理上传结果:', JSON.stringify(results, null, 2));
    
    try {
      // 如果是标准的success/results格式
      if (results.success && Array.isArray(results.results)) {
        const newPanels = results.results.map((result, index) => {
          console.log(`处理结果项 #${index+1}:`, result);
          
          // 检查是否有relativePath字段
          let processedImageUrl = null;
          if (result.processedFile) {
            if (result.relativePath) {
              processedImageUrl = `http://localhost:3002${result.relativePath}`;
              console.log('使用相对路径构建URL:', processedImageUrl);
            } else {
              // 直接使用上传图片，不获取处理后的图片
              console.log('使用默认路径构建URL:', processedImageUrl);
            }
          }
          
          return {
            id: `clothing-${index + 1}`,
            title: results.results.length === 1 ? '服装' : `服装 ${index + 1}`,
            status: result.error ? 'error' : 'completed',
            error: result.error || null,
            serverFileName: result.serverFileName,
            processedFile: result.processedFile,
            // 保存完整的processInfo以便后续使用
            processInfo: {
              serverFileName: result.serverFileName,
              processedFile: result.processedFile,
              relativePath: result.relativePath,
              url: result.url
            },
            fileInfo: {
              ...(result.fileInfo || {}),
              format: result.fileInfo?.type || 'image/png',
              serverFileName: result.serverFileName
            },
            showOriginal: true
          };
        });
        console.log('更新后的面板数据:', newPanels);
        onPanelsChange(newPanels);
        return;
      }

      // 处理初始的panels类型数据
      if (results.type === 'panels' && Array.isArray(results.panels)) {
        const panels = results.panels.map((panel, index) => ({
          ...panel,
          title: results.panels.length === 1 ? '服装' : `服装 ${index + 1}`,
          // 确保从上传结果中保存processInfo
          processInfo: panel.processInfo || null,
          // 确保保存服务器文件名
          serverFileName: panel.serverFileName || panel.processInfo?.serverFileName || panel.componentId
        }));
        onPanelsChange(panels);
        return;
      }

      throw new Error('无法识别的响应格式');

    } catch (error) {
      console.error('处理上传结果时出错:', error);
      const errorPanel = {
        componentId: `clothing-${Date.now()}`,
        title: '服装',
        status: 'error',
        error: error.message || '处理失败',
        serverFileName: results?.serverFileName || results?.filename,
        showOriginal: true
      };
      onPanelsChange([errorPanel]);
    }
  };

  const handleExpandClick = (e) => {
    const buttonRect = e.currentTarget.getBoundingClientRect();
    onExpandClick(panel, {
      top: buttonRect.top,
      left: buttonRect.left + buttonRect.width
    });
  };

  const handleDelete = () => {
    if (panel && panel.componentId) {
      onDelete?.(panel.componentId);
      message.success('已删除服装面板');
    }
  };

  const handleReupload = () => {
    if (panel && panel.componentId) {
      onReupload?.(panel);
    }
  };

  const getImageInfo = () => {
    if (!panel || !panel.processedFile) return null;

    if (panel.fileInfo) {
      return {
        size: `${(panel.fileInfo.size / (1024 * 1024)).toFixed(2)} MB`,
        resolution: `${panel.fileInfo.width} x ${panel.fileInfo.height} px`,
        format: panel.fileInfo.format
      };
    }

    return {
      size: '--',
      resolution: '--',
      format: '--'
    };
  };

  // 根据页面类型获取适当的状态文本
  const getStatusText = () => {
    if (pageType === 'fashion') {
      // 在时尚大片页面显示"抠图完成 - 上传完成"
      return '抠图完成 - 上传完成';
    } else if (pageType === 'try-on') {
      // 在模特换装页面显示简单状态"上传完成"，与换面料页面保持一致
      return '上传完成';
    } else {
      // 在其他页面(如服装复色页面)显示简单状态
      return '上传完成';
    }
  };



  return (
    <div className="panel-component">
      <div className="component-header">
        <div className="component-info">
          <ThumbnailWithPreview
            imageUrl={panel.processedFile || panel.url}
            alt={`${panel.title} 上传结果`}
            status={panel.status}
            error={panel.error}
            onStatusChange={onStatusChange}
            featureName="服装预览"
            transparentBg={true}
          />
          <div className="component-text">
            <h3>
              {pageType === 'recolor' ? '服装' : 
               pageType === 'fabric' ? '服装' :
               pageType === 'try-on' ? '服装' :
               panel.title}
            </h3>
            <div className="component-content">
              <p>
                {panel.status === 'completed' && (
                  <>
                    {getStatusText()}
                    {panel.type === 'model' && (
                      <>
                        {panel.hasMask ? (
                          <span className="mask-status">已手动绘制蒙版</span>
                        ) : (
                          <span className="mask-status">将自动绘制蒙板</span>
                        )}
                      </>
                    )}
                  </>
                )}
                {panel.status === 'processing' && '自动抠图中...'}
                {panel.status === 'error' && panel.error}
              </p>
            </div>
          </div>
        </div>
        <button 
          className={`expand-btn ${isActive ? 'active' : ''}`}
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>


    </div>
  );
};

ClothingPanel.propTypes = {
  panel: PropTypes.shape({
    componentId: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    status: PropTypes.oneOf(['processing', 'completed', 'error']).isRequired,
    error: PropTypes.string,
    processedFile: PropTypes.string,
    url: PropTypes.string,
    serverFileName: PropTypes.string,
    originalImage: PropTypes.string, // 保留originalImage字段，用于存储原始图片信息
    type: PropTypes.string, // 业务类型，如'clothing', 'recolorClothing'等
    source: PropTypes.string, // 图片来源，如'upload', 'preset', 'history'等
    fileInfo: PropTypes.shape({
      size: PropTypes.number,
      width: PropTypes.number,
      height: PropTypes.number,
      format: PropTypes.string,
      serverFileName: PropTypes.string,
    }),
    showOriginal: PropTypes.bool,
  }).isRequired,
  onExpandClick: PropTypes.func.isRequired,
  onDelete: PropTypes.func,
  onReupload: PropTypes.func,
  onStatusChange: PropTypes.func,
  isActive: PropTypes.bool,
  onPanelsChange: PropTypes.func.isRequired,
  pageType: PropTypes.string,
};

export default ClothingPanel; 