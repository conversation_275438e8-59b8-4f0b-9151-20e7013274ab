import React, { useState, useEffect } from 'react';
import './index.css';
import ComputeCost from '../ComputeCost';

const ControlPanel = React.forwardRef(({
  width,
  activeTab,
  onTabChange,
  onGenerate,
  disabled,
  children,
  tabs,
  featureName,
  quantity = 1
}, ref) => {
  // 如果没有提供标签页配置，则默认只有"操作区"一个标签
  const tabsData = tabs || [{ key: 'default', label: '操作区' }];
  
  // 使用state来跟踪是否为移动端
  const [isMobile, setIsMobile] = useState(false);
  
  // 监听窗口大小变化
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    // 初始化检查
    checkMobile();
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkMobile);
    
    // 清理监听器
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);
  
  // 在移动端时忽略传入的width属性，让CSS媒体查询生效
  const panelStyle = isMobile ? {} : { width };
  
  return (
    <div className="control-panel" ref={ref} style={panelStyle}>
      <div className="control-header">
        <div className="modal-tabs">
          {tabsData.map(tab => (
            <button 
              key={tab.key}
              className={`tab-button ${(!activeTab && tab.key === 'default') || activeTab === tab.key ? 'active' : ''}`}
              onClick={() => onTabChange && onTabChange(tab.key)}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      <div className="control-content">
        {children}
      </div>

      <div className="control-footer">
        <button 
          className="generate-btn" 
          onClick={onGenerate}
          disabled={disabled}
        >
          开始生成
          {featureName && <ComputeCost featureName={featureName} quantity={quantity} subType={activeTab} />}
        </button>
      </div>
    </div>
  );
});

export default ControlPanel; 