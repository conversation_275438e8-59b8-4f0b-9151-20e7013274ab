# 优秀案例管理系统使用说明

## 概述

新的优秀案例管理系统采用基于文件命名规则的动态生成方式，支持灵活数量的案例卡片，无需硬编码数量限制。

## 文件命名规则

### 图片文件命名
- **格式**: `{功能前缀}_{序号}.jpg`
- **示例**: 
  - `fashion_001.jpg` - 时尚大片第1个案例
  - `bg_005.jpg` - 换背景第5个案例
  - `virtual_012.jpg` - 虚拟模特第12个案例

### 视频文件命名（仅AI视频功能）
- **格式**: `{功能前缀}_{序号}.mp4`
- **示例**:
  - `imgtxt_001.mp4` - 图文成片第1个案例
  - `mulimg_003.mp4` - 多图成片第3个案例

## 功能前缀对照表

| 功能页面 |     前缀     |    示例文件名      |
|---------|--------------|--------------------|
| 时尚大片 | `fashion_`   | `fashion_001.jpg` |
| 模特换装 | `tryon_`     | `tryon_001.jpg`   |
| 服装复色 | `recolor_`   | `recolor_001.jpg` |
| 换背景   | `bg_`        | `bg_001.jpg`      |
| 虚拟模特 | `virtual_`   | `virtual_001.jpg` |
| 换模特   | `change_`    | `change_001.jpg`  |
| 细节还原 | `detail_`    | `detail_001.jpg`  |
| 手部修复 | `hand_`      | `hand_001.jpg`    |
| 爆款开发 | `trend_`     | `trend_001.jpg`   |
| 款式优化 | `opt_`       | `opt_001.jpg`     |
| 灵感探索 | `insp_`      | `insp_001.jpg`    |
| 换面料   | `fabric_`    | `fabric_001.jpg`  |
| 图文成片 | `imgtxt_`    | `imgtxt_001.jpg` + `imgtxt_001.mp4` |
| 多图成片 | `mulimg_`    | `mulimg_001.jpg` + `mulimg_001.mp4` |
| 自动抠图 | `matting_`   | `matting_001.jpg` |
| 智能扩图 | `extend_`    | `extend_001.jpg`  |
| 高清放大 | `upscale_`   | `upscale_001.jpg` |
| 消除笔   | `inpaint_`   | `inpaint_001.jpg` |

## 使用方法

### 1. 添加新案例
1. 准备图片文件，按照命名规则命名
2. 将文件上传到 `public/images/showcase/` 目录
3. 如果是AI视频功能，同时上传对应的视频文件到 `public/videos/showcase/` 目录
4. 重新部署前端应用

### 2. 删除案例
1. 从 `public/images/showcase/` 目录删除对应的图片文件
2. 如果是AI视频功能，同时删除对应的视频文件
3. 重新部署前端应用

### 3. 修改案例
1. 替换 `public/images/showcase/` 目录中的图片文件（保持文件名不变）
2. 如果是AI视频功能，同时替换对应的视频文件
3. 重新部署前端应用

## 系统特性

### ✅ 灵活数量支持
- 每个功能页面支持1-50个案例
- 无需预先定义固定数量
- 根据实际文件数量动态显示

### ✅ 统一命名规范
- 清晰的文件命名规则
- 便于管理和维护
- 支持批量操作

### ✅ 自动生成数据
- 系统自动根据文件命名生成案例数据
- 无需手动维护数据文件
- 减少人为错误

### ✅ 向后兼容
- 保持原有的API接口
- 现有组件无需修改
- 平滑升级

## 注意事项

1. **文件格式**: 图片必须为JPG格式，视频必须为MP4格式
2. **序号格式**: 序号必须为3位数字，不足3位前面补0（如：001, 012, 123）
3. **文件路径**: 图片文件放在 `public/images/showcase/`，视频文件放在 `public/videos/showcase/`
4. **部署要求**: 修改文件后需要重新部署前端应用才能生效

## 扩展建议

如果未来需要更高级的功能，可以考虑：

1. **API驱动**: 将文件列表通过后端API提供，支持实时更新
2. **管理后台**: 开发Web管理界面，支持在线管理案例
3. **CDN支持**: 将文件存储在CDN上，提高访问速度
4. **缓存机制**: 添加缓存层，减少文件检查开销

## 技术支持

如有问题，请检查：
1. 文件命名是否符合规范
2. 文件是否放在正确的目录
3. 文件格式是否正确
4. 是否已重新部署应用 