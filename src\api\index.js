import request, { getCurrentUserId } from './request';
import { getCSRFToken } from './security/csrf';
import handleError from '../services/error';

/**
 * 创建请求拦截器
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @param {string} context - 请求上下文，用于错误处理
 * @param {string} userId - 用户ID，为null时使用当前登录用户
 * @returns {Promise<any>} 请求结果
 */
export const createRequest = async (endpoint, options = {}, context = '请求', userId = null) => {
  const token = localStorage.getItem('token');
  // 使用传入的userId或从getCurrentUserId获取
  const currentUserId = userId || getCurrentUserId();
  
  // 添加任务相关API的详细跟踪
  const isTaskRequest = endpoint.includes('/tasks/');
  if (isTaskRequest) {
    console.log(`【API跟踪】${options.method || 'GET'} ${endpoint}, 用户ID: ${currentUserId}, 上下文: ${context}`);
  } else {
    console.log(`创建请求: ${endpoint}, 用户ID: ${currentUserId}`);
  }
  
  // 设置默认headers
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // 如果有token，添加到headers
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  // 对于非GET请求，添加CSRF令牌
  if (options.method && options.method !== 'GET') {
    const csrfToken = getCSRFToken();
    if (csrfToken) {
      headers['X-CSRF-Token'] = csrfToken;
    }
  }
  
  // 构建完整的URL和请求体
  let url = endpoint;
  let params = options.params || {};
  let body = options.body;
  
  // 如果提供了用户ID，添加到URL查询参数或请求体中
  // if (currentUserId) {
  //   if (!options.method || options.method === 'GET') {
  //     params = { ...params, userId: currentUserId };
  //   } else if (body) {
  //     body = { ...body, userId: currentUserId };
  //   }
  // } 

  try {
    // 记录开始时间
    const startTime = Date.now();
    let response;
    
    // 根据请求方法调用不同的request方法
    switch (options.method || 'GET') {
      case 'POST':
        response = await request.post(url, body);
        break;
      case 'PUT':
        response = await request.put(url, body);
        break;
      case 'DELETE':
        response = await request.delete(url);
        break;
      case 'PATCH':
        response = await request.patch(url, body);
        break;
      default: // GET
        response = await request.get(url, params);
    }
    
    // 记录任务相关API响应时间和结果
    if (isTaskRequest) {
      const duration = Date.now() - startTime;
      const dataSize = response ? JSON.stringify(response).length : 0;
      console.log(`【API跟踪】${options.method || 'GET'} ${endpoint} 响应，耗时: ${duration}ms，数据大小: ${dataSize}字节`);
      
      // 检查taskId
      if (response?.data?.taskId) {
        console.log(`【API跟踪】任务ID: ${response.data.taskId}`);
      }
      
      // 检查组件数据
      if (response?.data?.components) {
        const componentsType = typeof response.data.components;
        const isArray = Array.isArray(response.data.components);
        const count = isArray ? response.data.components.length : 
                    (componentsType === 'object' ? Object.keys(response.data.components).length : 0);
                    
        console.log(`【API跟踪】组件数据类型: ${componentsType}, 是否数组: ${isArray}, 数量: ${count}`);
      }
    }
    
    return response;
  } catch (error) {
    console.error(`API请求错误: ${endpoint}`, error);
    throw error;
  }
};

// 导出API方法
export const api = {
  get: (endpoint, params = {}, context = '获取数据', userId = null) => 
    createRequest(endpoint, { method: 'GET', params }, context, userId),
    
  post: (endpoint, data = {}, context = '提交数据', userId = null) => 
    createRequest(endpoint, {
      method: 'POST',
      body: data,
    }, context, userId),
    
  put: (endpoint, data = {}, context = '更新数据', userId = null) => 
    createRequest(endpoint, {
      method: 'PUT',
      body: data,
    }, context, userId),
    
  delete: (endpoint, context = '删除数据', userId = null) => 
    createRequest(endpoint, { method: 'DELETE' }, context, userId),
    
  patch: (endpoint, data = {}, context = '部分更新', userId = null) => 
    createRequest(endpoint, {
      method: 'PATCH',
      body: data,
    }, context, userId),
};

// 导出工具函数
export { getCurrentUserId };

// 默认导出API方法
export default api; 