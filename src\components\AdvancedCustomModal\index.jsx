import React, { useState, useEffect } from 'react';
import BaseModal from '../common/BaseModal';
import './index.css';

const AdvancedCustomModal = ({ 
  isOpen = false,  // 添加isOpen属性
  onClose, 
  onSave,
  savedSettings,
  onSettingsChange,
  pageType, // 页面类型参数
  positivePrompt, // 添加正面提示词属性
  negativePrompt, // 添加负面提示词属性
  onPositiveChange, // 添加正面提示词变更回调
  onNegativeChange  // 添加负面提示词变更回调
}) => {
  // 使用外部传入的提示词或从savedSettings中恢复提示词
  const [promptText, setPromptText] = useState(positivePrompt || savedSettings?.description || '');
  const [negativePromptText, setNegativePromptText] = useState(negativePrompt || savedSettings?.negativeDescription || '');
  
  // 当外部提示词变更时更新内部状态
  useEffect(() => {
    if (positivePrompt !== undefined) {
      setPromptText(positivePrompt);
    }
    if (negativePrompt !== undefined) {
      setNegativePromptText(negativePrompt);
    }
  }, [positivePrompt, negativePrompt]);

  // 根据页面类型确定标题和提示文本
  const getTitle = () => {
    if (pageType === 'optimize') {
      return '填写款式优化的提示词';
    }
    return '填写整体描述提示词';
  };

  const getPlaceholderText = (isNegative = false) => {
    if (pageType === 'optimize') {
      return isNegative 
        ? "描述您不希望出现的元素，例如：错误的结构、过度锐化、细节失真、色彩失调..."
        : "描述您期望的款式优化效果，例如：分体款改为连体款、使用想要的图案、优化设计细节、精细的材质、增强色彩...";
    } else {
      return isNegative 
        ? "描述您不希望出现的元素，例如：模糊不清、色彩失真、变形..."
        : "描述您期望的效果，越详细越好";
    }
  };

  const handlePromptChange = (e) => {
    setPromptText(e.target.value);
    if (onPositiveChange) onPositiveChange(e.target.value);
  };

  const handleNegativePromptChange = (e) => {
    setNegativePromptText(e.target.value);
    if (onNegativeChange) onNegativeChange(e.target.value);
  };

  const handleSaveCustom = () => {
    // 调用外部保存方法
    if (onSave) {
      onSave({
        description: promptText,
        negativeDescription: negativePromptText
      });
    }

    // 调用设置变更方法
    if (onSettingsChange) {
      onSettingsChange({
        description: promptText,
        negativeDescription: negativePromptText
      });
    }
  };

  const handleClear = () => {
    setPromptText('');
    setNegativePromptText('');
    if (onPositiveChange) onPositiveChange('');
    if (onNegativeChange) onNegativeChange('');
  };

  // 如果不是打开状态，不渲染任何内容
  if (!isOpen) return null;

  const tabs = [{ 
    key: 'custom', 
    label: pageType === 'optimize' ? '优化需求设置' : '自定义提示词' 
  }];

  const footer = (
    <>
      <button 
        className="clear-btn"
        onClick={handleClear}
      >
        清空内容
      </button>
      <button 
        className="save-settings-btn"
        onClick={() => {
          handleSaveCustom();
          onClose();
        }}
        disabled={!promptText.trim()}
      >
        确认设置
      </button>
    </>
  );

  return (
    <BaseModal
      className="advanced-custom-modal"
      onClose={onClose}
      tabs={tabs}
      activeTab="custom" // 直接设置为固定值
      onTabChange={null} // 不需要切换标签页
      footer={footer}
      size="large"
    >
      <div className="custom-scene">
        <div className="filter-section">
          <h3 className="section-title">{getTitle()}</h3>
          <div className="prompt-input">
            <label>正面提示词</label>
            <textarea
              placeholder={getPlaceholderText()}
              rows={4}
              className="prompt-textarea"
              value={promptText}
              onChange={handlePromptChange}
            />
          </div>
          <div className="prompt-input">
            <label>负面提示词（可选）</label>
            <textarea
              placeholder={getPlaceholderText(true)}
              rows={4}
              className="prompt-textarea"
              value={negativePromptText}
              onChange={handleNegativePromptChange}
            />
          </div>
        </div>
      </div>
    </BaseModal>
  );
};

export default AdvancedCustomModal; 