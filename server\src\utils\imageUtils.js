const sharp = require('sharp');
const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * 获取图片的EXIF方向信息
 * @param {string} imagePath - 图片文件路径
 * @returns {Promise<number>} - 方向值 (1-8)
 */
async function getImageOrientation(imagePath) {
  try {
    const metadata = await sharp(imagePath).metadata();
    return metadata.orientation || 1;
  } catch (error) {
    console.error('获取图片方向信息失败:', error);
    return 1; // 默认返回1（正常方向）
  }
}

/**
 * 自动校正图片方向
 * @param {string} originalPath - 原始图片路径
 * @param {string} outputPath - 输出图片路径（可选，如果不提供则创建临时文件）
 * @returns {Promise<string>} - 处理后的图片路径
 */
async function correctImageOrientation(originalPath, outputPath = null) {
  try {
    // 获取图片方向信息
    const orientation = await getImageOrientation(originalPath);
    
    // 如果方向正常，直接返回原路径
    if (orientation === 1) {
      return originalPath;
    }
    
    // 确定输出路径
    let finalOutputPath = outputPath;
    if (!finalOutputPath) {
      // 创建临时文件
      const tempDir = os.tmpdir();
      const originalName = path.basename(originalPath);
      const tempName = `corrected_${Date.now()}_${originalName}`;
      finalOutputPath = path.join(tempDir, tempName);
    }
    
    // 使用sharp自动校正方向
    await sharp(originalPath)
      .rotate() // sharp会自动根据EXIF信息旋转图片
      .toFile(finalOutputPath);
    
    console.log(`图片方向已校正: ${originalPath} -> ${finalOutputPath}`);
    return finalOutputPath;
    
  } catch (error) {
    console.error('校正图片方向失败:', error);
    throw error;
  }
}

/**
 * 处理图片并保存元数据
 * @param {string} originalPath - 原始图片路径
 * @param {string} outputPath - 输出路径（可选）
 * @returns {Promise<Object>} - 处理结果，包含路径和元数据
 */
async function processImageWithMetadata(originalPath, outputPath = null) {
  try {
    // 获取原始元数据
    const originalMetadata = await sharp(originalPath).metadata();
    
    // 检查并校正方向
    const orientation = originalMetadata.orientation || 1;
    let processedPath = originalPath;
    let isTempFile = false;
    
    if (orientation !== 1) {
      processedPath = await correctImageOrientation(originalPath, outputPath);
      // 检查是否为临时文件
      if (!outputPath && processedPath !== originalPath) {
        isTempFile = true;
      }
    }
    
    // 获取处理后的元数据
    const processedMetadata = await sharp(processedPath).metadata();
    
    return {
      originalPath,
      processedPath,
      originalMetadata,
      processedMetadata,
      orientation,
      wasCorrected: orientation !== 1,
      isTempFile
    };
    
  } catch (error) {
    console.error('处理图片失败:', error);
    throw error;
  }
}

/**
 * 批量处理图片方向
 * @param {Array<string>} imagePaths - 图片路径数组
 * @param {string} outputDir - 输出目录（可选）
 * @returns {Promise<Array<Object>>} - 处理结果数组
 */
async function batchCorrectImageOrientation(imagePaths, outputDir = null) {
  const results = [];
  
  for (const imagePath of imagePaths) {
    try {
      let outputPath = null;
      if (outputDir) {
        const filename = path.basename(imagePath);
        outputPath = path.join(outputDir, filename);
      }
      
      const result = await processImageWithMetadata(imagePath, outputPath);
      results.push(result);
    } catch (error) {
      console.error(`处理图片失败 ${imagePath}:`, error);
      results.push({
        originalPath: imagePath,
        error: error.message
      });
    }
  }
  
  return results;
}

/**
 * 清理临时文件
 * @param {string} tempFilePath - 临时文件路径
 */
function cleanupTempFile(tempFilePath) {
  try {
    if (tempFilePath && fs.existsSync(tempFilePath)) {
      fs.unlinkSync(tempFilePath);
      console.log(`临时文件已清理: ${tempFilePath}`);
    }
  } catch (error) {
    console.warn(`清理临时文件失败: ${tempFilePath}`, error);
  }
}

module.exports = {
  getImageOrientation,
  correctImageOrientation,
  processImageWithMetadata,
  batchCorrectImageOrientation,
  cleanupTempFile
}; 