import React, { createContext, useContext, useState, useCallback } from 'react';

const ModalContext = createContext({
  showModal: () => {},
  hideModal: () => {},
  modalState: {},
  setModalState: () => {}
});

export const ModalProvider = ({ children }) => {
  const [modalState, setModalState] = useState({
    modelSelect: { visible: false, data: null },
    sceneSelect: { visible: false, data: null },
    advancedCustom: { visible: false, data: null },
    imageDetails: { visible: false, data: null },
    uploadGuide: { visible: false, data: null },
    clothingOperations: { visible: false, data: null }
  });

  const showModal = useCallback((modalType, data = null) => {
    setModalState(prev => ({
      ...prev,
      [modalType]: { visible: true, data }
    }));
  }, []);

  const hideModal = useCallback((modalType) => {
    setModalState(prev => ({
      ...prev,
      [modalType]: { visible: false, data: null }
    }));
  }, []);

  return (
    <ModalContext.Provider value={{
      modalState,
      setModalState,
      showModal,
      hideModal
    }}>
      {children}
    </ModalContext.Provider>
  );
};

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export default ModalContext; 