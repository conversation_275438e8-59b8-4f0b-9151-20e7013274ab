import React from 'react';
import { MdBrush, MdOutlinePolyline } from 'react-icons/md';
import { FaEraser } from 'react-icons/fa';
import './index.css';

/**
 * 绘图工具组件 - 包含画笔、橡皮擦和笔刷大小调节功能
 * 
 * @param {Object} props
 * @param {string} props.activeTool - 当前激活的工具 ('brush' 或 'eraser')
 * @param {Function} props.onToolChange - 工具切换回调函数
 * @param {number} props.brushSize - 当前笔刷大小
 * @param {Function} props.onBrushSizeChange - 笔刷大小变化回调函数
 */
const DrawTools = ({ 
  activeTool = 'brush', 
  onToolChange, 
  brushSize = 28, 
  onBrushSizeChange 
}) => {
  return (
    <div className="draw-tools">
      <div className="tools-group">
        <div className="tool-buttons">
          <button 
            className={`tool-btn draw-tool-btn ${activeTool === 'brush' ? 'active' : ''}`}
            onClick={() => onToolChange('brush')}
            title="涂抹工具"
          >
            <MdBrush />
            <span className="tool-name">涂抹</span>
          </button>
          {/* 选区工具按钮 */}
          <button 
            className={`tool-btn draw-tool-btn ${activeTool === 'polygon' ? 'active' : ''}`}
            onClick={() => onToolChange('polygon')}
            title="选区工具"
          >
            <MdOutlinePolyline />
            <span className="tool-name">选区</span>
          </button>
          <button 
            className={`tool-btn draw-tool-btn ${activeTool === 'eraser' ? 'active' : ''}`}
            onClick={() => onToolChange('eraser')}
            title="擦除工具"
          >
            <FaEraser />
            <span className="tool-name">擦除</span>
          </button>
        </div>
      </div>

      <div className="tools-group">
        <div className="brush-size-control">
          <div className="brush-size-display">
            <span className="brush-size-value">{brushSize}px</span>
            <span className="brush-size-label">笔刷大小</span>
          </div>
          <input 
            type="range" 
            min="10" 
            max="100" 
            value={brushSize} 
            onChange={(e) => onBrushSizeChange(Number(e.target.value))}
            className="brush-size-slider"
            style={{
              '--value': brushSize,
              '--min': 10,
              '--max': 100
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default DrawTools; 