/**
 * 任务适配器工具模块
 * 提供任务数据结构的转换和验证函数
 */

import { generateId, ID_TYPES } from './idGenerator';

/**
 * 验证任务数据结构是否符合格式要求
 * @param {Object} task - 待验证的任务数据
 * @returns {Object} 包含验证结果和错误信息的对象
 */
export const validateTaskFormat = (task) => {
  const errors = [];
  let isValid = true;

  // 基本字段验证
  if (!task) {
    return { isValid: false, errors: ['任务对象为空'] };
  }

  if (!task.taskId) {
    errors.push('缺少任务ID');
    isValid = false;
  }

  // 验证components结构
  if (!task.components) {
    errors.push('缺少components对象');
    isValid = false;
  } else {
    // 检查是否有组件
    const componentCount = Object.keys(task.components).length;
    if (componentCount === 0) {
      errors.push('任务没有任何组件');
      isValid = false;
    }
    
    // 检查是否有主图片组件
    if (task.requiresImage !== false) { // 默认需要图片
      let hasMainImage = false;
      for (const component of Object.values(task.components)) {
        if (component.isMainImage) {
          hasMainImage = true;
          break;
        }
      }
      
      if (!hasMainImage) {
        errors.push('任务需要图片但没有主图片组件');
      }
    }
  }

  return { isValid, errors };
};

/**
 * 从任务中提取源图片组件信息
 * @param {Object} task - 任务对象
 * @returns {Object|null} 源图片组件或null
 */
export const extractSourceFromTask = (task) => {
  if (!task) return null;

  // 先使用getTaskComponent尝试获取
  let sourceComponent = getTaskComponent(task, 'sourceImagePanel');
  
  // 如果没有找到，尝试从components数组中查找
  if (!sourceComponent && task.components) {
    if (Array.isArray(task.components)) {
      sourceComponent = task.components.find(comp => 
        comp && comp.componentType === 'sourceImagePanel'
      );
    } else if (typeof task.components === 'object') {
      // 从对象格式的components中查找
      for (const key in task.components) {
        const comp = task.components[key];
        if (comp && comp.componentType === 'sourceImagePanel') {
          sourceComponent = comp;
      break;
    }
  }
    }
  }
  
  return sourceComponent;
};

/**
 * 获取任务源图片的URL
 * 根据任务数据结构自动找到正确的图片URL
 * @param {Object} task - 任务数据
 * @returns {string|null} 图片URL或null
 */
export const getTaskSourceImageUrl = (task) => {
  // 如果任务为空，返回null
  if (!task) return null;
  
  // 如果任务有components结构，查找主图片组件
  if (task.components) {
    // 1. 先查找标记为主图片的组件
    let mainImageComponent = null;
    
    if (Array.isArray(task.components)) {
      // 如果components是数组，查找标记为isMainImage的组件
      mainImageComponent = task.components.find(comp => comp && comp.isMainImage);
      if (mainImageComponent && mainImageComponent.url) {
        return mainImageComponent.url;
      }
    } else {
      // 如果components是对象，使用原有的查找逻辑
      for (const component of Object.values(task.components)) {
        if (component.isMainImage && component.url) {
          return component.url;
        }
      }
    }
    
    // 2. 查找常见的可能包含图片URL的组件
    const imageComponentTypes = ['sourceImagePanel', 'uploadBox', 'clothingPanel'];
    for (const componentType of imageComponentTypes) {
      const component = task.components[componentType];
      if (component && component.url) {
        return component.url;
      }
    }
  }
  
  // 3. 尝试使用serverFileName构建URL
  if (task.serverFileName) {
    const baseUrl = process.env.REACT_APP_BACKEND_URL;
    const userId = task.userId || 'developer';
    return `${baseUrl}/storage/${userId}/uploads/${task.serverFileName}`;
  }
  
  // 4. 如果有生成的图片，返回第一张
  if (task.generatedImages && Array.isArray(task.generatedImages) && task.generatedImages.length > 0) {
    const image = task.generatedImages[0];
    if (image.url) return image.url;
    
    // 如果没有直接的URL但有路径，构建URL
    if (image.path) {
      const baseUrl = process.env.REACT_APP_BACKEND_URL;
      return `${baseUrl}${image.path}`;
    }
  }
  
  // 如果所有尝试都失败，返回null
  return null;
};

// 页面-组件映射常量，定义每个页面类型所需的组件
const PAGE_COMPONENT_MAP = {
  'background': ['uploadBox', 'foregroundPanel', 'scenePanel', 'randomSeedSelector', 'quantityPanel'],
  'fabric': ['uploadBox', 'clothingPanel', 'uploadBox_Model', 'MaskDescriptionPanel', 'fabricPanel', 'randomSeedSelector', 'quantityPanel'],
  'fashion': ['uploadBox', 'clothingPanel', 'textDescriptionPanel', 'weightPanel','randomSeedSelector', 'imageSizeSelector', 'quantityPanel'],
  'recolor': ['uploadBox', 'clothingPanel', 'MaskDescriptionPanel', 'colorPanel', 'colorAdjustPanel', 'typeSelector', 'quantityPanel'],
  'try-on': ['uploadBox', 'clothingPanel', 'uploadBox_Model', 'modelMaskPanel', 'MaskDescriptionPanel', 'randomSeedSelector', 'quantityPanel'],
  'virtual': ['textDescriptionPanel', 'randomSeedSelector'],
  'inspiration': ['clothingAttributesPanel', 'randomSeedSelector', 'imageSizeSelector', 'quantityPanel'],
  'optimize': ['uploadBox_Model', 'modelMaskPanel', 'textDescriptionPanel', 'randomSeedSelector', 'quantityPanel'],
  'trending': ['uploadBox', 'patternPanel', 'uploadBox_Model', 'printingPanel', 'textDescriptionPanel', 'modelNobodyPanel', 'weightPanel', 'randomSeedSelector', 'quantityPanel'],
  'extend': ['uploadBox', 'sourceImagePanel', 'extendSize','randomSeedSelector', 'quantityPanel'],
  'extract': ['uploadBox', 'sourceImagePanel', 'imageExtractionOptions'],
  'matting': ['uploadBox', 'MaskDescriptionPanel', 'sourceImagePanel'],
  'upscale': ['uploadBox', 'sourceImagePanel', 'magnificationSize'],
  'drawing': ['uploadBox', 'clothingPanel', 'randomSeedSelector', 'quantityPanel'],
  'change-posture': ['uploadBox_Model', 'modelMaskPanel', 'textDescriptionPanel', 'randomSeedSelector', 'quantityPanel', 'typeSelector'],
  'detail-migration': ['uploadBox_Model', 'modelMaskPanel', 'textDescriptionPanel', 'randomSeedSelector', 'quantityPanel'],
  'hand-fix': ['uploadBox_Model', 'modelMaskPanel', 'textDescriptionPanel', 'randomSeedSelector', 'quantityPanel']
};

/**
 * 根据页面类型过滤任务组件，只保留页面需要的组件
 * @param {Object} components - 任务组件对象
 * @param {String} pageType - 页面类型
 * @returns {Object} 过滤后的组件对象
 */
export const filterTaskComponentsByPageType = (components, pageType) => {
  if (!components || !pageType) return components;
  
  // 获取该页面所需的组件列表
  const neededComponents = PAGE_COMPONENT_MAP[pageType.toLowerCase()] || [];
  console.log(`页面 ${pageType} 需要的组件:`, neededComponents);
  
  // 创建一个新的组件对象
  const filteredComponents = {};
  
  // 只保留页面需要的组件
  for (const [key, component] of Object.entries(components)) {
    // 检查组件类型是否在需要的列表中
    if (component.componentType && 
        neededComponents.includes(component.componentType.toLowerCase())) {
      filteredComponents[key] = component;
    } 
    // 特殊处理：保留标记为主图片的组件，无论页面是否需要
    else if (component.isMainImage) {
      filteredComponents[key] = component;
    }
  }
  
  return filteredComponents;
};

/**
 * 统一获取任务组件
 * @param {Object} task - 任务对象
 * @param {String} componentKey - 组件键名
 * @returns {Object|null} 组件数据或null
 */
export const getTaskComponent = (task, componentKey) => {
  if (!task) return null;
  
  // 检查任务中的components结构
  if (!task.components) return null;
  
  // 处理点号路径
  if (componentKey.includes('.')) {
    const [mainKey, subKey] = componentKey.split('.');
    const component = getTaskComponent(task, mainKey);
    if (!component) return null;
    return component[subKey];
  }
  
  // 处理components可能是对象或数组的情况
  if (Array.isArray(task.components)) {
    // 如果components是数组，只按standardType查找
    return task.components.find(comp => 
      comp && comp.componentType === componentKey
    ) || null;
  } else {
    // 如果components是对象，直接获取或查找匹配componentType的组件
    if (task.components[componentKey]) {
      return task.components[componentKey];
    }
    
    // 查找匹配componentType的组件
    for (const key in task.components) {
      const component = task.components[key];
      if (component && component.componentType === componentKey) {
        return component;
      }
    }
    return null;
  }
};

// 导出适配器工具模块
export const taskAdapters = {
  validateTaskFormat,
  extractSourceFromTask,
  getTaskSourceImageUrl,
  filterTaskComponentsByPageType,
  PAGE_COMPONENT_MAP
};

export default taskAdapters; 