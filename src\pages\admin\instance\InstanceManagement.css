.instance-management {
  padding: 24px;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-section .ant-card {
  border-radius: 8px;
}

.instance-table-card {
  border-radius: 8px;
  height: 600px;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.table-header h2 {
  margin: 0;
}

/* 状态标签样式 */
.status-tag {
  min-width: 80px;
  text-align: center;
}

/* 系统资源进度条样式 */
.ant-progress-line {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .instance-management {
    padding: 12px;
  }
  
  .stats-section .ant-col {
    margin-bottom: 12px;
  }
}

/* 添加详情抽屉相关样式 */
.instance-detail-drawer .ant-descriptions-item-label {
  width: 120px;
  font-weight: 500;
}

.instance-detail-drawer .ant-descriptions-item-content {
  word-break: break-all;
}

.port-config-item {
  margin-bottom: 8px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
}

.port-config-item:last-child {
  margin-bottom: 0;
}

/* 修改余额卡片样式 */
.balance-card {
  background: linear-gradient(135deg, #722ed1 0%, #1890ff 100%);
}

.balance-card .ant-statistic-title {
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  margin-bottom: 8px;
}

.balance-card .ant-statistic-content {
  color: #fff;
  font-size: 20px;
}

.balance-card .ant-statistic-content-value-decimal {
  font-size: 14px;
}

.balance-card .ant-btn-link {
  color: #fff;
  opacity: 0.85;
}

.balance-card .ant-btn-link:hover {
  color: #fff;
  opacity: 1;
}

/* 调整移动端显示 */
@media (max-width: 1400px) {
  .balance-card .ant-statistic-title {
    font-size: 12px;
  }
  
  .balance-card .ant-statistic-content {
    font-size: 16px;
  }
  
  .balance-card .ant-statistic-content-value-decimal {
    font-size: 12px;
  }
}

@media (max-width: 992px) {
  .stats-section .ant-col {
    margin-bottom: 16px;
  }
  
  .balance-card .ant-row {
    flex-direction: column;
  }
  
  .balance-card .ant-col {
    margin-bottom: 8px;
  }
}

/* 调整统计卡片间距 */
.stats-section .ant-col {
  margin-bottom: 0;
}

@media (max-width: 1200px) {
  .stats-section .ant-col {
    margin-bottom: 16px;
  }
}

.instance-table-fixed .ant-table-wrapper {
  flex: 1 1 auto;
  min-height: 0;
}

.instance-table-fixed .ant-table-body {
  min-height: 0;
  max-height: 440px;
}

.stats-section .ant-row {
  flex-wrap: nowrap !important;
  display: flex;
  overflow-x: auto;
  gap: 16px;
}
.stats-section .ant-col {
  min-width: 220px;
  flex: 0 0 auto;
} 