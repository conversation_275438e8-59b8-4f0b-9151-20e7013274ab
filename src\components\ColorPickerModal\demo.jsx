import React, { useState } from 'react';
import ColorPickerModal from './index';

/**
 * 选色组件使用示例
 */
const ColorPickerDemo = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedColor, setSelectedColor] = useState('#FF3C6A');

  const handleOpenModal = () => {
    setIsOpen(true);
  };

  const handleCloseModal = () => {
    setIsOpen(false);
  };

  const handleColorSelect = (color) => {
    setSelectedColor(color);
    console.log('选中的颜色:', color);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>选色组件演示</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <p>当前选中颜色: {selectedColor}</p>
        <div 
          style={{ 
            width: '50px', 
            height: '50px', 
            backgroundColor: selectedColor,
            borderRadius: '4px',
            marginTop: '10px',
            border: '1px solid #eee'
          }} 
        />
      </div>
      
      <button 
        onClick={handleOpenModal}
        style={{
          padding: '8px 16px',
          background: 'linear-gradient(45deg, #FF8C42, #FF3C6A)',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        打开选色器
      </button>
      
      {/* 选色组件 */}
      <ColorPickerModal
        isOpen={isOpen}
        onClose={handleCloseModal}
        initialColor={selectedColor}
        onColorSelect={handleColorSelect}
      />
    </div>
  );
};

export default ColorPickerDemo; 