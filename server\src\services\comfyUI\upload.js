const fs = require('fs');
const FormData = require('form-data');
const path = require('path');

// 上传和文件处理功能扩展
class UploadExtension {
  constructor(client) {
    this.client = client;
  }

  // 清理ComfyUI输出目录的功能已移除
  // ComfyUI未提供标准API来支持此功能

  // 上传整个文件夹的图片到云容器
  async uploadFolder(folderPath) {
    try {
      console.log(`开始上传文件夹: ${folderPath} 到云容器`);
      
      // 检查文件夹是否存在
      if (!fs.existsSync(folderPath)) {
        throw new Error(`文件夹不存在: ${folderPath}`);
      }
      
      // 读取文件夹中的所有文件
      const files = fs.readdirSync(folderPath);
      
      // 过滤出图片文件（简单过滤，可以根据需要调整）
      const imageFiles = files.filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp'].includes(ext);
      });
      
      console.log(`找到 ${imageFiles.length} 个图片文件准备上传`);
      
      if (imageFiles.length === 0) {
        throw new Error('文件夹中没有找到图片文件');
      }
      
      // 上传所有图片文件
      const uploadResults = [];
      for (const imageFile of imageFiles) {
        const imagePath = path.join(folderPath, imageFile);
        console.log(`准备上传文件: ${imagePath}`);
        
        try {
          const result = await this.uploadImage(imagePath);
          uploadResults.push({
            originalName: imageFile,
            uploadedName: result.name,
            success: true,
            ...result
          });
          console.log(`成功上传文件: ${imageFile} -> ${result.name}`);
        } catch (error) {
          console.error(`上传文件 ${imageFile} 失败:`, error);
          uploadResults.push({
            originalName: imageFile,
            success: false,
            error: error.message
          });
        }
      }
      
      const successCount = uploadResults.filter(r => r.success).length;
      console.log(`文件夹上传完成: 成功 ${successCount}/${imageFiles.length} 个文件`);
      
      return {
        folderPath,
        totalFiles: imageFiles.length,
        successCount,
        results: uploadResults
      };
    } catch (error) {
      console.error('上传文件夹失败:', error);
      throw new Error(`上传文件夹失败: ${error.message}`);
    }
  }

  // 上传图片
  async uploadImage(imagePath) {
    try {
      // 清理输出目录的代码已移除，因为ComfyUI不提供标准清理API
      
      // 检查文件
      console.log('尝试访问文件:', {
        imagePath,
        absolutePath: path.resolve(imagePath),
        exists: fs.existsSync(imagePath)
      });
      
      await fs.promises.access(imagePath, fs.constants.R_OK);
      const stats = await fs.promises.stat(imagePath);
      if (stats.size === 0) {
        throw new Error('文件大小为0');
      }

      // 添加更多目录检查日志
      console.log('当前上传目录设置:', {
        uploadDir: this.client.uploadDir,
        existsUploadDir: fs.existsSync(this.client.uploadDir),
        resolvedUploadDir: path.resolve(this.client.uploadDir)
      });
      
      const formData = new FormData();
      const imageFile = fs.createReadStream(imagePath);
      const filename = path.basename(imagePath);
      
      formData.append('image', imageFile, {
        filename: filename
      });
      
      // 使用确认的API路径前缀
      const uploadPath = `${this.client.apiPathPrefix || ''}/upload/image`;
      
      console.log('准备上传图片到云实例:', {
        path: imagePath,
        filename: filename,
        size: stats.size,
        url: `${this.client.baseURL}${uploadPath}`,
        apiKey: this.client.apiKey ? '已配置' : '未配置'
      });

      const response = await this.client.axiosInstance.post(uploadPath, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${this.client.apiKey}`,
          'X-Instance-ID': this.client.instanceId
        },
        maxBodyLength: Infinity,
        timeout: 30000
      });

      if (response.status !== 200) {
        throw new Error(`上传失败: ${response.status}`);
      }

      console.log('图片上传到云实例成功:', response.data);
      
      // 返回上传后的文件名
      return {
        name: response.data.name || filename,
        ...response.data
      };
    } catch (error) {
      console.error('上传图片到云实例时出错:', error);
      if (error.response?.data) {
        console.error('上传错误详情:', error.response.data);
      }
      throw new Error(`上传到云实例失败: ${error.message}`);
    }
  }

  // 检查ComfyUI输出文件
  async checkOutputFile(filename) {
    console.log(`检查云实例输出文件: ${filename}`);
    
    // 根据日志分析，将成功率高的路径放在前面优先尝试
    const pathsToCheck = [
      // 优先尝试已知成功率高的路径
      `/view?filename=${filename}`,
      `/view?filename=output/${filename}`,
      
      // 备选路径
      `${this.client.apiPathPrefix || ''}/view?filename=${filename}`,
      `${this.client.apiPathPrefix || ''}/output/${filename}`,
      `${this.client.apiPathPrefix || ''}/view/${filename}`,
      
      // 最不常用的备选路径
      `/getImage?filename=${filename}`,
      `/files/output/${filename}`
    ];

    // 尝试所有路径
    for (const pathToCheck of pathsToCheck) {
      try {
        console.log(`尝试通过云实例API检查路径: ${pathToCheck}`);
        const response = await this.client.axiosInstance.head(pathToCheck, {
          headers: {
            'Authorization': `Bearer ${this.client.apiKey}`,
            'X-Instance-ID': this.client.instanceId
          },
          validateStatus: function(status) {
            return status === 200; // 只接受200状态码
          },
          timeout: 3000 // 减少超时时间提高效率
        });
        
        if (response.status === 200) {
          console.log(`通过云实例API在路径 ${pathToCheck} 找到文件`);
          // 记录成功路径格式供下次使用
          this.client.lastSuccessfulPathFormat = pathToCheck.replace(filename, '${filename}');
          console.log(`记录成功路径格式: ${this.client.lastSuccessfulPathFormat}`);
          return { type: 'api', path: pathToCheck };
        }
      } catch (error) {
        if (error.response?.status === 404) {
          console.log(`路径 ${pathToCheck} 未找到文件`);
        } else {
          console.error(`检查路径 ${pathToCheck} 出错:`, error.message);
        }
      }
    }

    console.log('所有路径都未找到文件，构建可能的URL');
    
    // 构建备选直接URL，优先使用已知成功的格式
    const directUrls = [
      `${this.client.baseURL}/view?filename=${filename}`,
      `${this.client.baseURL}/view?filename=output/${filename}`,
      `${this.client.baseURL}/output/${filename}`,
      `${this.client.baseURL}/files/output/${filename}`
    ];
    
    return { 
      type: 'api', 
      path: `/view?filename=${filename}`, // 使用最可能成功的路径
      possibleUrls: directUrls
    };
  }

  // 复制或下载输出文件
  async copyOutputFile(fileInfo, filename, pageType = 'fashion', imageType = 'clothing', workflowType = null) {
    // 根据页面类型和工作流类型的组合决定目标目录
    let targetDir;
    
    // 所有情况都使用generated目录
    targetDir = this.client.getTargetGeneratedDir(pageType, imageType);
    console.log(`${pageType}页面使用${workflowType || '默认'}工作流，保存到generated目录:`, targetDir);
    
    const targetPath = path.join(targetDir, filename);
    
    console.log('准备复制/下载文件:', {
      type: fileInfo.type,
      sourcePath: fileInfo.path,
      targetPath: targetPath,
      pageType,
      imageType,
      workflowType,
      targetDir
    });
    
    try {
      if (fileInfo.type === 'local') {
        console.log('从ComfyUI输出目录复制文件到:', targetPath);
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true, mode: 0o755 });
          console.log('创建目标目录:', targetDir);
        }
        
        // 检查源文件
        const sourceStats = await fs.promises.stat(fileInfo.path);
        console.log('源文件状态:', {
          size: sourceStats.size,
          created: sourceStats.birthtime,
          modified: sourceStats.mtime
        });
        
        // 检查文件大小，如果为0，等待一段时间后重试
        if (sourceStats.size === 0) {
          console.log('警告：文件大小为0，等待3秒后重试');
          await new Promise(resolve => setTimeout(resolve, 3000));
          
          // 重新检查文件
          const newStats = await fs.promises.stat(fileInfo.path);
          if (newStats.size === 0) {
            console.error('错误：文件大小仍然为0，可能处理失败');
            throw new Error('处理后的文件大小为0，ComfyUI处理可能失败');
          } else {
            console.log('重试成功，文件大小现在为:', newStats.size);
          }
        }
        
        // 复制文件到目标位置
        await fs.promises.copyFile(fileInfo.path, targetPath);
        
        // 验证复制后的文件
        const targetStats = await fs.promises.stat(targetPath);
        console.log('目标文件状态:', {
          size: targetStats.size,
          createTime: targetStats.birthtime,
          modifyTime: targetStats.mtime
        });
        
        return targetPath;
      } else if (fileInfo.type === 'api') {
        console.log('从ComfyUI API下载文件到:', targetPath);
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true, mode: 0o755 });
          console.log('创建目标目录:', targetDir);
        }
        
        // 如果有possibleUrls字段，先尝试这些URL
        const urlsToTry = fileInfo.possibleUrls || [];
        
        // 添加标准URL作为备选
        urlsToTry.push(
          // 优先尝试视图URL（已知成功率高）
          `${this.client.baseURL}/view?filename=${filename}`,
          `${this.client.baseURL}/view?filename=output/${filename}`,
          // 使用路径访问
          `${this.client.baseURL}${fileInfo.path}`,
          // 下载直链
          `${this.client.baseURL}/output/${filename}`
        );
        
        console.log('将尝试以下URL下载文件:', urlsToTry);
        
        // 依次尝试所有URL
        for (const urlToTry of urlsToTry) {
          try {
            console.log(`尝试从URL下载: ${urlToTry}`);
            
            const response = await this.client.axiosInstance({
              method: 'GET',
              url: urlToTry,
              responseType: 'stream',
              headers: {
                'Authorization': `Bearer ${this.client.apiKey}`,
                'X-Instance-ID': this.client.instanceId
              },
              timeout: 15000, // 减少超时时间
              validateStatus: status => status === 200 // 只接受200状态
            });
            
            const writer = fs.createWriteStream(targetPath);
            
            // 下载流写入文件
            await new Promise((resolve, reject) => {
              response.data.pipe(writer);
              
              writer.on('finish', () => {
                console.log('文件下载完成:', targetPath);
                resolve();
              });
              
              writer.on('error', (err) => {
                console.error('文件写入失败:', err);
                reject(err);
              });
              
              response.data.on('error', (err) => {
                console.error('流读取失败:', err);
                reject(err);
              });
            });
            
            // 检查下载文件大小
            const stats = fs.statSync(targetPath);
            console.log(`下载完成，文件大小: ${stats.size} 字节`);
            
            if (stats.size > 0) {
              return targetPath;
            } else {
              console.log('下载的文件大小为0，继续尝试其他URL');
              // 删除空文件
              fs.unlinkSync(targetPath);
            }
          } catch (error) {
            console.log(`从 ${urlToTry} 下载失败:`, error.message);
            // 继续尝试下一个URL
          }
        }
        
        // 如果所有URL都失败，尝试直接创建一个占位文件并返回警告
        console.error('所有下载URL都失败，显示警告信息');
        throw new Error('无法从云实例获取生成的图片，但生成过程已完成。请检查云实例设置。');
      } else {
        throw new Error(`未知的文件类型: ${fileInfo.type}`);
      }
    } catch (error) {
      console.error('复制/下载文件失败:', {
        error: error.message,
        stack: error.stack,
        source: fileInfo.path,
        target: targetPath,
        pageType,
        imageType,
        targetDir
      });
      throw error;
    }
  }

  // 保存图片文件
  async saveImageFile(filename, imageStream) {
    const imagePath = path.join(this.client.fashionGeneratedDir, filename);
    console.log('保存处理后图片到:', imagePath);

    return new Promise((resolve, reject) => {
      const writer = fs.createWriteStream(imagePath);
      
      writer.on('error', (error) => {
        console.error('写入文件时出错:', error);
        reject(new Error(`保存图片失败: ${error.message}`));
      });

      writer.on('finish', () => {
        // 验证文件是否成功保存
        fs.access(imagePath, fs.constants.R_OK, (err) => {
          if (err) {
            reject(new Error(`无法访问保存的文件: ${err.message}`));
          } else {
            // 获取文件大小
            fs.stat(imagePath, (err, stats) => {
              if (err || stats.size === 0) {
                reject(new Error('保存的文件大小为0或无法获取文件信息'));
              } else {
                console.log(`文件保存成功，大小: ${stats.size} 字节`);
                resolve(imagePath);
              }
            });
          }
        });
      });

      imageStream.pipe(writer);
    });
  }
}

module.exports = function(client) {
  return new UploadExtension(client);
}; 