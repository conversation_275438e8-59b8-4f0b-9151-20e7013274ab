{"53": {"inputs": {"url": "https://images.pexels.com/photos/32085609/pexels-photo-32085609.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2\nhttps://qcloud.dpfile.com/pc/cpE9AzJkwk9lO3FuRBcEV26guJTVdnU2CnSG-54cpZjQ1axuoNr4DU--hTRFYgr-Y0q73sB2DyQcgmKUxZFQtw.jpg\nhttp://cust-comfyui-upload-use.oss-cn-hangzhou.aliyuncs.com/uploads/1747712552301-001.png", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "Load Images From URL ♾️Mixlab"}}, "54": {"inputs": {"filename_prefix": "MattingBgFile", "images": ["63", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "63": {"inputs": {"rem_mode": "RMBG-2.0", "image_output": "<PERSON>de", "save_prefix": "ComfyUI", "torchscript_jit": false, "add_background": "none", "refine_foreground": false, "images": ["53", 0]}, "class_type": "easy imageRemBg", "_meta": {"title": "Image Remove Bg"}}}