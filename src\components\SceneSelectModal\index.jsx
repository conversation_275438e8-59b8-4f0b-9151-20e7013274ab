import React, { useState, useEffect, useMemo, memo, Suspense, useRef } from 'react';
import { MdClose, MdZoomOutMap } from 'react-icons/md';
import BaseModal from '../common/BaseModal';
import ImagePreviewModal from '../common/ImagePreviewModal';
import { fetchAllSceneImages } from '../../config/scenes/sceneCategories';
import { SCENE_CATEGORIES } from '../../config/scenes/sceneCategories';
import './index.css';
import '../../styles/close-buttons.css';

import { message } from 'antd';
import { UPLOAD_CONFIG } from '../../config/uploads/upload';

// 骨架屏组件
const SceneSkeleton = () => (
  <div className="card-skeleton">
    {Array(12).fill(null).map((_, index) => (
      <div key={index} className="card-skeleton-item">
        <div className="card-skeleton-image"></div>
        <div className="card-skeleton-caption">
          <div className="card-skeleton-text"></div>
          <div className="card-skeleton-button"></div>
        </div>
      </div>
    ))}
  </div>
);

// 抽离场景列表为独立组件并使用 memo 包装
const SceneGrid = memo(({ scenes, selectedSceneId, onSceneSelect, onPreview, onCustomUpload, uploadedScene, onCustomDelete }) => (
  <div className="scenes-grid">
    {/* 添加自定义上传区域 */}
    {onCustomUpload && (
      <div 
        className={`card-item upload-entry ${uploadedScene ? 'uploaded' : ''} ${uploadedScene && (selectedSceneId === uploadedScene.id || selectedSceneId === 'custom-upload' || selectedSceneId === 'custom-' + uploadedScene.id || (!selectedSceneId && uploadedScene)) ? 'selected' : ''}`}
        onClick={() => uploadedScene && onSceneSelect(uploadedScene)}
      >
        <div className="upload-area-content">
          {uploadedScene ? (
            <>
              <div className="card-preview">
                <img 
                  src={uploadedScene.image}
                  alt="已上传场景"
                />
                <button 
                  className="delete-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    onCustomDelete?.();
                  }}
                  title="删除图片"
                >
                  <MdClose />
                </button>
              </div>
              <div className="card-caption">
                <div className="card-info">
                  <span className="card-name">已上传 场景参考图</span>
                </div>
                <button 
                  className="preview-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    onPreview('custom-upload');
                  }}
                  title="放大预览"
                >
                  <MdZoomOutMap />
                </button>
              </div>
            </>
          ) : (
            <>
              <input 
                type="file" 
                id="scene-upload" 
                className="file-input" 
                accept={UPLOAD_CONFIG.getAcceptTypes()}
                onChange={onCustomUpload}
                style={{ display: 'none' }}
              />
              <label htmlFor="scene-upload" className="upload-label">
                <div className="upload-icon">
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11 14V6H13V14H11Z" fill="currentColor"/>
                    <path d="M7 10L12 5L17 10H7Z" fill="currentColor"/>
                    <path d="M6 18V16H18V18H6Z" fill="currentColor"/>
                  </svg>
                </div>
                <div className="upload-text">
                  <span>上传参考图片</span>
                  <span className="upload-tip">提供合适的近景场景图</span>
                </div>
              </label>
            </>
          )}
        </div>
      </div>
    )}
    
    {scenes.map((scene) => (
      <div 
        key={scene.id} 
        className={`card-item ${selectedSceneId && (selectedSceneId === scene.id || selectedSceneId.toString() === scene.id.toString()) ? 'selected' : ''}`}
        onClick={() => onSceneSelect(scene)}
      >
        <div className="card-preview">
          <img 
            src={scene.thumbnail || scene.image} 
            alt={`场景 ${scene.id}`}
            loading="lazy"
          />
        </div>
        <div className="card-caption">
          <div className="card-info">
            <span className="card-number">#{scene.id}</span>
            <span className="card-name">{scene.name}</span>
          </div>
          <button 
            className="preview-button"
            onClick={(e) => {
              e.stopPropagation();
              onPreview(scene.id);
            }}
            title="放大预览"
          >
            <MdZoomOutMap />
          </button>
        </div>
      </div>
    ))}
  </div>
));

const SceneSelectModal = ({ 
  onClose, 
  onSelect, 
  selectedSceneId,
  savedSettings,
  onSettingsChange 
}) => {
  const [activeTab, setActiveTab] = useState('reference');
  const [previewImage, setPreviewImage] = useState(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewAlt, setPreviewAlt] = useState('场景预览');
  const [isLoading, setIsLoading] = useState(true);
  const [uploadedScene, setUploadedScene] = useState(null);
  
  // 新增：动态加载的场景数据状态
  const [dynamicScenes, setDynamicScenes] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // 过滤后的场景
  const filteredScenes = useMemo(() => {
    if (selectedCategory === 'all') return dynamicScenes;
    return dynamicScenes.filter(scene => scene.category === selectedCategory);
  }, [dynamicScenes, selectedCategory]);
  
  // 拖动相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const modalRef = useRef(null);
  const dragStartMouse = useRef({ x: 0, y: 0 });
  const dragStartTransform = useRef({ x: 0, y: 0 });
  
  // 从savedSettings中恢复提示词
  const [promptText, setPromptText] = useState(savedSettings?.description || '');
  const [negativePromptText, setNegativePromptText] = useState(savedSettings?.negativeDescription || '');

  // 新增：动态加载场景数据
  useEffect(() => {
    const loadDynamicScenes = async () => {
      setIsLoading(true);
      try {
        const scenes = await fetchAllSceneImages();
        setDynamicScenes(scenes);
      } catch (error) {
        console.error('加载场景数据失败:', error);
        message.error('加载场景数据失败');
      } finally {
        setIsLoading(false);
      }
    };

    loadDynamicScenes();
  }, []);

  // 拖动事件处理
  const handleMouseDown = (e) => {
    if (e.target.closest('.modal-header') || e.target.closest('.modal-tabs')) {
      setIsDragging(true);
      // 记录拖动起始点和当前变换位置
      dragStartMouse.current = { x: e.clientX, y: e.clientY };
      dragStartTransform.current = { ...position };
      e.preventDefault();
    }
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      const deltaX = e.clientX - dragStartMouse.current.x;
      const deltaY = e.clientY - dragStartMouse.current.y;
      const newX = dragStartTransform.current.x + deltaX;
      const newY = dragStartTransform.current.y + deltaY;
      setPosition({ x: newX, y: newY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 绑定全局鼠标事件
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging]);

  // 修复弹窗关闭问题 - 添加实际生效的关闭函数
  const handleModalClose = () => {
    // 确保onClose是函数
    if (typeof onClose === 'function') {
      onClose();
    }
    
    // 问题原因说明：
    // 之前的问题是当在确认设置按钮的点击处理程序中调用onClose后，
    // 由于异步渲染或事件冒泡/阻止等问题，弹窗关闭的信号没有正确传递到父组件。
    // 使用专门的handleModalClose函数可以确保所有调用点都使用相同的方式关闭弹窗，
    // 减少因组件重新渲染或事件处理顺序问题导致的关闭失效。
  };

  // 添加 ESC 键监听
  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === 'Escape' && previewImage) {
        setPreviewImage(null);
      }
    };

    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [previewImage]);

  // 检查savedSettings是否包含自定义上传的场景
  useEffect(() => {
    if (savedSettings && 
        ((savedSettings.type === 'custom' && savedSettings.image && savedSettings.image !== '/images/icons/scene-custom.png') ||
         (savedSettings.type === 'reference' && savedSettings.image && 
          // 检查是否为自定义上传的图片 - 可能是从URL或者是自定义ID
          (savedSettings.id && savedSettings.id.toString().startsWith('custom-')) || 
          !dynamicScenes.some(s => s.id === savedSettings.id)))) {
      // 如果是已上传的自定义场景，恢复状态
      const customScene = {
        id: savedSettings.id || ('custom-' + Date.now()),
        name: '场景参考图',
        type: 'reference',
        description: savedSettings.description || '用户上传的场景参考图',
        image: savedSettings.image,
        file: savedSettings.file
      };
      setUploadedScene(customScene);
      
      // 如果没有选中其他场景，则自动选中上传的场景
      if (!selectedSceneId) {
        onSelect?.(customScene);
      }
    }
  }, [savedSettings, selectedSceneId, onSelect, dynamicScenes]);

  // 使用 useMemo 缓存预览图片的获取函数
  const handlePreview = useMemo(() => (sceneId) => {
    // 处理自定义上传的图片
    if (sceneId === 'custom-upload' && uploadedScene) {
      setPreviewImage(uploadedScene.image);
      setPreviewAlt(uploadedScene.name || '场景预览');
      setPreviewVisible(true);
      return;
    }
    
    // 处理动态加载的场景 - 预览时使用大图
    const scene = dynamicScenes.find(s => s.id === sceneId);
    if (scene) {
      // 预览时优先使用大图，如果没有则使用缩略图
      setPreviewImage(scene.image);
      setPreviewAlt(scene.name || '场景预览');
      setPreviewVisible(true);
    }
  }, [dynamicScenes, uploadedScene]);

  // 使用 useMemo 缓存场景选择函数
  const handleSceneSelect = useMemo(() => async (scene) => {
    // 确保scene对象存在
    if (scene) {
      // 如果是动态加载的场景，需要获取图片文件
      if (!scene.file && !scene.id?.toString().startsWith('custom-')) {
        try {
          // 获取大图文件用于任务生成
          const response = await fetch(scene.image);
          const blob = await response.blob();
          const file = new File([blob], `scene-${scene.id}.jpg`, { type: 'image/jpeg' });
          
          // 添加文件信息到场景对象
          scene = { 
            ...scene, 
            file,
            fileUrl: scene.image
          };
        } catch (error) {
          console.error('获取场景图片文件失败:', error);
          message.error('获取场景图片文件失败');
        }
      }

      // 记录选择，这里确保更新状态
      onSelect?.(scene);

      // 如果是上传的场景，确保它的状态被正确保存
      if (scene.id && scene.id.toString().startsWith('custom-')) {
        onSettingsChange?.(scene);
      }
    }
  }, [onSelect, onSettingsChange]);

  const handlePromptChange = (e) => {
    setPromptText(e.target.value);
  };

  const handleNegativePromptChange = (e) => {
    setNegativePromptText(e.target.value);
  };

  const handleClear = () => {
    setPromptText('');
    setNegativePromptText('');
    setUploadedScene(null);
    onSettingsChange?.(null);
    onSelect?.(null);
  };

  const handleSaveCustomScene = () => {
    if (promptText.trim()) {
      const settings = {
        type: 'custom',
        name: '自定义场景',
        description: promptText,
        negativeDescription: negativePromptText.trim(),
        image: '/images/icons/scene-custom.png'
      };
      onSettingsChange?.(settings);
      onSelect?.(settings);
    }
  };

  // 处理自定义场景图片上传
  const handleCustomSceneUpload = (e) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      // 验证文件类型和大小
      if (!UPLOAD_CONFIG.isValidFileType(file.name)) {
        message.error('不支持的文件类型');
        return;
      }
      
      if (!UPLOAD_CONFIG.isValidFileSize(file.size)) {
        message.warning(`文件需${UPLOAD_CONFIG.maxSize}MB以内`);
        return;
      }
      
      // 创建文件URL
      const fileUrl = URL.createObjectURL(file);
      
      // 创建自定义场景对象 - 确保包含file属性
      const customId = 'custom-' + Date.now();
      const customScene = {
        id: customId,
        name: '场景参考图',
        type: 'background',  // 业务类型：背景图片
        source: 'upload',    // 来源：用户上传
        description: '用户上传的场景参考图',
        image: fileUrl,      // 用于显示的URL
        file: file,          // 原始文件对象
        fileUrl: fileUrl     // 文件URL
      };
      
      // 设置上传的场景为当前状态
      setUploadedScene(customScene);
      
      // 选择这个自定义场景 - 传递包含file的完整场景对象
      onSelect?.(customScene);
      
      // 同时更新父组件中的设置
      onSettingsChange?.(customScene);
      
      // 显示上传成功消息
      message.success('场景图片上传成功');
    }
  };

  const tabs = [
    { key: 'reference', label: '参考场景' },
    // 暂时隐藏 "预设场景" 和 "自定义场景" 标签页
    // { key: 'preset', label: '预设场景' },
    // { key: 'custom', label: '自定义场景' }
  ];

  const footer = (
    <>
      <button 
        className="clear-btn"
        onClick={handleClear}
      >
        清空内容
      </button>
      <button 
        className="save-settings-btn"
        onClick={() => {
          let handled = false;
          
          if (activeTab === 'reference') {
            // 检查是否有上传的场景参考图并且被选中
            if (uploadedScene && (
                !selectedSceneId || 
                selectedSceneId === uploadedScene.id || 
                selectedSceneId === 'custom-upload' || 
                selectedSceneId === 'custom-' + uploadedScene.id.replace('custom-', '')
              )) {
              // 如果有上传图片且被选中，使用上传的图片
              handleSceneSelect(uploadedScene);
              handled = true;
            } else if (selectedSceneId) {
              // 如果选择了动态加载的场景
              const selectedScene = dynamicScenes.find(scene => scene.id === selectedSceneId);
              if (selectedScene) {
                handleSceneSelect(selectedScene);
                handled = true;
              }
            }
          } else {
            handleSaveCustomScene();
            handled = true;
          }
          
          // 使用handleModalClose确保正确关闭弹窗
          handleModalClose();
        }}
        disabled={
          ((activeTab === 'reference') && !selectedSceneId && !uploadedScene) ||
          (activeTab === 'custom' && !promptText.trim())
        }
      >
        确认设置
      </button>
    </>
  );

  return (
    <>
      <div className="scene-select-modal-wrapper">
        <BaseModal
          className={`scene-select-modal ${isDragging ? 'dragging' : ''}`}
          onClose={handleModalClose}
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          footer={footer}
          size="large"
          dragRef={modalRef}
          dragStyle={{
            transform: `translate(${position.x}px, ${position.y}px)`,
            transition: isDragging ? 'none' : 'transform 0.2s ease'
          }}
          onDragMouseDown={handleMouseDown}
        >
          {activeTab === 'reference' ? (
            <div className="reference-scenes">
              {/* 新增：类型筛选控件 */}
              <div className="scene-type-filter">
                <div
                  className={`filter-option${selectedCategory === 'all' ? ' active' : ''}`}
                  onClick={() => setSelectedCategory('all')}
                >全部</div>
                {SCENE_CATEGORIES.map(cat => (
                  <div
                    key={cat.key}
                    className={`filter-option${selectedCategory === cat.key ? ' active' : ''}`}
                    onClick={() => setSelectedCategory(cat.key)}
                  >{cat.name}</div>
                ))}
              </div>
              {isLoading ? (
                <SceneSkeleton />
              ) : (
                <SceneGrid 
                  scenes={filteredScenes}
                  selectedSceneId={selectedSceneId}
                  onSceneSelect={handleSceneSelect}
                  onPreview={handlePreview}
                  onCustomUpload={handleCustomSceneUpload}
                  uploadedScene={uploadedScene}
                  onCustomDelete={() => {
                    setUploadedScene(null);
                    onSelect?.(null);
                    onSettingsChange?.(null);
                  }}
                />
              )}
            </div>
          ) : (
            <div className="custom-scene">
              <div className="filter-section">
                <h3 className="section-title">填写场景描述提示词</h3>
                <div className="prompt-input">
                  <label>正面提示词</label>
                  <textarea
                    placeholder="描述您期望的场景效果，例如：海滩度假、阳光明媚、自然光线..."
                    rows={4}
                    className="prompt-textarea"
                    value={promptText}
                    onChange={handlePromptChange}
                  />
                </div>
                <div className="prompt-input">
                  <label>负面提示词（可选）</label>
                  <textarea
                    placeholder="描述您不希望出现的元素，例如：阴暗环境、杂乱背景、模糊画面..."
                    rows={4}
                    className="prompt-textarea"
                    value={negativePromptText}
                    onChange={handleNegativePromptChange}
                  />
                </div>
              </div>
            </div>
          )}
        </BaseModal>
      </div>
      {/* 修正z-index，确保预览弹窗在最上层 */}
      <ImagePreviewModal
        visible={previewVisible}
        imageUrl={previewImage}
        onClose={() => setPreviewVisible(false)}
        alt={previewAlt}
        showHint={true}
        maxScale={4}
        minScale={0.5}
      />
    </>
  );
};

export default SceneSelectModal; 