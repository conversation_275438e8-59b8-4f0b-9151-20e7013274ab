import { Modal } from 'antd';

/**
 * 显示统一风格的确认弹窗
 * @param {Object} options 配置选项
 * @param {string} options.title 弹窗标题
 * @param {string|React.ReactNode} options.content 弹窗内容
 * @param {Function} options.onOk 确认按钮回调函数
 * @param {Function} options.onCancel 取消按钮回调函数
 * @param {boolean} options.isDanger 是否是危险操作，默认为true
 * @param {string} options.okText 确认按钮文本，默认为"确认"
 * @param {string} options.cancelText 取消按钮文本，默认为"取消"
 * @param {Object} options.okButtonProps 确认按钮的属性
 * @param {boolean} options.autoEnableOkButton 是否在6秒后自动启用确认按钮
 */
export const showConfirmModal = ({
  title = '确认操作',
  content,
  onOk,
  onCancel,
  isDanger = true,
  okText = '确认',
  cancelText = '取消',
  okButtonProps = {},
  autoEnableOkButton = false
}) => {
  // 创建一个Modal实例
  const modal = Modal.confirm({
    title,
    content,
    okText,
    cancelText,
    onOk,
    onCancel,
    okButtonProps: {
      danger: isDanger,
      // 使用主题色
      style: isDanger ? undefined : { backgroundColor: 'var(--primary-color)', borderColor: 'var(--primary-color)', color: '#fff' },
      ...okButtonProps
    },
    // 减少动画问题
    animation: false,
    transitionName: '',
    maskTransitionName: '',
    // 自定义class，可以在未来通过CSS进一步调整样式
    className: 'global-confirm-modal'
  });
  
  // 如果需要自动启用确认按钮
  if (autoEnableOkButton && okButtonProps.disabled) {
    // 6秒后启用确认按钮
    setTimeout(() => {
      // 更新对话框，启用确认按钮
      modal.update({
        okButtonProps: {
          ...okButtonProps,
          disabled: false
        }
      });
    }, 6000);
  }
  
  return modal;
};

/**
 * 显示删除确认弹窗
 * @param {Object} options 配置选项
 * @param {string} options.title 弹窗标题，默认为"确认删除"
 * @param {string|React.ReactNode} options.content 弹窗内容
 * @param {Function} options.onOk 确认按钮回调函数
 * @param {Object} options.okButtonProps 确认按钮属性
 * @param {boolean} options.autoEnableOkButton 是否在5秒后自动启用确认按钮
 */
export const showDeleteConfirmModal = ({
  title = '确认删除',
  content = '确定要删除吗？删除后将无法恢复。',
  onOk,
  okButtonProps,
  autoEnableOkButton
}) => {
  return showConfirmModal({
    title,
    content,
    onOk,
    isDanger: true,
    okText: '确认删除',
    cancelText: '取消',
    okButtonProps,
    autoEnableOkButton
  });
}; 