const Announcement = require('./announcement.model');
const { createError } = require('../../../utils/error');
const Plan = require("../subscribe/plan.model");

/**
 * 获取公告列表（管理员）
 */
exports.getAnnouncements = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    
    // 构建查询条件
    const query = {};
    if (status) {
      query.status = status;
    }
    
    // 获取总数
    const total = await Announcement.countDocuments(query);
    
    // 获取分页数据
    const announcements = await Announcement.find(query)
      .sort({ priority: -1, createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(Number(limit))
      .populate('createdBy', 'username')
      .populate('updatedBy', 'username');
    
    res.json({
      success: true,
      data: announcements,
      pagination: {
        total,
        page: Number(page),
        limit: Number(limit)
      }
    });
  } catch (error) {
    next(createError(500, '获取公告列表失败', error));
  }
};

/**
 * 创建公告
 */
exports.createAnnouncement = async (req, res, next) => {
  try {
    const { title, content, type, status, startTime, endTime, priority, showInHome, images, showMask, showTitle, showSubtitle } = req.body;
    const announcement = new Announcement({
      title,
      content,
      type,
      status: status ? 'active' : 'inactive',
      startTime,
      endTime,
      priority: priority || 0,
      showInHome: showInHome || false,
      images: images || [],
      showMask: typeof showMask === 'boolean' ? showMask : true,
      showTitle: typeof showTitle === 'boolean' ? showTitle : true,
      showSubtitle: typeof showSubtitle === 'boolean' ? showSubtitle : true,
      createdBy: req.user._id
    });
    await announcement.save();
    res.status(201).json({
      success: true,
      message: '公告创建成功',
      data: announcement
    });
  } catch (error) {
    next(createError(500, '创建公告失败', error));
  }
};

/**
 * 更新公告
 */
exports.updateAnnouncement = async (req, res, next) => {
  try {
    const { title, content, type, status, startTime, endTime, priority, showInHome, images, showMask, showTitle, showSubtitle } = req.body;
    const updateData = {
      title,
      content,
      type,
      status: status ? 'active' : 'inactive',
      startTime,
      endTime,
      priority: priority || 0,
      showInHome: showInHome || false,
      images: images || [],
      showMask: typeof showMask === 'boolean' ? showMask : true,
      showTitle: typeof showTitle === 'boolean' ? showTitle : true,
      showSubtitle: typeof showSubtitle === 'boolean' ? showSubtitle : true
    };
    const announcement = await Announcement.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    );
    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }
    res.json({
      success: true,
      message: '公告更新成功',
      data: announcement
    });
  } catch (error) {
    next(createError(500, '更新公告失败', error));
  }
};

/**
 * 删除公告
 */
exports.deleteAnnouncement = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const announcement = await Announcement.findById(id);
    if (!announcement) {
      throw createError(404, '公告不存在');
    }
    await Announcement.findByIdAndDelete(req.params.id);
    res.json({
      success: true,
      message: '公告删除成功'
    });
  } catch (error) {
    next(createError(error.status || 500, '删除公告失败', error));
  }
};

/**
 * 获取当前有效公告（用户）
 */
exports.getActiveAnnouncements = async (req, res, next) => {
  try {
    const now = new Date();
    
    // 查询条件：
    // 1. 状态为启用
    // 2. 在有效期内
    // 3. 允许在首页显示
    const announcements = await Announcement.find({
      status: 'active'
    })
    .sort({ priority: -1, createdAt: -1 })
    .limit(5); // 限制返回最新的5条
    
    res.json({
      success: true,
      data: announcements
    });
  } catch (error) {
    next(createError(500, '获取公告失败', error));
  }
}; 