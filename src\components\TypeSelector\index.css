.type-selector {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  min-height: 88px;
  display: flex;
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  position: relative;
}

.type-selector-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
}

.type-selector-label {
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
}

.type-selector-label span {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

.type-selector-main {
  flex: 1;
  padding: 12px 16px;
  padding-right: 55px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.type-selector-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.7;
  white-space: normal;
  margin-top: 6px;
}

.type-selector-switches {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 6px 24px;
  max-width: calc(100% - 20px);
}

.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px 0;
  min-width: 120px;
}

.switch-label {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin-right: 8px;
  font-weight: 400;
}

/* 旧的样式，保留以备兼容性 */
.type-selector-buttons {
  display: none;
}

.fabric-button {
  display: none;
}

/* 响应式处理 - 小屏幕时调整布局 */
@media (max-width: 768px) {
  .type-selector-main {
    padding-right: 16px; /* 减少右侧内边距 */
  }
  
  .type-selector-tip-button {
    top: 4px;
    right: 8px;
    min-width: 50px; /* 稍微缩小按钮 */
    height: 24px;
    font-size: 11px;
  }
  
  .type-selector-tip-button .tip-text {
    font-size: 11px;
  }
  
  .type-selector-tip-button svg {
    width: 12px;
    height: 12px;
  }
}

/* 超小屏幕时隐藏提示按钮 */
@media (max-width: 480px) {
  .type-selector-main {
    padding-right: 16px; /* 恢复正常的右侧内边距 */
  }
} 