#!/usr/bin/env node

/**
 * 工作流管理主脚本
 * 
 * 使用方法:
 * node server/src/scripts/workflowManager.js <command> [options]
 * 
 * 可用命令:
 * - insert: 插入工作流（完整版）
 * - quick: 快速插入工作流
 * - update: 更新工作流信息
 * - validate: 验证工作流数据
 * - help: 显示帮助信息
 */

const { spawn } = require('child_process');
const path = require('path');

class WorkflowManager {
  constructor() {
    this.scriptsDir = __dirname;
    this.commands = {
      insert: {
        script: 'insertWorkflowsFromJson.js',
        description: '完整的工作流插入脚本，支持详细配置',
        options: [
          '--clear: 清空现有数据重新插入',
          '--dry-run: 仅预览，不实际插入',
          '--user-id <id>: 指定创建者用户ID'
        ]
      },
      quick: {
        script: 'quickInsertWorkflows.js',
        description: '快速插入工作流，适合日常使用',
        options: []
      },
      update: {
        script: 'updateWorkflowsInfo.js',
        description: '批量更新工作流信息',
        options: [
          '--dry-run: 仅预览，不实际更新',
          '--field=<name>: 仅更新指定字段 (name|description|tags|priority)'
        ]
      },
      validate: {
        script: 'validateWorkflows.js',
        description: '验证工作流数据完整性',
        options: []
      }
    };
  }

  // 显示帮助信息
  showHelp() {
    console.log('🔧 工作流管理工具\n');
    console.log('使用方法:');
    console.log('  node server/src/scripts/workflowManager.js <command> [options]\n');
    
    console.log('可用命令:');
    Object.entries(this.commands).forEach(([cmd, info]) => {
      console.log(`\n  ${cmd.padEnd(10)} ${info.description}`);
      if (info.options.length > 0) {
        console.log('             选项:');
        info.options.forEach(option => {
          console.log(`               ${option}`);
        });
      }
    });

    console.log('\n示例:');
    console.log('  node server/src/scripts/workflowManager.js quick');
    console.log('  node server/src/scripts/workflowManager.js insert --dry-run');
    console.log('  node server/src/scripts/workflowManager.js update --field=priority');
    console.log('  node server/src/scripts/workflowManager.js validate');
    
    console.log('\n常用工作流程:');
    console.log('  1. 首次设置: workflowManager.js quick');
    console.log('  2. 验证数据: workflowManager.js validate');
    console.log('  3. 更新信息: workflowManager.js update --dry-run');
    console.log('  4. 重新初始化: workflowManager.js insert --clear');
  }

  // 显示状态信息
  async showStatus() {
    console.log('📊 工作流系统状态\n');
    
    try {
      // 运行验证脚本获取状态
      await this.runScript('validateWorkflows.js', []);
    } catch (error) {
      console.error('获取状态失败:', error.message);
    }
  }

  // 运行指定脚本
  runScript(scriptName, args = []) {
    return new Promise((resolve, reject) => {
      const scriptPath = path.join(this.scriptsDir, scriptName);
      
      console.log(`🚀 运行脚本: ${scriptName}`);
      if (args.length > 0) {
        console.log(`📝 参数: ${args.join(' ')}`);
      }
      console.log('─'.repeat(50));

      const child = spawn('node', [scriptPath, ...args], {
        stdio: 'inherit',
        cwd: process.cwd()
      });

      child.on('close', (code) => {
        console.log('─'.repeat(50));
        if (code === 0) {
          console.log(`✅ 脚本执行完成: ${scriptName}`);
          resolve();
        } else {
          console.log(`❌ 脚本执行失败: ${scriptName} (退出码: ${code})`);
          reject(new Error(`脚本执行失败，退出码: ${code}`));
        }
      });

      child.on('error', (error) => {
        console.error(`💥 脚本启动失败: ${error.message}`);
        reject(error);
      });
    });
  }

  // 交互式菜单
  async interactiveMenu() {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const question = (prompt) => {
      return new Promise((resolve) => {
        rl.question(prompt, resolve);
      });
    };

    try {
      console.log('🎯 工作流管理交互式菜单\n');
      console.log('请选择要执行的操作:');
      console.log('1. 快速插入工作流');
      console.log('2. 验证工作流数据');
      console.log('3. 更新工作流信息');
      console.log('4. 完整插入工作流');
      console.log('5. 查看系统状态');
      console.log('0. 退出');

      const choice = await question('\n请输入选项 (0-5): ');

      switch (choice) {
        case '1':
          await this.runScript('quickInsertWorkflows.js');
          break;
        case '2':
          await this.runScript('validateWorkflows.js');
          break;
        case '3':
          const updateMode = await question('选择更新模式 (1=预览, 2=执行): ');
          const args = updateMode === '1' ? ['--dry-run'] : [];
          await this.runScript('updateWorkflowsInfo.js', args);
          break;
        case '4':
          const insertMode = await question('选择插入模式 (1=预览, 2=执行, 3=清空重建): ');
          let insertArgs = [];
          if (insertMode === '1') insertArgs = ['--dry-run'];
          else if (insertMode === '3') insertArgs = ['--clear'];
          await this.runScript('insertWorkflowsFromJson.js', insertArgs);
          break;
        case '5':
          await this.showStatus();
          break;
        case '0':
          console.log('👋 再见！');
          break;
        default:
          console.log('❌ 无效选项');
      }
    } catch (error) {
      console.error('交互式菜单执行失败:', error.message);
    } finally {
      rl.close();
    }
  }

  // 主执行方法
  async run() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
      // 没有参数，显示交互式菜单
      await this.interactiveMenu();
      return;
    }

    const command = args[0];
    const options = args.slice(1);

    switch (command) {
      case 'help':
      case '--help':
      case '-h':
        this.showHelp();
        break;

      case 'status':
        await this.showStatus();
        break;

      case 'insert':
        await this.runScript('insertWorkflowsFromJson.js', options);
        break;

      case 'quick':
        await this.runScript('quickInsertWorkflows.js', options);
        break;

      case 'update':
        await this.runScript('updateWorkflowsInfo.js', options);
        break;

      case 'validate':
        await this.runScript('validateWorkflows.js', options);
        break;

      default:
        console.error(`❌ 未知命令: ${command}`);
        console.log('使用 "help" 查看可用命令');
        process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const manager = new WorkflowManager();
  manager.run().catch(error => {
    console.error('💥 执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = WorkflowManager;
