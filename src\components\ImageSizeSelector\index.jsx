import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import './index.css';
import '../../../src/styles/buttons.css'; // 导入按钮样式，包括开关组件
import { MdLock, MdLockOpen, MdExpandLess, MdExpandMore } from 'react-icons/md';

const ImageSizeSelector = ({
  onUseDefaultChange,
  onWidthChange,
  onHeightChange,
  defaultUseDefault = true,
  defaultWidth = 1024,
  defaultHeight = 1536,
  pageType = 'background',
  imageData = {},
}) => {
  // 定义真正的内部默认值，不受父组件状态影响
  const INTERNAL_DEFAULT_WIDTH = 1024;
  const INTERNAL_DEFAULT_HEIGHT = 1536;
  
  const [useDefault, setUseDefault] = useState(defaultUseDefault);
  const [width, setWidth] = useState(defaultWidth);
  const [height, setHeight] = useState(defaultHeight);
  const [aspectRatio, setAspectRatio] = useState(defaultWidth / defaultHeight);
  const [lockAspectRatio, setLockAspectRatio] = useState(true);
  const [activeInput, setActiveInput] = useState(null);
  
  // 组件初始化时，确保有默认设置
  useEffect(() => {
    if (defaultUseDefault !== useDefault) {
      setUseDefault(defaultUseDefault);
    }
    // 只有在使用默认尺寸时，才同步父组件的默认值变化
    if (useDefault && defaultWidth !== width) {
      setWidth(defaultWidth);
    }
    if (useDefault && defaultHeight !== height) {
      setHeight(defaultHeight);
    }
    // 只有在使用默认尺寸时，才更新宽高比
    // 避免在用户操作过程中重置宽高比
    // if (useDefault && defaultWidth && defaultHeight) {
    //   setAspectRatio(defaultWidth / defaultHeight);
    // }
  }, [defaultUseDefault, defaultWidth, defaultHeight, useDefault]);

  // 当 defaultUseDefault 改变时，使用父组件传递的值而不是强制重置
  useEffect(() => {
    setUseDefault(defaultUseDefault);
    
    // 使用父组件传递的尺寸值，而不是强制重置
    if (defaultWidth && defaultHeight) {
      setWidth(defaultWidth);
      setHeight(defaultHeight);
      // 只在初始化时设置宽高比，避免在用户操作过程中重置
      // setAspectRatio(defaultWidth / defaultHeight);
    } else {
      // 只有在没有父组件传递值时，才使用内部默认值
      setWidth(INTERNAL_DEFAULT_WIDTH);
      setHeight(INTERNAL_DEFAULT_HEIGHT);
      // 只在初始化时设置宽高比，避免在用户操作过程中重置
      // setAspectRatio(INTERNAL_DEFAULT_WIDTH / INTERNAL_DEFAULT_HEIGHT);
    }
    
    // 只在初始化时设置锁定状态，避免在用户操作过程中重置
    // setLockAspectRatio(true);
  }, [defaultUseDefault, defaultWidth, defaultHeight]);

  // 根据页面类型和图片数据确定默认尺寸
  useEffect(() => {
    if (!imageData) return;
    
    // 只有当使用默认尺寸时，才根据上传图片更新尺寸
    if (!useDefault) return;
    
    let determinedWidth = width;
    let determinedHeight = height;
    let shouldUpdate = false;
    
    // 判断逻辑：根据页面类型不同，获取不同的默认尺寸
    switch (pageType) {
      case 'try-on': // 模特换装页面
        // 采用模特原图尺寸
        if (imageData.modelImage?.fileInfo) {
          determinedWidth = imageData.modelImage.fileInfo.width;
          determinedHeight = imageData.modelImage.fileInfo.height;
          shouldUpdate = true;
        }
        break;
      case 'fabric': // 换面料页面
        // 采用服装图片尺寸
        if (imageData.clothingImage?.fileInfo) {
          determinedWidth = imageData.clothingImage.fileInfo.width;
          determinedHeight = imageData.clothingImage.fileInfo.height;
          shouldUpdate = true;
        }
        break;
      case 'trending': // 爆款开发页面 - trending页面类型
        // 采用版型图片尺寸
        if (imageData.patternImage?.fileInfo) {
          determinedWidth = imageData.patternImage.fileInfo.width;
          determinedHeight = imageData.patternImage.fileInfo.height;
          shouldUpdate = true;
        }
        break;
      default: // 其他只有一个上传区域的页面
        // 服装复色页面、换背景页面、款式优化页面、时尚大片页面
        if (imageData.uploadedImages?.length > 0 && imageData.uploadedImages[0]?.fileInfo) {
          determinedWidth = imageData.uploadedImages[0].fileInfo.width;
          determinedHeight = imageData.uploadedImages[0].fileInfo.height;
          shouldUpdate = true;
        }
    }
    
    // 只有当图片尺寸确实更新时，才改变状态和比例
    if (shouldUpdate && determinedWidth && determinedHeight) {
      setWidth(determinedWidth);
      setHeight(determinedHeight);
      setAspectRatio(determinedWidth / determinedHeight);
      
      // 同时通知父组件
      if (onWidthChange) {
        onWidthChange(determinedWidth);
      }
      if (onHeightChange) {
        onHeightChange(determinedHeight);
      }
    }
  }, [imageData, pageType, useDefault]);

  const handleDefaultToggle = () => {
    const newValue = !useDefault;
    
    setUseDefault(newValue);
    
    // 无论切换到哪个状态，都重置所有内部状态到内部默认值
    setWidth(INTERNAL_DEFAULT_WIDTH);
    setHeight(INTERNAL_DEFAULT_HEIGHT);
    setAspectRatio(INTERNAL_DEFAULT_WIDTH / INTERNAL_DEFAULT_HEIGHT);
    setLockAspectRatio(true); // 重置锁定状态为默认的 true（仅在切换开关时）
    
    // 通知父组件重置后的值
    if (onUseDefaultChange) {
      onUseDefaultChange(newValue);
    }
    if (onWidthChange) {
      onWidthChange(INTERNAL_DEFAULT_WIDTH);
    }
    if (onHeightChange) {
      onHeightChange(INTERNAL_DEFAULT_HEIGHT);
    }
    
    // 当切换回默认尺寸时，根据图片更新尺寸值（使用 setTimeout 确保状态更新后执行）
    if (newValue === true) {
      setTimeout(() => {
        // 根据页面类型和图片数据确定默认尺寸
        if (imageData) {
          let determinedWidth = INTERNAL_DEFAULT_WIDTH;
          let determinedHeight = INTERNAL_DEFAULT_HEIGHT;
          let shouldUpdate = false;
          
          // 判断逻辑：根据页面类型不同，获取不同的默认尺寸
          switch (pageType) {
            case 'try-on': // 模特换装页面
              if (imageData.modelImage?.fileInfo) {
                determinedWidth = imageData.modelImage.fileInfo.width;
                determinedHeight = imageData.modelImage.fileInfo.height;
                shouldUpdate = true;
              }
              break;
            case 'fabric': // 换面料页面
              if (imageData.clothingImage?.fileInfo) {
                determinedWidth = imageData.clothingImage.fileInfo.width;
                determinedHeight = imageData.clothingImage.fileInfo.height;
                shouldUpdate = true;
              }
              break;
            case 'pattern': // 爆款开发页面 - pattern图片类型
            case 'trending': // 爆款开发页面 - trending页面类型
              if (imageData.patternImage?.fileInfo) {
                determinedWidth = imageData.patternImage.fileInfo.width;
                determinedHeight = imageData.patternImage.fileInfo.height;
                shouldUpdate = true;
              }
              break;
            default: // 其他只有一个上传区域的页面
              if (imageData.uploadedImages?.length > 0 && imageData.uploadedImages[0]?.fileInfo) {
                determinedWidth = imageData.uploadedImages[0].fileInfo.width;
                determinedHeight = imageData.uploadedImages[0].fileInfo.height;
                shouldUpdate = true;
              }
          }
          
          // 更新尺寸值
          if (shouldUpdate && determinedWidth && determinedHeight) {
            setWidth(determinedWidth);
            setHeight(determinedHeight);
            setAspectRatio(determinedWidth / determinedHeight);
            
            // 同时通知父组件
            if (onWidthChange) {
              onWidthChange(determinedWidth);
            }
            if (onHeightChange) {
              onHeightChange(determinedHeight);
            }
          }
        }
      }, 0);
    }
  };

  const handleWidthChange = (e) => {
    // 在输入过程中不限制范围，如果输入为空则显示空字符串而非0
    const inputValue = e.target.value;
    const newWidth = inputValue === '' ? '' : parseInt(inputValue);
    setWidth(newWidth);
    
    // 只有在锁定宽高比时，才进行高度联动计算
    if (lockAspectRatio && aspectRatio && typeof newWidth === 'number' && newWidth > 0) {
      // 添加宽高比合理性检查，避免极端值
      const safeAspectRatio = aspectRatio > 0.1 && aspectRatio < 10 ? aspectRatio : (INTERNAL_DEFAULT_WIDTH / INTERNAL_DEFAULT_HEIGHT);
      const calculatedHeight = Math.round(newWidth / safeAspectRatio);
      
      setHeight(calculatedHeight);
      
      // 宽高比锁定时，同时回调宽度和高度
      if (onWidthChange) {
        onWidthChange(newWidth);
      }
      if (onHeightChange) {
        onHeightChange(calculatedHeight);
      }
    } else {
      // 未锁定宽高比时，只回调宽度，不进行任何联动
      if (onWidthChange && typeof newWidth === 'number') {
        onWidthChange(newWidth);
      }
    }
  };
  
  const handleHeightChange = (e) => {
    // 在输入过程中不限制范围，如果输入为空则显示空字符串而非0
    const inputValue = e.target.value;
    const newHeight = inputValue === '' ? '' : parseInt(inputValue);
    setHeight(newHeight);
    
    // 只有在锁定宽高比时，才进行宽度联动计算
    if (lockAspectRatio && aspectRatio && typeof newHeight === 'number' && newHeight > 0) {
      // 添加宽高比合理性检查，避免极端值
      const safeAspectRatio = aspectRatio > 0.1 && aspectRatio < 10 ? aspectRatio : (INTERNAL_DEFAULT_WIDTH / INTERNAL_DEFAULT_HEIGHT);
      const calculatedWidth = Math.round(newHeight * safeAspectRatio);
      setWidth(calculatedWidth);
      
      // 宽高比锁定时，同时回调高度和宽度
      if (onHeightChange) {
        onHeightChange(newHeight);
      }
      if (onWidthChange) {
        onWidthChange(calculatedWidth);
      }
    } else {
      // 未锁定宽高比时，只回调高度，不进行任何联动
      if (onHeightChange && typeof newHeight === 'number') {
        onHeightChange(newHeight);
      }
    }
  };
  
  // 添加onBlur处理函数，在失去焦点时进行范围验证
  const handleWidthBlur = () => {
    // 如果为空，设置为默认值512
    if (width === '') {
      const defaultValue = 512;
      setWidth(defaultValue);
      
      if (onWidthChange) {
        onWidthChange(defaultValue);
      }
      
      // 只有在锁定宽高比时，才进行高度联动
      if (lockAspectRatio && aspectRatio) {
        const safeAspectRatio = aspectRatio > 0.1 && aspectRatio < 10 ? aspectRatio : (INTERNAL_DEFAULT_WIDTH / INTERNAL_DEFAULT_HEIGHT);
        const calculatedHeight = Math.round(defaultValue / safeAspectRatio);
        const validHeight = Math.max(512, Math.min(2048, calculatedHeight));
        setHeight(validHeight);
        
        // 宽高比锁定时，同时回调宽度和高度
        if (onWidthChange) {
          onWidthChange(defaultValue);
        }
        if (onHeightChange) {
          onHeightChange(validHeight);
        }
      } else {
        // 未锁定宽高比时，只回调宽度，不进行任何联动
        if (onWidthChange) {
          onWidthChange(defaultValue);
        }
      }
      return;
    }
    
    // 确保值在有效范围内
    const validWidth = Math.max(512, Math.min(2048, width));
    
    if (validWidth !== width) {
      setWidth(validWidth);
      
      // 只有在锁定宽高比时，才进行高度联动
      if (lockAspectRatio && aspectRatio) {
        const safeAspectRatio = aspectRatio > 0.1 && aspectRatio < 10 ? aspectRatio : (INTERNAL_DEFAULT_WIDTH / INTERNAL_DEFAULT_HEIGHT);
        const calculatedHeight = Math.round(validWidth / safeAspectRatio);
        const validHeight = Math.max(512, Math.min(2048, calculatedHeight));
        setHeight(validHeight);
        
        // 宽高比锁定时，同时回调宽度和高度
        if (onWidthChange) {
          onWidthChange(validWidth);
        }
        if (onHeightChange) {
          onHeightChange(validHeight);
        }
      } else {
        // 未锁定宽高比时，只回调宽度，不进行任何联动
        if (onWidthChange) {
          onWidthChange(validWidth);
        }
      }
    }
  };
  
  const handleHeightBlur = () => {
    // 如果为空，设置为默认值512
    if (height === '') {
      const defaultValue = 512;
      setHeight(defaultValue);
      
      if (onHeightChange) {
        onHeightChange(defaultValue);
      }
      
      // 只有在锁定宽高比时，才进行宽度联动
      if (lockAspectRatio && aspectRatio) {
        const safeAspectRatio = aspectRatio > 0.1 && aspectRatio < 10 ? aspectRatio : (INTERNAL_DEFAULT_WIDTH / INTERNAL_DEFAULT_HEIGHT);
        const calculatedWidth = Math.round(defaultValue * safeAspectRatio);
        const validWidth = Math.max(512, Math.min(2048, calculatedWidth));
        setWidth(validWidth);
        
        // 宽高比锁定时，同时回调高度和宽度
        if (onHeightChange) {
          onHeightChange(defaultValue);
        }
        if (onWidthChange) {
          onWidthChange(validWidth);
        }
      } else {
        // 未锁定宽高比时，只回调高度，不进行任何联动
        if (onHeightChange) {
          onHeightChange(defaultValue);
        }
      }
      return;
    }
    
    // 确保值在有效范围内
    const validHeight = Math.max(512, Math.min(2048, height));
    
    if (validHeight !== height) {
      setHeight(validHeight);
      
      // 只有在锁定宽高比时，才进行宽度联动
      if (lockAspectRatio && aspectRatio) {
        const safeAspectRatio = aspectRatio > 0.1 && aspectRatio < 10 ? aspectRatio : (INTERNAL_DEFAULT_WIDTH / INTERNAL_DEFAULT_HEIGHT);
        const calculatedWidth = Math.round(validHeight * safeAspectRatio);
        const validWidth = Math.max(512, Math.min(2048, calculatedWidth));
        setWidth(validWidth);
        
        // 宽高比锁定时，同时回调高度和宽度
        if (onHeightChange) {
          onHeightChange(validHeight);
        }
        if (onWidthChange) {
          onWidthChange(validWidth);
        }
      } else {
        // 未锁定宽高比时，只回调高度，不进行任何联动
        if (onHeightChange) {
          onHeightChange(validHeight);
        }
      }
    }
  };
  
  const toggleLockAspectRatio = () => {
    // 切换锁定状态
    setLockAspectRatio(!lockAspectRatio);
    
    // 如果从解锁变为锁定，则更新宽高比
    if (!lockAspectRatio) {
      // 确保宽高值都是有效的数字，避免产生无效的宽高比
      const validWidth = typeof width === 'number' && width > 0 ? width : INTERNAL_DEFAULT_WIDTH;
      const validHeight = typeof height === 'number' && height > 0 ? height : INTERNAL_DEFAULT_HEIGHT;
      const newAspectRatio = validWidth / validHeight;
      
      setAspectRatio(newAspectRatio);
    }
  };

  // 添加数值调节函数
  const adjustWidthValue = (delta) => {
    const newWidth = Math.max(512, Math.min(2048, width + delta));
    setWidth(newWidth);
    
    // 如果锁定宽高比，则更新高度
    if (lockAspectRatio && aspectRatio) {
      const safeAspectRatio = aspectRatio > 0.1 && aspectRatio < 10 ? aspectRatio : (INTERNAL_DEFAULT_WIDTH / INTERNAL_DEFAULT_HEIGHT);
      const calculatedHeight = Math.round(newWidth / safeAspectRatio);
      const validHeight = Math.max(512, Math.min(2048, calculatedHeight));
      setHeight(validHeight);
      
      // 宽高比锁定时，同时回调宽度和高度
      if (onWidthChange) {
        onWidthChange(newWidth);
      }
      if (onHeightChange) {
        onHeightChange(validHeight);
      }
    } else if (onWidthChange) {
      // 未锁定宽高比时，只回调宽度
      onWidthChange(newWidth);
    }
  };

  const adjustHeightValue = (delta) => {
    const newHeight = Math.max(512, Math.min(2048, height + delta));
    setHeight(newHeight);
    
    // 如果锁定宽高比，则更新宽度
    if (lockAspectRatio && aspectRatio) {
      const safeAspectRatio = aspectRatio > 0.1 && aspectRatio < 10 ? aspectRatio : (INTERNAL_DEFAULT_WIDTH / INTERNAL_DEFAULT_HEIGHT);
      const calculatedWidth = Math.round(newHeight * safeAspectRatio);
      const validWidth = Math.max(512, Math.min(2048, calculatedWidth));
      setWidth(validWidth);
      
      // 宽高比锁定时，同时回调高度和宽度
      if (onHeightChange) {
        onHeightChange(newHeight);
      }
      if (onWidthChange) {
        onWidthChange(validWidth);
      }
    } else if (onHeightChange) {
      // 未锁定宽高比时，只回调高度
      onHeightChange(newHeight);
    }
  };

  return (
    <div className="image-size-selector">
      <div className="selector-content">
        <div className="selector-label">
          <span>图片尺寸</span>
        </div>
        <div className="selector-area">
          <div className="component-text">
            <h3>
              {useDefault ? "默认尺寸" : "自定义尺寸"}
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={useDefault}
                  onChange={handleDefaultToggle}
                />
                <span className="toggle-track"></span>
              </label>
            </h3>
            <div className="component-content">
              {useDefault ? (
                <p>
                  使用默认图片尺寸1024×1536生成
                </p>
              ) : (
                <div className="size-input-container">
                  <div className="size-input-group">
                    <span className="size-label">宽</span>
                    <div 
                      className="size-input-wrapper"
                      onMouseEnter={() => setActiveInput('width')}
                      onMouseLeave={() => setActiveInput(null)}
                    >
                      <input 
                        type="number" 
                        className="size-input" 
                        value={width}
                        onChange={handleWidthChange}
                        onBlur={handleWidthBlur}
                      />
                      {activeInput === 'width' && (
                        <div className="number-controls">
                          <button 
                            className="number-control-btn" 
                            onClick={() => adjustWidthValue(1)}
                          >
                            <MdExpandLess />
                          </button>
                          <button 
                            className="number-control-btn" 
                            onClick={() => adjustWidthValue(-1)}
                          >
                            <MdExpandMore />
                          </button>
                        </div>
                      )}
                    </div>
                    <span className="size-unit">px</span>
                  </div>
                  
                  <button 
                    className="aspect-ratio-btn"
                    onClick={toggleLockAspectRatio}
                    title={lockAspectRatio ? "解锁长宽比" : "锁定长宽比"}
                  >
                    {lockAspectRatio ? <MdLock /> : <MdLockOpen />}
                  </button>
                  
                  <div className="size-input-group">
                    <span className="size-label">高</span>
                    <div 
                      className="size-input-wrapper"
                      onMouseEnter={() => setActiveInput('height')}
                      onMouseLeave={() => setActiveInput(null)}
                    >
                      <input 
                        type="number" 
                        className="size-input" 
                        value={height}
                        onChange={handleHeightChange}
                        onBlur={handleHeightBlur}
                      />
                      {activeInput === 'height' && (
                        <div className="number-controls">
                          <button 
                            className="number-control-btn" 
                            onClick={() => adjustHeightValue(1)}
                          >
                            <MdExpandLess />
                          </button>
                          <button 
                            className="number-control-btn" 
                            onClick={() => adjustHeightValue(-1)}
                          >
                            <MdExpandMore />
                          </button>
                        </div>
                      )}
                    </div>
                    <span className="size-unit">px</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

ImageSizeSelector.propTypes = {
  onUseDefaultChange: PropTypes.func,
  onWidthChange: PropTypes.func,
  onHeightChange: PropTypes.func,
  defaultUseDefault: PropTypes.bool,
  defaultWidth: PropTypes.number,
  defaultHeight: PropTypes.number,
  pageType: PropTypes.string,
  imageData: PropTypes.object, // 添加imageData属性的类型定义
};

export default ImageSizeSelector; 