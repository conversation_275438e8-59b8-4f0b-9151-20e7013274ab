/* 认证模块标签页样式 */

/* 标签页容器 */
.login-modal .tab-group, 
.reset-modal .tab-group,
.captcha-modal .tab-group {
  display: flex;
  gap: 0;
  height: 36px;
  margin-bottom: -1px;
  margin-left: 0 !important;
  margin-top: 0;
  width: 100%;
}

/* 标签按钮 */
.tab-btn {
  padding: 0 24px;
  border: 1px solid transparent;
  background: transparent;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  position: relative;
  transition: var(--transition-fast);
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  margin-right: 1px;
  user-select: none;
  white-space: nowrap;
  flex-shrink: 0;
}

.tab-btn:first-child {
  margin-left: 0 !important;
}

.tab-btn:last-child {
  margin-right: 4px;
}

.tab-btn:hover {
  background: transparent;
  color: var(--text-primary);
  position: relative;
}

.tab-btn:not(.active):hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(100% - 2px);
  height: 28px;
  background: var(--bg-hover);
  border-radius: var(--radius-md);
}

.tab-btn.active {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-color);
  border-bottom-color: var(--bg-primary);
  font-weight: 500;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--brand-primary);
  width: calc(100% - 48px);
  margin: 0 24px;
  transition: transform 0.2s ease;
}

.tab-btn:not(.active) {
  border-bottom: 1px solid var(--border-color);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .tab-group {
    width: 100%;
    margin-left: 0;
  }
  
  .tab-btn {
    flex: 1;
    font-size: 13px;
    padding: 0 6px;
  }
  
  .tab-btn.active::after {
    width: calc(100% - 12px);
    margin: 0 6px;
  }
} 