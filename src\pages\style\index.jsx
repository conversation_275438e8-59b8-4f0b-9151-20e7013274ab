import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getFeaturesByCategory } from '../../config/features';
import './index.css';

const StylePage = () => {
  const navigate = useNavigate();
  const [error, setError] = useState(null);

  const handleNavigate = (path) => {
    try {
      navigate(path);
    } catch (err) {
      setError({
        title: '导航错误',
        message: '无法跳转到指定页面，请稍后重试。'
      });
    }
  };

  if (error) {
    return (
      <div className="error-container">
        <h2>{error.title}</h2>
        <p>{error.message}</p>
        <button onClick={() => setError(null)}>返回</button>
      </div>
    );
  }

  const features = getFeaturesByCategory('款式设计');

  return (
    <div className="model-page">
      <div className="model-header">
        <h1>款式设计</h1>
        <p>使用AI技术辅助设计服装款式，激发创意灵感</p>
      </div>
      <div className="page-feature-grid">
        {features.map((feature) => (
          <div 
            key={feature.id} 
            className="page-feature-card"
            onClick={() => {
              const pathMap = {
                '爆款开发（融合+线稿）': '/style/trending',
                '爆款延伸': '/style/divergent',
                '款式优化': '/style/optimize',
                '灵感探索（创款+创意）': '/style/inspiration',
                '换面料': '/style/fabric',
                '生成线稿': '/style/drawing'
              };
              handleNavigate(pathMap[feature.name]);
            }}
          >
            <div className="feature-icon">
              <img src={feature.image} alt={feature.name} />
            </div>
            <h3>{feature.name}</h3>
            <p>{feature.description}</p>
          </div>
        ))}
      </div>
      <div className="feature-tip">
        点击上方卡片或侧边栏按钮，进入对应功能页面
      </div>
    </div>
  );
};

export default StylePage; 