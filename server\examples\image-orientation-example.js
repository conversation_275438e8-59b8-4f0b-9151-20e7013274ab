const imageUtils = require('../src/utils/imageUtils');
const path = require('path');

/**
 * 示例：处理上传的图片文件
 */
async function processUploadedImage(uploadedFilePath, originalFilename) {
  try {
    console.log(`开始处理上传的图片: ${originalFilename}`);
    
    // 检查是否为图片文件
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const fileExt = originalFilename.toLowerCase().substring(originalFilename.lastIndexOf('.'));
    
    if (!imageExtensions.includes(fileExt)) {
      console.log('非图片文件，跳过方向校正');
      return {
        processedPath: uploadedFilePath,
        fileInfo: {
          originalname: originalFilename,
          isImage: false
        }
      };
    }
    
    // 处理图片方向校正
    const result = await imageUtils.processImageWithMetadata(uploadedFilePath);
    
    // 构建文件信息
    const fileInfo = {
      originalname: originalFilename,
      isImage: true,
      originalOrientation: result.orientation,
      wasCorrected: result.wasCorrected,
      originalDimensions: {
        width: result.originalMetadata.width,
        height: result.originalMetadata.height
      },
      processedDimensions: {
        width: result.processedMetadata.width,
        height: result.processedMetadata.height
      },
      format: result.processedMetadata.format
    };
    
    if (result.wasCorrected) {
      console.log(`图片方向已校正: ${result.orientation} -> 1`);
      console.log(`尺寸变化: ${result.originalMetadata.width}x${result.originalMetadata.height} -> ${result.processedMetadata.width}x${result.processedMetadata.height}`);
    } else {
      console.log('图片方向正常，无需校正');
    }
    
    return {
      processedPath: result.processedPath,
      fileInfo: fileInfo
    };
    
  } catch (error) {
    console.error('图片处理失败:', error);
    // 返回原文件路径，不中断上传流程
    return {
      processedPath: uploadedFilePath,
      fileInfo: {
        originalname: originalFilename,
        isImage: true,
        error: error.message
      }
    };
  }
}

/**
 * 示例：批量处理多个上传文件
 */
async function processMultipleUploads(files) {
  const results = [];
  
  for (const file of files) {
    console.log(`\n处理文件: ${file.originalname}`);
    const result = await processUploadedImage(file.path, file.originalname);
    results.push({
      originalFile: file,
      processedResult: result
    });
  }
  
  return results;
}

/**
 * 示例：模拟上传处理流程
 */
async function simulateUploadProcess() {
  console.log('=== 图片方向校正功能演示 ===\n');
  
  // 模拟上传的文件信息
  const mockFiles = [
    {
      path: path.join(__dirname, 'uploads', 'sample1.jpg'),
      originalname: 'sample1.jpg',
      size: 1024000
    },
    {
      path: path.join(__dirname, 'uploads', 'sample2.png'),
      originalname: 'sample2.png',
      size: 2048000
    },
    {
      path: path.join(__dirname, 'uploads', 'document.pdf'),
      originalname: 'document.pdf',
      size: 512000
    }
  ];
  
  try {
    const results = await processMultipleUploads(mockFiles);
    
    console.log('\n=== 处理结果汇总 ===');
    results.forEach((result, index) => {
      const { originalFile, processedResult } = result;
      console.log(`\n文件 ${index + 1}: ${originalFile.originalname}`);
      console.log(`- 文件大小: ${originalFile.size} bytes`);
      console.log(`- 是否为图片: ${processedResult.fileInfo.isImage}`);
      
      if (processedResult.fileInfo.isImage) {
        if (processedResult.fileInfo.error) {
          console.log(`- 处理状态: 失败 (${processedResult.fileInfo.error})`);
        } else {
          console.log(`- 原始方向: ${processedResult.fileInfo.originalOrientation}`);
          console.log(`- 是否校正: ${processedResult.fileInfo.wasCorrected ? '是' : '否'}`);
          console.log(`- 原始尺寸: ${processedResult.fileInfo.originalDimensions.width}x${processedResult.fileInfo.originalDimensions.height}`);
          console.log(`- 处理后尺寸: ${processedResult.fileInfo.processedDimensions.width}x${processedResult.fileInfo.processedDimensions.height}`);
        }
      }
    });
    
  } catch (error) {
    console.error('演示失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  simulateUploadProcess();
}

module.exports = {
  processUploadedImage,
  processMultipleUploads,
  simulateUploadProcess
}; 