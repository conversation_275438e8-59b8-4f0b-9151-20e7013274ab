/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/buttons.css';
@import '../../styles/inputs.css';
@import '../../styles/tabs.css';
@import '../../styles/panels.css';
@import '../../styles/scrollbars.css';

/* 复用模特选择弹窗的基础样式 */
.advanced-custom-modal {
  composes: base-modal;
}

.advanced-custom-modal .model-select-modal-content {
  pointer-events: auto;
  width: 1200px;
  height: calc(100vh - 150px);
  min-height: 600px;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  margin-left: 290px;
  border: 1px solid var(--border-color);
}

.advanced-custom-modal .close-button {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  border: none;
  background: var(--bg-hover);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  z-index: 1;
}

.advanced-custom-modal .close-button:hover {
  background: var(--bg-active);
  color: var(--text-primary);
}

.advanced-custom-modal .modal-header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 20px 20px 0;
  margin: 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  background: var(--bg-primary);
}

.advanced-custom-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  margin-top: 0;
  padding-bottom: 80px;
  will-change: transform;
  -webkit-overflow-scrolling: touch;
}

/* 自定义描述区域样式 */
.custom-scene {
  padding: 0 var(--spacing-lg);
}

.filter-section {
  margin-top: var(--spacing-lg);
}

.section-title {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: 500;
}

[data-theme="dark"] .section-title {
  color: var(--text-primary);
}

.prompt-input {
  margin-bottom: var(--spacing-lg);
}

.prompt-input label {
  display: block;
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.prompt-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  resize: none;
  transition: var(--transition-normal);
}

[data-theme="dark"] .custom-scene .prompt-textarea {
  background: var(--bg-secondary);
  border-color: var(--border-light);
  color: var(--text-primary);
}

.custom-scene .prompt-textarea:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

[data-theme="dark"] .custom-scene .prompt-textarea:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

.custom-scene .prompt-textarea::placeholder {
  color: var(--text-tertiary);
}

[data-theme="dark"] .custom-scene .prompt-textarea::placeholder {
  color: var(--text-tertiary);
}

/* 使用公共样式类 */
.modal-footer,
.custom-scene-actions {
  composes: button-group from '../../styles/buttons.css';
}

/* 开发中提示样式 */
.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
  padding: var(--spacing-xl);
}

.coming-soon-icon {
  width: 120px;
  height: 120px;
  margin-bottom: var(--spacing-xl);
  opacity: 0.8;
}

.coming-soon h3 {
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.coming-soon p {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 