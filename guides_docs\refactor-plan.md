# 代码重构计划 - 服务层迁移

## 概述
当前项目正在进行服务层架构重构，将API和工具函数整合到更合理的服务层结构中。我们需要制定明确的计划来彻底完成这个重构过程。

## 当前状态
已完成：
- 创建了新的服务目录结构 `src/services/`
- 将核心功能迁移到新位置
  - `src/utils/errorUtils.js` → `src/services/error/utils.js`
  - `src/services/errorHandler.js` → `src/services/error/index.js`
  - `src/api/task.js` → `src/services/task/index.js`
  - `src/api/auth.js` → `src/services/auth/index.js`
- 为过渡期添加了桥接层保持兼容性

待完成：
- 更新所有引用旧路径的代码
- 删除桥接层文件

## 迁移计划

### 第1阶段：明确标记废弃文件
- [x] 为所有桥接层文件添加废弃警告注释
- [x] 明确指出新的引用路径

### 第2阶段：系统性更新引用（当前）
根据搜索结果，有以下文件需要更新：
1. 任务相关（`api/task.js`）：
   - `src/pages/基础页面/index.jsx`
   - `src/pages/tools/upscale/index.jsx`
   - `src/pages/tools/matting/index.jsx`
   - `src/pages/tools/extract/index.jsx`
   - `src/pages/tools/extend/index.jsx`
   - `src/pages/style/trending/index.jsx`
   - `src/pages/style/inspiration/index.jsx`
   - `src/pages/style/optimize/index.jsx`
   - `src/pages/model/try-on/index.jsx`
   - `src/pages/model/virtual/index.jsx`
   - `src/pages/model/recolor/index.jsx`
   - `src/pages/model/fabric/index.jsx`
   - `src/pages/model/fashion/index.jsx` (已更新)
   - `src/pages/model/background/index.jsx`

2. 错误处理（`services/errorHandler.js`）：
   - README.MD（需要更新文档）

按照以下步骤更新：
1. 更新每个文件的引用，修改路径
2. 测试确保功能正常
3. 记录已完成的更新

### 第3阶段：清理和验证
- [ ] 检查代码中是否还有遗漏的引用
- [ ] 运行完整的构建和测试
- [ ] 修复可能的问题

### 第4阶段：删除遗留文件
- [ ] 删除以下文件：
  - `src/utils/errorUtils.js`
  - `src/services/errorHandler.js`
  - `src/api/task.js`
  - `src/api/auth.js`

## 测试策略
每次更新后：
1. 启动应用，检查相关功能
2. 检查控制台有无错误
3. 验证核心功能如：任务创建、身份验证等

## 进度跟踪

| 文件 | 状态 | 更新日期 | 测试结果 |
|-----|-----|---------|---------|
| src/pages/model/fashion/index.jsx | 已完成 | 2025-03-29 | 通过 |
| ... | ... | ... | ... |

## 注意事项
- 更新引用时需注意保持相对路径正确
- 可能需要根据实际项目布局调整路径
- 优先处理最常用的组件和页面

## 完成标准
- 所有引用都使用新路径
- 旧文件已删除
- 应用测试通过，无运行时错误
- 文档已更新，反映新结构 