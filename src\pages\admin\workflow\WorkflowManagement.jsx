import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Tag,
  message,
  Popconfirm,
  Alert,
  Row,
  Col,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import api from '../../../api';
import './WorkflowManagement.css';

const { Option } = Select;
const { TextArea } = Input;

const WorkflowManagement = () => {
  // 基础状态
  const [loading, setLoading] = useState(false);
  
  // 工作流管理状态
  const [availableWorkflows, setAvailableWorkflows] = useState([]);
  const [workflowModalVisible, setWorkflowModalVisible] = useState(false);
  const [editingWorkflow, setEditingWorkflow] = useState(null);
  const [workflowForm] = Form.useForm();
  const [workflowCategories] = useState(['款式设计', '模特图', '工具', '其他']);

  // 强制关闭Modal的方法
  const forceCloseModal = () => {
    console.log('强制关闭工作流Modal');
    setWorkflowModalVisible(false);
    setEditingWorkflow(null);
    workflowForm.resetFields();
  };

  useEffect(() => {
    loadWorkflows();
  }, []);

  // 监听Modal状态变化，确保遮罩层正确处理
  useEffect(() => {
    if (!workflowModalVisible) {
      // 确保Modal关闭时清理所有相关状态
      const timer = setTimeout(() => {
        document.body.style.overflow = 'auto';
        // 移除可能残留的遮罩层
        const masks = document.querySelectorAll('.ant-modal-mask');
        masks.forEach(mask => {
          if (mask.parentNode) {
            mask.parentNode.removeChild(mask);
          }
        });
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [workflowModalVisible]);

  // 添加键盘事件监听器
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && workflowModalVisible) {
        console.log('ESC键关闭工作流Modal');
        forceCloseModal();
      }
    };

    if (workflowModalVisible) {
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [workflowModalVisible]);

  // 加载工作流列表
  const loadWorkflows = async () => {
    try {
      setLoading(true);
      const response = await api.get('/runninghub/admin/workflows');
      if (response && response.success) {
        setAvailableWorkflows(response.data || []);
      }
    } catch (error) {
      console.error('加载工作流列表失败:', error);
      message.error('加载工作流列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存工作流
  const saveWorkflow = async (values) => {
    try {
      setLoading(true);

      if (editingWorkflow) {
        // 更新工作流
        const response = await api.put(`/workflows/${editingWorkflow.id}`, values);
        if (response && response.success) {
          message.success('工作流更新成功');
          forceCloseModal();
          await loadWorkflows();
        } else {
          message.error(response?.message || '工作流更新失败');
        }
      } else {
        // 创建工作流
        const response = await api.post('/workflows', values);
        if (response && response.success) {
          message.success('工作流创建成功');
          forceCloseModal();
          await loadWorkflows();
        } else {
          message.error(response?.message || '工作流创建失败');
        }
      }
    } catch (error) {
      console.error('保存工作流失败:', error);
      message.error('保存工作流失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 删除工作流
  const deleteWorkflow = async (workflowId) => {
    try {
      setLoading(true);
      const response = await api.delete(`/workflows/${workflowId}`);
      if (response && response.success) {
        message.success('工作流删除成功');
        await loadWorkflows();
      } else {
        message.error(response?.message || '工作流删除失败');
      }
    } catch (error) {
      console.error('删除工作流失败:', error);
      message.error('删除工作流失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 批量更新工作流状态
  const batchUpdateWorkflowStatus = async (ids, enabled) => {
    try {
      setLoading(true);
      const response = await api.put('/workflows/batch/status', { ids, enabled });
      if (response && response.success) {
        message.success(response.message);
        await loadWorkflows();
      } else {
        message.error(response?.message || '批量更新失败');
      }
    } catch (error) {
      console.error('批量更新工作流状态失败:', error);
      message.error('批量更新工作流状态失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '工作流信息',
      key: 'info',
      render: (_, workflow) => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
            {workflow.name}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            ID: {workflow.id} | 版本: {workflow.version}
          </div>
          <div style={{ color: '#999', fontSize: '12px', marginTop: '4px' }}>
            {workflow.description}
          </div>
        </div>
      )
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => (
        <Tag color={
          category === '款式设计' ? 'blue' :
          category === '模特图' ? 'green' :
          category === '工具' ? 'orange' : 'default'
        }>
          {category}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 80,
      render: (enabled) => (
        <Tag color={enabled ? 'success' : 'default'}>
          {enabled ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '支持平台',
      dataIndex: 'supportedPlatforms',
      key: 'supportedPlatforms',
      width: 120,
      render: (platforms) => (
        <div>
          {platforms?.map(platform => (
            <Tag key={platform} size="small">
              {platform === 'comfyui' ? 'ComfyUI' : 'RunningHub'}
            </Tag>
          ))}
        </div>
      )
    },
    {
      title: '使用统计',
      key: 'usage',
      width: 120,
      render: (_, workflow) => (
        <div style={{ fontSize: '12px' }}>
          <div>总运行: {workflow.usageStats?.totalRuns || 0}</div>
          <div>成功率: {workflow.successRate || 0}%</div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, workflow) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingWorkflow(workflow);
              workflowForm.setFieldsValue({
                ...workflow,
                parameters: workflow.parameters || {}
              });
              setWorkflowModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个工作流吗？"
            onConfirm={() => deleteWorkflow(workflow.id)}
            okText="确定"
            cancelText="取消"
            placement="topRight"
            onCancel={() => {
              console.log('删除工作流操作已取消');
            }}
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="workflow-management">
      <Card title="工作流管理">
        <Alert
          message="工作流管理"
          description="在这里可以管理系统中的所有工作流定义。添加的工作流将自动出现在配置管理的映射选项中。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Space style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingWorkflow(null);
              workflowForm.resetFields();
              setWorkflowModalVisible(true);
            }}
          >
            添加工作流
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadWorkflows}
            loading={loading}
          >
            刷新
          </Button>
        </Space>

        <Table
          dataSource={availableWorkflows}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 工作流编辑模态框 */}
      <Modal
        title={editingWorkflow ? '编辑工作流' : '添加工作流'}
        open={workflowModalVisible}
        onCancel={() => {
          console.log('工作流Modal取消操作');
          forceCloseModal();
        }}
        onOk={() => workflowForm.submit()}
        width={800}
        destroyOnClose={true}
        maskClosable={true}
        keyboard={true}
        centered={true}
      >
        <Form
          form={workflowForm}
          layout="vertical"
          onFinish={saveWorkflow}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="id"
                label="工作流ID"
                rules={[
                  { required: true, message: '请输入工作流ID' },
                  { pattern: /^[A-Za-z0-9-_]+$/, message: 'ID只能包含字母、数字、横线和下划线' }
                ]}
              >
                <Input
                  placeholder="例如: A01-trending"
                  disabled={!!editingWorkflow}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="工作流名称"
                rules={[{ required: true, message: '请输入工作流名称' }]}
              >
                <Input placeholder="例如: 爆款开发" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  {workflowCategories.map(category => (
                    <Option key={category} value={category}>
                      {category}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="version"
                label="版本"
              >
                <Input placeholder="例如: 1.0.0" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="displayName"
            label="显示名称"
          >
            <Input placeholder="用于显示的名称（可选）" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={3} placeholder="请输入工作流描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="supportedPlatforms"
                label="支持的平台"
                rules={[{ required: true, message: '请选择支持的平台' }]}
              >
                <Select mode="multiple" placeholder="请选择支持的平台">
                  <Option value="comfyui">ComfyUI</Option>
                  <Option value="runninghub">RunningHub</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="recommendedPlatform"
                label="推荐平台"
              >
                <Select placeholder="请选择推荐平台">
                  <Option value="auto">自动选择</Option>
                  <Option value="comfyui">ComfyUI</Option>
                  <Option value="runninghub">RunningHub</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
              >
                <Input type="number" placeholder="数字越大优先级越高" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="enabled"
                label="启用状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Select
              mode="tags"
              placeholder="输入标签，按回车添加"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default WorkflowManagement;
