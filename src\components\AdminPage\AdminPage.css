.admin-layout {
  min-height: 100vh;
  width: 100%;
}

.admin-logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
  overflow: hidden;
}

.admin-header {
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-dropdown {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.content-wrapper {
  margin: 0 16px;
  flex: 1;
  min-height: 0;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.content-container {
  padding: 24px;
  min-height: 360px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.admin-footer {
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .header-right {
    gap: 8px;
  }
  
  .user-dropdown span {
    display: none;
  }
  
  .content-container {
    padding: 16px;
  }
}

.admin-header .logo {
  font-size: 20px;
  font-weight: bold;
}

.admin-header .user-info {
  display: flex;
  align-items: center;
}

.admin-header .user-info span {
  margin-right: 15px;
}

.admin-sider {
  background: #fff;
}

.admin-content {
  background: #fff;
  padding: 24px;
  margin: 0;
  min-height: 280px;
}

.admin-page {
  width: 100%;
}

.admin-header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.admin-header-actions h1 {
  margin: 0;
}

body.admin-body .navbar,
body.admin-body .sub-navbar,
body.admin-body .sidebar,
body.admin-body .sidebar-collapse-btn {
  display: none !important;
}

body.admin-body .content-wrap {
  margin-left: 0 !important;
  padding-top: 0 !important;
}

body.admin-body .app {
  padding-top: 0 !important;
}

.admin-layout {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #f0f2f5;
}

.ant-form-inline .ant-form-item {
  margin-bottom: 16px;
} 