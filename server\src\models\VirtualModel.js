const mongoose = require('mongoose');

const virtualModelSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  userId: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true,
    maxLength: 20
  },
  imageUrl: {
    type: String,
    required: true
  },
  createdAt: {
    type: String,
    required: true
  },
  // 标签信息
  tags: {
    gender: {
      type: String,
      required: true,
    },
    age: {
      type: String,
      required: true,
    },
    region: {
      type: String,
      required: true,
    },
    bodyType: {
      type: String,
      required: true,
    }
  },
  // 添加isAll字段
  isAll: {
    type: Boolean,
    default: false
  },
  images: {
    type: Array,
  }
});

module.exports = mongoose.model('VirtualModel', virtualModelSchema); 