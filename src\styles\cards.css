/**
 * 统一卡片样式定义
 * 
 * 包含以下组件：
 * 1. 卡片网格 (.cards-grid) - 响应式网格布局
 * 2. 卡片项目 (.card-item) - 基础卡片容器
 * 3. 卡片预览 (.card-preview) - 图片预览区域
 * 4. 卡片标题 (.card-caption) - 底部信息栏
 * 5. 骨架屏 (.card-skeleton) - 加载状态占位
 * 6. 特定场景 (.scenes-grid, .models-grid) - 针对不同场景的布局
 * 
 * 特点：
 * - 响应式网格布局
 * - 统一的图片比例（2:3）
 * - 优雅的加载状态
 * - 平滑的交互动画
 * - 支持选中状态
 * - 支持预览功能
 * 
 * 使用示例：
 * <div class="cards-grid models-grid">
 *   <div class="card-item">
 *     <div class="card-preview">
 *       <img src="image.jpg" alt="预览图" />
 *     </div>
 *     <div class="card-caption">
 *       <div class="card-info">
 *         <span class="card-number">#001</span>
 *         <span class="card-name">标题</span>
 *       </div>
 *       <button class="preview-button">预览</button>
 *     </div>
 *   </div>
 * </div>
 */

@import './theme.css';

/* 通用卡片网格样式 */
.cards-grid {
  display: grid;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  padding: 0;
  width: 100%;
  will-change: transform;
}

/* 卡片基础样式 */
.card-item {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: var(--transition-normal);
  cursor: pointer;
  aspect-ratio: 2/3;
  padding: 0;
  width: 100%;
  display: block;
  border: 1px solid var(--border-light);
  will-change: transform;
  transform: translateZ(0);
}

.card-item:hover {
  border-color: var(--brand-primary);
  box-shadow: var(--shadow-brand);
}

.card-item.selected {
  border-color: var(--brand-primary);
  box-shadow: var(--shadow-brand);
}

.card-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: block;
}

.card-preview img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 卡片标题和预览按钮样式 */
.card-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-xs);
  background: var(--bg-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0;
  border-top: 1px solid var(--border-lighter);
}

.card-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.card-number {
  font-size: var(--font-size-sm);
  opacity: 0.85;
  color: var(--text-secondary);
  font-weight: 500;
}

.card-name {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
}

/* 骨架屏样式 */
.card-skeleton {
  display: grid;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  padding: 0;
  width: 100%;
}

.card-skeleton-item {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  aspect-ratio: 2/3;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
}

.card-skeleton-image {
  width: 100%;
  height: calc(100% - 40px);
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.card-skeleton-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  padding: var(--spacing-xs);
  background: var(--bg-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0;
  border-top: 1px solid var(--border-lighter);
}

.card-skeleton-text {
  width: 70%;
  height: 16px;
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

.card-skeleton-button {
  width: 24px;
  height: 24px;
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 场景卡片特定样式 */
.scenes-grid {
  composes: cards-grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
}

/* 模特卡片特定样式 */
.models-grid {
  composes: cards-grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: var(--spacing-sm);
} 