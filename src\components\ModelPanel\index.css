/* 现有样式保持不变 */

/* 添加按钮组样式 */
.panel-actions {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 信息按钮样式 */
.info-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 0;
}

.info-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 信息图标样式 */
.info-icon {
  width: 16px;
  height: 16px;
  color: #666;
  opacity: 0.7;
}

.info-btn:hover .info-icon {
  opacity: 1;
  color: #1890ff;
}

/* 确保展开按钮样式与原来一致 */
.expand-btn {
  /* 保持原有样式不变 */
}

.selected-model-preview {
  position: relative;
  background-image: linear-gradient(45deg, #d1d1d1 25%, transparent 25%),
                    linear-gradient(-45deg, #d1d1d1 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #d1d1d1 75%),
                    linear-gradient(-45deg, transparent 75%, #d1d1d1 75%);
  background-size: 12px 12px;
  background-position: 0 0, 0 6px, 6px -6px, -6px 0px;
  background-color: var(--bg-primary);
}
[data-theme="dark"] .selected-model-preview {
  background-image: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                    linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
  background-size: 12px 12px;
  background-position: 0 0, 0 6px, 6px -6px, -6px 0px;
  background-color: var(--bg-primary);
} 