const workflow = {
  prompt: {
    "2": {
      "inputs": {
        "threshold": 0.30000000000000004,
        "detail_method": "VITMatte",
        "detail_erode": 6,
        "detail_dilate": 6,
        "black_point": 0.15000000000000002,
        "white_point": 0.99,
        "process_detail": true,
        "prompt": "bra, panties, swimsuit, cloth",
        "device": "cuda",
        "max_megapixels": 2,
        "image": [
          "4",
          0
        ],
        "sam_models": [
          "3",
          0
        ]
      },
      "class_type": "LayerMask: SegmentAnythingUltra V3",
      "_meta": {
        "title": "LayerMask: SegmentAnythingUltra V3(Advance)"
      }
    },
    "3": {
      "inputs": {
        "sam_model": "sam_vit_h (2.56GB)",
        "grounding_dino_model": "GroundingDINO_SwinB (938MB)"
      },
      "class_type": "LayerMask: LoadSegmentAnythingModels",
      "_meta": {
        "title": "LayerMask: Load SegmentAnything Models(Advance)"
      }
    },
    "4": {
      "inputs": {
        "aspect_ratio": "original",
        "proportional_width": 1,
        "proportional_height": 1,
        "fit": "letterbox",
        "method": "lanczos",
        "round_to_multiple": "8",
        "scale_to_side": "longest",
        "scale_to_length": 1536,
        "background_color": "#000000",
        "image": [
          "33",
          0
        ]
      },
      "class_type": "LayerUtility: ImageScaleByAspectRatio V2",
      "_meta": {
        "title": "LayerUtility: ImageScaleByAspectRatio V2"
      }
    },
    "6": {
      "inputs": {
        "fill_background": true,
        "background_color": "#FFFFFF",
        "RGBA_image": [
          "2",
          0
        ],
        "mask": [
          "2",
          1
        ]
      },
      "class_type": "LayerUtility: ImageRemoveAlpha",
      "_meta": {
        "title": "LayerUtility: ImageRemoveAlpha"
      }
    },
    "9": {
      "inputs": {
        "select": 1,
        "sel_mode": false,
        "input1": [
          "6",
          0
        ],
        "input2": [
          "17",
          0
        ],
        "input3": [
          "4",
          0
        ]
      },
      "class_type": "ImpactSwitch",
      "_meta": {
        "title": "取词选项输入（1衣服/2模特/3整体）"
      }
    },
    "13": {
      "inputs": {
        "threshold": 0.30000000000000004,
        "detail_method": "VITMatte",
        "detail_erode": 6,
        "detail_dilate": 6,
        "black_point": 0.15000000000000002,
        "white_point": 0.99,
        "process_detail": true,
        "prompt": "human",
        "device": "cuda",
        "max_megapixels": 2,
        "image": [
          "4",
          0
        ],
        "sam_models": [
          "3",
          0
        ]
      },
      "class_type": "LayerMask: SegmentAnythingUltra V3",
      "_meta": {
        "title": "LayerMask: SegmentAnythingUltra V3(Advance)"
      }
    },
    "14": {
      "inputs": {
        "text_input": "",
        "task": "more_detailed_caption",
        "fill_mask": true,
        "keep_model_loaded": false,
        "max_new_tokens": 1024,
        "num_beams": 3,
        "do_sample": true,
        "output_mask_select": "",
        "seed": 932841107813142,
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        },
        "image": [
          "9",
          0
        ],
        "florence2_model": [
          "15",
          0
        ]
      },
      "class_type": "Florence2Run",
      "_meta": {
        "title": "Florence2Run"
      }
    },
    "15": {
      "inputs": {
        "model": "Florence-2-large-PromptGen-v1.5",
        "precision": "fp16",
        "attention": "sdpa"
      },
      "class_type": "Florence2ModelLoader",
      "_meta": {
        "title": "Florence2ModelLoader"
      }
    },
    "17": {
      "inputs": {
        "fill_background": true,
        "background_color": "#FFFFFF",
        "RGBA_image": [
          "13",
          0
        ],
        "mask": [
          "13",
          1
        ]
      },
      "class_type": "LayerUtility: ImageRemoveAlpha",
      "_meta": {
        "title": "LayerUtility: ImageRemoveAlpha"
      }
    },
    "32": {
      "inputs": {
        "text": [
          "14",
          2
        ],
        "path": "/root/ComfyUI/output/",
        "filename_prefix": "Extract",
        "filename_delimiter": "_",
        "filename_number_padding": 4,
        "file_extension": ".txt",
        "encoding": "utf-8",
        "filename_suffix": ""
      },
      "class_type": "Save Text File",
      "_meta": {
        "title": "导出txt"
      }
    },
    "33": {
      "inputs": {
        "url": "https://",
        "speak_and_recognation": {
          "__value__": [
            false,
            true
          ]
        }
      },
      "class_type": "LoadImagesFromURL",
      "_meta": {
        "title": "原始图片上传"
      }
    }
  }
};

module.exports = workflow; 